import { test, expect } from '@playwright/test'

test.describe('数据源配置管理功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到数据源配置页面
    await page.goto('/dataSourceConfig?free=true')

    // 等待页面加载完成
    await page.waitForSelector('text=数据源配置管理', { timeout: 10000 })

    // 清理本地存储（在页面加载后）
    try {
      await page.evaluate(() => {
        localStorage.clear()
      })
    } catch (error) {
      console.log('localStorage clear failed:', error)
    }
  })

  test('页面基本元素显示正常', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('text=数据源配置管理')).toBeVisible()

    // 检查搜索表单
    await expect(page.locator('input[placeholder*="配置名称"]')).toBeVisible()
    await expect(page.locator('button:has-text("查询")')).toBeVisible()

    // 检查新增按钮
    await expect(page.locator('button:has-text("新增")')).toBeVisible()

    // 检查表格
    await expect(page.locator('.el-table')).toBeVisible()

    // 检查分页组件
    await expect(page.locator('.el-pagination')).toBeVisible()
  })

  test('默认数据加载正常', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForSelector('.el-table tbody tr')
    
    // 检查是否有数据行
    const rows = await page.locator('.el-table tbody tr').count()
    expect(rows).toBeGreaterThan(0)
    
    // 检查表格列
    await expect(page.locator('th:has-text("序号")')).toBeVisible()
    await expect(page.locator('th:has-text("名称")')).toBeVisible()
    await expect(page.locator('th:has-text("语言")')).toBeVisible()
    await expect(page.locator('th:has-text("创建人")')).toBeVisible()
    await expect(page.locator('th:has-text("创建时间")')).toBeVisible()
    await expect(page.locator('th:has-text("操作")')).toBeVisible()
    
    // 检查操作按钮
    await expect(page.locator('button:has-text("预览")')).toBeVisible()
    await expect(page.locator('button:has-text("编辑")')).toBeVisible()
    await expect(page.locator('button:has-text("删除")')).toBeVisible()
  })

  test('搜索功能正常', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForSelector('.el-table tbody tr')
    
    // 输入搜索关键词
    await page.fill('input[placeholder*="配置名称"]', '图表')
    await page.click('button:has-text("查询")')
    
    // 等待搜索结果
    await page.waitForTimeout(500)
    
    // 检查搜索结果
    const rows = await page.locator('.el-table tbody tr').count()
    expect(rows).toBeGreaterThan(0)
    
    // 检查搜索结果是否包含关键词
    const firstRowName = await page.locator('.el-table tbody tr:first-child td:nth-child(2)').textContent()
    expect(firstRowName).toContain('图表')
  })



  test('新增配置功能正常', async ({ page }) => {
    // 点击新增按钮
    await page.click('button:has-text("新增")')

    // 等待弹窗出现
    await expect(page.locator('.el-dialog:has-text("新增配置")')).toBeVisible()

    // 填写表单
    await page.click('.el-select:has-text("配置名称")')
    await page.click('.el-option:has-text("图表标签")')

    // 选择语言（单选按钮，默认已选中文）
    await page.check('input[value="英文"]')

    await page.fill('input[placeholder*="创建人"]', '测试用户')

    // 点击确定按钮
    await page.click('.el-dialog button:has-text("确定")')

    // 等待保存完成
    await page.waitForTimeout(500)

    // 检查弹窗是否关闭
    await expect(page.locator('.el-dialog:has-text("新增配置")')).not.toBeVisible()

    // 检查成功消息
    await expect(page.locator('.el-message:has-text("新增成功")')).toBeVisible()
  })

  test('预览功能正常', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForSelector('.el-table tbody tr')
    
    // 点击第一行的预览按钮
    await page.click('.el-table tbody tr:first-child button:has-text("预览")')
    
    // 等待预览弹窗出现
    await expect(page.locator('.el-dialog:has-text("预览")')).toBeVisible()
    
    // 检查加载状态
    await expect(page.locator('.preview-loading')).toBeVisible()
    await expect(page.locator('.loading-text')).toBeVisible()
    
    // 等待加载完成
    await page.waitForSelector('.preview-content', { timeout: 3000 })
    
    // 检查预览内容
    await expect(page.locator('.content-display')).toBeVisible()
    
    // 关闭预览弹窗
    await page.click('.el-dialog button:has-text("确定")')
    
    // 检查弹窗是否关闭
    await expect(page.locator('.el-dialog:has-text("预览")')).not.toBeVisible()
  })

  test('编辑功能正常', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForSelector('.el-table tbody tr')
    
    // 点击第一行的编辑按钮
    await page.click('.el-table tbody tr:first-child button:has-text("编辑")')
    
    // 等待编辑弹窗出现
    await expect(page.locator('.el-dialog:has-text("编辑配置")')).toBeVisible()
    
    // 修改创建人
    await page.fill('input[placeholder*="创建人"]', '修改后的用户')
    
    // 点击确定按钮
    await page.click('.el-dialog button:has-text("确定")')
    
    // 等待保存完成
    await page.waitForTimeout(500)
    
    // 检查成功消息
    await expect(page.locator('.el-message:has-text("编辑成功")')).toBeVisible()
  })

  test('删除功能正常', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForSelector('.el-table tbody tr')
    
    // 获取删除前的行数
    const initialRowCount = await page.locator('.el-table tbody tr').count()
    
    // 点击第一行的删除按钮
    await page.click('.el-table tbody tr:first-child button:has-text("删除")')
    
    // 确认删除
    await page.click('.el-popconfirm button:has-text("确定")')
    
    // 等待删除完成
    await page.waitForTimeout(500)
    
    // 检查成功消息
    await expect(page.locator('.el-message:has-text("删除成功")')).toBeVisible()
    
    // 检查行数是否减少
    const finalRowCount = await page.locator('.el-table tbody tr').count()
    expect(finalRowCount).toBe(initialRowCount - 1)
  })

  test('数据持久化功能正常', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForSelector('.el-table tbody tr')
    
    // 获取初始数据
    const initialRowCount = await page.locator('.el-table tbody tr').count()
    
    // 刷新页面
    await page.reload()
    
    // 等待页面重新加载
    await page.waitForSelector('.el-table tbody tr')
    
    // 检查数据是否保持
    const reloadedRowCount = await page.locator('.el-table tbody tr').count()
    expect(reloadedRowCount).toBe(initialRowCount)
    
    // 检查本地存储
    const storageData = await page.evaluate(() => {
      return localStorage.getItem('data_source_configs')
    })
    expect(storageData).not.toBeNull()
  })

  test('分页功能正常', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForSelector('.el-table tbody tr')
    
    // 检查分页组件
    await expect(page.locator('.el-pagination')).toBeVisible()
    
    // 如果有多页，测试翻页
    const nextButton = page.locator('.el-pagination .btn-next')
    if (await nextButton.isEnabled()) {
      await nextButton.click()
      await page.waitForTimeout(500)
      
      // 检查页码是否改变
      await expect(page.locator('.el-pagination .number.is-active:has-text("2")')).toBeVisible()
    }
  })
})
