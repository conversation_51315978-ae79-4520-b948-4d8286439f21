import { test, expect } from '@playwright/test'

test.describe('业务表主键定义基础功能测试', () => {
	test.beforeEach(async ({ page }) => {
		// 清空本地存储，确保测试环境干净
		await page.goto('/primary-key-definition?free=true')
		await page.evaluate(() => {
			localStorage.clear()
		})
		await page.reload()
		await page.waitForTimeout(2000) // 等待页面加载
	})

	test('页面基础元素显示正常', async ({ page }) => {
		await page.goto('/primary-key-definition?free=true')
		await page.waitForTimeout(2000)

		// 检查页面标题
		await expect(page.locator('h2')).toContainText('业务表主键定义')

		// 检查搜索框
		await expect(page.locator('input[placeholder="请输入主键名称"]')).toBeVisible()

		// 检查操作按钮
		await expect(page.locator('text=自定义主键')).toBeVisible()
		await expect(page.locator('text=批量修改')).toBeVisible()
		await expect(page.locator('text=批量导入')).toBeVisible()
		await expect(page.locator('text=批量导出')).toBeVisible()
		await expect(page.locator('text=更多操作')).toBeVisible()

		// 检查表格
		await expect(page.locator('table')).toBeVisible()
	})

	test('默认数据加载正常', async ({ page }) => {
		await page.goto('/primary-key-definition?free=true')
		await page.waitForTimeout(2000)

		// 检查是否有默认数据
		const rows = await page.locator('table tbody tr').count()
		expect(rows).toBeGreaterThan(0)

		// 检查第一行数据
		await expect(page.locator('table tbody tr').first()).toContainText('主键1')
	})

	test('搜索功能正常', async ({ page }) => {
		await page.goto('/primary-key-definition?free=true')
		await page.waitForTimeout(2000)

		// 输入搜索关键词
		await page.fill('input[placeholder="请输入主键名称"]', '主键1')
		await page.click('text=查询')
		await page.waitForTimeout(1000)

		// 检查搜索结果
		const visibleRows = await page.locator('table tbody tr').count()
		expect(visibleRows).toBe(1)
		await expect(page.locator('table tbody tr').first()).toContainText('主键1')

		// 清空搜索
		await page.fill('input[placeholder="请输入主键名称"]', '')
		await page.click('text=查询')
		await page.waitForTimeout(1000)

		// 检查是否显示所有数据
		const allRows = await page.locator('table tbody tr').count()
		expect(allRows).toBeGreaterThan(1)
	})

	test('新增主键功能', async ({ page }) => {
		await page.goto('/primary-key-definition?free=true')
		await page.waitForTimeout(2000)

		// 点击自定义主键按钮
		await page.click('text=自定义主键')
		await page.waitForTimeout(1000)

		// 等待弹框出现
		await expect(page.locator('.el-dialog')).toBeVisible()
		await expect(page.locator('.el-dialog__header')).toContainText('主键新增')

		// 填写表单
		await page.fill('input[placeholder="请输入主键名称"]', '自动化测试主键')
		
		// 选择数据类型
		await page.click('.el-select')
		await page.waitForTimeout(500)
		await page.click('.el-option:has-text("字符型C")')

		// 提交表单
		await page.click('.el-dialog .el-button--primary:has-text("确定")')
		await page.waitForTimeout(2000)

		// 检查新数据是否出现在表格中
		await expect(page.locator('table tbody')).toContainText('自动化测试主键')
	})

	test('Excel 导出功能', async ({ page }) => {
		await page.goto('/primary-key-definition?free=true')
		await page.waitForTimeout(2000)

		// 监听下载事件
		const downloadPromise = page.waitForEvent('download')

		// 点击批量导出按钮
		await page.click('text=批量导出')

		// 等待下载开始
		const download = await downloadPromise

		// 检查下载文件名
		expect(download.suggestedFilename()).toMatch(/主键定义数据_.*\.xlsx/)
	})

	test('下载导入模板功能', async ({ page }) => {
		await page.goto('/primary-key-definition?free=true')
		await page.waitForTimeout(2000)

		// 监听下载事件
		const downloadPromise = page.waitForEvent('download')

		// 点击更多操作下拉菜单
		await page.click('text=更多操作')
		await page.waitForTimeout(500)
		await page.click('text=下载导入模板')

		// 等待下载开始
		const download = await downloadPromise

		// 检查下载文件名
		expect(download.suggestedFilename()).toBe('主键定义导入模板.xlsx')
	})

	test('数据持久化验证', async ({ page }) => {
		await page.goto('/primary-key-definition?free=true')
		await page.waitForTimeout(2000)

		// 添加一个新主键
		await page.click('text=自定义主键')
		await page.waitForTimeout(1000)
		
		await page.fill('input[placeholder="请输入主键名称"]', '持久化测试主键')
		await page.click('.el-select')
		await page.waitForTimeout(500)
		await page.click('.el-option:has-text("字符型C")')
		await page.click('.el-dialog .el-button--primary:has-text("确定")')
		await page.waitForTimeout(2000)

		// 刷新页面
		await page.reload()
		await page.waitForTimeout(2000)

		// 检查数据是否仍然存在
		await expect(page.locator('table tbody')).toContainText('持久化测试主键')
	})
})
