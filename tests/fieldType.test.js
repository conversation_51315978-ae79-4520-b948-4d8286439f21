// 字段类型管理功能自动化测试
import { test, expect } from '@playwright/test'

test.describe('字段类型管理', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到字段类型管理页面
    await page.goto('/fieldType')
    await page.waitForLoadState('networkidle')
  })

  test('页面基本元素检查', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('text=字段类型管理')).toBeVisible()
    
    // 检查按钮是否存在
    await expect(page.locator('text=新增字段类型')).toBeVisible()
    await expect(page.locator('text=批量导出')).toBeVisible()
    await expect(page.locator('text=批量导入')).toBeVisible()
    await expect(page.locator('text=更新日志')).toBeVisible()
    await expect(page.locator('text=字段信息模板')).toBeVisible()
    await expect(page.locator('text=更多操作')).toBeVisible()
    await expect(page.locator('text=批量删除')).toBeVisible()
  })

  test('批量导出功能测试', async ({ page }) => {
    // 点击批量导出按钮
    await page.click('text=批量导出')
    
    // 等待下载开始（应该导出全部数据）
    const downloadPromise = page.waitForEvent('download')
    const download = await downloadPromise
    
    // 验证文件名包含"全部"
    expect(download.suggestedFilename()).toContain('全部')
    expect(download.suggestedFilename()).toContain('.xlsx')
  })

  test('批量导入弹窗测试', async ({ page }) => {
    // 点击批量导入按钮
    await page.click('text=批量导入')
    
    // 检查弹窗是否打开
    await expect(page.locator('text=批量导入')).toBeVisible()
    await expect(page.locator('text=操作流程：')).toBeVisible()
    await expect(page.locator('text=下载模板')).toBeVisible()
    await expect(page.locator('text=填写表格')).toBeVisible()
    await expect(page.locator('text=上传表格')).toBeVisible()
    
    // 检查导入模板下载按钮
    await expect(page.locator('text=导入模板下载')).toBeVisible()
    
    // 检查上传区域
    await expect(page.locator('text=点击或将文件拖拽到这里上传')).toBeVisible()
  })

  test('更新日志功能测试', async ({ page }) => {
    // 点击更新日志按钮
    await page.click('text=更新日志')
    
    // 检查更新日志弹窗
    await expect(page.locator('text=字段更新日志')).toBeVisible()
    await expect(page.locator('text=新增')).toBeVisible()
    
    // 点击新增按钮
    await page.click('button:has-text("新增")')
    
    // 检查新增更新日志弹窗
    await expect(page.locator('text=更新内容')).toBeVisible()
    await expect(page.locator('text=更新时间')).toBeVisible()
    await expect(page.locator('text=创建人')).toBeVisible()
    await expect(page.locator('text=更新版本')).toBeVisible()
    
    // 检查时间选择器
    await expect(page.locator('.el-date-editor')).toBeVisible()
  })

  test('搜索功能测试', async ({ page }) => {
    // 测试字段名称搜索
    await page.fill('input[placeholder="请输入字段名称"]', '测试')
    await page.click('text=查询')
    
    // 测试重置功能
    await page.click('text=重置')
    
    // 验证搜索框已清空
    await expect(page.locator('input[placeholder="请输入字段名称"]')).toHaveValue('')
  })

  test('新增字段类型测试', async ({ page }) => {
    // 点击新增按钮
    await page.click('text=新增字段类型')
    
    // 检查新增弹窗
    await expect(page.locator('text=新增常用字段类型')).toBeVisible()
    
    // 关闭弹窗
    await page.click('text=取消')
  })
})

// 导出功能测试
test.describe('导出功能', () => {
  test('模板下载测试', async ({ page }) => {
    await page.goto('/fieldType')
    
    // 打开批量导入弹窗
    await page.click('text=批量导入')
    
    // 点击模板下载
    const downloadPromise = page.waitForEvent('download')
    await page.click('text=导入模板下载')
    const download = await downloadPromise
    
    // 验证下载的文件
    expect(download.suggestedFilename()).toContain('模板')
    expect(download.suggestedFilename()).toContain('.xlsx')
  })
})
