// 批量导入功能专项测试
import { test, expect } from '@playwright/test'
import path from 'path'

test.describe('批量导入功能测试', () => {
  const testFilePath = path.join(process.cwd(), 'downloads', '字段类型数据_2025-07-08.xlsx')
  
  test.beforeEach(async ({ page }) => {
    // 导航到字段类型管理页面
    await page.goto('/fieldType')
    await page.waitForLoadState('networkidle')
  })

  test('批量导入弹窗基本功能', async ({ page }) => {
    // 点击批量导入按钮
    await page.click('text=批量导入')
    
    // 检查弹窗是否正确打开
    await expect(page.locator('.el-dialog__title:has-text("批量导入")')).toBeVisible()
    
    // 检查操作流程指引
    await expect(page.locator('text=操作流程：')).toBeVisible()
    await expect(page.locator('text=下载模板')).toBeVisible()
    await expect(page.locator('text=填写表格')).toBeVisible()
    await expect(page.locator('text=上传表格')).toBeVisible()
    
    // 检查导入模板下载按钮
    await expect(page.locator('button:has-text("导入模板下载")')).toBeVisible()
    
    // 检查上传区域
    await expect(page.locator('text=点击或将文件拖拽到这里上传')).toBeVisible()
    
    // 检查上传按钮
    await expect(page.locator('button:has-text("上传")')).toBeVisible()
  })

  test('文件选择和文件名显示功能', async ({ page }) => {
    // 打开批量导入弹窗
    await page.click('text=批量导入')
    
    // 等待弹窗完全加载
    await page.waitForSelector('.el-upload')
    
    // 模拟文件选择
    const fileInput = page.locator('input[type="file"]')
    await fileInput.setInputFiles(testFilePath)
    
    // 等待文件处理
    await page.waitForTimeout(1000)
    
    // 检查是否显示成功消息
    await expect(page.locator('.el-message--success')).toBeVisible()
    
    // 检查文件名是否显示在指定位置
    await expect(page.locator('.selected-file .file-name')).toBeVisible()
    await expect(page.locator('.selected-file .file-name')).toContainText('字段类型数据_2025-07-08.xlsx')
  })

  test('文件上传和数据导入功能', async ({ page }) => {
    // 打开批量导入弹窗
    await page.click('text=批量导入')
    
    // 选择测试文件
    const fileInput = page.locator('input[type="file"]')
    await fileInput.setInputFiles(testFilePath)
    
    // 等待文件选择完成
    await page.waitForTimeout(1000)
    
    // 点击上传按钮
    await page.click('button:has-text("上传")')
    
    // 等待上传处理
    await page.waitForTimeout(3000)
    
    // 检查是否显示成功消息
    await expect(page.locator('.el-message--success')).toBeVisible()
    
    // 检查弹窗是否关闭
    await expect(page.locator('.el-dialog__title:has-text("批量导入")')).not.toBeVisible()
    
    // 检查表格中是否有新增的数据
    await expect(page.locator('text=测试用户名字段')).toBeVisible()
    await expect(page.locator('text=测试年龄字段')).toBeVisible()
  })

  test('文件类型验证', async ({ page }) => {
    // 打开批量导入弹窗
    await page.click('text=批量导入')
    
    // 创建一个非Excel文件进行测试
    const invalidFile = path.join(process.cwd(), 'package.json')
    
    // 尝试选择非Excel文件
    const fileInput = page.locator('input[type="file"]')
    await fileInput.setInputFiles(invalidFile)
    
    // 等待处理
    await page.waitForTimeout(1000)
    
    // 检查是否显示错误消息
    await expect(page.locator('.el-message--error')).toBeVisible()
    await expect(page.locator('text=请上传Excel文件')).toBeVisible()
  })

  test('模板下载功能', async ({ page }) => {
    // 打开批量导入弹窗
    await page.click('text=批量导入')
    
    // 点击模板下载按钮
    const downloadPromise = page.waitForEvent('download')
    await page.click('button:has-text("导入模板下载")')
    const download = await downloadPromise
    
    // 验证下载的文件
    expect(download.suggestedFilename()).toContain('模板')
    expect(download.suggestedFilename()).toContain('.xlsx')
    
    // 检查成功消息
    await expect(page.locator('.el-message--success')).toBeVisible()
    await expect(page.locator('text=模板下载成功')).toBeVisible()
  })

  test('文件移除功能', async ({ page }) => {
    // 打开批量导入弹窗
    await page.click('text=批量导入')
    
    // 选择文件
    const fileInput = page.locator('input[type="file"]')
    await fileInput.setInputFiles(testFilePath)
    
    // 等待文件选择完成
    await page.waitForTimeout(1000)
    
    // 检查文件名是否显示
    await expect(page.locator('.selected-file .file-name')).toBeVisible()
    
    // 点击移除文件按钮
    await page.click('.el-upload-list__item-delete')
    
    // 检查文件名是否消失
    await expect(page.locator('.selected-file .file-name')).not.toBeVisible()
  })
})

// 数据验证测试
test.describe('导入数据验证', () => {
  test('重复数据检测', async ({ page }) => {
    await page.goto('/fieldType')
    
    // 先导入一次数据
    await page.click('text=批量导入')
    const fileInput = page.locator('input[type="file"]')
    await fileInput.setInputFiles(path.join(process.cwd(), 'downloads', '字段类型数据_2025-07-08.xlsx'))
    await page.waitForTimeout(1000)
    await page.click('button:has-text("上传")')
    await page.waitForTimeout(3000)
    
    // 再次尝试导入相同数据
    await page.click('text=批量导入')
    await fileInput.setInputFiles(path.join(process.cwd(), 'downloads', '字段类型数据_2025-07-08.xlsx'))
    await page.waitForTimeout(1000)
    await page.click('button:has-text("上传")')
    await page.waitForTimeout(2000)
    
    // 检查是否显示重复数据错误
    await expect(page.locator('.el-message--error')).toBeVisible()
    await expect(page.locator('text=已存在')).toBeVisible()
  })
})
