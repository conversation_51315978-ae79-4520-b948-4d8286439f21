# 批量操作功能测试

## 测试目标
验证任务目标拆解详情页面中的批量状态更新和批量分配责任人功能已从手动文本输入改为下拉选择。

## 测试步骤

### 1. 访问页面
1. 打开浏览器访问：http://localhost:5176/login?free=true
2. 登录后导航到任务目标拆解页面
3. 点击任意任务的"详情"按钮进入详情页面

### 2. 测试批量状态更新功能
1. 在子任务列表中选择一个或多个子任务（勾选复选框）
2. 点击"批量状态更新"按钮
3. **预期结果**：应该弹出一个对话框，包含：
   - 标题："批量状态更新"
   - 下拉选择框，包含选项：未开始、执行中、已完成、已暂停
   - 显示已选择的子任务数量
   - 确认和取消按钮

### 3. 测试批量分配责任人功能
1. 在子任务列表中选择一个或多个子任务（勾选复选框）
2. 点击"批量分配责任人"按钮
3. **预期结果**：应该弹出一个对话框，包含：
   - 标题："批量分配责任人"
   - 可搜索的下拉选择框，包含责任人选项（格式：姓名 - 部门 - 职位）
   - 显示已选择的子任务数量
   - 确认和取消按钮

### 4. 功能验证
1. 选择状态/责任人后点击确认
2. **预期结果**：
   - 显示成功消息
   - 选中的子任务状态/责任人得到更新
   - 弹窗关闭
   - 选择状态清空

## 修改内容总结

### 代码变更
1. **批量状态更新**：
   - 移除了 `ElMessageBox.prompt` 的手动输入方式
   - 添加了 `batchStatusDialogVisible` 状态控制弹窗
   - 添加了 `statusOptions` 状态选项数组
   - 添加了 `confirmBatchStatusUpdate` 确认方法

2. **批量分配责任人**：
   - 移除了 `ElMessageBox.prompt` 的手动输入方式
   - 添加了 `batchAssignDialogVisible` 状态控制弹窗
   - 添加了 `responsiblePersonOptions` 责任人选项数组
   - 添加了 `confirmBatchAssign` 确认方法

3. **UI组件**：
   - 添加了两个 `el-dialog` 组件用于批量操作
   - 添加了对应的样式 `.batch-dialog-content`

### 用户体验改进
- 从手动输入改为下拉选择，减少输入错误
- 提供预定义选项，确保数据一致性
- 支持搜索功能（责任人选择）
- 显示选中任务数量，提供更好的反馈
