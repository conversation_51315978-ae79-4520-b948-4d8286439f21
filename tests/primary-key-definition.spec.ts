import { test, expect } from '@playwright/test'

test.describe('业务表主键定义功能测试', () => {
	test.beforeEach(async ({ page }) => {
		// 清空本地存储，确保测试环境干净
		await page.goto('/primary-key-definition')
		await page.evaluate(() => {
			localStorage.clear()
		})
		await page.reload()
	})

	test('页面基础元素显示正常', async ({ page }) => {
		await page.goto('/primary-key-definition')

		// 检查页面标题
		await expect(page.locator('h2')).toContainText('业务表主键定义')

		// 检查搜索框
		await expect(page.locator('input[placeholder="请输入主键名称"]')).toBeVisible()

		// 检查操作按钮
		await expect(page.locator('text=自定义主键')).toBeVisible()
		await expect(page.locator('text=批量修改')).toBeVisible()
		await expect(page.locator('text=批量导入')).toBeVisible()
		await expect(page.locator('text=批量导出')).toBeVisible()
		await expect(page.locator('text=更多操作')).toBeVisible()

		// 检查表格
		await expect(page.locator('.el-table')).toBeVisible()
	})

	test('默认数据加载正常', async ({ page }) => {
		await page.goto('/primary-key-definition')

		// 等待表格数据加载
		await page.waitForSelector('.el-table tbody tr')

		// 检查是否有默认数据
		const rows = await page.locator('.el-table tbody tr').count()
		expect(rows).toBeGreaterThan(0)

		// 检查第一行数据
		await expect(page.locator('.el-table tbody tr').first()).toContainText('主键1')
	})

	test('搜索功能正常', async ({ page }) => {
		await page.goto('/primary-key-definition')
		await page.waitForSelector('.el-table tbody tr')

		// 输入搜索关键词
		await page.fill('input[placeholder="请输入主键名称"]', '主键1')
		await page.click('text=查询')

		// 等待搜索结果
		await page.waitForTimeout(500)

		// 检查搜索结果
		const visibleRows = await page.locator('.el-table tbody tr').count()
		expect(visibleRows).toBe(1)
		await expect(page.locator('.el-table tbody tr').first()).toContainText('主键1')

		// 清空搜索
		await page.fill('input[placeholder="请输入主键名称"]', '')
		await page.click('text=查询')
		await page.waitForTimeout(500)

		// 检查是否显示所有数据
		const allRows = await page.locator('.el-table tbody tr').count()
		expect(allRows).toBeGreaterThan(1)
	})

	test('主键名称列排序功能', async ({ page }) => {
		await page.goto('/primary-key-definition')
		await page.waitForSelector('.el-table tbody tr')

		// 获取排序前的第一行数据
		const firstRowBefore = await page.locator('.el-table tbody tr').first().locator('td').nth(1).textContent()

		// 点击主键名称列标题进行排序
		await page.click('.el-table th:has-text("主键名称")')
		await page.waitForTimeout(500)

		// 检查排序后的结果
		const firstRowAfter = await page.locator('.el-table tbody tr').first().locator('td').nth(1).textContent()
		
		// 再次点击切换排序方向
		await page.click('.el-table th:has-text("主键名称")')
		await page.waitForTimeout(500)

		// 第三次点击取消排序
		await page.click('.el-table th:has-text("主键名称")')
		await page.waitForTimeout(500)
	})

	test('新增主键功能', async ({ page }) => {
		await page.goto('/primary-key-definition')
		await page.waitForSelector('.el-table tbody tr')

		// 点击自定义主键按钮
		await page.click('text=自定义主键')

		// 等待弹框出现
		await expect(page.locator('.el-dialog')).toBeVisible()
		await expect(page.locator('.el-dialog__header')).toContainText('主键新增')

		// 填写表单
		await page.fill('input[placeholder="请输入主键名称"]', '测试主键')
		await page.click('.el-select:has-text("请选择数据类型")')
		await page.click('.el-option:has-text("字符型C")')
		await page.fill('input[placeholder="请输入主键说明"]', '这是一个测试主键')

		// 设置主键更新通知
		await page.click('.el-switch')

		// 提交表单
		await page.click('.el-dialog .el-button--primary:has-text("确定")')

		// 等待成功消息
		await expect(page.locator('.el-message--success')).toBeVisible()

		// 检查新数据是否出现在表格中
		await expect(page.locator('.el-table tbody')).toContainText('测试主键')
	})

	test('编辑主键功能', async ({ page }) => {
		await page.goto('/primary-key-definition')
		await page.waitForSelector('.el-table tbody tr')

		// 点击第一行的修改按钮
		await page.click('.el-table tbody tr:first-child .el-button:has-text("修改")')

		// 等待编辑弹框出现
		await expect(page.locator('.el-dialog')).toBeVisible()
		await expect(page.locator('.el-dialog__header')).toContainText('编辑主键')

		// 修改主键名称
		await page.fill('input[placeholder="请输入主键名称"]', '修改后的主键')

		// 提交修改
		await page.click('.el-dialog .el-button--primary:has-text("确定")')

		// 等待成功消息
		await expect(page.locator('.el-message--success')).toBeVisible()

		// 检查修改是否生效
		await expect(page.locator('.el-table tbody')).toContainText('修改后的主键')
	})

	test('删除主键功能', async ({ page }) => {
		await page.goto('/primary-key-definition')
		await page.waitForSelector('.el-table tbody tr')

		// 获取删除前的行数
		const rowsBefore = await page.locator('.el-table tbody tr').count()

		// 点击第一行的删除按钮
		await page.click('.el-table tbody tr:first-child .el-button:has-text("删除")')

		// 确认删除
		await expect(page.locator('.el-message-box')).toBeVisible()
		await page.click('.el-message-box .el-button--primary:has-text("确定")')

		// 等待成功消息
		await expect(page.locator('.el-message--success')).toBeVisible()

		// 检查行数是否减少
		const rowsAfter = await page.locator('.el-table tbody tr').count()
		expect(rowsAfter).toBe(rowsBefore - 1)
	})

	test('批量修改功能', async ({ page }) => {
		await page.goto('/primary-key-definition')
		await page.waitForSelector('.el-table tbody tr')

		// 选择多行数据
		await page.click('.el-table tbody tr:first-child .el-checkbox')
		await page.click('.el-table tbody tr:nth-child(2) .el-checkbox')

		// 点击批量修改按钮
		await page.click('text=批量修改')

		// 等待批量修改弹框出现
		await expect(page.locator('.el-dialog')).toBeVisible()
		await expect(page.locator('.el-dialog__header')).toContainText('批量修改')

		// 填写批量修改表单
		await page.click('.el-select:has-text("请选择数据类型")')
		await page.click('.el-option:has-text("数值型N")')
		await page.fill('.el-input-number input', '1')
		await page.fill('.el-input-number:nth-child(2) input', '10')

		// 提交批量修改
		await page.click('.el-dialog .el-button--primary:has-text("确定")')

		// 等待成功消息
		await expect(page.locator('.el-message--success')).toBeVisible()
	})

	test('主键清理规则功能', async ({ page }) => {
		await page.goto('/primary-key-definition')

		// 点击更多操作下拉菜单
		await page.click('text=更多操作')
		await page.click('text=主键清理规则')

		// 等待清理规则弹框出现
		await expect(page.locator('.el-dialog')).toBeVisible()
		await expect(page.locator('.el-dialog__header')).toContainText('主键清理规则')

		// 修改清理时间
		await page.fill('.el-input-number input', '60')

		// 选择清理策略
		await page.click('.el-select:has-text("请选择清理策略")')
		await page.click('.el-option:has-text("删除")')

		// 保存清理规则
		await page.click('.el-dialog .el-button--primary:has-text("确定")')

		// 等待成功消息
		await expect(page.locator('.el-message--success')).toBeVisible()
	})

	test('数据持久化验证', async ({ page }) => {
		await page.goto('/primary-key-definition')
		await page.waitForSelector('.el-table tbody tr')

		// 添加一个新主键
		await page.click('text=自定义主键')
		await page.fill('input[placeholder="请输入主键名称"]', '持久化测试主键')
		await page.click('.el-select:has-text("请选择数据类型")')
		await page.click('.el-option:has-text("字符型C")')
		await page.click('.el-switch')
		await page.click('.el-dialog .el-button--primary:has-text("确定")')
		await expect(page.locator('.el-message--success')).toBeVisible()

		// 刷新页面
		await page.reload()
		await page.waitForSelector('.el-table tbody tr')

		// 检查数据是否仍然存在
		await expect(page.locator('.el-table tbody')).toContainText('持久化测试主键')
	})

	test('表单验证功能', async ({ page }) => {
		await page.goto('/primary-key-definition')

		// 点击自定义主键按钮
		await page.click('text=自定义主键')
		await expect(page.locator('.el-dialog')).toBeVisible()

		// 不填写必填项，直接提交
		await page.click('.el-dialog .el-button--primary:has-text("确定")')

		// 检查验证错误消息
		await expect(page.locator('.el-form-item__error')).toBeVisible()

		// 填写主键名称但不选择数据类型
		await page.fill('input[placeholder="请输入主键名称"]', '验证测试')
		await page.click('.el-dialog .el-button--primary:has-text("确定")')

		// 检查数据类型验证错误
		await expect(page.locator('.el-form-item__error:has-text("请选择主键数据类型")')).toBeVisible()
	})

	test('Excel 导出功能', async ({ page }) => {
		await page.goto('/primary-key-definition')
		await page.waitForSelector('.el-table tbody tr')

		// 监听下载事件
		const downloadPromise = page.waitForEvent('download')

		// 点击批量导出按钮
		await page.click('text=批量导出')

		// 等待下载开始
		const download = await downloadPromise

		// 检查下载文件名
		expect(download.suggestedFilename()).toMatch(/主键定义数据_.*\.xlsx/)
	})

	test('下载导入模板功能', async ({ page }) => {
		await page.goto('/primary-key-definition')

		// 监听下载事件
		const downloadPromise = page.waitForEvent('download')

		// 点击更多操作下拉菜单
		await page.click('text=更多操作')
		await page.click('text=下载导入模板')

		// 等待下载开始
		const download = await downloadPromise

		// 检查下载文件名
		expect(download.suggestedFilename()).toBe('主键定义导入模板.xlsx')
	})

	test('Excel 导入功能界面', async ({ page }) => {
		await page.goto('/primary-key-definition')

		// 点击批量导入按钮
		await page.click('text=批量导入')

		// 检查文件选择对话框是否触发
		// 注意：由于安全限制，我们无法直接测试文件上传，
		// 但可以验证点击事件是否正确触发
		await page.waitForTimeout(500)
	})

	test('响应式设计验证', async ({ page }) => {
		// 测试移动端视图
		await page.setViewportSize({ width: 375, height: 667 })
		await page.goto('/primary-key-definition')

		// 检查工具栏是否变为垂直布局
		const toolbar = page.locator('.toolbar')
		await expect(toolbar).toBeVisible()

		// 检查按钮是否正确显示
		await expect(page.locator('text=自定义主键')).toBeVisible()
		await expect(page.locator('text=批量修改')).toBeVisible()

		// 恢复桌面端视图
		await page.setViewportSize({ width: 1280, height: 720 })
	})
})
