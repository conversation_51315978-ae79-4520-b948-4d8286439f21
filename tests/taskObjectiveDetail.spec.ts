import { test, expect } from '@playwright/test'

test.describe('任务目标拆解详情页面测试', () => {
  test.beforeEach(async ({ page }) => {
    // 直接访问详情页面
    await page.goto('/taskObjectiveDecomposition/detail/1?free=true')

    // 等待页面加载完成
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000) // 减少等待时间
  })

  test('详情页面基本元素显示', async ({ page }) => {
    // 检查页面基本结构
    await expect(page.locator('body')).toBeVisible()

    // 检查任务基本信息标题
    await expect(page.getByRole('heading', { name: '任务基本信息' })).toBeVisible()
    await expect(page.locator('text=任务名称：')).toBeVisible()
    await expect(page.locator('text=任务类型：')).toBeVisible()

    // 检查子任务列表标题
    await expect(page.getByRole('heading', { name: '子任务列表' })).toBeVisible()

    // 检查操作按钮
    await expect(page.getByRole('button', { name: '进度配置' })).toBeVisible()
    await expect(page.getByRole('button', { name: '更多操作' })).toBeVisible()

    // 检查表格
    await expect(page.getByRole('table')).toBeVisible()
  })

  test('进度配置弹窗功能', async ({ page }) => {
    // 点击第一个进度配置按钮（使用first()避免重复元素问题）
    await page.getByRole('button', { name: '进度配置' }).first().click()

    // 检查弹窗是否打开
    await expect(page.getByRole('dialog', { name: '进度配置' })).toBeVisible()

    // 简化测试，只验证弹窗打开和关闭
    await page.getByRole('button', { name: '取消' }).click()

    // 验证弹窗已关闭
    await expect(page.getByRole('dialog', { name: '进度配置' })).not.toBeVisible()
  })

  test('更多操作下拉菜单', async ({ page }) => {
    // 点击更多操作按钮
    await page.getByRole('button', { name: '更多操作' }).click()

    // 等待下拉菜单出现
    await page.waitForTimeout(1000)

    // 检查下拉菜单是否存在
    await expect(page.getByRole('menu', { name: '更多操作' })).toBeVisible()

    // 检查菜单项是否存在
    await expect(page.getByRole('menuitem', { name: '难度分析' })).toBeVisible()
    await expect(page.getByRole('menuitem', { name: '时长分析' })).toBeVisible()
    await expect(page.getByRole('menuitem', { name: '提醒历史' })).toBeVisible()
    await expect(page.getByRole('menuitem', { name: '编辑' })).toBeVisible()
  })

  test('子任务数据验证', async ({ page }) => {
    // 等待表格完全加载
    await page.waitForSelector('.table-section .el-table', { timeout: 10000 })

    // 检查表格是否存在
    await expect(page.locator('.table-section .el-table')).toBeVisible()

    // 检查表格体是否存在
    const tableBody = page.locator('.table-section .el-table tbody')
    await expect(tableBody).toBeVisible()

    // 等待数据加载
    await page.waitForTimeout(3000)

    // 检查是否有数据行或空状态
    const hasRows = await page.locator('.table-section .el-table tbody tr').count() > 0
    const hasEmptyState = await page.locator('.el-table__empty-text').isVisible()

    // 至少应该有行或空状态之一
    expect(hasRows || hasEmptyState).toBeTruthy()

    console.log('表格状态 - 有数据行:', hasRows, '有空状态:', hasEmptyState)
  })

  test('子任务表格操作', async ({ page }) => {
    // 等待表格完全加载
    await page.waitForTimeout(3000) // 等待数据加载

    // 检查表格是否存在
    await expect(page.getByRole('table')).toBeVisible()

    // 检查子任务表格是否有数据行
    const rowCount = await page.getByRole('row').count()
    console.log('表格行数:', rowCount)

    if (rowCount > 1) { // 大于1因为包含表头行
      // 检查第一个数据行的操作按钮
      const editButtons = page.getByRole('button', { name: '编辑' })
      const deleteButtons = page.getByRole('button', { name: '删除' })
      const copyButtons = page.getByRole('button', { name: '复制' })

      await expect(editButtons.first()).toBeVisible()
      await expect(deleteButtons.first()).toBeVisible()
      await expect(copyButtons.first()).toBeVisible()

      // 点击第一个编辑按钮
      await editButtons.first().click()

      // 简化测试，只验证按钮点击成功
      console.log('编辑按钮点击成功')
    } else {
      // 如果没有数据，至少验证表格结构正确
      console.log('表格没有数据，验证表格结构')
      await expect(page.getByRole('table')).toBeVisible()
    }
  })

  test('返回功能', async ({ page }) => {
    // 点击返回按钮
    await page.locator('.page-header button:has-text("返回")').click()
    
    // 等待页面跳转
    await page.waitForLoadState('networkidle')
    
    // 检查是否返回到列表页面
    await expect(page).toHaveURL(/\/taskObjectiveDecomposition/)
  })

  test('时长分析功能', async ({ page }) => {
    // 点击更多操作
    await page.getByRole('button', { name: '更多操作' }).click()

    // 等待下拉菜单出现
    await page.waitForTimeout(1000)

    // 使用menuitem角色选择器点击时长分析
    await page.getByRole('menuitem', { name: '时长分析' }).click()

    // 检查时长分析弹窗
    await expect(page.getByRole('dialog', { name: '时长分析' })).toBeVisible()

    // 检查表格内容
    await expect(page.locator('text=任务接收')).toBeVisible()
    await expect(page.locator('text=任务填报')).toBeVisible()

    // 测试确认按钮
    await page.getByRole('button', { name: '确认' }).click()

    // 弹窗应该关闭
    await expect(page.getByRole('dialog', { name: '时长分析' })).not.toBeVisible()
  })

  test('提醒历史功能', async ({ page }) => {
    // 点击更多操作
    await page.getByRole('button', { name: '更多操作' }).click()

    // 等待下拉菜单出现
    await page.waitForTimeout(1000)

    // 使用menuitem角色选择器点击提醒历史
    await page.getByRole('menuitem', { name: '提醒历史' }).click()

    // 检查提醒历史弹窗
    await expect(page.getByRole('dialog', { name: '任务提醒历史' })).toBeVisible()

    // 检查表格内容（简化验证）
    await expect(page.locator('text=2025-04-23')).toBeVisible()
    await expect(page.locator('text=2025-05-23')).toBeVisible()
    await expect(page.locator('text=2025-06-23')).toBeVisible()

    // 检查统计信息
    await expect(page.locator('text=提醒统计')).toBeVisible()

    // 测试确认按钮
    await page.getByRole('button', { name: '确认' }).click()

    // 检查成功消息
    await expect(page.locator('text=提醒历史确认成功')).toBeVisible()
  })

  test('进度追踪功能', async ({ page }) => {
    // 点击更多操作
    await page.getByRole('button', { name: '更多操作' }).click()

    // 等待下拉菜单出现
    await page.waitForTimeout(1000)

    // 使用menuitem角色选择器点击进度追踪
    await page.getByRole('menuitem', { name: '进度追踪' }).click()

    // 检查进度追踪弹窗
    await expect(page.getByRole('dialog', { name: '子任务历史进度追踪' })).toBeVisible()

    // 关闭弹窗
    await page.getByRole('button', { name: '取消' }).click()
  })

  test('难度分析弹窗功能', async ({ page }) => {
    // 点击更多操作
    await page.getByRole('button', { name: '更多操作' }).click()

    // 等待下拉菜单出现
    await page.waitForTimeout(1000)

    // 使用menuitem角色选择器点击难度分析
    await page.getByRole('menuitem', { name: '难度分析' }).click()

    // 检查难度分析弹窗
    await expect(page.getByRole('dialog', { name: '难度分析' })).toBeVisible()

    // 简化测试，只验证弹窗打开和关闭
    await page.getByRole('button', { name: '确认' }).click()

    // 检查弹窗关闭
    await expect(page.getByRole('dialog', { name: '难度分析' })).not.toBeVisible()
  })

  test('弹窗关闭功能测试', async ({ page }) => {
    // 测试时长分析弹窗的关闭功能
    await page.getByRole('button', { name: '更多操作' }).click()
    await page.waitForTimeout(1000)
    await page.getByRole('menuitem', { name: '时长分析' }).click()
    await expect(page.getByRole('dialog', { name: '时长分析' })).toBeVisible()

    // 点击取消按钮关闭
    await page.getByRole('button', { name: '取消' }).click()
    await expect(page.getByRole('dialog', { name: '时长分析' })).not.toBeVisible()

    // 测试提醒历史弹窗的关闭功能
    await page.getByRole('button', { name: '更多操作' }).click()
    await page.waitForTimeout(1000)
    await page.getByRole('menuitem', { name: '提醒历史' }).click()
    await expect(page.getByRole('dialog', { name: '任务提醒历史' })).toBeVisible()

    // 点击取消按钮关闭
    await page.getByRole('button', { name: '取消' }).click()
    await expect(page.getByRole('dialog', { name: '任务提醒历史' })).not.toBeVisible()
  })

  test('数据加载状态测试', async ({ page }) => {
    // 测试时长分析弹窗的加载状态
    await page.getByRole('button', { name: '更多操作' }).click()
    await page.waitForTimeout(1000)
    await page.getByRole('menuitem', { name: '时长分析' }).click()

    // 检查弹窗打开
    await expect(page.getByRole('dialog', { name: '时长分析' })).toBeVisible()

    // 点击确认按钮
    await page.getByRole('button', { name: '确认' }).click()

    // 弹窗应该关闭
    await expect(page.getByRole('dialog', { name: '时长分析' })).not.toBeVisible()
  })
})
