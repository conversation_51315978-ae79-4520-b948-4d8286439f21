import { test, expect } from '@playwright/test'

test.describe('任务目标分解详情页面', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到任务详情页面，添加free=true跳过认证
    await page.goto('/taskObjectiveDecomposition/detail/1?free=true')
    await page.waitForLoadState('networkidle')
  })

  test('应该正确显示页面基本信息', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('h2.page-title')).toContainText('任务目标拆解')
    
    // 检查任务基本信息卡片
    await expect(page.locator('.info-card')).toBeVisible()
    await expect(page.locator('.info-card h3')).toContainText('任务基本信息')
  })

  test('应该正确显示子任务列表', async ({ page }) => {
    // 检查子任务列表卡片
    await expect(page.locator('.subtask-card')).toBeVisible()
    await expect(page.locator('.subtask-card h3')).toContainText('详情')
    
    // 检查表格是否存在
    await expect(page.locator('.table-section')).toBeVisible()
  })

  test('子任务编辑弹窗功能测试', async ({ page }) => {
    // 等待表格加载完成
    await page.waitForSelector('.table-section table', { timeout: 10000 })
    
    // 点击第一行的编辑按钮
    const editButton = page.locator('button:has-text("编辑")').first()
    await editButton.click()
    
    // 检查弹窗是否打开
    await expect(page.locator('.el-dialog')).toBeVisible()
    await expect(page.locator('.el-dialog__title')).toContainText('业务表子任务编辑')
    
    // 检查负责人下拉选择框
    const responsiblePersonSelect = page.locator('label:has-text("责任人")').locator('..').locator('.el-select')
    await expect(responsiblePersonSelect).toBeVisible()
    
    // 点击下拉框
    await responsiblePersonSelect.click()
    
    // 检查下拉选项格式（姓名 - 部门 - 职位）
    const options = page.locator('.el-select-dropdown .el-option')
    await expect(options.first()).toBeVisible()
    
    const firstOptionText = await options.first().textContent()
    expect(firstOptionText).toMatch(/^.+ - .+ - .+$/)
    
    // 选择一个选项
    await options.first().click()
    
    // 测试取消按钮
    const cancelButton = page.locator('button:has-text("取消")')
    await expect(cancelButton).toBeVisible()
    await cancelButton.click()
    
    // 检查弹窗是否关闭
    await expect(page.locator('.el-dialog')).not.toBeVisible()
  })

  test('子任务编辑弹窗确定按钮功能测试', async ({ page }) => {
    // 等待表格加载完成
    await page.waitForSelector('.table-section table', { timeout: 10000 })
    
    // 点击第一行的编辑按钮
    const editButton = page.locator('button:has-text("编辑")').first()
    await editButton.click()
    
    // 等待弹窗打开
    await expect(page.locator('.el-dialog')).toBeVisible()
    
    // 填写表单
    await page.fill('input[placeholder="请输入子任务名称"]', '测试任务名称')
    
    // 选择任务类型
    const taskTypeSelect = page.locator('label:has-text("子任务类型")').locator('..').locator('.el-select')
    await taskTypeSelect.click()
    await page.locator('.el-select-dropdown .el-option:has-text("业务报表")').click()
    
    // 选择优先级
    const prioritySelect = page.locator('label:has-text("子任务优先级设置")').locator('..').locator('.el-select')
    await prioritySelect.click()
    await page.locator('.el-select-dropdown .el-option:has-text("高")').click()
    
    // 选择负责人
    const responsiblePersonSelect = page.locator('label:has-text("责任人")').locator('..').locator('.el-select')
    await responsiblePersonSelect.click()
    await page.locator('.el-select-dropdown .el-option').first().click()
    
    // 填写参与人
    await page.fill('input[placeholder="请输入参与人，多人用逗号分隔"]', '测试参与人')
    
    // 点击确定按钮
    const confirmButton = page.locator('button:has-text("确定")')
    await confirmButton.click()
    
    // 检查成功消息（如果有的话）
    // 注意：这里可能需要根据实际的消息提示组件进行调整
    
    // 检查弹窗是否关闭
    await expect(page.locator('.el-dialog')).not.toBeVisible()
  })

  test('新增子任务功能测试', async ({ page }) => {
    // 点击新增子任务按钮
    const addButton = page.locator('button:has-text("新增子任务")')
    await addButton.click()
    
    // 检查弹窗是否打开
    await expect(page.locator('.el-dialog')).toBeVisible()
    await expect(page.locator('.el-dialog__title')).toContainText('业务表子任务编辑')
    
    // 检查表单字段是否为空
    const taskNameInput = page.locator('input[placeholder="请输入子任务名称"]')
    await expect(taskNameInput).toHaveValue('')
    
    // 检查负责人下拉框是否为空
    const responsiblePersonSelect = page.locator('label:has-text("责任人")').locator('..').locator('.el-select')
    const selectText = await responsiblePersonSelect.locator('.el-select__placeholder').textContent()
    expect(selectText).toContain('请选择责任人')
  })

  test('表单验证测试', async ({ page }) => {
    // 点击新增子任务按钮
    const addButton = page.locator('button:has-text("新增子任务")')
    await addButton.click()
    
    // 等待弹窗打开
    await expect(page.locator('.el-dialog')).toBeVisible()
    
    // 直接点击确定按钮，不填写任何内容
    const confirmButton = page.locator('button:has-text("确定")')
    await confirmButton.click()
    
    // 检查是否显示验证错误信息
    const errorMessages = page.locator('.el-form-item__error')
    await expect(errorMessages.first()).toBeVisible()
  })

  test('筛选功能测试', async ({ page }) => {
    // 测试任务名称筛选
    const taskNameInput = page.locator('input[placeholder="请输入任务名称"]')
    await taskNameInput.fill('永川区')
    
    const searchButton = page.locator('button:has-text("查询")')
    await searchButton.click()
    
    // 等待搜索结果
    await page.waitForTimeout(1000)
    
    // 测试重置功能
    const resetButton = page.locator('button:has-text("重置")')
    await resetButton.click()
    
    // 检查输入框是否被清空
    await expect(taskNameInput).toHaveValue('')
  })
})
