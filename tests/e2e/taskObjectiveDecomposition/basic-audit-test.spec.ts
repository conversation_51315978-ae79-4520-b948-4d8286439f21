import { test, expect } from '@playwright/test'

test.describe('子任务审核页面基础功能测试', () => {
  test('审核页面基本加载和显示测试', async ({ page }) => {
    // 直接导航到审核页面
    await page.goto('/taskObjectiveDecomposition/audit/1?free=true')
    
    // 等待页面加载完成
    await page.waitForSelector('.subtask-audit-page', { timeout: 30000 })
    
    // 验证页面标题
    await expect(page.locator('.page-title')).toHaveText('子任务审核')
    
    // 验证主要组件是否存在
    await expect(page.locator('.left-panel')).toBeVisible()
    await expect(page.locator('.right-panel')).toBeVisible()
    
    // 验证流程说明
    await expect(page.locator('.panel-title')).toHaveText('流程说明')
    await expect(page.locator('.step-item')).toHaveCount(4)
    
    // 验证任务信息卡片
    await expect(page.locator('.info-card .card-title')).toHaveText('任务信息')
    
    // 验证审核表单
    await expect(page.locator('.audit-form-card .card-title')).toHaveText('审核操作')
    
    // 验证审核历史
    await expect(page.locator('.history-card .card-title')).toHaveText('审核历史')
    
    // 验证相关文档
    await expect(page.locator('.documents-card .card-title')).toHaveText('相关文档')
    
    console.log('✅ 审核页面基本加载和显示测试通过')
  })

  test('审核表单基本交互测试', async ({ page }) => {
    // 导航到审核页面
    await page.goto('/taskObjectiveDecomposition/audit/1?free=true')
    await page.waitForSelector('.subtask-audit-page', { timeout: 30000 })
    
    // 验证表单元素存在
    const auditResultSelect = page.locator('.el-select').first()
    const auditCommentTextarea = page.locator('textarea').first()
    const submitButton = page.locator('button:has-text("提交审核")')
    const resetButton = page.locator('button:has-text("重置")')

    await expect(auditResultSelect).toBeVisible()
    await expect(auditCommentTextarea).toBeVisible()
    await expect(submitButton).toBeVisible()
    await expect(resetButton).toBeVisible()

    // 测试表单填写
    await auditResultSelect.click()
    await page.locator('.el-option:has-text("通过")').click()
    await auditCommentTextarea.fill('测试审核意见，数据填报完整，符合要求。')

    // 验证表单内容
    await expect(auditCommentTextarea).toHaveValue('测试审核意见，数据填报完整，符合要求。')

    // 测试重置功能
    await resetButton.click()
    await expect(auditCommentTextarea).toHaveValue('')
    
    console.log('✅ 审核表单基本交互测试通过')
  })

  test('页面导航功能测试', async ({ page }) => {
    // 导航到审核页面
    await page.goto('/taskObjectiveDecomposition/audit/1?free=true')
    await page.waitForSelector('.subtask-audit-page', { timeout: 30000 })
    
    // 验证返回按钮存在
    const returnButton = page.locator('.return-button')
    await expect(returnButton).toBeVisible()
    
    // 验证刷新按钮存在
    const refreshButton = page.locator('.refresh-button')
    await expect(refreshButton).toBeVisible()
    
    // 测试刷新功能
    await refreshButton.click()
    
    // 等待加载完成
    await page.waitForTimeout(2000)
    
    console.log('✅ 页面导航功能测试通过')
  })

  test('数据表格显示测试', async ({ page }) => {
    // 导航到审核页面
    await page.goto('/taskObjectiveDecomposition/audit/1?free=true')
    await page.waitForSelector('.subtask-audit-page', { timeout: 30000 })
    
    // 等待数据加载完成
    await page.waitForTimeout(5000)
    
    // 验证审核历史表格
    const historyTable = page.locator('.history-card')
    await expect(historyTable).toBeVisible()
    
    // 验证相关文档表格
    const documentsTable = page.locator('.documents-card')
    await expect(documentsTable).toBeVisible()
    
    console.log('✅ 数据表格显示测试通过')
  })

  test('响应式布局测试', async ({ page }) => {
    // 导航到审核页面
    await page.goto('/taskObjectiveDecomposition/audit/1?free=true')
    await page.waitForSelector('.subtask-audit-page', { timeout: 30000 })
    
    // 测试桌面端布局
    await page.setViewportSize({ width: 1400, height: 800 })
    await expect(page.locator('.left-panel')).toBeVisible()
    await expect(page.locator('.right-panel')).toBeVisible()
    
    // 测试移动端布局
    await page.setViewportSize({ width: 800, height: 600 })
    await expect(page.locator('.left-panel')).toBeVisible()
    await expect(page.locator('.right-panel')).toBeVisible()
    
    console.log('✅ 响应式布局测试通过')
  })
})
