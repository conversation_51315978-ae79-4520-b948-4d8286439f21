import { test, expect } from '@playwright/test'

test.describe('子任务详情页面', () => {
  test.beforeEach(async ({ page }) => {
    // 先访问任务详情页面
    await page.goto('/taskObjectiveDecomposition/detail/1?free=true')
    await page.waitForLoadState('networkidle')

    // 等待页面标题加载
    await page.waitForSelector('h2:has-text("任务目标拆解")', { timeout: 10000 })

    // 等待表格加载完成
    await page.waitForSelector('.el-table', { timeout: 10000 })
    await page.waitForTimeout(3000)
  })

  test('页面基本元素加载', async ({ page }) => {
    // 等待详情按钮可见并点击
    const detailButton = page.locator('.el-button.el-button--info').first()
    await detailButton.waitFor({ state: 'visible', timeout: 10000 })
    await detailButton.click()
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000)

    // 检查页面标题
    await expect(page.locator('h2')).toContainText('子任务详情')

    // 检查返回按钮
    await expect(page.getByRole('button', { name: '返回', exact: true })).toBeVisible()

    // 检查子任务基本信息卡片
    await expect(page.locator('h3').filter({ hasText: '子任务基本信息' })).toBeVisible()

    // 检查进度信息卡片
    await expect(page.locator('h3').filter({ hasText: '进度信息' })).toBeVisible()

    // 检查所属任务信息卡片
    await expect(page.locator('h3').filter({ hasText: '所属任务信息' })).toBeVisible()
  })

  test('子任务基本信息显示正确', async ({ page }) => {
    // 点击第一个子任务的详情按钮
    await page.locator('.el-button.el-button--info').first().click()
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(1000)

    // 验证子任务基本信息
    await expect(page.locator('text=子任务名称：')).toBeVisible()
    await expect(page.locator('text=永川区民政局填报流程')).toBeVisible()
    
    await expect(page.locator('text=子任务类型：')).toBeVisible()
    await expect(page.locator('text=业务报表')).toBeVisible()
    
    await expect(page.locator('text=子任务分类：')).toBeVisible()
    await expect(page.locator('text=党的建设')).toBeVisible()
    
    await expect(page.locator('text=责任人：')).toBeVisible()
    await expect(page.locator('text=张三')).toBeVisible()
    
    await expect(page.locator('text=参与人：')).toBeVisible()
    await expect(page.locator('text=朱沱镇-公共服务-王五等')).toBeVisible()
    
    await expect(page.locator('text=任务状态：')).toBeVisible()
    await expect(page.locator('.el-tag').filter({ hasText: '待审核' })).toBeVisible()
    
    await expect(page.locator('text=序号：')).toBeVisible()
    await expect(page.locator('text=1').last()).toBeVisible()
  })

  test('进度信息显示正确', async ({ page }) => {
    // 点击第一个子任务的详情按钮
    await page.locator('.el-button.el-button--info').first().click()
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(1000)

    // 验证进度信息
    await expect(page.locator('text=任务进度：')).toBeVisible()
    await expect(page.locator('.el-progress')).toBeVisible()
    await expect(page.locator('text=30%')).toBeVisible()
    
    await expect(page.locator('text=创建时间：')).toBeVisible()
    await expect(page.locator('text=2024-04-01')).toBeVisible()
    
    await expect(page.locator('text=更新时间：')).toBeVisible()
  })

  test('所属任务信息显示正确', async ({ page }) => {
    // 点击第一个子任务的详情按钮
    await page.locator('.el-button.el-button--info').first().click()
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(1000)

    // 验证所属任务信息
    await expect(page.locator('text=任务名称：').last()).toBeVisible()
    await expect(page.locator('text=数据收集')).toBeVisible()
    
    await expect(page.locator('text=任务类型：').last()).toBeVisible()
    await expect(page.locator('text=报表')).toBeVisible()
    
    await expect(page.locator('text=创建部门：')).toBeVisible()
    await expect(page.locator('text=永川区民政局-安全科-程飞')).toBeVisible()
    
    await expect(page.locator('text=开始时间：').last()).toBeVisible()
    await expect(page.locator('text=2024-01-01')).toBeVisible()
    
    await expect(page.locator('text=执行周期：')).toBeVisible()
    await expect(page.locator('text=一次性')).toBeVisible()
    
    await expect(page.locator('text=任务状态：').last()).toBeVisible()
    await expect(page.locator('.el-tag').filter({ hasText: '执行中' })).toBeVisible()
  })

  test('返回功能正常', async ({ page }) => {
    // 点击第一个子任务的详情按钮
    await page.locator('.el-button.el-button--info').first().click()
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(1000)

    // 验证在子任务详情页面
    await expect(page.locator('h2')).toContainText('子任务详情')

    // 点击返回按钮
    await page.getByRole('button', { name: '返回', exact: true }).click()
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(1000)

    // 验证返回到任务详情页面
    await expect(page.locator('h2')).toContainText('任务目标拆解')
    await expect(page.locator('h3').filter({ hasText: '任务基本信息' })).toBeVisible()
    await expect(page.locator('h3').filter({ hasText: '详情' })).toBeVisible()
  })

  test('不同子任务数据显示正确', async ({ page }) => {
    // 测试第二个子任务
    await page.locator('.el-table__row').nth(1).locator('.el-button.el-button--info').click()
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(1000)

    // 验证第二个子任务的数据
    await expect(page.locator('text=永川区民政局填报流程')).toBeVisible()
    await expect(page.locator('text=李四')).toBeVisible() // 第二个子任务的责任人
    await expect(page.locator('.el-tag').filter({ hasText: '执行中' })).toBeVisible() // 第二个子任务的状态
    await expect(page.locator('text=2').last()).toBeVisible() // 序号为2

    // 返回并测试第三个子任务
    await page.getByRole('button', { name: '返回', exact: true }).click()
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(1000)

    await page.locator('.el-table__row').nth(2).locator('.el-button.el-button--info').click()
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(1000)

    // 验证第三个子任务的数据
    await expect(page.locator('text=系统填报流程')).toBeVisible()
    await expect(page.locator('text=王五')).toBeVisible() // 第三个子任务的责任人
    await expect(page.locator('text=民生服务')).toBeVisible() // 第三个子任务的分类
    await expect(page.locator('text=3').last()).toBeVisible() // 序号为3
  })

  test('路由参数正确传递', async ({ page }) => {
    // 点击第一个子任务的详情按钮
    await page.locator('.el-button.el-button--info').first().click()
    await page.waitForLoadState('networkidle')

    // 验证URL包含正确的子任务ID
    expect(page.url()).toContain('/taskObjectiveDecomposition/subtask/st1')
    expect(page.url()).toContain('free=true')

    // 返回并测试第二个子任务
    await page.getByRole('button', { name: '返回', exact: true }).click()
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(1000)

    await page.locator('.el-table__row').nth(1).locator('.el-button.el-button--info').click()
    await page.waitForLoadState('networkidle')

    // 验证第二个子任务的URL
    expect(page.url()).toContain('/taskObjectiveDecomposition/subtask/st2')
    expect(page.url()).toContain('free=true')
  })

  test('页面样式和布局正确', async ({ page }) => {
    // 点击第一个子任务的详情按钮
    await page.locator('.el-button.el-button--info').first().click()
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(1000)

    // 验证页面布局
    await expect(page.locator('.subtask-detail-page')).toBeVisible()
    await expect(page.locator('.page-header')).toBeVisible()
    await expect(page.locator('.page-content')).toBeVisible()

    // 验证卡片组件
    await expect(page.locator('.info-card')).toBeVisible()
    await expect(page.locator('.progress-card')).toBeVisible()
    await expect(page.locator('.parent-task-card')).toBeVisible()

    // 验证进度条组件
    await expect(page.locator('.el-progress')).toBeVisible()
    await expect(page.locator('.el-progress-bar')).toBeVisible()

    // 验证标签组件
    await expect(page.locator('.el-tag')).toHaveCount(2) // 任务状态和父任务状态
  })
})
