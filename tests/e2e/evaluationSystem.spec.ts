import { test, expect } from '@playwright/test'

test.describe('评价体系功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 清除localStorage确保测试环境干净
    await page.goto('/?free=true')
    await page.evaluate(() => {
      localStorage.removeItem('evaluation_system_data')
    })

    // 导航到评价体系页面，使用免登录参数
    await page.goto('/evaluationSystem?free=true')
    await page.waitForLoadState('networkidle')
  })

  test('页面加载和基本元素显示', async ({ page }) => {
    // 检查页面标题 - 使用更精确的选择器
    await expect(page.locator('text=评价体系').first()).toBeVisible()

    // 检查新增按钮
    await expect(page.locator('button:has-text("新增")')).toBeVisible()

    // 检查搜索表单
    await expect(page.locator('input[placeholder*="审批部门"]')).toBeVisible()
    await expect(page.locator('input[placeholder*="审批角色"]')).toBeVisible()

    // 检查表格是否显示
    await expect(page.locator('table').first()).toBeVisible()

    // 检查表格列标题
    await expect(page.locator('text=审批部门')).toBeVisible()
    await expect(page.locator('text=审批角色')).toBeVisible()
    await expect(page.locator('text=分类')).toBeVisible()
    await expect(page.locator('text=评价语句')).toBeVisible()
    await expect(page.locator('text=操作')).toBeVisible()
  })

  test('模拟数据加载', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(2000)

    // 检查是否有数据行
    const rows = page.locator('table tbody tr')
    const rowCount = await rows.count()
    expect(rowCount).toBeGreaterThan(0)

    // 检查分页组件
    await expect(page.locator('text=共 25 条')).toBeVisible()
  })

  test('新增功能测试', async ({ page }) => {
    // 点击新增按钮
    await page.click('button:has-text("新增")')

    // 检查弹窗是否打开
    await expect(page.locator('text=评价体系设置')).toBeVisible()

    // 填写表单 - 使用点击下拉选项的方式
    await page.click('combobox[placeholder*="审批部门"]')
    await page.click('text=财政部')

    await page.click('combobox[placeholder*="审批角色"]')
    await page.click('text=主任')

    await page.click('combobox[placeholder*="分类"]')
    await page.click('text=常用')

    await page.fill('textbox[placeholder*="评价语句"]', '这是一个测试评价语句')

    // 提交表单
    await page.click('button:has-text("确定")')

    // 检查成功消息
    await expect(page.locator('text=新增成功')).toBeVisible()

    // 检查弹窗是否关闭
    await expect(page.locator('text=评价体系设置')).not.toBeVisible()

    // 验证数据是否添加到表格中
    await expect(page.locator('text=这是一个测试评价语句')).toBeVisible()
  })

  test('编辑功能测试', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(2000)

    // 点击第一行的编辑按钮
    await page.click('table tbody tr:first-child button:has-text("编辑")')

    // 检查编辑弹窗是否打开
    await expect(page.locator('text=编辑评价体系')).toBeVisible()

    // 修改评价语句
    await page.fill('textbox[placeholder*="评价语句"]', '修改后的评价语句')

    // 提交表单
    await page.click('button:has-text("确定")')

    // 检查成功消息
    await expect(page.locator('text=编辑成功')).toBeVisible()

    // 验证数据是否更新
    await expect(page.locator('text=修改后的评价语句')).toBeVisible()
  })

  test('删除功能测试', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(2000)

    // 获取删除前的行数
    const initialRowCount = await page.locator('table tbody tr').count()

    // 点击第一行的删除按钮
    await page.click('table tbody tr:first-child button:has-text("删除")')

    // 确认删除
    await page.click('button:has-text("确定")')

    // 检查成功消息
    await expect(page.locator('text=删除成功')).toBeVisible()

    // 验证行数是否减少
    await page.waitForTimeout(500)
    const finalRowCount = await page.locator('table tbody tr').count()
    expect(finalRowCount).toBe(initialRowCount - 1)
  })

  test('搜索功能测试', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(1000)
    
    // 在审批部门搜索框中输入
    await page.fill('input[placeholder*="审批部门"]', '财政部')
    
    // 点击查询按钮
    await page.click('button:has-text("查询")')
    
    // 等待搜索结果
    await page.waitForTimeout(500)
    
    // 验证搜索结果只包含财政部的数据
    const rows = page.locator('.el-table tbody tr')
    const rowCount = await rows.count()
    
    if (rowCount > 0) {
      // 检查每一行是否都包含"财政部"
      for (let i = 0; i < rowCount; i++) {
        await expect(rows.nth(i)).toContainText('财政部')
      }
    }
  })

  test('分页功能测试', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(1000)
    
    // 检查分页组件是否存在
    await expect(page.locator('.el-pagination')).toBeVisible()
    
    // 如果有多页，测试翻页功能
    const nextButton = page.locator('.el-pagination .btn-next')
    if (await nextButton.isEnabled()) {
      // 点击下一页
      await nextButton.click()
      await page.waitForTimeout(500)
      
      // 检查页码是否改变
      await expect(page.locator('.el-pagination .number.is-active')).toContainText('2')
      
      // 点击上一页
      await page.click('.el-pagination .btn-prev')
      await page.waitForTimeout(500)
      
      // 检查是否回到第一页
      await expect(page.locator('.el-pagination .number.is-active')).toContainText('1')
    }
  })

  test('表单验证测试', async ({ page }) => {
    // 点击新增按钮
    await page.click('button:has-text("新增")')
    
    // 不填写任何内容直接提交
    await page.click('button:has-text("确定")')
    
    // 检查验证错误消息
    await expect(page.locator('text=请选择审批部门')).toBeVisible()
    await expect(page.locator('text=请选择审批角色')).toBeVisible()
    await expect(page.locator('text=请输入评价语句')).toBeVisible()
  })

  test('数据持久化测试', async ({ page }) => {
    // 添加一条新数据
    await page.click('button:has-text("新增")')
    await page.selectOption('select[placeholder*="审批部门"]', '财政部')
    await page.selectOption('select[placeholder*="审批角色"]', '主任')
    await page.selectOption('select[placeholder*="分类"]', '常用')
    await page.fill('textarea[placeholder*="评价语句"]', '持久化测试数据')
    await page.click('button:has-text("确定")')
    
    // 等待保存完成
    await page.waitForTimeout(1000)
    
    // 刷新页面
    await page.reload()
    await page.waitForLoadState('networkidle')
    
    // 验证数据是否仍然存在
    await expect(page.locator('text=持久化测试数据')).toBeVisible()
  })

  test('响应式设计测试', async ({ page }) => {
    // 测试不同屏幕尺寸
    await page.setViewportSize({ width: 1200, height: 800 })
    await expect(page.locator('.el-table')).toBeVisible()
    
    await page.setViewportSize({ width: 768, height: 600 })
    await expect(page.locator('.el-table')).toBeVisible()
    
    await page.setViewportSize({ width: 480, height: 600 })
    await expect(page.locator('.el-table')).toBeVisible()
  })
})
