import { test, expect } from '@playwright/test'

test.describe('评价体系功能测试 - 简化版', () => {
  test.beforeEach(async ({ page }) => {
    // 清除localStorage确保测试环境干净
    await page.goto('/?free=true')
    await page.evaluate(() => {
      localStorage.removeItem('evaluation_system_data')
    })
    
    // 导航到评价体系页面，使用免登录参数
    await page.goto('/evaluationSystem?free=true')
    await page.waitForLoadState('networkidle')
  })

  test('页面基本功能验证', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('text=评价体系').first()).toBeVisible()
    
    // 检查新增按钮
    await expect(page.locator('button:has-text("新增")')).toBeVisible()
    
    // 检查搜索表单
    await expect(page.locator('textbox[placeholder*="审批部门"]')).toBeVisible()
    await expect(page.locator('textbox[placeholder*="审批角色"]')).toBeVisible()
    
    // 检查表格列标题
    await expect(page.locator('text=审批部门')).toBeVisible()
    await expect(page.locator('text=审批角色')).toBeVisible()
    await expect(page.locator('text=分类')).toBeVisible()
    await expect(page.locator('text=评价语句')).toBeVisible()
    await expect(page.locator('text=操作')).toBeVisible()
    
    // 检查是否有数据行
    const rows = page.locator('table tbody tr')
    const rowCount = await rows.count()
    expect(rowCount).toBeGreaterThan(0)
    
    // 检查分页信息
    await expect(page.locator('text=共 25 条')).toBeVisible()
  })

  test('新增对话框打开测试', async ({ page }) => {
    // 点击新增按钮
    await page.click('button:has-text("新增")')
    
    // 检查弹窗是否打开
    await expect(page.locator('text=评价体系设置')).toBeVisible()
    
    // 检查表单字段
    await expect(page.locator('text=审批部门')).toBeVisible()
    await expect(page.locator('text=审批角色')).toBeVisible()
    await expect(page.locator('text=分类')).toBeVisible()
    await expect(page.locator('text=评价语句')).toBeVisible()
    
    // 检查按钮
    await expect(page.locator('button:has-text("确定")')).toBeVisible()
    await expect(page.locator('button:has-text("关闭")')).toBeVisible()
    
    // 关闭对话框
    await page.click('button:has-text("关闭")')
    await expect(page.locator('text=评价体系设置')).not.toBeVisible()
  })

  test('编辑对话框打开测试', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(2000)
    
    // 点击第一行的编辑按钮
    await page.click('table tbody tr:first-child button:has-text("编辑")')
    
    // 检查编辑弹窗是否打开
    await expect(page.locator('text=编辑评价体系')).toBeVisible()
    
    // 关闭对话框
    await page.click('button:has-text("关闭")')
    await expect(page.locator('text=编辑评价体系')).not.toBeVisible()
  })

  test('搜索功能测试', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(2000)
    
    // 在审批部门搜索框中输入
    await page.fill('textbox[placeholder*="审批部门"]', '财政部')
    
    // 点击查询按钮
    await page.click('button:has-text("查询")')
    
    // 等待搜索结果
    await page.waitForTimeout(1000)
    
    // 验证搜索结果包含财政部
    await expect(page.locator('text=财政部')).toBeVisible()
  })

  test('分页功能测试', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(2000)
    
    // 检查分页组件是否存在
    await expect(page.locator('text=共 25 条')).toBeVisible()
    
    // 检查页码按钮
    const pageButtons = page.locator('list listitem')
    const buttonCount = await pageButtons.count()
    expect(buttonCount).toBeGreaterThan(0)
    
    // 如果有第二页，测试翻页功能
    const page2Button = page.locator('text="2"')
    if (await page2Button.isVisible()) {
      await page2Button.click()
      await page.waitForTimeout(500)
      
      // 验证页面已切换
      await expect(page.locator('text="2"')).toBeVisible()
    }
  })

  test('数据持久化验证', async ({ page }) => {
    // 等待数据加载
    await page.waitForTimeout(2000)
    
    // 获取第一行数据作为参考
    const firstRowText = await page.locator('table tbody tr:first-child').textContent()
    
    // 刷新页面
    await page.reload()
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000)
    
    // 验证数据是否仍然存在
    const firstRowTextAfterReload = await page.locator('table tbody tr:first-child').textContent()
    expect(firstRowTextAfterReload).toBe(firstRowText)
  })
})
