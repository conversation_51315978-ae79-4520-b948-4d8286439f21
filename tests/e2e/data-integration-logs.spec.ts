import { test, expect } from '@playwright/test'

test.describe('数据集成日志功能', () => {
  test.beforeEach(async ({ page }) => {
    // 清除localStorage
    await page.evaluate(() => {
      localStorage.clear()
    })
    
    // 导航到数据集成页面
    await page.goto('/reportIntegrationSpotCheck/dataIntegration')
    await page.waitForLoadState('networkidle')
  })

  test('应该能够生成和查看日志', async ({ page }) => {
    // 等待页面加载完成
    await expect(page.locator('text=数据集成')).toBeVisible()
    
    // 查找第一个任务的生成日志按钮
    const generateLogButton = page.locator('button:has-text("生成")').first()
    await expect(generateLogButton).toBeVisible()
    
    // 点击生成日志
    await generateLogButton.click()
    
    // 等待成功消息
    await expect(page.locator('text=日志生成成功')).toBeVisible()
    
    // 验证按钮变为"查看"
    await expect(page.locator('button:has-text("查看")').first()).toBeVisible()
    
    // 点击查看日志
    await page.locator('button:has-text("查看")').first().click()
    
    // 验证日志弹窗打开
    await expect(page.locator('text=操作日志')).toBeVisible()
    
    // 验证日志表格存在
    await expect(page.locator('.el-table')).toBeVisible()
    
    // 验证日志列标题
    await expect(page.locator('text=序号')).toBeVisible()
    await expect(page.locator('text=操作类型')).toBeVisible()
    await expect(page.locator('text=操作时间')).toBeVisible()
    await expect(page.locator('text=操作人员')).toBeVisible()
    await expect(page.locator('text=操作结果')).toBeVisible()
    await expect(page.locator('text=详细描述')).toBeVisible()
    await expect(page.locator('text=影响记录数')).toBeVisible()
  })

  test('应该能够搜索日志', async ({ page }) => {
    // 先生成日志
    await page.locator('button:has-text("生成")').first().click()
    await expect(page.locator('text=日志生成成功')).toBeVisible()
    
    // 打开日志弹窗
    await page.locator('button:has-text("查看")').first().click()
    await expect(page.locator('text=操作日志')).toBeVisible()
    
    // 在搜索框中输入关键词
    const searchInput = page.locator('input[placeholder*="搜索操作人员"]')
    await searchInput.fill('张三')
    
    // 验证搜索结果
    await page.waitForTimeout(500) // 等待搜索完成
  })

  test('创建任务时应该自动记录日志', async ({ page }) => {
    // 点击创建任务按钮
    await page.locator('button:has-text("创建任务")').click()
    
    // 填写表单
    await page.locator('input[placeholder*="集成任务名称"]').fill('测试任务')
    
    // 提交表单
    await page.locator('button:has-text("确定")').click()
    
    // 等待成功消息
    await expect(page.locator('text=创建任务成功')).toBeVisible()
    
    // 查找新创建的任务并生成日志（如果需要）
    const taskRow = page.locator('text=测试任务').locator('..').locator('..')
    const logButton = taskRow.locator('button').filter({ hasText: /生成|查看/ })
    
    // 如果是"查看"按钮，说明已经有日志了
    const buttonText = await logButton.textContent()
    if (buttonText?.includes('查看')) {
      await logButton.click()
      
      // 验证日志中包含创建操作
      await expect(page.locator('text=数据新增')).toBeVisible()
      await expect(page.locator('text=创建新的集成任务')).toBeVisible()
    }
  })

  test('编辑任务时应该自动记录日志', async ({ page }) => {
    // 点击第一个任务的编辑按钮
    await page.locator('button:has-text("编辑")').first().click()
    
    // 修改任务名称
    const nameInput = page.locator('input[placeholder*="集成任务名称"]')
    await nameInput.clear()
    await nameInput.fill('修改后的任务名称')
    
    // 提交修改
    await page.locator('button:has-text("确定")').click()
    
    // 等待成功消息
    await expect(page.locator('text=编辑任务成功')).toBeVisible()
    
    // 查看日志验证编辑操作被记录
    const taskRow = page.locator('text=修改后的任务名称').locator('..').locator('..')
    const logButton = taskRow.locator('button').filter({ hasText: /生成|查看/ })
    
    const buttonText = await logButton.textContent()
    if (buttonText?.includes('查看')) {
      await logButton.click()
      
      // 验证日志中包含修改操作
      await expect(page.locator('text=数据修改')).toBeVisible()
    }
  })

  test('导出任务时应该自动记录日志', async ({ page }) => {
    // 选择第一个任务
    await page.locator('.el-checkbox').first().click()
    
    // 点击批量导出
    await page.locator('button:has-text("批量导出")').click()
    
    // 等待导出成功消息
    await expect(page.locator('text=导出成功')).toBeVisible()
    
    // 验证日志记录（需要先生成日志才能查看）
    const logButton = page.locator('button').filter({ hasText: /生成|查看/ }).first()
    const buttonText = await logButton.textContent()
    
    if (buttonText?.includes('生成')) {
      await logButton.click()
      await expect(page.locator('text=日志生成成功')).toBeVisible()
    }
    
    await page.locator('button:has-text("查看")').first().click()
    
    // 验证日志中包含导出操作
    await expect(page.locator('text=数据导出')).toBeVisible()
  })
})
