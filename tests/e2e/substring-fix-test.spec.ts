import { test, expect } from '@playwright/test'

test.describe('修复substring错误测试', () => {
  test('验证页面加载不会出现substring错误', async ({ page }) => {
    // 监听控制台错误
    const consoleErrors: string[] = []
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    // 监听页面错误
    const pageErrors: string[] = []
    page.on('pageerror', error => {
      pageErrors.push(error.message)
    })

    // 访问首页，使用免登录参数
    await page.goto('/?free=true')
    
    // 等待页面加载
    await page.waitForTimeout(5000)
    
    // 检查是否有substring相关的错误
    const substringErrors = [...consoleErrors, ...pageErrors].filter(error => 
      error.includes('substring') || error.includes('Cannot read properties of undefined')
    )
    
    console.log('控制台错误:', consoleErrors)
    console.log('页面错误:', pageErrors)
    console.log('substring相关错误:', substringErrors)
    
    // 断言：不应该有substring相关的错误
    expect(substringErrors.length).toBe(0)
    
    // 验证页面基本元素存在
    await expect(page.locator('body')).toBeVisible()
  })

  test('验证任务详情页面加载不会出现substring错误', async ({ page }) => {
    // 监听控制台错误
    const consoleErrors: string[] = []
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })

    // 监听页面错误
    const pageErrors: string[] = []
    page.on('pageerror', error => {
      pageErrors.push(error.message)
    })

    // 访问任务详情页面，使用免登录参数
    await page.goto('/taskObjectiveDecomposition/detail/1?free=true')
    
    // 等待页面加载
    await page.waitForTimeout(8000)
    
    // 检查是否有substring相关的错误
    const substringErrors = [...consoleErrors, ...pageErrors].filter(error => 
      error.includes('substring') || error.includes('Cannot read properties of undefined')
    )
    
    console.log('控制台错误:', consoleErrors)
    console.log('页面错误:', pageErrors)
    console.log('substring相关错误:', substringErrors)
    
    // 断言：不应该有substring相关的错误
    expect(substringErrors.length).toBe(0)
    
    // 验证页面基本元素存在
    await expect(page.locator('body')).toBeVisible()
  })
})
