import { test, expect } from '@playwright/test'

test.describe('任务目标拆解详情页面 - 上移下移功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 直接访问详情页面，使用免登录参数
    await page.goto('/taskObjectiveDecomposition/detail/1?free=true')

    // 等待页面基本加载完成
    await page.waitForLoadState('domcontentloaded')
    await page.waitForTimeout(5000) // 等待数据加载
  })

  test('验证子任务列表数据存在', async ({ page }) => {
    // 等待表格完全加载
    await page.waitForSelector('.table-section .el-table', { timeout: 10000 })

    // 检查表格是否存在
    await expect(page.locator('.table-section .el-table')).toBeVisible()

    // 检查表格体是否存在
    const tableBody = page.locator('.table-section .el-table tbody')
    await expect(tableBody).toBeVisible()

    // 等待数据加载
    await page.waitForTimeout(2000)

    // 检查是否有数据行
    const rowCount = await page.locator('.table-section .el-table tbody tr').count()
    console.log('子任务数据行数:', rowCount)

    // 应该至少有一行数据
    expect(rowCount).toBeGreaterThan(0)
  })

  test('验证第一行只显示下移按钮', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForTimeout(3000)

    // 获取第一行数据
    const firstRow = page.locator('.table-section .el-table tbody tr').first()
    await expect(firstRow).toBeVisible()

    // 点击第一行的更多操作按钮
    const moreButton = firstRow.locator('button:has-text("更多")')
    await expect(moreButton).toBeVisible()
    await moreButton.click()

    // 等待下拉菜单出现
    await page.waitForTimeout(1000)

    // 检查下移按钮是否存在
    const moveDownButton = page.getByRole('menuitem', { name: '下移' })
    await expect(moveDownButton).toBeVisible()

    // 检查上移按钮是否不存在（第一行不应该有上移按钮）
    const moveUpButton = page.getByRole('menuitem', { name: '上移' })
    const moveUpExists = await moveUpButton.isVisible().catch(() => false)
    
    if (moveUpExists) {
      // 如果上移按钮存在，检查它是否被禁用
      const isDisabled = await moveUpButton.isDisabled().catch(() => true)
      expect(isDisabled).toBeTruthy()
    }

    // 关闭下拉菜单
    await page.keyboard.press('Escape')
  })

  test('验证最后一行只显示上移按钮', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForTimeout(3000)

    // 获取所有数据行
    const allRows = page.locator('.table-section .el-table tbody tr')
    const rowCount = await allRows.count()
    
    if (rowCount > 1) {
      // 获取最后一行数据
      const lastRow = allRows.nth(rowCount - 1)
      await expect(lastRow).toBeVisible()

      // 点击最后一行的更多操作按钮
      const moreButton = lastRow.locator('button:has-text("更多")')
      await expect(moreButton).toBeVisible()
      await moreButton.click()

      // 等待下拉菜单出现
      await page.waitForTimeout(1000)

      // 检查上移按钮是否存在
      const moveUpButton = page.getByRole('menuitem', { name: '上移' })
      await expect(moveUpButton).toBeVisible()

      // 检查下移按钮是否不存在（最后一行不应该有下移按钮）
      const moveDownButton = page.getByRole('menuitem', { name: '下移' })
      const moveDownExists = await moveDownButton.isVisible().catch(() => false)
      
      if (moveDownExists) {
        // 如果下移按钮存在，检查它是否被禁用
        const isDisabled = await moveDownButton.isDisabled().catch(() => true)
        expect(isDisabled).toBeTruthy()
      }

      // 关闭下拉菜单
      await page.keyboard.press('Escape')
    } else {
      console.log('只有一行数据，跳过最后一行测试')
    }
  })

  test('验证中间行同时显示上移和下移按钮', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForTimeout(3000)

    // 获取所有数据行
    const allRows = page.locator('.table-section .el-table tbody tr')
    const rowCount = await allRows.count()
    
    if (rowCount > 2) {
      // 获取中间的一行数据（第二行）
      const middleRow = allRows.nth(1)
      await expect(middleRow).toBeVisible()

      // 点击中间行的更多操作按钮
      const moreButton = middleRow.locator('button:has-text("更多")')
      await expect(moreButton).toBeVisible()
      await moreButton.click()

      // 等待下拉菜单出现
      await page.waitForTimeout(1000)

      // 检查上移按钮是否存在且可用
      const moveUpButton = page.getByRole('menuitem', { name: '上移' })
      await expect(moveUpButton).toBeVisible()

      // 检查下移按钮是否存在且可用
      const moveDownButton = page.getByRole('menuitem', { name: '下移' })
      await expect(moveDownButton).toBeVisible()

      // 关闭下拉菜单
      await page.keyboard.press('Escape')
    } else {
      console.log('数据行数不足3行，跳过中间行测试')
    }
  })

  test('测试下移功能', async ({ page }) => {
    // 监听控制台日志
    const consoleLogs: string[] = []
    page.on('console', msg => {
      consoleLogs.push(`${msg.type()}: ${msg.text()}`)
    })

    // 等待表格数据加载
    await page.waitForTimeout(3000)

    // 获取所有数据行
    const allRows = page.locator('.table-section .el-table tbody tr')
    const rowCount = await allRows.count()

    if (rowCount > 1) {
      // 获取第一行的任务名称
      const firstRowTaskName = await allRows.first().locator('td').nth(1).textContent()
      console.log('第一行任务名称:', firstRowTaskName)

      // 点击第一行的更多操作按钮
      const moreButton = allRows.first().locator('button:has-text("更多")')
      await moreButton.click()

      // 等待下拉菜单出现
      await page.waitForTimeout(1000)

      // 点击下移按钮
      const moveDownButton = page.getByRole('menuitem', { name: '下移' })
      await expect(moveDownButton).toBeVisible()
      await moveDownButton.click()

      // 等待操作完成
      await page.waitForTimeout(5000) // 增加等待时间

      // 检查是否有成功或警告消息
      const successMessage = page.locator('.el-message--success')
      const warningMessage = page.locator('.el-message--warning')

      const hasSuccess = await successMessage.count() > 0
      const hasWarning = await warningMessage.count() > 0

      console.log('有成功消息:', hasSuccess)
      console.log('有警告消息:', hasWarning)

      // 验证数据顺序已改变
      const newFirstRowTaskName = await allRows.first().locator('td').nth(1).textContent()
      console.log('下移后第一行任务名称:', newFirstRowTaskName)

      // 打印控制台日志
      console.log('浏览器控制台日志:')
      consoleLogs.forEach(log => console.log(log))

      // 如果有成功消息，第一行的任务名称应该已经改变
      if (hasSuccess) {
        expect(newFirstRowTaskName).not.toBe(firstRowTaskName)
      } else {
        console.log('没有成功消息，可能操作失败或已经是边界条件')
      }
    } else {
      console.log('数据行数不足，跳过下移测试')
    }
  })

  test('测试上移功能', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForTimeout(3000)

    // 获取所有数据行
    const allRows = page.locator('.table-section .el-table tbody tr')
    const rowCount = await allRows.count()
    
    if (rowCount > 1) {
      // 获取第二行的任务名称
      const secondRowTaskName = await allRows.nth(1).locator('td').nth(1).textContent()
      console.log('第二行任务名称:', secondRowTaskName)

      // 点击第二行的更多操作按钮
      const moreButton = allRows.nth(1).locator('button:has-text("更多")')
      await moreButton.click()

      // 等待下拉菜单出现
      await page.waitForTimeout(1000)

      // 点击上移按钮
      const moveUpButton = page.getByRole('menuitem', { name: '上移' })
      await expect(moveUpButton).toBeVisible()
      await moveUpButton.click()

      // 等待操作完成
      await page.waitForTimeout(2000)

      // 检查成功消息
      await expect(page.locator('.el-message--success')).toBeVisible()

      // 验证数据顺序已改变
      const newFirstRowTaskName = await allRows.first().locator('td').nth(1).textContent()
      console.log('上移后第一行任务名称:', newFirstRowTaskName)

      // 第一行的任务名称应该是原来第二行的任务名称
      expect(newFirstRowTaskName).toBe(secondRowTaskName)
    } else {
      console.log('数据行数不足，跳过上移测试')
    }
  })

  test('测试边界条件 - 第一行上移应该显示警告', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForTimeout(3000)

    // 获取第一行
    const firstRow = page.locator('.table-section .el-table tbody tr').first()
    
    // 点击第一行的更多操作按钮
    const moreButton = firstRow.locator('button:has-text("更多")')
    await moreButton.click()

    // 等待下拉菜单出现
    await page.waitForTimeout(1000)

    // 检查上移按钮是否不可见或被禁用
    const moveUpButton = page.getByRole('menuitem', { name: '上移' })
    const moveUpExists = await moveUpButton.isVisible().catch(() => false)
    
    if (moveUpExists) {
      // 如果上移按钮存在，点击它应该显示警告消息
      await moveUpButton.click()
      
      // 等待警告消息出现
      await page.waitForTimeout(1000)
      
      // 检查警告消息
      await expect(page.locator('.el-message--warning')).toBeVisible()
    }

    // 关闭下拉菜单
    await page.keyboard.press('Escape')
  })

  test('测试边界条件 - 最后一行下移应该显示警告', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForTimeout(3000)

    // 获取所有数据行
    const allRows = page.locator('.table-section .el-table tbody tr')
    const rowCount = await allRows.count()
    
    if (rowCount > 1) {
      // 获取最后一行
      const lastRow = allRows.nth(rowCount - 1)
      
      // 点击最后一行的更多操作按钮
      const moreButton = lastRow.locator('button:has-text("更多")')
      await moreButton.click()

      // 等待下拉菜单出现
      await page.waitForTimeout(1000)

      // 检查下移按钮是否不可见或被禁用
      const moveDownButton = page.getByRole('menuitem', { name: '下移' })
      const moveDownExists = await moveDownButton.isVisible().catch(() => false)
      
      if (moveDownExists) {
        // 如果下移按钮存在，点击它应该显示警告消息
        await moveDownButton.click()
        
        // 等待警告消息出现
        await page.waitForTimeout(1000)
        
        // 检查警告消息
        await expect(page.locator('.el-message--warning')).toBeVisible()
      }

      // 关闭下拉菜单
      await page.keyboard.press('Escape')
    }
  })
})
