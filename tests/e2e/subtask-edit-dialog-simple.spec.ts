import { test, expect } from '@playwright/test'

test.describe('编辑子任务弹窗测试 - 简化版', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到任务目标拆解详情页面，使用free=true跳过认证
    await page.goto('/taskObjectiveDecomposition/detail/1?free=true')
    await page.waitForLoadState('networkidle')
    
    // 等待页面完全加载
    await page.waitForTimeout(2000)
  })

  test('应该能够打开和关闭编辑子任务弹窗', async ({ page }) => {
    // 点击第一个子任务的编辑按钮
    await page.locator('table tbody tr:first-child').getByRole('button', { name: '编辑' }).click()

    // 等待弹窗出现
    await page.waitForTimeout(1000)

    // 验证弹窗标题
    await expect(page.locator('[aria-label="业务表子任务编辑"]')).toBeVisible()

    // 验证必填字段存在（使用更精确的选择器，只检查弹窗内的标签）
    await expect(page.locator('[aria-label="业务表子任务编辑"] label').getByText('子任务名称')).toBeVisible()
    await expect(page.locator('[aria-label="业务表子任务编辑"] label').getByText('子任务类型')).toBeVisible()
    await expect(page.locator('[aria-label="业务表子任务编辑"] label').getByText('子任务优先级设置')).toBeVisible()
    await expect(page.locator('[aria-label="业务表子任务编辑"] label').getByText('责任人')).toBeVisible()
    await expect(page.locator('[aria-label="业务表子任务编辑"] label').getByText('参与人', { exact: true })).toBeVisible()

    // 验证参与人权限配置按钮存在
    await expect(page.getByRole('button', { name: '参与人权限配置' })).toBeVisible()

    // 点击关闭按钮
    await page.getByRole('button', { name: '关闭此对话框' }).click()

    // 验证弹窗已关闭
    await page.waitForTimeout(500)
    await expect(page.locator('[aria-label="业务表子任务编辑"]')).not.toBeVisible()
  })

  test('应该能够打开参与人权限设置弹窗', async ({ page }) => {
    // 打开编辑子任务弹窗
    await page.locator('table tbody tr:first-child').getByRole('button', { name: '编辑' }).click()
    await page.waitForTimeout(1000)

    // 点击参与人权限配置按钮
    await page.getByRole('button', { name: '参与人权限配置' }).click()
    await page.waitForTimeout(1000)

    // 验证权限设置弹窗标题
    await expect(page.locator('[aria-label="参与人权限设置"]')).toBeVisible()

    // 验证表格表头（使用CSS类选择器避免文本冲突）
    await expect(page.locator('[aria-label="参与人权限设置"] .header-cell.sequence')).toContainText('序号')
    await expect(page.locator('[aria-label="参与人权限设置"] .header-cell.participant')).toContainText('参与人')
    await expect(page.locator('[aria-label="参与人权限设置"] .header-cell.permission')).toContainText('授予权限')

    // 验证参与人列表（使用更精确的选择器，只检查权限弹窗内的参与人）
    await expect(page.locator('[aria-label="参与人权限设置"] .body-cell.participant').getByText('张三')).toBeVisible()
    await expect(page.locator('[aria-label="参与人权限设置"] .body-cell.participant').getByText('李四')).toBeVisible()
    await expect(page.locator('[aria-label="参与人权限设置"] .body-cell.participant').getByText('王五')).toBeVisible()
    await expect(page.locator('[aria-label="参与人权限设置"] .body-cell.participant').getByText('XXX')).toBeVisible()

    // 验证权限按钮
    await expect(page.locator('[aria-label="参与人权限设置"]').getByRole('button', { name: '新增' }).first()).toBeVisible()
    await expect(page.locator('[aria-label="参与人权限设置"]').getByRole('button', { name: '编辑' }).first()).toBeVisible()
    await expect(page.locator('[aria-label="参与人权限设置"]').getByRole('button', { name: '删除' }).first()).toBeVisible()

    // 点击确定按钮关闭权限设置弹窗
    await page.locator('[aria-label="参与人权限设置"]').getByRole('button', { name: '确定' }).click()
    await page.waitForTimeout(1000)

    // 验证权限设置弹窗已关闭
    await expect(page.locator('[aria-label="参与人权限设置"]')).not.toBeVisible()

    // 验证成功提示消息
    await expect(page.getByText('参与人权限配置成功')).toBeVisible()

    // 验证主弹窗仍然打开
    await expect(page.locator('[aria-label="业务表子任务编辑"]')).toBeVisible()
  })

  test('应该能够切换权限按钮状态', async ({ page }) => {
    // 打开编辑子任务弹窗
    await page.locator('table tbody tr:first-child').getByRole('button', { name: '编辑' }).click()
    await page.waitForTimeout(1000)

    // 打开权限设置弹窗
    await page.getByRole('button', { name: '参与人权限配置' }).click()
    await page.waitForTimeout(1000)

    // 获取第一个参与人的新增按钮（使用更精确的选择器）
    const addButton = page.locator('[aria-label="参与人权限设置"]').getByRole('button', { name: '新增' }).first()

    // 检查初始状态（根据实际情况，初始状态可能是default）
    const initialClass = await addButton.getAttribute('class')
    const isInitiallyPrimary = initialClass?.includes('el-button--primary')

    // 点击切换状态
    await addButton.click()
    await page.waitForTimeout(200)

    // 验证状态已切换
    if (isInitiallyPrimary) {
      await expect(addButton).toHaveClass(/el-button--default/)
    } else {
      await expect(addButton).toHaveClass(/el-button--primary/)
    }

    // 再次点击恢复状态
    await addButton.click()
    await page.waitForTimeout(200)

    // 验证状态已恢复
    if (isInitiallyPrimary) {
      await expect(addButton).toHaveClass(/el-button--primary/)
    } else {
      await expect(addButton).toHaveClass(/el-button--default/)
    }
  })

  test('应该能够验证必填字段', async ({ page }) => {
    // 打开编辑子任务弹窗
    await page.locator('table tbody tr:first-child').getByRole('button', { name: '编辑' }).click()
    await page.waitForTimeout(1000)

    // 清空子任务名称字段（使用更精确的选择器）
    const nameInput = page.locator('[aria-label="业务表子任务编辑"]').locator('input').first()
    await nameInput.clear()

    // 点击确定按钮
    await page.locator('[aria-label="业务表子任务编辑"]').getByRole('button', { name: '确定' }).click()
    await page.waitForTimeout(500)

    // 验证表单验证错误信息（可能需要等待验证触发）
    await page.waitForTimeout(1000)
    await expect(page.getByText('请输入子任务名称')).toBeVisible()
  })
})
