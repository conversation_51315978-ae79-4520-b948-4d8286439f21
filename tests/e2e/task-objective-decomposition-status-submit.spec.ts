import { test, expect } from '@playwright/test'

/**
 * 任务目标分解详情页面 - 状态列和提交按钮功能测试
 * 测试新增的状态选项、提交按钮位置调整和提交确认对话框功能
 */
test.describe('任务目标分解详情页面 - 状态列和提交按钮测试', () => {
  
  test.beforeEach(async ({ page }) => {
    // 导航到任务目标分解详情页面，使用 free=true 跳过认证
    await page.goto('http://localhost:4000/taskObjectiveDecomposition/detail/1?free=true')
    
    // 等待页面加载完成
    await page.waitForSelector('table')
    await page.waitForTimeout(2000) // 等待数据加载
  })

  test('应该显示新的状态选项', async ({ page }) => {
    // 点击状态选择下拉框 - 使用更精确的选择器
    await page.locator('div').filter({ hasText: /^请选择任务状态$/ }).nth(1).click()

    // 等待下拉选项出现
    await page.waitForSelector('[role="listbox"] [role="option"]')

    // 验证新的状态选项是否存在
    const statusOptions = [
      '未提交',
      '待审核',
      '已提交',
      '已退回',
      '已驳回',
      '已完成'
    ]

    for (const status of statusOptions) {
      await expect(page.getByRole('option', { name: status })).toBeVisible()
    }

    // 点击空白处关闭下拉框
    await page.click('body')
  })

  test('应该正确显示按钮顺序', async ({ page }) => {
    // 获取第一行的操作按钮
    const firstRowButtons = page.locator('table tbody tr:first-child td:last-child .el-button')
    
    // 验证按钮顺序：详情、关联、提交、删除、编辑、审核
    const expectedButtonOrder = ['详情', '关联', '提交', '删除', '编辑', '审核']
    
    for (let i = 0; i < expectedButtonOrder.length; i++) {
      const buttonText = await firstRowButtons.nth(i).textContent()
      expect(buttonText?.trim()).toBe(expectedButtonOrder[i])
    }
  })

  test('应该显示正确的提交确认对话框', async ({ page }) => {
    // 点击第一行的提交按钮
    await page.click('table tbody tr:first-child .el-button:has-text("提交")')
    
    // 验证确认对话框是否出现
    await expect(page.locator('.el-message-box')).toBeVisible()
    
    // 验证对话框标题
    await expect(page.locator('.el-message-box__title')).toHaveText('子任务提交确认')
    
    // 验证对话框内容
    const expectedMessage = '你正在进行子任务提交操作，提交后审核人可以查看任务信息，请确认操作。'
    await expect(page.locator('.el-message-box__message')).toContainText(expectedMessage)
    
    // 验证按钮文本
    await expect(page.locator('.el-message-box__btns .el-button--primary')).toHaveText('确认提交')
    await expect(page.locator('.el-message-box__btns .el-button:not(.el-button--primary)')).toHaveText('取消')
  })

  test('应该成功提交并更新状态', async ({ page }) => {
    // 记录提交前的状态
    const initialStatus = await page.locator('table tbody tr:first-child td:nth-child(8)').textContent()
    
    // 点击第一行的提交按钮
    await page.click('table tbody tr:first-child .el-button:has-text("提交")')
    
    // 确认提交
    await page.click('.el-message-box__btns .el-button--primary')
    
    // 等待状态更新
    await page.waitForTimeout(1000)
    
    // 验证成功消息
    await expect(page.locator('.el-message--success')).toBeVisible()
    await expect(page.locator('.el-message--success')).toContainText('提交成功，状态已更新为待审核')
    
    // 验证状态已更新为"待审核"
    const updatedStatus = await page.locator('table tbody tr:first-child td:nth-child(8)').textContent()
    expect(updatedStatus?.trim()).toBe('待审核')
  })

  test('应该支持取消提交操作', async ({ page }) => {
    // 记录提交前的状态
    const initialStatus = await page.locator('table tbody tr:first-child td:nth-child(8)').textContent()
    
    // 点击第一行的提交按钮
    await page.click('table tbody tr:first-child .el-button:has-text("提交")')
    
    // 取消提交
    await page.click('.el-message-box__btns .el-button:not(.el-button--primary)')
    
    // 验证取消消息
    await expect(page.locator('.el-message--info')).toBeVisible()
    await expect(page.locator('.el-message--info')).toContainText('已取消提交')
    
    // 验证状态未改变
    const currentStatus = await page.locator('table tbody tr:first-child td:nth-child(8)').textContent()
    expect(currentStatus).toBe(initialStatus)
  })

  test('应该正确筛选状态', async ({ page }) => {
    // 选择"待审核"状态进行筛选
    await page.locator('div').filter({ hasText: /^请选择任务状态$/ }).nth(1).click()
    await page.waitForSelector('[role="listbox"] [role="option"]')
    await page.getByRole('option', { name: '待审核' }).click()

    // 点击查询按钮
    await page.locator('.el-button').filter({ hasText: '查询' }).click()

    // 等待筛选结果
    await page.waitForTimeout(2000)

    // 验证只显示待审核状态的记录
    const statusCells = page.locator('table tbody tr td:nth-child(8)')
    const count = await statusCells.count()

    if (count > 0) {
      for (let i = 0; i < count; i++) {
        const statusText = await statusCells.nth(i).textContent()
        expect(statusText?.trim()).toBe('待审核')
      }
    }

    // 验证搜索成功消息
    await expect(page.locator('.el-message')).toContainText('搜索完成')
  })

  test('应该正确重置搜索条件', async ({ page }) => {
    // 先进行筛选
    await page.locator('div').filter({ hasText: /^请选择任务状态$/ }).nth(1).click()
    await page.waitForSelector('[role="listbox"] [role="option"]')
    await page.getByRole('option', { name: '已完成' }).click()
    await page.locator('.el-button').filter({ hasText: '查询' }).click()
    await page.waitForTimeout(2000)

    // 记录筛选后的记录数
    const filteredCount = await page.locator('table tbody tr').count()

    // 点击重置按钮
    await page.locator('.el-button').filter({ hasText: '重置' }).click()
    await page.waitForTimeout(2000)

    // 验证重置消息
    await expect(page.locator('.el-message')).toContainText('已重置搜索条件')

    // 验证状态选择框已重置 - 检查显示的文本
    await expect(page.locator('div').filter({ hasText: /^请选择任务状态$/ }).nth(1)).toBeVisible()

    // 验证显示所有记录
    const totalCount = await page.locator('table tbody tr').count()
    expect(totalCount).toBeGreaterThanOrEqual(filteredCount)
  })

  test('应该正确显示状态标签颜色', async ({ page }) => {
    // 验证不同状态的标签颜色类型
    const statusColorMap = {
      '未提交': 'info',
      '待审核': 'warning', 
      '已提交': 'success',
      '已退回': 'danger',
      '已驳回': 'danger',
      '已完成': 'success'
    }
    
    // 检查表格中的状态标签
    const statusCells = page.locator('table tbody tr td:nth-child(8) .el-tag')
    const count = await statusCells.count()
    
    for (let i = 0; i < count; i++) {
      const statusTag = statusCells.nth(i)
      const statusText = await statusTag.textContent()
      const expectedColorType = statusColorMap[statusText?.trim() as keyof typeof statusColorMap]
      
      if (expectedColorType) {
        await expect(statusTag).toHaveClass(new RegExp(`el-tag--${expectedColorType}`))
      }
    }
  })

  test('应该正确更新任务统计信息', async ({ page }) => {
    // 记录初始统计信息 - 使用更简单的选择器
    const initialBusinessCount = await page.locator('text=业务报表子任务数量').locator('..').textContent()
    const initialProgress = await page.locator('text=子任务提交进度').locator('..').textContent()

    // 进行状态筛选
    await page.locator('div').filter({ hasText: /^请选择任务状态$/ }).nth(1).click()
    await page.waitForSelector('[role="listbox"] [role="option"]')
    await page.getByRole('option', { name: '已完成' }).click()
    await page.locator('.el-button').filter({ hasText: '查询' }).click()
    await page.waitForTimeout(2000)

    // 验证统计信息已更新（筛选后的统计）
    const filteredBusinessCount = await page.locator('text=业务报表子任务数量').locator('..').textContent()
    const filteredProgress = await page.locator('text=子任务提交进度').locator('..').textContent()

    // 统计信息应该发生变化（除非所有记录都是已完成状态）
    expect(filteredBusinessCount).toBeDefined()
    expect(filteredProgress).toBeDefined()

    // 重置后验证统计信息恢复
    await page.locator('.el-button').filter({ hasText: '重置' }).click()
    await page.waitForTimeout(2000)

    const resetBusinessCount = await page.locator('text=业务报表子任务数量').locator('..').textContent()
    const resetProgress = await page.locator('text=子任务提交进度').locator('..').textContent()

    expect(resetBusinessCount).toBe(initialBusinessCount)
    expect(resetProgress).toBe(initialProgress)
  })
})
