import { test, expect } from '@playwright/test'

test.describe('业务表主键定义 - 批量修改功能', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到主键定义页面
    await page.goto('/primary-key-definition')

    // 等待页面加载完成
    await page.waitForSelector('[data-test="batch-edit-button"]', { timeout: 60000 })
  })

  test('应该显示批量修改按钮', async ({ page }) => {
    const batchEditButton = page.locator('[data-test="batch-edit-button"]')
    await expect(batchEditButton).toBeVisible()
    await expect(batchEditButton).toHaveText('批量修改')
  })

  test('未选择行时批量修改按钮应该被禁用', async ({ page }) => {
    const batchEditButton = page.locator('[data-test="batch-edit-button"]')
    await expect(batchEditButton).toBeDisabled()
  })

  test('表格应该支持多选功能', async ({ page }) => {
    // 检查表格是否有复选框列
    const checkboxes = page.locator('table tbody tr td:first-child input[type="checkbox"]')
    await expect(checkboxes.first()).toBeVisible()
  })

  test('选择行后批量修改按钮应该被启用', async ({ page }) => {
    // 选择第一行
    await page.locator('table tbody tr:first-child td:first-child input[type="checkbox"]').click()
    
    // 批量修改按钮应该被启用
    const batchEditButton = page.locator('[data-test="batch-edit-button"]')
    await expect(batchEditButton).toBeEnabled()
  })

  test('点击批量修改按钮应该打开弹窗', async ({ page }) => {
    // 选择第一行
    await page.locator('table tbody tr:first-child td:first-child input[type="checkbox"]').click()
    
    // 点击批量修改按钮
    await page.locator('[data-test="batch-edit-button"]').click()
    
    // 检查弹窗是否打开
    await expect(page.locator('.el-dialog')).toBeVisible()
    await expect(page.locator('.el-dialog__title')).toHaveText('批量修改')
  })

  test('批量修改弹窗应该显示选中的记录数量', async ({ page }) => {
    // 选择多行
    await page.locator('table tbody tr:nth-child(1) td:first-child input[type="checkbox"]').click()
    await page.locator('table tbody tr:nth-child(2) td:first-child input[type="checkbox"]').click()
    
    // 点击批量修改按钮
    await page.locator('[data-test="batch-edit-button"]').click()
    
    // 检查弹窗中的选中记录数量信息
    await expect(page.locator('.batch-edit-info')).toContainText('已选择')
    await expect(page.locator('.batch-edit-info')).toContainText('2')
    await expect(page.locator('.batch-edit-info')).toContainText('条记录进行批量修改')
  })

  test('批量修改表单应该包含必要的字段', async ({ page }) => {
    // 选择第一行
    await page.locator('table tbody tr:first-child td:first-child input[type="checkbox"]').click()
    
    // 点击批量修改按钮
    await page.locator('[data-test="batch-edit-button"]').click()
    
    // 检查表单字段
    await expect(page.locator('label:has-text("主键数据类型")')).toBeVisible()
    await expect(page.locator('label:has-text("最小长度")')).toBeVisible()
    await expect(page.locator('label:has-text("最大长度")')).toBeVisible()
    
    // 检查数据类型下拉框
    await expect(page.locator('.el-select')).toBeVisible()
    
    // 检查数字输入框
    await expect(page.locator('.el-input-number').first()).toBeVisible()
    await expect(page.locator('.el-input-number').nth(1)).toBeVisible()
  })

  test('未选择数据类型时应该显示错误提示', async ({ page }) => {
    // 选择第一行
    await page.locator('table tbody tr:first-child td:first-child input[type="checkbox"]').click()
    
    // 点击批量修改按钮
    await page.locator('[data-test="batch-edit-button"]').click()
    
    // 不选择数据类型，直接点击确认
    await page.locator('button:has-text("确认")').click()
    
    // 检查错误提示
    await expect(page.locator('.el-message--error')).toContainText('请选择数据类型')
  })

  test('完整的批量修改流程', async ({ page }) => {
    // 1. 选择多行
    await page.locator('table tbody tr:nth-child(1) td:first-child input[type="checkbox"]').click()
    await page.locator('table tbody tr:nth-child(2) td:first-child input[type="checkbox"]').click()
    
    // 2. 点击批量修改按钮
    await page.locator('[data-test="batch-edit-button"]').click()
    
    // 3. 填写表单
    await page.locator('.el-select').click()
    await page.locator('.el-option:has-text("字符型")').click()
    
    await page.locator('.el-input-number input').first().fill('5')
    await page.locator('.el-input-number input').nth(1).fill('50')
    
    // 4. 点击确认
    await page.locator('button:has-text("确认")').click()
    
    // 5. 检查成功提示
    await expect(page.locator('.el-message--success')).toContainText('成功批量修改')
    
    // 6. 检查弹窗关闭
    await expect(page.locator('.el-dialog')).not.toBeVisible()
    
    // 7. 检查选择状态清空
    const batchEditButton = page.locator('[data-test="batch-edit-button"]')
    await expect(batchEditButton).toBeDisabled()
  })

  test('取消批量修改应该关闭弹窗', async ({ page }) => {
    // 选择第一行
    await page.locator('table tbody tr:first-child td:first-child input[type="checkbox"]').click()
    
    // 点击批量修改按钮
    await page.locator('[data-test="batch-edit-button"]').click()
    
    // 点击取消按钮
    await page.locator('button:has-text("取消")').click()
    
    // 检查弹窗关闭
    await expect(page.locator('.el-dialog')).not.toBeVisible()
  })

  test('响应式设计 - 移动端视图', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 })
    
    // 检查批量修改按钮在移动端是否正常显示
    const batchEditButton = page.locator('[data-test="batch-edit-button"]')
    await expect(batchEditButton).toBeVisible()
    
    // 选择行并打开弹窗
    await page.locator('table tbody tr:first-child td:first-child input[type="checkbox"]').click()
    await page.locator('[data-test="batch-edit-button"]').click()
    
    // 检查弹窗在移动端是否正常显示
    await expect(page.locator('.el-dialog')).toBeVisible()
    
    // 检查表单字段是否正常显示
    await expect(page.locator('.el-select')).toBeVisible()
    await expect(page.locator('.el-input-number').first()).toBeVisible()
  })

  test('键盘导航支持', async ({ page }) => {
    // 选择第一行
    await page.locator('table tbody tr:first-child td:first-child input[type="checkbox"]').click()
    
    // 使用 Tab 键导航到批量修改按钮
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab')
    
    // 使用 Enter 键打开弹窗
    await page.keyboard.press('Enter')
    
    // 检查弹窗是否打开
    await expect(page.locator('.el-dialog')).toBeVisible()
    
    // 使用 Escape 键关闭弹窗
    await page.keyboard.press('Escape')
    
    // 检查弹窗是否关闭
    await expect(page.locator('.el-dialog')).not.toBeVisible()
  })
})
