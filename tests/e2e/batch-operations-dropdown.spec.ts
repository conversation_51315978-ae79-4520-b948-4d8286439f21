import { test, expect } from '@playwright/test'

test.describe('批量操作下拉选择功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 访问登录页面并跳过认证
    await page.goto('http://localhost:5176/login?free=true')
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle')
    
    // 导航到任务目标拆解页面
    await page.goto('http://localhost:5176/taskObjectiveDecomposition?free=true')
    await page.waitForLoadState('networkidle')
    
    // 点击第一个任务的详情按钮
    await page.click('button:has-text("详情")')
    await page.waitForLoadState('networkidle')
  })

  test('批量状态更新应该显示下拉选择而不是文本输入', async ({ page }) => {
    // 选择第一个子任务
    await page.click('table tbody tr:first-child input[type="checkbox"]')
    
    // 点击批量状态更新按钮
    await page.click('button:has-text("批量状态更新")')
    
    // 验证弹窗出现
    await expect(page.locator('.el-dialog__title:has-text("批量状态更新")')).toBeVisible()
    
    // 验证下拉选择框存在
    const selectElement = page.locator('.batch-dialog-content .el-select')
    await expect(selectElement).toBeVisible()
    
    // 点击下拉选择框
    await selectElement.click()
    
    // 验证状态选项存在
    await expect(page.locator('.el-select-dropdown__item:has-text("未开始")')).toBeVisible()
    await expect(page.locator('.el-select-dropdown__item:has-text("执行中")')).toBeVisible()
    await expect(page.locator('.el-select-dropdown__item:has-text("已完成")')).toBeVisible()
    await expect(page.locator('.el-select-dropdown__item:has-text("已暂停")')).toBeVisible()
    
    // 验证选中任务数量显示
    await expect(page.locator('.selected-count:has-text("已选择 1 个子任务")')).toBeVisible()
    
    // 选择一个状态
    await page.click('.el-select-dropdown__item:has-text("执行中")')
    
    // 点击确认按钮
    await page.click('button:has-text("确认")')
    
    // 验证成功消息
    await expect(page.locator('.el-message--success')).toBeVisible()
    
    // 验证弹窗关闭
    await expect(page.locator('.el-dialog__title:has-text("批量状态更新")')).not.toBeVisible()
  })

  test('批量分配责任人应该显示下拉选择而不是文本输入', async ({ page }) => {
    // 选择第一个子任务
    await page.click('table tbody tr:first-child input[type="checkbox"]')
    
    // 点击批量分配责任人按钮
    await page.click('button:has-text("批量分配责任人")')
    
    // 验证弹窗出现
    await expect(page.locator('.el-dialog__title:has-text("批量分配责任人")')).toBeVisible()
    
    // 验证下拉选择框存在
    const selectElement = page.locator('.batch-dialog-content .el-select')
    await expect(selectElement).toBeVisible()
    
    // 点击下拉选择框
    await selectElement.click()
    
    // 验证责任人选项存在（检查几个示例）
    await expect(page.locator('.el-select-dropdown__item:has-text("张三 - 技术部 - 开发工程师")')).toBeVisible()
    await expect(page.locator('.el-select-dropdown__item:has-text("李四 - 产品部 - 产品经理")')).toBeVisible()
    
    // 验证选中任务数量显示
    await expect(page.locator('.selected-count:has-text("已选择 1 个子任务")')).toBeVisible()
    
    // 选择一个责任人
    await page.click('.el-select-dropdown__item:has-text("张三 - 技术部 - 开发工程师")')
    
    // 点击确认按钮
    await page.click('button:has-text("确认")')
    
    // 验证成功消息
    await expect(page.locator('.el-message--success')).toBeVisible()
    
    // 验证弹窗关闭
    await expect(page.locator('.el-dialog__title:has-text("批量分配责任人")')).not.toBeVisible()
  })

  test('批量操作按钮在未选择任务时应该被禁用', async ({ page }) => {
    // 验证初始状态下批量操作按钮被禁用
    await expect(page.locator('button:has-text("批量状态更新")')).toBeDisabled()
    await expect(page.locator('button:has-text("批量分配责任人")')).toBeDisabled()
    
    // 选择一个任务
    await page.click('table tbody tr:first-child input[type="checkbox"]')
    
    // 验证批量操作按钮变为可用
    await expect(page.locator('button:has-text("批量状态更新")')).not.toBeDisabled()
    await expect(page.locator('button:has-text("批量分配责任人")')).not.toBeDisabled()
  })

  test('批量操作弹窗应该支持取消操作', async ({ page }) => {
    // 选择第一个子任务
    await page.click('table tbody tr:first-child input[type="checkbox"]')
    
    // 测试批量状态更新取消
    await page.click('button:has-text("批量状态更新")')
    await expect(page.locator('.el-dialog__title:has-text("批量状态更新")')).toBeVisible()
    await page.click('button:has-text("取消")')
    await expect(page.locator('.el-dialog__title:has-text("批量状态更新")')).not.toBeVisible()
    
    // 测试批量分配责任人取消
    await page.click('button:has-text("批量分配责任人")')
    await expect(page.locator('.el-dialog__title:has-text("批量分配责任人")')).toBeVisible()
    await page.click('button:has-text("取消")')
    await expect(page.locator('.el-dialog__title:has-text("批量分配责任人")')).not.toBeVisible()
  })
})
