import { test, expect } from '@playwright/test'

test.describe('配置抽查模板页面', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到配置抽查模板页面，添加 free=true 跳过登录验证
    await page.goto('/reportIntegrationSpotCheck/configTemplate?free=true')

    // 等待页面加载完成
    await page.waitForSelector('.config-template', { timeout: 10000 })
  })

  test('页面基本元素渲染正确', async ({ page }) => {
    // 验证页面标题
    await expect(page.locator('text=配置抽查模板')).toBeVisible()
    
    // 验证右上角按钮组
    await expect(page.locator('text=新建抽查模板')).toBeVisible()
    await expect(page.locator('text=抽查模板使用记录')).toBeVisible()
    await expect(page.locator('text=批量删除')).toBeVisible()
    
    // 验证搜索表单
    await expect(page.locator('input[placeholder*="模板名称"]')).toBeVisible()
    await expect(page.locator('text=查询')).toBeVisible()
    
    // 验证表格存在
    await expect(page.locator('.el-table')).toBeVisible()
    
    // 验证分页组件
    await expect(page.locator('.el-pagination')).toBeVisible()
  })

  test('表格数据加载和显示', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForSelector('.el-table__body tr', { timeout: 10000 })
    
    // 验证表格列头
    await expect(page.locator('text=序号')).toBeVisible()
    await expect(page.locator('text=抽查模板名称')).toBeVisible()
    await expect(page.locator('text=类别')).toBeVisible()
    await expect(page.locator('text=创建时间')).toBeVisible()
    await expect(page.locator('text=模板描述')).toBeVisible()
    await expect(page.locator('text=操作')).toBeVisible()
    
    // 验证至少有一行数据
    const rows = page.locator('.el-table__body tr')
    await expect(rows).toHaveCountGreaterThan(0)
    
    // 验证操作按钮存在
    await expect(page.locator('text=导出').first()).toBeVisible()
    await expect(page.locator('text=复制').first()).toBeVisible()
    await expect(page.locator('text=查看').first()).toBeVisible()
    await expect(page.locator('text=历史').first()).toBeVisible()
    await expect(page.locator('text=编辑').first()).toBeVisible()
    await expect(page.locator('text=删除').first()).toBeVisible()
  })

  test('搜索功能测试', async ({ page }) => {
    // 等待页面加载完成
    await page.waitForSelector('.el-table__body tr', { timeout: 10000 })
    
    // 获取初始行数
    const initialRows = await page.locator('.el-table__body tr').count()
    
    // 输入搜索条件
    await page.fill('input[placeholder*="模板名称"]', '财政')
    await page.click('text=查询')
    
    // 等待搜索结果
    await page.waitForTimeout(1000)
    
    // 验证搜索结果
    const searchRows = await page.locator('.el-table__body tr').count()
    expect(searchRows).toBeLessThanOrEqual(initialRows)
  })

  test('右上角按钮点击测试', async ({ page }) => {
    // 测试新建抽查模板按钮
    await page.click('text=新建抽查模板')
    await expect(page.locator('.el-message')).toBeVisible({ timeout: 5000 })
    
    // 等待消息消失
    await page.waitForTimeout(2000)
    
    // 测试抽查模板使用记录按钮
    await page.click('text=抽查模板使用记录')
    await expect(page.locator('.el-message')).toBeVisible({ timeout: 5000 })
    
    // 等待消息消失
    await page.waitForTimeout(2000)
    
    // 测试批量删除按钮（无选择时）
    await page.click('text=批量删除')
    await expect(page.locator('.el-message')).toBeVisible({ timeout: 5000 })
  })

  test('操作按钮点击测试', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForSelector('.el-table__body tr', { timeout: 10000 })
    
    // 测试导出按钮
    await page.click('.el-table__body tr:first-child .el-button:has-text("导出")')
    await expect(page.locator('.el-message')).toBeVisible({ timeout: 5000 })
    
    // 等待消息消失
    await page.waitForTimeout(2000)
    
    // 测试复制按钮
    await page.click('.el-table__body tr:first-child .el-button:has-text("复制")')
    await expect(page.locator('.el-message')).toBeVisible({ timeout: 5000 })
    
    // 等待消息消失
    await page.waitForTimeout(2000)
    
    // 测试查看按钮
    await page.click('.el-table__body tr:first-child .el-button:has-text("查看")')
    await expect(page.locator('.el-message')).toBeVisible({ timeout: 5000 })
  })

  test('数据持久化测试', async ({ page }) => {
    // 等待页面加载完成
    await page.waitForSelector('.el-table__body tr', { timeout: 10000 })

    // 获取第一行数据
    const firstRowText = await page.locator('.el-table__body tr').first().textContent()

    // 刷新页面
    await page.reload()
    await page.waitForSelector('.el-table__body tr', { timeout: 10000 })

    // 验证数据仍然存在
    const newFirstRowText = await page.locator('.el-table__body tr').first().textContent()
    expect(newFirstRowText).toBe(firstRowText)
  })

  test('分页功能测试', async ({ page }) => {
    // 等待页面加载完成
    await page.waitForSelector('.el-table__body tr', { timeout: 10000 })
    
    // 验证分页信息
    await expect(page.locator('.el-pagination__total')).toBeVisible()
    
    // 如果有多页，测试翻页功能
    const nextButton = page.locator('.el-pagination .btn-next')
    if (await nextButton.isEnabled()) {
      // 记录第一页第一行数据
      const firstRowText = await page.locator('.el-table__body tr').first().textContent()
      
      // 点击下一页
      await nextButton.click()
      await page.waitForTimeout(500)
      
      // 验证数据已更新
      const newFirstRowText = await page.locator('.el-table__body tr').first().textContent()
      expect(newFirstRowText).not.toBe(firstRowText)
    }
  })

  test('表格选择功能测试', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForSelector('.el-table__body tr', { timeout: 10000 })
    
    // 选择第一行
    await page.click('.el-table__body tr:first-child .el-checkbox')
    
    // 验证选择状态
    const checkbox = page.locator('.el-table__body tr:first-child .el-checkbox input')
    await expect(checkbox).toBeChecked()
    
    // 测试批量删除按钮（有选择时）
    await page.click('text=批量删除')
    await expect(page.locator('.el-message')).toBeVisible({ timeout: 5000 })
  })

  test('响应式布局测试', async ({ page }) => {
    // 测试不同屏幕尺寸
    await page.setViewportSize({ width: 1200, height: 800 })
    await page.waitForSelector('.el-table', { timeout: 10000 })
    
    // 验证表格在大屏幕下正常显示
    await expect(page.locator('.el-table')).toBeVisible()
    
    // 切换到小屏幕
    await page.setViewportSize({ width: 768, height: 600 })
    await page.waitForTimeout(500)
    
    // 验证表格仍然可见
    await expect(page.locator('.el-table')).toBeVisible()
  })
})
