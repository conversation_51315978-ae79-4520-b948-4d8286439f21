import { test, expect } from '@playwright/test';

test.describe('子任务属性管理弹窗基础功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到子任务属性管理页面
    await page.goto('/taskObjectiveDecomposition/subtaskAttributeManagement/1?free=true');
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
  });

  test('属性值统计弹窗基础功能测试', async ({ page }) => {
    // 点击属性值统计按钮
    await page.getByRole('button', { name: '属性值统计' }).click();
    
    // 验证弹窗打开
    await expect(page.locator('.el-dialog')).toContainText('子任务属性信息统计');
    
    // 等待数据加载完成
    await page.waitForTimeout(2000);
    
    // 验证统计表格存在
    await expect(page.locator('.statistics-table')).toBeVisible();
    
    // 验证导出按钮存在
    await expect(page.getByRole('button', { name: '导出' })).toBeVisible();
    
    // 测试确认功能
    await page.getByRole('button', { name: '确认' }).click();
    await page.waitForTimeout(1500);
    
    // 验证成功消息
    await expect(page.locator('.el-message').last()).toContainText('统计确认成功');
  });

  test('权限设置弹窗基础功能测试', async ({ page }) => {
    // 点击权限设置按钮
    await page.getByRole('button', { name: '权限设置' }).click();
    
    // 验证弹窗打开
    await expect(page.locator('.el-dialog')).toContainText('子任务属性权限设置');
    
    // 等待数据加载完成
    await page.waitForTimeout(1500);
    
    // 验证权限表格存在
    await expect(page.locator('.permission-table')).toBeVisible();
    
    // 验证添加权限设置按钮存在
    await expect(page.getByRole('button', { name: '添加权限设置' })).toBeVisible();
    
    // 验证保存按钮存在
    await expect(page.getByRole('button', { name: '保存' })).toBeVisible();
    
    // 测试取消功能
    await page.getByRole('button', { name: '取消' }).click();
    
    // 验证取消消息
    await expect(page.locator('.el-message').last()).toContainText('已取消权限设置');
  });

  test('紧急程度规则弹窗基础功能测试', async ({ page }) => {
    // 点击紧急程度规则按钮
    await page.getByRole('button', { name: '紧急程度规则' }).click();
    
    // 验证弹窗打开
    await expect(page.locator('.el-dialog')).toContainText('子任务属性紧急程度规则');
    
    // 等待数据加载完成
    await page.waitForTimeout(1200);
    
    // 验证规则表格存在
    await expect(page.locator('.rules-table')).toBeVisible();
    
    // 验证规则录入按钮存在
    await expect(page.getByRole('button', { name: '规则录入' })).toBeVisible();
    
    // 测试取消功能
    await page.getByRole('button', { name: '取消' }).click();
    
    // 验证取消消息
    await expect(page.locator('.el-message').last()).toContainText('已取消紧急程度规则');
  });

  test('风险等级管理弹窗基础功能测试', async ({ page }) => {
    // 点击风险等级管理按钮
    await page.getByRole('button', { name: '风险等级管理' }).click();
    
    // 验证弹窗打开
    await expect(page.locator('.el-dialog')).toContainText('属性风险等级分类管理');
    
    // 等待数据加载完成
    await page.waitForTimeout(1500);
    
    // 验证风险等级表格存在
    await expect(page.locator('.risk-level-table')).toBeVisible();
    
    // 验证风险等级标签存在
    await expect(page.locator('.risk-level-table .el-tag').first()).toBeVisible();
    
    // 测试确认功能
    await page.getByRole('button', { name: '确认' }).click();
    await page.waitForTimeout(1200);
    
    // 验证确认成功消息
    await expect(page.locator('.el-message').last()).toContainText('风险等级管理确认成功');
  });

  test('历史记录弹窗基础功能测试', async ({ page }) => {
    // 点击历史记录按钮
    await page.getByRole('button', { name: '历史记录' }).click();
    
    // 验证弹窗打开
    await expect(page.locator('.el-dialog')).toContainText('子任务属性历史记录');
    
    // 等待数据加载完成
    await page.waitForTimeout(2000);
    
    // 验证标签页存在
    await expect(page.getByRole('tab', { name: '业务报表子任务' })).toBeVisible();
    await expect(page.getByRole('tab', { name: '临时报表子任务' })).toBeVisible();
    
    // 验证统计信息区域存在
    await expect(page.locator('.statistics-section').first()).toBeVisible();
    
    // 测试标签页切换
    await page.getByRole('tab', { name: '临时报表子任务' }).click();
    
    // 验证标签页切换成功
    await expect(page.getByRole('tab', { name: '临时报表子任务' })).toHaveAttribute('aria-selected', 'true');
    
    // 测试确认功能
    await page.getByRole('button', { name: '确认' }).click();
    await page.waitForTimeout(1200);
    
    // 验证确认成功消息
    await expect(page.locator('.el-message').last()).toContainText('历史记录确认成功');
  });

  test('所有弹窗的基本打开关闭测试', async ({ page }) => {
    const dialogs = [
      { button: '属性值统计', title: '子任务属性信息统计' },
      { button: '权限设置', title: '子任务属性权限设置' },
      { button: '紧急程度规则', title: '子任务属性紧急程度规则' },
      { button: '风险等级管理', title: '属性风险等级分类管理' },
      { button: '历史记录', title: '子任务属性历史记录' }
    ];

    for (const dialog of dialogs) {
      // 打开弹窗
      await page.getByRole('button', { name: dialog.button }).click();
      
      // 验证弹窗打开
      await expect(page.locator('.el-dialog')).toContainText(dialog.title);
      
      // 等待弹窗完全加载
      await page.waitForTimeout(1000);
      
      // 点击关闭按钮（X按钮）
      await page.locator('.el-dialog__close').click();
      
      // 等待弹窗关闭
      await page.waitForTimeout(500);
    }
  });

  test('弹窗按钮存在性测试', async ({ page }) => {
    // 验证所有弹窗触发按钮都存在
    await expect(page.getByRole('button', { name: '属性值统计' })).toBeVisible();
    await expect(page.getByRole('button', { name: '权限设置' })).toBeVisible();
    await expect(page.getByRole('button', { name: '紧急程度规则' })).toBeVisible();
    await expect(page.getByRole('button', { name: '风险等级管理' })).toBeVisible();
    await expect(page.getByRole('button', { name: '历史记录' })).toBeVisible();
  });

  test('弹窗响应性测试', async ({ page }) => {
    // 测试属性值统计弹窗的响应性
    await page.getByRole('button', { name: '属性值统计' }).click();
    
    // 验证弹窗快速打开
    await expect(page.locator('.el-dialog')).toBeVisible();
    
    // 验证弹窗内容加载
    await page.waitForTimeout(2000);
    await expect(page.locator('.statistics-table')).toBeVisible();
    
    // 关闭弹窗
    await page.getByRole('button', { name: '取消' }).click();
    
    // 验证弹窗快速关闭
    await page.waitForTimeout(500);
  });
});
