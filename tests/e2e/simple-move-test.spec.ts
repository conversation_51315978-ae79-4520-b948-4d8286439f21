import { test, expect } from '@playwright/test'

test.describe('简单上移下移功能测试', () => {
  test('验证页面可以访问并测试上移下移功能', async ({ page }) => {
    // 访问页面
    await page.goto('/taskObjectiveDecomposition/detail/1?free=true')
    
    // 等待页面加载
    await page.waitForTimeout(8000)
    
    // 检查页面标题
    await expect(page.locator('h2:has-text("任务目标拆解")')).toBeVisible()
    
    // 检查是否有表格
    const table = page.locator('.el-table')
    if (await table.count() > 0) {
      console.log('找到表格')
      
      // 检查是否有数据行
      const rows = page.locator('.el-table tbody tr')
      const rowCount = await rows.count()
      console.log('数据行数:', rowCount)
      
      if (rowCount > 0) {
        // 尝试点击第一行的更多按钮
        const firstRow = rows.first()
        const moreButton = firstRow.locator('button:has-text("更多")')
        
        if (await moreButton.count() > 0) {
          console.log('找到更多按钮，点击...')
          await moreButton.click()
          await page.waitForTimeout(2000)
          
          // 检查下拉菜单
          const dropdown = page.locator('.el-dropdown-menu')
          if (await dropdown.isVisible()) {
            console.log('下拉菜单已显示')
            
            // 检查上移下移按钮
            const moveUpButton = page.getByRole('menuitem', { name: '上移' })
            const moveDownButton = page.getByRole('menuitem', { name: '下移' })
            
            const hasUp = await moveUpButton.count() > 0
            const hasDown = await moveDownButton.count() > 0
            
            console.log('上移按钮存在:', hasUp)
            console.log('下移按钮存在:', hasDown)
            
            // 如果有下移按钮，测试点击
            if (hasDown) {
              console.log('测试下移功能...')
              await moveDownButton.click()
              await page.waitForTimeout(3000)
              
              // 检查是否有成功消息
              const successMessage = page.locator('.el-message--success')
              if (await successMessage.count() > 0) {
                console.log('下移操作成功')
              }
            }
          }
        }
      } else {
        console.log('没有找到数据行')
      }
    } else {
      console.log('没有找到表格')
    }
    
    // 基本断言 - 页面应该加载成功
    await expect(page.locator('body')).toBeVisible()
  })
})
