import { test, expect } from '@playwright/test'

test.describe('主要领导日志分析功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到主要领导日志分析页面，添加免登参数
    await page.goto('/leadershipEfficiency/leaderLogAnalysis?free=true')

    // 等待页面加载完成
    await page.waitForLoadState('networkidle')

    // 等待页面主要元素加载
    await page.waitForSelector('.leader-log-analysis', { timeout: 10000 })
  })

  test('页面基本加载测试', async ({ page }) => {
    // 验证页面标题
    await expect(page.locator('h1, .block-title')).toContainText('主要领导日志分析')

    // 验证表格存在
    await expect(page.locator('.el-table')).toBeVisible()

    // 验证分页组件存在
    await expect(page.locator('.el-pagination')).toBeVisible()
  })

  test('表格表头验证', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.el-table')
    
    // 验证所有必需的表头
    const expectedHeaders = ['序号', '所属部门', '领导角色', '今日审批量', '平均审核耗时', '驳回次数']
    
    for (const header of expectedHeaders) {
      await expect(page.locator('.el-table th').filter({ hasText: header })).toBeVisible()
    }
  })

  test('数据加载和显示测试', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForSelector('.el-table tbody tr')
    
    // 验证表格有数据行
    const rows = page.locator('.el-table tbody tr')
    const rowCount = await rows.count()
    expect(rowCount).toBeGreaterThan(0)
    
    // 验证第一行数据格式
    const firstRow = rows.first()
    await expect(firstRow.locator('td').nth(0)).toContainText(/^\d+$/) // 序号应该是数字
    await expect(firstRow.locator('td').nth(1)).not.toBeEmpty() // 所属部门不为空
    await expect(firstRow.locator('td').nth(2)).not.toBeEmpty() // 领导角色不为空
    await expect(firstRow.locator('td').nth(3)).toContainText(/\d+件/) // 今日审批量格式
    await expect(firstRow.locator('td').nth(4)).toContainText(/\d+h\d+m/) // 平均审核耗时格式
    await expect(firstRow.locator('td').nth(5)).toContainText(/\d+次/) // 驳回次数格式
  })

  test('表格样式和交互测试', async ({ page }) => {
    // 等待页面加载完成
    await page.waitForSelector('.el-table tbody tr')

    // 验证表格样式
    const table = page.locator('.el-table')
    await expect(table).toHaveCSS('border', /.*/)

    // 验证表格行数据
    const rows = page.locator('.el-table tbody tr')
    const rowCount = await rows.count()
    expect(rowCount).toBeGreaterThan(0)

    // 验证表格hover效果
    const firstRow = rows.first()
    await firstRow.hover()
    await page.waitForTimeout(200)

    // 验证数据格式样式
    const approvalCell = firstRow.locator('td').nth(3)
    const rejectionCell = firstRow.locator('td').nth(5)

    await expect(approvalCell).toContainText(/\d+件/)
    await expect(rejectionCell).toContainText(/\d+次/)
  })

  test('分页功能测试', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.el-table tbody tr')
    
    // 验证分页组件存在
    await expect(page.locator('.el-pagination')).toBeVisible()
    
    // 获取总数信息
    const totalText = await page.locator('.el-pagination__total').textContent()
    expect(totalText).toMatch(/共\s*\d+\s*条/)
    
    // 测试页面大小切换
    const pageSizeSelector = page.locator('.el-pagination__sizes .el-select')
    if (await pageSizeSelector.isVisible()) {
      await pageSizeSelector.click()
      await page.locator('.el-select-dropdown__item').filter({ hasText: '20' }).click()
      await page.waitForTimeout(500)
      
      // 验证页面大小变化
      const rowsAfterSizeChange = await page.locator('.el-table tbody tr').count()
      expect(rowsAfterSizeChange).toBeLessThanOrEqual(20)
    }
    
    // 测试页码切换（如果有多页）
    const nextPageButton = page.locator('.el-pagination .btn-next')
    if (await nextPageButton.isEnabled()) {
      await nextPageButton.click()
      await page.waitForTimeout(500)
      
      // 验证页码变化
      const currentPage = await page.locator('.el-pagination__editor input').inputValue()
      expect(parseInt(currentPage)).toBeGreaterThan(1)
    }
  })

  test('数据持久化测试', async ({ page }) => {
    // 等待初始数据加载
    await page.waitForSelector('.el-table tbody tr')

    // 获取初始数据
    const initialRowCount = await page.locator('.el-table tbody tr').count()
    const firstRowData = await page.locator('.el-table tbody tr').first().locator('td').nth(1).textContent()

    // 刷新页面
    await page.reload()
    await page.waitForLoadState('networkidle')
    await page.waitForSelector('.leader-log-analysis', { timeout: 10000 })
    await page.waitForSelector('.el-table tbody tr')

    // 验证数据持久化（页面刷新后数据应该保持）
    const afterReloadRowCount = await page.locator('.el-table tbody tr').count()
    const afterReloadFirstRowData = await page.locator('.el-table tbody tr').first().locator('td').nth(1).textContent()

    expect(afterReloadRowCount).toBe(initialRowCount)
    expect(afterReloadFirstRowData).toBe(firstRowData)
  })

  test('响应式布局测试', async ({ page }) => {
    // 测试不同屏幕尺寸下的布局
    await page.setViewportSize({ width: 1920, height: 1080 })
    await page.waitForSelector('.el-table')
    
    // 验证桌面端布局
    await expect(page.locator('.el-table')).toBeVisible()
    
    // 测试平板尺寸
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.waitForTimeout(500)
    await expect(page.locator('.el-table')).toBeVisible()
    
    // 测试手机尺寸
    await page.setViewportSize({ width: 375, height: 667 })
    await page.waitForTimeout(500)
    await expect(page.locator('.el-table')).toBeVisible()
  })

  test('数据格式验证测试', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.el-table tbody tr')
    
    const rows = page.locator('.el-table tbody tr')
    const rowCount = await rows.count()
    
    // 验证至少有20条数据（符合需求）
    expect(rowCount).toBeGreaterThanOrEqual(10) // 考虑分页，至少当前页有数据
    
    // 验证数据格式
    for (let i = 0; i < Math.min(5, rowCount); i++) {
      const row = rows.nth(i)
      
      // 序号应该是数字
      const sequence = await row.locator('td').nth(0).textContent()
      expect(sequence).toMatch(/^\d+$/)
      
      // 所属部门不为空
      const department = await row.locator('td').nth(1).textContent()
      expect(department?.trim()).toBeTruthy()
      
      // 领导角色不为空
      const role = await row.locator('td').nth(2).textContent()
      expect(role?.trim()).toBeTruthy()
      
      // 今日审批量格式：数字+件
      const approvalCount = await row.locator('td').nth(3).textContent()
      expect(approvalCount).toMatch(/^\d+件$/)
      
      // 平均审核耗时格式：数字h数字m
      const avgTime = await row.locator('td').nth(4).textContent()
      expect(avgTime).toMatch(/^\d+h\d+m$/)
      
      // 驳回次数格式：数字+次
      const rejectionCount = await row.locator('td').nth(5).textContent()
      expect(rejectionCount).toMatch(/^\d+次$/)
    }
  })

  test('页面错误恢复测试', async ({ page }) => {
    // 等待页面正常加载
    await page.waitForSelector('.el-table tbody tr')

    // 验证页面能正常显示数据
    const rows = await page.locator('.el-table tbody tr').count()
    expect(rows).toBeGreaterThan(0)

    // 测试页面刷新后的恢复能力
    await page.reload()
    await page.waitForLoadState('networkidle')
    await page.waitForSelector('.leader-log-analysis', { timeout: 10000 })

    // 验证页面重新加载后仍能正常显示
    await page.waitForSelector('.el-table tbody tr')
    const rowsAfterReload = await page.locator('.el-table tbody tr').count()
    expect(rowsAfterReload).toBeGreaterThan(0)
  })
})
