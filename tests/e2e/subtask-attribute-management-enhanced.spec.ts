import { test, expect } from '@playwright/test';

test.describe('子任务属性管理页面功能增强测试', () => {
  const baseUrl = 'http://localhost:5177';
  const testUrl = `${baseUrl}/taskObjectiveDecomposition/subtaskAttributeManagement/1?free=true`;

  test.beforeEach(async ({ page }) => {
    // 导航到测试页面
    await page.goto(testUrl);
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
    
    // 等待表格数据加载完成（模拟的1.5秒延迟）
    await page.waitForTimeout(2000);
  });

  test('页面基本加载测试', async ({ page }) => {
    // 验证页面标题
    await expect(page.locator('.page-title')).toContainText('子任务属性');
    
    // 验证搜索区域存在
    await expect(page.locator('.search-section')).toBeVisible();
    
    // 验证表格存在
    await expect(page.locator('.table-section')).toBeVisible();
    
    // 验证数据已加载
    const tableRows = page.locator('table tbody tr');
    await expect(tableRows.first()).toBeVisible();
  });

  test('搜索功能测试', async ({ page }) => {
    // 输入搜索关键词
    await page.fill('.search-input input', '永川区');
    
    // 点击搜索按钮
    await page.click('button:has-text("搜索")');
    
    // 等待搜索完成（模拟1秒延迟）
    await page.waitForTimeout(1500);
    
    // 验证搜索结果
    await expect(page.locator('.el-message--success')).toContainText('搜索完成');
    
    // 验证表格数据已更新
    const rows = page.locator('table tbody tr');
    // 第一列是复选框，第二列是行号，第三列才是属性名称
    await expect(rows.first().locator('td').nth(2)).toContainText('永川区');
  });

  test('重置功能测试', async ({ page }) => {
    // 先进行搜索
    await page.fill('.search-input input', '永川区');
    await page.click('button:has-text("搜索")');
    await page.waitForTimeout(1500);
    
    // 点击重置按钮
    await page.click('button:has-text("重置")');
    await page.waitForTimeout(1000);
    
    // 验证表单已重置
    await expect(page.locator('.search-input input')).toHaveValue('');
    
    // 验证表格数据已重置
    const rows = page.locator('table tbody tr');
    await expect(rows.first()).toBeVisible();
  });

  test('搜索记录功能测试', async ({ page }) => {
    // 先进行一次搜索以生成搜索记录
    await page.fill('.search-input input', '环保督察');
    await page.click('button:has-text("搜索")');
    await page.waitForTimeout(1500);
    
    // 点击搜索记录按钮
    await page.click('button:has-text("搜索记录")');
    
    // 验证搜索记录弹窗打开
    await expect(page.locator('.el-dialog__title')).toContainText('搜索记录');
    
    // 验证搜索记录存在
    const recordItems = page.locator('.record-item');
    await expect(recordItems.first()).toBeVisible();
    
    // 验证记录内容
    await expect(page.locator('.condition-tag').first()).toContainText('关键词: 环保督察');
    
    // 关闭弹窗
    await page.click('.el-dialog__footer button:has-text("关闭")');
  });

  test('批量删除功能测试', async ({ page }) => {
    // 选择前两个表格行的复选框
    const firstRowCheckbox = page.locator('table tbody tr:first-child .el-checkbox__input');
    const secondRowCheckbox = page.locator('table tbody tr:nth-child(2) .el-checkbox__input');
    
    await firstRowCheckbox.click();
    await secondRowCheckbox.click();
    
    // 验证批量操作区域显示
    await expect(page.locator('.batch-operations')).toBeVisible();
    
    // 验证选中数量（使用正则表达式匹配任意数量）
    await expect(page.locator('.selected-count')).toContainText(/已选择 \d+ 项/);
    
    // 点击批量删除按钮
    await page.click('button:has-text("属性值批量删除")');
    
    // 验证确认对话框
    await expect(page.locator('.el-message-box__title')).toContainText('批量删除确认');
    
    // 确认删除
    await page.click('.el-message-box__btns .el-button--primary');
    
    // 验证删除成功消息
    await expect(page.locator('.el-message--success')).toContainText('成功删除');
    
    // 验证批量操作区域隐藏
    await expect(page.locator('.batch-operations')).not.toBeVisible();
  });

  test('属性关联功能测试', async ({ page }) => {
    // 点击第一行的更多按钮
    const moreButton = page.locator('table tbody tr').first().locator('button:has-text("更多")');
    await moreButton.click();
    
    // 点击属性关联选项
    await page.click('.el-dropdown-menu__item:has-text("属性关联")');
    
    // 验证属性关联弹窗打开
    await expect(page.locator('.el-dialog__title')).toContainText('子任务属性关联');
    
    // 验证弹窗内容区域显示
    await expect(page.locator('.el-dialog__body')).toBeVisible();
    
    // 关闭弹窗
    await page.click('.el-dialog__headerbtn');
  });

  test('弹窗标题样式统一测试', async ({ page }) => {
    // 打开新增子任务属性弹窗
    await page.click('button:has-text("新增子任务属性")');
    
    // 验证弹窗标题样式
    const dialogTitle = page.locator('.el-dialog__title');
    await expect(dialogTitle).toBeVisible();
    
    // 验证标题可以拖拽（检查拖拽相关的CSS类）
    const dialog = page.locator('.el-dialog');
    await expect(dialog).toHaveClass(/is-draggable/);
    
    // 关闭弹窗
    await page.click('.el-dialog__headerbtn');
    
    // 打开属性类型管理弹窗
    await page.click('button:has-text("属性类型管理")');
    
    // 验证标题样式一致性
    await expect(page.locator('.el-dialog__title')).toBeVisible();
    
    // 关闭弹窗
    await page.click('.el-dialog__headerbtn');
  });

  test('操作按钮悬停效果测试', async ({ page }) => {
    // 悬停在第一行的操作按钮上
    const firstRow = page.locator('table tbody tr').first();
    const adjustButton = firstRow.locator('button:has-text("调整")');
    
    // 验证按钮存在
    await expect(adjustButton).toBeVisible();
    
    // 悬停测试
    await adjustButton.hover();
    
    // 验证更多按钮存在且可点击
    const moreButton = firstRow.locator('button:has-text("更多")');
    await expect(moreButton).toBeVisible();
    await expect(moreButton).toBeEnabled();
  });

  test('表格分页功能测试', async ({ page }) => {
    // 验证分页组件存在
    await expect(page.locator('.el-pagination')).toBeVisible();
    
    // 验证当前页码
    await expect(page.locator('.el-pager .is-active')).toContainText('1');
    
    // 验证总数显示
    await expect(page.locator('.el-pagination__total')).toContainText('共');
  });

  test('数据加载状态测试', async ({ page }) => {
    // 刷新页面测试加载状态
    await page.reload();
    
    // 验证加载状态
    await expect(page.locator('.el-loading-mask').first()).toBeVisible();
    
    // 等待加载完成
    await page.waitForTimeout(2000);
    
    // 验证加载状态消失
    await expect(page.locator('.el-loading-mask')).not.toBeVisible();
    
    // 验证数据已加载
    const tableRows = page.locator('table tbody tr');
    await expect(tableRows.first()).toBeVisible();
  });

  test('错误处理测试', async ({ page }) => {
    // 测试空搜索
    await page.click('button:has-text("搜索")');
    await page.waitForTimeout(1500);
    
    // 验证搜索完成消息（即使是空搜索也应该有反馈）
    await expect(page.locator('.el-message')).toBeVisible();
  });
});
