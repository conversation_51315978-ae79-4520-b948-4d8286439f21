import { test, expect } from '@playwright/test'

test.use({
  baseURL: 'http://localhost:5178'
})

test.describe('数据集成功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到报表整合度抽查页面
    await page.goto('/reportIntegrationSpotCheck')
    await page.waitForLoadState('networkidle')
  })

  test('应该能够从更多菜单导航到数据集成页面', async ({ page }) => {
    // 点击更多下拉菜单
    await page.click('text=更多')
    await page.waitForSelector('.el-dropdown-menu')
    
    // 验证数据集成选项存在
    await expect(page.locator('text=数据集成')).toBeVisible()
    
    // 点击数据集成选项
    await page.click('text=数据集成')
    
    // 验证页面跳转成功
    await expect(page).toHaveURL('/reportIntegrationSpotCheck/dataIntegration')
    await expect(page.locator('text=数据集成').first()).toBeVisible()
  })

  test('应该显示数据集成任务列表', async ({ page }) => {
    // 导航到数据集成页面
    await page.goto('/reportIntegrationSpotCheck/dataIntegration')
    await page.waitForLoadState('networkidle')
    
    // 验证页面标题
    await expect(page.locator('text=数据集成').first()).toBeVisible()
    
    // 验证返回按钮存在
    await expect(page.locator('text=返回')).toBeVisible()
    
    // 验证表格存在
    await expect(page.locator('.el-table')).toBeVisible()
    
    // 验证表头
    await expect(page.locator('text=集成任务名称')).toBeVisible()
    await expect(page.locator('text=创建时间')).toBeVisible()
    await expect(page.locator('text=进程')).toBeVisible()
    await expect(page.locator('text=状态')).toBeVisible()
    await expect(page.locator('text=日志')).toBeVisible()
    await expect(page.locator('text=数据质量报告')).toBeVisible()
    
    // 验证有数据行
    await expect(page.locator('.el-table__body tr')).toHaveCount({ min: 1 })
  })

  test('应该能够搜索任务', async ({ page }) => {
    await page.goto('/reportIntegrationSpotCheck/dataIntegration')
    await page.waitForLoadState('networkidle')
    
    // 输入搜索关键词
    await page.fill('input[placeholder*="集成任务名称"]', '任务1')
    
    // 点击查询按钮
    await page.click('text=查询')
    
    // 等待加载完成
    await page.waitForTimeout(1000)
    
    // 验证搜索结果
    const rows = page.locator('.el-table__body tr')
    await expect(rows).toHaveCount({ min: 1 })
  })

  test('应该能够生成和查看日志', async ({ page }) => {
    await page.goto('/reportIntegrationSpotCheck/dataIntegration')
    await page.waitForLoadState('networkidle')
    
    // 找到第一个"生成"按钮（日志列）
    const generateLogBtn = page.locator('text=生成').first()
    if (await generateLogBtn.isVisible()) {
      await generateLogBtn.click()
      
      // 验证成功消息
      await expect(page.locator('text=日志生成成功')).toBeVisible()
      
      // 等待按钮变为"查看"
      await page.waitForTimeout(1000)
      
      // 点击查看按钮
      await page.click('text=查看')
      
      // 验证日志弹窗打开
      await expect(page.locator('text=查看日志')).toBeVisible()
      
      // 验证日志表格
      await expect(page.locator('.log-dialog-content .el-table')).toBeVisible()
      
      // 关闭弹窗
      await page.press('body', 'Escape')
    }
  })

  test('应该能够生成数据质量报告', async ({ page }) => {
    await page.goto('/reportIntegrationSpotCheck/dataIntegration')
    await page.waitForLoadState('networkidle')
    
    // 找到数据质量报告列的"生成"按钮
    const generateReportBtns = page.locator('text=生成')
    const reportGenerateBtn = generateReportBtns.last()
    
    if (await reportGenerateBtn.isVisible()) {
      await reportGenerateBtn.click()
      
      // 验证成功消息
      await expect(page.locator('text=数据质量报告生成成功')).toBeVisible()
      
      // 等待按钮变化
      await page.waitForTimeout(2000)
      
      // 验证操作按钮出现
      await expect(page.locator('text=导出')).toBeVisible()
      await expect(page.locator('text=打印')).toBeVisible()
      await expect(page.locator('text=分享')).toBeVisible()
    }
  })

  test('应该能够执行批量操作', async ({ page }) => {
    await page.goto('/reportIntegrationSpotCheck/dataIntegration')
    await page.waitForLoadState('networkidle')
    
    // 选择第一行
    await page.click('.el-table__body tr:first-child .el-checkbox')
    
    // 验证批量导出按钮可用
    const exportBtn = page.locator('text=批量导出')
    await expect(exportBtn).not.toHaveClass(/is-disabled/)
    
    // 验证批量删除按钮可用
    const deleteBtn = page.locator('text=批量删除')
    await expect(deleteBtn).not.toHaveClass(/is-disabled/)
    
    // 测试批量导出
    await exportBtn.click()
    await expect(page.locator('text=导出成功')).toBeVisible()
  })

  test('应该能够使用返回按钮', async ({ page }) => {
    await page.goto('/reportIntegrationSpotCheck/dataIntegration')
    await page.waitForLoadState('networkidle')
    
    // 点击返回按钮
    await page.click('text=返回')
    
    // 验证返回到上一页
    await expect(page).toHaveURL('/reportIntegrationSpotCheck')
  })

  test('数据持久化测试', async ({ page }) => {
    await page.goto('/reportIntegrationSpotCheck/dataIntegration')
    await page.waitForLoadState('networkidle')

    // 记录初始数据数量
    const initialRowCount = await page.locator('.el-table__body tr').count()

    // 刷新页面
    await page.reload()
    await page.waitForLoadState('networkidle')

    // 验证数据仍然存在
    const afterReloadRowCount = await page.locator('.el-table__body tr').count()
    expect(afterReloadRowCount).toBe(initialRowCount)
  })
})
