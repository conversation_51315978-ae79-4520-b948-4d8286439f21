import { test, expect } from '@playwright/test'

test.describe('编辑子任务弹窗测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到任务目标拆解详情页面，使用free=true跳过认证
    await page.goto('/taskObjectiveDecomposition/detail/1?free=true')
    await page.waitForLoadState('networkidle')

    // 关闭可能存在的提示弹窗
    try {
      await page.getByRole('button', { name: '确定' }).click({ timeout: 2000 })
    } catch (e) {
      // 忽略错误，可能没有提示弹窗
    }
  })

  test('应该正确显示编辑子任务弹窗', async ({ page }) => {
    // 点击第一个子任务的编辑按钮
    await page.locator('table tbody tr:first-child').getByRole('button', { name: '编辑' }).click()

    // 等待编辑弹窗出现
    await page.waitForSelector('[aria-label="业务表子任务编辑"]')

    // 验证弹窗标题
    await expect(page.getByLabel('业务表子任务编辑')).toContainText('业务表子任务编辑')

    // 验证必填字段标签存在（使用更精确的选择器）
    await expect(page.getByLabel('业务表子任务编辑').getByText('子任务名称')).toBeVisible()
    await expect(page.getByLabel('业务表子任务编辑').getByText('子任务类型')).toBeVisible()
    await expect(page.getByLabel('业务表子任务编辑').getByText('子任务优先级设置')).toBeVisible()
    await expect(page.getByLabel('业务表子任务编辑').getByText('责任人')).toBeVisible()
    await expect(page.getByLabel('业务表子任务编辑').getByText('参与人')).toBeVisible()

    // 验证参与人权限配置按钮存在
    await expect(page.getByRole('button', { name: '参与人权限配置' })).toBeVisible()

    // 验证表单字段已填充数据
    await expect(page.getByLabel('业务表子任务编辑').getByRole('textbox').first()).toHaveValue('永川区民政局填报流程')
  })

  test('应该正确显示参与人权限设置弹窗', async ({ page }) => {
    // 打开编辑子任务弹窗
    await page.locator('table tbody tr:first-child').getByRole('button', { name: '编辑' }).click()
    await page.waitForSelector('[aria-label="业务表子任务编辑"]')

    // 点击参与人权限配置按钮
    await page.getByRole('button', { name: '参与人权限配置' }).click()

    // 等待权限设置弹窗出现
    await page.waitForSelector('[aria-label="参与人权限设置"]')

    // 验证权限设置弹窗标题
    await expect(page.getByLabel('参与人权限设置')).toContainText('参与人权限设置')

    // 验证表格表头（使用更精确的选择器）
    await expect(page.getByLabel('参与人权限设置').getByText('序号')).toBeVisible()
    await expect(page.getByLabel('参与人权限设置').getByText('参与人')).toBeVisible()
    await expect(page.getByLabel('参与人权限设置').getByText('授予权限')).toBeVisible()

    // 验证参与人列表
    const participants = ['张三', '李四', '王五', 'XXX']
    for (const participant of participants) {
      await expect(page.getByLabel('参与人权限设置').getByText(participant)).toBeVisible()
    }

    // 验证权限按钮
    const permissions = ['新增', '编辑', '删除']
    for (const permission of permissions) {
      await expect(page.getByLabel('参与人权限设置').getByRole('button', { name: permission }).first()).toBeVisible()
    }
  })

  test('应该能够切换权限按钮状态', async ({ page }) => {
    // 打开编辑子任务弹窗
    await page.locator('table tbody tr:first-child').getByRole('button', { name: '编辑' }).click()
    await page.waitForSelector('[role="dialog"]')

    // 打开权限设置弹窗
    await page.getByRole('button', { name: '参与人权限配置' }).click()
    await page.waitForSelector('[aria-label="参与人权限设置"]')

    // 获取第一个参与人的新增按钮
    const addButton = page.getByLabel('参与人权限设置').getByRole('button', { name: '新增' }).first()

    // 检查初始状态（应该是选中状态，type="primary"）
    await expect(addButton).toHaveClass(/el-button--primary/)

    // 点击切换状态
    await addButton.click()

    // 验证状态已切换（应该变为默认状态）
    await expect(addButton).toHaveClass(/el-button--default/)

    // 再次点击恢复状态
    await addButton.click()

    // 验证状态已恢复
    await expect(addButton).toHaveClass(/el-button--primary/)
  })

  test('应该能够保存权限配置并关闭弹窗', async ({ page }) => {
    // 打开编辑子任务弹窗
    await page.locator('table tbody tr:first-child').getByRole('button', { name: '编辑' }).click()
    await page.waitForSelector('[role="dialog"]')

    // 打开权限设置弹窗
    await page.getByRole('button', { name: '参与人权限配置' }).click()
    await page.waitForSelector('[aria-label="参与人权限设置"]')

    // 点击确定按钮
    await page.getByLabel('参与人权限设置').getByRole('button', { name: '确定' }).click()

    // 验证权限设置弹窗已关闭
    await expect(page.getByLabel('参与人权限设置')).not.toBeVisible()

    // 验证成功提示消息
    await expect(page.getByText('参与人权限配置成功')).toBeVisible()

    // 验证参与人字段已更新
    const participantInput = page.getByLabel('业务表子任务编辑').locator('input').nth(3) // 第4个输入框是参与人
    await expect(participantInput).toHaveValue('张三、李四、王五、XXX')

    // 验证主弹窗仍然打开
    await expect(page.getByLabel('业务表子任务编辑')).toBeVisible()
  })

  test('应该能够关闭主弹窗', async ({ page }) => {
    // 打开编辑子任务弹窗
    await page.locator('table tbody tr:first-child').getByRole('button', { name: '编辑' }).click()
    await page.waitForSelector('[role="dialog"]')

    // 验证弹窗已打开
    await expect(page.getByLabel('业务表子任务编辑')).toBeVisible()

    // 点击关闭按钮
    await page.getByRole('button', { name: '关闭此对话框' }).click()

    // 验证弹窗已关闭
    await expect(page.getByLabel('业务表子任务编辑')).not.toBeVisible()
  })

  test('应该能够通过取消按钮关闭主弹窗', async ({ page }) => {
    // 打开编辑子任务弹窗
    await page.locator('table tbody tr:first-child').getByRole('button', { name: '编辑' }).click()
    await page.waitForSelector('[role="dialog"]')

    // 验证弹窗已打开
    await expect(page.getByLabel('业务表子任务编辑')).toBeVisible()

    // 点击取消按钮
    await page.getByLabel('业务表子任务编辑').getByRole('button', { name: '取消' }).click()

    // 验证弹窗已关闭
    await expect(page.getByLabel('业务表子任务编辑')).not.toBeVisible()
  })

  test('应该验证必填字段', async ({ page }) => {
    // 打开编辑子任务弹窗
    await page.locator('table tbody tr:first-child').getByRole('button', { name: '编辑' }).click()
    await page.waitForSelector('[role="dialog"]')

    // 清空必填字段
    const nameInput = page.getByLabel('业务表子任务编辑').locator('input').first()
    const responsibleInput = page.getByLabel('业务表子任务编辑').locator('input').nth(2)

    await nameInput.clear()
    await responsibleInput.clear()

    // 点击确定按钮
    await page.getByLabel('业务表子任务编辑').getByRole('button', { name: '确定' }).click()

    // 验证表单验证错误信息
    await expect(page.getByText('请输入子任务名称')).toBeVisible()
    await expect(page.getByText('请输入责任人')).toBeVisible()
  })

  test('应该正确显示优先级选择器', async ({ page }) => {
    // 打开编辑子任务弹窗
    await page.locator('table tbody tr:first-child').getByRole('button', { name: '编辑' }).click()
    await page.waitForSelector('[role="dialog"]')

    // 点击优先级选择器（使用更稳定的选择器）
    await page.getByLabel('业务表子任务编辑').locator('.el-select').nth(1).click()

    // 验证优先级选项
    const priorities = ['高', '中', '低']
    for (const priority of priorities) {
      await expect(page.getByRole('option', { name: priority })).toBeVisible()
    }

    // 选择一个优先级
    await page.getByRole('option', { name: '高' }).click()

    // 验证选择结果
    await expect(page.getByLabel('业务表子任务编辑').locator('.el-select').nth(1)).toContainText('高')
  })
})
