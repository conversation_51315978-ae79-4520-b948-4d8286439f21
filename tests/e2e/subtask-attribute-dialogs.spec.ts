import { test, expect } from '@playwright/test';

test.describe('子任务属性管理弹窗功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到子任务属性管理页面
    await page.goto('/taskObjectiveDecomposition/subtaskAttributeManagement/1?free=true');

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
  });

  test('属性值统计弹窗功能测试', async ({ page }) => {
    // 点击属性值统计按钮
    await page.getByRole('button', { name: '属性值统计' }).click();
    
    // 验证弹窗打开
    await expect(page.locator('.el-dialog')).toContainText('子任务属性信息统计');
    
    // 等待数据加载完成
    await page.waitForTimeout(2000);
    
    // 验证统计数据显示
    await expect(page.locator('.statistics-table')).toBeVisible();
    await expect(page.locator('.statistics-summary')).toBeVisible();
    await expect(page.locator('.statistics-summary')).toContainText('临时报表高频属性值设置频次');
    
    // 测试导出功能
    const exportButton = page.getByRole('button', { name: '导出' });
    await exportButton.click();
    
    // 验证导出按钮状态变化
    await expect(exportButton).toBeDisabled();
    await page.waitForTimeout(2500); // 等待导出完成
    await expect(exportButton).toBeEnabled();
    
    // 测试确认功能
    await page.getByRole('button', { name: '确认' }).click();
    await page.waitForTimeout(1500);
    
    // 验证成功消息
    await expect(page.locator('.el-message').last()).toContainText('统计确认成功');
  });

  test('权限设置弹窗功能测试', async ({ page }) => {
    // 点击权限设置按钮
    await page.getByRole('button', { name: '权限设置' }).click();
    
    // 验证弹窗打开
    await expect(page.locator('.el-dialog')).toContainText('子任务属性权限设置');
    
    // 等待数据加载完成
    await page.waitForTimeout(1500);
    
    // 验证权限表格显示
    await expect(page.locator('.permission-table')).toBeVisible();
    
    // 验证初始权限数据
    await expect(page.locator('.permission-table .el-table__body')).toContainText('管理员');
    await expect(page.locator('.permission-table .el-table__body')).toContainText('台账运维员');
    
    // 测试添加权限设置功能
    const addButton = page.getByRole('button', { name: '添加权限设置' });
    await addButton.click();
    
    // 验证新行添加成功
    await expect(page.locator('.el-message').last()).toContainText('已添加新的权限设置行');
    
    // 测试权限复选框交互
    const checkboxes = page.locator('input[type="checkbox"]');
    const firstCheckbox = checkboxes.first();
    await firstCheckbox.click();
    
    // 测试保存功能
    await page.getByRole('button', { name: '保存' }).click();
    await page.waitForTimeout(2000);
    
    // 验证保存成功消息
    await expect(page.locator('.el-message').last()).toContainText('权限设置保存成功');
  });

  test('紧急程度规则弹窗功能测试', async ({ page }) => {
    // 点击紧急程度规则按钮
    await page.getByRole('button', { name: '紧急程度规则' }).click();
    
    // 验证弹窗打开
    await expect(page.locator('.el-dialog')).toContainText('子任务属性紧急程度规则');
    
    // 等待数据加载完成
    await page.waitForTimeout(1200);
    
    // 验证规则表格显示
    await expect(page.locator('.rules-table')).toBeVisible();
    await expect(page.locator('.rules-table .el-table__body')).toContainText('业务报表');
    await expect(page.locator('.rules-table .el-table__body')).toContainText('临时报表');
    
    // 测试规则录入功能
    await page.getByRole('button', { name: '规则录入' }).click();
    
    // 验证规则录入弹窗打开
    await expect(page.locator('.el-dialog')).toContainText('子任务属性紧急程度规则录入');
    
    // 填写表单
    await page.locator('select').first().selectOption('业务报表');
    await page.locator('select').nth(1).selectOption('特急');
    await page.locator('input[placeholder="请输入"]').fill('3');
    
    // 提交表单
    await page.getByRole('button', { name: '确认' }).click();
    await page.waitForTimeout(1200);
    
    // 验证规则添加成功
    await expect(page.locator('.el-message').last()).toContainText('规则录入成功');

    // 关闭主弹窗
    await page.getByRole('button', { name: '确认' }).click();
    await page.waitForTimeout(1200);

    // 验证确认成功消息
    await expect(page.locator('.el-message').last()).toContainText('规则确认成功');
  });

  test('风险等级管理弹窗功能测试', async ({ page }) => {
    // 点击风险等级管理按钮
    await page.getByRole('button', { name: '风险等级管理' }).click();
    
    // 验证弹窗打开
    await expect(page.locator('.el-dialog')).toContainText('属性风险等级分类管理');
    
    // 等待数据加载完成
    await page.waitForTimeout(1500);
    
    // 验证风险等级表格显示
    await expect(page.locator('.risk-level-table')).toBeVisible();
    await expect(page.locator('.risk-level-table .el-table__body')).toContainText('临时报表');
    await expect(page.locator('.risk-level-table .el-table__body')).toContainText('业务报表');
    
    // 验证风险等级标签显示
    await expect(page.locator('.el-tag')).toBeVisible();
    
    // 测试确认功能
    await page.getByRole('button', { name: '确认' }).click();
    await page.waitForTimeout(1200);
    
    // 验证确认成功消息
    await expect(page.locator('.el-message').last()).toContainText('风险等级管理确认成功');
  });

  test('历史记录弹窗功能测试', async ({ page }) => {
    // 点击历史记录按钮
    await page.getByRole('button', { name: '历史记录' }).click();
    
    // 验证弹窗打开
    await expect(page.locator('.el-dialog')).toContainText('子任务属性历史记录');
    
    // 等待数据加载完成
    await page.waitForTimeout(2000);
    
    // 验证标签页显示
    await expect(page.getByRole('tab', { name: '业务报表子任务' })).toBeVisible();
    await expect(page.getByRole('tab', { name: '临时报表子任务' })).toBeVisible();
    
    // 验证统计信息显示
    await expect(page.locator('.statistics-section').first()).toContainText('属性值修改记录统计');
    await expect(page.locator('.statistics-section').first()).toContainText('重要程度调整统计');
    await expect(page.locator('.statistics-section').first()).toContainText('风险等级调整统计');
    
    // 验证历史记录表格显示
    await expect(page.locator('.history-table').first()).toBeVisible();
    await expect(page.locator('.history-table .el-table__body').first()).toContainText('系统填报流程');
    await expect(page.locator('.history-table .el-table__body').first()).toContainText('永川区民政局填报流程');
    
    // 测试标签页切换
    await page.getByRole('tab', { name: '临时报表子任务' }).click();
    
    // 验证标签页切换成功
    await expect(page.getByRole('tab', { name: '临时报表子任务' })).toHaveAttribute('aria-selected', 'true');
    
    // 验证数据一致性（根据设计，两个标签页显示相同数据）
    await expect(page.locator('.statistics-section').first()).toContainText('属性值修改记录统计');
    
    // 测试确认功能
    await page.getByRole('button', { name: '确认' }).click();
    await page.waitForTimeout(1200);
    
    // 验证确认成功消息
    await expect(page.locator('.el-message').last()).toContainText('历史记录确认成功');
  });

  test('所有弹窗的取消功能测试', async ({ page }) => {
    const dialogs = [
      { button: '属性值统计', message: '已取消属性值统计' },
      { button: '权限设置', message: '已取消权限设置' },
      { button: '紧急程度规则', message: '已取消紧急程度规则' },
      { button: '风险等级管理', message: '已取消风险等级管理' },
      { button: '历史记录', message: '已取消历史记录查看' }
    ];

    for (const dialog of dialogs) {
      // 打开弹窗
      await page.getByRole('button', { name: dialog.button }).click();
      
      // 等待弹窗打开
      await page.waitForTimeout(500);
      
      // 点击取消按钮
      await page.getByRole('button', { name: '取消' }).click();
      
      // 验证取消消息
      await expect(page.locator('.el-message').last()).toContainText(dialog.message);
      
      // 等待消息消失
      await page.waitForTimeout(1000);
    }
  });

  test('弹窗加载状态测试', async ({ page }) => {
    // 测试属性值统计弹窗的加载状态
    await page.getByRole('button', { name: '属性值统计' }).click();

    // 验证弹窗打开
    await expect(page.locator('.el-dialog')).toContainText('子任务属性信息统计');

    // 等待加载完成
    await page.waitForTimeout(2000);

    // 验证数据显示
    await expect(page.locator('.statistics-table')).toBeVisible();

    // 关闭弹窗
    await page.getByRole('button', { name: '取消' }).click();
  });
});
