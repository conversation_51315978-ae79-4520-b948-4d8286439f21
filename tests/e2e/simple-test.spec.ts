import { test, expect } from '@playwright/test'

test.describe('简单页面访问测试', () => {
  test('应该能够访问主键定义页面', async ({ page }) => {
    // 导航到主键定义页面
    await page.goto('/primary-key-definition')
    
    // 等待页面标题加载
    await page.waitForSelector('h1, .page-title, .el-card__header', { timeout: 30000 })
    
    // 检查页面是否包含主键相关内容
    const pageContent = await page.textContent('body')
    expect(pageContent).toContain('主键')
    
    console.log('页面访问成功')
  })

  test('应该能够看到表格', async ({ page }) => {
    await page.goto('/index/primary-key-definition')
    
    // 等待表格加载
    await page.waitForSelector('table, .el-table, .table-v2', { timeout: 30000 })
    
    console.log('表格加载成功')
  })
})
