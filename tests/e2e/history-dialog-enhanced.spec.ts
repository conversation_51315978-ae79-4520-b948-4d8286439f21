import { test, expect } from '@playwright/test';

test.describe('历史记录弹窗增强功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到子任务属性管理页面
    await page.goto('/taskObjectiveDecomposition/subtaskAttributeManagement/1?free=true');
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
  });

  test('历史记录弹窗标签页数据切换测试', async ({ page }) => {
    // 点击历史记录按钮
    await page.getByRole('button', { name: '历史记录' }).click();
    
    // 验证弹窗打开
    await expect(page.locator('.el-dialog')).toContainText('子任务属性历史记录');
    
    // 等待业务报表数据加载完成
    await page.waitForTimeout(2000);
    
    // 验证业务报表标签页是默认选中的
    await expect(page.getByRole('tab', { name: '业务报表子任务' })).toHaveAttribute('aria-selected', 'true');
    
    // 验证业务报表数据存在
    await expect(page.locator('.statistics-section').first()).toContainText('属性值修改记录统计');
    
    // 记录业务报表的统计数据
    const businessStats = await page.locator('.statistics-section .stat-value').allTextContents();
    console.log('业务报表统计数据:', businessStats);
    
    // 切换到临时报表子任务标签页
    await page.getByRole('tab', { name: '临时报表子任务' }).click();
    
    // 验证标签页切换成功
    await expect(page.getByRole('tab', { name: '临时报表子任务' })).toHaveAttribute('aria-selected', 'true');
    
    // 等待临时报表数据加载
    await page.waitForTimeout(2000);
    
    // 验证临时报表数据加载成功消息
    await expect(page.locator('.el-message').last()).toContainText('临时报表历史记录数据加载完成');
    
    // 记录临时报表的统计数据
    const temporaryStats = await page.locator('.statistics-section .stat-value').allTextContents();
    console.log('临时报表统计数据:', temporaryStats);
    
    // 验证两个标签页的数据不同
    expect(businessStats).not.toEqual(temporaryStats);
    
    // 验证临时报表有自己的历史记录数据
    await expect(page.locator('.history-table .el-table__body').first()).toContainText('临时数据统计报表');
    
    // 切换回业务报表标签页
    await page.getByRole('tab', { name: '业务报表子任务' }).click();
    
    // 验证业务报表数据重新显示
    await expect(page.locator('.history-table .el-table__body').first()).toContainText('系统填报流程');
    
    // 关闭弹窗
    await page.getByRole('button', { name: '取消' }).click();
  });

  test('历史记录弹窗本地持久化测试', async ({ page }) => {
    // 清除本地存储
    await page.evaluate(() => {
      localStorage.removeItem('subtask-history-records');
    });
    
    // 第一次打开弹窗
    await page.getByRole('button', { name: '历史记录' }).click();
    await page.waitForTimeout(2000);
    
    // 切换到临时报表并等待数据加载
    await page.getByRole('tab', { name: '临时报表子任务' }).click();
    await page.waitForTimeout(2000);
    
    // 记录临时报表数据
    const temporaryStats = await page.locator('.statistics-section .stat-value').allTextContents();
    
    // 关闭弹窗
    await page.getByRole('button', { name: '取消' }).click();
    
    // 验证数据已保存到本地存储
    const storedData = await page.evaluate(() => {
      return localStorage.getItem('subtask-history-records');
    });
    expect(storedData).not.toBeNull();
    
    // 第二次打开弹窗
    await page.getByRole('button', { name: '历史记录' }).click();
    
    // 直接切换到临时报表标签页
    await page.getByRole('tab', { name: '临时报表子任务' }).click();
    
    // 验证数据从本地存储加载（应该很快显示，不需要重新加载）
    const cachedStats = await page.locator('.statistics-section .stat-value').allTextContents();
    expect(cachedStats).toEqual(temporaryStats);
    
    // 关闭弹窗
    await page.getByRole('button', { name: '取消' }).click();
  });

  test('历史记录确认后数据重置测试', async ({ page }) => {
    // 打开历史记录弹窗
    await page.getByRole('button', { name: '历史记录' }).click();
    await page.waitForTimeout(2000);
    
    // 验证有数据
    const initialStats = await page.locator('.statistics-section .stat-value').allTextContents();
    const hasData = initialStats.some(stat => parseInt(stat) > 0);
    expect(hasData).toBe(true);
    
    // 点击确认按钮
    await page.getByRole('button', { name: '确认' }).click();
    await page.waitForTimeout(1500);
    
    // 验证确认成功消息
    await expect(page.locator('.el-message').last()).toContainText('历史记录确认成功');
    
    // 重新打开弹窗
    await page.getByRole('button', { name: '历史记录' }).click();
    await page.waitForTimeout(1000);
    
    // 验证数据已重置为0
    const resetStats = await page.locator('.statistics-section .stat-value').allTextContents();
    const allZero = resetStats.every(stat => parseInt(stat) === 0);
    expect(allZero).toBe(true);
    
    // 验证历史记录表格为空
    await expect(page.locator('.history-table .el-table__body')).toContainText('暂无数据');
    
    // 关闭弹窗
    await page.getByRole('button', { name: '取消' }).click();
  });

  test('历史记录弹窗数据加载状态测试', async ({ page }) => {
    // 清除本地存储确保需要重新加载
    await page.evaluate(() => {
      localStorage.removeItem('subtask-history-records');
    });
    
    // 打开弹窗
    await page.getByRole('button', { name: '历史记录' }).click();
    
    // 验证加载状态
    await expect(page.locator('[v-loading="true"]')).toBeVisible();
    
    // 等待加载完成
    await page.waitForTimeout(2000);
    
    // 验证加载完成消息
    await expect(page.locator('.el-message').last()).toContainText('业务报表历史记录数据加载完成');
    
    // 切换标签页触发新的加载
    await page.getByRole('tab', { name: '临时报表子任务' }).click();
    
    // 验证再次加载
    await expect(page.locator('[v-loading="true"]')).toBeVisible();
    
    // 等待加载完成
    await page.waitForTimeout(2000);
    
    // 验证临时报表加载完成消息
    await expect(page.locator('.el-message').last()).toContainText('临时报表历史记录数据加载完成');
    
    // 关闭弹窗
    await page.getByRole('button', { name: '取消' }).click();
  });
});
