import { test, expect } from '@playwright/test'

test.describe('调试上移下移功能', () => {
  test('调试下移功能', async ({ page }) => {
    // 监听控制台日志
    const consoleLogs: string[] = []
    page.on('console', msg => {
      consoleLogs.push(`${msg.type()}: ${msg.text()}`)
    })

    // 访问页面
    await page.goto('/taskObjectiveDecomposition/detail/1?free=true')
    
    // 等待页面加载
    await page.waitForTimeout(8000)
    
    // 检查页面是否正确加载
    await expect(page.locator('h2:has-text("任务目标拆解")')).toBeVisible()
    
    // 检查表格是否存在
    const table = page.locator('.table-section .el-table')
    await expect(table).toBeVisible()
    
    // 获取所有数据行
    const allRows = page.locator('.table-section .el-table tbody tr')
    const rowCount = await allRows.count()
    console.log('数据行数:', rowCount)
    
    if (rowCount > 1) {
      // 获取第一行的任务名称
      const firstRowTaskName = await allRows.first().locator('td').nth(1).textContent()
      console.log('第一行任务名称:', firstRowTaskName)
      
      // 获取第二行的任务名称
      const secondRowTaskName = await allRows.nth(1).locator('td').nth(1).textContent()
      console.log('第二行任务名称:', secondRowTaskName)

      // 点击第一行的更多操作按钮
      const moreButton = allRows.first().locator('button:has-text("更多")')
      await expect(moreButton).toBeVisible()
      console.log('找到更多按钮')
      
      await moreButton.click()
      console.log('点击了更多按钮')

      // 等待下拉菜单出现
      await page.waitForTimeout(2000)

      // 直接检查下移按钮是否存在
      const moveDownButton = page.getByRole('menuitem', { name: '下移' })
      const moveDownExists = await moveDownButton.count() > 0
      console.log('下移按钮是否存在:', moveDownExists)

      if (moveDownExists) {
        console.log('点击下移按钮')
        await moveDownButton.click()

        // 等待操作完成
        await page.waitForTimeout(3000)

        // 检查消息
        const successCount = await page.locator('.el-message--success').count()
        const warningCount = await page.locator('.el-message--warning').count()
        const errorCount = await page.locator('.el-message--error').count()

        console.log('成功消息数量:', successCount)
        console.log('警告消息数量:', warningCount)
        console.log('错误消息数量:', errorCount)

        // 再次获取第一行任务名称
        const newFirstRowTaskName = await allRows.first().locator('td').nth(1).textContent()
        console.log('操作后第一行任务名称:', newFirstRowTaskName)

        // 获取新的第二行任务名称
        const newSecondRowTaskName = await allRows.nth(1).locator('td').nth(1).textContent()
        console.log('操作后第二行任务名称:', newSecondRowTaskName)

        // 检查是否发生了交换
        if (newFirstRowTaskName === secondRowTaskName && newSecondRowTaskName === firstRowTaskName) {
          console.log('✅ 下移操作成功：数据顺序已正确交换')
        } else if (newFirstRowTaskName === firstRowTaskName) {
          console.log('❌ 下移操作失败：第一行数据没有变化')
        } else {
          console.log('⚠️ 下移操作结果未知：数据发生了其他变化')
        }

        // 打印控制台日志
        console.log('=== 浏览器控制台日志 ===')
        consoleLogs.forEach(log => console.log(log))
      } else {
        console.log('没有找到下移按钮')
      }
    }
    
    // 基本断言 - 页面应该加载成功
    await expect(page.locator('body')).toBeVisible()
  })
})
