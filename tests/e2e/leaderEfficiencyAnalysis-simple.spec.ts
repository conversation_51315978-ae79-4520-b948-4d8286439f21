import { test, expect } from '@playwright/test'

test.describe('分管领导效率分析功能测试 - 简化版', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到分管领导效率分析页面，添加免登参数
    await page.goto('/dataSourceConfig/leaderEfficiencyAnalysis?free=true')
    
    // 等待页面加载完成，增加超时时间
    await page.waitForSelector('.leader-efficiency-analysis', { timeout: 10000 })
  })

  test('页面基本元素渲染正确', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('text=分管领导效率分析')).toBeVisible()

    // 检查表格是否存在 - 使用更精确的选择器
    await expect(page.locator('.el-table')).toBeVisible()

    // 检查分页组件
    await expect(page.locator('.el-pagination')).toBeVisible()
  })

  test('表格数据显示', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForSelector('table tbody tr', { timeout: 5000 })
    
    // 检查是否有数据行
    const rows = await page.locator('table tbody tr').count()
    expect(rows).toBeGreaterThan(0)
    expect(rows).toBeLessThanOrEqual(10) // 默认每页10条
  })

  test('分页功能基本测试', async ({ page }) => {
    // 等待数据加载
    await page.waitForSelector('table tbody tr')
    
    // 检查分页信息
    const paginationInfo = page.locator('.el-pagination__total')
    await expect(paginationInfo).toBeVisible()
  })
})
