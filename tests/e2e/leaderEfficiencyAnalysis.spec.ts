import { test, expect } from '@playwright/test'

test.describe('分管领导效率分析功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到分管领导效率分析页面，添加免登参数
    await page.goto('/dataSourceConfig/leaderEfficiencyAnalysis?free=true')

    // 等待页面加载完成，增加超时时间
    await page.waitForSelector('.leader-efficiency-analysis', { timeout: 10000 })
  })

  test('页面基本元素渲染正确', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('text=分管领导效率分析')).toBeVisible()

    // 检查表格表头 - 使用更精确的选择器
    await expect(page.locator('table th').filter({ hasText: '分管领导' })).toBeVisible()
    await expect(page.locator('table th').filter({ hasText: '今日审批量' })).toBeVisible()
    await expect(page.locator('table th').filter({ hasText: '平均审核耗时' })).toBeVisible()
    await expect(page.locator('table th').filter({ hasText: '驳回次数' })).toBeVisible()
    await expect(page.locator('table th').filter({ hasText: '敏感操作次数' })).toBeVisible()

    // 检查分页组件
    await expect(page.locator('.el-pagination')).toBeVisible()
  })

  test('模拟数据生成和显示', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForSelector('table tbody tr', { timeout: 5000 })
    
    // 检查是否有数据行
    const rows = await page.locator('table tbody tr').count()
    expect(rows).toBeGreaterThan(0)
    expect(rows).toBeLessThanOrEqual(10) // 默认每页10条
    
    // 检查第一行数据格式
    const firstRow = page.locator('table tbody tr').first()
    await expect(firstRow.locator('td').nth(0)).toContainText(/\S+/) // 分管领导名称不为空
    await expect(firstRow.locator('td').nth(1)).toContainText(/\d+/) // 今日审批量为数字
    await expect(firstRow.locator('td').nth(2)).toContainText(/\d+h/) // 平均审核耗时格式
    await expect(firstRow.locator('td').nth(3)).toContainText(/\d+/) // 驳回次数为数字
    await expect(firstRow.locator('td').nth(4)).toContainText(/\d+/) // 敏感操作次数为数字
  })

  test('分页功能正常工作', async ({ page }) => {
    // 等待数据加载
    await page.waitForSelector('table tbody tr')
    
    // 检查总数据量
    const totalText = await page.locator('.el-pagination__total').textContent()
    expect(totalText).toMatch(/共 \d+ 条/)
    
    // 如果有多页，测试翻页功能
    const nextButton = page.locator('.el-pagination .btn-next')
    if (await nextButton.isEnabled()) {
      // 记录第一页第一行数据
      const firstPageFirstRowText = await page.locator('table tbody tr').first().textContent()
      
      // 点击下一页
      await nextButton.click()
      await page.waitForTimeout(500) // 等待数据更新
      
      // 检查数据是否变化
      const secondPageFirstRowText = await page.locator('table tbody tr').first().textContent()
      expect(firstPageFirstRowText).not.toBe(secondPageFirstRowText)
      
      // 返回第一页
      await page.locator('.el-pagination .btn-prev').click()
      await page.waitForTimeout(500)
      
      // 验证回到第一页
      const backToFirstPageText = await page.locator('table tbody tr').first().textContent()
      expect(backToFirstPageText).toBe(firstPageFirstRowText)
    }
  })

  test('每页显示数量切换功能', async ({ page }) => {
    // 等待数据加载
    await page.waitForSelector('table tbody tr')
    
    // 点击每页显示数量选择器
    await page.locator('.el-pagination__sizes .el-select').click()
    
    // 选择20条每页
    await page.locator('text=20 条/页').click()
    await page.waitForTimeout(500)
    
    // 检查表格行数是否更新
    const rows = await page.locator('table tbody tr').count()
    expect(rows).toBeLessThanOrEqual(20)
  })

  test('数据持久化功能', async ({ page }) => {
    // 等待数据加载
    await page.waitForSelector('table tbody tr')

    // 获取当前数据
    const originalData = await page.evaluate(() => {
      return localStorage.getItem('leader_efficiency_data')
    })

    expect(originalData).toBeTruthy()

    // 刷新页面，保持免登参数
    await page.goto('/dataSourceConfig/leaderEfficiencyAnalysis?free=true')
    await page.waitForSelector('table tbody tr')

    // 检查数据是否保持一致
    const reloadedData = await page.evaluate(() => {
      return localStorage.getItem('leader_efficiency_data')
    })

    expect(reloadedData).toBe(originalData)
  })

  test('表格响应式布局', async ({ page }) => {
    // 测试不同屏幕尺寸下的表格显示
    await page.setViewportSize({ width: 1200, height: 800 })
    await page.waitForSelector('table')
    
    // 检查表格是否可见
    await expect(page.locator('table')).toBeVisible()
    
    // 测试小屏幕
    await page.setViewportSize({ width: 768, height: 600 })
    await page.waitForTimeout(500)
    
    // 表格应该仍然可见
    await expect(page.locator('table')).toBeVisible()
  })

  test('数据格式验证', async ({ page }) => {
    // 等待数据加载
    await page.waitForSelector('table tbody tr')
    
    // 获取所有数据行
    const rows = await page.locator('table tbody tr').all()
    
    for (const row of rows) {
      const cells = await row.locator('td').all()
      
      // 验证分管领导名称不为空
      const leaderName = await cells[0].textContent()
      expect(leaderName?.trim()).toBeTruthy()
      
      // 验证今日审批量为数字
      const approvalCount = await cells[1].textContent()
      expect(approvalCount).toMatch(/^\d+$/)
      
      // 验证平均审核耗时格式
      const avgTime = await cells[2].textContent()
      expect(avgTime).toMatch(/^\d+h$/)
      
      // 验证驳回次数为数字
      const rejectCount = await cells[3].textContent()
      expect(rejectCount).toMatch(/^\d+$/)
      
      // 验证敏感操作次数为数字
      const sensitiveCount = await cells[4].textContent()
      expect(sensitiveCount).toMatch(/^\d+$/)
    }
  })
})
