import { test, expect } from '@playwright/test'

test.describe('任务目标分解详情页面 - 功能增强测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到详情页面
    await page.goto('http://localhost:5176/taskObjectiveDecomposition/detail/1?free=true')
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(3000)
  })

  test('数据展示优化 - 表格数据正确显示', async ({ page }) => {
    // 验证子任务列表标题
    await expect(page.locator('h3')).toContainText('详情')
    
    // 验证表格头部
    await expect(page.locator('text=子任务名称')).toBeVisible()
    await expect(page.locator('text=子任务类型')).toBeVisible()
    await expect(page.locator('text=任务状态')).toBeVisible()
    await expect(page.locator('text=任务进度')).toBeVisible()
    
    // 验证数据正确显示（不再是"-"）
    const firstRowData = page.locator('table tbody tr:first-child')
    await expect(firstRowData.locator('td:nth-child(2)')).not.toContainText('-')
    await expect(firstRowData.locator('td:nth-child(3)')).not.toContainText('-')
    
    // 验证进度条显示
    await expect(page.locator('div[role="progressbar"]')).toBeVisible()
    
    // 验证操作按钮
    await expect(page.locator('button:has-text("编辑")')).toBeVisible()
    await expect(page.locator('button:has-text("删除")')).toBeVisible()
    await expect(page.locator('button:has-text("复制")')).toBeVisible()
  })

  test('编辑功能完善 - 编辑子任务弹窗', async ({ page }) => {
    // 点击编辑按钮
    await page.click('table tbody tr:first-child button:has-text("编辑")')
    
    // 验证编辑弹窗打开
    await expect(page.locator('text=编辑子任务')).toBeVisible()
    
    // 验证表单字段已填充数据
    const taskNameInput = page.locator('input[placeholder*="子任务名称"]')
    await expect(taskNameInput).not.toHaveValue('')
    
    // 验证表单控件
    await expect(page.locator('text=子任务类型')).toBeVisible()
    await expect(page.locator('text=子任务分类')).toBeVisible()
    await expect(page.locator('text=责任人')).toBeVisible()
    await expect(page.locator('text=参与人')).toBeVisible()
    await expect(page.locator('text=任务状态')).toBeVisible()
    await expect(page.locator('text=任务进度')).toBeVisible()
    
    // 验证进度滑块
    await expect(page.locator('div[role="slider"]')).toBeVisible()
    
    // 验证字符计数
    await expect(page.locator('text=/ 50')).toBeVisible()
    await expect(page.locator('text=/ 100')).toBeVisible()
    
    // 关闭弹窗
    await page.click('button:has-text("取消")')
    await expect(page.locator('text=编辑子任务')).not.toBeVisible()
  })

  test('编辑功能完善 - 新增子任务弹窗', async ({ page }) => {
    // 点击新增子任务按钮
    await page.click('button:has-text("新增子任务")')
    
    // 验证新增弹窗打开
    await expect(page.locator('text=新增子任务')).toBeVisible()
    
    // 验证表单字段为空
    const taskNameInput = page.locator('input[placeholder*="子任务名称"]')
    await expect(taskNameInput).toHaveValue('')
    
    // 验证默认状态
    await expect(page.locator('text=未开始')).toBeVisible()
    
    // 验证进度默认为0
    const progressInput = page.locator('input[type="number"]')
    await expect(progressInput).toHaveValue('0')
    
    // 验证字符计数显示0
    await expect(page.locator('text=0 / 50')).toBeVisible()
    await expect(page.locator('text=0 / 100')).toBeVisible()
    
    // 关闭弹窗
    await page.click('button:has-text("取消")')
    await expect(page.locator('text=新增子任务')).not.toBeVisible()
  })

  test('批量操作增强 - 批量操作按钮状态管理', async ({ page }) => {
    // 验证初始状态下批量操作按钮被禁用
    await expect(page.locator('button:has-text("批量删除")')).toBeDisabled()
    await expect(page.locator('button:has-text("批量状态更新")')).toBeDisabled()
    await expect(page.locator('button:has-text("批量分配责任人")')).toBeDisabled()
    
    // 验证新增子任务按钮始终可用
    await expect(page.locator('button:has-text("新增子任务")')).not.toBeDisabled()
    
    // 选择第一行
    await page.click('table tbody tr:first-child input[type="checkbox"]')
    
    // 验证批量操作按钮状态变化
    await expect(page.locator('button:has-text("批量删除")')).not.toBeDisabled()
    await expect(page.locator('button:has-text("批量状态更新")')).not.toBeDisabled()
    await expect(page.locator('button:has-text("批量分配责任人")')).not.toBeDisabled()
    
    // 验证按钮显示选中数量
    await expect(page.locator('button:has-text("批量删除 (1)")')).toBeVisible()
    
    // 取消选择
    await page.click('table tbody tr:first-child input[type="checkbox"]')
    
    // 验证按钮重新被禁用
    await expect(page.locator('button:has-text("批量删除")')).toBeDisabled()
    await expect(page.locator('button:has-text("批量删除 (0)")')).toBeVisible()
  })

  test('数据导出功能增强', async ({ page }) => {
    // 设置下载监听
    const downloadPromise = page.waitForEvent('download')
    
    // 点击导出按钮
    await page.click('button:has-text("导出")')
    
    // 等待下载完成
    const download = await downloadPromise
    
    // 验证文件名包含预期内容
    expect(download.suggestedFilename()).toContain('子任务列表')
    expect(download.suggestedFilename()).toContain('.csv')
    expect(download.suggestedFilename()).toMatch(/\d{4}-\d{2}-\d{2}/)
    
    // 验证成功消息
    await expect(page.locator('text=导出成功')).toBeVisible()
  })

  test('复制和删除功能验证', async ({ page }) => {
    // 获取初始行数
    const initialRows = await page.locator('table tbody tr').count()
    
    // 测试复制功能
    await page.click('table tbody tr:first-child button:has-text("复制")')
    await page.waitForTimeout(1000)
    
    // 验证复制成功
    await expect(page.locator('text=复制成功')).toBeVisible()
    const newRows = await page.locator('table tbody tr').count()
    expect(newRows).toBe(initialRows + 1)
    
    // 测试删除功能
    await page.click('table tbody tr:first-child button:has-text("删除")')
    await page.waitForSelector('text=确定要删除')
    await page.click('button:has-text("确定")')
    await page.waitForTimeout(1000)
    
    // 验证删除成功
    await expect(page.locator('text=删除成功')).toBeVisible()
    const finalRows = await page.locator('table tbody tr').count()
    expect(finalRows).toBe(initialRows)
  })

  test('搜索和重置功能', async ({ page }) => {
    // 输入搜索关键词
    await page.fill('input[placeholder="请输入任务名称"]', '永川区民政局')
    
    // 点击查询按钮
    await page.click('button:has-text("查询")')
    
    // 等待搜索结果
    await page.waitForTimeout(1000)
    
    // 验证搜索结果
    await expect(page.locator('text=搜索完成')).toBeVisible()
    
    // 点击重置按钮
    await page.click('button:has-text("重置")')
    
    // 验证重置结果
    await expect(page.locator('text=已重置搜索条件')).toBeVisible()
  })

  test('页面基本信息显示', async ({ page }) => {
    // 验证页面标题
    await expect(page.locator('h2')).toContainText('任务目标拆解')
    
    // 验证任务基本信息
    await expect(page.locator('text=任务名称：')).toBeVisible()
    await expect(page.locator('text=任务类型：')).toBeVisible()
    await expect(page.locator('text=创建部门/创建责任人：')).toBeVisible()
    await expect(page.locator('text=开始时间：')).toBeVisible()
    
    // 验证子任务统计信息
    await expect(page.locator('text=业务报表子任务数量：')).toBeVisible()
    await expect(page.locator('text=临时报表子任务数量：')).toBeVisible()
  })

  test('分页功能验证', async ({ page }) => {
    // 验证分页信息显示
    await expect(page.locator('text=共')).toBeVisible()
    await expect(page.locator('text=条')).toBeVisible()
    
    // 验证分页控件
    await expect(page.locator('button:has-text("上一页")')).toBeVisible()
    await expect(page.locator('button:has-text("下一页")')).toBeVisible()
  })
})
