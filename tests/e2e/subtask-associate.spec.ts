import { test, expect } from '@playwright/test'

test.describe('子任务关联任务计算页面', () => {
  test.beforeEach(async ({ page }) => {
    // 访问页面，添加free参数跳过认证
    await page.goto('/taskObjectiveDecomposition/associate/1?free=true')
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle')
  })

  test('页面基本元素渲染正确', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('h2')).toContainText('子任务关联任务计算')

    // 检查返回按钮（使用更精确的选择器）
    await expect(page.locator('.header-left button')).toContainText('返回')

    // 检查头部操作按钮
    await expect(page.locator('text=关联关系实际计算')).toBeVisible()
    await expect(page.locator('text=确认关联')).toBeVisible()
    await expect(page.locator('text=取消')).toBeVisible()
  })

  test('任务基本信息展示正确', async ({ page }) => {
    // 等待任务信息加载
    await page.waitForSelector('.task-info-section')
    
    // 检查任务基本信息标题
    await expect(page.locator('.task-info-section h3')).toContainText('任务基本信息')
    
    // 检查信息字段
    await expect(page.locator('text=子任务名称：')).toBeVisible()
    await expect(page.locator('text=永川区民政局填报清单')).toBeVisible()
    
    await expect(page.locator('text=任务类型：')).toBeVisible()
    await expect(page.locator('text=业务报表')).toBeVisible()
    
    await expect(page.locator('text=任务分类：')).toBeVisible()
    await expect(page.locator('text=党的建设')).toBeVisible()
    
    await expect(page.locator('text=责任人：')).toBeVisible()
    await expect(page.locator('text=张飞')).toBeVisible()
    
    await expect(page.locator('text=参与人：')).toBeVisible()
  })

  test('业务表列表展示正确', async ({ page }) => {
    // 等待表格加载
    await page.waitForSelector('.business-table-section')

    // 检查业务表标题
    await expect(page.locator('.business-table-section h3')).toContainText('业务表')

    // 检查表格列头（使用更精确的选择器）
    await expect(page.locator('th').filter({ hasText: '序号' })).toBeVisible()
    await expect(page.locator('th').filter({ hasText: '业务表名称' })).toBeVisible()
    await expect(page.locator('th').filter({ hasText: '发布部门' })).toBeVisible()
    await expect(page.locator('th').filter({ hasText: '所属板块' })).toBeVisible()
    await expect(page.locator('th').filter({ hasText: '所属通道' })).toBeVisible()
    await expect(page.locator('th').filter({ hasText: '更新周期' })).toBeVisible()
    await expect(page.locator('th').filter({ hasText: '截止时间' })).toBeVisible()
    await expect(page.locator('th').filter({ hasText: '操作' })).toBeVisible()

    // 检查表格数据
    await expect(page.locator('text=登记失业人员信息表')).toBeVisible()
    await expect(page.locator('text=渝中区人力资源和社会保障局')).toBeVisible()
    await expect(page.locator('text=民生服务')).toBeVisible()
    await expect(page.locator('text=创业就业/就业服务')).toBeVisible()
  })

  test('操作按钮功能正常', async ({ page }) => {
    // 等待页面加载完成
    await page.waitForSelector('.business-table-section')

    // 等待表格数据加载
    await page.waitForSelector('text=登记失业人员信息表')

    // 测试"可跳转计算方式"按钮
    await page.locator('button').filter({ hasText: '可跳转计算方式' }).first().click()

    // 检查消息提示
    await expect(page.locator('.el-message')).toContainText('跳转计算方式')

    // 等待消息消失
    await page.waitForTimeout(1000)

    // 测试"移除"按钮
    await page.locator('button').filter({ hasText: '移除' }).first().click()

    // 检查移除成功消息
    await expect(page.locator('.el-message')).toContainText('已移除关联')
  })

  test('返回按钮功能正常', async ({ page }) => {
    // 点击返回按钮（使用更精确的选择器）
    await page.locator('.header-left button').click()

    // 检查是否返回到上一页（这里可能需要根据实际情况调整）
    await page.waitForTimeout(500)
  })

  test('头部操作按钮功能正常', async ({ page }) => {
    // 测试"关联关系实际计算"按钮
    await page.locator('text=关联关系实际计算').click()
    
    // 检查消息提示
    await expect(page.locator('.el-message')).toContainText('确认关联关系实际计算')
  })

  test('响应式布局正常', async ({ page }) => {
    // 测试不同屏幕尺寸下的布局
    await page.setViewportSize({ width: 1200, height: 800 })
    await expect(page.locator('.page-header')).toBeVisible()
    
    await page.setViewportSize({ width: 768, height: 600 })
    await expect(page.locator('.page-header')).toBeVisible()
    
    await page.setViewportSize({ width: 375, height: 667 })
    await expect(page.locator('.page-header')).toBeVisible()
  })

  test('加载状态正常', async ({ page }) => {
    // 重新访问页面以测试加载状态
    await page.goto('/taskObjectiveDecomposition/associate/1?free=true')
    
    // 检查加载状态（可能需要根据实际加载时间调整）
    await page.waitForSelector('.subtask-associate')
    
    // 确保数据加载完成后内容可见
    await expect(page.locator('.task-info-section')).toBeVisible()
    await expect(page.locator('.business-table-section')).toBeVisible()
  })
})
