import { test, expect } from '@playwright/test'

test.describe('子任务关系展示弹窗', () => {
  test.beforeEach(async ({ page }) => {
    // 访问测试页面
    await page.goto('/test/subtask-relation?free=true')
    await page.waitForLoadState('networkidle')
  })

  test('页面基本元素显示正常', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('h1')).toContainText('子任务关系展示弹窗测试')
    
    // 检查打开弹窗按钮
    const openButton = page.locator('button:has-text("打开子任务关系展示弹窗")')
    await expect(openButton).toBeVisible()
    
    // 检查功能特性说明
    await expect(page.locator('h2:has-text("功能特性")')).toBeVisible()
    await expect(page.locator('h3:has-text("业务报表子任务")')).toBeVisible()
    await expect(page.locator('h3:has-text("临时报表子任务")')).toBeVisible()
  })

  test('弹窗打开和关闭功能', async ({ page }) => {
    // 点击打开弹窗按钮
    await page.click('button:has-text("打开子任务关系展示弹窗")')
    
    // 等待弹窗出现
    const dialog = page.locator('.subtask-relation-dialog')
    await expect(dialog).toBeVisible()
    
    // 检查弹窗标题
    await expect(page.locator('.el-dialog__title')).toContainText('子任务关系展示')
    
    // 检查tab标签
    await expect(page.locator('.el-tabs__item:has-text("业务报表子任务")')).toBeVisible()
    await expect(page.locator('.el-tabs__item:has-text("临时报表子任务")')).toBeVisible()
    
    // 点击关闭按钮
    await page.click('.el-dialog__close')
    
    // 验证弹窗已关闭
    await expect(dialog).not.toBeVisible()
  })

  test('业务报表子任务tab功能', async ({ page }) => {
    // 打开弹窗
    await page.click('button:has-text("打开子任务关系展示弹窗")')
    await page.waitForSelector('.subtask-relation-dialog')
    
    // 默认应该在业务报表tab
    const businessTab = page.locator('.el-tabs__item:has-text("业务报表子任务")')
    await expect(businessTab).toHaveClass(/is-active/)
    
    // 检查流程图工具栏
    await expect(page.locator('text=可视化布局设置')).toBeVisible()
    await expect(page.locator('text=缩放:')).toBeVisible()
    await expect(page.locator('text=120%')).toBeVisible()
    
    // 检查Vue Flow容器
    const vueFlowContainer = page.locator('.vue-flow-container')
    await expect(vueFlowContainer).toBeVisible()
    
    // 检查流程节点（等待Vue Flow渲染完成）
    await page.waitForTimeout(1000)
    const nodes = page.locator('.vue-flow__node')
    await expect(nodes.first()).toBeVisible()
    
    // 检查右侧分析面板
    await expect(page.locator('h4:has-text("倾向关系分析")')).toBeVisible()
    await expect(page.locator('text=直接依赖: 3个')).toBeVisible()
    await expect(page.locator('text=间接依赖: 1个')).toBeVisible()
    await expect(page.locator('text=平均: 25分钟')).toBeVisible()
    await expect(page.locator('text=最长: 45分钟')).toBeVisible()
  })

  test('临时报表子任务tab功能', async ({ page }) => {
    // 打开弹窗
    await page.click('button:has-text("打开子任务关系展示弹窗")')
    await page.waitForSelector('.subtask-relation-dialog')
    
    // 切换到临时报表tab
    await page.click('.el-tabs__item:has-text("临时报表子任务")')
    
    // 验证tab切换成功
    const temporaryTab = page.locator('.el-tabs__item:has-text("临时报表子任务")')
    await expect(temporaryTab).toHaveClass(/is-active/)
    
    // 检查横向关系表格
    await expect(page.locator('h4:has-text("横向关系")')).toBeVisible()
    const horizontalTable = page.locator('.table-section').first().locator('.el-table')
    await expect(horizontalTable).toBeVisible()
    
    // 检查表格列标题
    await expect(page.locator('th:has-text("序号")')).toBeVisible()
    await expect(page.locator('th:has-text("横向部门名称")')).toBeVisible()
    await expect(page.locator('th:has-text("部门层级")')).toBeVisible()
    await expect(page.locator('th:has-text("响应状态")')).toBeVisible()
    await expect(page.locator('th:has-text("响应时间")')).toBeVisible()
    
    // 检查表格数据
    await expect(page.locator('td:has-text("经济生态板块工作人员")')).toBeVisible()
    await expect(page.locator('td:has-text("公共服务工作人员")')).toBeVisible()
    
    // 检查状态标签
    const statusTags = page.locator('.el-tag')
    await expect(statusTags.first()).toBeVisible()
    
    // 检查纵向关系表格
    await expect(page.locator('h4:has-text("纵向关系")')).toBeVisible()
    const verticalTable = page.locator('.table-section').nth(1).locator('.el-table')
    await expect(verticalTable).toBeVisible()
    
    // 检查纵向表格数据
    await expect(page.locator('td:has-text("金汤街社区")')).toBeVisible()
    await expect(page.locator('td:has-text("华一路社区")')).toBeVisible()
    await expect(page.locator('td:has-text("掌卫路社区")')).toBeVisible()
    
    // 检查分页组件
    const pagination = page.locator('.el-pagination')
    await expect(pagination.first()).toBeVisible()
    
    // 检查右侧分析面板（临时报表tab也有）
    await expect(page.locator('h4:has-text("倾向关系分析")')).toBeVisible()
  })

  test('tab切换功能', async ({ page }) => {
    // 打开弹窗
    await page.click('button:has-text("打开子任务关系展示弹窗")')
    await page.waitForSelector('.subtask-relation-dialog')
    
    // 验证默认在业务报表tab
    const businessTab = page.locator('.el-tabs__item:has-text("业务报表子任务")')
    const temporaryTab = page.locator('.el-tabs__item:has-text("临时报表子任务")')
    
    await expect(businessTab).toHaveClass(/is-active/)
    await expect(temporaryTab).not.toHaveClass(/is-active/)
    
    // 切换到临时报表tab
    await temporaryTab.click()
    await expect(temporaryTab).toHaveClass(/is-active/)
    await expect(businessTab).not.toHaveClass(/is-active/)
    
    // 切换回业务报表tab
    await businessTab.click()
    await expect(businessTab).toHaveClass(/is-active/)
    await expect(temporaryTab).not.toHaveClass(/is-active/)
  })

  test('弹窗底部按钮功能', async ({ page }) => {
    // 打开弹窗
    await page.click('button:has-text("打开子任务关系展示弹窗")')
    await page.waitForSelector('.subtask-relation-dialog')
    
    // 检查底部按钮
    const cancelButton = page.locator('.dialog-footer button:has-text("取消")')
    const confirmButton = page.locator('.dialog-footer button:has-text("确认")')
    
    await expect(cancelButton).toBeVisible()
    await expect(confirmButton).toBeVisible()
    
    // 点击取消按钮
    await cancelButton.click()
    
    // 验证弹窗已关闭
    await expect(page.locator('.subtask-relation-dialog')).not.toBeVisible()
    
    // 重新打开弹窗测试确认按钮
    await page.click('button:has-text("打开子任务关系展示弹窗")')
    await page.waitForSelector('.subtask-relation-dialog')
    
    // 点击确认按钮
    await page.click('.dialog-footer button:has-text("确认")')
    
    // 验证弹窗已关闭
    await expect(page.locator('.subtask-relation-dialog')).not.toBeVisible()
  })

  test('响应式设计验证', async ({ page }) => {
    // 测试不同屏幕尺寸下的显示效果
    
    // 桌面尺寸
    await page.setViewportSize({ width: 1200, height: 800 })
    await page.click('button:has-text("打开子任务关系展示弹窗")')
    await page.waitForSelector('.subtask-relation-dialog')
    
    // 验证弹窗在桌面尺寸下正常显示
    const dialog = page.locator('.el-dialog')
    await expect(dialog).toBeVisible()
    
    // 关闭弹窗
    await page.click('.el-dialog__close')
    
    // 平板尺寸
    await page.setViewportSize({ width: 768, height: 600 })
    await page.click('button:has-text("打开子任务关系展示弹窗")')
    await page.waitForSelector('.subtask-relation-dialog')
    
    // 验证弹窗在平板尺寸下正常显示
    await expect(dialog).toBeVisible()
    
    // 关闭弹窗
    await page.click('.el-dialog__close')
  })

  test('数据持久化功能', async ({ page }) => {
    // 清除localStorage
    await page.evaluate(() => localStorage.clear())
    
    // 打开弹窗
    await page.click('button:has-text("打开子任务关系展示弹窗")')
    await page.waitForSelector('.subtask-relation-dialog')
    
    // 切换到临时报表tab查看数据
    await page.click('.el-tabs__item:has-text("临时报表子任务")')
    
    // 验证数据存在
    await expect(page.locator('td:has-text("经济生态板块工作人员")')).toBeVisible()
    
    // 关闭弹窗
    await page.click('.el-dialog__close')
    
    // 检查localStorage中是否保存了数据
    const storageData = await page.evaluate(() => {
      return localStorage.getItem('subtask-relation-data')
    })
    
    expect(storageData).toBeTruthy()
    
    // 解析存储的数据
    const parsedData = JSON.parse(storageData)
    expect(parsedData.horizontalTableData).toBeDefined()
    expect(parsedData.verticalTableData).toBeDefined()
    expect(parsedData.analysisData).toBeDefined()
  })
})
