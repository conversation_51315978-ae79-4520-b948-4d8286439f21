import {defineConfig, loadEnv} from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import viteCompression from 'vite-plugin-compression'
import pages from 'vite-plugin-pages'
import VueSetupExtend from 'vite-plugin-vue-setup-extend'
import {visualizer} from 'rollup-plugin-visualizer'

import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import {ElementPlusResolver} from 'unplugin-vue-components/resolvers'

const timestamp = new Date().getTime()

export default ({mode, command}) => {
	const isBuild = command === 'build'
	const env = loadEnv(mode, process.cwd())
	return defineConfig({
		// base: process.env.NODE_ENV == 'production' ? '/' : '/api',
		define: {
			'process.env': env,

			__VUE_OPTIONS_API__: true,
			__VUE_PROD_DEVTOOLS__: false,
			__VUE_PROD_HYDRATION_MISMATCH_DETAILS__: true,
		},
		plugins: [
			vue(),

			// 自动导入 Vue API，比如 ref、computed、defineProps 等
			AutoImport({
				imports: ['vue', 'vue-router'],
				resolvers: [ElementPlusResolver()],
				dts: 'src/auto-imports.d.ts',
			}),

			// 自动注册组件（UI库组件）
			Components({
				resolvers: [
					ElementPlusResolver({
						importStyle: 'sass', // 推荐使用 'sass'，以利用 vite 的 sass 编译缓存
					}),
				],
				dts: 'src/components.d.ts',
			}),

			VueSetupExtend(),
			viteCompression({
				verbose: true,
				disable: false,
				threshold: 10240,
				algorithm: 'brotliCompress',
				ext: '.br',
				// algorithm: 'gzip',
				// ext: '.gz',
			}),
			pages({
				dirs: 'src/views',
				exclude: ['**/components/*.vue', '**/component/*.vue', '**/*.ts'],
				onRoutesGenerated: (routes) => {
					routes.forEach((r) => {
						if (!r || !r.component) {
							console.warn('⚠️ 无效路由：', r)
						}
					})
					return routes
				},
				extendRoute(route) {
					// 如果当前路由没有定义 meta，添加一个默认的 meta
					return {
						...route,
						meta: {
							title:
								route.meta?.modifiedTitle ||
								route.meta?.title ||
								route.meta?.childTitle ||
								'-未命名',
							// ...route.meta, // 保留原有的 meta
							ignoreLabel: route.meta?.ignoreLabel,
						},
					}
				},
			}),
			visualizer({
				filename: './dist/stats.html',
				open: false,
				gzipSize: true,
				brotliSize: true,
			}),
		],
		build: {
			sourcemap: false,
			minify: 'esbuild', //terser
			outDir: 'dist',
			assetsDir: 'assets',
			cssCodeSplit: true,
			reportCompressedSize: false,
			chunkSizeWarningLimit: 1500,
			rollupOptions: {
				// treeshake: false,
				input: {
					main: path.resolve(__dirname, 'index.html'),
				},
				output: {
					chunkFileNames: `assets/[name].[hash]${timestamp}.js`,
					entryFileNames: `assets/[name].[hash]${timestamp}.js`,
					assetFileNames: `assets/[name].[hash]${timestamp}.[extname]`,
					manualChunks(id) {
						if (id.includes('node_modules')) {
							// return id.toString().split('node_modules/')[1].split('/')[0].toString()
							// if (id.includes('element-plus')) return 'vendor-element'
							// if (id.includes('vue')) return 'vendor-vue'
							// return 'vendor-others'

							if (id.includes('element-plus')) return 'vendor-element'
							if (id.includes('vue')) return 'vendor-vue'
							if (id.includes('node_modules')) {
								const dirs = id.toString().split('node_modules/')[1].split('/')
								const pkgName = dirs[0].startsWith('@')
									? dirs.slice(0, 2).join('/')
									: dirs[0]
								return `vendor-${pkgName}`
							}
						}
					},
				},
				maxParallelFileOps: 2,
				logLevel: 'silent',
			},
			assetsInlineLimit: 1024,

			terserOptions: {
				compress: {
					//生产环境时移除console
					drop_console: true,
					drop_debugger: true,
				},
			},
		},
		// 配置路径别名
		resolve: {
			alias: {
				'@': path.resolve(__dirname, './src'),
				'#': path.resolve(__dirname, './src/api'),
				$: path.resolve(__dirname, './src/stores'),
			},
		},
		css: {
			preprocessorOptions: {
				scss: {
					additionalData: `@use 'akvts/src/styles/lib/_mixin.scss' as *;`,
					api: 'modern-compiler', // or 'modern'
				},
			},
		},
		server: {
			host: '0.0.0.0',
			port: Number(env.VITE_PORT),
		},
		logLevel: isBuild ? 'error' : 'info',
	})
}
