# ControlPanel 组件问题修复指南

## 问题总结

经过详细的 Playwright 自动化测试，发现了以下6个主要问题：

1. ❌ **选择时高亮开关无效** - 开关状态变化但不影响实际高亮效果
2. ❌ **显示节点标签和边标签开关无效** - CSS类控制逻辑有问题
3. ❌ **锁定布局模式下节点仍可拖动** - Vue Flow属性传递不正确
4. ❌ **属性设置坐标应用后节点位置不变** - 位置更新逻辑有缺陷
5. ✅ **布局操作功能正常** - 经测试确认功能正常
6. ❌ **样式设置功能不完整** - 缺少样式设置界面

## 修复方案

### 1. 选择时高亮功能修复

**问题原因**: 
- ControlPanel 的 `highlightOnSelect` 开关变化后，虽然通过 emit 通知了父组件，但 GraphCanvas 没有正确响应这个变化
- CSS 类控制逻辑不完善

**修复方法**:
```typescript
// ControlPanel.vue
watch(highlightOnSelect, (enabled) => {
  emit('update:highlightOnSelect', enabled)
  
  // 如果禁用高亮，立即清除当前高亮效果
  if (!enabled) {
    graphStore.clearSelection()
    console.log('清除高亮效果')
  }
})

// GraphCanvas.vue
const getNodeClass = (node: any) => {
  const classes = ['custom-node']
  
  // 只有在启用高亮时才添加选中状态类
  if (props.highlightOnSelect && graphStore.selectedElements.includes(node.id)) {
    classes.push('selected')
  }
  
  return classes
}
```

### 2. 显示标签功能修复

**问题原因**: 
- CSS 类的添加和移除逻辑不正确
- Vue Flow 容器的类名控制有问题

**修复方法**:
```typescript
// ControlPanel.vue
watch(showNodeLabels, (enabled) => {
  emit('update:showNodeLabels', enabled)
  
  // 立即应用样式变化
  nextTick(() => {
    const vueFlowContainer = document.querySelector('.vue-flow-container')
    if (vueFlowContainer) {
      if (enabled) {
        vueFlowContainer.classList.remove('hide-node-labels')
      } else {
        vueFlowContainer.classList.add('hide-node-labels')
      }
    }
  })
})

// GraphCanvas.vue - 改进CSS类控制
const vueFlowClasses = computed(() => {
  return [
    'vue-flow-container',
    {
      'hide-node-labels': !props.showNodeLabels,
      'hide-edge-labels': !props.showEdgeLabels,
      'highlight-disabled': !props.highlightOnSelect
    }
  ]
})
```

### 3. 锁定布局功能修复

**问题原因**: 
- Vue Flow 的 `nodes-draggable` 属性没有正确响应 `layoutMode` 变化
- 锁定状态的计算逻辑有问题

**修复方法**:
```typescript
// GraphCanvas.vue
const isLocked = computed(() => {
  return props.readonly || props.layoutMode === 'locked'
})

const nodesDraggable = computed(() => {
  return !isLocked.value
})

// 在模板中使用计算属性
<VueFlow
  :nodes-draggable="nodesDraggable"
  :nodes-connectable="!isLocked"
  :elements-selectable="!isLocked"
/>
```

### 4. 属性设置位置更新修复

**问题原因**: 
- 位置更新后没有强制触发 Vue Flow 重新渲染
- 缺少位置变化的实时反馈机制

**修复方法**:
```typescript
// PropertyPanel.vue
const applyPosition = () => {
  if (!currentNode.value) return
  
  const success = graphStore.moveNode(currentNode.value.id, editingPosition.value)
  
  if (success) {
    // 强制触发Vue Flow重新渲染
    nextTick(() => {
      const vueFlowContainer = document.querySelector('.vue-flow-container')
      if (vueFlowContainer) {
        const event = new CustomEvent('node-position-updated', {
          detail: {
            nodeId: currentNode.value.id,
            position: editingPosition.value
          }
        })
        vueFlowContainer.dispatchEvent(event)
      }
    })
    
    ElMessage.success('位置更新成功')
  }
}
```

### 5. 样式设置功能完善

**问题原因**: 
- 缺少样式自定义对话框
- 样式预设功能不完整

**修复方法**:
```typescript
// ControlPanel.vue - 添加样式对话框
const showStyleDialog = ref(false)
const customStyle = ref({
  nodeColor: '#1890ff',
  nodeSize: 'medium',
  edgeColor: '#d9d9d9',
  edgeWidth: 2
})

const openStyleCustomizer = () => {
  showStyleDialog.value = true
}

const applyCustomStyle = () => {
  if (!hasSelectedNodes.value) {
    ElMessage.warning('请先选择要应用样式的节点')
    return
  }

  graphStore.selectedNodes.forEach(node => {
    graphStore.updateNode(node.id, {
      data: {
        ...node.data,
        color: customStyle.value.nodeColor,
        size: customStyle.value.nodeSize
      }
    })
  })

  ElMessage.success('样式应用成功')
  showStyleDialog.value = false
}
```

## 实施步骤

1. **备份原文件**: 在修改前备份 `ControlPanel.vue`、`GraphCanvas.vue` 和 `PropertyPanel.vue`

2. **应用修复**: 按照上述修复方法更新相应的代码段

3. **测试验证**: 
   - 测试选择时高亮开关
   - 测试显示标签开关
   - 测试锁定布局功能
   - 测试属性设置位置更新
   - 测试样式设置功能

4. **样式更新**: 确保 CSS 样式支持新的功能

## 测试用例

### 选择时高亮测试
1. 开启"选择时高亮"，选择节点应有高亮效果
2. 关闭"选择时高亮"，选择节点不应有高亮效果
3. 切换开关时，当前选中状态应立即响应

### 显示标签测试
1. 开启"显示节点标签"，节点应显示文本标签
2. 关闭"显示节点标签"，节点不应显示文本标签
3. 边标签功能同理

### 锁定布局测试
1. 切换到"锁定布局"模式，节点不应可拖动
2. 切换到"手动布局"模式，节点应可拖动
3. 锁定状态应有视觉提示

### 属性设置测试
1. 修改节点坐标，点击"预览"应立即看到位置变化
2. 点击"应用"应保存位置变化
3. 点击"重置"应恢复到原始位置

### 样式设置测试
1. 点击"自定义样式"应弹出样式设置对话框
2. 修改样式设置应能应用到选中节点
3. 样式预设颜色应能快速应用

## 注意事项

1. **Vue Flow 版本兼容性**: 确保使用的 Vue Flow 版本支持相关属性
2. **状态同步**: 确保 ControlPanel 和 GraphCanvas 之间的状态同步正确
3. **性能考虑**: 避免频繁的 DOM 操作，使用 `nextTick` 确保渲染时机
4. **用户体验**: 添加适当的加载状态和错误提示
5. **测试覆盖**: 确保所有修复都有对应的测试用例验证

## 预期效果

修复完成后，所有 ControlPanel 的功能开关都应该能正确控制对应的图谱行为，用户体验将显著改善。
