/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AI: typeof import('./src/components/AI.vue')['default']
    AICharts: typeof import('./src/components/AICharts.vue')['default']
    AuthorizedComp: typeof import('./src/components/common/authorized-comp.vue')['default']
    Bar: typeof import('./src/components/charts/Bar.vue')['default']
    BasetableComp: typeof import('./src/components/common/basetable-comp.vue')['default']
    BusinessProcess: typeof import('./src/components/BusinessProcess.vue')['default']
    DepartmentFavoriteComp: typeof import('./src/components/common/department-favorite-comp.vue')['default']
    DepartmentSelection: typeof import('./src/components/DepartmentSelection.vue')['default']
    DialogComp: typeof import('./src/components/common/dialog-comp.vue')['default']
    DrawerComp: typeof import('./src/components/common/drawer-comp.vue')['default']
    DrawerPlusComp: typeof import('./src/components/common/drawer-plus-comp.vue')['default']
    DropdownTree: typeof import('./src/components/common/dropdownTree.vue')['default']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElFooter: typeof import('element-plus/es')['ElFooter']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElOptionGroup: typeof import('element-plus/es')['ElOptionGroup']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElResult: typeof import('element-plus/es')['ElResult']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElStep: typeof import('element-plus/es')['ElStep']
    ElSteps: typeof import('element-plus/es')['ElSteps']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElTreeV2: typeof import('element-plus/es')['ElTreeV2']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ExcelComp: typeof import('./src/components/common/excel-comp.vue')['default']
    ExportComp: typeof import('./src/components/common/export-comp.vue')['default']
    ExportDataDeclaration: typeof import('./src/components/common/ExportDataDeclaration.vue')['default']
    FeedbackComp: typeof import('./src/components/common/feedback-comp.vue')['default']
    FeedbackModal: typeof import('./src/components/common/feedback-modal.vue')['default']
    FloatingBall: typeof import('./src/components/common/floatingBall.vue')['default']
    FormComp: typeof import('./src/components/common/form-comp.vue')['default']
    InfoOverviewComp: typeof import('./src/components/common/info-overview-comp.vue')['default']
    LabelsComp: typeof import('./src/components/common/labels-comp.vue')['default']
    LedgerFillConfig: typeof import('./src/components/ledger-fill-config.vue')['default']
    Line: typeof import('./src/components/charts/Line.vue')['default']
    LinkedDataComp: typeof import('./src/components/common/linked-data-comp.vue')['default']
    LoadingComp: typeof import('./src/components/common/loading-comp.vue')['default']
    MarkdownPreview: typeof import('./src/components/common/markdown-preview.vue')['default']
    MenuItemComp: typeof import('./src/components/common/menu-item-comp.vue')['default']
    NavigationComp: typeof import('./src/components/common/navigation-comp.vue')['default']
    NavigationCompPlus: typeof import('./src/components/common/navigation-comp-plus.vue')['default']
    NoticeList: typeof import('./src/components/notice-list.vue')['default']
    Organize: typeof import('./src/components/organize.vue')['default']
    ParticleBackground: typeof import('./src/components/common/ParticleBackground.vue')['default']
    Pie: typeof import('./src/components/charts/Pie.vue')['default']
    PopupComp: typeof import('./src/components/common/popup-comp.vue')['default']
    ProgressBar: typeof import('./src/components/common/progress-bar.vue')['default']
    ReportFilter: typeof import('./src/components/report-filter.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScheduleProgressComp: typeof import('./src/components/common/schedule-progress-comp.vue')['default']
    SelectRegionOrDepartment: typeof import('./src/components/common/SelectRegionOrDepartment.vue')['default']
    TablecolumnComp: typeof import('./src/components/common/tablecolumn-comp.vue')['default']
    TimePicker: typeof import('./src/components/common/TimePicker.vue')['default']
    TopComp: typeof import('./src/components/common/top-comp.vue')['default']
    Tree_comp: typeof import('./src/components/common/tree_comp.vue')['default']
    UploadfileComp: typeof import('./src/components/common/uploadfile-comp.vue')['default']
    ValidateCode: typeof import('./src/components/ValidateCode.vue')['default']
    ViewFlow: typeof import('./src/components/common/ViewFlow.vue')['default']
    XlsxPlusComp: typeof import('./src/components/common/xlsx-plus-comp.vue')['default']
  }
  export interface GlobalDirectives {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
