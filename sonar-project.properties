# must be unique in a given SonarQube instance
sonar.projectKey=ReportSystem
sonar.token=sqp_e99dc8bab1ca0d894b37332861554038ac1b3b9d
sonar.sources=.
sonar.host.url=https://sq.akvts.net:88
sonar.exclusions=**/node_modules/**,**/public/**,**/dist/**,**/coverage/**,**/src/assets/**,**/src/environments/**,**/src/test.ts,**/src/polyfills.ts,**/src/main.ts,**/src/styles.scss,**/src/test.ts,**/src/tsconfig.app.json,**/src/tsconfig.spec.json,**/src/tslint.json,**/src/karma.conf.js,**/src/tsconfig.json,**/src/tsconfig.server.json,**/src/tsconfig.e2e.json,**/src/tsconfig.json,**/src/tsconfig.app.json,**/src/tsconfig.spec.json,**/src/tslint.json,**/src/karma.conf.js,**/src/tsconfig.json,**/src/tsconfig.app.json,**/src/tsconfig.spec.json,**/src/tslint.json,**/src/karma.conf.js,**/src/tsconfig.json,**/src/tsconfig.app.json,**/src/tsconfig.spec.json,**/src/tslint.json,**/src/karma.conf.js,**/src/tsconfig.json,**/src/tsconfig.app.json,**/src/tsconfig.spec.json,**/src/tslint.json,**/src/karma.conf.js,**/src/tsconfig.json,**/src/tsconfig.app.json,**/src/tsconfig.spec.json,**/src/tslint.json,**/src/karma.conf.js,**/src/tsconfig.json,**/src/tsconfig.app.json,**/src/tsconfig.spec.json,**/src/tslint.json,**/src/karma.conf.js,**/src/tsconfig.json,**/src/tsconfig.app.json,**/src/tsconfig.spec.json,**/src/tslint.json,**/src/karma.conf.js,**/src/tsconfig.json,**/src/tsconfig.app.json,**/src/tsconfig.spec.json,**/src/tslint.json,**/src/karma.conf.js,**/src/tsconfig.json,**/src/tsconfig.app.json,**/src/tsconfig.spec.json,**/src/tslint.json,**/src/karma.conf.js,**/src/tsconfig.json,**/src/tsconfig.app.json,**/src/tsconfig.spec.json,**/src/tslint.json,**/src/karma.conf.js,**/src/tsconfig.json,**/src/tsconfig.app.json,**/src/tsconfig.spec.json,**/src/tslint.json,**/src/karma.conf.js,**/src/tsconfig.json,**/src/tsconfig.app.json,**/src/tsconfig.spec.json,**/src/tslint.json,**/src/karma.conf.js,**/,**/src/views/template/**

# --- optional properties ---

# defaults to project key
#sonar.projectName=My project
# defaults to 'not provided'
#sonar.projectVersion=1.0
 
# Path is relative to the sonar-project.properties file. Defaults to .
#sonar.sources=.
 
# Encoding of the source code. Default is default system encoding
#sonar.sourceEncoding=UTF-8