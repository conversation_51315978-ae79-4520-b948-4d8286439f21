<!-- 修复后的 PropertyPanel.vue 关键部分 -->
<script setup lang="ts">
// ... 其他导入保持不变

// ==================== 修复: 位置更新逻辑 ====================

/**
 * 预览位置变化 - 修复版本
 */
const previewPosition = () => {
  if (!currentNode.value) return
  
  console.log('预览位置变化:', editingPosition.value)
  
  // 立即更新节点位置进行预览
  graphStore.moveNode(currentNode.value.id, editingPosition.value)
  isPreviewing.value = true
  
  ElMessage.info('位置预览已应用，点击"应用"保存或"重置"恢复')
}

/**
 * 应用位置变化 - 修复版本
 */
const applyPosition = () => {
  if (!currentNode.value) return
  
  try {
    console.log('应用位置变化:', editingPosition.value)
    
    // 确保位置更新被正确应用
    const success = graphStore.moveNode(currentNode.value.id, editingPosition.value)
    
    if (success) {
      // 更新原始位置为当前编辑位置
      originalPosition.value = { ...editingPosition.value }
      isPreviewing.value = false
      
      // 强制触发Vue Flow重新渲染
      nextTick(() => {
        // 通知Vue Flow更新节点位置
        const vueFlowContainer = document.querySelector('.vue-flow-container')
        if (vueFlowContainer) {
          const event = new CustomEvent('node-position-updated', {
            detail: {
              nodeId: currentNode.value.id,
              position: editingPosition.value
            }
          })
          vueFlowContainer.dispatchEvent(event)
        }
      })
      
      ElMessage.success('位置更新成功')
    } else {
      ElMessage.error('位置更新失败')
    }
  } catch (error) {
    console.error('应用位置变化失败:', error)
    ElMessage.error('位置更新失败')
  }
}

/**
 * 重置位置 - 修复版本
 */
const resetPosition = () => {
  if (!currentNode.value) return
  
  console.log('重置位置到原始值:', originalPosition.value)
  
  editingPosition.value = { ...originalPosition.value }
  
  if (isPreviewing.value) {
    // 恢复到原始位置
    graphStore.moveNode(currentNode.value.id, originalPosition.value)
    isPreviewing.value = false
    
    // 强制触发Vue Flow重新渲染
    nextTick(() => {
      const vueFlowContainer = document.querySelector('.vue-flow-container')
      if (vueFlowContainer) {
        const event = new CustomEvent('node-position-updated', {
          detail: {
            nodeId: currentNode.value.id,
            position: originalPosition.value
          }
        })
        vueFlowContainer.dispatchEvent(event)
      }
    })
  }
  
  ElMessage.info('位置已重置')
}

/**
 * 监听坐标输入变化 - 新增
 */
const handlePositionChange = (axis: 'x' | 'y', value: number) => {
  editingPosition.value[axis] = value
  
  // 如果正在预览，实时更新位置
  if (isPreviewing.value && currentNode.value) {
    graphStore.moveNode(currentNode.value.id, editingPosition.value)
  }
}

// ==================== 监听器修复 ====================

/** 监听选中节点变化 - 修复版本 */
watch(currentNode, (newNode) => {
  if (newNode) {
    // 确保获取最新的节点位置
    const latestNode = graphStore.nodes.find(n => n.id === newNode.id)
    if (latestNode) {
      editingPosition.value = { ...latestNode.position }
      originalPosition.value = { ...latestNode.position }
      isPreviewing.value = false

      console.log('节点选中，初始位置:', latestNode.position)

      // 同步节点属性到编辑状态
      editingProperties.value = {
        label: latestNode.data?.label || '',
        description: latestNode.data?.description || '',
        priority: latestNode.data?.priority || 'medium',
        assignee: latestNode.data?.assignee || '',
        dueDate: latestNode.data?.dueDate ? (typeof latestNode.data.dueDate === 'string' ? latestNode.data.dueDate : latestNode.data.dueDate.toISOString().split('T')[0]) : '',
        status: latestNode.data?.status || 'not-started',
        progress: latestNode.data?.progress || 0,
        tags: latestNode.data?.tags || []
      }
    }
  }
}, { immediate: true })

</script>

<template>
  <div v-if="visible" class="property-panel">
    <div class="panel-header">
      <h4 class="panel-title">属性设置</h4>
      <el-button
        type="text"
        size="small"
        @click="closePanel"
        class="close-btn"
      >
        ×
      </el-button>
    </div>

    <div class="panel-content">
      <!-- 基本信息 -->
      <div class="info-section">
        <h5 class="section-title">基本信息</h5>
        <div class="info-item">
          <span class="info-label">ID:</span>
          <span class="info-value">{{ currentNode?.id }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">标签:</span>
          <span class="info-value">{{ currentNode?.data?.label }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">类型:</span>
          <span class="info-value">{{ currentNode?.type }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">状态:</span>
          <span class="info-value">{{ currentNode?.data?.status }}</span>
        </div>
      </div>

      <!-- 位置设置 - 修复版本 -->
      <div class="position-section">
        <h5 class="section-title">位置设置</h5>
        <div class="position-controls">
          <div class="coordinate-input">
            <span class="coordinate-label">X坐标:</span>
            <el-input-number
              v-model="editingPosition.x"
              :step="10"
              :precision="0"
              size="small"
              @change="(value) => handlePositionChange('x', value)"
            />
          </div>
          <div class="coordinate-input">
            <span class="coordinate-label">Y坐标:</span>
            <el-input-number
              v-model="editingPosition.y"
              :step="10"
              :precision="0"
              size="small"
              @change="(value) => handlePositionChange('y', value)"
            />
          </div>
        </div>
        
        <div class="position-actions">
          <el-button
            size="small"
            @click="previewPosition"
            :disabled="!currentNode"
          >
            预览
          </el-button>
          <el-button
            type="primary"
            size="small"
            @click="applyPosition"
            :disabled="!currentNode"
          >
            应用
          </el-button>
          <el-button
            size="small"
            @click="resetPosition"
            :disabled="!currentNode"
          >
            重置
          </el-button>
        </div>
        
        <!-- 位置状态提示 -->
        <div v-if="isPreviewing" class="position-hint">
          <el-icon><Warning /></el-icon>
          <span>正在预览位置变化</span>
        </div>
      </div>

      <!-- 属性编辑 -->
      <div class="properties-section">
        <h5 class="section-title">属性编辑</h5>
        <el-form :model="editingProperties" label-width="80px" size="small">
          <el-form-item label="标题">
            <el-input v-model="editingProperties.label" />
          </el-form-item>
          
          <el-form-item label="描述">
            <el-input
              v-model="editingProperties.description"
              type="textarea"
              :rows="3"
            />
          </el-form-item>
          
          <el-form-item label="优先级">
            <el-select v-model="editingProperties.priority">
              <el-option label="低" value="low" />
              <el-option label="中" value="medium" />
              <el-option label="高" value="high" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="负责人">
            <el-input v-model="editingProperties.assignee" />
          </el-form-item>
          
          <el-form-item label="截止日期">
            <el-date-picker
              v-model="editingProperties.dueDate"
              type="date"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          
          <el-form-item label="状态">
            <el-select v-model="editingProperties.status">
              <el-option label="未开始" value="not-started" />
              <el-option label="进行中" value="in-progress" />
              <el-option label="已完成" value="completed" />
              <el-option label="已暂停" value="paused" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="进度">
            <el-slider
              v-model="editingProperties.progress"
              :min="0"
              :max="100"
              :step="5"
              show-input
            />
          </el-form-item>
        </el-form>
        
        <div class="properties-actions">
          <el-button
            type="primary"
            size="small"
            @click="saveProperties"
            :disabled="!currentNode"
          >
            保存属性
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.property-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  max-height: 80vh;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #f8f9fa;
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  font-size: 18px;
  font-weight: bold;
  color: #999;
  
  &:hover {
    color: #666;
  }
}

.panel-content {
  padding: 20px;
  max-height: calc(80vh - 60px);
  overflow-y: auto;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.info-section,
.position-section,
.properties-section {
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  
  .info-label {
    font-weight: 500;
    color: #666;
  }
  
  .info-value {
    color: #333;
  }
}

.position-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.coordinate-input {
  display: flex;
  align-items: center;
  gap: 12px;
  
  .coordinate-label {
    width: 60px;
    font-weight: 500;
    color: #666;
  }
}

.position-actions,
.properties-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.position-hint {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
  padding: 8px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
  font-size: 12px;
  color: #d46b08;
}
</style>
