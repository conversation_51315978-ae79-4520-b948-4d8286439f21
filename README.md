# Inspur Web 项目

## 项目简介

这是一个基于 Vue3 的政务管理系统前端项目，主要用于企业内部的账本管理、任务管理、数据维护等功能。项目采用现代化的前端技术栈，具有良好的性能和用户体验。系统支持实时数据更新、多级权限管理、数据可视化等企业级特性。

### 项目特点

-   基于 Vue3 + TypeScript 开发，确保代码质量和可维护性
-   采用 Vite 构建，支持快速的开发体验和高效的生产构建
-   使用 Element Plus 组件库，提供统一的用户界面体验
-   支持实时数据更新和推送
-   多级权限管理系统
-   支持大数据量的表格处理和展示
-   模块化的项目结构，便于扩展和维护

## 技术栈

### 核心框架

-   Vue 3.5.13：采用组合式 API，提供更好的代码组织和复用
-   Vite 5.4.11：下一代前端构建工具，提供极速的开发体验
-   TypeScript：增加代码的可维护性和可靠性

### 状态管理

-   Pinia 2.2.6：新一代 Vue 状态管理工具，提供更简单的 API 和更好的 TypeScript 支持

### 路由管理

-   Vue Router 4.4.5：Vue 官方路由管理器，支持配置式路由和动态路由

### 网络请求

-   HTTP 客户端：Axios 1.7.7，提供拦截器和请求/响应数据转换
-   实时通信：SignalR 8.0.7，用于实现服务器推送和实时数据更新

### 工具库

-   ExcelJS：处理 Excel 文件的导入导出
-   Crypto-js：数据加密和解密
-   File-saver：文件下载处理
-   Markdown-it：文档渲染
-   JS-Cookie：Cookie 管理
-   Highlight.js：代码高亮显示

## 功能简介

### 1. 账本管理模块

-   账本填报：支持多种格式的数据录入和验证
-   账本审核：多级审核流程，支持批量审核
-   账本权限管理：细粒度的权限控制
-   数据导入导出：支持 Excel 格式的数据导入导出
-   历史记录查询：完整的操作日志记录

### 2. 任务管理模块

-   任务链接：可视化的任务流程管理
-   任务待办：个人待办任务管理
-   任务审核：支持多级审核流程
-   任务追踪：实时任务状态更新

### 3. 数据维护模块

-   数据源管理：支持多数据源配置
-   数据统计分析：可视化的数据展示
-   数据校验：自动化的数据质量检查
-   数据同步：实时数据同步功能

### 4. 统计分析模块

-   报表统计：多维度的数据统计和分析
-   五星评级：数据质量评估系统
-   数据概览：直观的数据展示界面
-   自定义报表：支持自定义统计维度

## 项目结构

```
├── public/                 # 静态资源
│   ├── fonticon/          # 字体图标
│   └── luckysheet/        # 表格插件
├── src/
│   ├── api/               # API接口定义和配置
│   │   ├── CommonApi.ts   # 通用接口
│   │   ├── LedgerApi.ts   # 账本相关接口
│   │   └── WorkflowApi.ts # 工作流接口
│   ├── assets/            # 项目资源文件
│   ├── components/        # 公共组件
│   │   ├── common/        # 基础公共组件
│   │   └── BusinessProcess.vue # 业务流程组件
│   ├── define/            # 类型定义和常量
│   ├── directive/         # 自定义指令
│   ├── hooks/             # 组合式函数
│   │   ├── usePermissions.js # 权限控制hook
│   │   └── useSignalr.js    # 实时通信hook
│   ├── layout/           # 布局组件
│   ├── plugin/           # 插件配置
│   ├── router/           # 路由配置
│   ├── stores/           # 状态管理
│   ├── styles/           # 样式文件
│   ├── views/            # 页面视图
│   └── worker/           # Web Worker
```

## 主要插件

### 1. LuckySheet

-   功能：在线电子表格插件
-   用途：提供类似 Excel 的数据录入和展示界面
-   特点：支持公式计算、数据验证、单元格格式化等

### 2. SignalR

-   功能：实时通信框架
-   用途：实现服务器推送和实时数据更新
-   特点：支持多种传输协议，自动重连机制

### 3. ExcelJS

-   功能：Excel 文件处理库
-   用途：实现 Excel 文件的读写和转换
-   特点：支持复杂的 Excel 格式和公式

### 4. Element Plus

-   功能：Vue3 组件库
-   用途：提供统一的 UI 组件
-   特点：支持按需引入，主题定制

### 5. 其他工具插件

-   VConsole：移动端调试工具
-   Highlight.js：代码高亮显示
-   Crypto-js：数据加密解密
-   File-saver：文件下载处理

## 开发命令

```bash
# 安装依赖
npm install

# 开发环境运行
npm run dev

# 生产环境打包
npm run build

# 预览生产环境
npm run preview
```

## 环境要求

-   Node.js >= 16.0.0
-   npm >= 7.0.0
-   现代浏览器（支持 ES6+）
