// vite.config.ts
import { defineConfig, loadEnv } from "file:///D:/%E5%B7%A5%E4%BD%9C%E6%96%87%E4%BB%B6/code/vue13.2.3-ysa/Inspur-BasicGov-Web/node_modules/vite/dist/node/index.js";
import vue from "file:///D:/%E5%B7%A5%E4%BD%9C%E6%96%87%E4%BB%B6/code/vue13.2.3-ysa/Inspur-BasicGov-Web/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import path from "path";
import viteCompression from "file:///D:/%E5%B7%A5%E4%BD%9C%E6%96%87%E4%BB%B6/code/vue13.2.3-ysa/Inspur-BasicGov-Web/node_modules/vite-plugin-compression/dist/index.mjs";
import pages from "file:///D:/%E5%B7%A5%E4%BD%9C%E6%96%87%E4%BB%B6/code/vue13.2.3-ysa/Inspur-BasicGov-Web/node_modules/vite-plugin-pages/dist/index.js";
import VueSetupExtend from "file:///D:/%E5%B7%A5%E4%BD%9C%E6%96%87%E4%BB%B6/code/vue13.2.3-ysa/Inspur-BasicGov-Web/node_modules/vite-plugin-vue-setup-extend/dist/index.mjs";
import { visualizer } from "file:///D:/%E5%B7%A5%E4%BD%9C%E6%96%87%E4%BB%B6/code/vue13.2.3-ysa/Inspur-BasicGov-Web/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
import AutoImport from "file:///D:/%E5%B7%A5%E4%BD%9C%E6%96%87%E4%BB%B6/code/vue13.2.3-ysa/Inspur-BasicGov-Web/node_modules/unplugin-auto-import/dist/vite.js";
import Components from "file:///D:/%E5%B7%A5%E4%BD%9C%E6%96%87%E4%BB%B6/code/vue13.2.3-ysa/Inspur-BasicGov-Web/node_modules/unplugin-vue-components/dist/vite.js";
import { ElementPlusResolver } from "file:///D:/%E5%B7%A5%E4%BD%9C%E6%96%87%E4%BB%B6/code/vue13.2.3-ysa/Inspur-BasicGov-Web/node_modules/unplugin-vue-components/dist/resolvers.js";
var __vite_injected_original_dirname = "D:\\\u5DE5\u4F5C\u6587\u4EF6\\code\\vue13.2.3-ysa\\Inspur-BasicGov-Web";
var timestamp = (/* @__PURE__ */ new Date()).getTime();
var vite_config_default = ({ mode, command }) => {
  const isBuild = command === "build";
  const env = loadEnv(mode, process.cwd());
  return defineConfig({
    // base: process.env.NODE_ENV == 'production' ? '/' : '/api',
    define: {
      "process.env": env,
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: false,
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: true
    },
    plugins: [
      vue(),
      // 自动导入 Vue API，比如 ref、computed、defineProps 等
      AutoImport({
        imports: ["vue", "vue-router"],
        resolvers: [ElementPlusResolver()],
        dts: "src/auto-imports.d.ts"
      }),
      // 自动注册组件（UI库组件）
      Components({
        resolvers: [
          ElementPlusResolver({
            importStyle: "sass"
            // 推荐使用 'sass'，以利用 vite 的 sass 编译缓存
          })
        ],
        dts: "src/components.d.ts"
      }),
      VueSetupExtend(),
      viteCompression({
        verbose: true,
        disable: false,
        threshold: 10240,
        algorithm: "brotliCompress",
        ext: ".br"
        // algorithm: 'gzip',
        // ext: '.gz',
      }),
      pages({
        dirs: "src/views",
        exclude: ["**/components/*.vue", "**/component/*.vue", "**/*.ts"],
        onRoutesGenerated: (routes) => {
          routes.forEach((r) => {
            if (!r || !r.component) {
              console.warn("\u26A0\uFE0F \u65E0\u6548\u8DEF\u7531\uFF1A", r);
            }
          });
          return routes;
        },
        extendRoute(route) {
          return {
            ...route,
            meta: {
              title: route.meta?.modifiedTitle || route.meta?.title || route.meta?.childTitle || "-\u672A\u547D\u540D",
              // ...route.meta, // 保留原有的 meta
              ignoreLabel: route.meta?.ignoreLabel
            }
          };
        }
      }),
      visualizer({
        filename: "./dist/stats.html",
        open: false,
        gzipSize: true,
        brotliSize: true
      })
    ],
    build: {
      sourcemap: false,
      minify: "esbuild",
      //terser
      outDir: "dist",
      assetsDir: "assets",
      cssCodeSplit: true,
      reportCompressedSize: false,
      chunkSizeWarningLimit: 1500,
      rollupOptions: {
        // treeshake: false,
        input: {
          main: path.resolve(__vite_injected_original_dirname, "index.html")
        },
        output: {
          chunkFileNames: `assets/[name].[hash]${timestamp}.js`,
          entryFileNames: `assets/[name].[hash]${timestamp}.js`,
          assetFileNames: `assets/[name].[hash]${timestamp}.[extname]`,
          manualChunks(id) {
            if (id.includes("node_modules")) {
              if (id.includes("element-plus")) return "vendor-element";
              if (id.includes("vue")) return "vendor-vue";
              if (id.includes("node_modules")) {
                const dirs = id.toString().split("node_modules/")[1].split("/");
                const pkgName = dirs[0].startsWith("@") ? dirs.slice(0, 2).join("/") : dirs[0];
                return `vendor-${pkgName}`;
              }
            }
          }
        },
        maxParallelFileOps: 2,
        logLevel: "silent"
      },
      assetsInlineLimit: 1024,
      terserOptions: {
        compress: {
          //生产环境时移除console
          drop_console: true,
          drop_debugger: true
        }
      }
    },
    // 配置路径别名
    resolve: {
      alias: {
        "@": path.resolve(__vite_injected_original_dirname, "./src"),
        "#": path.resolve(__vite_injected_original_dirname, "./src/api"),
        $: path.resolve(__vite_injected_original_dirname, "./src/stores")
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use 'akvts/src/styles/lib/_mixin.scss' as *;`,
          api: "modern-compiler"
          // or 'modern'
        }
      }
    },
    server: {
      host: "0.0.0.0",
      port: Number(env.VITE_PORT)
    },
    logLevel: isBuild ? "error" : "info"
  });
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
