<!-- 修复后的 ControlPanel.vue 关键部分 -->
<script setup lang="ts">
// ... 其他导入保持不变

// ==================== 修复1: 高亮控制逻辑 ====================

/**
 * 监听高亮设置变化 - 修复版本
 */
watch(highlightOnSelect, (enabled) => {
  // 通知父组件更新高亮设置
  emit('update:highlightOnSelect', enabled)

  // 如果禁用高亮，立即清除当前高亮效果
  if (!enabled) {
    // 清除所有选中状态的视觉高亮
    graphStore.clearSelection()
    console.log('清除高亮效果')
  }
})

// ==================== 修复2: 节点标签显示控制 ====================

/**
 * 监听节点标签显示设置变化 - 修复版本
 */
watch(showNodeLabels, (enabled) => {
  emit('update:showNodeLabels', enabled)
  console.log('节点标签显示:', enabled)
  
  // 立即应用样式变化
  nextTick(() => {
    const vueFlowContainer = document.querySelector('.vue-flow-container')
    if (vueFlowContainer) {
      if (enabled) {
        vueFlowContainer.classList.remove('hide-node-labels')
      } else {
        vueFlowContainer.classList.add('hide-node-labels')
      }
    }
  })
})

/**
 * 监听边标签显示设置变化 - 修复版本
 */
watch(showEdgeLabels, (enabled) => {
  emit('update:showEdgeLabels', enabled)
  console.log('边标签显示:', enabled)
  
  // 立即应用样式变化
  nextTick(() => {
    const vueFlowContainer = document.querySelector('.vue-flow-container')
    if (vueFlowContainer) {
      if (enabled) {
        vueFlowContainer.classList.remove('hide-edge-labels')
      } else {
        vueFlowContainer.classList.add('hide-edge-labels')
      }
    }
  })
})

// ==================== 修复3: 布局模式控制 ====================

/**
 * 处理布局模式变化 - 修复版本
 */
const handleLayoutModeChange = (mode: 'locked' | 'auto' | 'manual') => {
  switch (mode) {
    case 'locked':
      console.log('切换到锁定布局模式')
      // 清除选择状态并禁用交互
      graphStore.clearSelection()
      // 通过emit确保GraphCanvas接收到正确的layoutMode
      emit('update:layoutMode', mode)
      break

    case 'auto':
      console.log('切换到自动布局模式')
      emit('update:layoutMode', mode)
      if (graphStore.nodes.length > 0) {
        graphOperations.applyAutoLayout('dagre')
      }
      break

    case 'manual':
      console.log('切换到手动布局模式')
      emit('update:layoutMode', mode)
      break

    default:
      console.warn('未知的布局模式:', mode)
  }
}

// ==================== 修复4: 样式设置功能 ====================

/** 是否显示样式自定义对话框 */
const showStyleDialog = ref(false)

/** 当前自定义样式 */
const customStyle = ref({
  nodeColor: '#1890ff',
  nodeSize: 'medium',
  edgeColor: '#d9d9d9',
  edgeWidth: 2
})

/**
 * 打开样式自定义器 - 修复版本
 */
const openStyleCustomizer = () => {
  console.log('打开样式自定义器')
  showStyleDialog.value = true
}

/**
 * 应用自定义样式
 */
const applyCustomStyle = () => {
  if (!hasSelectedNodes.value) {
    ElMessage.warning('请先选择要应用样式的节点')
    return
  }

  // 为选中的节点应用自定义样式
  graphStore.selectedNodes.forEach(node => {
    graphStore.updateNode(node.id, {
      data: {
        ...node.data,
        color: customStyle.value.nodeColor,
        size: customStyle.value.nodeSize
      }
    })
  })

  ElMessage.success('样式应用成功')
  showStyleDialog.value = false
}

/**
 * 关闭样式对话框
 */
const closeStyleDialog = () => {
  showStyleDialog.value = false
}

</script>

<template>
  <!-- ... 其他模板内容保持不变 -->

  <!-- 修复后的样式设置区域 -->
  <div class="panel-section">
    <div class="section-title">
      <el-icon><Setting /></el-icon>
      <span>样式设置</span>
    </div>

    <div class="style-settings">
      <div class="style-presets">
        <h6 class="preset-title">样式预设</h6>
        <div class="preset-colors">
          <div
            v-for="color in stylePresets"
            :key="color.name"
            class="color-preset"
            :style="{ backgroundColor: color.value }"
            :title="color.name"
            @click="applyColorPreset(color.value)"
          />
        </div>
      </div>

      <div class="custom-style">
        <h6 class="custom-title">自定义样式</h6>
        <el-button
          size="small"
          @click="openStyleCustomizer"
          class="style-btn"
        >
          <el-icon><Edit /></el-icon>
          <span>自定义样式</span>
        </el-button>
      </div>
    </div>
  </div>

  <!-- 样式自定义对话框 -->
  <el-dialog
    v-model="showStyleDialog"
    title="自定义样式"
    width="400px"
    :before-close="closeStyleDialog"
  >
    <el-form :model="customStyle" label-width="80px">
      <el-form-item label="节点颜色">
        <el-color-picker v-model="customStyle.nodeColor" />
      </el-form-item>
      
      <el-form-item label="节点大小">
        <el-select v-model="customStyle.nodeSize">
          <el-option label="小" value="small" />
          <el-option label="中" value="medium" />
          <el-option label="大" value="large" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="连线颜色">
        <el-color-picker v-model="customStyle.edgeColor" />
      </el-form-item>
      
      <el-form-item label="连线宽度">
        <el-slider
          v-model="customStyle.edgeWidth"
          :min="1"
          :max="5"
          :step="1"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="closeStyleDialog">取消</el-button>
      <el-button type="primary" @click="applyCustomStyle">应用</el-button>
    </template>
  </el-dialog>
</template>
