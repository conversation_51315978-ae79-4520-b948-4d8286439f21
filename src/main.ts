import {createApp} from 'vue'
import {createPinia} from 'pinia'
import {router} from './router'
import {request} from './api'
import {components} from './components/index'
import App from './App.vue'

import dragMove from './directive/d.js'

import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import akvts from 'akvts'
import {directives as AkvtsDir} from 'akvts/src/directive'
import '@/plugin/rpn'

// 引入md预览器
import VueMarkdownEditor from '@kangc/v-md-editor'
import VMdPreview from '@kangc/v-md-editor/lib/preview'

import githubTheme from '@kangc/v-md-editor/lib/theme/github.js'
import hljs from 'highlight.js'

VMdPreview.use(githubTheme, {
	config: {
		toc: {
			includeLevel: [1, 2],
		},
	},
	Hljs: hljs,
})

VueMarkdownEditor.use(githubTheme, {
	Hljs: hljs,
})

const pinia = createPinia()
const app = createApp(App)

function initApp() {
	app.provide('#app', app)
	app.provide('#axios', request)

	app.use(ElementPlus, {locale: zhCn})

	for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
		app.component(key, component)
	}

	AkvtsDir(app) // 使用框架指令

	components(app)
	// directives(app)

	app.use(VMdPreview)
	app.use(VueMarkdownEditor)
	app.use(dragMove)
	app.use(pinia)
	app.use(router)
	app.use(akvts) // 使用框架

	try {
		// new VConsole()
	} catch (e) {}

	app.mount('#app')
	document.title = import.meta.env.VITE_TITLE
}

initApp()
