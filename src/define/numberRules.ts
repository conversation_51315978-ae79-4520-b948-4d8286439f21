// 允许输入的数字等于几
export const isEqual = (numStr:any, target:any) =>{ 
    console.log(numStr == target) 
    console.log(numStr ) 
    console.log( target) 

    return numStr == target;  
}  
// 允许输入的数字不等于几
export const isNotEqual = (numStr:any, target:any) =>{  
    console.log(numStr != target) 
    console.log(numStr ) 
    console.log( target) 
    return numStr != target;
}  
// 允许输入的数字大于几
export const isGreaterThan = (numStr:any, target:any) =>{  
    const num = parseFloat(numStr);  
    return !isNaN(num) && num > target; 
}  
// 允许输入的数字大于等于几
export const isGreaterThanOrEqual = (numStr:any, target:any) =>{  
    const num = parseFloat(numStr);  
    return !isNaN(num) && num >= target;   
}  
// 允许输入的数字小于几
export const isLessThan = (numStr:any, target:any) =>{  
    const num = parseFloat(numStr);  
    return !isNaN(num) && num < target; 
}  
// 允许输入的数字小于等于几
export const isLessThanOrEqual = (numStr:any, target:any) =>{  
    const num = parseFloat(numStr);  
    return !isNaN(num) && num <= target;
}  
// 允许输入的数字小于包含在几与几之间
export const isBetweenExclusive = (numStr:any, min:any, max:any) =>{  
    [min, max] = [min, max].sort((a, b) => a - b);  
  
    const num = parseFloat(numStr);  
    return !isNaN(num) && num >= min && num <= max;  
}  
// 允许输入的数字小于不包含在几与几之间
export const isNotBetween = (numStr:any, min:any, max:any) =>{  
    [min, max] = [min, max].sort((a, b) => a - b);  
  
    const num = parseFloat(numStr);  
    return isNaN(num) || num < min || num > max;  
}  