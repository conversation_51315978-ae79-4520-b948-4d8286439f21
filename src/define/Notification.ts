export enum Notification {
	ExportHistory = 'Abp.Ledger.Notifications.TableDataExportNotice', // 导出记录成功

	LedgerOnline = 'Abp.Ledger.Notifications.LedgerOnlineNotice', // 上线提醒
	LedgerOnlineToAuthority = 'LedgerCrossDepartmentEmpower', // 上线提醒-跳授权页
	LedgerOnlineToLedger = 'LedgerDepartmentEmpower', // 上线提醒-跳业务表卡片页

	LedgerUrgeTransaction = 'Abp.Ledger.Notifications.LedgerUrgeTransactionNotice', // 催办提醒
	LedgerDataUrgeTransaction = 'LedgerDataUrgeTransaction', // 催办提醒-跳业务表详情页

	LedgerAuditedSubmit = 'Abp.Ledger.Notifications.LedgerAuditedSubmitNotice', // 数据审核提醒
	LedgerAuditedCompleted = 'Abp.Ledger.Notifications.LedgerAuditedCompletedNotice', // 审核结果提醒

	LedgerInfoUpdated = 'Abp.Ledger.Notifications.LedgerInfoUpdatedNotice', // 业务表信息更新提醒

	LedgerOverdueReminder = 'Abp.Ledger.Notifications.LedgerOverdueReminder', // 业务表逾期提醒
	LedgerOverdueReminderLeader = 'Abp.Ledger.Notifications.LedgerOverdueLeaderReminder', // 业务表逾期提醒-领导

	UserOffline = 'Abp.Ledger.Notifications.UserOfflineNotice', // 账号禁用痛通知

	LedgerResponse = 'Abp.Ledger.Notifications.LedgeResponseNotice',

	PlanTaskAudit = 'Abp.Report.Notifications.PlanTaskAudit', // 报表审核提醒
	PlanTaskAuditResult = 'Abp.Report.Notifications.PlanTaskAuditResult', // 审核结果通知
	ReportFilling = 'Abp.Report.Notifications.ReportFilling', // 报表填报提醒
	ReportRevoke = 'Abp.Report.Notifications.ReportRevoke', // 报表撤回通知
	ReportStop = 'Abp.Report.Notifications.ReportStop', // 报表终止通知
	DataAudit = 'Abp.Report.Notifications.DataAudit', // 数据审核提醒
	ReportTask = 'ReportTask', // 报表任务
	PlanTask = 'PlanTask', // 计划任务 q
	planTaskAudit = 'PlanTaskAudit', // 报表任务 q
	ReportTaskAudit = 'ReportTaskAudit', // 计划任务 q
	ReportTaskDataAudit = 'ReportTaskDataAudit',
	TaskPending = 'taskPending',

	RunwayChange = 'Abp.Ledger.Notifications.LedgerDataUpdate',
	ledgerAuditedCompleted = 'LedgerAuditedCompleted',
	ledgerDataAuditNotice = 'LedgerDataAuditNotice',

	ReportUrge = 'Abp.Report.Notifications.PlanTaskUrgeTransactionNotice', // 报表催办
}

const GetNotification = (mainLabel: string, path: string, query: {}) => {
	return {
		mainLabel,
		path,
		query,
	}
}

export const NotificationConfig: any = {
	[Notification.LedgerOnline]: {
		[Notification.LedgerOnlineToAuthority]: (json: any) =>
			GetNotification('', '/ledgerAuthority/authority', {}),
		[Notification.LedgerOnlineToLedger]: (json: any) =>
			GetNotification('', '/ledger/overview', {}),
	},
	[Notification.LedgerUrgeTransaction]: {
		[Notification.LedgerDataUrgeTransaction]: (json: any) =>
			GetNotification('/ledger/overview', '/ledger/fill', {ledgerId: json.LedgerId}),
	},
	[Notification.LedgerAuditedSubmit]: (json: any) =>
		GetNotification('/taskPending', '/taskPending/task-review-details', {
			id: json.Id,
			taskId: json.TaskId,
			keyword: json.Keyword,
			businessId: json.BusinessId,
			Remark: json.Remark,
		}),
	[Notification.LedgerAuditedCompleted]: {
		[Notification.ledgerAuditedCompleted]: (json: any) => {
			return GetNotification('/taskPending', '/taskPending/record', {})
		},
		[Notification.ledgerDataAuditNotice]: (json: any) => {
			return GetNotification('/taskPending', '/taskPending', {})
		},
	},

	[Notification.ExportHistory]: (json: any) =>
		GetNotification('/ledger/overview', '/ledger/fill', {
			ledgerId: json.LedgerId,
			exportRecord: true,
		}),

	[Notification.LedgerInfoUpdated]: (json: any) =>
		GetNotification('/ledger/overview', '/ledger/fill', {
			ledgerId: json.LedgerId,
		}),

	[Notification.LedgerOverdueReminder]: (json: any) =>
		GetNotification('/ledger/overview', '/ledger/fill', {
			ledgerId: json.LedgerId,
		}),

	[Notification.LedgerOverdueReminderLeader]: (json: any) =>
		GetNotification('/ledger/overview', '/ledger/fill', {
			ledgerId: json.LedgerId,
		}),
	[Notification.LedgerResponse]: (json: any) =>
		GetNotification('/ledgerAuthority', '/ledgerAuthority/authority', {}),

	[Notification.RunwayChange]: (json: any) => GetNotification('/runway', '/runway', {}),
	// 跳转任务待办列表
	[Notification.ReportUrge]: (json: any) => {
		return GetNotification('/taskPending', '/taskPending', {})
	},
	//跳转至任务审核详情页面
	[Notification.PlanTaskAudit]: {
		[Notification.ReportTask]: (json: any) => {
			let data: any = {
				id: json.PlanTaskId,
				taskId: json.TaskId,
				type: json.Type,
				currentIndex: 1,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskPending', '/taskPending/detail', data)
		},
		[Notification.PlanTask]: (json: any) => {
			let data: any = {
				id: json.PlanTaskId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/detail', data)
		},
		[Notification.ReportTaskAudit]: (json: any) => {
			let data: any = {
				reportTaskId: json.ReportTaskId,
				areaOrganizationUnitId: json.AreaOrganizationUnitId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/report-task-detail', data)
		},
		[Notification.planTaskAudit]: (json: any) => {
			let data: any = {
				id: json.PlanTaskId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/detail', data)
		},
	},
	[Notification.PlanTaskAuditResult]: {
		[Notification.ReportTask]: (json: any) => {
			let data: any = {
				id: json.PlanTaskId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/detail', data)
		},
		[Notification.PlanTask]: (json: any) => {
			let data: any = {
				id: json.PlanTaskId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/detail', data)
		},
		[Notification.ReportTaskAudit]: (json: any) => {
			let data: any = {
				reportTaskId: json.ReportTaskId,
				areaOrganizationUnitId: json.AreaOrganizationUnitId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/report-task-detail', data)
		},
		[Notification.planTaskAudit]: (json: any) => {
			let data: any = {
				id: json.PlanTaskId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/detail', data)
		},
		[Notification.TaskPending]: (json: any) => {
			let data: any = {
				menu: '任务填报',
			}
			return GetNotification('/taskPending', '/taskPending/record', data)
		},
	},
	[Notification.ReportFilling]: {
		[Notification.ReportTask]: (json: any) => {
			let data: any = {
				menu: '任务填报',
			}
			// if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskPending', '/taskPending', data)
		},
		[Notification.PlanTask]: (json: any) => {
			let data: any = {
				id: json.PlanTaskId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/detail', data)
		},
		[Notification.ReportTaskAudit]: (json: any) => {
			let data: any = {
				reportTaskId: json.ReportTaskId,
				areaOrganizationUnitId: json.AreaOrganizationUnitId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/report-task-detail', data)
		},
		[Notification.planTaskAudit]: (json: any) => {
			let data: any = {
				id: json.PlanTaskId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/detail', data)
		},
	},
	[Notification.ReportRevoke]: {
		[Notification.ReportTask]: (json: any) => {
			let data: any = {
				reportTaskId: json.ReportTaskId,
				areaOrganizationUnitId: json.AreaOrganizationUnitId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/report-task-detail', data)
		},
		[Notification.PlanTask]: (json: any) => {
			let data: any = {
				id: json.PlanTaskId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/detail', data)
		},
		[Notification.ReportTaskAudit]: (json: any) => {
			let data: any = {
				reportTaskId: json.ReportTaskId,
				areaOrganizationUnitId: json.AreaOrganizationUnitId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/report-task-detail', data)
		},
		[Notification.planTaskAudit]: (json: any) => {
			let data: any = {
				id: json.PlanTaskId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/detail', data)
		},
	},
	[Notification.ReportStop]: {
		[Notification.ReportTask]: (json: any) => {
			let data: any = {
				reportTaskId: json.ReportTaskId,
				areaOrganizationUnitId: json.AreaOrganizationUnitId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/report-task-detail', data)
		},
		[Notification.PlanTask]: (json: any) => {
			let data: any = {
				id: json.PlanTaskId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/detail', data)
		},
		[Notification.ReportTaskAudit]: (json: any) => {
			let data: any = {
				reportTaskId: json.ReportTaskId,
				areaOrganizationUnitId: json.AreaOrganizationUnitId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/report-task-detail', data)
		},
		[Notification.planTaskAudit]: (json: any) => {
			let data: any = {
				id: json.PlanTaskId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/detail', data)
		},
	},
	[Notification.DataAudit]: {
		[Notification.ReportTask]: (json: any) => {
			let data: any = {
				reportTaskId: json.ReportTaskId,
				areaOrganizationUnitId: json.AreaOrganizationUnitId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/report-task-detail', data)
		},
		[Notification.PlanTask]: (json: any) => {
			let data: any = {
				id: json.PlanTaskId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/detail', data)
		},
		[Notification.ReportTaskAudit]: (json: any) => {
			let data: any = {
				reportTaskId: json.ReportTaskId,
				areaOrganizationUnitId: json.AreaOrganizationUnitId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/report-task-detail', data)
		},
		[Notification.planTaskAudit]: (json: any) => {
			let data: any = {
				id: json.PlanTaskId,
				type: json.Type,
			}
			if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			return GetNotification('/taskQualityReport', '/taskQualityReport/detail', data)
		},
		[Notification.ReportTaskDataAudit]: (json: any) => {
			let data: any = {
				taskId: json.TaskId,
				reportTaskId: json.ReportTaskId,
				areaOrganizationUnitId: json.AreaOrganizationUnitId,
				type: json.Type,
				businessType: json.BusinessType,
			}
			data.currentIndex = 4
			// if (json.CurrentIndex) data.currentIndex = json.CurrentIndex
			// else data.currentIndex  = 4
			return GetNotification('/taskPending', '/taskPending/report-task-detail', data)
		},
	},
}
/**
 * 通知类型
 */
enum NoticeType {
	/**
	 * 业务表上线
	 */
	ledgerOnline = 101,
	/**
	 * 业务表下线
	 */
	ledgerDeline = 102,
	/**
	 * 业务表催办
	 */
	ledgerUrge = 103,
	/**
	 * 业务表审核
	 */
	ledgerAudit = 104,
	/**
	 * 业务表审核结果
	 */
	ledgerAuditResult = 105,
	/**
	 * 身份认证
	 */
	ledgerIdentity = 106,

	/**
	 * 业务表数据导出
	 */
	ledgerExport = 107,

	/**
	 * 业务表信息更新
	 */
	ledgerUpdate = 108,
	/**
	 * 业务表基础信息更新
	 */
	ledgerBaseUpdate = 109,

	/**
	 * 业务表响应
	 */
	ledgerResponse = 110,

	/**
	 * 业务表超期
	 */
	ledgerOverdue = 113,

	/**
	 * 业务表超期提醒(数据管理岗位)
	 */
	ledgerSGGOverdue = 114,

	/**
	 * 自定义消息通知
	 */
	CustomNotice = 111,

	/**
	 * 用户下线通知
	 */
	userDeline = 112,
}

export const NoticeTabList = [
	{
		name: '业务表消息',
		value: 1,
		typeList: [
			{label: '业务表上线', value: NoticeType.ledgerOnline},
			{label: '业务表下线', value: NoticeType.ledgerDeline},
			{label: '业务表催办', value: NoticeType.ledgerUrge},
			{label: '业务表数据审核', value: NoticeType.ledgerAudit},
			{label: '业务表数据审核结果', value: NoticeType.ledgerAuditResult},
			{label: '身份认证', value: NoticeType.ledgerIdentity},
			{label: '数据导出', value: NoticeType.ledgerExport},
			{label: '业务表信息更新', value: NoticeType.ledgerUpdate},
			{label: '业务表基础信息更新', value: NoticeType.ledgerBaseUpdate},
			{label: '业务表响应', value: NoticeType.ledgerResponse},
			{label: '业务表超期', value: NoticeType.ledgerOverdue},
			{label: '业务表超期(数据管理岗)', value: NoticeType.ledgerSGGOverdue},
		],
	},
	{name: '报表消息', value: 2, typeList: []},
	{
		name: '系统消息',
		value: 3,
		typeList: [
			{label: '自定义通知', value: NoticeType.CustomNotice},
			{label: '用户下线通知', value: NoticeType.userDeline},
		],
	},
]
