import util from '@/plugin/util'

// 主键数据类型选项
export const PrimaryKeyDataTypes = [
	{ label: '字符型C', value: 'string_c' },
	{ label: '数值型N', value: 'number_n' },
	{ label: '逻辑性B', value: 'boolean_b' },
	{ label: '日期型T', value: 'date_t' },
	{ label: 'XXXX', value: 'xxxx' }
]

// 主键加密级别选项
export const EncryptionLevels = [
	{ label: '无/低/高', value: 'none_low_high' },
	{ label: '中等', value: 'medium' },
	{ label: '高级', value: 'high' }
]

// 清理策略选项
export const CleanupStrategies = [
	{ label: '归档', value: 'archive' },
	{ label: '删除', value: 'delete' }
]

// 主键存储位置选项
export const StorageLocationOptions = [
	{ label: '请选择', value: '' },
	{ label: '文件系统', value: 'filesystem' },
	{ label: '存储到配置文件', value: 'configfile' }
]

// 备份频率选项
export const BackupFrequencyOptions = [
	{ label: '每小时', value: 'hourly' },
	{ label: '每日', value: 'daily' },
	{ label: '每周', value: 'weekly' },
	{ label: '每月', value: 'monthly' }
]

// 保留时间选项
export const RetentionTimeOptions = [
	{ label: '保留30天', value: '30' },
	{ label: '保留60天', value: '60' },
	{ label: '保留120天', value: '120' }
]

// 主键索引字段选项
export const IndexFieldOptions = [
	{ label: '请选择', value: '' },
	{ label: '用户名', value: 'username' },
	{ label: '身份证号', value: 'id_card' },
	{ label: '手机号码', value: 'phone' },
	{ label: '邮箱地址', value: 'email' },
	{ label: '员工编号', value: 'employee_id' },
	{ label: '部门编码', value: 'department_code' },
	{ label: '创建时间', value: 'create_time' },
	{ label: '更新时间', value: 'update_time' }
]

// 主键数据接口定义
export interface PrimaryKeyData {
	id: string // 唯一标识
	sequence: number // 序号
	name: string // 主键名称
	dataType: string // 数据类型
	minLength?: number // 最小长度
	maxLength?: number // 最大长度
	description?: string // 说明
	isDefaultValue: boolean // 主键默认值
	updateNotification: boolean // 主键更新通知
	encryptionLevel?: string // 主键加密级别
	encryptionContent?: string // 主键加密内容
	relatedTable?: string // 关联业务表
	cleanupRule?: CleanupRuleData // 主键清理规则
	storageLocation?: StorageLocationConfig // 主键存储位置
	compositeKeyConfig?: CompositeKeyConfig // 复合主键配置
	indexConfig?: IndexConfig // 主键索引配置
	lockConfig?: LockConfig // 主键锁定配置
	foreignKeyRelationConfig?: ForeignKeyRelationConfig // 主键与外键关联配置
	dynamicExtensionConfig?: DynamicExtensionConfig // 主键动态扩展配置
	version?: string // 主键版本
	showOnMobile: boolean // 是否展示移动端
	createTime: string // 创建时间
	updateTime: string // 更新时间
}

// 批量修改数据接口
export interface BatchUpdateData {
	dataType: string // 主键数据类型
	minLength: number // 最小长度
	maxLength: number // 最大长度
}

// 清理规则数据接口
export interface CleanupRuleData {
	cleanupTime: number // 清理时间（天数）
	cleanupStrategy: string // 清理策略
}

// 主键存储位置配置接口
export interface StorageLocationConfig {
	storageLocation: string // 存储位置
}

// 主键备份策略配置接口
export interface BackupStrategyConfig {
	backupStartTime: string // 主键备份开始时间
	fullBackupFrequency: string // 全量备份频率
	incrementalBackupFrequency: string // 增量备份频率
	retentionTime: string // 保留时间
	restoreTargetDate: string // 恢复至指定时期
	restoreExecutionTime: string // 恢复执行时间
}

// 复合主键配置接口
export interface CompositeKeyConfig {
	integerType: boolean // 整数类型
	floatType: boolean // 浮点数类型
	stringType: boolean // 字符串类型
	dateTimeType: boolean // 日期时间类型
}

// 主键索引配置接口
export interface IndexConfig {
	indexField: string // 索引字段
}

// 主键锁定配置接口
export interface LockConfig {
	isLocked: boolean // 是否被锁定
	lockEndTime: string // 锁定结束时间
	lockReason?: string // 锁定原因（可选）
}

// 历史记录数据接口
export interface HistoryRecord {
	id: string // 历史记录唯一标识
	primaryKeyId: string // 主键ID
	primaryKeyName: string // 主键名称
	oldDataType: string // 原数据类型
	newDataType: string // 新数据类型
	operatorName: string // 操作人姓名
	operationTime: string // 操作时间
	operationType: 'update' | 'batch_update' // 操作类型：单条修改/批量修改
}

// 历史记录列配置接口
export interface HistoryColumnConfig {
	sequence: boolean // 序号
	primaryKeyName: boolean // 主键名称
	oldDataType: boolean // 原数据类型
	newDataType: boolean // 新数据类型
	operatorName: boolean // 操作人
	operationTime: boolean // 操作时间
	operationType: boolean // 操作类型
}

// 主键统计数据接口
export interface PrimaryKeyStatsData {
	id: string // 主键ID
	name: string // 主键名称
	usageCount: number // 使用次数
}

// 主键策略枚举
export enum PrimaryKeyStrategy {
	AUTO_INCREMENT = 'auto_increment', // 自增序列
	UUID = 'uuid' // UUID
}

// 主键策略选项
export const PrimaryKeyStrategyOptions = [
	{ label: '自增序列', value: PrimaryKeyStrategy.AUTO_INCREMENT },
	{ label: 'UUID', value: PrimaryKeyStrategy.UUID }
]

// UUID版本选项
export const UUIDVersionOptions = [
	{ label: 'V1.0', value: 'v1.0' },
	{ label: 'V2.0', value: 'v2.0' },
	{ label: 'V3.0', value: 'v3.0' },
	{ label: 'V4.0', value: 'v4.0' }
]

// 自增序列配置接口
export interface AutoIncrementConfig {
	startValue: string // 起始值
	stepSize: number // 步长
}

// UUID配置接口
export interface UUIDConfig {
	version: string // UUID版本
	uppercase: boolean // 大写
	withHyphen: boolean // 连带字符
}

// 主键自动生成配置接口
export interface PrimaryKeyAutoGenConfig {
	strategy: PrimaryKeyStrategy // 主键策略
	autoIncrementConfig?: AutoIncrementConfig // 自增序列配置
	uuidConfig?: UUIDConfig // UUID配置
}

// 本地存储键名
export const STORAGE_KEYS = {
	PRIMARY_KEYS: 'PRIMARY_KEYS_DATA',
	CLEANUP_RULES: 'PRIMARY_KEYS_CLEANUP_RULES',
	SORT_CONFIG: 'PRIMARY_KEYS_SORT_CONFIG',
	HISTORY_RECORDS: 'PRIMARY_KEYS_HISTORY_RECORDS',
	HISTORY_COLUMN_CONFIG: 'PRIMARY_KEYS_HISTORY_COLUMN_CONFIG',
	STORAGE_LOCATION_CONFIG: 'PRIMARY_KEYS_STORAGE_LOCATION_CONFIG',
	BACKUP_STRATEGY_CONFIG: 'PRIMARY_KEYS_BACKUP_STRATEGY_CONFIG',
	AUTO_GEN_CONFIG: 'PRIMARY_KEYS_AUTO_GEN_CONFIG',
	AUTH_STRATEGY_CONFIG: 'PRIMARY_KEYS_AUTH_STRATEGY_CONFIG',
	UNIQUENESS_CONFIG: 'PRIMARY_KEYS_UNIQUENESS_CONFIG',
	CHANGE_PERMISSION_CONFIG: 'PRIMARY_KEY_CHANGE_PERMISSION_CONFIG'
}

// 排序配置接口
export interface SortConfig {
	field: string // 排序字段
	order: 'asc' | 'desc' | null // 排序方向：升序/降序/无排序
}

// 默认主键数据
export const getDefaultPrimaryKeyData = (): PrimaryKeyData[] => [
	{
		id: util._guid(),
		sequence: 1,
		name: '主键1',
		dataType: 'number_n',
		minLength: 2,
		maxLength: 3,
		description: 'xxx',
		isDefaultValue: true,
		updateNotification: true,
		encryptionLevel: 'none_low_high',
		encryptionContent: '',
		relatedTable: '用户表',
		cleanupRule: getDefaultCleanupRule(),
		storageLocation: getDefaultStorageLocationConfig(),
		compositeKeyConfig: getDefaultCompositeKeyConfig(),
		indexConfig: getDefaultIndexConfig(),
		lockConfig: getDefaultLockConfig(),
		version: '1.0',
		showOnMobile: false,
		createTime: new Date().toISOString(),
		updateTime: new Date().toISOString()
	},
	{
		id: util._guid(),
		sequence: 2,
		name: '主键2',
		dataType: 'date_t',
		minLength: 1,
		maxLength: 5,
		description: 'xxx',
		isDefaultValue: true,
		updateNotification: true,
		encryptionLevel: 'none_low_high',
		encryptionContent: '',
		relatedTable: '订单表',
		cleanupRule: getDefaultCleanupRule(),
		storageLocation: getDefaultStorageLocationConfig(),
		compositeKeyConfig: getDefaultCompositeKeyConfig(),
		indexConfig: getDefaultIndexConfig(),
		lockConfig: getDefaultLockConfig(),
		version: '1.1',
		showOnMobile: true,
		createTime: new Date().toISOString(),
		updateTime: new Date().toISOString()
	},
	{
		id: util._guid(),
		sequence: 3,
		name: '主键3',
		dataType: 'date_t',
		minLength: 1,
		maxLength: 5,
		description: 'xxx',
		isDefaultValue: true,
		updateNotification: true,
		encryptionLevel: 'none_low_high',
		encryptionContent: '',
		relatedTable: '产品表',
		compositeKeyConfig: getDefaultCompositeKeyConfig(),
		indexConfig: getDefaultIndexConfig(),
		lockConfig: getDefaultLockConfig(),
		version: '2.0',
		showOnMobile: false,
		createTime: new Date().toISOString(),
		updateTime: new Date().toISOString()
	},
	{
		id: util._guid(),
		sequence: 4,
		name: '主键4',
		dataType: 'date_t',
		minLength: 1,
		maxLength: 1,
		description: 'xxx',
		isDefaultValue: true,
		updateNotification: true,
		encryptionLevel: 'none_low_high',
		encryptionContent: '',
		relatedTable: '日志表',
		compositeKeyConfig: getDefaultCompositeKeyConfig(),
		indexConfig: getDefaultIndexConfig(),
		lockConfig: getDefaultLockConfig(),
		version: '1.5',
		showOnMobile: true,
		createTime: new Date().toISOString(),
		updateTime: new Date().toISOString()
	},

]

// 默认清理规则
export const getDefaultCleanupRule = (): CleanupRuleData => ({
	cleanupTime: 30,
	cleanupStrategy: 'archive'
})

// 默认存储位置配置
export const getDefaultStorageLocationConfig = (): StorageLocationConfig => ({
	storageLocation: ''
})

// 默认排序配置
export const getDefaultSortConfig = (): SortConfig => ({
	field: '',
	order: null
})

// 默认历史记录列配置
export const getDefaultHistoryColumnConfig = (): HistoryColumnConfig => ({
	sequence: true,
	primaryKeyName: true,
	oldDataType: true,
	newDataType: true,
	operatorName: true,
	operationTime: true,
	operationType: false // 默认不显示操作类型列
})

// 默认备份策略配置
export const getDefaultBackupStrategyConfig = (): BackupStrategyConfig => ({
	backupStartTime: '',
	fullBackupFrequency: 'daily',
	incrementalBackupFrequency: 'hourly',
	retentionTime: '30',
	restoreTargetDate: '',
	restoreExecutionTime: ''
})

// 默认复合主键配置
export const getDefaultCompositeKeyConfig = (): CompositeKeyConfig => ({
	integerType: false,
	floatType: false,
	stringType: false,
	dateTimeType: false
})

// 默认主键索引配置
export const getDefaultIndexConfig = (): IndexConfig => ({
	indexField: ''
})

// 默认主键锁定配置
export const getDefaultLockConfig = (): LockConfig => ({
	isLocked: false,
	lockEndTime: '',
	lockReason: ''
})

// 默认自增序列配置
export const getDefaultAutoIncrementConfig = (): AutoIncrementConfig => ({
	startValue: '1',
	stepSize: 1
})

// 默认UUID配置
export const getDefaultUUIDConfig = (): UUIDConfig => ({
	version: 'v4.0',
	uppercase: false,
	withHyphen: true
})

// 默认主键自动生成配置
export const getDefaultPrimaryKeyAutoGenConfig = (): PrimaryKeyAutoGenConfig => ({
	strategy: PrimaryKeyStrategy.AUTO_INCREMENT,
	autoIncrementConfig: getDefaultAutoIncrementConfig(),
	uuidConfig: getDefaultUUIDConfig()
})

// 主键认证策略配置接口
export interface PrimaryKeyAuthStrategyConfig {
	enabled: boolean // 是否启用验证策略
	skipWhitespace: boolean // 跳过空格
	skipSpecialChars: boolean // 跳过特殊字符
}

// 默认主键认证策略配置
export const getDefaultPrimaryKeyAuthStrategyConfig = (): PrimaryKeyAuthStrategyConfig => ({
	enabled: false,
	skipWhitespace: false,
	skipSpecialChars: false
})

// 主键唯一性配置接口
export interface PrimaryKeyUniquenessConfig {
	nullCheck: boolean // 空值检查 - 默认勾选且禁用
	uniquenessCheck: boolean // 唯一性检查 - 默认勾选且禁用
	dataTypeCheck: boolean // 数据类型检查 - 默认未勾选，用户可配置
}

// 主键与外键关联配置接口
export interface ForeignKeyRelationConfig {
	masterTable: string // 主表字段
	slaveTable: string // 从表字段
	cascadeDelete: boolean // 级联删除
	cascadeUpdate: boolean // 级联更新
	setNull: boolean // 设为空
}

// 主键更改权限配置接口
export interface PrimaryKeyChangePermissionConfig {
	authorizedPersons: string[] // 授权人员
}

// 主键动态扩展配置接口
export interface DynamicExtensionConfig {
	extensionField: string // 动态扩展字段
	extensionType: string // 扩展类型
	isRequired: boolean // 是否必填
	defaultValue: string // 默认值
	fieldDescription: string // 字段描述
}

// 默认主键唯一性配置
export const getDefaultPrimaryKeyUniquenessConfig = (): PrimaryKeyUniquenessConfig => ({
	nullCheck: true,
	uniquenessCheck: true,
	dataTypeCheck: false
})

// 默认主键与外键关联配置
export const getDefaultForeignKeyRelationConfig = (): ForeignKeyRelationConfig => ({
	masterTable: '',
	slaveTable: '',
	cascadeDelete: false,
	cascadeUpdate: false,
	setNull: false
})

// 默认主键更改权限配置
export const getDefaultPrimaryKeyChangePermissionConfig = (): PrimaryKeyChangePermissionConfig => ({
	authorizedPersons: []
})

// 默认主键动态扩展配置
export const getDefaultDynamicExtensionConfig = (): DynamicExtensionConfig => ({
	extensionField: '',
	extensionType: '',
	isRequired: false,
	defaultValue: '',
	fieldDescription: ''
})
