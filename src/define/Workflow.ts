import {useGuid} from '@/hooks/useGuid'

export enum FlowType {
	Initiate = '业务表流程',
	Fill = '临时报表流程',
	Export = '业务表导出流程',
}

export enum FlowNodeTypes {
	Input = 'input',
	Output = 'output',
	Report = 'report',
	Review = 'review',
	Condition = 'condition',
	Gateway = 'gateway',
	Sub = 'sub',
	Main = 'main',
}

export enum ExportFlowTypes {
	Exception = -1, //审核异常
	None = 0, //无需审核
	Launching = 1, //发起
	Auditing = 2, //审核中
	Audited = 3, //审核通过
	AuditFailed = 4, //审核不通过
}

export const ExportFlowTypeMap: {[key: string]: any} = {
	[ExportFlowTypes.Exception]: '异常:danger',
	[ExportFlowTypes.None]: '无需审核:primary',
	[ExportFlowTypes.Launching]: '发起:primary',
	[ExportFlowTypes.Auditing]: '审核中:warning',
	[ExportFlowTypes.Audited]: '通过:success',
	[ExportFlowTypes.AuditFailed]: '驳回:danger',
}

export const FlowNodeTypeNames = {
	[FlowNodeTypes.Input]: '流程开始',
	[FlowNodeTypes.Output]: '流程结束',
	[FlowNodeTypes.Report]: '填报',
	[FlowNodeTypes.Review]: '审核',
	[FlowNodeTypes.Condition]: '条件',
	[FlowNodeTypes.Gateway]: '网关',
	[FlowNodeTypes.Sub]: '子任务',
	[FlowNodeTypes.Main]: '主任务',
}

export enum FlowLRNodeTypes {
	Default = -1,
	MyLine = 0,
	StartEvent = 1,
	EndEvent = 2,
	GatewayAnd = 3,
	GatewayXor = 4,
	GatewayInclusive = 5,
	UserTask = 6,
	ScripteTask = 7,
	Subprocess = 8,
	Custom = 9, // 填报
}

export enum FlowAuditCode {
	Create = 'create',
	Agree = 'agree',
	Disagree = 'disagree',
	Timeout = 'timeout',
}

export enum FlowAuditTypes {
	LedgerFilling = 'LedgerFilling', //业务表填报审核
	LedgerDataExport = 'LedgerDataExport', //业务表数据导出审核
	ReportTaskIssude = 'ReportTaskIssude', //报表任务下发审核
	ReportTaskTranspondAudit = 'ReportTaskTranspondAudit', //报表转发审核
	ReportTaskDataAudit = 'ReportTaskDataAudit', //报表数据审核
	ReportTaskFilling = 'ReportTaskFilling', //报表任务填报审核
}

export const FlowAuditTypeData: {[key: string]: any} = {
	[FlowAuditTypes.LedgerFilling]: {
		name: '填报审核',
		code: FlowAuditTypes.LedgerFilling,
	},
	[FlowAuditTypes.LedgerDataExport]: {
		name: '数据导出审核',
		code: FlowAuditTypes.LedgerDataExport,
	},
	[FlowAuditTypes.ReportTaskIssude]: {
		name: '任务下发审核',
		code: FlowAuditTypes.ReportTaskIssude,
	},
	[FlowAuditTypes.ReportTaskTranspondAudit]: {
		name: '转发审核',
		code: FlowAuditTypes.ReportTaskTranspondAudit,
	},
	[FlowAuditTypes.ReportTaskDataAudit]: {
		name: '数据审核',
		code: FlowAuditTypes.ReportTaskDataAudit,
	},
	[FlowAuditTypes.ReportTaskFilling]: {
		name: '任务填报审核',
		code: FlowAuditTypes.ReportTaskFilling,
	},
}

export const DefalutWorkflow = [
	// 业务表填报流程
	{
		id: useGuid(),
		label: FlowNodeTypeNames[FlowNodeTypes.Input],
		type: FlowNodeTypes.Input,
		childNode: [
			{
				id: useGuid(),
				label: FlowNodeTypeNames[FlowNodeTypes.Review],
				type: FlowNodeTypes.Review,
				childNode: [
					{
						id: useGuid(),
						label: `${FlowNodeTypeNames[FlowNodeTypes.Review]}`,
						type: FlowNodeTypes.Review,
						childNode: [],
					},
				],
			},
		],
	},

	// 业务表填报多级审核流程
	{
		id: useGuid(),
		label: FlowNodeTypeNames[FlowNodeTypes.Input],
		type: FlowNodeTypes.Input,
		childNode: [
			{
				id: useGuid(),
				label: FlowNodeTypeNames[FlowNodeTypes.Gateway],
				type: FlowNodeTypes.Gateway,
				childNode: null,
				conditionNodes: [
					{
						id: useGuid(),
						label: '条件',
						type: FlowNodeTypes.Condition,
						childNode: [
							{
								id: useGuid(),
								label: '审核',
								type: FlowNodeTypes.Review,
								childNode: [
									{
										id: useGuid(),
										label: '审核',
										type: FlowNodeTypes.Review,
										childNode: [
											{
												id: useGuid(),
												label: '审核',
												type: FlowNodeTypes.Review,
												childNode: [
													{
														id: useGuid(),
														label: '审核',
														type: FlowNodeTypes.Review,
														childNode: [],
													},
												],
											},
										],
									},
								],
							},
						],
					},
					{
						id: useGuid(),
						label: '条件',
						type: FlowNodeTypes.Condition,
						childNode: [
							{
								id: useGuid(),
								label: '审核',
								type: FlowNodeTypes.Review,
								childNode: [
									{
										id: useGuid(),
										label: '审核',
										type: FlowNodeTypes.Review,
										childNode: [],
									},
								],
							},
						],
					},
					{
						id: useGuid(),
						label: '条件',
						type: FlowNodeTypes.Condition,
						childNode: [
							{
								id: useGuid(),
								label: '审核',
								type: FlowNodeTypes.Review,
								childNode: [
									{
										id: useGuid(),
										label: '审核',
										type: FlowNodeTypes.Review,
										childNode: [],
									},
								],
							},
						],
					},
					{
						id: useGuid(),
						label: '条件',
						type: FlowNodeTypes.Condition,
						childNode: [
							{
								id: useGuid(),
								label: '审核',
								type: FlowNodeTypes.Review,
								childNode: [],
							},
						],
					},
				],
			},
		],
	},

	// 业务表按授权顺序审核流程
	{
		id: useGuid(),
		label: FlowNodeTypeNames[FlowNodeTypes.Input],
		type: FlowNodeTypes.Input,
		childNode: [
			{
				id: useGuid(),
				label: '审核',
				type: FlowNodeTypes.Review,
				childNode: [
					{
						id: useGuid(),
						label: '审核',
						type: FlowNodeTypes.Review,
						childNode: [
							{
								id: useGuid(),
								label: FlowNodeTypeNames[FlowNodeTypes.Gateway],
								type: FlowNodeTypes.Gateway,
								childNode: null,
								conditionNodes: [
									{
										id: useGuid(),
										label: '条件',
										type: FlowNodeTypes.Condition,
										childNode: [
											{
												id: useGuid(),
												label: '审核',
												type: FlowNodeTypes.Review,
												childNode: [
													{
														id: useGuid(),
														label: '审核',
														type: FlowNodeTypes.Review,
														childNode: [],
													},
												],
											},
										],
									},
									{
										id: useGuid(),
										label: '条件',
										type: FlowNodeTypes.Condition,
										childNode: [],
									},
								],
							},
						],
					},
				],
			},
		],
	},
]
