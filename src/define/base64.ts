export class Base64 {
	// private property
	// 字符串转base64
	public static encodeBase64(str) {
		// 对字符串进行编码
		let encode = encodeURI(str)
		// 对编码的字符串转化base64
		let base64 = btoa(encode)
		return base64
	}

	// base64转字符串
	public static decodeBase64(base64) {
		// 对base64转编码
		let decode = atob(base64)
		// 编码转字符串
		let str = decodeURI(decode)
		return str
	}

	// public method for encoding
	public static encode(input) {
		let _keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='
		let output = ''
		let chr1, chr2, chr3, enc1, enc2, enc3, enc4
		let i = 0
		input = this._utf8_encode(input)
		while (i < input.length) {
			chr1 = input.charCodeAt(i++)
			chr2 = input.charCodeAt(i++)
			chr3 = input.charCodeAt(i++)
			enc1 = chr1 >> 2
			enc2 = ((chr1 & 3) << 4) | (chr2 >> 4)
			enc3 = ((chr2 & 15) << 2) | (chr3 >> 6)
			enc4 = chr3 & 63
			if (isNaN(chr2)) {
				enc3 = enc4 = 64
			} else if (isNaN(chr3)) {
				enc4 = 64
			}
			output =
				output +
				_keyStr.charAt(enc1) +
				_keyStr.charAt(enc2) +
				_keyStr.charAt(enc3) +
				_keyStr.charAt(enc4)
		}
		return output
	}

	// public method for decoding
	public static decode(input) {
		let _keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='
		let output = ''
		let chr1, chr2, chr3
		let enc1, enc2, enc3, enc4
		let i = 0
		input = input.replace(/[^A-Za-z0-9\+\/\=]/g, '')
		while (i < input.length) {
			enc1 = _keyStr.indexOf(input.charAt(i++))
			enc2 = _keyStr.indexOf(input.charAt(i++))
			enc3 = _keyStr.indexOf(input.charAt(i++))
			enc4 = _keyStr.indexOf(input.charAt(i++))
			chr1 = (enc1 << 2) | (enc2 >> 4)
			chr2 = ((enc2 & 15) << 4) | (enc3 >> 2)
			chr3 = ((enc3 & 3) << 6) | enc4
			output = output + String.fromCharCode(chr1)
			if (enc3 != 64) {
				output = output + String.fromCharCode(chr2)
			}
			if (enc4 != 64) {
				output = output + String.fromCharCode(chr3)
			}
		}
		output = this._utf8_decode(output)
		return output
	}

	// private method for UTF-8 encoding
	static _utf8_encode(string) {
		string = string.replace(/\r\n/g, '\n')
		let utftext = ''
		for (let n = 0; n < string.length; n++) {
			let c = string.charCodeAt(n)
			if (c < 128) {
				utftext += String.fromCharCode(c)
			} else if (c > 127 && c < 2048) {
				utftext += String.fromCharCode((c >> 6) | 192)
				utftext += String.fromCharCode((c & 63) | 128)
			} else {
				utftext += String.fromCharCode((c >> 12) | 224)
				utftext += String.fromCharCode(((c >> 6) & 63) | 128)
				utftext += String.fromCharCode((c & 63) | 128)
			}
		}
		return utftext
	}

	static _utf8_decode(utftext) {
		let string = ''
		let i = 0
		let c = 0
		let c1 = 0
		let c2 = 0
		while (i < utftext.length) {
			c = utftext.charCodeAt(i)
			if (c < 128) {
				string += String.fromCharCode(c)
				i++
			} else if (c > 191 && c < 224) {
				c2 = utftext.charCodeAt(i + 1)
				string += String.fromCharCode(((c & 31) << 6) | (c2 & 63))
				i += 2
			} else {
				c2 = utftext.charCodeAt(i + 1)
				let c3 = utftext.charCodeAt(i + 2)
				string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63))
				i += 3
			}
		}
		return string
	}
}
