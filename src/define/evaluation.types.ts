/**
 * 评估数据类型定义
 */

// 评估记录接口
export interface EvaluationRecord {
  id: string
  序号: number
  名称: string
  评估得分: string
  创建时间: string
  创建人: string
}

// 评估名称选项
export interface EvaluationNameOption {
  value: string
  label: string
}

// 评估表单数据
export interface EvaluationFormData {
  名称: string
  评估得分: number | null
}

// 搜索表单数据
export interface SearchFormData {
  searchKeyword: string
}

// 分页数据
export interface PaginationData {
  currentPage: number
  pageSize: number
  total: number
  pageSizeArray: number[]
}
