enum AuthorizationMode {
	Personnel = 0, // 用户
	Role = 1, // 角色
	Department = 2, // 科室
}

enum AuthorizationClass {
	Department = '本部门授权',
	CrossDepartment = '跨部门授权',
	DistrictsAndCounties = '区县授权',
}

enum AuthorizationRoles {
	Management = '数据领导', // 数据管理岗
	Leader = '分管领导',
	Head = '科(处)室负责人',
	Staff = '工作人员',
}

enum AuthorizationType {
	View = 0, // 查看
	Create = 1, // 新增
	Update = 2, // 编辑
	Delete = 3, // 删除
	Export = 4, // 导出
	Approve = 5, // 审批
}

const AuthorizationTypeMap: {[key: number]: string} = {
	[AuthorizationType.View]: '查看',
	[AuthorizationType.Create]: '新增',
	[AuthorizationType.Update]: '编辑',
	[AuthorizationType.Delete]: '删除',
	[AuthorizationType.Export]: '导出',
	[AuthorizationType.Approve]: '审批',
}

const AuthorizationRolesMap = [
	{
		label: AuthorizationRoles.Management,
		value: AuthorizationRoles.Management,
		otherLabel: '数据管理岗',
	},
	{label: AuthorizationRoles.Leader, value: AuthorizationRoles.Leader},
	{label: AuthorizationRoles.Head, value: AuthorizationRoles.Head},
	{label: AuthorizationRoles.Staff, value: AuthorizationRoles.Staff},
]

export {
	AuthorizationMode,
	AuthorizationType,
	AuthorizationTypeMap,
	AuthorizationClass,
	AuthorizationRoles,
	AuthorizationRolesMap,
}
