export const FeedbackTypeList = [
	{name: '业务表问题反馈', value: 1},
	{name: '报表问题反馈', value: 2},
	{name: '系统优化建议', value: 3},
	{name: '业务表数据核实反馈', value: 4},
]
enum QuestionFeedBackStatus {
	/**
	 * 未处理
	 */
	Untreated = 1,
	/**
	 * 处理中
	 */
	Processing = 2,
	/**
	 * 已处理
	 */
	Completed = 3,
}
export const displayName = (type: number) => {
	const item = FeedbackTypeList.find((item) => item.value === type)
	return item ? item.name : ''
}

export enum FeedbackStatus {
	/**
	 * 未处理
	 */
	Untreated = 1,
	/**
	 * 处理中
	 */
	Processing = 2,
	/**
	 * 已处理
	 */
	Completed = 3,
}

export const FeedBackStatusTrans = [
	{
		label: '未处理',
		value: FeedbackStatus.Untreated,
		color: 'danger',
	},
	{
		label: '处理中',
		value: FeedbackStatus.Processing,
		color: 'warning',
	},
	{
		label: '已处理',
		value: FeedbackStatus.Completed,
		color: 'success',
	},
]
