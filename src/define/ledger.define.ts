import util from '@/plugin/util'

export const DataTypeList = [
	{name: '文本', value: 'string', row: 1111},
	{name: '整数', value: 'int'},
	{name: '小数', value: 'decimal'},
	{name: '日期', value: 'date'},
	{name: '日期和时间', value: 'datetime'},
	{name: '单选', value: 'radio'},
	{name: '多选', value: 'checkbox'},
	{name: '图片', value: 'images'},
	{name: '附件', value: 'attachments'},
]
export const defaultTableData = [
	{
		__id: util._guid(),
		isUnique: false, // 是否唯一
		name: 'City', // 列名
		
		displayName: '所属城市', // 别名
		// tableInfoId: null, // 数据集id
		dbTableFieldId: null, // 所属表数据集映射字段id
		tableDataSetId: null, // 数据集字段id
		show: false,
		isNodeDis: false,
		__isAddNew: true,
		__tableDataSetStr: '', // 数据集显示名称
		__desensitization: false, // 脱敏展示
		desensitizationType: null, // 脱敏类型
		type: 'string_500', // 字段类型
		multiple: null, // true 多选 false 单选 null 自由输入
		isNullable: false, // 是否必填
		isListField: true, // 是否列表字段
		isQueryField: true, // 是否查询字段
		editDisabled: false, // 是否可编辑
		options: null, // 单选/多选选项
		maxLength: 500, // 文本限制
		sort: 1, // 列表展示顺序
		sorsIndex: 1,
		isString: false,
		__notSource: true, // 无数据来源
		lastOnlineTime: 'field',
		clientSettings: [
			{
				client: 1,
				sort: null,
				isDisplay: false,
			},
		], // 是否展示在移动端
	},
	{
		__id: util._guid(),

		isUnique: false, // 是否唯一
		
		name: 'District', // 列名
		displayName: '所属区县', // 别名
		// tableInfoId: null, // 数据集id
		isNodeDis: false,
		dbTableFieldId: null, // 所属表数据集映射字段id
		tableDataSetId: null, // 数据集字段id
		__isAddNew: true,
		isNodeListField: true,
		__tableDataSetStr: '', // 数据集显示名称
		__desensitization: false, // 脱敏展示
		desensitizationType: null, // 脱敏类型
		type: 'string_500', // 字段类型
		multiple: null, // true 多选 false 单选 null 自由输入
		isNullable: false, // 是否必填
		isListField: true, // 是否列表字段
		isQueryField: true, // 是否查询字段
		editDisabled: false, // 是否可编辑
		lastOnlineTime: 'field',
		clientSettings: [
			{
				client: 1,
				sort: null,
				isDisplay: false,
			},
		], // 是否展示在移动端
		options: null, // 单选/多选选项
		maxLength: 500, // 文本限制
		isString: false,
		sort: 2, // 列表展示顺序
		sorsIndex: 2,

		__notSource: true, // 无数据来源
	},
	{
		__id: util._guid(),
		
		isUnique: false, // 是否唯一
		name: 'Street', // 列名
		displayName: '所属街镇', // 别名
		// tableInfoId: null, // 数据集id
		dbTableFieldId: null, // 所属表数据集映射字段id
		tableDataSetId: null, // 数据集字段id
		__tableDataSetStr: '', // 数据集显示名称
		isNodeDis: false,
		__isAddNew: true,
		__desensitization: false, // 脱敏展示
		desensitizationType: null, // 脱敏类型
		type: 'string_500', // 字段类型
		isNodeListField: true,
		isString: false,
		multiple: null, // true 多选 false 单选 null 自由输入
		isNullable: false, // 是否必填
		isListField: true, // 是否列表字段
		isQueryField: true, // 是否查询字段
		editDisabled: false, // 是否可编辑
		lastOnlineTime: 'field',
		clientSettings: [
			{
				client: 1,
				sort: null,
				isDisplay: false,
			},
		], // 是否展示在移动端
		options: null, // 单选/多选选项
		maxLength: 500, // 文本限制

		sort: 3, // 列表展示顺序
		sorsIndex: 3,

		__notSource: true, // 无数据来源
	},
	{
		__id: util._guid(),
		
		isUnique: false, // 是否唯一
		name: 'Community', // 列名
		displayName: '所属村社', // 别名
		__isAddNew: true,
		// tableInfoId: null, // 数据集id
		isNodeDis: false,
		isNodeListField: true,

		dbTableFieldId: null, // 所属表数据集映射字段id
		tableDataSetId: null, // 数据集字段id
		__tableDataSetStr: '', // 数据集显示名称
		__desensitization: false, // 脱敏展示
		desensitizationType: null, // 脱敏类型
		type: 'string_500', // 字段类型
		multiple: null, // true 多选 false 单选 null 自由输入
		isNullable: false, // 是否必填
		isListField: true, // 是否列表字段
		isQueryField: true, // 是否查询字段
		lastOnlineTime: 'field',
		isString: false,
		editDisabled: false, // 是否可编辑
		clientSettings: [
			{
				client: 1,
				sort: null,
				isDisplay: false,
			},
		], // 是否展示在移动端
		options: null, // 单选/多选选项
		maxLength: 500, // 文本限制

		sort: 4, // 列表展示顺序
		sorsIndex: 4,
		__notSource: true, // 无数据来源
	},
	// {
	// 	__id: util._guid(),

	// 	isUnique: false, // 是否唯一
	// 	name: 'Department', // 列名
	// 	displayName: '所属部门', // 别名
	// 	__isAddNew: true,
	// 	// tableInfoId: null, // 数据集id
	// 	dbTableFieldId: null, // 所属表数据集映射字段id
	// 	tableDataSetId: null, // 数据集字段id
	// 	__tableDataSetStr: '', // 数据集显示名称
	// 	__desensitization: false, // 脱敏展示
	// 	desensitizationType: null, // 脱敏类型
	// 	type: 'string_500', // 字段类型
	// 	multiple: null, // true 多选 false 单选 null 自由输入
	// 	isNullable: false, // 是否必填
	// 	isListField: true, // 是否列表字段
	// 	isQueryField: true, // 是否查询字段
	// 	editDisabled: false, // 是否可编辑
	// 	options: null, // 单选/多选选项
	// 	maxLength: 500, // 文本限制

	// 	lastOnlineTime: 'field',

	// 	sort: 5, // 列表展示顺序
	// 	sorsIndex: 5,
	// 	__notSource: true, // 无数据来源
	// 	clientSettings: [
	// 		{
	// 			client: 1,
	// 			sort: null,
	// 			isDisplay: false,
	// 		},
	// 	], // 是否展示在移动端
	// },
	{
		__id: util._guid(),
		
		isUnique: false, // 是否唯一
		name: 'UpdateTime', // 列名
		displayName: '更新时间', // 别名
		// tableInfoId: null, // 数据集id
		dbTableFieldId: null, // 所属表数据集映射字段id
		__isAddNew: true,
		tableDataSetId: null, // 数据集字段id
		__tableDataSetStr: '', // 数据集显示名称
		__desensitization: false, // 脱敏展示
		desensitizationType: null, // 脱敏类型
		type: 'datetime', // 字段类型
		multiple: null, // true 多选 false 单选 null 自由输入
		isNullable: false, // 是否必填
		isListField: true, // 是否列表字段
		isQueryField: true, // 是否查询字段
		editDisabled: false, // 是否可编辑
		lastOnlineTime: 'field',
		calculateRule: null,
		isString: false,
		clientSettings: [
			{
				client: 1,
				sort: null,
				isDisplay: false,
			},
		], // 是否展示在移动端
		options: null, // 单选/多选选项
		sort: 5, // 列表展示顺序
		sorsIndex: 5,
		__notSource: true, // 无数据来源
	},
	{
		__id: util._guid(),
		
		isUnique: false, // 是否唯一
		name: 'Informant', // 列名
		displayName: '填报人', // 别名
		// tableInfoId: null, // 数据集id
		dbTableFieldId: null, // 所属表数据集映射字段id
		tableDataSetId: null, // 数据集字段id
		__isAddNew: true,
		__tableDataSetStr: '', // 数据集显示名称
		__desensitization: false, // 脱敏展示
		desensitizationType: null, // 脱敏类型
		type: 'string_500', // 字段类型
		multiple: null, // true 多选 false 单选 null 自由输入
		isNullable: false, // 是否必填
		isListField: true, // 是否列表字段
		isQueryField: true, // 是否查询字段
		lastOnlineTime: 'field',
		isString: false,
		editDisabled: false, // 是否可编辑
		clientSettings: [
			{
				client: 1,
				sort: null,
				isDisplay: false,
			},
		], // 是否展示在移动端
		options: null, // 单选/多选选项

		maxLength: 500, // 文本限制
		sort: 6, // 列表展示顺序
		sorsIndex: 6,

		__notSource: true, // 无数据来源
	},
	{
		__id: util._guid(),
		
		isUnique: false, // 是否唯一
		name: 'Editor', // 列名
		displayName: '编辑人', // 别名
		// tableInfoId: null, // 数据集id
		dbTableFieldId: null, // 所属表数据集映射字段id
		tableDataSetId: null, // 数据集字段id
		__tableDataSetStr: '', // 数据集显示名称
		__desensitization: false, // 脱敏展示
		desensitizationType: null, // 脱敏类型
		__isAddNew: true,
		lastOnlineTime: 'field',
		isString: false,
		type: 'string_500', // 字段类型
		multiple: null, // true 多选 false 单选 null 自由输入
		isNullable: false, // 是否必填
		isListField: true, // 是否列表字段
		isQueryField: true, // 是否查询字段
		editDisabled: false, // 是否可编辑
		clientSettings: [
			{
				client: 1,
				sort: null,
				isDisplay: false,
			},
		], // 是否展示在移动端
		options: null, // 单选/多选选项
		maxLength: 500, // 文本限制

		sort: 7, // 列表展示顺序
		sorsIndex: 7,
		__notSource: true, // 无数据来源
	},
	{
		__id: util._guid(),
		
		isUnique: false, // 是否唯一
		name: 'DataSource', // 列名
		displayName: '数据来源', // 别名
		// tableInfoId: null, // 数据集id
		dbTableFieldId: null, // 所属表数据集映射字段id
		tableDataSetId: null, // 数据集字段id
		__tableDataSetStr: '', // 数据集显示名称
		__desensitization: false, // 脱敏展示
		desensitizationType: null, // 脱敏类型
		__isAddNew: true,
		lastOnlineTime: 'field',
		isString: false,
		type: 'string_500', // 字段类型
		multiple: null, // true 多选 false 单选 null 自由输入
		isNullable: false, // 是否必填
		isListField: true, // 是否列表字段
		isQueryField: true, // 是否查询字段
		editDisabled: false, // 是否可编辑
		clientSettings: [
			{
				client: 1,
				sort: null,
				isDisplay: false,
			},
		], // 是否展示在移动端
		options: null, // 单选/多选选项
		maxLength: 500, // 文本限制

		sort: 8, // 列表展示顺序
		sorsIndex: 8,
		__notSource: true, // 无数据来源
	},
// 	{
// 		__id: util._guid(),

// 		isUnique: false, // 是否唯一
// 		name: 'DataVisibilityRange', // 列名
// 		displayName: '数据可见范围', // 别名
// 		// tableInfoId: null, // 数据集id
// 		dbTableFieldId: null, // 所属表数据集映射字段id
// 		tableDataSetId: null, // 数据集字段id
// 		__tableDataSetStr: '', // 数据集显示名称
// 		__desensitization: false, // 脱敏展示
// 		desensitizationType: null, // 脱敏类型
// 		__isAddNew: true,
// 		lastOnlineTime: 'field',
// 		isString: false,
// 		type: 'string_500', // 字段类型
// 		multiple: null, // true 多选 false 单选 null 自由输入
// 		isNullable: false, // 是否必填
// 		isListField: true, // 是否列表字段
// 		isQueryField: true, // 是否查询字段
// 		editDisabled: false, // 是否可编辑
// 		clientSettings: [
// 						{
// 										client: 1,
// 										sort: null,
// 										isDisplay: false,
// 						},
// 		], // 是否展示在移动端
// 		options: null, // 单选/多选选项
// 		maxLength: 500, // 文本限制

// 		sort: 9, // 列表展示顺序
// 		sorsIndex: 9,
// 		__notSource: true, // 无数据来源
// },

]

// export const DataNewTypeList = [
// 	{ name: '文本(50以内)', value: 'string_50' },
// 	{ name: '文本(200以内)', value: 'string_200' },
// 	{ name: '文本(500以内)', value: 'string_500' },
// 	{ name: '文本', value: 'string' },
// 	{ name: '整数', value: 'int' },
// 	{ name: '小数', value: 'decimal' },
// 	{ name: '日期', value: 'date' },
// 	{ name: '日期和时间', value: 'datetime' },
// 	{ name: '单选', value: 'radio' },
// 	{ name: '多选', value: 'checkbox' },
// 	{ name: '图片', value: 'images' },
// 	{ name: '附件', value: 'attachments' },
// 	{ name: '居民身份证号', value: 'identification_number' },
// 	{ name: '电话号码', value: 'phone' },
// 	// { name: '残疾人证号', value: 'disability_certificate_number' },
// 	{ name: '性别', value: 'sex' },
// 	{ name: '年龄', value: 'age' },
// 	{ name: '出生日期', value: 'birthday' },
// 	{ name: '邮箱', value: 'email' },

// ]
export const DataNewTypeList = [
	{
		label: '基础数据',
		options: [
			// { name: '文本(50以内)', value: 'string_50' },
			// { name: '文本(200以内)', value: 'string_200' },
			// { name: '文本(500以内)', value: 'string_500' },
			{name: '长文本(超过500个文字)', value: 'string_0'},
			{name: '短文本(不超过500个文字)', value: 'string_500'},
			// { name: '文本', value: 'string' },
			{name: '整数', value: 'int'},
			{name: '小数', value: 'decimal'},
			// {name: '日期', value: 'date'},
			{name: '日期和时间', value: 'datetime'},
			{name: '单选', value: 'radio'},
			{name: '多选', value: 'checkbox'},
			{name: '图片', value: 'images'},
			{name: '附件', value: 'attachments'},
		],
	},
	{
		label: '常用数据',
		options: [
			{name: '居民身份证号', value: 'identification_number'},
			{name: '证件号', value: 'certificate'},
			{name: '联系电话', value: 'phone'},
			// { name: '残疾人证号', value: 'disability_certificate_number' },
			{name: '性别', value: 'sex'},
			{name: '年龄', value: 'age'},
			{name: '出生日期', value: 'birthday'},
			{name: '邮箱', value: 'email'},
		],
	},
]
export const DataNewTypeListOld = [
	{
		label: '基础数据',
		options: [
			// { name: '文本(50以内)', value: 'string_50' },
			// { name: '文本(200以内)', value: 'string_200' },
			// { name: '文本(500以内)', value: 'string_500' },
			{name: '长文本(超过500个文字)', value: 'string_0'},
			{name: '短文本(不超过500个文字)', value: 'string_500'},
			// { name: '文本', value: 'string' },
			{name: '整数', value: 'int', disabled: true},
			{name: '小数', value: 'decimal', disabled: true},
			// {name: '日期', value: 'date'},
			{name: '日期和时间', value: 'datetime', disabled: true},
			{name: '单选', value: 'radio'},
			{name: '多选', value: 'checkbox'},
			{name: '图片', value: 'images', disabled: true},
			{name: '附件', value: 'attachments', disabled: true},
		],
	},
	{
		label: '常用数据',
		options: [
			{name: '居民身份证号', value: 'identification_number'},
			{name: '证件号', value: 'certificate',disabled: true},
			{name: '联系电话', value: 'phone'},
			// { name: '残疾人证号', value: 'disability_certificate_number' },
			{name: '性别', value: 'sex'},
			{name: '年龄', value: 'age', disabled: true},
			{name: '出生日期', value: 'birthday', disabled: true},
			{name: '邮箱', value: 'email', disabled: true},
		],
	},
]
export const RunwayList = [
	{id: '2', label: '党的建设', name: '党的建设', value: 1, icon: 'dangj', index: 1},
	{id: '3', label: '经济发展', name: '经济发展', value: 2, icon: 'jingj', index: 2},
	{id: '4', label: '民生服务', name: '民生服务', value: 3, icon: 'gongg', index: 3},
	{id: '5', label: '平安法治', name: '平安法治', value: 4, icon: 'pinga', index: 4},
	// {id: '1', label: '市级共性业务表', name: '市级共性业务表', value: 0, icon: 'city', index: 0},
]
export const RunwayListData = [
	{id: '0', label: '全部业务表', name: '全部业务表', value: 0, icon: 'city', index: 0},
	// {id: '1', label: '市级共性业务表', name: '市级共性业务表', value: 1, icon: 'city', index: 1},
	{id: '2', label: '党的建设', name: '党的建设', value: 2, icon: 'dangj', index: 2},
	{id: '3', label: '经济发展', name: '经济发展', value: 3, icon: 'jingj', index: 3},
	{id: '4', label: '民生服务', name: '民生服务', value: 4, icon: 'gongg', index: 4},
	{id: '5', label: '平安法治', name: '平安法治', value: 5, icon: 'pinga', index: 5},
]

export const DesensitizationRules = [
	{label: '手机号', value: 1},
	{label: '邮箱', value: 2},
	{label: '身份证', value: 3},
	{label: '银行卡', value: 4},
	{label: '地址', value: 5},
	{label: '姓名', value: 6},
	{label: '其他数据类型脱敏', value: 0},
]

export const ReminderCycleList = [
	// {label: '全部', value: null},
	{label: '不提醒', value: 0},
	// {label: '每天', value: 1},
	{label: '每周', value: 2},
	{label: '每月', value: 3},
	{label: '每半月', value: 6},
	{label: '每季度', value: 4},
	{label: '每半年', value: 7},
	{label: '每年', value: 5},
]

export const warnLevelList = [
	{label: '正常提醒', value: 1},
	{label: '黄色预警', value: 2},
	{label: '红色预警', value: 3},
	// {label: '已处理预警', value: 0},
	// {label: '其他', value: 5},
]
