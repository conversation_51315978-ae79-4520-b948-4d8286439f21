/**
 * 任务关系图谱数据类型定义
 * 基于Vue Flow和项目规范文档的类型定义
 */

import type { Node, Edge } from '@vue-flow/core'

// ==================== 节点类型定义 ====================

/**
 * 任务节点类型枚举
 * 对应原型设计中的6种节点类型
 */
export enum TaskNodeType {
  /** 圆明说明 - 红色圆形节点 */
  EXPLANATION = 'explanation',
  /** 主任务节点 - 蓝色大圆形节点 */
  MAIN_TASK = 'main-task',
  /** 子任务节点 - 青色中等圆形节点 */
  SUB_TASK = 'sub-task',
  /** 已完成任务 - 绿色节点 */
  COMPLETED = 'completed',
  /** 进行中任务 - 橙色节点 */
  IN_PROGRESS = 'in-progress',
  /** 未开始任务 - 红色节点 */
  NOT_STARTED = 'not-started'
}

/**
 * 任务状态枚举
 */
export enum TaskStatus {
  NOT_STARTED = 'not-started',
  IN_PROGRESS = 'in-progress',
  COMPLETED = 'completed',
  BLOCKED = 'blocked',
  CANCELLED = 'cancelled'
}

/**
 * 节点优先级枚举
 */
export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

/**
 * 节点大小枚举
 */
export enum NodeSize {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large'
}

// ==================== 边类型定义 ====================

/**
 * 连线类型枚举
 */
export enum EdgeType {
  /** 依赖关系 - 实线箭头 */
  DEPENDENCY = 'dependency',
  /** 顺序关系 - 实线箭头，动画 */
  SEQUENCE = 'sequence',
  /** 并行关系 - 虚线 */
  PARALLEL = 'parallel',
  /** 条件关系 - 虚线箭头 */
  CONDITIONAL = 'conditional',
  /** 自定义关系 */
  CUSTOM = 'custom'
}

// ==================== 数据接口定义 ====================

/**
 * 任务节点数据接口
 */
export interface TaskNodeData {
  /** 节点标签/名称 */
  label: string
  /** 节点描述 */
  description?: string
  /** 任务状态 */
  status: TaskStatus
  /** 优先级 */
  priority?: TaskPriority
  /** 负责人 */
  assignee?: string
  /** 参与人员 */
  participants?: string[]
  /** 截止日期 */
  dueDate?: Date | string
  /** 标签 */
  tags?: string[]
  /** 进度百分比 */
  progress?: number
  /** 预估工时 */
  estimatedHours?: number
  /** 实际工时 */
  actualHours?: number
  /** 节点颜色 */
  color?: string
  /** 节点大小 */
  size?: NodeSize
  /** 扩展元数据 */
  metadata?: Record<string, any>
  /** 原始节点类型（用于样式计算） */
  originalType?: TaskNodeType
}

/**
 * 连线数据接口
 */
export interface EdgeData {
  /** 连线标签 */
  label?: string
  /** 连线描述 */
  description?: string
  /** 条件表达式 */
  condition?: string
  /** 权重 */
  weight?: number
  /** 连线颜色 */
  color?: string
  /** 是否动画 */
  animated?: boolean
  /** 扩展元数据 */
  metadata?: Record<string, any>
}

/**
 * 任务图谱节点接口
 * 扩展Vue Flow的Node接口
 */
export interface TaskGraphNode extends Node {
  /** 节点类型 */
  type: TaskNodeType
  /** 节点数据 */
  data: TaskNodeData
  /** 节点位置 */
  position: { x: number; y: number }
  /** 是否可拖拽 */
  draggable?: boolean
  /** 是否可选择 */
  selectable?: boolean
  /** 是否可删除 */
  deletable?: boolean
  /** 是否可连接 */
  connectable?: boolean
  /** 自定义样式类名 */
  className?: string
  /** 内联样式 */
  style?: Record<string, any>
}

/**
 * 任务图谱连线接口
 * 扩展Vue Flow的Edge接口
 */
export interface TaskGraphEdge extends Edge {
  /** 连线类型 */
  type: EdgeType
  /** 连线数据 */
  data?: EdgeData
  /** 源节点ID */
  source: string
  /** 目标节点ID */
  target: string
  /** 是否动画 */
  animated?: boolean
  /** 连线标签 */
  label?: string
  /** 自定义样式类名 */
  className?: string
  /** 内联样式 */
  style?: Record<string, any>
}

// ==================== 视口和配置 ====================

/**
 * 视口信息接口
 */
export interface Viewport {
  /** X轴偏移 */
  x: number
  /** Y轴偏移 */
  y: number
  /** 缩放比例 */
  zoom: number
}

/**
 * 图谱元数据接口
 */
export interface GraphMetadata {
  /** 创建者 */
  creator?: string
  /** 最后修改者 */
  lastModifier?: string
  /** 版本号 */
  version: number
  /** 描述 */
  description?: string
  /** 标签 */
  tags?: string[]
  /** 扩展属性 */
  [key: string]: any
}

/**
 * 图谱数据主接口
 */
export interface GraphData {
  /** 图谱ID */
  id: string
  /** 图谱名称 */
  name: string
  /** 图谱描述 */
  description?: string
  /** 节点列表 */
  nodes: TaskGraphNode[]
  /** 连线列表 */
  edges: TaskGraphEdge[]
  /** 视口信息 */
  viewport: Viewport
  /** 元数据 */
  metadata: GraphMetadata
  /** 创建时间 */
  createdAt: Date | string
  /** 更新时间 */
  updatedAt: Date | string
}

// ==================== 样式配置 ====================

/**
 * 节点样式配置接口
 */
export interface NodeStyleConfig {
  /** 背景色 */
  backgroundColor: string
  /** 边框色 */
  borderColor: string
  /** 文字颜色 */
  textColor: string
  /** 节点尺寸 */
  size: { width: number; height: number }
  /** 边框圆角 */
  borderRadius: string
  /** 字体大小 */
  fontSize?: string
  /** 字体粗细 */
  fontWeight?: string
}

/**
 * 连线样式配置接口
 */
export interface EdgeStyleConfig {
  /** 线条颜色 */
  stroke: string
  /** 线条宽度 */
  strokeWidth: number
  /** 虚线样式 */
  strokeDasharray: string
  /** 箭头标记 */
  markerEnd: string
  /** 是否动画 */
  animated: boolean
}

// ==================== 操作历史 ====================

/**
 * 操作类型枚举
 */
export enum OperationType {
  ADD_NODE = 'add_node',
  UPDATE_NODE = 'update_node',
  DELETE_NODE = 'delete_node',
  ADD_EDGE = 'add_edge',
  UPDATE_EDGE = 'update_edge',
  DELETE_EDGE = 'delete_edge',
  MOVE_NODE = 'move_node',
  BATCH_OPERATION = 'batch_operation'
}

/**
 * 操作历史记录接口
 */
export interface OperationRecord {
  /** 操作ID */
  id: string
  /** 操作类型 */
  type: OperationType
  /** 操作时间戳 */
  timestamp: number
  /** 操作数据 */
  data: any
  /** 操作前状态 */
  beforeState?: any
  /** 操作后状态 */
  afterState?: any
  /** 操作描述 */
  description?: string
}

// ==================== 导出格式 ====================

/**
 * 导出格式枚举
 */
export enum ExportFormat {
  PNG = 'png',
  SVG = 'svg',
  JSON = 'json',
  PDF = 'pdf'
}

/**
 * 导出配置接口
 */
export interface ExportConfig {
  /** 导出格式 */
  format: ExportFormat
  /** 文件名 */
  filename?: string
  /** 图片质量 (0-1) */
  quality?: number
  /** 背景色 */
  backgroundColor?: string
  /** 是否包含背景 */
  includeBackground?: boolean
  /** 缩放比例 */
  scale?: number
}

// ==================== 样式映射配置 ====================

/**
 * 节点样式映射配置
 * 基于原型设计的颜色和尺寸规范
 */
export const NODE_STYLE_MAP: Record<TaskNodeType, NodeStyleConfig> = {
  [TaskNodeType.EXPLANATION]: {
    backgroundColor: '#ff4d4f',
    borderColor: '#cf1322',
    textColor: '#ffffff',
    size: { width: 80, height: 80 },
    borderRadius: '50%',
    fontSize: '12px',
    fontWeight: 'normal'
  },
  [TaskNodeType.MAIN_TASK]: {
    backgroundColor: '#1890ff',
    borderColor: '#096dd9',
    textColor: '#ffffff',
    size: { width: 120, height: 120 },
    borderRadius: '50%',
    fontSize: '14px',
    fontWeight: 'bold'
  },
  [TaskNodeType.SUB_TASK]: {
    backgroundColor: '#13c2c2',
    borderColor: '#08979c',
    textColor: '#ffffff',
    size: { width: 100, height: 100 },
    borderRadius: '50%',
    fontSize: '12px',
    fontWeight: 'normal'
  },
  [TaskNodeType.COMPLETED]: {
    backgroundColor: '#52c41a',
    borderColor: '#389e0d',
    textColor: '#ffffff',
    size: { width: 90, height: 90 },
    borderRadius: '50%',
    fontSize: '12px',
    fontWeight: 'normal'
  },
  [TaskNodeType.IN_PROGRESS]: {
    backgroundColor: '#fa8c16',
    borderColor: '#d46b08',
    textColor: '#ffffff',
    size: { width: 90, height: 90 },
    borderRadius: '50%',
    fontSize: '12px',
    fontWeight: 'normal'
  },
  [TaskNodeType.NOT_STARTED]: {
    backgroundColor: '#f5222d',
    borderColor: '#cf1322',
    textColor: '#ffffff',
    size: { width: 90, height: 90 },
    borderRadius: '50%',
    fontSize: '12px',
    fontWeight: 'normal'
  }
}

/**
 * 连线样式映射配置
 */
export const EDGE_STYLE_MAP: Record<EdgeType, EdgeStyleConfig> = {
  [EdgeType.DEPENDENCY]: {
    stroke: '#1890ff',
    strokeWidth: 2,
    strokeDasharray: 'none',
    markerEnd: 'url(#arrow)',
    animated: false
  },
  [EdgeType.SEQUENCE]: {
    stroke: '#52c41a',
    strokeWidth: 2,
    strokeDasharray: 'none',
    markerEnd: 'url(#arrow)',
    animated: true
  },
  [EdgeType.PARALLEL]: {
    stroke: '#fa8c16',
    strokeWidth: 2,
    strokeDasharray: '5,5',
    markerEnd: 'url(#arrow)',
    animated: false
  },
  [EdgeType.CONDITIONAL]: {
    stroke: '#722ed1',
    strokeWidth: 2,
    strokeDasharray: '10,5',
    markerEnd: 'url(#arrow)',
    animated: false
  },
  [EdgeType.CUSTOM]: {
    stroke: '#8c8c8c',
    strokeWidth: 2,
    strokeDasharray: 'none',
    markerEnd: 'url(#arrow)',
    animated: false
  }
}

// ==================== 图例配置 ====================

/**
 * 图例项接口
 */
export interface LegendItem {
  /** 节点类型 */
  type: TaskNodeType
  /** 显示标签 */
  label: string
  /** 颜色 */
  color: string
  /** 描述 */
  description?: string
}

/**
 * 图例配置
 * 对应右侧图例面板的显示内容
 */
export const LEGEND_ITEMS: LegendItem[] = [
  {
    type: TaskNodeType.EXPLANATION,
    label: '圆明说明',
    color: '#ff4d4f',
    description: '用于说明和注释的节点'
  },
  {
    type: TaskNodeType.MAIN_TASK,
    label: '主任务节点',
    color: '#1890ff',
    description: '主要任务节点，通常是核心业务流程'
  },
  {
    type: TaskNodeType.SUB_TASK,
    label: '子任务节点',
    color: '#13c2c2',
    description: '子任务节点，从属于主任务'
  },
  {
    type: TaskNodeType.COMPLETED,
    label: '已完成任务',
    color: '#52c41a',
    description: '已经完成的任务节点'
  },
  {
    type: TaskNodeType.IN_PROGRESS,
    label: '进行中任务',
    color: '#fa8c16',
    description: '正在执行中的任务节点'
  },
  {
    type: TaskNodeType.NOT_STARTED,
    label: '未开始任务',
    color: '#f5222d',
    description: '尚未开始的任务节点'
  }
]

// ==================== 错误处理 ====================

/**
 * 图谱错误类型枚举
 */
export enum GraphErrorType {
  VALIDATION_ERROR = 'validation_error',
  NETWORK_ERROR = 'network_error',
  STORAGE_ERROR = 'storage_error',
  EXPORT_ERROR = 'export_error',
  IMPORT_ERROR = 'import_error'
}

/**
 * 图谱错误接口
 */
export interface GraphError {
  /** 错误类型 */
  type: GraphErrorType
  /** 错误消息 */
  message: string
  /** 错误详情 */
  details?: any
  /** 错误时间戳 */
  timestamp: Date
  /** 是否可恢复 */
  recoverable: boolean
}
