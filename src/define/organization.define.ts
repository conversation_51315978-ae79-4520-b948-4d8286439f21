export const DEFAULT_UNITTYPE = [
	{
		label: '行政区域',
		value: 1,
	},
	{
		label: '行政部门',
		value: 2,
	},
]
export const DEFAULT_STAFFROLETYPE = [
	{
		label: '普通员工',
		value: 0,
	},
	{
		label: '部门领导',
		value: 1,
	},
	{
		label: '部门干事',
		value: 2,
	},
	{
		label: '社区成员',
		value: 3,
	},
]
export const STAFFROLEARRAY = ['普通员工', '分管领导', '工作人员', '社区成员', '数据领导']

export const USER_ROLES_ENUM = Object.freeze({
	WORK_STAFF: '工作人员',
	DEPARTMENT_LEADER: '分管领导',
	DATA_LEADER: '数据领导',
	LEDGER_PERATOR: '区县台账运维员',
	YKZ: '基础功能-渝快政',
	MAIN_LEDGER: '主要领导',
	PERSON_CHARGE: '科(处)室负责人',
	DATA_EXPORT: '数据导出',
	USER_ADMIN: '管理员',
})
// 用户基础角色
export const USER_BASE_ROLES_ENUM = Object.freeze({
	LEDGER_ONLINE: '台账上下线',
})

// 发布部门级联初始
export const DEPARTMENT_OPTIONS_ENUM = [
	{label: '市级部门', value: 2},
	{label: '区县部门', value: 3, disabled: true},
	{label: '各街镇', value: 4, disabled: true},
]

export const RUNWAY_BLOCK = {
	DDJS: '党的建设',
	JJFZ: '经济发展',
	MSFW: '民生服务',
	PAFZ: '平安法治',
}
