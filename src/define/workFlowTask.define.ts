// 流程-类型定义文件
export enum FlowTaskStatusEnum {
	/**
	 * 进行中
	 */
	PROCESS = 1,
	/**
	 * 已完成
	 */
	FINISH = 2,
	/**
	 * 已暂停
	 */
	STOP = 3,
	/**
	 * 已取消
	 */
	CANCEL = 4,
}
export const FlowTaskStatus = [
	{label: '进行中', value: FlowTaskStatusEnum.PROCESS, color: '#3063c7'},
	{label: '已完成', value: FlowTaskStatusEnum.FINISH, color: '#78bf4a'},
	{label: '已暂停', value: FlowTaskStatusEnum.STOP, color: '#ea3e3c'},
	{label: '已取消', value: FlowTaskStatusEnum.CANCEL, color: '#061626'},
]

export enum FlowExecutionCycleEnum {
	/**
	 * 一次性
	 */
	ONCE = 0,
	/**
	 * 每日一次
	 */
	DAY = 1,
	/**
	 * 每周一次
	 */
	WEEK = 2,
	/**
	 * 每月一次
	 */
	MONTH = 3,
	/**
	 * 每季度一次
	 */
	QUARTER = 4,
	/**
	 * 每半年一次
	 */
	HALFYEAR = 7,

	/**
	 * 每半月一次
	 */
	HALFMONTH = 6,
	/**
	 * 每年一次
	 */
	YEAR = 5,
}
export const FlowExecutionCycle = [
	{
		label: '一次性',
		value: FlowExecutionCycleEnum.ONCE,
	},
	{
		label: '每日一次',
		value: FlowExecutionCycleEnum.DAY,
	},
	{
		label: '每周一次',
		value: FlowExecutionCycleEnum.WEEK,
	},
	{
		label: '每半月一次',
		value: FlowExecutionCycleEnum.HALFMONTH,
	},
	{
		label: '每月一次',
		value: FlowExecutionCycleEnum.MONTH,
	},
	{
		label: '每季度一次',
		value: FlowExecutionCycleEnum.QUARTER,
	},
	{
		label: '每半年一次',
		value: FlowExecutionCycleEnum.HALFYEAR,
	},
	{
		label: '每年一次',
		value: FlowExecutionCycleEnum.YEAR,
	},
]

export enum TaskTypeEnum {
	/**
	 * 报表
	 */
	REPORT = '报表',
	/**
	 * 业务表
	 */
	LEDGER = '业务表',
}
export const TASKTYPE = [
	{
		label: '报表',
		value: TaskTypeEnum.REPORT,
	},
	{
		label: '业务表',
		value: TaskTypeEnum.LEDGER,
	},
]
