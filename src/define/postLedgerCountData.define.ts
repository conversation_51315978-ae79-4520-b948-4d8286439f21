export const data = [
	{
		field: 'totalCount',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'femaleCount',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'XB',
							FieldValue: '女',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'gongzuoshequ',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'SFJZZGZSQ',
							FieldValue: '是',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'shequ',
		maxResultCount: 100,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'CSLX',
							FieldValue: '社区',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'cunshe',
		maxResultCount: 100,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'CSLX',
							FieldValue: '行政村',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'b1',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'XCLX',
							FieldValue: '社区工作者',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'b2',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'XCLX',
							FieldValue: '村干部',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'b3',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'XCLX',
							FieldValue: '镇街临聘人员',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'c1',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'ZHLX',
							FieldValue: '原政法综治网格',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'c2',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'ZHLX',
							FieldValue: '原城市管理网格',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'c3',
		maxResultCount: 100,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					// {
					// 	Key: -1,
					// 	Value: {
					// 		FieldName: 'District',
					// 		FieldValue: name,
					// 		ConditionalType: 0,
					// 		CSharpTypeName: 'string',
					// 	},
					// },
					{
						Key: -1,
						Value: {
							FieldName: 'ZHLX',
							FieldValue: '其他力量',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'd1',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'ZPLX',
							FieldValue: '区级统一招聘',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'd2',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'ZPLX',
							FieldValue: '各镇街自行招聘',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'e',
		maxResultCount: 100,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					// {
					// 	Key: -1,
					// 	Value: {
					// 		FieldName: 'District',
					// 		FieldValue: name,
					// 		ConditionalType: 0,
					// 		CSharpTypeName: 'string',
					// 	},
					// },
					{
						Key: -1,
						Value: {
							FieldName: 'QTLX',
							FieldValue: '其他类型',
							ConditionalType: 12,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f1',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateAvg: ['JBGZY'],
				},
				'JBGZY',
			],
			[
				{
					AggregateAvg: ['GMBXFYY'],
				},
				'GMBXFYY',
			],
			[
				{
					AggregateAvg: ['QTBTY'],
				},
				'QTBTY',
			],
		],

		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},

	{
		field: 'f2',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateAvg: ['JBGZY'],
				},
				'SFZH',
			],
		],

		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f3-1',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'GMBXLB',
							FieldValue: '是',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f3-2',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'YLBX',
							FieldValue: '是',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f3-3',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'SYBX',
							FieldValue: '是',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f3-4',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'GSBX',
							FieldValue: '是',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f3-5',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'SYBX1',
							FieldValue: '是',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f4',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateAvg: ['GMBXFYY'],
				},
				'SFZH',
			],
		],

		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f5',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'SFGMZFGJJ',
							FieldValue: '是',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f6',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateAvg: ['QTBTY'],
				},
				'SFZH',
			],
		],

		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f7-1',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'ZZMM',
							FieldValue: '中共党员',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f7-2',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'ZZMM',
							FieldValue: '共青团员',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f7-3',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'ZZMM',
							FieldValue: '民主党派',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f7-4',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'ZZMM',
							FieldValue: '群众',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f8-1-1',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'NL',
							FieldValue: '30',
							ConditionalType: 5,
							CSharpTypeName: 'int',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f8-1-2',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'NL',
							FieldValue: '30',
							ConditionalType: 5,
							CSharpTypeName: 'int',
						},
					},
					{
						Key: 0,
						Value: {
							FieldName: 'XB',
							FieldValue: '女',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f8-2-1',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'NL',
							FieldValue: '40',
							ConditionalType: 5,
							CSharpTypeName: 'int',
						},
					},
					{
						Key: 0,
						Value: {
							FieldName: 'NL',
							FieldValue: '30',
							ConditionalType: 2,
							CSharpTypeName: 'int',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f8-2-2',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'NL',
							FieldValue: '40',
							ConditionalType: 5,
							CSharpTypeName: 'int',
						},
					},
					{
						Key: 0,
						Value: {
							FieldName: 'NL',
							FieldValue: '30',
							ConditionalType: 2,
							CSharpTypeName: 'int',
						},
					},
					{
						Key: 0,
						Value: {
							FieldName: 'XB',
							FieldValue: '女',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f8-3-1',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'NL',
							FieldValue: '50',
							ConditionalType: 5,
							CSharpTypeName: 'int',
						},
					},
					{
						Key: 0,
						Value: {
							FieldName: 'NL',
							FieldValue: '40',
							ConditionalType: 2,
							CSharpTypeName: 'int',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f8-3-2',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'NL',
							FieldValue: '50',
							ConditionalType: 5,
							CSharpTypeName: 'int',
						},
					},
					{
						Key: 0,
						Value: {
							FieldName: 'NL',
							FieldValue: '40',
							ConditionalType: 2,
							CSharpTypeName: 'int',
						},
					},
					{
						Key: 0,
						Value: {
							FieldName: 'XB',
							FieldValue: '女',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f8-4-1',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'NL',
							FieldValue: '60',
							ConditionalType: 5,
							CSharpTypeName: 'int',
						},
					},
					{
						Key: 0,
						Value: {
							FieldName: 'NL',
							FieldValue: '50',
							ConditionalType: 2,
							CSharpTypeName: 'int',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f8-4-2',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'NL',
							FieldValue: '60',
							ConditionalType: 5,
							CSharpTypeName: 'int',
						},
					},
					{
						Key: 0,
						Value: {
							FieldName: 'NL',
							FieldValue: '50',
							ConditionalType: 2,
							CSharpTypeName: 'int',
						},
					},
					{
						Key: 0,
						Value: {
							FieldName: 'XB',
							FieldValue: '女',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f8-5-1',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'NL',
							FieldValue: '60',
							ConditionalType: 2,
							CSharpTypeName: 'int',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f8-5-2',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'NL',
							FieldValue: '60',
							ConditionalType: 2,
							CSharpTypeName: 'int',
						},
					},
					{
						Key: 0,
						Value: {
							FieldName: 'XB',
							FieldValue: '女',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f9-1',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'XL',
							FieldValue: '初中及以下',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f9-2',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'XL',
							FieldValue: '中专',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f9-3',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'XL',
							FieldValue: '高中（中技）',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f9-4',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'XL',
							FieldValue: '大学专科',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f9-5',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'XL',
							FieldValue: '大学本科',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
	{
		field: 'f9-6',
		maxResultCount: 50,
		skipCount: 0,
		ledgerId: '3a1283a6-1890-4682-79d8-8d39f4cbcf2b',
		selectByModel: [
			['District'],
			[
				{
					AggregateCount: ['{int}:1'],
				},
				'SFZH',
			],
		],
		conditionalByModel: [
			{
				ConditionalList: [
					{
						Key: -1,
						Value: {
							FieldName: 'XL',
							FieldValue: '研究生及以上',
							ConditionalType: 0,
							CSharpTypeName: 'string',
						},
					},
				],
			},
		],
		GroupByModel: [
			{
				FieldName: 'District',
			},
		],
		orderByModel: [
			{
				FieldName: 'District',
			},
		],
	},
]
