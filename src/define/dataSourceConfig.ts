// 数据源配置项类型定义
export interface DataSourceConfigItem {
  id: string
  nameZh: string
  nameEn: string
  category: string
  language: string  // 改为单一语言字段
  creator: string
  createTime: number
}

// 数据源配置项分类
export enum DataSourceCategory {
  CHART = 'chart',
  DATA_PROCESSING = 'data_processing', 
  DATA_LOADING = 'data_loading',
  PERFORMANCE = 'performance',
  SYSTEM = 'system',
  USER_EXPERIENCE = 'user_experience'
}

// 预定义的数据源配置项映射表
export const DATA_SOURCE_CONFIG_MAP: Record<string, { nameZh: string; nameEn: string; category: DataSourceCategory }> = {
  'chart_labels': {
    nameZh: '图表标签',
    nameEn: 'Chart Labels',
    category: DataSourceCategory.CHART
  },
  'chart_axes': {
    nameZh: '图表坐标轴',
    nameEn: 'Chart Axes',
    category: DataSourceCategory.CHART
  },
  'chart_legend': {
    nameZh: '图表图例',
    nameEn: 'Chart Legend',
    category: DataSourceCategory.CHART
  },
  'chart_title': {
    nameZh: '图表标题',
    nameEn: 'Chart Title',
    category: DataSourceCategory.CHART
  },
  'data_format': {
    nameZh: '数据格式',
    nameEn: 'Data Format',
    category: DataSourceCategory.DATA_PROCESSING
  },
  'data_units': {
    nameZh: '数据单位',
    nameEn: 'Data Units',
    category: DataSourceCategory.DATA_PROCESSING
  },
  'data_precision': {
    nameZh: '数据精度',
    nameEn: 'Data Precision',
    category: DataSourceCategory.DATA_PROCESSING
  },
  'rounding': {
    nameZh: '四舍五入',
    nameEn: 'Rounding',
    category: DataSourceCategory.DATA_PROCESSING
  },
  'data_truncation': {
    nameZh: '数据截断',
    nameEn: 'Data Truncation',
    category: DataSourceCategory.DATA_PROCESSING
  },
  'chart_filling': {
    nameZh: '图表填充',
    nameEn: 'Chart Filling',
    category: DataSourceCategory.DATA_PROCESSING
  },
  'chart_deduplication': {
    nameZh: '图表去重',
    nameEn: 'Chart Deduplication',
    category: DataSourceCategory.DATA_PROCESSING
  },
  'data_loading': {
    nameZh: '数据加载',
    nameEn: 'Data Loading',
    category: DataSourceCategory.DATA_LOADING
  },
  'data_caching': {
    nameZh: '数据缓存',
    nameEn: 'Data Caching',
    category: DataSourceCategory.DATA_LOADING
  },
  'loading_progress': {
    nameZh: '数据加载进度',
    nameEn: 'Loading Progress',
    category: DataSourceCategory.DATA_LOADING
  },
  'loading_timeout': {
    nameZh: '数据加载超时',
    nameEn: 'Loading Timeout',
    category: DataSourceCategory.DATA_LOADING
  },
  'visualization_performance': {
    nameZh: '可视化性能',
    nameEn: 'Visualization Performance',
    category: DataSourceCategory.PERFORMANCE
  },
  'response_speed': {
    nameZh: '可视化响应速度',
    nameEn: 'Response Speed',
    category: DataSourceCategory.PERFORMANCE
  },
  'rendering_speed': {
    nameZh: '渲染速度',
    nameEn: 'Rendering Speed',
    category: DataSourceCategory.PERFORMANCE
  },
  'interaction_speed': {
    nameZh: '交互速度',
    nameEn: 'Interaction Speed',
    category: DataSourceCategory.PERFORMANCE
  },
  'stability': {
    nameZh: '稳定性',
    nameEn: 'Stability',
    category: DataSourceCategory.SYSTEM
  },
  'compatibility': {
    nameZh: '兼容性',
    nameEn: 'Compatibility',
    category: DataSourceCategory.SYSTEM
  },
  'scalability': {
    nameZh: '可扩展性',
    nameEn: 'Scalability',
    category: DataSourceCategory.SYSTEM
  },
  'customization': {
    nameZh: '定制化',
    nameEn: 'Customization',
    category: DataSourceCategory.SYSTEM
  },
  'personalization': {
    nameZh: '个性化',
    nameEn: 'Personalization',
    category: DataSourceCategory.SYSTEM
  },
  'user_experience': {
    nameZh: '用户体验',
    nameEn: 'User Experience',
    category: DataSourceCategory.USER_EXPERIENCE
  },
  'interface_aesthetics': {
    nameZh: '界面美观',
    nameEn: 'Interface Aesthetics',
    category: DataSourceCategory.USER_EXPERIENCE
  },
  'ease_of_operation': {
    nameZh: '操作便捷性',
    nameEn: 'Ease of Operation',
    category: DataSourceCategory.USER_EXPERIENCE
  },
  'multi_language_support': {
    nameZh: '多语言支持',
    nameEn: 'Multi-language Support',
    category: DataSourceCategory.USER_EXPERIENCE
  }
}

// 分类标签映射
export const CATEGORY_LABELS: Record<DataSourceCategory, { zh: string; en: string }> = {
  [DataSourceCategory.CHART]: { zh: '图表相关', en: 'Chart Related' },
  [DataSourceCategory.DATA_PROCESSING]: { zh: '数据处理', en: 'Data Processing' },
  [DataSourceCategory.DATA_LOADING]: { zh: '数据加载', en: 'Data Loading' },
  [DataSourceCategory.PERFORMANCE]: { zh: '性能相关', en: 'Performance' },
  [DataSourceCategory.SYSTEM]: { zh: '系统特性', en: 'System Features' },
  [DataSourceCategory.USER_EXPERIENCE]: { zh: '用户体验', en: 'User Experience' }
}

// 获取配置项的显示名称
export const getConfigItemName = (key: string, language: 'zh' | 'en'): string => {
  const config = DATA_SOURCE_CONFIG_MAP[key]
  if (!config) return key
  return language === 'zh' ? config.nameZh : config.nameEn
}

// 获取分类显示名称
export const getCategoryName = (category: DataSourceCategory, language: 'zh' | 'en'): string => {
  const label = CATEGORY_LABELS[category]
  if (!label) return category
  return language === 'zh' ? label.zh : label.en
}
