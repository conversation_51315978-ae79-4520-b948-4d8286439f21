export enum SystematicTypeEnum {
	Municipal = 1, // 市级标准
	Street = 2, // 街镇体系
	Application = 3, // 六大应用体系
}

export const SystematicTypes = [
	{label: '市级标准', value: SystematicTypeEnum.Municipal},
	{label: '街镇体系', value: SystematicTypeEnum.Street},
	{label: '六大应用体系', value: SystematicTypeEnum.Application},
]

export enum SubSystematicTypeEnum {
	Block = 1, // 板块
	Road = 2, // 跑道
	SubRoad = 3, //子跑道
	SubRoadNext = 4, // 子跑道下一级, 二级子跑道
}
