/**
 * 子任务属性数据模型定义
 * Subtask Attribute Data Model Definitions
 */

export interface SubtaskAttribute {
  /** 唯一标识符 */
  id: string
  /** 子任务属性名称 */
  attributeName: string
  /** 子任务类型 */
  taskType: string
  /** 属性类型 */
  attributeType: string
  /** 属性值 */
  attributeValue: number
  /** 紧急程度 */
  urgencyLevel: string
  /** 风险等级 */
  riskLevel: string
  /** 重要程度 */
  importanceLevel: string
  /** 状态 */
  status: string
  /** 最后修改时间 */
  lastModified: string
  /** 修改人 */
  modifier: string
}

export interface SubtaskAttributeQuery {
  /** 搜索关键词 */
  keyword?: string
  /** 任务类型筛选 */
  taskType?: string
  /** 属性类型筛选 */
  attributeType?: string
  /** 紧急程度筛选 */
  urgencyLevel?: string
  /** 风险等级筛选 */
  riskLevel?: string
  /** 重要程度筛选 */
  importanceLevel?: string
  /** 状态筛选 */
  status?: string
  /** 页码 */
  page?: number
  /** 每页数量 */
  pageSize?: number
}

export interface SubtaskAttributeOperation {
  /** 操作类型 */
  type: 'adjust' | 'maintain' | 'more'
  /** 操作标签 */
  label: string
  /** 操作图标 */
  icon?: string
  /** 是否禁用 */
  disabled?: boolean
}

export interface SubtaskAttributeTab {
  /** 标签键 */
  key: string
  /** 标签名称 */
  label: string
  /** 是否激活 */
  active?: boolean
}

// 枚举定义
export enum UrgencyLevel {
  URGENT = '特急',
  HIGH = '紧急',
  MEDIUM = '一般',
  LOW = '不急'
}

export enum RiskLevel {
  HIGH = '高风险',
  MEDIUM = '中风险',
  LOW = '低风险'
}

export enum ImportanceLevel {
  CRITICAL = '非常重要',
  IMPORTANT = '重要',
  MEDIUM = '一般',
  LOW = '不重要'
}

export enum TaskStatus {
  IN_PROGRESS = '进行中',
  COMPLETED = '已完成',
  PENDING = '待处理',
  CANCELLED = '已取消'
}

export enum TaskType {
  BUSINESS_REPORT = '业务报表',
  DATA_COLLECTION = '数据采集',
  WORKFLOW_APPROVAL = '流程审批',
  SYSTEM_MAINTENANCE = '系统维护',
  QUALITY_CONTROL = '质量控制'
}

export enum AttributeType {
  PARTY_BUILDING = '党的建设',
  ADMINISTRATIVE = '行政管理',
  PUBLIC_SERVICE = '公共服务',
  ECONOMIC_DEVELOPMENT = '经济发展',
  SOCIAL_GOVERNANCE = '社会治理',
  ENVIRONMENTAL_PROTECTION = '环境保护'
}