import {dayjs} from 'element-plus'
export enum periodEnum {
	/** 一次性填报 */
	once = 1,
	/** 每月一次 */
	month = 2,
	/** 每季度一次 */
	quarter = 3,
	/** 每年一次 */
	year = 4,
	/** 每日一次 */
	day = 5,
	/** 每周一次 */
	week = 6,
	/** 每半月一次 */
	halfMonth = 7,
	/** 每半年一次 */
	halfYear = 8,
}
export enum PlanTaskStatus {
	/**
	 * 已创建
	 */
	Created = 1,
	/**
	 * 待提交审核
	 */
	PendingAudit = 2,
	/**
	 * 进行中
	 */
	Processing = 3,
	/**
	 * 已驳回
	 */
	Rejected = 4,
	/**
	 * 已完成
	 */
	Completed = 5,
	/**
	 * 待分管领导审核
	 */
	InChargeLeaderPendingAudit = 6,
	/**
	 * 待数据领导审核
	 */
	DataLeaderPendingAudit = 7,
	/**
	 * 已终止
	 */
	Stope = 8,
	/**
	 * 已完成（数据领导填报完毕
	 */
	DataLeaderCompleted = 9,
	/**
	 * 时间到期 终止任务
	 */
	StopeDate = 10,
	/**
	 * 已撤回
	 */
	Revoke = 11,
	/**
	 * 已删除
	 */
	IsDelete = 12,
}
export const periodList = [
	{label: '一次性', value: periodEnum.once, color: '#3b818c'},
	{label: '每月一次', value: periodEnum.month, color: '#5cb3cc'},
	{label: '每季度一次', value: periodEnum.quarter, color: '#8abcd1'},
	{label: '每年一次', value: periodEnum.year, color: '#2983bb'},
	{label: '每日一次', value: periodEnum.day, color: '#134857'},
	{label: '每周一次', value: periodEnum.week, color: '#1a94bc'},
	{label: '每半月一次', value: periodEnum.halfMonth, color: '#158bb8'},
	{label: '每半年一次', value: periodEnum.halfYear, color: '#1177b0'},
]
export const StatusSelect = [
	{label: '待提交', value: 1},
	{label: '待审核', value: 2},
	{label: '填报中', value: 3},
	{label: '已驳回', value: 4},
	{label: '已终止', value: 8},
	{label: '已完结', value: 5},
]

export enum ReportsFlowStatusEnum {
	// 计划任务
	Created = 1, // 已创建
	PendingAudit = 2, // 待提交审核
	Processing = 3, // 进行中
	Rejected = 4, // 已驳回
	Completed = 5, // 已完成
	InChargeLeaderPendingAudit = 6, // 分管领导审核
	DataLeaderPendingAudit = 7, // 数据领导审核
	PlantTaskStope = 8, // 手动终止任务
	StopeDate = 10, // 时间到期终止任务
	Revoke = 11, // 撤回
	IsDeleted = 12, // 已删除
	PendingReview = 13, // 待审核
	PendingExecution = 14, // 待执行
	Executing = 15, // 执行中
	Expiration = 16, // 已到期
	Terminated = 17, // 已终止

	// 我转发的状态
	TurnWaiting = 0, // 待填报
	TurnWaitingForAudit = 1, // 数据待审核
	TurnAudited = 2, // 填报已审核
	TurnRejected = 3, // 内部审核已驳回
	TurnIssued = 4, // 已下发
	TurnWaitingForIssueAudit = 5, // 提交待审核
	TurnIssueRejected = 6, // 下发申请已驳回
	TurnArchived = 7, // 已归档
	TurnDataLeaderPendingAudit = 8, // 数据领导待处理
	TurnInnerAudit = 9, // 内部审核
	TurnInnerWrite = 10, // 内部转发待填写
	TurnStope = 11, // 任务终止
	TurnDataLeaderAuditRejected = 12, // 数据领导提交报表被驳回
	TurnIssuedArchived = 13, // 下发已归档
	TurnInternalArchived = 14, // 内部下发已归档
	TurnTranspondRejected = 15, // 数据领导转发审核流程被驳回
	TurnSubmitForReview = 16, // 提交待审核
	TurnDataAudited = 17, // 数据待审核
	TurnTranspondAudit = 18, // 转发审核

	// 任务填报
	Waiting = 0, // 待填报
	WaitingForAudit = 1, // 数据待审核
	Audited = 2, // 填报已审核
	RejectedTask = 3, // 内部审核已驳回
	Issued = 4, // 已下发
	WaitingForIssueAudit = 5, // 申请下发待审核
	IssueRejected = 6, // 下发申请已驳回
	Archived = 7, // 已归档
	DataLeaderPendingAuditTask = 8, // 数据领导待处理
	InnerAudit = 9, // 内部审核
	InnerWrite = 10, // 内部转发待填写
	StopeTask = 11, // 任务终止
	DataLeaderAuditRejected = 12, // 领导审核下级数据驳回
	IssuedArchived = 13, // 下发已归档
	InternalArchived = 14, // 内部下发已归档
	TranspondRejected = 15, // 数据领导转发审核流程被驳回
	SubmitForReview = 16, // 数据审核完成
	DataAudited = 17, // 数据待审核
	TranspondAudit = 18, // 转发审核

	// 新任务填报
	FillReportPendingProcessing = 1, // 待处理
	FillReportRejected = 2, // 已驳回
	FillReportStoped = 3, // 已终止
	FillReportCompleted = 4, // 已完成
}

export const ReportsFlowStatus = [
	{label: '已创建', value: ReportsFlowStatusEnum.Created, type: 'plan'},
	{label: '待提交', value: ReportsFlowStatusEnum.PendingAudit, type: 'plan'},
	{label: '进行中', value: ReportsFlowStatusEnum.Processing, type: 'plan', enable: true},
	{label: '已驳回', value: ReportsFlowStatusEnum.Rejected, type: 'plan', enable: true},
	{label: '已完成', value: ReportsFlowStatusEnum.Completed, type: 'plan', enable: true},
	{label: '分管领导审核', value: ReportsFlowStatusEnum.InChargeLeaderPendingAudit, type: 'plan'},
	{label: '数据领导审核', value: ReportsFlowStatusEnum.DataLeaderPendingAudit, type: 'plan'},
	{label: '已终止', value: ReportsFlowStatusEnum.PlantTaskStope, type: 'plan', enable: true},
	{label: '已终止', value: ReportsFlowStatusEnum.StopeDate, type: 'plan'},
	{label: '已撤回', value: ReportsFlowStatusEnum.Revoke, type: 'plan', enable: true},
	{label: '已删除', value: ReportsFlowStatusEnum.IsDeleted, type: 'plan'},
	{label: '待审核', value: ReportsFlowStatusEnum.PendingReview, type: 'plan', enable: true},
	{label: '待执行', value: ReportsFlowStatusEnum.PendingExecution, type: 'plan', enable: true},
	{label: '执行中', value: ReportsFlowStatusEnum.Executing, type: 'plan', enable: true},
	{label: '已过期', value: ReportsFlowStatusEnum.Expiration, type: 'plan'},
	{label: '已终止', value: ReportsFlowStatusEnum.Terminated, type: 'plan'},

	// 我的转发
	{label: '待填报', value: ReportsFlowStatusEnum.TurnWaiting, type: 'turn'},
	{label: '待审核', value: ReportsFlowStatusEnum.TurnWaitingForAudit, type: 'turn'},
	{label: '已审核', value: ReportsFlowStatusEnum.TurnAudited, type: 'turn'},
	{label: '已驳回', value: ReportsFlowStatusEnum.TurnRejected, type: 'turn'},
	{label: '已下发', value: ReportsFlowStatusEnum.TurnIssued, type: 'turn'},
	{label: '待审核', value: ReportsFlowStatusEnum.TurnWaitingForIssueAudit, type: 'turn'},
	{label: '已驳回', value: ReportsFlowStatusEnum.TurnIssueRejected, type: 'turn'},
	{label: '已归档', value: ReportsFlowStatusEnum.TurnArchived, type: 'turn'},
	{label: '待处理', value: ReportsFlowStatusEnum.TurnDataLeaderPendingAudit, type: 'turn'},
	{label: '待审核', value: ReportsFlowStatusEnum.TurnInnerAudit, type: 'turn'},
	{label: '待填写', value: ReportsFlowStatusEnum.TurnInnerWrite, type: 'turn'},
	{label: '已终止', value: ReportsFlowStatusEnum.TurnStope, type: 'turn'},
	{label: '已驳回', value: ReportsFlowStatusEnum.TurnDataLeaderAuditRejected, type: 'turn'},
	{label: '已归档', value: ReportsFlowStatusEnum.TurnIssuedArchived, type: 'turn'},
	{label: '已归档', value: ReportsFlowStatusEnum.TurnInternalArchived, type: 'turn'},
	{label: '已驳回', value: ReportsFlowStatusEnum.TurnTranspondRejected, type: 'turn'},
	{label: '待审核', value: ReportsFlowStatusEnum.TurnSubmitForReview, type: 'turn'},
	{label: '待审核', value: ReportsFlowStatusEnum.TurnDataAudited, type: 'turn'},
	{label: '待审核', value: ReportsFlowStatusEnum.TurnTranspondAudit, type: 'turn'},

	// 任务填报
	{label: '待填报', value: ReportsFlowStatusEnum.Waiting, type: 'task'},
	{label: '待审核', value: ReportsFlowStatusEnum.WaitingForAudit, type: 'task'},
	{label: '已审核', value: ReportsFlowStatusEnum.Audited, type: 'task'},
	{label: '已驳回', value: ReportsFlowStatusEnum.RejectedTask, type: 'task'},
	{label: '已下发', value: ReportsFlowStatusEnum.Issued, type: 'task'},
	{label: '待审核', value: ReportsFlowStatusEnum.WaitingForIssueAudit, type: 'task'},
	{label: '已驳回', value: ReportsFlowStatusEnum.IssueRejected, type: 'task'},
	{label: '已归档', value: ReportsFlowStatusEnum.Archived, type: 'task'},
	{label: '待处理', value: ReportsFlowStatusEnum.DataLeaderPendingAuditTask, type: 'task'},
	{label: '内部审核', value: ReportsFlowStatusEnum.InnerAudit, type: 'task'},
	{label: '内部填写', value: ReportsFlowStatusEnum.InnerWrite, type: 'task'},
	{label: '已终止', value: ReportsFlowStatusEnum.StopeTask, type: 'task'},
	{label: '已驳回', value: ReportsFlowStatusEnum.DataLeaderAuditRejected, type: 'task'},
	{label: '已归档', value: ReportsFlowStatusEnum.IssuedArchived, type: 'task'},
	{label: '已归档', value: ReportsFlowStatusEnum.InternalArchived, type: 'task'},
	{label: '已驳回', value: ReportsFlowStatusEnum.TranspondRejected, type: 'task'},
	{label: '待审核', value: ReportsFlowStatusEnum.SubmitForReview, type: 'task'},
	{label: '待审核', value: ReportsFlowStatusEnum.DataAudited, type: 'task'},
	{label: '待审核', value: ReportsFlowStatusEnum.TranspondAudit, type: 'task'},

	// 新任务填报
	{label: '待处理', value: ReportsFlowStatusEnum.FillReportPendingProcessing, type: 'fillReport'},
	{label: '已驳回', value: ReportsFlowStatusEnum.FillReportRejected, type: 'fillReport'},
	{label: '已终止', value: ReportsFlowStatusEnum.FillReportStoped, type: 'fillReport'},
	{label: '已完成', value: ReportsFlowStatusEnum.FillReportCompleted, type: 'fillReport'},
]

export const ReportsFlowStatusType = (status: number, type: string = 'plan') => {
	if (type === 'plan') {
		switch (status) {
			case ReportsFlowStatusEnum.PendingAudit:
			case ReportsFlowStatusEnum.InChargeLeaderPendingAudit:
			case ReportsFlowStatusEnum.DataLeaderPendingAudit:
			case ReportsFlowStatusEnum.PendingReview:
			case ReportsFlowStatusEnum.PendingExecution:
				return 'primary'
			case ReportsFlowStatusEnum.Processing:
			case ReportsFlowStatusEnum.Executing:
				return 'warning'
			case ReportsFlowStatusEnum.Rejected:
			case ReportsFlowStatusEnum.PlantTaskStope:
			case ReportsFlowStatusEnum.StopeDate:
			case ReportsFlowStatusEnum.Revoke:
			case ReportsFlowStatusEnum.IsDeleted:
			case ReportsFlowStatusEnum.Expiration:
			case ReportsFlowStatusEnum.Terminated:
				return 'danger'
			case ReportsFlowStatusEnum.Created:
			case ReportsFlowStatusEnum.Completed:
				return 'success'
			default:
				return 'info'
		}
	} else if (type === 'task') {
		switch (status) {
			case ReportsFlowStatusEnum.Waiting:
			case ReportsFlowStatusEnum.WaitingForAudit:
			case ReportsFlowStatusEnum.WaitingForIssueAudit:
			case ReportsFlowStatusEnum.DataLeaderPendingAuditTask:
			case ReportsFlowStatusEnum.InnerAudit:
			case ReportsFlowStatusEnum.InnerWrite:
			case ReportsFlowStatusEnum.SubmitForReview:
			case ReportsFlowStatusEnum.TranspondAudit:
			case ReportsFlowStatusEnum.DataAudited:
				return 'primary'
			case ReportsFlowStatusEnum.RejectedTask:
			case ReportsFlowStatusEnum.IssueRejected:
			case ReportsFlowStatusEnum.StopeTask:
			case ReportsFlowStatusEnum.DataLeaderAuditRejected:
			case ReportsFlowStatusEnum.TranspondRejected:
				return 'danger'
			case ReportsFlowStatusEnum.Issued:
			case ReportsFlowStatusEnum.Audited:
			case ReportsFlowStatusEnum.Archived:
			case ReportsFlowStatusEnum.IssuedArchived:
			case ReportsFlowStatusEnum.InternalArchived:
				return 'success'
			default:
				return 'info'
		}
	} else if (type === 'turn') {
		switch (status) {
			case ReportsFlowStatusEnum.TurnWaiting:
			case ReportsFlowStatusEnum.TurnWaitingForAudit:
			case ReportsFlowStatusEnum.TurnWaitingForIssueAudit:
			case ReportsFlowStatusEnum.TurnDataLeaderPendingAudit:
			case ReportsFlowStatusEnum.TurnInnerAudit:
			case ReportsFlowStatusEnum.TurnInnerWrite:
			case ReportsFlowStatusEnum.TurnSubmitForReview:
			case ReportsFlowStatusEnum.TurnDataAudited:
			case ReportsFlowStatusEnum.TurnTranspondAudit:
				return 'primary'
			case ReportsFlowStatusEnum.TurnRejected:
			case ReportsFlowStatusEnum.TurnIssueRejected:
			case ReportsFlowStatusEnum.TurnStope:
			case ReportsFlowStatusEnum.TurnDataLeaderAuditRejected:
			case ReportsFlowStatusEnum.TurnTranspondRejected:
				return 'danger'
			case ReportsFlowStatusEnum.TurnAudited:
			case ReportsFlowStatusEnum.TurnIssued:
			case ReportsFlowStatusEnum.TurnArchived:
			case ReportsFlowStatusEnum.TurnIssuedArchived:
			case ReportsFlowStatusEnum.TurnInternalArchived:
				return 'success'
			default:
				return 'info'
		}
	} else if (type === 'fillReport') {
		switch (status) {
			case ReportsFlowStatusEnum.FillReportPendingProcessing:
				return 'primary'
			case ReportsFlowStatusEnum.FillReportRejected:
			case ReportsFlowStatusEnum.FillReportStoped:
				return 'danger'
			case ReportsFlowStatusEnum.FillReportCompleted:
				return 'success'
			default:
				return 'primary'
		}
	}
}

export const ReportTaskStatusSelect = [
	{label: '待处理', value: 8},
	{label: '内部待填写', value: 10},
	{label: '内部待审核', value: 9},
	{label: '已下发', value: 4},
	{label: '已驳回', value: 3},
	{label: '已终止', value: 11},
]
export const statusTrans = [
	{name: '待提交', index: PlanTaskStatus.Created, color: '#d2d4d5', class: 'el-button--plain'}, // 已创建
	{
		name: '待审核',
		index: PlanTaskStatus.PendingAudit,
		color: '#e7c085',
		class: 'el-button--info',
	}, // 待提交
	{
		name: '填报中',
		index: PlanTaskStatus.Processing,
		color: '#00a5de',
		class: 'el-button--warning',
	}, // 进行中
	{
		name: '审核驳回',
		index: PlanTaskStatus.Rejected,
		color: '#ff0000',
		class: 'el-button--danger',
	},
	{
		name: '已完结',
		index: PlanTaskStatus.Completed,
		color: '#00c319',
		class: 'el-button--success',
	},
	{
		name: '待审核',
		index: PlanTaskStatus.InChargeLeaderPendingAudit,
		color: '#e7c085',
		class: 'el-button--info',
	}, // 分管领导待审核
	{
		name: '待审核',
		index: PlanTaskStatus.DataLeaderPendingAudit,
		color: '#e7c085',
		class: 'el-button--info',
	}, // 数据领导待审核
	{name: '已终止', index: PlanTaskStatus.Stope, color: '#ff0000', class: 'el-button--danger'},
	{
		name: '已完结',
		index: PlanTaskStatus.DataLeaderCompleted,
		color: '#00c319',
		class: 'el-button--success',
	},
	{name: '已终止', index: PlanTaskStatus.StopeDate, color: '#ff0000', class: 'el-button--danger'},
	{name: '已撤回', index: PlanTaskStatus.Revoke, color: 'gray', class: 'el-button--info'},
	{name: '已删除', index: PlanTaskStatus.IsDelete, color: 'red', class: 'el-button--danger'},
]
export const reportStatusTrans: any = [
	{name: '待填报', value: 0, class: 'el-button--warning', enable: true},
	{name: '待审核', value: 1, class: 'el-button--warning', enable: true},
	{name: '已审核', value: 2, class: 'el-button--success'},
	{name: '已驳回', value: 3, class: 'el-button--danger', enable: true},
	{name: '已下发', value: 4, class: 'el-button--success', enable: true},
	{name: '下发待审核', value: 5, class: 'el-button--info'},
	{name: '下发已驳回', value: 6, class: 'el-button--danger'},
	{name: '已归档', value: 7, class: 'el-button--info'},
	{name: '待处理', value: 8, class: 'el-button--warning'},
	{name: '内部待审核', value: 9, class: 'el-button--warning'},
	{name: '内部待填写', value: 10, class: 'el-button--warning'}, // 内部转发待填写
	{name: '已终止', value: 11, class: 'el-button--danger'},
	{name: '已驳回', value: 12, class: 'el-button--danger'}, // 分管领导审核下级提交上来的数据驳回
	{name: '下发已审核', value: 13, class: 'el-button--success'},
	{name: '已归档', value: 14, class: 'el-button--info'},
]
export const week = [
	{name: '周一', value: dayjs().day(1).format('YYYY-MM-DD')},
	{name: '周二', value: dayjs().day(2).format('YYYY-MM-DD')},
	{name: '周三', value: dayjs().day(3).format('YYYY-MM-DD')},
	{name: '周四', value: dayjs().day(4).format('YYYY-MM-DD')},
	{name: '周五', value: dayjs().day(5).format('YYYY-MM-DD')},
	{name: '周六', value: dayjs().day(6).format('YYYY-MM-DD')},
	{name: '周日', value: dayjs().day(0).add(7, 'day').format('YYYY-MM-DD')},
]

export enum FIlterMode {
	/**
	 * 自己填报的
	 */
	Self = 0,
	/**
	 * 下级提交数据待审核-干事员(工作人员)审核
	 */
	WaitingForAudit = 1,
	/**
	 * 待填写
	 */
	PendingWrite = 2,
	/**
	 * 申请下发待审核
	 */
	WaitingForIssurAudit = 3,
	/**
	 * 已归档
	 */
	Archived = 4,
	/**
	 * 自己审核的数据
	 */
	SelfAudited = 5,
	/**
	 * 我的已办
	 */
	MyDone = 6,
	/**
	 * 提交记录
	 */
	SubmitRecord = 7,
	/**
	 * 数据领导审核
	 */
	DataLeaderIssureAudit = 8,
	/**
	 * 分管领导审核
	 */
	InChargeLeaderPendingAudit = 9,
	/**
	 * 工作人员
	 */
	Worker = 10,
}

export const reportTastStatus = [
	{name: '进行中', color: '#00a5de'},
	{name: '已完成', color: '#00c319'},
	{name: '已取消', color: '#d2d4d5'},
	{name: '已终止', color: '#e53736'},
]
// 获取当月第几周
function getMonthWeek(date) {
	const currentDay = new Date(date)
	const theSaturday = currentDay.getDate() + (6 - currentDay.getDay())
	return Math.ceil(theSaturday / 7)
}
// 阿拉伯数字转汉字
function numberConvertToUppercase(num: any) {
	num = Number(num)
	var upperCaseNumber = [
		'零',
		'一',
		'二',
		'三',
		'四',
		'五',
		'六',
		'七',
		'八',
		'九',
		'十',
		'百',
		'千',
		'万',
		'亿',
	]
	var length = String(num).length
	if (length == 1) {
		return upperCaseNumber[num]
	} else if (length == 2) {
		if (num == 10) {
			return upperCaseNumber[num]
		} else if (num > 10 && num < 20) {
			return '十' + upperCaseNumber[String(num).charAt(1)]
		} else {
			return (
				upperCaseNumber[String(num).charAt(0)] +
				'十' +
				upperCaseNumber[String(num).charAt(1)].replace('零', '')
			)
		}
	}
}
/**
 * @description 获取批次
 * @param period number
 * @param date string | Date
 * @returns
 */
export function GetBatch(period: number, date: string) {
	const year = dayjs(date).year()
	const month = dayjs(date).month() + 1

	switch (period) {
		case 1:
			return '一次性任务'
		case 2:
			return `${year}${month >= 10 ? month : '0' + month}批次`
		case 3:
			const quarter = Math.floor((month - 1) / 3) + 1
			return `${year}第${quarter}季度批次`
		case 4:
			return `${year}批次`
		case 5:
			return `${dayjs(date).format('YYYYMMDD')}批次`
		case 6:
			return `${dayjs(date).format('YYYYMM')}第${numberConvertToUppercase(
				getMonthWeek(date)
			)}周`
		case 7:
			console.log(date)

			return `${dayjs(date).format('YYYYMM')}${
				dayjs(date).isBefore(dayjs().format('YYYY-MM-15')) ? '上' : '下'
			}`
		case 8:
			return `${dayjs(date).format('YYYY')}${
				dayjs(date).isBefore(dayjs().format('YYYY-07-01')) ? '上' : '下'
			}`
		default:
			return '未知批次'
	}
}

// 获取当前季度有多少天
function getCurrentQuarterDays() {
	const now = new Date()
	const quarter = Math.floor(now.getMonth() / 3) + 1
	const startOfQuarter = new Date(now.getFullYear(), (quarter - 1) * 3, 1)
	const endOfQuarter = new Date(now.getFullYear(), quarter * 3, 0)
	const diff = (Number(endOfQuarter) - Number(startOfQuarter)) / (1000 * 60 * 60 * 24) + 1
	return diff
}
// 获取每个周期的剩余时间
export function surplusDate(type: number, endDate: any, onceDate: string) {
	if (
		(endDate === undefined || endDate.length === 0) &&
		type !== periodEnum.once &&
		type !== periodEnum.day
	) {
		return '-'
	}
	// console.log(type)

	switch (type) {
		case periodEnum.once:
			if (dayjs().isBefore(dayjs(onceDate))) {
				return dayjs(onceDate).diff(dayjs(), 'days') + 1
			} else {
				return false
			}
		case periodEnum.month:
			const everyDay = dayjs(dayjs().format('YYYY-MM') + '-' + endDate[0].split('-')[2])
			if (dayjs().isBefore(everyDay)) {
				return dayjs(everyDay).diff(dayjs(), 'days') + 1
			} else return false
		case periodEnum.quarter:
			// 获取每周期第一季度
			const quarter1 = dayjs(
				dayjs().format('YYYY') + endDate[0].split('-').slice(1, 3).join('-')
			).format('YYYY-MM-DD')
			// 获取每周期第二季度
			const quarter2 = dayjs(
				dayjs().format('YYYY') + endDate[1].split('-').slice(1, 3).join('-')
			).format('YYYY-MM-DD')
			// 获取每周期第三季度
			const quarter3 = dayjs(
				dayjs().format('YYYY') + endDate[2].split('-').slice(1, 3).join('-')
			).format('YYYY-MM-DD')
			// 获取每周期第四季度
			const quarter4 = dayjs(
				dayjs().format('YYYY') + endDate[3].split('-').slice(1, 3).join('-')
			).format('YYYY-MM-DD')
			// 判断第一季度
			if (dayjs().isAfter(dayjs().startOf('month')) && dayjs().isBefore(quarter1))
				return dayjs(quarter1).diff(dayjs(), 'days') + 1
			// 判断第二季度
			if (dayjs().isAfter(dayjs().format('YYYY-04-01')) && dayjs().isBefore(quarter2))
				return dayjs(quarter2).diff(dayjs(), 'days') + 1
			// 判断第三季度
			if (dayjs().isAfter(dayjs().format('YYYY-07-01')) && dayjs().isBefore(quarter3))
				return dayjs(quarter3).diff(dayjs(), 'days') + 1
			// 判断第四季度
			if (dayjs().isAfter(dayjs().format('YYYY-10-01')) && dayjs().isBefore(quarter4))
				return dayjs(quarter4).diff(dayjs(), 'days') + 1
			// 判断第一季度是否超期
			if (dayjs().isBefore(dayjs().format('YYYY-04-01')) && dayjs().isAfter(quarter1))
				return false
			// 判断第二季度是否超期
			if (dayjs().isBefore(dayjs().format('YYYY-07-01')) && dayjs().isAfter(quarter2))
				return false
			// 判断第三季度是否超期
			if (dayjs().isBefore(dayjs().format('YYYY-10-01')) && dayjs().isAfter(quarter3))
				return false
			// 判断第四季度是否超期
			if (dayjs().isBefore(dayjs().format('YYYY-12-31')) && dayjs().isAfter(quarter4))
				return false
		case periodEnum.year:
			const everyYear = dayjs(
				dayjs().format('YYYY') + endDate[0].split('-').slice(1, 3).join('-')
			)
			if (dayjs().isBefore(everyYear)) {
				return dayjs(everyYear).diff(dayjs(), 'days') + 1
			} else {
				return false
			}
		case periodEnum.day:
			console.log(onceDate)

			if (dayjs().isBefore(dayjs(onceDate))) return '1'
			else return false
		case periodEnum.week:
			const week = Number(endDate[0])
			const weekEndDate = Number.isNaN(week)
				? dayjs(endDate[0]).format('YYYY-MM-DD')
				: week === 0
				? dayjs().day(0).add(7, 'day').format('YYYY-MM-DD')
				: dayjs().day(week).format('YYYY-MM-DD')
			console.log(weekEndDate)

			if (dayjs().isBefore(dayjs(weekEndDate).format('YYYY-MM-DD 23:59:59')))
				return (
					dayjs(dayjs(weekEndDate).format('YYYY-MM-DD 23:59:59')).diff(dayjs(), 'days') +
					1
				)
			else return false
		// return
		// return '周'
		case periodEnum.halfMonth:
			const tophalf = dayjs(dayjs().format('YYYY-MM') + '-' + endDate[0].split('-')[2])
			const bottomhalf = dayjs(dayjs().format('YYYY-MM') + '-' + endDate[1].split('-')[2])
			// 判断上半月
			if (dayjs().isBefore(tophalf) && dayjs().isAfter(dayjs().startOf('month')))
				return dayjs(tophalf).diff(dayjs(), 'days') + 1
			// 判断上半月是否超期
			if (dayjs().isBefore(dayjs().format('YYYY-MM-15')) && dayjs().isAfter(tophalf))
				return false
			// 判断下半月
			if (dayjs().isAfter(dayjs().format('YYYY-MM-15')) && dayjs().isBefore(bottomhalf))
				return dayjs(bottomhalf).diff(dayjs(), 'days') + 1
			// 判断下半月是否超期
			if (dayjs().isBefore(dayjs().endOf('month')) && dayjs().isAfter(bottomhalf))
				return false
		case periodEnum.halfYear:
			const tophalfYear = dayjs(
				dayjs().format('YYYY') + endDate[0].split('-').slice(1, 3).join('-')
			)
			const bottomhalfYear = dayjs(
				dayjs().format('YYYY') + endDate[1].split('-').slice(1, 3).join('-')
			)
			// 判断上半年
			if (dayjs().isBefore(tophalfYear) && dayjs().isAfter(dayjs().startOf('year')))
				return dayjs(tophalfYear).diff(dayjs(), 'days') + 1
			// 判断下半年
			if (dayjs().isAfter(dayjs().format('YYYY-07-01')) && dayjs().isBefore(bottomhalfYear))
				return dayjs(bottomhalfYear).diff(dayjs(), 'days') + 1
			// 判断上半年是否超期
			if (dayjs().isBefore(dayjs().format('YYYY-07-01')) && dayjs().isAfter(tophalfYear))
				return false
			// 判断下半年是否超期
			if (dayjs().isBefore(dayjs().endOf('year')) && dayjs().isAfter(bottomhalfYear))
				return false
	}
}

// 工作待办类型枚举

export enum WorkTodoTypeEnum {
	/** 业务表数据待审核 */
	LedgerDataProgress = 1,
	/** 报表下发待初审 */
	ReportDistributeStart = 2,
	/** 报表下发待终审 */
	ReportDistributeEnd = 3,
	/** 报表待填报 */
	ReportFilling = 4,
	/** 报表数据待审核 */
	ReportDataProgress = 5,
	/** 业务表无数据审核提交 */
	LedgerNoDataAudit = 6,
}

export const ReportLedgerType = [
	{label: '业务表数据待审核', value: WorkTodoTypeEnum.LedgerDataProgress},
	{label: '报表下发待初审', value: WorkTodoTypeEnum.ReportDistributeStart},
	{label: '报表下发待终审', value: WorkTodoTypeEnum.ReportDistributeEnd},
	{label: '报表待填报', value: WorkTodoTypeEnum.ReportFilling},
	{label: '报表数据待审核', value: WorkTodoTypeEnum.ReportDataProgress},
	{label: '业务表无更新确认', value: WorkTodoTypeEnum.LedgerNoDataAudit},
]

// 多人填报提交状态
export enum FillerFillingState {
	/**
	 * 待填报/未提交
	 */
	PendingWrite = 1,
	/**
	 * 已提交
	 */

	Submited = 2,
	/**
	 * 已驳回
	 */
	Rejected = 3,
}

export const FillerFillingStateList = [
	{label: '未提交', value: FillerFillingState.PendingWrite},
	{label: '已提交', value: FillerFillingState.Submited},
	{label: '已驳回', value: FillerFillingState.Rejected},
]
