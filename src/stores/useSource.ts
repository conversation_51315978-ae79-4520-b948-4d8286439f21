import {defineStore} from 'pinia'
import {getLedgerType} from '@/api/LedgerApi'

// 共用数据源
export const useSource = defineStore('useSource', {
	state: () => {
		return {
			ledgerType: {items: [], totalCount: 0},
			Organize: [],
		}
	},
	getters: {
		getLedgerType: (state) => state.ledgerType,
		getOrganize: (state) => state.Organize,
	},
	actions: {
		async pullLedgerType() {
			if (this.ledgerType.items.length > 0) {
				return
			}
			const res: any = await getLedgerType()
			this.ledgerType = res.data
		},

		setOrganize(data: any) {
			this.Organize = data
		},
	},
})
