import {request} from '@/api'
import {STAFFROLEARRAY} from '@/define/organization.define'
import util from '@/plugin/util'
import {defineStore} from 'pinia'
export const useTaskManageStore = defineStore('taskManageStore', {
	state: () => ({
		taskList: [] as any[],
		totalCount: 0 as number,
		checkStaffList: [] as any[],
		auditLeaderList: [] as any[],
		checkDepartmentList: [] as any[],
	}),
	getters: {},
	actions: {
		getSameOrganizationStaff() {
			const currentLoginUserInfo = JSON.parse(localStorage.getItem('currentUserInfo') as string)
			request
				?.get(
					`/api/filling/area-organization-unit/${currentLoginUserInfo.areaOrganizationUnitId}/users`
				)
				.then((users: any) => {
					this.checkStaffList =
						users.data.items
							.filter(
								(user: any) =>
									!user.staffRole.includes(STAFFROLEARRAY[1]) &&
									user.id !== JSON.parse(localStorage.getItem('currentUserInfo') as string).id
							)
							.map((res: any) => ({
								label: res.name,
								value: res.id,
							})) ?? []
					this.auditLeaderList =
						users.data.items
							.filter((user: any) => user.staffRole.includes(STAFFROLEARRAY[1]))
							.map((res: any) => ({
								label: res.name,
								value: res.id,
							})) ?? []
				})
		},
		getNextOrganizationTree() {
			// if (localStorage.getItem('nextChildren') !== null) {
			// 	this.checkDepartmentList = JSON.parse(localStorage.getItem('nextChildren') as string)
			// } else {
			// 	request
			// 		?.request({
			// 			method: 'get',
			// 			url: '/api/platform/region/all?grade=5',
			// 			headers: {
			// 				Urlkey: 'iframeCode',
			// 			},
			// 		})
			// 		.then((res: any) => {
			// 			// let allNodeChild: any[] = []
			// 			const promise: any[] = []
			// 			const departmentList: any = []
			// 			const staffInfo = JSON.parse(localStorage.getItem('currentDepartmentInfo') ?? 'null')
			// 			// staffInfo.forEach((e: any) => {
			// 			const {city, district, community, street} = staffInfo
			// 			const role = community || street || district || city
			// 			const node = res.data.items.find((f: any) => f.name === role)
			// 			const nodeChild = res.data.items
			// 				.filter((f: any) => f.parentId === node.id || f.id === node.id)
			// 				.map((e: any) => ({...e, disabled: true}))
			// 			// allNodeChild = allNodeChild.concat(nodeChild)
			// 			nodeChild.forEach(async (data: any) => {
			// 				promise.push(
			// 					new Promise(async (resolve, reject) => {
			// 						const res = await request.request({
			// 							method: 'get',
			// 							url: `/api/platform/department?RegionId=${data.id}`,
			// 							headers: {
			// 								Urlkey: 'iframeCode',
			// 							},
			// 						})
			// 						if (res) {
			// 							res.data.items.forEach((v: any) => (v.parentId = data.id))
			// 							resolve(res.data.items)
			// 						} else {
			// 							reject()
			// 						}
			// 					})
			// 				)
			// 			})
			// 			// })
			// 			Promise.all(promise).then((resList) => {
			// 				resList.forEach((dd) => {
			// 					if (dd.length !== 0) {
			// 						dd.forEach((ee: any) => {
			// 							departmentList.push(ee)
			// 						})
			// 					}
			// 				})
			// 				const newNodeChild = nodeChild.concat(departmentList)
			// 				this.checkDepartmentList = util.arrayToTree(newNodeChild)
			// 				window.localStorage.setItem('nextChildren', JSON.stringify(this.checkDepartmentList))
			// 			})
			// 		})
			// }
		},
	},
})
