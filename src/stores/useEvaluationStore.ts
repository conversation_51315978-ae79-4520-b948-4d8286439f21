import { defineStore } from 'pinia'
import { EvaluationRecord, EvaluationNameOption } from '@/define/evaluation.types'

// 预设的评估名称选项
export const EVALUATION_NAME_OPTIONS: EvaluationNameOption[] = [
  { value: '清洁性评估', label: '清洁性评估' },
  { value: '完整性评估', label: '完整性评估' },
  { value: '一致性评估', label: '一致性评估' },
  { value: '可靠性评估', label: '可靠性评估' },
  { value: '安全性评估', label: '安全性评估' },
  { value: '隐私性评估', label: '隐私性评估' },
  { value: '合规性评估', label: '合规性评估' }
]

export const useEvaluationStore = defineStore('evaluationStore', {
  state: () => ({
    evaluations: [] as EvaluationRecord[],
    loading: false
  }),

  getters: {
    getEvaluations: (state) => state.evaluations,
    getEvaluationById: (state) => (id: string) =>
      state.evaluations.find(item => item.id === id),
    getTotalCount: (state) => state.evaluations.length,
    getFilteredEvaluations: (state) => (keyword: string = '') => {
      if (!keyword.trim()) {
        return state.evaluations
      }
      return state.evaluations.filter(item =>
        item.名称.toLowerCase().includes(keyword.toLowerCase())
      )
    }
  },

  actions: {
    // 初始化数据
    initializeData() {
      const stored = localStorage.getItem('evaluation_records')
      // 检查存储的数据是否包含精确到分钟的时间格式
      let needsUpdate = false

      if (stored) {
        try {
          const parsedData = JSON.parse(stored)
          // 检查第一条数据的时间格式，如果不包含时分，则需要更新
          if (parsedData.length > 0 && parsedData[0].创建时间 && !parsedData[0].创建时间.includes(':')) {
            needsUpdate = true
          } else {
            this.evaluations = parsedData
          }
        } catch (error) {
          needsUpdate = true
        }
      } else {
        needsUpdate = true
      }

      if (needsUpdate) {
        // 初始化模拟数据 - 使用更真实的时间分布
        this.evaluations = [
          {
            id: '1',
            序号: 1,
            名称: '清洁性评估',
            评估得分: '99%',
            创建时间: '2025.7.11 09:15',
            创建人: '李明'
          },
          {
            id: '2',
            序号: 2,
            名称: '完整性评估',
            评估得分: '99%',
            创建时间: '2025.7.11 10:32',
            创建人: '王芳'
          },
          {
            id: '3',
            序号: 3,
            名称: '一致性评估',
            评估得分: '99%',
            创建时间: '2025.7.11 11:48',
            创建人: '张伟'
          },
          {
            id: '4',
            序号: 4,
            名称: '时效性评估',
            评估得分: '99%',
            创建时间: '2025.7.11 13:26',
            创建人: '刘强'
          },
          {
            id: '5',
            序号: 5,
            名称: '可靠性评估',
            评估得分: '99%',
            创建时间: '2025.7.11 14:41',
            创建人: '陈静'
          },
          {
            id: '6',
            序号: 6,
            名称: '安全性评估',
            评估得分: '99%',
            创建时间: '2025.7.11 15:57',
            创建人: '杨磊'
          },
          {
            id: '7',
            序号: 7,
            名称: '隐私性评估',
            评估得分: '99%',
            创建时间: '2025.7.11 16:33',
            创建人: '赵敏'
          },
          {
            id: '8',
            序号: 8,
            名称: '合规性评估',
            评估得分: '99%',
            创建时间: '2025.7.11 17:19',
            创建人: '孙涛'
          }
        ]
        this.saveToStorage()
      }
    },

    // 强制重新初始化数据（清除旧格式数据）
    forceReinitialize() {
      localStorage.removeItem('evaluation_records')
      this.initializeData()
    },

    // 保存到本地存储
    saveToStorage() {
      localStorage.setItem('evaluation_records', JSON.stringify(this.evaluations))
    },

    // 添加评估记录
    addEvaluation(data: { 名称: string; 评估得分: number }) {
      const newId = Date.now().toString()

      // 真实的创建人姓名列表
      const realCreators = ['李明', '王芳', '张伟', '刘强', '陈静', '杨磊', '赵敏', '孙涛', '周华', '吴娟']
      const randomCreator = realCreators[Math.floor(Math.random() * realCreators.length)]

      const newRecord: EvaluationRecord = {
        id: newId,
        序号: this.evaluations.length + 1,
        名称: data.名称,
        评估得分: `${data.评估得分}%`,
        创建时间: this.formatDateTime(new Date()),
        创建人: randomCreator
      }

      this.evaluations.push(newRecord)
      this.updateSequenceNumbers()
      this.saveToStorage()
      return newRecord
    },

    // 更新评估记录
    updateEvaluation(id: string, data: { 名称: string; 评估得分: number }) {
      const index = this.evaluations.findIndex(item => item.id === id)
      if (index !== -1) {
        this.evaluations[index] = {
          ...this.evaluations[index],
          名称: data.名称,
          评估得分: `${data.评估得分}%`
        }
        this.saveToStorage()
        return this.evaluations[index]
      }
      return null
    },

    // 删除评估记录
    deleteEvaluation(id: string) {
      const index = this.evaluations.findIndex(item => item.id === id)
      if (index !== -1) {
        this.evaluations.splice(index, 1)
        this.updateSequenceNumbers()
        this.saveToStorage()
        return true
      }
      return false
    },

    // 更新序号
    updateSequenceNumbers() {
      this.evaluations.forEach((item, index) => {
        item.序号 = index + 1
      })
    },

    // 格式化日期时间（精确到分钟）
    formatDateTime(date: Date): string {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')

      return `${year}.${month}.${day} ${hours}:${minutes}`
    },

    // 搜索评估记录
    searchEvaluations(keyword: string) {
      if (!keyword.trim()) {
        return this.evaluations
      }
      return this.evaluations.filter(item => 
        item.名称.toLowerCase().includes(keyword.toLowerCase())
      )
    },

    // 获取分页数据
    getPaginatedEvaluations(page: number, pageSize: number, keyword: string = '') {
      const filteredData = this.searchEvaluations(keyword)
      const start = (page - 1) * pageSize
      const end = start + pageSize
      
      return {
        data: filteredData.slice(start, end),
        total: filteredData.length
      }
    }
  }
})
