import {defineStore} from 'pinia'
import {request} from '@/api'
import {getLegderAuditTableList} from '@/api/LedgerApi'
import {getTodoNotice} from '@/api/ReportApi'

export const useUserStore = defineStore('useUserStore', {
	state: () => {
		return {
			application: null,
			token: localStorage.getItem('access_token') ?? '',
			userInfo: JSON.parse(localStorage.getItem('currentUserInfo') ?? 'null'),
			platformInfo: JSON.parse(localStorage.getItem('currentPlatformInfo') ?? 'null'),
			currentDepartment: JSON.parse(localStorage.getItem('currentDepartmentInfo') ?? 'null'),
			todoCount: 0 as number,
			workCount: 0 as number,
			badgeObj: {} as any,
			auditObj: {} as any,
			//TODO: 临时解决方案 业务表用户数据
			casualUser: {} as any,

			auditCount: 0,
			fillCount: 0,

			currentLedgerUserPermissions: [],
			currentDepartmentId: '',

			logout: true,
			beforeLogoutHandle: [] as any,
		}
	},
	getters: {
		getApplication: (state) => state.application,
		getToken: (state) => state.token,
		getCasualUser: (state) => state.casualUser,
		getUserInfo: (state) => state.userInfo,
		getPlatformInfo: (state) => state.platformInfo,
		isRoleAdmin: (state) => state.userInfo?.staffRole.includes('ledger-super-admin'),
		getCurrentLedgerUserPermissions: (state) => state.currentLedgerUserPermissions,
		getCurrentDepartment: (state) => state.currentDepartment,
		getCurrentDepartmentId: (state) => state.currentDepartmentId,
		getLogout: (state) => state.logout,
		getAuditCount: (state) => state.auditCount,
		getFillCount: (state) => state.fillCount,
	},
	actions: {
		setApplication(application: any) {
			this.application = application
		},
		setBeforeLogoutHandle(promise: any) {
			this.beforeLogoutHandle.push(promise)
		},
		setLogout(logout: boolean) {
			this.logout = logout
		},
		async clear(fn?: Function) {
			for (let i = 0; i < this.beforeLogoutHandle.length; i++) {
				await this.beforeLogoutHandle[i].handle()
			}

			this.token = ''
			this.userInfo = null
			this.beforeLogoutHandle = []
			typeof fn === 'function' && fn()
		},
		setToken(token: string, refresh_token: string) {
			this.token = token
			localStorage.setItem('access_token', token)
			localStorage.setItem('refresh_token', refresh_token)
		},
		setCurrentUserInfo(user: any) {
			console.log('currentUserInfo', user)
			this.userInfo = user
			localStorage.setItem('currentUserInfo', JSON.stringify(user))
		},
		setCurrentPlatformInfo(platform: any) {
			this.platformInfo = platform
			localStorage.setItem('currentPlatformInfo', JSON.stringify(platform))
		},
		setCurrentDepartment(department: any) {
			this.currentDepartment = department
			localStorage.setItem('currentDepartmentInfo', JSON.stringify(department))
		},
		setCasualUser(casualUser: any) {
			this.casualUser = casualUser
		},
		setTodoCount(count: number) {
			this.todoCount = count
		},

		async getAuditListCount() {
			if (this.getCurrentDepartmentId) {
				const res = await getLegderAuditTableList({
					LedgerName: '',
					skipCount: 0,
					MaxResultCount: 1,
					BigDepartmentId: this.getCurrentDepartmentId,
					status: 0,
				})
				console.log(res.data, 'getAuditListCount')
				this.auditObj = res.data
			} else {
				setTimeout(() => {
					this.getAuditListCount()
				}, 1000)
			}
		},
		async getWorkTodoCount() {
			const params = {
				skipCount: 0,
				maxResultCount: 1,
			}
			const res = await getTodoNotice(params, 1)
			const {totalCount} = res.data
			// userStore.$patch({workCount: totalCount})
			this.workCount = totalCount
		},
		setCurrentLedgerUserPermissions(arr: any) {
			this.currentLedgerUserPermissions = arr
		},
		setCurrentDepartmentId(id: string) {
			this.currentDepartmentId = id
		},
		setAuditkCount(count: any) {
			this.auditCount = count
		},
		setFillCount(count: any) {
			this.fillCount = count
		},
	},
})
