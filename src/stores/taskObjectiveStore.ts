import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 数据类型定义
export interface TaskObjective {
  id: string
  sequence: number
  taskName: string
  taskType: string
  createDepartment: string
  startTime: string
  executionCycle: string
  taskStatus: '执行中' | '已暂停'
  taskSwitch: boolean
}

export interface BusinessReport {
  id: string
  sequence: number
  reportName: string
  publishDepartment: string
  belongingModule: string
  dataUpdateFrequency: string
  dataSource: string
  updateFrequency: string
  stopTime: string
}

export interface TaskConfig {
  taskType: string
  taskName: string
  taskCategory: string
  relatedBusinessReport: string
  businessReportTemplate: File | null
  taskDescription: string
  taskPriority: 'low' | 'medium' | 'high'
  responsiblePerson: string
  participants: string[]
  dateFormat: string
  timeFormat: string
  taskTimezone: string
  startTime: string
  endTime: string
  reminderTime: string
}

export interface PersonnelData {
  id: string
  name: string
  department: string
  role: string
  permissions: string[]
}

export interface SubTask {
  id: string
  taskId: string
  sequence: number
  taskName: string
  taskType: string
  taskCategory: string
  responsiblePerson: string
  participants: string
  taskStatus: '未提交' | '待审核' | '已提交' | '已退回' | '已驳回' | '已完成'
  progress: number
  urgencyLevel: 'P1' | 'P2' | 'P3' | 'P4' // 紧急程度：P1-特急，P2-加急，P3-平急，P4-不重要
  importanceLevel: 'I1' | 'I2' | 'I3' | 'I4' // 重要程度：I1-非常重要，I2-重要，I3-一般重要，I4-不重要
  createTime: string
  updateTime: string
}

export interface TimezoneOption {
  value: string
  label: string
}

export const useTaskObjectiveStore = defineStore('taskObjective', () => {
  // 状态
  const tasks = ref<TaskObjective[]>([])
  const businessReports = ref<BusinessReport[]>([])
  const personnelData = ref<PersonnelData[]>([])
  const subTasks = ref<SubTask[]>([])
  const taskConfigs = ref<Record<string, TaskConfig>>({})
  
  // 时区选项
  const timezoneOptions = ref<TimezoneOption[]>([
    { value: 'UTC+07:00_Barnaul', label: '(UTC+07:00) 巴尔瑙尔，戈尔诺-阿尔泰斯克' },
    { value: 'UTC+07:00_Khovd', label: '(UTC+07:00) 科布多' },
    { value: 'UTC+07:00_Krasnoyarsk', label: '(UTC+07:00) 克拉斯诺亚尔斯克' },
    { value: 'UTC+07:00_Bangkok', label: '(UTC+07:00) 曼谷，河内，雅加达' },
    { value: 'UTC+07:00_Tomsk', label: '(UTC+07:00) 托木斯克' },
    { value: 'UTC+07:00_Novosibirsk', label: '(UTC+07:00) 新西伯利亚' },
    { value: 'UTC+08:00_Beijing', label: '(UTC+08:00) 北京，重庆，香港特别行政区，乌鲁木齐' },
    { value: 'UTC+08:00_Kuala_Lumpur', label: '(UTC+08:00) 吉隆坡，新加坡' },
    { value: 'UTC+08:00_Perth', label: '(UTC+08:00) 珀斯' },
    { value: 'UTC+08:00_Taipei', label: '(UTC+08:00) 台北' },
    { value: 'UTC+08:00_Ulaanbaatar', label: '(UTC+08:00) 乌兰巴托' },
    { value: 'UTC+08:00_Irkutsk', label: '(UTC+08:00) 伊尔库茨克' },
    { value: 'UTC+08:45_Eucla', label: '(UTC+08:45) 尤克拉' }
  ])

  // 计算属性
  const filteredTasks = computed(() => {
    return (keyword: string) => {
      if (!keyword) return tasks.value
      return tasks.value.filter(task => 
        task.taskName.toLowerCase().includes(keyword.toLowerCase())
      )
    }
  })

  const filteredBusinessReports = computed(() => {
    return (filters: { name?: string, department?: string, module?: string }) => {
      let result = businessReports.value
      
      if (filters.name) {
        result = result.filter(report => 
          report.reportName.toLowerCase().includes(filters.name!.toLowerCase())
        )
      }
      
      if (filters.department) {
        result = result.filter(report => 
          report.publishDepartment.toLowerCase().includes(filters.department!.toLowerCase())
        )
      }
      
      if (filters.module) {
        result = result.filter(report => 
          report.belongingModule.toLowerCase().includes(filters.module!.toLowerCase())
        )
      }
      
      return result
    }
  })

  // 计算属性 - 获取任务的子任务
  const getSubTasksByTaskId = computed(() => {
    return (taskId: string) => {
      return subTasks.value.filter(subTask => subTask.taskId === taskId)
    }
  })

  // 计算属性 - 根据ID获取子任务
  const getSubTaskById = computed(() => {
    return (subTaskId: string) => {
      return subTasks.value.find(subTask => subTask.id === subTaskId)
    }
  })

  // 方法
  const getTaskById = (taskId: string): TaskObjective | undefined => {
    return tasks.value.find(task => task.id === taskId)
  }
  const initializeData = () => {
    // 从localStorage加载数据
    loadFromStorage()
    
    // 如果没有数据，初始化模拟数据
    if (tasks.value.length === 0) {
      initializeMockData()
    }
  }

  const initializeMockData = () => {
    // 初始化任务数据
    tasks.value = [
      {
        id: '1',
        sequence: 1,
        taskName: '数据收集',
        taskType: '报表',
        createDepartment: '永川区民政局-安全科-程飞',
        startTime: '2024-01-01',
        executionCycle: '一次性',
        taskStatus: '执行中',
        taskSwitch: true
      },
      {
        id: '2',
        sequence: 2,
        taskName: '人口基础信息采集',
        taskType: '业务表',
        createDepartment: '永川区民政局-安全科-程飞',
        startTime: '2024-01-01',
        executionCycle: '每周一次',
        taskStatus: '执行中',
        taskSwitch: true
      },
      {
        id: '3',
        sequence: 3,
        taskName: '数据收集',
        taskType: '报表',
        createDepartment: '永川区民政局-安全科-程飞',
        startTime: '2024-01-01',
        executionCycle: '每周一次',
        taskStatus: '执行中',
        taskSwitch: true
      },
      {
        id: '4',
        sequence: 4,
        taskName: '数据收集',
        taskType: '业务表',
        createDepartment: '永川区民政局-安全科-程飞',
        startTime: '2024-01-01',
        executionCycle: '每月一次',
        taskStatus: '已暂停',
        taskSwitch: false
      },
      {
        id: '5',
        sequence: 5,
        taskName: '数据收集',
        taskType: '报表',
        createDepartment: '永川区民政局-安全科-程飞',
        startTime: '2024-01-01',
        executionCycle: '每年一次',
        taskStatus: '已暂停',
        taskSwitch: false
      },
      {
        id: '6',
        sequence: 6,
        taskName: '数据收集',
        taskType: '业务表',
        createDepartment: '永川区民政局-安全科-程飞',
        startTime: '2024-01-01',
        executionCycle: '一次性',
        taskStatus: '已暂停',
        taskSwitch: false
      },
      {
        id: '7',
        sequence: 7,
        taskName: '数据收集',
        taskType: '报表',
        createDepartment: '永川区民政局-安全科-程飞',
        startTime: '2024-01-01',
        executionCycle: '每日一次',
        taskStatus: '已暂停',
        taskSwitch: false
      },
      {
        id: '8',
        sequence: 8,
        taskName: '数据收集',
        taskType: '业务表',
        createDepartment: '永川区民政局-安全科-程飞',
        startTime: '2024-01-01',
        executionCycle: '每周一次',
        taskStatus: '已暂停',
        taskSwitch: false
      },
      {
        id: '9',
        sequence: 9,
        taskName: '数据收集',
        taskType: '报表',
        createDepartment: '永川区民政局-安全科-程飞',
        startTime: '2024-01-01',
        executionCycle: '每月一次',
        taskStatus: '已暂停',
        taskSwitch: false
      },
      {
        id: '10',
        sequence: 10,
        taskName: '数据收集',
        taskType: '业务表',
        createDepartment: '永川区民政局-安全科-程飞',
        startTime: '2024-01-01',
        executionCycle: '每年一次',
        taskStatus: '已暂停',
        taskSwitch: false
      }
    ]

    // 初始化业务报表数据
    businessReports.value = [
      {
        id: 'br1',
        sequence: 1,
        reportName: '产业改革业务表',
        publishDepartment: '永川区-供销社',
        belongingModule: '党的建设',
        dataUpdateFrequency: '每周',
        dataSource: '——',
        updateFrequency: '每周',
        stopTime: '未设置'
      },
      {
        id: 'br2',
        sequence: 2,
        reportName: '失业人员登记表',
        publishDepartment: '永川区-财政局',
        belongingModule: '经济发展',
        dataUpdateFrequency: '每日',
        dataSource: '——',
        updateFrequency: '每日',
        stopTime: '每周五前'
      },
      {
        id: 'br3',
        sequence: 3,
        reportName: '人口普查登记表',
        publishDepartment: '永川区-民政局',
        belongingModule: '民生服务',
        dataUpdateFrequency: '每月',
        dataSource: '——',
        updateFrequency: '每月',
        stopTime: '每月三前'
      },
      {
        id: 'br4',
        sequence: 4,
        reportName: '老年人登记表',
        publishDepartment: '永川区-交通局',
        belongingModule: '平安法治',
        dataUpdateFrequency: '每日',
        dataSource: '——',
        updateFrequency: '每日',
        stopTime: '——'
      },
      {
        id: 'br5',
        sequence: 5,
        reportName: '耕地面积业务表',
        publishDepartment: '永川区-公安局',
        belongingModule: '党的建设',
        dataUpdateFrequency: '每日',
        dataSource: '——',
        updateFrequency: '每日',
        stopTime: '每周一前'
      },
      {
        id: 'br6',
        sequence: 6,
        reportName: '产业改革业务表',
        publishDepartment: '产业改革业务表',
        belongingModule: '经济发展',
        dataUpdateFrequency: '每周',
        dataSource: '创业就业-就业服务',
        updateFrequency: '每周',
        stopTime: '——'
      },
      {
        id: 'br7',
        sequence: 7,
        reportName: '失业人员登记表',
        publishDepartment: '失业人员登记表',
        belongingModule: '民生服务',
        dataUpdateFrequency: '每日',
        dataSource: '创业就业-就业服务',
        updateFrequency: '每日',
        stopTime: '未设置'
      },
      {
        id: 'br8',
        sequence: 8,
        reportName: '人口普查登记表',
        publishDepartment: '人口普查登记表',
        belongingModule: '平安法治',
        dataUpdateFrequency: '每月',
        dataSource: '创业就业-就业服务',
        updateFrequency: '每月',
        stopTime: '未设置'
      },
      {
        id: 'br9',
        sequence: 9,
        reportName: '老年人登记表',
        publishDepartment: '老年人登记表',
        belongingModule: '党的建设',
        dataUpdateFrequency: '每日',
        dataSource: '创业就业-就业服务',
        updateFrequency: '每日',
        stopTime: '每周一前'
      },
      {
        id: 'br10',
        sequence: 10,
        reportName: '耕地面积业务表',
        publishDepartment: '耕地面积业务表',
        belongingModule: '经济发展',
        dataUpdateFrequency: '每日',
        dataSource: '创业就业-就业服务',
        updateFrequency: '每日',
        stopTime: '——'
      }
    ]

    // 初始化人员数据
    personnelData.value = [
      {
        id: 'p1',
        name: '张三',
        department: '永川区民政局-安全科',
        role: '科员',
        permissions: ['新增', '编辑', '删除']
      },
      {
        id: 'p2',
        name: '李四',
        department: '永川区民政局-安全科',
        role: '副科长',
        permissions: ['新增', '编辑']
      },
      {
        id: 'p3',
        name: '王五',
        department: '永川区民政局-安全科',
        role: '科长',
        permissions: ['新增', '编辑', '删除']
      },
      {
        id: 'p4',
        name: '程飞',
        department: '永川区民政局-安全科',
        role: '科员',
        permissions: ['新增', '编辑', '删除']
      },
      {
        id: 'p5',
        name: '刘六',
        department: '朱沱镇-公共服务',
        role: '主任',
        permissions: ['新增', '编辑', '删除']
      },
      {
        id: 'p6',
        name: '赵七',
        department: '朱沱镇-公共服务',
        role: '副主任',
        permissions: ['新增', '编辑']
      },
      {
        id: 'p7',
        name: '孙八',
        department: '永川区财政局-预算科',
        role: '科长',
        permissions: ['新增', '编辑', '删除']
      },
      {
        id: 'p8',
        name: '周九',
        department: '永川区财政局-预算科',
        role: '科员',
        permissions: ['新增', '编辑']
      },
      {
        id: 'p9',
        name: '吴十',
        department: '永川区交通局-运输科',
        role: '科长',
        permissions: ['新增', '编辑', '删除']
      },
      {
        id: 'p10',
        name: '郑十一',
        department: '永川区交通局-运输科',
        role: '副科长',
        permissions: ['新增', '编辑']
      },
      {
        id: 'p11',
        name: '王十二',
        department: '永川区公安局-治安科',
        role: '科长',
        permissions: ['新增', '编辑', '删除']
      },
      {
        id: 'p12',
        name: '李十三',
        department: '永川区公安局-治安科',
        role: '警员',
        permissions: ['新增', '编辑']
      },
      {
        id: 'p13',
        name: '张十四',
        department: '永川区供销社-业务科',
        role: '主任',
        permissions: ['新增', '编辑', '删除']
      },
      {
        id: 'p14',
        name: '陈十五',
        department: '永川区供销社-业务科',
        role: '科员',
        permissions: ['新增', '编辑']
      },
      {
        id: 'p15',
        name: '杨十六',
        department: '永川区卫健委-医政科',
        role: '科长',
        permissions: ['新增', '编辑', '删除']
      }
    ]

    // 初始化子任务数据 - 根据原型图完善数据，确保责任人姓名与人员数据一致
    subTasks.value = [
      {
        id: 'st1',
        taskId: '1',
        sequence: 1,
        taskName: '永川区民政局填报流程',
        taskType: '业务报表',
        taskCategory: '党的建设',
        responsiblePerson: '张三',
        participants: '朱沱镇-公共服务-王五等',
        taskStatus: '未提交',
        progress: 30,
        urgencyLevel: 'P2',
        importanceLevel: 'I2',
        createTime: '2024-04-01',
        updateTime: '2024-04-23'
      },
      {
        id: 'st2',
        taskId: '1',
        sequence: 2,
        taskName: '永川区民政局填报流程',
        taskType: '业务报表',
        taskCategory: '党的建设',
        responsiblePerson: '李四',
        participants: '朱沱镇-公共服务-刘六等',
        taskStatus: '待审核',
        progress: 30,
        urgencyLevel: 'P1',
        importanceLevel: 'I1',
        createTime: '2024-04-01',
        updateTime: '2024-04-23'
      },
      {
        id: 'st3',
        taskId: '1',
        sequence: 3,
        taskName: '系统填报流程',
        taskType: '业务报表',
        taskCategory: '民生服务',
        responsiblePerson: '王五',
        participants: '朱沱镇-公共服务-刘六等',
        taskStatus: '已提交',
        progress: 100,
        urgencyLevel: 'P3',
        importanceLevel: 'I3',
        createTime: '2024-04-01',
        updateTime: '2024-04-23'
      },
      {
        id: 'st4',
        taskId: '1',
        sequence: 4,
        taskName: '系统填报流程',
        taskType: '临时报表',
        taskCategory: '民生服务',
        responsiblePerson: '程飞',
        participants: '朱沱镇-公共服务-刘六等',
        taskStatus: '已退回',
        progress: 80,
        urgencyLevel: 'P1',
        importanceLevel: 'I2',
        createTime: '2024-04-01',
        updateTime: '2024-04-23'
      },
      {
        id: 'st5',
        taskId: '1',
        sequence: 5,
        taskName: '永川区民政局填报流程',
        taskType: '临时报表',
        taskCategory: '民生服务',
        responsiblePerson: '刘六',
        participants: '朱沱镇-公共服务-刘六等',
        taskStatus: '已驳回',
        progress: 65,
        urgencyLevel: 'P2',
        importanceLevel: 'I3',
        createTime: '2024-04-01',
        updateTime: '2024-04-23'
      },
      {
        id: 'st6',
        taskId: '1',
        sequence: 6,
        taskName: '系统填报流程',
        taskType: '临时报表',
        taskCategory: '民生服务',
        responsiblePerson: '赵七',
        participants: '朱沱镇-公共服务-刘六等',
        taskStatus: '已完成',
        progress: 100,
        urgencyLevel: 'P4',
        importanceLevel: 'I4',
        createTime: '2024-04-01',
        updateTime: '2024-04-23'
      },
      {
        id: 'st7',
        taskId: '1',
        sequence: 7,
        taskName: '系统填报流程',
        taskType: '临时报表',
        taskCategory: '民生服务',
        responsiblePerson: '孙八',
        participants: '朱沱镇-公共服务-刘六等',
        taskStatus: '已完成',
        progress: 100,
        urgencyLevel: 'P3',
        importanceLevel: 'I3',
        createTime: '2024-04-01',
        updateTime: '2024-04-23'
      },
      // 为其他任务添加子任务
      {
        id: 'st8',
        taskId: '2',
        sequence: 1,
        taskName: '人口信息采集流程',
        taskType: '业务报表',
        taskCategory: '民生服务',
        responsiblePerson: '张三',
        participants: '朱沱镇-公共服务-王五等',
        taskStatus: '执行中',
        progress: 45,
        urgencyLevel: 'P2',
        importanceLevel: 'I2',
        createTime: '2024-04-01',
        updateTime: '2024-04-23'
      },
      {
        id: 'st9',
        taskId: '3',
        sequence: 1,
        taskName: '数据收集流程',
        taskType: '报表',
        taskCategory: '经济发展',
        responsiblePerson: '李四',
        participants: '朱沱镇-公共服务-李四等',
        taskStatus: '执行中',
        progress: 60,
        urgencyLevel: 'P1',
        importanceLevel: 'I1',
        createTime: '2024-04-01',
        updateTime: '2024-04-23'
      }
    ]

    // 保存到localStorage
    saveToStorage()
  }

  const updateTaskSwitch = (taskId: string, enabled: boolean) => {
    const task = tasks.value.find(t => t.id === taskId)
    if (task) {
      task.taskSwitch = enabled
      task.taskStatus = enabled ? '执行中' : '已暂停'
      saveToStorage()
    }
  }

  const saveTaskConfig = (taskId: string, config: TaskConfig) => {
    taskConfigs.value[taskId] = config
    saveToStorage()
  }

  const getTaskConfig = (taskId: string): TaskConfig | null => {
    return taskConfigs.value[taskId] || null
  }

  // 子任务CRUD操作方法
  const addSubTask = (subTask: Omit<SubTask, 'id' | 'createTime' | 'updateTime' | 'sequence'>) => {
    // 获取同一任务下的最大序号
    const taskSubTasks = subTasks.value.filter(st => st.taskId === subTask.taskId)
    const maxSequence = taskSubTasks.length > 0 ? Math.max(...taskSubTasks.map(st => st.sequence)) : 0

    const newSubTask: SubTask = {
      ...subTask,
      id: `st_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      sequence: maxSequence + 1,
      urgencyLevel: subTask.urgencyLevel || 'P3', // 默认为平急
      importanceLevel: subTask.importanceLevel || 'I3', // 默认为一般重要
      createTime: new Date().toISOString().split('T')[0],
      updateTime: new Date().toISOString().split('T')[0]
    }
    subTasks.value.push(newSubTask)
    saveToStorage()
    return newSubTask
  }

  const updateSubTask = (subTaskId: string, updates: Partial<SubTask>) => {
    const index = subTasks.value.findIndex(st => st.id === subTaskId)
    if (index !== -1) {
      subTasks.value[index] = {
        ...subTasks.value[index],
        ...updates,
        updateTime: new Date().toISOString().split('T')[0]
      }
      saveToStorage()
      return subTasks.value[index]
    }
    return null
  }

  const deleteSubTask = (subTaskId: string) => {
    const index = subTasks.value.findIndex(st => st.id === subTaskId)
    if (index !== -1) {
      const deletedSubTask = subTasks.value.splice(index, 1)[0]
      saveToStorage()
      return deletedSubTask
    }
    return null
  }

  const deleteSubTasks = (subTaskIds: string[]) => {
    const deletedSubTasks = subTasks.value.filter(st => subTaskIds.includes(st.id))
    subTasks.value = subTasks.value.filter(st => !subTaskIds.includes(st.id))
    saveToStorage()
    return deletedSubTasks
  }

  const copySubTask = (subTaskId: string) => {
    const originalSubTask = subTasks.value.find(st => st.id === subTaskId)
    if (originalSubTask) {
      const copiedSubTask = addSubTask({
        ...originalSubTask,
        taskName: `${originalSubTask.taskName}(副本)`
      })
      return copiedSubTask
    }
    return null
  }

  // 子任务上移
  const moveSubTaskUp = (subTaskId: string) => {
    console.log('开始上移子任务:', subTaskId)
    const subTask = subTasks.value.find(st => st.id === subTaskId)
    if (!subTask) {
      console.log('未找到子任务:', subTaskId)
      return false
    }

    // 获取同一任务下的所有子任务，按序号排序
    const taskSubTasks = subTasks.value
      .filter(st => st.taskId === subTask.taskId)
      .sort((a, b) => a.sequence - b.sequence)

    console.log('任务下的子任务列表:', taskSubTasks.map(st => ({ id: st.id, sequence: st.sequence, name: st.taskName })))

    const currentIndex = taskSubTasks.findIndex(st => st.id === subTaskId)
    console.log('当前子任务索引:', currentIndex)

    // 如果已经是第一个，无法上移
    if (currentIndex <= 0) {
      console.log('已经是第一个，无法上移')
      return false
    }

    // 交换序号
    const prevSubTask = taskSubTasks[currentIndex - 1]
    console.log('交换前:', { current: subTask.sequence, prev: prevSubTask.sequence })

    const tempSequence = subTask.sequence
    subTask.sequence = prevSubTask.sequence
    prevSubTask.sequence = tempSequence

    console.log('交换后:', { current: subTask.sequence, prev: prevSubTask.sequence })

    // 更新时间
    subTask.updateTime = new Date().toISOString().split('T')[0]
    prevSubTask.updateTime = new Date().toISOString().split('T')[0]

    saveToStorage()
    console.log('上移完成，保存到存储')
    return true
  }

  // 子任务下移
  const moveSubTaskDown = (subTaskId: string) => {
    console.log('开始下移子任务:', subTaskId)
    const subTask = subTasks.value.find(st => st.id === subTaskId)
    if (!subTask) {
      console.log('未找到子任务:', subTaskId)
      return false
    }

    // 获取同一任务下的所有子任务，按序号排序
    const taskSubTasks = subTasks.value
      .filter(st => st.taskId === subTask.taskId)
      .sort((a, b) => a.sequence - b.sequence)

    console.log('任务下的子任务列表:', taskSubTasks.map(st => ({ id: st.id, sequence: st.sequence, name: st.taskName })))

    const currentIndex = taskSubTasks.findIndex(st => st.id === subTaskId)
    console.log('当前子任务索引:', currentIndex)

    // 如果已经是最后一个，无法下移
    if (currentIndex >= taskSubTasks.length - 1) {
      console.log('已经是最后一个，无法下移')
      return false
    }

    // 交换序号
    const nextSubTask = taskSubTasks[currentIndex + 1]
    console.log('交换前:', { current: subTask.sequence, next: nextSubTask.sequence })

    const tempSequence = subTask.sequence
    subTask.sequence = nextSubTask.sequence
    nextSubTask.sequence = tempSequence

    console.log('交换后:', { current: subTask.sequence, next: nextSubTask.sequence })

    // 更新时间
    subTask.updateTime = new Date().toISOString().split('T')[0]
    nextSubTask.updateTime = new Date().toISOString().split('T')[0]

    saveToStorage()
    console.log('下移完成，保存到存储')
    return true
  }

  // 本地存储方法
  const saveToStorage = () => {
    localStorage.setItem('taskObjective_tasks', JSON.stringify(tasks.value))
    localStorage.setItem('taskObjective_businessReports', JSON.stringify(businessReports.value))
    localStorage.setItem('taskObjective_personnelData', JSON.stringify(personnelData.value))
    localStorage.setItem('taskObjective_subTasks', JSON.stringify(subTasks.value))
    localStorage.setItem('taskObjective_taskConfigs', JSON.stringify(taskConfigs.value))
  }

  const loadFromStorage = () => {
    const storedTasks = localStorage.getItem('taskObjective_tasks')
    const storedReports = localStorage.getItem('taskObjective_businessReports')
    const storedPersonnel = localStorage.getItem('taskObjective_personnelData')
    const storedSubTasks = localStorage.getItem('taskObjective_subTasks')
    const storedConfigs = localStorage.getItem('taskObjective_taskConfigs')

    if (storedTasks) {
      tasks.value = JSON.parse(storedTasks)
    }
    if (storedReports) {
      businessReports.value = JSON.parse(storedReports)
    }
    if (storedPersonnel) {
      personnelData.value = JSON.parse(storedPersonnel)
    }
    if (storedSubTasks) {
      subTasks.value = JSON.parse(storedSubTasks)
    }
    if (storedConfigs) {
      taskConfigs.value = JSON.parse(storedConfigs)
    }
  }

  // 重置演示数据
  const resetDemoData = () => {
    // 清除localStorage中的数据
    localStorage.removeItem('taskObjective_tasks')
    localStorage.removeItem('taskObjective_businessReports')
    localStorage.removeItem('taskObjective_personnelData')
    localStorage.removeItem('taskObjective_subTasks')
    localStorage.removeItem('taskObjective_taskConfigs')

    // 重新初始化模拟数据
    initializeMockData()
  }

  return {
    // 状态
    tasks,
    businessReports,
    personnelData,
    subTasks,
    taskConfigs,
    timezoneOptions,

    // 计算属性
    filteredTasks,
    filteredBusinessReports,
    getSubTasksByTaskId,
    getSubTaskById,

    // 方法
    getTaskById,
    initializeData,
    initializeMockData,
    updateTaskSwitch,
    saveTaskConfig,
    getTaskConfig,

    // 子任务CRUD方法
    addSubTask,
    updateSubTask,
    deleteSubTask,
    deleteSubTasks,
    copySubTask,
    moveSubTaskUp,
    moveSubTaskDown,

    // 存储方法
    saveToStorage,
    loadFromStorage,
    resetDemoData
  }
})
