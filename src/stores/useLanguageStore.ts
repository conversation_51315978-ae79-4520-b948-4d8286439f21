import { defineStore } from 'pinia'
import { ref } from 'vue'

export type Language = 'zh' | 'en'

export const useLanguageStore = defineStore('language', () => {
  // 当前语言状态
  const currentLanguage = ref<Language>('zh')
  
  // 从本地存储初始化语言设置
  const initLanguage = () => {
    const savedLanguage = localStorage.getItem('app_language') as Language
    if (savedLanguage && ['zh', 'en'].includes(savedLanguage)) {
      currentLanguage.value = savedLanguage
    } else {
      // 如果没有保存的语言设置，默认设置为中文
      currentLanguage.value = 'zh'
      localStorage.setItem('app_language', 'zh')
    }
  }
  
  // 切换语言
  const setLanguage = (language: Language) => {
    currentLanguage.value = language
    localStorage.setItem('app_language', language)
  }
  
  // 获取当前语言
  const getCurrentLanguage = () => currentLanguage.value
  
  // 判断是否为中文
  const isZh = () => currentLanguage.value === 'zh'
  
  // 判断是否为英文
  const isEn = () => currentLanguage.value === 'en'
  
  return {
    currentLanguage,
    initLanguage,
    setLanguage,
    getCurrentLanguage,
    isZh,
    isEn
  }
})
