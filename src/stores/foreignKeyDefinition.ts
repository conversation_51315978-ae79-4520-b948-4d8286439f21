import { defineStore } from 'pinia'

// 外键定义数据接口
export interface ForeignKeyDefinition {
  id: string
  外键名称: string
  外键类型: string
  最小长度: number
  最大长度: number
  外键版本: string
  说明: string
  状态: boolean
  创建时间: string
  创建人: string
}

// 业务表接口
export interface BusinessTable {
  id: string
  表名: string
  表描述: string
  表类型: string
  创建时间: string
}

// 外键与业务表关联关系接口
export interface ForeignKeyBusinessTableRelation {
  id: string
  外键ID: string
  外键名称: string
  业务表ID: string
  业务表名称: string
  关联字段: string
  关联类型: string
  创建时间: string
  创建人: string
}

// 外键类型选项
export const FOREIGN_KEY_TYPES = [
  { label: '字符型C', value: '字符型C' },
  { label: '数值型N', value: '数值型N' },
  { label: '逻辑型L', value: '逻辑型L' },
  { label: '日期型T', value: '日期型T' },
  { label: '浮点型F', value: '浮点型F' },
  { label: '货币型Y', value: '货币型Y' },
  { label: '备注型M', value: '备注型M' }
]

// 业务表选项
export const BUSINESS_TABLES = [
  { label: '用户信息表', value: 'user_info', description: '存储用户基本信息' },
  { label: '订单主表', value: 'order_main', description: '订单核心数据表' },
  { label: '产品信息表', value: 'product_info', description: '产品基础信息' },
  { label: '部门组织表', value: 'department', description: '组织架构信息' },
  { label: '客户主数据表', value: 'customer_master', description: '客户基础数据' },
  { label: '供应商信息表', value: 'supplier_info', description: '供应商基础信息' },
  { label: '企业信息表', value: 'company_info', description: '企业基础信息' },
  { label: '地区信息表', value: 'region_info', description: '地理区域信息' },
  { label: '分类管理表', value: 'category_manage', description: '分类信息管理' },
  { label: '财务科目表', value: 'finance_subject', description: '财务会计科目' }
]

// 关联类型选项
export const RELATION_TYPES = [
  { label: '一对一', value: 'one_to_one' },
  { label: '一对多', value: 'one_to_many' },
  { label: '多对一', value: 'many_to_one' },
  { label: '多对多', value: 'many_to_many' }
]

// 真实姓名生成器
const generateRealisticName = (): string => {
  const surnames = ['李', '王', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗', '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧', '程', '曹', '袁', '邓', '许', '傅', '沈', '曾', '彭', '吕', '苏', '卢', '蒋', '蔡', '贾', '丁', '魏', '薛', '叶', '阎']
  const givenNames = ['明', '芳', '伟', '强', '静', '磊', '敏', '刚', '丽', '飞', '华', '军', '娟', '涛', '超', '勇', '艳', '杰', '峰', '辉', '斌', '雷', '鹏', '建', '文', '龙', '云', '志', '宇', '浩', '凯', '晨', '阳', '欣', '悦', '婷', '雯', '琳', '慧', '颖', '洁', '莉', '萍', '红', '燕', '玲', '梅', '霞', '雪', '晶', '佳']

  const surname = surnames[Math.floor(Math.random() * surnames.length)]
  const givenName = givenNames[Math.floor(Math.random() * givenNames.length)]

  // 30% 概率生成双字名
  if (Math.random() < 0.3) {
    const secondGivenName = givenNames[Math.floor(Math.random() * givenNames.length)]
    return surname + givenName + secondGivenName
  }

  return surname + givenName
}

export const useForeignKeyDefinitionStore = defineStore('foreignKeyDefinition', {
  state: () => ({
    foreignKeys: [] as ForeignKeyDefinition[],
    businessTables: [] as BusinessTable[],
    foreignKeyBusinessRelations: [] as ForeignKeyBusinessTableRelation[],
    dataVersion: '2.0' // 添加数据版本标识
  }),

  getters: {
    // 获取过滤后的外键列表
    getFilteredForeignKeys: (state) => (filters: any) => {
      let filtered = [...state.foreignKeys]

      if (filters.外键名称) {
        filtered = filtered.filter(item => 
          item.外键名称.toLowerCase().includes(filters.外键名称.toLowerCase())
        )
      }

      if (filters.外键类型) {
        filtered = filtered.filter(item => item.外键类型 === filters.外键类型)
      }

      if (filters.最小长度范围) {
        filtered = filtered.filter(item => item.最小长度 === filters.最小长度范围)
      }

      if (filters.最大长度范围) {
        filtered = filtered.filter(item => item.最大长度 === filters.最大长度范围)
      }

      return filtered
    }
  },

  actions: {
    // 初始化数据
    initializeData() {
      const stored = localStorage.getItem('foreignKeyDefinitions')
      const storedVersion = localStorage.getItem('foreignKeyDefinitionsVersion')

      // 如果没有存储数据或版本不匹配，强制使用新的真实数据
      if (!stored || storedVersion !== this.dataVersion) {
        // 初始化真实的业务数据
        this.foreignKeys = [
          {
            id: '1',
            外键名称: 'USER_ID',
            外键类型: '字符型C',
            最小长度: 8,
            最大长度: 20,
            外键版本: 'V1.0',
            说明: '用户唯一标识符，用于关联用户表主键',
            状态: true,
            创建时间: '2024-01-15',
            创建人: '李明'
          },
          {
            id: '2',
            外键名称: 'ORDER_NO',
            外键类型: '字符型C',
            最小长度: 10,
            最大长度: 30,
            外键版本: 'V1.2',
            说明: '订单编号外键，关联订单主表',
            状态: true,
            创建时间: '2024-01-20',
            创建人: '王芳'
          },
          {
            id: '3',
            外键名称: 'PRODUCT_CODE',
            外键类型: '字符型C',
            最小长度: 6,
            最大长度: 15,
            外键版本: 'V2.0',
            说明: '产品编码外键，用于产品信息关联',
            状态: true,
            创建时间: '2024-02-01',
            创建人: '张伟'
          },
          {
            id: '4',
            外键名称: 'DEPT_ID',
            外键类型: '数值型N',
            最小长度: 3,
            最大长度: 6,
            外键版本: 'V1.0',
            说明: '部门编号外键，关联组织架构表',
            状态: true,
            创建时间: '2024-02-10',
            创建人: '刘强'
          },
          {
            id: '5',
            外键名称: 'CREATE_TIME',
            外键类型: '日期型T',
            最小长度: 19,
            最大长度: 19,
            外键版本: 'V1.0',
            说明: '创建时间外键，标准日期时间格式',
            状态: true,
            创建时间: '2024-02-15',
            创建人: '陈静'
          },
          {
            id: '6',
            外键名称: 'STATUS_FLAG',
            外键类型: '逻辑型L',
            最小长度: 1,
            最大长度: 1,
            外键版本: 'V1.0',
            说明: '状态标识外键，布尔值类型',
            状态: true,
            创建时间: '2024-02-20',
            创建人: '杨磊'
          },
          {
            id: '7',
            外键名称: 'AMOUNT',
            外键类型: '货币型Y',
            最小长度: 1,
            最大长度: 15,
            外键版本: 'V1.1',
            说明: '金额外键，用于财务相关表关联',
            状态: true,
            创建时间: '2024-03-01',
            创建人: '黄敏'
          },
          {
            id: '8',
            外键名称: 'DESCRIPTION',
            外键类型: '备注型M',
            最小长度: 1,
            最大长度: 500,
            外键版本: 'V1.0',
            说明: '描述信息外键，长文本类型',
            状态: false,
            创建时间: '2024-03-05',
            创建人: '赵刚'
          },
          {
            id: '9',
            外键名称: 'CUSTOMER_ID',
            外键类型: '字符型C',
            最小长度: 8,
            最大长度: 25,
            外键版本: 'V1.3',
            说明: '客户编号外键，关联客户主数据',
            状态: true,
            创建时间: '2024-03-10',
            创建人: '周娜'
          },
          {
            id: '10',
            外键名称: 'PRICE',
            外键类型: '浮点型F',
            最小长度: 1,
            最大长度: 12,
            外键版本: 'V2.1',
            说明: '价格外键，支持小数点精度',
            状态: true,
            创建时间: '2024-03-15',
            创建人: '吴涛'
          },
          {
            id: '11',
            外键名称: 'COMPANY_CODE',
            外键类型: '字符型C',
            最小长度: 6,
            最大长度: 12,
            外键版本: 'V1.0',
            说明: '公司代码外键，关联企业信息表',
            状态: true,
            创建时间: '2024-03-20',
            创建人: '徐丽'
          },
          {
            id: '12',
            外键名称: 'REGION_ID',
            外键类型: '数值型N',
            最小长度: 2,
            最大长度: 4,
            外键版本: 'V1.1',
            说明: '区域编号外键，用于地理位置关联',
            状态: true,
            创建时间: '2024-03-25',
            创建人: '孙辉'
          },
          {
            id: '13',
            外键名称: 'CATEGORY_TYPE',
            外键类型: '字符型C',
            最小长度: 3,
            最大长度: 8,
            外键版本: 'V2.0',
            说明: '分类类型外键，产品分类标识',
            状态: false,
            创建时间: '2024-04-01',
            创建人: '朱红'
          },
          {
            id: '14',
            外键名称: 'SUPPLIER_NO',
            外键类型: '字符型C',
            最小长度: 8,
            最大长度: 16,
            外键版本: 'V1.2',
            说明: '供应商编号外键，关联供应商主数据',
            状态: true,
            创建时间: '2024-04-05',
            创建人: '何军'
          },
          {
            id: '15',
            外键名称: 'UPDATE_TIME',
            外键类型: '日期型T',
            最小长度: 19,
            最大长度: 19,
            外键版本: 'V1.0',
            说明: '更新时间外键，记录最后修改时间',
            状态: true,
            创建时间: '2024-04-10',
            创建人: '林峰'
          },
          {
            id: '16',
            外键名称: 'PRIORITY_LEVEL',
            外键类型: '数值型N',
            最小长度: 1,
            最大长度: 2,
            外键版本: 'V1.0',
            说明: '优先级外键，业务处理优先级标识',
            状态: true,
            创建时间: '2024-04-15',
            创建人: '郭亮'
          },
          {
            id: '17',
            外键名称: 'DISCOUNT_RATE',
            外键类型: '浮点型F',
            最小长度: 3,
            最大长度: 6,
            外键版本: 'V1.1',
            说明: '折扣率外键，支持百分比计算',
            状态: false,
            创建时间: '2024-04-20',
            创建人: '韩雪'
          },
          {
            id: '18',
            外键名称: 'APPROVAL_STATUS',
            外键类型: '逻辑型L',
            最小长度: 1,
            最大长度: 1,
            外键版本: 'V2.0',
            说明: '审批状态外键，流程审批标识',
            状态: true,
            创建时间: '2024-04-25',
            创建人: '冯超'
          },
          {
            id: '19',
            外键名称: 'REMARK_TEXT',
            外键类型: '备注型M',
            最小长度: 1,
            最大长度: 1000,
            外键版本: 'V1.0',
            说明: '备注文本外键，详细说明信息',
            状态: true,
            创建时间: '2024-05-01',
            创建人: '曹丽'
          },
          {
            id: '20',
            外键名称: 'BRANCH_CODE',
            外键类型: '字符型C',
            最小长度: 4,
            最大长度: 10,
            外键版本: 'V1.3',
            说明: '分支机构代码外键，组织架构关联',
            状态: true,
            创建时间: '2024-05-05',
            创建人: '彭飞'
          }
        ]
        this.saveToStorage()
      } else {
        // 使用存储的数据，但版本匹配
        this.foreignKeys = JSON.parse(stored)
      }
    },

    // 强制重新初始化数据（用于清除旧的假数据）
    forceReinitializeData() {
      // 清除旧数据
      localStorage.removeItem('foreignKeyDefinitions')
      localStorage.removeItem('foreignKeyDefinitionsVersion')
      // 重新初始化
      this.initializeData()
    },

    // 保存到本地存储
    saveToStorage() {
      localStorage.setItem('foreignKeyDefinitions', JSON.stringify(this.foreignKeys))
      localStorage.setItem('foreignKeyDefinitionsVersion', this.dataVersion)
    },

    // 新增外键定义
    addForeignKey(foreignKey: Omit<ForeignKeyDefinition, 'id' | '创建时间' | '创建人'>) {
      const newForeignKey: ForeignKeyDefinition = {
        ...foreignKey,
        id: Date.now().toString(),
        创建时间: new Date().toISOString().split('T')[0],
        创建人: generateRealisticName()
      }
      this.foreignKeys.push(newForeignKey)
      this.saveToStorage()
      return newForeignKey
    },

    // 更新外键定义
    updateForeignKey(id: string, updates: Partial<ForeignKeyDefinition>) {
      const index = this.foreignKeys.findIndex(item => item.id === id)
      if (index !== -1) {
        this.foreignKeys[index] = { ...this.foreignKeys[index], ...updates }
        this.saveToStorage()
        return true
      }
      return false
    },

    // 删除外键定义
    deleteForeignKey(id: string) {
      const index = this.foreignKeys.findIndex(item => item.id === id)
      if (index !== -1) {
        this.foreignKeys.splice(index, 1)
        this.saveToStorage()
        return true
      }
      return false
    },

    // 切换状态
    toggleStatus(id: string) {
      const foreignKey = this.foreignKeys.find(item => item.id === id)
      if (foreignKey) {
        foreignKey.状态 = !foreignKey.状态
        this.saveToStorage()
        return foreignKey.状态 // 返回新的状态值
      }
      return null // 返回null表示操作失败
    },

    // 初始化业务表数据
    initializeBusinessTables() {
      const stored = localStorage.getItem('businessTables')
      if (stored) {
        this.businessTables = JSON.parse(stored)
      } else {
        // 初始化业务表数据
        this.businessTables = [
          { id: '1', 表名: 'user_info', 表描述: '用户信息表', 表类型: '主表', 创建时间: '2024-01-01' },
          { id: '2', 表名: 'order_main', 表描述: '订单主表', 表类型: '主表', 创建时间: '2024-01-01' },
          { id: '3', 表名: 'product_info', 表描述: '产品信息表', 表类型: '主表', 创建时间: '2024-01-01' },
          { id: '4', 表名: 'department', 表描述: '部门组织表', 表类型: '主表', 创建时间: '2024-01-01' },
          { id: '5', 表名: 'customer_master', 表描述: '客户主数据表', 表类型: '主表', 创建时间: '2024-01-01' },
          { id: '6', 表名: 'supplier_info', 表描述: '供应商信息表', 表类型: '主表', 创建时间: '2024-01-01' },
          { id: '7', 表名: 'company_info', 表描述: '企业信息表', 表类型: '主表', 创建时间: '2024-01-01' },
          { id: '8', 表名: 'region_info', 表描述: '地区信息表', 表类型: '主表', 创建时间: '2024-01-01' },
          { id: '9', 表名: 'category_manage', 表描述: '分类管理表', 表类型: '主表', 创建时间: '2024-01-01' },
          { id: '10', 表名: 'finance_subject', 表描述: '财务科目表', 表类型: '主表', 创建时间: '2024-01-01' }
        ]
        this.saveBusinessTablesToStorage()
      }
    },

    // 初始化外键业务表关联数据
    initializeForeignKeyBusinessRelations() {
      const stored = localStorage.getItem('foreignKeyBusinessRelations')
      if (stored) {
        this.foreignKeyBusinessRelations = JSON.parse(stored)
      } else {
        // 初始化一些示例关联数据
        this.foreignKeyBusinessRelations = [
          {
            id: '1',
            外键ID: '1',
            外键名称: 'USER_ID',
            业务表ID: '1',
            业务表名称: 'user_info',
            关联字段: 'id',
            关联类型: 'one_to_one',
            创建时间: '2024-01-15',
            创建人: '李明'
          },
          {
            id: '2',
            外键ID: '2',
            外键名称: 'ORDER_NO',
            业务表ID: '2',
            业务表名称: 'order_main',
            关联字段: 'order_number',
            关联类型: 'one_to_many',
            创建时间: '2024-01-20',
            创建人: '王芳'
          }
        ]
        this.saveForeignKeyBusinessRelationsToStorage()
      }
    },

    // 保存业务表到本地存储
    saveBusinessTablesToStorage() {
      localStorage.setItem('businessTables', JSON.stringify(this.businessTables))
    },

    // 保存外键业务表关联到本地存储
    saveForeignKeyBusinessRelationsToStorage() {
      localStorage.setItem('foreignKeyBusinessRelations', JSON.stringify(this.foreignKeyBusinessRelations))
    },

    // 创建外键与业务表关联
    createForeignKeyBusinessRelation(relation: Omit<ForeignKeyBusinessTableRelation, 'id' | '创建时间' | '创建人'>) {
      const newRelation: ForeignKeyBusinessTableRelation = {
        ...relation,
        id: Date.now().toString(),
        创建时间: new Date().toISOString().split('T')[0],
        创建人: generateRealisticName()
      }
      this.foreignKeyBusinessRelations.push(newRelation)
      this.saveForeignKeyBusinessRelationsToStorage()
      return newRelation
    },

    // 删除外键与业务表关联
    deleteForeignKeyBusinessRelation(id: string) {
      const index = this.foreignKeyBusinessRelations.findIndex(item => item.id === id)
      if (index !== -1) {
        this.foreignKeyBusinessRelations.splice(index, 1)
        this.saveForeignKeyBusinessRelationsToStorage()
        return true
      }
      return false
    },

    // 获取外键的业务表关联
    getForeignKeyBusinessRelations(foreignKeyId: string) {
      return this.foreignKeyBusinessRelations.filter(item => item.外键ID === foreignKeyId)
    }
  }
})
