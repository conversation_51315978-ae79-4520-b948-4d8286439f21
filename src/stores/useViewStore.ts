import {defineStore} from 'pinia'

// 根据业务需求定义独立的 Store, 单独在组件按需加载使用, 避免混合使用
// 异步页面组件切换/缓存处理

export const useViewStore = defineStore('useViewStore', {
	state: () => {
		return {
			times: +new Date(),
			frame: true, // 是否显示框架 UI
			isYKZFrame: false,
			// 当前页面
			current: '',
			viewId: JSON.parse(localStorage.getItem('viewId') || '-1'),
			viewIndex: Number(JSON.parse(localStorage.getItem('viewIndex') || '-1')),
			labels: JSON.parse(localStorage.getItem('labels') || '[]'),
			outlink: JSON.parse(localStorage.getItem('outlink') || '""'),
			storage: JSON.parse(
				localStorage.getItem('currentView') ||
					`{"name":"homeIndex","module":"home","page":"index"}`
			),
			// 不需要缓存的页面末尾加入-exclude
			exclude: /^((?!detail|Detail).)*$/,
			// 已全局注册过的页面组件, 避免重复注册
			register: [] as string[],
			// 切换页面组件时需要清理上个页面未完成的请求
			aborts: null as AbortController | null,

			loadCount: {finish: 0, count: 0} as any,
			loadFinish: false,
			enableLoading: true,
			visibleLoading: false,
			LDF_RULE: {},
			// 菜单点击跳转时, 用于判断是否是 iframe 跳转
			// 如果是 iframe 跳转, 则不清理未完成的请求
			isIframe: false,

			isBuild: true,

			permissions: [] as any[],

			mScroll: null,
		}
	},
	getters: {
		getFrame: (state) => state.frame,
		getLabels: (state) => state.labels,
		getLoadCount: (state) => state.loadCount,
		getLoadFinish: (state) => state.loadFinish,
		getBuild: (state) => state.isBuild,
		getCheckRegister: (state) => (name: string) => state.register.some((key) => key === name),
		getPermissions: (state) => state.permissions,
		getEnableLoading: (state) => state.enableLoading,
		getMScroll: (state) => state.mScroll,
	},
	actions: {
		enableProgressBar(enable: boolean) {
			this.enableLoading = enable
		},
		clear() {
			// localStorage.clear()
			// 此处是为了保持当前人的登录状态。
			const keepFieldName = 'departmentforStreet'
			const keepFieldValue = localStorage.getItem(keepFieldName)
			localStorage.clear()
			// 重新设置之前保留的字段
			if (keepFieldValue !== null) {
				localStorage.setItem(keepFieldName, keepFieldValue)
			}
			// localStorage.removeItem('currentView')
			// localStorage.removeItem('outlink')
			// localStorage.removeItem('labels')
			// localStorage.removeItem('viewIndex')
			// localStorage.removeItem('viewId')
			this.register = []
			this.current = ''
			this.labels = []
			this.viewId = ''
			this.viewIndex = -1
			this.outlink = ''
			this.storage = {}
			this.permissions = []
		},
		// 清除请求
		clearAbort() {
			this.aborts?.abort()
			this.aborts = null
		},
		// 缓存本地
		save(name: string, module: string, page: string) {
			this.storage = {name, module, page}
			localStorage.setItem('currentView', JSON.stringify({name, module, page}))
		},
		saveViewId(id: string) {
			this.viewId = id
			localStorage.setItem('viewId', JSON.stringify(this.viewId))
		},
		saveOutlink(outlink: string) {
			this.outlink = outlink
			localStorage.setItem('outlink', JSON.stringify(this.outlink))
		},
		saveViewIndex(index: number) {
			this.viewIndex = index === undefined ? '-1' : index
			localStorage.setItem('viewIndex', JSON.stringify(this.viewIndex))
		},

		toggleView(name: string, module: string, page: string) {
			this.current = name
			// 缓存本地
			this.save(name, module, page)
		},

		addView(name: string, module: string, page: string) {
			if (!this.getCheckRegister(name)) {
				this.register.push(name)
			}
			this.toggleView(name, module, page)
		},

		saveLabels(lables: any) {
			this.labels = lables
			localStorage.setItem('labels', JSON.stringify(lables))
		},

		setLoadFinish(finish: boolean) {
			this.loadFinish = finish
		},
		setLoadCount(str: string, count?: number) {
			if (count === 0) {
				this.loadCount.finish = 0
				this.loadCount.count = 0
			} else {
				this.loadCount[str] += 1
			}
		},

		setBuildStats(stats: boolean) {
			this.isBuild = stats
		},

		savePermissions(p: any) {
			this.permissions = p
		},
		setFrame(stats: boolean) {
			this.frame = stats
		},

		setMScroll(scroll: any) {
			this.mScroll = scroll
		},
		setRule(data: any) {
			this.LDF_RULE = data
		},
	},
})
