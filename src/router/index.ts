import {createRouter, createWebHistory} from 'vue-router'
import {useViewStore} from '../stores/useViewStore'
import {useUserStore} from '@/stores/useUserStore'
import routes from 'virtual:generated-pages'

routes.unshift({
	path: '/',
	redirect: (to: any) => {
		if (to.query.free) {
			return `/index${'?free=' + to.query.free}`
		}

		if (to.query.authUserId) {
			return `/ledgerConfig${'?authUserId=' + to.query.authUserId}`
		}

		if (to.query.access_token) {
			return `/ledgerConfig${'?access_token=' + to.query.access_token}`
		}

		if (to.query.refresh_token) {
			return `/ledgerConfig${'?refresh_token=' + to.query.refresh_token}`
		}

		if (to.query.accountid) {
			return `/ledgerConfig${'?accountid=' + to.query.accountid}`
		}

		if (to.query.auth_code) {
			return `/ledgerConfig${'?auth_code=' + to.query.auth_code}`
		}

		return '/ledgerConfig'
	},
})

console.log('routes', routes)

export const router = createRouter({
	// 基于浏览器 URL 的 hash 路由模式 /#/home
	// history: createWebHashHistory(),
	// 使用 createWebHistory 需要服务配置所有的路由请求都返回首页, 再由前端代码进行路由的匹配和处理
	history: createWebHistory(),
	routes,
})

router.beforeEach((to, from, next) => {
	console.log('路由', to, from)

	let store = useViewStore()
	if (store.aborts) {
		store.clearAbort()
	}

	if (to.query.build) {
		store.setBuildStats(to.query.build === 'true')
	}

	const isAuthenticated = !!useUserStore().getToken
	const backPath = import.meta.env.DEV ? '/login' : '/401'

	console.log('isAuthenticated', isAuthenticated)
	// 免登需携带的参数
	if (
		to.query.authUserId ||
		to.query.access_token ||
		to.query.refresh_token ||
		to.query.accountid ||
		to.query.auth_code ||
		to.query.free
	) {
		// 免登录
		next()
	} else if (to.path === backPath) {
		// 登录页
		if (isAuthenticated) {
			next('/')
		} else {
			next()
		}
	} else {
		// 其他页面
		if (isAuthenticated) {
			next()
		} else {
			next(backPath)
		}
	}
})
