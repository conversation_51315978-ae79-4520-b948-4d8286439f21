/// <reference types="vite/client" />

// Vue JSX 类型定义
declare global {
  namespace JSX {
    interface Element extends VNode {}
    interface ElementClass extends ComponentRenderProxy {}
    interface ElementAttributesProperty {
      $props: {}
    }
    interface IntrinsicElements {
      [elem: string]: any
    }
  }
}

declare module 'qs'
declare module '~pages'
declare module 'virtual:generated-pages'
declare module 'element-plus/dist/locale/zh-cn.mjs'
declare module '@kangc/v-md-editor/lib/preview'
declare module '@kangc/v-md-editor/lib/theme/github.js'
declare module '@kangc/v-md-editor/lib/theme/vuepress.js'
declare module 'event-source-polyfill'
declare module 'akvts'
declare module 'akvts/src/directive'
declare module 'file-saver'
declare module '@kangc/v-md-editor'

interface Window {
	luckysheet: any
}
