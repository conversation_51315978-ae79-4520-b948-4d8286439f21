.base-table-comp {
	.header {
		user-select: none;
		overflow: hidden;

		> .layout {
			align-items: center;
			border-radius: 5px;
			display: flex;
			border: var(--z-border);
			background: rgba(243, 243, 243, 1);
			height: 35px;
			padding: 0 10px;
			margin-bottom: 10px;
		}

		a {
			font-size: 13px;
		}

		.slot {
			flex: 1;
			text-align: right;
		}

		.header-tip {
			align-items: center;
			color: rgb(103, 103, 242);
			display: flex;

			a,
			> i,
			> span {
				margin: 0 10px 0 0;
			}
		}

		.setting {
			button {
				margin-left: 12px;
			}
		}

		.icon {
			font-size: 14px;
			margin-right: 3px;
		}

		&.notStyle {
			> .layout {
				border: none;
				background: transparent;
				height: 0;
				padding: 0;
				margin: 0;
			}
		}
	}

	.pages {
		display: flex;
		justify-content: flex-end;
		// padding: 0 10px 10px 10px;
	}

	.unoicon {
		margin-right: 0;
	}

	.setting {
		i {
			font-size: 16px !important;
		}
	}

	.header .el-checkbox-group {
		flex-wrap: wrap !important;
		.el-checkbox {
			margin-bottom: 10px;
		}
	}
}
