@keyframes barAnimtion {
	0% {
		left: -50px;
	}

	100% {
		left: 100%;
	}
}

.progress-bar-comp {
	user-select: none;
	position: fixed;
	opacity: 0;
	visibility: hidden;
	transition: opacity 0.2s linear;
	z-index: -1;

	&.begin {
		background: var(--z-theme);
		height: 100%;
		opacity: 1;
		visibility: visible;
		left: 0;
		top: 0;
		width: 100%;
		z-index: 3995;
	}

	&.end-before {
		opacity: 0;
	}

	.message {
		color: var(--z-theme);
		height: 25px;
		position: fixed;
		right: calc(50% - 125px);
		top: calc(50% - 40.5px);
		width: 250px;
	}

	.percentage {
		background: var(--z-theme);
		box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.24);
		border: var(--z-line);
		border-radius: 50px;
		display: flex;
		height: 15px;
		overflow: hidden;
		position: fixed;
		right: calc(50% - 125px);
		top: calc(50% - 14.5px);
		width: 250px;
		z-index: 3994;

		.bar {
			border-radius: 15px;
			background: var(--z-main);
			height: 15px;
			overflow: hidden;
			position: absolute;
			margin-top: -1px;
			width: 0;
		}

		.bar-animtion {
			background: linear-gradient(to left, transparent, rgba(255, 255, 255, 0.1), transparent);
			height: 100%;
			width: 50px;
			position: absolute;
			animation: barAnimtion 1.5s ease-in-out infinite;
		}

		.txt {
			align-items: center;
			display: flex;
			color: var(--z-main);
			line-height: 12px;
			padding: 0 5px;
			margin: 0 auto;
			position: relative;
			transition: all 0.3s linear;

			&.half {
				color: var(--z-theme);
			}
		}
	}
}
