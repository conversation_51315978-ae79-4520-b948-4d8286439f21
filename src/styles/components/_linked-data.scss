.linked-data-comp {
	border-radius: 5px;
	border: var(--z-border);
	user-select: none;
	display: flex;
	height: 100%;
	margin-right: 10px;
	overflow: hidden;

	.left {
		height: 100%;
		width: 100%;
	}

	.search {
		align-items: center;
		border-bottom: var(--z-border);
		display: flex;
		padding: 10px;
		white-space: nowrap;

		span {
			font-size: 13px;
			i {
				margin-top: -1px;
			}
		}
	}

	.linke-title {
		display: flex;
	}

	.item {
		position: relative;

		.svg-set {
			background: #efefef;
			color: #666;
			cursor: pointer;
			height: 40px;
			line-height: 44px;
			position: absolute;
			right: 0;
			top: 0;
			text-align: center;
			width: 40px;
			svg {
				transition: all 0.3s linear;
			}
			&:hover {
				svg {
					transform: scale(1.4) rotate(90deg);
				}
			}
		}
	}

	.list {
		height: 100%;

		> div {
			border-bottom: var(--z-border);
			cursor: pointer;
			> label {
				background-color: #fafafa;
				height: 40px;
				padding: 5px 10px;
				// margin-top: -1px;
				width: 100%;
			}
		}

		.el-checkbox__input {
			position: relative;
			top: -0.5px;
		}

		.el-checkbox__label {
			display: flex;
			overflow: hidden;
			width: calc(100% - 50px);
			i {
				color: #f00;
				font-size: 15px;
				margin-left: 10px;
			}
			span {
				overflow: hidden;
				text-overflow: ellipsis;
				width: 100%;
			}
		}
	}

	.child {
		opacity: 0;
		transition: all 0.15s ease-in-out;
		overflow: hidden;
		max-height: 0;

		&.open {
			background-color: #fefefe;
			opacity: 1;
		}

		.item {
			align-items: center;
			border-top: 1px solid transparent;
			border-bottom: 1px solid transparent;
			cursor: move;
			color: #333;
			display: flex;
			height: 40px;
			line-height: 40px;
			margin-top: -1px;
			text-indent: 55px;
			position: relative;
			white-space: nowrap;
			transition: all 0.3s linear;

			&.linked {
				background-color: #faecd8;
				border-top-color: #e8d0af;
				border-bottom-color: #e8d0af;
			}

			strong {
				height: 40px;
				position: absolute;
				left: -40px;
			}

			span {
				border-left: 5px solid transparent;
				height: 40px;
				font-size: 13px;
				flex: 1;
				max-width: calc(100% - 5px);
				overflow: hidden;
				text-overflow: ellipsis;
			}

			i {
				cursor: pointer;
				color: var(--z-border);
				font-size: 18px;
				height: 40px;
				position: absolute;
				top: -2px;
				text-indent: 0;
				text-align: center;
				transition: all 0.3s linear;
				right: 0;
				width: 40px;

				&:hover {
					color: var(--z-main);
					transform: scale(1.2);
				}
			}

			&:hover {
				border-top: 1px solid #e8d0af;
				transform: translate3d(0, 1px, 0);
				background-color: #f8e3c5;
			}

			.tj {
				border-left: 5px solid #f78989;
			}
			.js {
				border-left: 5px solid #66b1ff;
			}
		}
	}
	.excel-comp {
		.title {
			padding: 5px !important;
		}

		.sheet-main {
			height: calc(100% - 40px) !important;
		}
	}

	.excel_tips {
		i {
			margin-right: 5px;
		}

		align-items: center;
		display: flex;
		font-size: 12px;
	}
}
