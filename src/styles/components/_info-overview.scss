.info-overview-comp {
	border-radius: 5px;
	color: var(--z-font-color);
	display: flex;
	flex-wrap: wrap;
	// overflow: hidden;
	border-top: 1px solid var(--z-line);
	border-left: 1px solid var(--z-line);
	position: relative;
	transition: all 0.15s ease-in-out;
	z-index: 1;

	> div {
		background-color: #fff;
		border-right: 1px solid var(--z-line);
		border-bottom: 1px solid var(--z-line);
		transition: all 0.15s ease-in-out;
		overflow: hidden;

		> div:nth-child(1) {
			background: var(--z-bg-secondary);
			border-right: 1px solid var(--z-line);
		}
	}

	&.show {
		border-color: #f1f1f1;
		height: 0 !important;

		> div {
			height: 0 !important;
			overflow: hidden;
			opacity: 0;
		}

		.switch {
			border-radius: 4px;
			i {
				rotate: 180deg;
			}
		}
	}

	.switch {
		align-items: center;
		border: var(--z-border);
		background-color: #fff;
		border-radius: 0 0 4px 4px;
		bottom: -25px;
		cursor: pointer;
		display: flex;
		height: 26px;
		right: 0;
		justify-content: center;
		opacity: 1;
		position: absolute;
		padding-left: 5px;
		transition: all 0.15s linear;
		width: 56px;
		white-space: nowrap;

		i {
			color: rgba(33, 61, 91, 0.5);
			font-size: 16px;
			transition: all 0.15s linear;
		}
	}
}
