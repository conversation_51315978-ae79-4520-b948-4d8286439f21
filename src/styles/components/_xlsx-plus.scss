.xlsx-plus {
	display: flex;
	flex-wrap: wrap;
	height: 100%;
	overflow: hidden;
	padding: 10px 0;
	position: relative;
	width: 100%;

	.header {
		align-items: center;
		flex-grow: 1;
		display: flex;
		height: 40px;
		width: 100%;
	}

	.aside {
		height: calc(100% - 40px);
		overflow: hidden;
		width: 250px;
	}

	.sheet-main {
		overflow: auto;

		&::-webkit-scrollbar {
			width: 0px;
			height: 0px;
		}
	}

	.file {
		cursor: pointer;
		height: 400px;
		top: -9999px;
		position: absolute;
		left: -9999px;
		width: 400px;
		opacity: 0.01;
		z-index: 1;
	}

	.input-file {
		input {
			cursor: pointer;
		}

		height: 24px;
		position: relative;
		overflow: hidden !important;
		width: 70px;
		z-index: 2;
	}

	.unoicon {
		cursor: pointer;
		height: 24px !important;
		margin-right: 0;
	}

	.slot {
		align-items: center;
		display: flex;
		height: 24px;
		line-height: 24px;

		&:nth-child(1) {
			padding-right: 10px;
			justify-content: flex-start;
		}
		&:nth-child(2) {
			flex: 1;
			padding-right: 10px;
			justify-content: flex-start;
		}
		&:nth-child(3) {
			flex: 15;
			justify-content: flex-end;
		}

		button {
			width: 80px;
		}
	}

	.auto-save {
		box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.12);
		border-radius: 0 5px 5px 0;
		background-color: #fff;
		height: 28px;
		left: -100%;
		opacity: 0;
		position: absolute;
		top: 150px;
		transition: all 0.3s linear;
		z-index: -1;

		&.on {
			opacity: 1;
			left: 1px;
			z-index: 1998;
		}

		button {
			border-radius: 0 5px 5px 0;
		}
	}
}
