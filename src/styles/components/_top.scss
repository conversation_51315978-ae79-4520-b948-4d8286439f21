.top-comp {
	align-items: center;
	background: #fafafa;
	// box-shadow: -6px -6px 6px -6px rgba(0, 0, 0, 0.05) inset;
	// box-shadow: 0 1px 5px #0003, 0 2px 2px #00000024, 0 3px 1px -2px #0000001f;
	border-bottom: var(--z-border);
	display: flex;

	height: 40px;
	padding: 0 10px;
	// margin: 0 -10px;
	margin-bottom: 10px;
	// user-select: none;
	position: relative;
	z-index: 2;

	h1 {
		font-size: 16px;
	}

	.left {
		align-items: center;
		display: flex;

		.label {
			align-items: center;
			color: #333;
			display: flex;
			font-weight: 500;
			font-size: 14px;
			line-height: 1;
			white-space: nowrap;
		}

		.back {
			border-right: 2px solid #e9e9e9;
			margin-right: 20px;
			position: relative;
			font-size: 14px;
			line-height: 1;
			padding-right: 10px;
			text-decoration: none;
			white-space: nowrap;
		}
	}

	.center {
		flex: 1;
		margin: 0 20px;
	}

	.search {
		.icon {
			font-size: 14px;
			margin-right: 3px;
		}
	}
}
