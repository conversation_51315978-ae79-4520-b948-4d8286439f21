@keyframes LabelAdd {
	from {
		opacity: 0;
		padding: 0;
		width: 0;
	}

	to {
		opacity: 1;
		padding: 0 20px;
		width: 10%;
	}
}

@keyframes LabelClose {
	from {
		opacity: 1;
		padding: 0 20px;
		width: 10%;
	}

	to {
		opacity: 0;
		padding: 0;
		width: 0;
	}
}

.lables-comp {
	align-items: flex-end;
	// box-shadow: -12px -12px 24px -24px rgba(0, 0, 0, 0.34) inset;
	// background: $labelsBg;
	// border-bottom: $labelsBorderColor;
	display: flex;
	font-size: 13px;
	height: 40px;
	padding: 0 0 0 40px;
	user-select: none;

	.item {
		align-items: center;
		border-radius: 5px 5px 0 0;
		background-color: #f1f1f1;
		border: 1px solid #dcdfe6;
		color: #666;
		cursor: pointer;
		display: flex;
		height: 30px;
		position: relative;
		padding: 0;
		// overflow: hidden;
		top: 1px;
		animation: LabelAdd 0.3s linear forwards;
		transition: all 0.2s linear;
		width: 0;
		z-index: 1;

		&:hover {
			.close {
				opacity: 1;
			}
		}

		&:nth-child(1) {
			overflow: hidden !important;
			opacity: 0 !important;
			padding: 0 !important;
			width: 0px !important;
		}

		&:nth-child(2) {
			width: 96px !important;
		}

		&:not(:first-child) {
			margin-left: -1px;
		}

		&.active {
			border-bottom: 1px solid transparent;
			background-color: #fafafa;
			box-shadow: 1.5px 1.5px 6px 0 rgba(0, 0, 0, 0.12);
			color: #333;
			font-weight: 500;
			height: 32px;
			z-index: 2;

			.close {
				top: calc(50% - 9.5px);
			}
		}

		&.drag {
			animation: LabelClose 0.3s linear forwards;
		}

		&.enter {
			opacity: 0.2 !important;
		}

		&.delete {
			animation: LabelClose 0.3s linear forwards;
		}
	}

	.title {
		flex: 1;
		overflow: hidden;
		position: relative;
		top: 1px;
		text-overflow: ellipsis;
		text-align: left;
		white-space: nowrap;
	}

	.close {
		font-size: 16px;
		line-height: 1;
		right: 10px;
		opacity: 0;
		position: absolute;
		top: calc(50% - 8.5px);
		transition: all 0.1s linear;

		&:hover {
			transform: scale(1.2) translate(0, 0);
			transform-origin: center center;
		}
	}
}

.virtual {
	align-items: center;
	border-radius: 5px 5px 0 0;
	border: 2px dashed #b3a8ea;
	background-color: rgba($color: #b3a8ea, $alpha: 0.5);
	box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.24);
	display: flex;
	height: 29px;
	margin: 0 1px 0 0;
	padding: 0 5px;
	position: absolute;
	width: 0px;
	z-index: 2;
	span {
		color: #8c79e9;
		font-weight: 600;
	}
	span:nth-child(2) {
		display: none;
	}
}
