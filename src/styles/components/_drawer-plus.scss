.drawer-plus-comp {
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	flex-direction: column;
	height: 100%;
	left: 0;
	opacity: 0;
	overflow: hidden;
	position: fixed;
	top: 0;
	transition: all 0.3s linear;
	width: 100%;
	visibility: hidden;
	z-index: 14;

	&.open {
		visibility: visible;
		opacity: 1;
	}

	:deep(.luckysheet) {
		max-width: 100%;
	}

	.drawer-layout {
		display: flex;
		flex-wrap: wrap;
		box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
		background-color: #fff;
		bottom: 0;
		position: absolute;
		top: 0;
		transition: all 0.2s linear;
		right: -100%;
		width: 70%;

		&.open {
			right: 0;
		}
	}

	.drawer-header {
		align-items: center;
		border-bottom: 1px solid #eee;
		display: flex;
		height: 40px;
		padding: 0 10px;
		width: 100%;

		.slot {
			flex: 1;
		}

		h1 {
			font-size: 14px;
		}

		.close {
			cursor: pointer;
			height: 40px;
			line-height: 40px;
			font-size: 18px;
			width: 40px;
			transition: all 0.2s linear;
			text-align: center;

			&:hover {
				transform: scale(1.4);
			}
		}
	}

	.drawer-content {
		height: calc(100% - 40px);
		width: 100%;

		.xlsx-plus {
			padding: 0;
		}
	}
}
