@charset "UTF-8";
.luckysheet-rows-h-selected,
.luckysheet-rows-h-hover {
  transform: translate(0px, -12px);
}

input::-moz-placeholder, textarea::-moz-placeholder {
  font-size: 13px;
}

input::placeholder,
textarea::placeholder {
  font-size: 13px;
}

/* 滚动条整体 */
::-webkit-scrollbar {
  width: 10px; /* 设置滚动条宽度 */
  height: 10px; /* 横向滚动条的高度 */
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: transparent; /* 滚动条轨道背景色 */
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background-color: #cbcbcb; /* 滑块的背景色 */
  border-radius: 10px; /* 滑块圆角 */
  border: 3px solid transparent; /* 透明边框，用于给滑块周围留空间 */
  background-clip: content-box; /* 限制背景颜色只显示在内容区域 */
}

/* 滑块在 hover 时的样式 */
::-webkit-scrollbar-thumb:hover {
  background-color: #cbcbcb; /* 滑块 hover 状态下的背景色 */
}

.luckysheet-protection-sheet-validation {
  border: none;
  border-radius: 5px;
  padding: 0;
  overflow: hidden;
}
.luckysheet-protection-sheet-validation .luckysheet-modal-dialog-title {
  background-color: var(--z-main);
  color: #fff;
  padding: 10px;
}
.luckysheet-protection-sheet-validation .luckysheet-modal-dialog-title .luckysheet-modal-dialog-title-close {
  right: 8px;
  padding: 0;
  top: 10px;
  width: auto;
}
.luckysheet-protection-sheet-validation .luckysheet-slider-protection-column {
  align-items: center;
  display: flex;
  justify-content: center;
}
.luckysheet-protection-sheet-validation .luckysheet-protection-rangeItemiInput {
  display: none;
}
.luckysheet-protection-sheet-validation .luckysheet-slider-protection-row {
  margin-top: 0 !important;
}
.luckysheet-protection-sheet-validation .luckysheet-modal-dialog-buttons {
  display: flex;
  justify-content: center;
}
.luckysheet-protection-sheet-validation .luckysheet-modal-dialog-buttons button {
  margin-right: 0;
  margin-bottom: 20px;
}

.top-comp {
  align-items: center;
  background: #fafafa;
  border-bottom: var(--z-border);
  display: flex;
  height: 40px;
  padding: 0 10px;
  margin-bottom: 10px;
  position: relative;
  z-index: 2;
}
.top-comp h1 {
  font-size: 16px;
}
.top-comp .left {
  align-items: center;
  display: flex;
}
.top-comp .left .label {
  align-items: center;
  color: #333;
  display: flex;
  font-weight: 500;
  font-size: 14px;
  line-height: 1;
  white-space: nowrap;
}
.top-comp .left .back {
  border-right: 2px solid #e9e9e9;
  margin-right: 20px;
  position: relative;
  font-size: 14px;
  line-height: 1;
  padding-right: 10px;
  text-decoration: none;
  white-space: nowrap;
}
.top-comp .center {
  flex: 1;
  margin: 0 20px;
}
.top-comp .search .icon {
  font-size: 14px;
  margin-right: 3px;
}

.left-menu {
  background: var(--i-color-lightgray);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  min-height: 100%;
}
.left-menu .unoicon i {
  font-size: 18px !important;
  position: relative;
  top: -1px;
}
.left-menu ul {
  background-color: #fafafa;
}
.left-menu .el-sub-menu__title,
.left-menu .el-sub-menu.is-active .el-sub-menu__title {
  align-items: center;
  border-top: var(--z-border);
  border-bottom: var(--z-border);
  border-color: #eeeeee;
  display: flex;
  font-weight: 500;
  margin-top: -1px;
}
.left-menu .el-sub-menu__title:hover,
.left-menu .el-sub-menu.is-active .el-sub-menu__title:hover {
  background: #f9f9f9;
}
.left-menu .el-sub-menu__title span,
.left-menu .el-sub-menu.is-active .el-sub-menu__title span {
  height: 50px;
  line-height: 52px;
}

.base-table-comp .header {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  overflow: hidden;
}
.base-table-comp .header > .layout {
  align-items: center;
  border-radius: 5px;
  display: flex;
  border: var(--z-border);
  background: rgb(243, 243, 243);
  height: 35px;
  padding: 0 10px;
  margin-bottom: 10px;
}
.base-table-comp .header a {
  font-size: 13px;
}
.base-table-comp .header .slot {
  flex: 1;
  text-align: right;
}
.base-table-comp .header .header-tip {
  align-items: center;
  color: rgb(103, 103, 242);
  display: flex;
}
.base-table-comp .header .header-tip a,
.base-table-comp .header .header-tip > i,
.base-table-comp .header .header-tip > span {
  margin: 0 10px 0 0;
}
.base-table-comp .header .setting button {
  margin-left: 12px;
}
.base-table-comp .header .icon {
  font-size: 14px;
  margin-right: 3px;
}
.base-table-comp .header.notStyle > .layout {
  border: none;
  background: transparent;
  height: 0;
  padding: 0;
  margin: 0;
}
.base-table-comp .pages {
  display: flex;
  justify-content: flex-end;
}
.base-table-comp .unoicon {
  margin-right: 0;
}
.base-table-comp .setting i {
  font-size: 16px !important;
}
.base-table-comp .header .el-checkbox-group {
  flex-wrap: wrap !important;
}
.base-table-comp .header .el-checkbox-group .el-checkbox {
  margin-bottom: 10px;
}

.drawer-comp .el-drawer__header {
  background: var(--i-color-lightgray) !important;
}
.drawer-comp .el-drawer__header span {
  font-size: 15px;
}

.drawer-plus-comp {
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  height: 100%;
  left: 0;
  opacity: 0;
  overflow: hidden;
  position: fixed;
  top: 0;
  transition: all 0.3s linear;
  width: 100%;
  visibility: hidden;
  z-index: 14;
}
.drawer-plus-comp.open {
  visibility: visible;
  opacity: 1;
}
.drawer-plus-comp :deep(.luckysheet) {
  max-width: 100%;
}
.drawer-plus-comp .drawer-layout {
  display: flex;
  flex-wrap: wrap;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
  background-color: #fff;
  bottom: 0;
  position: absolute;
  top: 0;
  transition: all 0.2s linear;
  right: -100%;
  width: 70%;
}
.drawer-plus-comp .drawer-layout.open {
  right: 0;
}
.drawer-plus-comp .drawer-header {
  align-items: center;
  border-bottom: 1px solid #eee;
  display: flex;
  height: 40px;
  padding: 0 10px;
  width: 100%;
}
.drawer-plus-comp .drawer-header .slot {
  flex: 1;
}
.drawer-plus-comp .drawer-header h1 {
  font-size: 14px;
}
.drawer-plus-comp .drawer-header .close {
  cursor: pointer;
  height: 40px;
  line-height: 40px;
  font-size: 18px;
  width: 40px;
  transition: all 0.2s linear;
  text-align: center;
}
.drawer-plus-comp .drawer-header .close:hover {
  transform: scale(1.4);
}
.drawer-plus-comp .drawer-content {
  height: calc(100% - 40px);
  width: 100%;
}
.drawer-plus-comp .drawer-content .xlsx-plus {
  padding: 0;
}

.form-comp .el-input {
  width: 100%;
}
.form-comp .el-form-item__label {
  white-space: nowrap;
}
.form-comp .el-form-item__label strong {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}
.form-comp .el-radio,
.form-comp .el-checkbox {
  background-color: #fff;
}
.form-comp .form-comp-btns {
  text-align: center;
  width: 100%;
}
.form-comp .form-comp-btns i {
  margin-right: 3px;
}
.form-comp .el-radio-group,
.form-comp .el-checkbox-group {
  flex-wrap: wrap !important;
}
.form-comp .el-radio-group .el-radio,
.form-comp .el-checkbox-group .el-radio {
  margin-right: 15px;
}
.form-comp .full {
  width: 100%;
}

@keyframes LabelAdd {
  from {
    opacity: 0;
    padding: 0;
    width: 0;
  }
  to {
    opacity: 1;
    padding: 0 20px;
    width: 10%;
  }
}
@keyframes LabelClose {
  from {
    opacity: 1;
    padding: 0 20px;
    width: 10%;
  }
  to {
    opacity: 0;
    padding: 0;
    width: 0;
  }
}
.lables-comp {
  align-items: flex-end;
  display: flex;
  font-size: 13px;
  height: 40px;
  padding: 0 0 0 40px;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.lables-comp .item {
  align-items: center;
  border-radius: 5px 5px 0 0;
  background-color: #f1f1f1;
  border: 1px solid #dcdfe6;
  color: #666;
  cursor: pointer;
  display: flex;
  height: 30px;
  position: relative;
  padding: 0;
  top: 1px;
  animation: LabelAdd 0.3s linear forwards;
  transition: all 0.2s linear;
  width: 0;
  z-index: 1;
}
.lables-comp .item:hover .close {
  opacity: 1;
}
.lables-comp .item:nth-child(1) {
  overflow: hidden !important;
  opacity: 0 !important;
  padding: 0 !important;
  width: 0px !important;
}
.lables-comp .item:nth-child(2) {
  width: 96px !important;
}
.lables-comp .item:not(:first-child) {
  margin-left: -1px;
}
.lables-comp .item.active {
  border-bottom: 1px solid transparent;
  background-color: #fafafa;
  box-shadow: 1.5px 1.5px 6px 0 rgba(0, 0, 0, 0.12);
  color: #333;
  font-weight: 500;
  height: 32px;
  z-index: 2;
}
.lables-comp .item.active .close {
  top: calc(50% - 9.5px);
}
.lables-comp .item.drag {
  animation: LabelClose 0.3s linear forwards;
}
.lables-comp .item.enter {
  opacity: 0.2 !important;
}
.lables-comp .item.delete {
  animation: LabelClose 0.3s linear forwards;
}
.lables-comp .title {
  flex: 1;
  overflow: hidden;
  position: relative;
  top: 1px;
  text-overflow: ellipsis;
  text-align: left;
  white-space: nowrap;
}
.lables-comp .close {
  font-size: 16px;
  line-height: 1;
  right: 10px;
  opacity: 0;
  position: absolute;
  top: calc(50% - 8.5px);
  transition: all 0.1s linear;
}
.lables-comp .close:hover {
  transform: scale(1.2) translate(0, 0);
  transform-origin: center center;
}

.virtual {
  align-items: center;
  border-radius: 5px 5px 0 0;
  border: 2px dashed #b3a8ea;
  background-color: rgba(179, 168, 234, 0.5);
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.24);
  display: flex;
  height: 29px;
  margin: 0 1px 0 0;
  padding: 0 5px;
  position: absolute;
  width: 0px;
  z-index: 2;
}
.virtual span {
  color: #8c79e9;
  font-weight: 600;
}
.virtual span:nth-child(2) {
  display: none;
}

@keyframes barAnimtion {
  0% {
    left: -50px;
  }
  100% {
    left: 100%;
  }
}
.progress-bar-comp {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  position: fixed;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s linear;
  z-index: -1;
}
.progress-bar-comp.begin {
  background: var(--z-theme);
  height: 100%;
  opacity: 1;
  visibility: visible;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 3995;
}
.progress-bar-comp.end-before {
  opacity: 0;
}
.progress-bar-comp .message {
  color: var(--z-theme);
  height: 25px;
  position: fixed;
  right: calc(50% - 125px);
  top: calc(50% - 40.5px);
  width: 250px;
}
.progress-bar-comp .percentage {
  background: var(--z-theme);
  box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.24);
  border: var(--z-line);
  border-radius: 50px;
  display: flex;
  height: 15px;
  overflow: hidden;
  position: fixed;
  right: calc(50% - 125px);
  top: calc(50% - 14.5px);
  width: 250px;
  z-index: 3994;
}
.progress-bar-comp .percentage .bar {
  border-radius: 15px;
  background: var(--z-main);
  height: 15px;
  overflow: hidden;
  position: absolute;
  margin-top: -1px;
  width: 0;
}
.progress-bar-comp .percentage .bar-animtion {
  background: linear-gradient(to left, transparent, rgba(255, 255, 255, 0.1), transparent);
  height: 100%;
  width: 50px;
  position: absolute;
  animation: barAnimtion 1.5s ease-in-out infinite;
}
.progress-bar-comp .percentage .txt {
  align-items: center;
  display: flex;
  color: var(--z-main);
  line-height: 12px;
  padding: 0 5px;
  margin: 0 auto;
  position: relative;
  transition: all 0.3s linear;
}
.progress-bar-comp .percentage .txt.half {
  color: var(--z-theme);
}

.xlsx-plus {
  display: flex;
  flex-wrap: wrap;
  height: 100%;
  overflow: hidden;
  padding: 10px 0;
  position: relative;
  width: 100%;
}
.xlsx-plus .header {
  align-items: center;
  flex-grow: 1;
  display: flex;
  height: 40px;
  width: 100%;
}
.xlsx-plus .aside {
  height: calc(100% - 40px);
  overflow: hidden;
  width: 250px;
}
.xlsx-plus .sheet-main {
  overflow: auto;
}
.xlsx-plus .sheet-main::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
.xlsx-plus .file {
  cursor: pointer;
  height: 400px;
  top: -9999px;
  position: absolute;
  left: -9999px;
  width: 400px;
  opacity: 0.01;
  z-index: 1;
}
.xlsx-plus .input-file {
  height: 24px;
  position: relative;
  overflow: hidden !important;
  width: 70px;
  z-index: 2;
}
.xlsx-plus .input-file input {
  cursor: pointer;
}
.xlsx-plus .unoicon {
  cursor: pointer;
  height: 24px !important;
  margin-right: 0;
}
.xlsx-plus .slot {
  align-items: center;
  display: flex;
  height: 24px;
  line-height: 24px;
}
.xlsx-plus .slot:nth-child(1) {
  padding-right: 10px;
  justify-content: flex-start;
}
.xlsx-plus .slot:nth-child(2) {
  flex: 1;
  padding-right: 10px;
  justify-content: flex-start;
}
.xlsx-plus .slot:nth-child(3) {
  flex: 15;
  justify-content: flex-end;
}
.xlsx-plus .slot button {
  width: 80px;
}
.xlsx-plus .auto-save {
  box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.12);
  border-radius: 0 5px 5px 0;
  background-color: #fff;
  height: 28px;
  left: -100%;
  opacity: 0;
  position: absolute;
  top: 150px;
  transition: all 0.3s linear;
  z-index: -1;
}
.xlsx-plus .auto-save.on {
  opacity: 1;
  left: 1px;
  z-index: 1998;
}
.xlsx-plus .auto-save button {
  border-radius: 0 5px 5px 0;
}

.linked-data-comp {
  border-radius: 5px;
  border: var(--z-border);
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  display: flex;
  height: 100%;
  margin-right: 10px;
  overflow: hidden;
}
.linked-data-comp .left {
  height: 100%;
  width: 100%;
}
.linked-data-comp .search {
  align-items: center;
  border-bottom: var(--z-border);
  display: flex;
  padding: 10px;
  white-space: nowrap;
}
.linked-data-comp .search span {
  font-size: 13px;
}
.linked-data-comp .search span i {
  margin-top: -1px;
}
.linked-data-comp .linke-title {
  display: flex;
}
.linked-data-comp .item {
  position: relative;
}
.linked-data-comp .item .svg-set {
  background: #efefef;
  color: #666;
  cursor: pointer;
  height: 40px;
  line-height: 44px;
  position: absolute;
  right: 0;
  top: 0;
  text-align: center;
  width: 40px;
}
.linked-data-comp .item .svg-set svg {
  transition: all 0.3s linear;
}
.linked-data-comp .item .svg-set:hover svg {
  transform: scale(1.4) rotate(90deg);
}
.linked-data-comp .list {
  height: 100%;
}
.linked-data-comp .list > div {
  border-bottom: var(--z-border);
  cursor: pointer;
}
.linked-data-comp .list > div > label {
  background-color: #fafafa;
  height: 40px;
  padding: 5px 10px;
  width: 100%;
}
.linked-data-comp .list .el-checkbox__input {
  position: relative;
  top: -0.5px;
}
.linked-data-comp .list .el-checkbox__label {
  display: flex;
  overflow: hidden;
  width: calc(100% - 50px);
}
.linked-data-comp .list .el-checkbox__label i {
  color: #f00;
  font-size: 15px;
  margin-left: 10px;
}
.linked-data-comp .list .el-checkbox__label span {
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}
.linked-data-comp .child {
  opacity: 0;
  transition: all 0.15s ease-in-out;
  overflow: hidden;
  max-height: 0;
}
.linked-data-comp .child.open {
  background-color: #fefefe;
  opacity: 1;
}
.linked-data-comp .child .item {
  align-items: center;
  border-top: 1px solid transparent;
  border-bottom: 1px solid transparent;
  cursor: move;
  color: #333;
  display: flex;
  height: 40px;
  line-height: 40px;
  margin-top: -1px;
  text-indent: 55px;
  position: relative;
  white-space: nowrap;
  transition: all 0.3s linear;
}
.linked-data-comp .child .item.linked {
  background-color: #faecd8;
  border-top-color: #e8d0af;
  border-bottom-color: #e8d0af;
}
.linked-data-comp .child .item strong {
  height: 40px;
  position: absolute;
  left: -40px;
}
.linked-data-comp .child .item span {
  border-left: 5px solid transparent;
  height: 40px;
  font-size: 13px;
  flex: 1;
  max-width: calc(100% - 5px);
  overflow: hidden;
  text-overflow: ellipsis;
}
.linked-data-comp .child .item i {
  cursor: pointer;
  color: var(--z-border);
  font-size: 18px;
  height: 40px;
  position: absolute;
  top: -2px;
  text-indent: 0;
  text-align: center;
  transition: all 0.3s linear;
  right: 0;
  width: 40px;
}
.linked-data-comp .child .item i:hover {
  color: var(--z-main);
  transform: scale(1.2);
}
.linked-data-comp .child .item:hover {
  border-top: 1px solid #e8d0af;
  transform: translate3d(0, 1px, 0);
  background-color: #f8e3c5;
}
.linked-data-comp .child .item .tj {
  border-left: 5px solid #f78989;
}
.linked-data-comp .child .item .js {
  border-left: 5px solid #66b1ff;
}
.linked-data-comp .excel-comp .title {
  padding: 5px !important;
}
.linked-data-comp .excel-comp .sheet-main {
  height: calc(100% - 40px) !important;
}
.linked-data-comp .excel_tips {
  align-items: center;
  display: flex;
  font-size: 12px;
}
.linked-data-comp .excel_tips i {
  margin-right: 5px;
}

.info-overview-comp {
  border-radius: 5px;
  color: var(--z-font-color);
  display: flex;
  flex-wrap: wrap;
  border-top: 1px solid var(--z-line);
  border-left: 1px solid var(--z-line);
  position: relative;
  transition: all 0.15s ease-in-out;
  z-index: 1;
}
.info-overview-comp > div {
  background-color: #fff;
  border-right: 1px solid var(--z-line);
  border-bottom: 1px solid var(--z-line);
  transition: all 0.15s ease-in-out;
  overflow: hidden;
}
.info-overview-comp > div > div:nth-child(1) {
  background: var(--z-bg-secondary);
  border-right: 1px solid var(--z-line);
}
.info-overview-comp.show {
  border-color: #f1f1f1;
  height: 0 !important;
}
.info-overview-comp.show > div {
  height: 0 !important;
  overflow: hidden;
  opacity: 0;
}
.info-overview-comp.show .switch {
  border-radius: 4px;
}
.info-overview-comp.show .switch i {
  rotate: 180deg;
}
.info-overview-comp .switch {
  align-items: center;
  border: var(--z-border);
  background-color: #fff;
  border-radius: 0 0 4px 4px;
  bottom: -25px;
  cursor: pointer;
  display: flex;
  height: 26px;
  right: 0;
  justify-content: center;
  opacity: 1;
  position: absolute;
  padding-left: 5px;
  transition: all 0.15s linear;
  width: 56px;
  white-space: nowrap;
}
.info-overview-comp .switch i {
  color: rgba(33, 61, 91, 0.5);
  font-size: 16px;
  transition: all 0.15s linear;
}

.ledger .cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding: 0 20px 10px 20px;
}
.ledger .cards .item {
  border-radius: 4px;
  border: 1px solid rgba(var(--z-main-rgb), 0.3);
  background-color: #fff;
  display: flex;
  margin-right: 10px;
  margin-bottom: 10px;
  max-height: 145px;
  overflow: hidden;
  position: relative;
  transition: all 0.3s linear;
  width: calc(25% - 7.6923076923px);
}
.ledger .cards .item:hover {
  border: 1px solid rgba(var(--z-main-rgb), 1);
}
.ledger .cards .item:nth-child(4n) {
  margin-right: 0;
}
.ledger .cards .item .info {
  align-items: flex-start;
  border-radius: 0 5px 5px 0;
  cursor: pointer;
  position: relative;
  padding: 10px;
  transition: all 0.15s linear;
  width: 100%;
  z-index: 1;
}
.ledger .cards .item .info .new {
  width: 50px;
  height: 50px;
  position: absolute;
  background: rgb(23, 100, 206);
  top: -25px;
  left: -25px;
  transform: rotate(45deg);
}
.ledger .cards .item .info .new span {
  font-size: 9px;
  color: #fff;
  position: absolute;
  bottom: 18px;
  right: -10px;
  display: block;
  width: 40px;
  text-align: center;
  transform: rotate(270deg);
}
.ledger .cards .item .info .info-context {
  padding: 8px 10px 0px 10px;
  height: 100%;
  min-height: 120px;
  max-width: 100%;
}
.ledger .cards .item .info .info-context .count {
  text-align: center;
}
.ledger .cards .item .info .info-context .count div {
  color: var(--z-font-color);
}
.ledger .cards .item .info .info-context .count label {
  color: var(--z-main);
  padding: 10px 0;
}
.ledger .cards .item .info .info-context .department {
  background: var(--z-bg-secondary);
  color: var(--z-main);
  font-weight: 500;
  max-width: 50%;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 5px 10px;
  white-space: nowrap;
}
.ledger .cards .item .info .info-context .department + div {
  color: #ccc;
}
.ledger .cards .item .info .info-context .department + div i {
  margin-right: 2px;
}
.ledger .cards .item .info h2 {
  align-items: center;
  display: block;
  flex: 1;
  font-size: 16px;
  font-weight: 400;
  line-height: 1;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ledger .cards .item .info h2 small {
  color: var(--z-main);
  margin-right: 5px;
}
.ledger .cards .item .info h2 span {
  color: var(--z-font-color);
  font-weight: 500;
  flex: 1;
  overflow: hidden;
  padding: 0 0 0 5px;
  position: relative;
  text-overflow: ellipsis;
  translate: -5px 0 0;
  white-space: nowrap;
}
.ledger .cards .item .info h2 span img {
  height: 16px !important;
  width: 16px !important;
}
.ledger .cards .item .info h2 small {
  font-weight: 500;
}
.ledger .cards .item .info .desc {
  align-items: center;
  color: #999;
  display: flex;
  font-size: 12px;
  line-height: 18px;
  margin-top: 3px;
  scale: 0.95;
  translate: -5px 0 0;
  white-space: nowrap;
}
.ledger .cards .item .info .desc strong {
  color: var(--z-main);
  font-style: italic;
  font-size: 14px;
  font-weight: 500;
  margin-left: 5px;
}
.ledger .cards .item .info .desc small {
  display: inline-block;
  translate: 5px 0 0;
}
.ledger .cards .item .btns {
  align-items: center;
  background: #fafafa;
  display: flex;
  border-left: var(--z-border);
  flex-direction: column;
  opacity: 0.7;
  overflow: hidden;
  transition: all 0.15s linear;
  width: 85px;
}
.ledger .cards .item .btns > div {
  align-items: center;
  border-bottom: var(--z-border);
  cursor: pointer;
  display: flex;
  flex: 1;
  opacity: 0.5;
  padding: 5px 10px;
  white-space: nowrap;
  width: 100%;
}
.ledger .cards .item .btns > div i {
  color: #333;
  font-size: 16px;
  margin-right: 10px;
  transition: all 0.15s linear;
}
.ledger .cards .item .btns > div span {
  color: #333;
  font-size: 12px;
}
.ledger .cards .item .btns > div:last-child {
  border-bottom: none;
}
.ledger .cards .item .btns > div:hover {
  opacity: 1;
}
.ledger .cards .item.star .star {
  color: var(--z-main);
  opacity: 1;
}
.ledger i.star {
  top: 14px;
  color: #666;
  font-size: 20px;
  opacity: 0.5;
  position: absolute;
  right: 10px;
}
.ledger i.star:hover {
  color: rgb(204, 204, 6);
  opacity: 1;
}
.ledger .status {
  align-items: center;
  bottom: -1px;
  display: flex;
  font-size: 14px;
  position: absolute;
  right: 5px;
}
.ledger .status i {
  font-size: 16px;
  margin-right: 3px;
}
.ledger .status > div {
  align-items: center;
  display: flex;
}
.ledger .card-item.half {
  margin-right: 20px;
  width: calc(50% - 10px);
}
.ledger .card-item.half .cards .item {
  width: calc(50% - 5px);
}
.ledger .pending-upgrade {
  color: rgba(136, 6, 6, 0.4);
}
.ledger .pending-upgrade-border {
  border-bottom-color: rgba(136, 6, 6, 0.4) !important;
}
.ledger .time-out {
  color: rgba(136, 6, 6, 0.6);
}
.ledger .time-out-border {
  border-bottom-color: rgba(136, 6, 6, 0.6) !important;
}
.ledger .time-outed {
  color: rgb(136, 6, 6);
}
.ledger .time-outed-border {
  border-bottom-color: rgb(136, 6, 6) !important;
}
.ledger .complete {
  color: rgb(57, 205, 16);
}
.ledger .complete-border {
  border-bottom-color: rgb(57, 205, 16) !important;
}

.custom-form {
  align-items: flex-start;
  flex-direction: column;
  display: flex;
  padding: 20px;
  width: 100%;
}
.custom-form .item {
  align-items: center;
  display: flex;
  height: 40px;
  margin-bottom: 10px;
  width: 100%;
}
.custom-form .item .label {
  font-weight: 500;
  margin-right: 10px;
  min-width: 100px;
  text-align: right;
}
.custom-form .item .value {
  flex: 1;
}/*# sourceMappingURL=main.css.map */