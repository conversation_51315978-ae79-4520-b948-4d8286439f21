.luckysheet-rows-h-selected,
.luckysheet-rows-h-hover {
	transform: translate(0px, -12px);
}

input::placeholder,
textarea::placeholder {
	font-size: 13px;
}

/* 滚动条整体 */
::-webkit-scrollbar {
	width: 10px; /* 设置滚动条宽度 */
	height: 10px; /* 横向滚动条的高度 */
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
	background-color: transparent; /* 滚动条轨道背景色 */
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
	background-color: #cbcbcb; /* 滑块的背景色 */
	border-radius: 10px; /* 滑块圆角 */
	border: 3px solid transparent; /* 透明边框，用于给滑块周围留空间 */
	background-clip: content-box; /* 限制背景颜色只显示在内容区域 */
}

/* 滑块在 hover 时的样式 */
::-webkit-scrollbar-thumb:hover {
	background-color: #cbcbcb; /* 滑块 hover 状态下的背景色 */
}

// lucksheet调整样式
.luckysheet-protection-sheet-validation {
	border: none;
	border-radius: 5px;
	padding: 0;
	overflow: hidden;

	.luckysheet-modal-dialog-title {
		background-color: var(--z-main);
		color: #fff;
		padding: 10px;
		.luckysheet-modal-dialog-title-close {
			right: 8px;
			padding: 0;
			top: 10px;
			width: auto;
		}
	}

	.luckysheet-slider-protection-column {
		align-items: center;
		display: flex;
		justify-content: center;
	}

	.luckysheet-protection-rangeItemiInput {
		display: none;
	}

	.luckysheet-slider-protection-row {
		margin-top: 0 !important;
	}

	.luckysheet-modal-dialog-buttons {
		display: flex;
		justify-content: center;
		button {
			margin-right: 0;
			margin-bottom: 20px;
		}
	}
}
