.ledger {
	.cards {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-start;
		padding: 0 20px 10px 20px;

		.item {
			border-radius: 4px;
			border: 1px solid rgba(var(--z-main-rgb), 0.3);
			background-color: #fff;
			display: flex;
			margin-right: 10px;
			margin-bottom: 10px;
			max-height: 145px;
			overflow: hidden;
			position: relative;
			transition: all 0.3s linear;
			width: calc(25% - 10px / 1.3);

			&:hover {
				border: 1px solid rgba(var(--z-main-rgb), 1);
			}

			&:nth-child(4n) {
				margin-right: 0;
			}

			.info {
				align-items: flex-start;
				border-radius: 0 5px 5px 0;
				cursor: pointer;
				position: relative;
				padding: 10px;
				transition: all 0.15s linear;
				width: 100%;
				z-index: 1;
				.new {
					width: 50px;
					height: 50px;
					position: absolute;
					background: rgb(23, 100, 206);
					top: -25px;
					left: -25px;
					transform: rotate(45deg);
					& span {
						font-size: 9px;
						color: #fff;
						position: absolute;
						bottom: 18px;
						right: -10px;
						display: block;
						width: 40px;
						text-align: center;
						transform: rotate(270deg);
					}
				}
				.info-context {
					padding: 8px 10px 0px 10px;
					height: 100%;
					min-height: 120px;
					max-width: 100%;

					.count {
						text-align: center;
						div {
							color: var(--z-font-color);
						}
						label {
							color: var(--z-main);
							padding: 10px 0;
						}
					}
					.department {
						background: var(--z-bg-secondary);
						color: var(--z-main);
						font-weight: 500;
						max-width: 50%;
						overflow: hidden;
						text-overflow: ellipsis;
						padding: 5px 10px;
						white-space: nowrap;

						& + div {
							color: #ccc;
							i {
								margin-right: 2px;
							}
						}
					}
				}

				h2 {
					align-items: center;
					display: block;
					flex: 1;
					font-size: 16px;
					font-weight: 400;
					line-height: 1;
					width: 100%;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;

					small {
						color: var(--z-main);
						margin-right: 5px;
					}

					span {
						color: var(--z-font-color);
						font-weight: 500;
						flex: 1;
						overflow: hidden;
						padding: 0 0 0 5px;
						position: relative;
						text-overflow: ellipsis;
						translate: -5px 0 0;
						white-space: nowrap;
						// max-width: 80%;
						img {
							height: 16px !important;
							width: 16px !important;
						}
					}

					small {
						font-weight: 500;
					}
				}

				.desc {
					align-items: center;
					color: #999;
					display: flex;
					font-size: 12px;
					line-height: 18px;
					margin-top: 3px;
					scale: 0.95;
					translate: -5px 0 0;
					white-space: nowrap;

					strong {
						color: var(--z-main);
						font-style: italic;
						font-size: 14px;
						font-weight: 500;
						margin-left: 5px;
					}

					small {
						display: inline-block;
						translate: 5px 0 0;
					}
				}
			}

			.btns {
				align-items: center;
				background: #fafafa;
				display: flex;
				border-left: var(--z-border);
				flex-direction: column;
				opacity: 0.7;
				overflow: hidden;
				transition: all 0.15s linear;
				width: 85px;

				> div {
					align-items: center;
					border-bottom: var(--z-border);
					cursor: pointer;
					display: flex;
					flex: 1;
					opacity: 0.5;
					padding: 5px 10px;
					white-space: nowrap;
					width: 100%;
					i {
						color: #333;
						font-size: 16px;
						margin-right: 10px;
						transition: all 0.15s linear;
					}

					span {
						color: #333;
						font-size: 12px;
					}
					&:last-child {
						border-bottom: none;
					}
					&:hover {
						opacity: 1;
					}
				}
			}

			&.star {
				.star {
					color: var(--z-main);
					opacity: 1;
				}
			}
		}
	}

	i.star {
		top: 14px;
		color: #666;
		font-size: 20px;
		opacity: 0.5;
		position: absolute;
		right: 10px;

		&:hover {
			color: rgb(204, 204, 6);
			opacity: 1;
		}
	}

	.status {
		align-items: center;
		bottom: -1px;
		display: flex;
		font-size: 14px;
		position: absolute;
		right: 5px;
		i {
			font-size: 16px;
			margin-right: 3px;
		}
		> div {
			align-items: center;
			display: flex;
		}
	}

	.card-item.half {
		margin-right: 20px;
		width: calc(50% - 10px);
		.cards {
			.item {
				width: calc(50% - 5px);
			}
		}
	}

	.pending-upgrade {
		color: rgba(136, 6, 6, 0.4);
	}
	.pending-upgrade-border {
		border-bottom-color: rgba(136, 6, 6, 0.4) !important;
	}

	.time-out {
		color: rgba(136, 6, 6, 0.6);
	}
	.time-out-border {
		border-bottom-color: rgba(136, 6, 6, 0.6) !important;
	}
	.time-outed {
		color: rgba(136, 6, 6, 1);
	}
	.time-outed-border {
		border-bottom-color: rgba(136, 6, 6, 1) !important;
	}

	.complete {
		color: rgb(57, 205, 16);
	}
	.complete-border {
		border-bottom-color: rgb(57, 205, 16) !important;
	}
}

.custom-form {
	align-items: flex-start;
	flex-direction: column;
	display: flex;
	padding: 20px;
	width: 100%;

	.item {
		align-items: center;
		display: flex;
		height: 40px;
		margin-bottom: 10px;
		width: 100%;
		.label {
			font-weight: 500;
			margin-right: 10px;
			min-width: 100px;
			text-align: right;
		}

		.value {
			flex: 1;
		}
	}
}
