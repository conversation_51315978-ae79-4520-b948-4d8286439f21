// 主键定义功能测试工具
import { STORAGE_KEYS } from '@/define/primary-key.define'

export class PrimaryKeyTestUtils {
	// 清空所有本地存储数据
	static clearAllData() {
		Object.values(STORAGE_KEYS).forEach(key => {
			localStorage.removeItem(key)
		})
		console.log('已清空所有主键定义相关的本地存储数据')
	}

	// 检查本地存储数据
	static checkStorageData() {
		const data: any = {}
		Object.entries(STORAGE_KEYS).forEach(([name, key]) => {
			const value = localStorage.getItem(key)
			data[name] = value ? JSON.parse(value) : null
		})
		console.log('当前本地存储数据:', data)
		return data
	}

	// 验证数据持久化
	static validatePersistence() {
		const data = this.checkStorageData()
		const hasData = Object.values(data).some(value => value !== null)
		
		if (hasData) {
			console.log('✅ 数据持久化验证通过 - 本地存储中存在数据')
			return true
		} else {
			console.log('❌ 数据持久化验证失败 - 本地存储中无数据')
			return false
		}
	}

	// 模拟数据操作测试
	static async simulateOperations() {
		console.log('开始模拟数据操作测试...')
		
		// 这里可以添加更多的测试逻辑
		// 比如模拟添加、编辑、删除操作
		
		console.log('数据操作测试完成')
	}
}

// 在开发环境下暴露到全局，方便调试
if (import.meta.env.DEV) {
	(window as any).PrimaryKeyTestUtils = PrimaryKeyTestUtils
}
