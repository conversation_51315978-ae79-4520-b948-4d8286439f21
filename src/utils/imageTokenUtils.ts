/**
 * 图片URL鉴权工具
 * 用于在图片URL中添加token参数进行鉴权
 */

/**
 * 检查URL是否需要鉴权
 * @param url 要检查的URL
 * @returns 是否需要鉴权
 */
export const isAuthRequiredUrl = (url: string): boolean => {
  if (!url) return false;

  // 检查是否是相对路径的API请求
  if (url.startsWith('/api/') || url.includes('/api/')) {
    return true;
  }

  // 检查是否是完整URL的API请求
  const apiServers = [
    '***********:8001/api/',
    'localhost:8001/api/',
    '127.0.0.1:8001/api/'
  ];

  return apiServers.some(server => url.includes(server));
};

/**
 * 在URL中添加token参数
 * @param url 原始URL
 * @param token 鉴权token
 * @returns 添加token参数后的URL
 */
export const addTokenToUrl = (url: string, token: string): string => {
  if (!url || !token) return url;

  // 检查URL是否已经包含 access_token 参数
  if (url.includes('access_token=')) {
    console.log('URL already contains access_token parameter:', url);
    return url; // 如果已经包含了access_token参数，直接返回原始URL
  }

  // 检查URL是否已经包含查询参数
  const hasQueryParams = url.includes('?');

  // 添加token参数
  return hasQueryParams
    ? `${url}&access_token=${token}`
    : `${url}?access_token=${token}`;
};

/**
 * 处理Markdown文本中的图片URL
 * 在需要鉴权的图片URL中添加token参数
 * @param markdownText Markdown文本
 * @param token 鉴权token
 * @returns 处理后的Markdown文本
 */
export const processMarkdownImages = (markdownText: string, token: string): string => {
  if (!markdownText || !token) return markdownText;

  // 匹配Markdown中的图片语法 ![alt](url)
  const imageRegex = /!\[(.*?)\]\((.*?)\)/g;

  return markdownText.replace(imageRegex, (match, alt, url) => {
    // 检查URL是否需要鉴权
    if (isAuthRequiredUrl(url)) {
      // 添加token参数
      const newUrl = addTokenToUrl(url, token);
      return `![${alt}](${newUrl})`;
    }

    // 如果不需要鉴权，保持原样
    return match;
  });
};

/**
 * 处理Markdown文本
 * @param markdownText Markdown文本
 * @returns 处理后的Markdown文本
 */
export const processMarkdownWithToken = (markdownText: string): string => {
  if (!markdownText) return markdownText;

  // 获取token
  const token = localStorage.getItem('access_token');
  if (!token) return markdownText;

  // 处理Markdown中的图片
  return processMarkdownImages(markdownText, token);
};
