import {request} from '@/api'

export const uploadFile = async (fileItem: any, customizePath: string, useTimeStemp = true) => {
	const prefix = `${new Date().getTime()}`
	// fileItem.name = fileItem.objectId + '-' + fileItem.name
	const data = {
		Path: customizePath,
		Object: useTimeStemp ? `${prefix}-${fileItem.name}` : fileItem.name,
		Overwrite: true,
		File: fileItem,
	}
	const res = await request.uploadFile(
		{url: '/api/files/public'},
		{
			data: data,
			file: data.File,
		}
	)
	if (res) {
		const {data} = res
		return `/${encodeURI(data.path)}${encodeURI(data.name)}`
	} else return null
}
