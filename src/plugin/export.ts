import * as ExcelJs from 'exceljs'

export class ExportExcel {
	private static instance: ExportExcel
	constructor() {
		this.init()
	}

	static getInstance() {
		if (!ExportExcel.instance) {
			ExportExcel.instance = new ExportExcel()
		}
		return ExportExcel.instance
	}

	private init() {
		return this
	}

	private getConvertColor(color: string): string {
		let colorStr = color.replace('#', '')
		if (colorStr.length === 3) {
			colorStr = colorStr
				.split('')
				.map((item) => item + item)
				.join('')
		}
		return colorStr
	}

	private setWookBookInfo(workbook: any, creator: string) {
		workbook.creator = creator
		workbook.lastModifiedBy = creator
		workbook.created = new Date()
		workbook.modified = new Date()
		workbook.lastPrinted = new Date()
	}

	private setWorkSheetStyle(worksheet: any, rowData: any, headEndRow: number) {
		rowData.forEach((row: any, index: number) => {
			const r = worksheet.getRow(index + 1)

			if (r.model !== null) {
				// debugger
				r.model.cells.forEach((cell: any, cellIndex: number) => {
					const curCell = worksheet.getCell(cell.address)
					// 表头样式
					// if (index < headEndRow) {
					r.height = 20
					curCell.border = {
						top: {style: 'thin'},
						left: {style: 'thin'},
						bottom: {style: 'thin'},
						right: {style: 'thin'},
					}
					curCell.font = {
						size: 12,
						bold: true,
					}
					curCell.alignment = {vertical: 'middle', horizontal: 'center'}
					// }
					//  else {
					// 	curCell.alignment = {vertical: 'middle', horizontal: 'center'}
					// }
					// // 设置换行显示
					// if (!row[cellIndex]?.v && !row[cellIndex]?.m) {
					// 	if (row[cellIndex]?.ct?.s) {
					// 		// row[cellIndex].ct.s[0].v = row[cellIndex]?.ct?.s[0].v.replace('\r\n', '')
					// 		curCell.value = row[cellIndex]?.ct?.s[0].v.replace('\r\n', '')
					// 	}
					// }
					// 设置日期格式
					if (row[cellIndex] !== null && row[cellIndex]?.ct?.fa !== 'General') {
						curCell.numFmt = row[cellIndex]?.ct?.fa
					}
					// 填充背景色
					if (row[cellIndex]?.bg) {
						curCell.fill = {
							type: 'pattern',
							pattern: 'solid',
							fgColor: {argb: `FF${this.getConvertColor(row[cellIndex].bg)}`},
						}
					}
					// console.log(2222, curCell)
				})
			}
		})
	}

	private cellDateToExcelDate(worksheet: any, sheet: any) {
		if (!('luckysheet' in window)) return

		const {config, head, data} = sheet
		console.log('cellDateToExcelDate ===== ')

		// 转数据
		const rowData = window.luckysheet
			.transToData([...head, ...data])
			.filter((item: any) => item.some(Boolean))
		for (let i = 0; i < rowData.length; i++) {
			const raw = rowData[i].map((item: any) => {
				if (item) {
					if (item?.v) {
						return item?.v
					} else {
						return item?.ct !== undefined && item?.ct?.s !== undefined ? item.ct?.s[0]?.v : null
					}
				} else {
					return null
				}
			})
			const row = worksheet.getRow(i + 1)
			row.values = raw
			row.commit()
		}

		// 合并
		const merges = Object.values(config.merge)
		for (const element of merges) {
			const {r, c, rs, cs} = element as any
			worksheet.mergeCells(r + 1, c + 1, r + rs, c + cs)
		}

		// 表头列属性
		const columns = []
		const maxr = Math.max(...Object.values(head).map((item: any) => item.r))
		const maxrs = merges.length ? Math.max(...merges.map((item: any) => item.rs)) : 1
		const maxc = merges.length
			? Math.max(...merges.map((item: any) => item.c + item.cs), head.length)
			: 0

		for (let i = 0; i <= maxc; i++) {
			columns.push({
				width: 25,
			})
		}

		console.log(columns)
		worksheet.columns = columns

		// // 样式
		this.setWorkSheetStyle(worksheet, rowData, Math.max(maxr, maxrs))

		// console.log('luckysheet rows: ', rowData)
	}

	async download(sheets: any[], fileName: string = 'creator.xlsx', creator: string = 'System') {
		const workbook = new ExcelJs.Workbook()
		this.setWookBookInfo(workbook, creator)

		console.log('sheets: ', sheets)
		console.log('workbook: ', workbook)

		// 工作薄配置
		workbook.calcProperties.fullCalcOnLoad = true
		workbook.views = [
			{
				x: 0,
				y: 0,
				width: 10000,
				height: 20000,
				firstSheet: 0,
				activeTab: 1,
				visibility: 'visible',
			},
		]

		// 冻结表头
		sheets.forEach((sheet: any) => {
			// 表头冻结
			// const merges = Object.values(sheet.config.merge)
			// const maxr = Math.max(...Object.values(sheet.head).map((item: any) => item.r))
			// const maxrs = merges.length ? Math.max(...merges.map((item: any) => item.rs)) : 1

			// workbook.addWorksheet(sheet.name, {
			// 	views: [{state: 'frozen', xSplit: 0, ySplit: Math.max(maxr, maxrs)}],
			// })

			const merges = Object.values(sheet.config.merge)
			const maxr = Math.max(...Object.values(sheet.head).map((item: any) => item.r))
			const maxrs = merges.length ? Math.max(...merges.map((item: any) => item.rs)) : 1

			workbook.addWorksheet(sheet.name, {
				// views: [{state: 'frozen', xSplit: 0}],
			})
		})

		// 转换数据
		workbook.eachSheet((worksheet, sheetId) => {
			console.log('worksheet: ', worksheet, sheetId)
			this.cellDateToExcelDate(worksheet, sheets[sheetId - 1])
		})

		// 导出下载
		const buffer = await workbook.xlsx.writeBuffer()

		const blob = new Blob([buffer], {
			type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
		})

		const link = document.createElement('a')
		link.href = window.URL.createObjectURL(blob)
		link.download = `${fileName}.xlsx`
		link.click()
		link.remove()
		console.log('download success')
	}
}
