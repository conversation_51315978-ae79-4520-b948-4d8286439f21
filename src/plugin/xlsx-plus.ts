import * as ExcelJs from 'exceljs'
interface Config {
	header: Array<any>
	source: any
	file: File
	batch?: number
	onBeginBefore?: Function
	onBegin?: Function
	onProgress?: Function
	onEnd?: Function
	onFinish?: Function
	onError?: Function
}
class XlsxPlus {
	header: Array<any> = []
	source: any | null = null
	file: File | null = null
	batch: number = 10
	onBeginBefore?: Function
	onBegin?: Function
	onProgress?: Function
	onEnd?: Function
	onFinish?: Function
	onError?: Function

	workbook!: ExcelJs.Workbook | null

	constructor(config?: Config) {
		Object.assign(this, config)
		this.batch > 20 && (this.batch = 20)
		this.init()
	}

	init() {
		this.workbook = new ExcelJs.Workbook()
	}

	async import(): Promise<any> {
		this.onBeginBefore?.()
		console.log('xlsx-plus import begin before ====================')
		return new Promise(async (resolve, reject) => {
			let file: any = await this._readFileInChunks(this.file as File, 1024 * 1024)
			let workbook: any = await this.workbook!.xlsx.load(file)
			const sheets = workbook!.worksheets
			const len = sheets.length

			if (!this._verify().sheetIsEqual(this.source?.data.length)) {
				return false
			}

			for (let i = 0; i < len; i++) {
				if (!this._verify().columnIsEqual(sheets[i], i)) {
					return false
				}
			}

			console.log('xlsx-plus import begin ====================')
			this.onBegin?.()

			for (let i = 0; i < len; i++) {
				console.log('xlsx-plus step 1: reding excel')
				await this._toLuckySheet(workbook!.worksheets[i], i)
			}

			console.log('xlsx-plus step 3: return data')
			console.log('xlsx-plus import end ====================')
			this.onFinish?.(true)
			resolve('')
			this.file = null
			this.source = null
			this.workbook = null

			file = null
			workbook = null
		})
	}

	_readFileInChunks(file: File, chunkSize: number): Promise<ArrayBuffer> {
		return new Promise((resolve, reject) => {
			const fileReader = new FileReader()
			let offset = 0
			let chunks: ArrayBuffer[] = []

			fileReader.onerror = () => {
				fileReader.abort()
				reject(new Error('Failed to read file.'))
			}

			fileReader.onload = () => {
				chunks.push(fileReader.result as ArrayBuffer)
				offset += chunkSize
				if (offset >= file.size) {
					const result = new Uint8Array(offset)
					let currentPos = 0
					for (let chunk of chunks) {
						result.set(new Uint8Array(chunk), currentPos)
						currentPos += chunk.byteLength
					}
					resolve(result.buffer)
				} else {
					readChunk(offset)
				}
			}

			const readChunk = (start: number) => {
				const end = Math.min(start + chunkSize, file.size)
				const blob = file.slice(start, end)
				fileReader.readAsArrayBuffer(blob)
			}

			readChunk(0)
		})
	}

	async _toLuckySheet(worksheet: any, index: number): Promise<any> {
		console.log('xlsx-plus step 2: converting to sheet')

		const headerEnd = this.header[index].rr + 1
		const headerCellEnd = this.header[index].cc + 1
		const mr = worksheet.rowCount - headerEnd
		const size = Math.ceil(mr / this.batch)
		const max = mr < size ? 1 : Math.ceil(mr / size)

		let rc = 0
		let merges: any = []
		let mergeModel: any = []

		if (worksheet.hasMerges) {
			const md = Object.values(worksheet._merges)
			const mdlen = md.length

			for (let i = 0; i < mdlen; i++) {
				const item: any = md[i]
				const [r, rr] = item.range.split(':')[0].match(/[A-Z]|[0-9]+/g)
				if (Number(rr) > headerEnd) {
					merges.push(item.range)
					mergeModel.push(item.model)
				}
			}
		}

		const progressChunk = async (rows: Array<any>, startRow: number, endRow: number) => {
			const temporary: any = []
			const len = rows?.length | 0

			for (let i = 0; i < len; i++) {
				const rvs = rows[i].values.slice(1)
				temporary.push(this._toLuckySheetCell(Array.from(rvs).slice(0, headerCellEnd)))
				rc++
			}

			this.onProgress?.({
				data: temporary,
				order: worksheet.orderNo,
				progress: Math.round((rc / mr) * 100),
				range: {
					row: [startRow, endRow],
					column: [0, headerCellEnd - 1],
				},
				maxr: mr + headerEnd,
			})
		}

		for (let r = 0; r < max; r++) {
			const gr = r === max - 1 ? mr - r * size : size
			const rows = worksheet.getRows(r * size + headerEnd + 1, gr)

			const startRow = r * size
			let endRow = r * size + size - 1

			if (!rows) continue
			if (rows.length !== size) {
				endRow = startRow + rows.length - 1
			}
			if (rows[0].values.length > 0) {
				progressChunk(rows, startRow + headerEnd, endRow + headerEnd)
				await this._wait(0)
			}
		}

		console.log('xlsx-plus step 2: converting to sheet end')
		this.onEnd?.({merges, mergeModel})
		await this._wait(1000)
	}

	_toLuckySheetCell(values: any): Array<any> {
		return values.map((value: any, index: number) => ({
			m: value || '-',
			ct: {
				fa: 'General',
				t: 'g',
			},
			v: value || '-',
		}))
	}

	_wait(ms: number) {
		return new Promise((resolve: any) => setTimeout(resolve, ms))
	}

	_verify() {
		const error = {
			equal: 'Sheet页与模版不匹配, 请检查文件',
			equalHeader: '表头与模版不匹配, 请检查文件',
			column: '第{0}行, {1}列单元格是空数据, 请检查文件',
			empty: '存在单元格有空数据, 请检查文件',
		}
		return {
			sheetIsEqual: (count: number): boolean => {
				if (this.workbook?.worksheets.length !== count) {
					this.onError?.(error.equal)
					this.onFinish?.(false)
					return false
				}
				return true
			},
			columnIsEqual: (sheet: any, index: number): boolean => {
				const headerEnd = this.header[index].rr // 表头结束行
				const columnEnd = this.header[index].cc // 表头结束列
				const count = sheet.rowCount - headerEnd - 1 // 总行数 - 表头行数

				// 表头模版不匹配
				const headers = sheet.getRows(1, headerEnd + 1)
				if (headers.some((item: any) => item.values.slice(1).length - 1 !== columnEnd)) {
					this.onError?.(error.equalHeader)
					this.onFinish?.(false)
					return false
				}

				for (let i = headerEnd + 1; i < count; i++) {
					const row = sheet.getRows(i, 1)
					if (row[0]) {
						const curRow = row[0]
						const cells = curRow._cells
						console.log(111, cells)
						cells.forEach((x: any) => {
							if (x.value == 0) {
								x.value = '0'
							}
						})
						const len = cells.filter((f: any) => f && f.value).length - 1
						// 判断空单元格
						if (cells.filter((v: any) => v).length !== cells.length) {
							this.onError?.(error.empty)
							this.onFinish?.(false)
							return false
						} else if (len !== columnEnd) {
							// 对应列与值不匹配
							const columnIndex = cells.filter((v: any) => v).findIndex((f: any) => !f.value)
							this.onError?.(
								error.column.replace('{0}', curRow._number).replace('{1}', columnIndex + 1)
							)
							this.onFinish?.(false)
							return false
						}
					}
				}
				return true
			},
		}
	}
}

export default XlsxPlus
