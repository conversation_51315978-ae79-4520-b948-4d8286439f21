import * as ExcelJs from 'exceljs'

interface Sheet {
	name?: string
	celldata?: Array<any>
	config?: {
		merge?: Array<any>
	}
}

type FunctionType = Function | undefined | null
interface Hooks {
	onImportBegin?: FunctionType
	onImportProgress?: FunctionType
	onImportend?: FunctionType
}

/**
 * @name: xlsx
 * @description: 导入/出excel
 */
class Xlsx {
	file: File
	sheets: Array<Sheet> = [] as Array<Sheet>
	workbook!: ExcelJs.Workbook

	onImportBegin: FunctionType
	onImportProgress: FunctionType
	onImportend: FunctionType

	constructor(file: File, hooks?: Hooks | null) {
		this.file = file
		this.init()
		this.onImportBegin = hooks?.onImportBegin
		this.onImportProgress = hooks?.onImportProgress
		this.onImportend = hooks?.onImportend
	}

	init() {
		this.workbook = new ExcelJs.Workbook()
	}

	_wait(ms: number) {
		return new Promise((resolve: any) => {
			setTimeout(() => resolve(), ms)
		})
	}

	// 导入excel
	async import(): Promise<Array<Sheet>> {
		if (this.onImportBegin) {
			this.onImportBegin()
			await this._wait(1)
		}
		return new Promise((resolve, reject) => {
			const reader = new FileReader()
			reader.onload = (e) => {
				const data = e.target?.result
				this.workbook.xlsx.load(data as Buffer).then((workbook) => {
					workbook.eachSheet(async (worksheet: any, sheetId: any) => {
						//TODO: 仅导入第一个sheet
						const id = worksheet.id
						if (id === 1) {
							await this.__xlsxToLuckSheet(worksheet, sheetId).then((sheet) => {
								this.sheets.push(sheet)
								resolve(this.sheets)
							})
						}
					})
				})
			}
			reader.readAsArrayBuffer(this.file)
		})
	}

	async __xlsxToLuckSheet(worksheet: any, sheetId: any): Promise<Sheet> {
		return new Promise(async (resolve, reject) => {
			// 当前 sheet 数据
			const self = this
			const sheet = {celldata: [], config: {merge: []}} as Sheet
			const rows = worksheet._rows.filter((r: any) => r)

			const maxRow = worksheet._rows.length
			const maxCol = worksheet._rows
				.filter((r: any) => r)
				.reduce((a: number, b: any) => a + b._cells.length, 0)

			let cellCount = 0
			let importEndTriggered = false

			sheet.name = worksheet.name

			// 合并单元格
			for (const key in worksheet._merges) {
				const merge = worksheet._merges[key]
				sheet.config!.merge!.push({
					r: merge.top - 1,
					c: merge.left - 1,
					rs: merge.bottom - merge.top,
					cs: merge.right - merge.left,
					range: merge.range,
				})
			}

			// 单元格行列转换
			for (let i = 0; i < maxRow; i++) {
				const row = rows[i]
				if (row) {
					for (let j = 0; j < row._cells.length; j++) {
						const cell = row._cells[j]
						const celldata = {
							r: row._number - 1,
							c: j,
							v: cell?.value,
						}
						cellCount++
						sheet.celldata!.push(celldata)

						// 导入中钩子
						if (self.onImportProgress) {
							const progress = (1 + (cellCount / maxCol) * 100) | 0

							self.onImportProgress({
								d: celldata,
								m: sheet.config?.merge?.[i],
								p: progress,
								mr: worksheet.rowCount, // excel 行数 包含空行
							})

							if (progress >= 100 && self.onImportend && !importEndTriggered) {
								importEndTriggered = true
								if (this.onImportend) {
									this.onImportend(this.sheets)
								}
								resolve(sheet)
							}
						}
						await self._wait(1)
					}
				}
			}
		})
	}
}

export default Xlsx
