import {downloadStatementFile, downloadStaticFile} from '@/api/ReportApi'

/**
 * 工具
 */
interface KeyString {
	[key: string]: any
}

const util = {
	sleep: (ms: number) => new Promise((resolve) => setTimeout(resolve, ms)),
	_guid() {
		function s4() {
			return Math.floor((1 + Math.random()) * 0x10000)
				.toString(16)
				.substring(1)
		}
		return s4() + s4() + '-' + s4() + '-' + s4() + '-' + s4() + '-' + s4() + s4() + s4()
	},
	_deepCopy(obj: any) {
		if (typeof obj !== 'object' || obj === null) {
			return obj
		}

		let copy: KeyString = Array.isArray(obj) ? [] : {}
		for (let key in obj) {
			if (obj.hasOwnProperty(key)) {
				copy[key] = this._deepCopy(obj[key])
			}
		}
		return copy
	},
	getRandomColor(): string {
		const letters: string = '0123456789ABCDEF'
		let color: string = '#'
		for (let i: number = 0; i < 6; i++) {
			color += letters[Math.floor(Math.random() * 16)]
		}
		return color
	},
	// 短线链接命名转驼峰命名 xxx-xxx to xxXx || xxx-xxx to XxxXxx
	toCamelCase(str: string, isCapitalized = false) {
		return isCapitalized
			? str.replace(/(?:^|-)(\w)/g, (_, c) => c.toUpperCase())
			: str.replace(/([-_]\w)/g, function (match) {
					return match.toUpperCase().replace('-', '').replace('-', '')
			  })
	},

	// 驼峰命名转符号拼接命名 xxXx to xx/xx
	splitCamelCase(str: string, symbol = '-') {
		return str.replace(/([a-z])([A-Z])/g, `$1${symbol}$2`).toLowerCase()
	},

	// 驼峰命名拆分成数组
	camelCaseToArray(str: string) {
		return str
			.replace(/([a-z])([A-Z])/g, `$1-$2`)
			.toLowerCase()
			.split('-')
	},

	/**
	 * 平铺数据生成树状结构(非递归)
	 */
	// 使用parentId构建目录树
	arrayToTree(arr: any[]) {
		console.log(arr)

		const newArr: any = []
		const map: any = {}
		arr.forEach((item) => {
			// if (!item.children)
			//   item.children = [] // 判断数据是否有子级   没有则进行子级的添加
			map[item.id] = item // 添加对应的映射关系
			item.value = item.id
			item.label = item.displayName || item.name || item.taskName
		})
		arr.forEach((item) => {
			if (map[item.parentId || item.companyId]) {
				if (!map[item.parentId || item.companyId].children)
					map[item.parentId || item.companyId].children = []
				map[item.parentId || item.companyId].children.push(item)
			} else {
				newArr.push(item)
			}
		})
		console.log(newArr)

		return newArr.length ? newArr : []
	},
	// 使用code构建目录树
	// arrayToTree(data: any[]) {
	// 	const tree:any[] = [];

	// 	data.forEach(item => {
	// 	  const path = item.code.split('.');
	// 	  let currentNode = tree;

	// 	  path.forEach((code:any, index:any) => {
	// 		const existingNode = currentNode.find(node => node.code === code);

	// 		if (existingNode) {
	// 			existingNode.parentId = null;
	// 		  currentNode = existingNode.children;
	// 		  console.log(currentNode)
	// 		} else {
	// 		  const newNode = {
	// 			code,
	// 			label: item.displayName,
	// 			value:item.id,
	// 			children: []
	// 		  };

	// 		  currentNode.push(newNode);
	// 		  currentNode = newNode.children;
	// 		}
	// 	  });
	// 	});

	// 	return tree;
	// },
	// 树状结构扁平化
	treeToArray(tree: any) {
		let res: any[] = []
		for (const item of tree) {
			const {children, ...i} = item
			// 如果children为null，则将其视为空数组
			const childrenArray = children === null ? [] : children
			if (childrenArray && childrenArray.length) res = res.concat(util.treeToArray(childrenArray))

			res.push(i)
		}
		return res
	},

	// 从对象中提取非空值的键值对并返回一个新的对象
	extractNonEmptyValuesFromSearchParams(searchParams: any) {
		const params = Object.keys(searchParams)
			.filter(
				(key) =>
					searchParams[key] !== null &&
					searchParams[key] !== undefined &&
					searchParams[key] !== ''
			)
			.reduce((acc, key) => ({...acc, [key]: searchParams[key]}), {})
		return params
	},
	async downloadAttachment(att: any, status: number) {
		const res = await downloadStatementFile(att.id, status)

		if (res && res.data) {
			// Create a blob URL from the response data
			const blob = new Blob([res.data])
			const url = window.URL.createObjectURL(blob)

			// Create a temporary anchor element to trigger the download
			const link = document.createElement('a')
			link.href = url
			// Use the attachment name if available, otherwise use a default name
			link.download = att.name || att.fileName || `downloaded_file_${new Date().getTime()}`
			document.body.appendChild(link)
			link.click()

			// Clean up
			document.body.removeChild(link)
			window.URL.revokeObjectURL(url)
		}
	},
	async downloadAStaticttachment(att: any) {
		const res = await downloadStaticFile(att.id)

		if (res && res.data) {
			// Create a blob URL from the response data
			const blob = new Blob([res.data])
			const url = window.URL.createObjectURL(blob)

			// Create a temporary anchor element to trigger the download
			const link = document.createElement('a')
			link.href = url
			// Use the attachment name if available, otherwise use a default name
			link.download = att.name || att.fileName || `downloaded_file_${new Date().getTime()}`
			document.body.appendChild(link)
			link.click()

			// Clean up
			document.body.removeChild(link)
			window.URL.revokeObjectURL(url)
		}
	},
}

export default util
