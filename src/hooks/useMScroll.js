export const useMScroll = (dom) => {
	const mScroll = dom || document.querySelector('.main-container>.container-body')
	const isScrollBottom = (offset = 0) => {
		// 使用scrollHeight的0.1%作为容差值，最小1px，最大10px
		const tolerance = Math.min(Math.max(Math.floor(mScroll.scrollHeight * 0.001), 1), 10)
		const remainingDistance = Math.abs(
			mScroll.scrollHeight - mScroll.scrollTop - offset - (mScroll.clientHeight - offset)
		)
		return remainingDistance <= tolerance
	}

	return {
		isScrollBottom,
	}
}
