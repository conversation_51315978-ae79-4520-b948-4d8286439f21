function isFirstHalfOfMonth() {
	const currentDate = new Date()
	const dayOfMonth = currentDate.getDate()
	return dayOfMonth <= 15
}
function isFirstHalfOfYear() {
	const currentDate = new Date()
	const monthOfYear = currentDate.getMonth()
	return monthOfYear < 6
}

export const useUpdateCycle = (config) => {
	const date = new Date()
	const weekDays = ['一', '二', '三', '四', '五', '六', '日']
	const year = date.getFullYear()
	const month = date.getMonth() + 1
	const qtr = month <= 3 ? 1 : month <= 6 ? 2 : month <= 9 ? 3 : 4

	if (config.interval == 2) {
		if (config.weeklyDeadlineDayOfWeek !== null) {
			return `周${weekDays[config.weeklyDeadlineDayOfWeek - 1]}`
		} else {
			return '未设置'
		}
	} else if (config.interval == 3) {
		if (config.monthlyDeadlineDay) {
			return `${config.monthlyDeadlineDay}日`
		} else {
			return '未设置'
		}
	} else if (config.interval == 4) {
		if (config['quarterlyDeadlineDay' + qtr] !== null && config['quarterlyDeadlineDay' + qtr] !== undefined) {
			const text = new Date(
				config['quarterlyDeadlineDay' + qtr].substring(4).replace(/./, year + '-')
			)
			return `${text.getMonth() + 1}月${text.getDate()}日`
		} else {
			return '未设置'
		}
	} else if (config.interval == 5) {
		if (config.yearlyDeadlineDay !== null && config.yearlyDeadlineDay !== undefined) {
			const text = new Date(config.yearlyDeadlineDay.substring(4).replace(/./, year + '-'))
			return `${text.getMonth() + 1}月${text.getDate()}日`
		} else {
			return '未设置'
		}
	} else if (config.interval == 6) {
		if (isFirstHalfOfMonth()) {
			// 上半个月
			if (config.downHalfMonthReminderTime) {
				return `${config.downHalfMonthReminderTime}日`
			} else {
				return '未设置'
			}
		} else {
			if (config.downHalfMonthDateOnlyTime) {
				return `${config.downHalfMonthDateOnlyTime}日`
			} else {
				return '未设置'
			}
		}
	} else if (config.interval == 7) {
		if (isFirstHalfOfYear()) {
			if (config.upHalfYearDateOnlyTime !== null && config.upHalfYearDateOnlyTime !== undefined) {
				const text = new Date(config.upHalfYearDateOnlyTime.substring(4).replace(/./, year + '-'))
				return `${text.getMonth() + 1}月${text.getDate()}日`
			} else {
				return '未设置'
			}
		} else {
			if (config.downHalfYearDateOnlyTime !== null && config.downHalfYearDateOnlyTime !== undefined) {
				const text = new Date(config.downHalfYearDateOnlyTime.substring(4).replace(/./, year + '-'))
				return `${text.getMonth() + 1}月${text.getDate()}日`
			} else {
				return '未设置'
			}
		}
	} else if (config.interval == 0) {
		// 不提醒
		return '-'
	}
}
