import {useUserStore} from '@/stores/useUserStore'

export const Permissions = {
	City: 'city',
	District: 'district',
	Street: 'street',
	Community: 'community',
}

// 行政层级权限
export const usePermission = (permission) => {
	const user = useUserStore()
	if (user) {
		const info = user.getUserInfo
		switch (permission) {
			case Permissions.City:
				return (
					info.community === null &&
					info.street === null &&
					info.district === null &&
					info.city !== null
				)
			case Permissions.District:
				return info.community === null && info.street === null && info.district !== null
			case Permissions.Street:
				return info.community === null && info.street !== null
			case Permissions.Community:
				return info.community !== null
		}
	}
	return false
}

// 功能权限
export const usePermissions = (permission) => {
	const user = useUserStore()
	if (user && user.getApplication) {
		const app = user.getApplication
		const {grantedPolicies} = app || {}
		return grantedPolicies[permission]
	}
	return false
}
