export const useArrayToTree = (
	arr: [],
	key: string,
	parentKey: string,
	labelKey: string,
	customAttr: boolean = true,
	children: string = 'child',
	sort: boolean = false
): Array<any> => {
	const result: any = []
	const map: any = {}

	arr.forEach((item: any) => {
		map[item[key]] = item
	})

	arr.forEach((item: any) => {
		const parent = map[item[parentKey]]

		if (customAttr) {
			item.selected = []
			item.checked = false
			item.indeterminate = false
			item.label = item[labelKey]
		}

		if (parent) {
			;(parent[children] || (parent[children] = [])).push(item)
			if (parent[children] && sort) {
				parent[children].sort((a: any, b: any) => a.sort - b.sort)
			}
		} else {
			if (customAttr) {
				delete item.checked
				item.checkAll = false
			}
			result.push(item)
		}
	})

	if (sort) {
		result.sort((a: any, b: any) => a.sort - b.sort)
	}

	return result
}

export const useTreeLastChecked = (tree: any, children: string = 'child'): any[] => {
	const result: any[] = []
	tree.forEach((item: any) => {
		if (item[children]) {
			result.push(...useTreeLastChecked(item[children], children))
		} else {
			result.push(item)
		}
	})
	return result
}

export const useLedgerDN = (fields: any, group: any): any => {
	const getGroupName = (cg: any, group: any[], currentGroupName = ''): string => {
		currentGroupName = `${cg.name}-${currentGroupName}`

		const g = group.find((f: any) => f.id === cg.parentId)
		if (g) {
			return getGroupName(g, group, currentGroupName)
		}

		return currentGroupName.substring(0, currentGroupName.length - 1)
	}

	fields.forEach((f: any) => {
		group.forEach((g: any) => {
			if (g.tableFields && g.tableFields.length > 0) {
				if (g.tableFields.some((s: any) => s.id === f.id)) {
					let currentGroupName = getGroupName(g, group)
					f.displayName = `${f.displayName}【${currentGroupName}】`
				}
			}
		})
	})

	return fields
}
