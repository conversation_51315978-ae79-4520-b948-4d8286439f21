export const useValidateCode = () => {
	// 定义验证码类型常量
	const codeType = {
		tm: 'tm', // 业务表列表脱敏验证
		dc: 'dc', // 业务表导出验证
		infotm: 'infotm', // 业务表详情脱敏
		sjfx: 'sjfx', // 数据分析
	}

	// 存储键名映射
	const STORAGE_KEY_PREFIX = 'CODE_'
	const getStorageKey = (type) => `${STORAGE_KEY_PREFIX}${type.toUpperCase()}`

	const EXPIRED_TIME = 30 * 60 * 1000 // 30分钟过期时间

	// 从本地存储获取数据，避免重复解析
	const getStorageData = (type) => {
		const key = getStorageKey(type)
		try {
			return JSON.parse(localStorage.getItem(key) || '{}')
		} catch (e) {
			console.error(`解析${key}数据失败:`, e)
			return {}
		}
	}

	// 缓存数据对象
	const codeStorage = {
		[codeType.tm]: getStorageData(codeType.tm),
		[codeType.dc]: getStorageData(codeType.dc),
		[codeType.infotm]: getStorageData(codeType.infotm),
		[codeType.sjfx]: getStorageData(codeType.sjfx),
	}

	// 保存数据到本地存储
	const saveToStorage = (type) => {
		const key = getStorageKey(type)
		localStorage.setItem(key, JSON.stringify(codeStorage[type]))
	}

	// 保存所有数据
	const saveAllToStorage = () => {
		Object.keys(codeStorage).forEach((type) => {
			saveToStorage(type)
		})
	}

	/**
	 * 设置验证码
	 * @param {string} key - 业务表ID
	 * @param {string} code - 验证码
	 * @param {string} type - 验证码类型
	 */
	const setCode = (key, code, type) => {
		if (!key || !code || !type || !codeStorage[type]) return
		codeStorage[type][key] = `${Date.now()}:${code}`
		saveToStorage(type)
	}

	/**
	 * 获取验证码信息
	 * @param {string} key - 业务表ID
	 * @param {string} type - 验证码类型
	 * @returns {Object|null} - 验证码信息对象或null
	 */
	const getCode = (key, type) => {
		if (!key || !type || !codeStorage[type]) return null

		const codeData = codeStorage[type][key]
		if (!codeData) return null

		const [timestamp, code] = codeData.split(':')
		return {
			timestamp: Number(timestamp),
			code,
		}
	}

	/**
	 * 检查验证码是否过期
	 * @param {string} key - 业务表ID
	 * @param {string} type - 验证码类型
	 * @returns {boolean} - 是否已过期
	 */
	const expired = (key, type) => {
		if (!key || !type || !codeStorage[type]) return true

		const codeData = codeStorage[type][key]
		if (!codeData) return true

		const [timestamp] = codeData.split(':')
		const now = Date.now()
		const isExpired = now - Number(timestamp) > EXPIRED_TIME

		// 如果过期，删除该验证码并保存
		if (isExpired) {
			delete codeStorage[type][key]
			saveToStorage(type)
		}

		return isExpired
	}

	return {
		codeType,
		getCode,
		setCode,
		expired,
	}
}
