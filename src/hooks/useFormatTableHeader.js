/**
 * 动态表头
 * @param {tableInfo.tableFieldGroups} groups
 * @param {tableInfo.fields} fields
 */

const CardsMap = {
	identification_number: '居民身份证号',
	passport: '护照号',
	hk_macao_permit: '港澳居民来往内地通行证',
	taiwan_permit: '台湾居民来往内地通行证',
}

const useFormatTableColumnTypes = (field, currentItem, fields) => {
	let type = field.type

	if (field.type === 'int' || field.type === 'decimal') {
		type = 'text'
		currentItem.inputType = 'number'
	} else if (field.type === 'bool') {
		type = 'switch'
	} else if (field.type === 'guid' || field.type === 'uuid') {
		type = 'text'
	} else if (
		field.type === 'string' ||
		field.type === 'identification_number' ||
		field.type === 'certificate' ||
		field.type === 'phone' ||
		field.type === 'email'
	) {
		const hasOptions = field.options.length > 0

		type = 'text'

		if ((hasOptions && field.multiple === null) || field.type === 'certificate') {
			type = 'select'

			if (field.type === 'certificate') {
				currentItem.options = field.customValueOptions?.map((option) => {
					const [key, value] = Object.entries(option)[0]

					return {
						value: value,
						label: CardsMap[value],
					}
				})
			}
		} else if (hasOptions && field.multiple) {
			type = 'checkbox'
		} else if (hasOptions && field.multiple === false) {
			type = 'radio'
		}

		if (hasOptions) {
			currentItem.options = field.options.map((option) => ({
				value: option,
				label: option,
			}))
		}
		// images, attachments 插槽处理
	}
	// console.log('currentItem1111', currentItem);
	currentItem.type = type
}

const useFormatTableHeader = (tableGroups, fields, callback) => {
	const repeatingFields = []
	const havePermissionGroupFields = []

	tableGroups.forEach((item) => {
		const prop = `table-group-${Math.random().toString(36).slice(2)}-prop`

		if (item.tableFields) {
			const group = {
				prop,
				label: item.displayName || item.name,
				children: [],
				sort: item.sort,
				raw: JSON.parse(JSON.stringify(item)),
			}

			item.tableFields.forEach((field) => {
				if (field.isListField && field.isDepartmentField) {
					let currentItem = {
						prop: field.name,
						label: field.displayName,
						sort: field.sort,
						raw: JSON.parse(JSON.stringify(field)),
					}
					useFormatTableColumnTypes(field, currentItem, fields)
					typeof callback === 'function' && callback(currentItem)
					group.children.push(currentItem)
				}
				repeatingFields.push(field.name)
			})

			group.children.sort((a, b) => a.sort - b.sort)

			const exist = havePermissionGroupFields.find(
				(history) => history.raw.id === item.parentId
			)

			if (exist) {
				exist.children.push(group)
				exist.children.sort((a, b) => a.sort - b.sort)
			} else {
				havePermissionGroupFields.push(group)
			}
		} else {
			let currentItem = {
				prop,
				label: item.displayName || item.name,
				children: [],
				sort: item.sort,
				raw: JSON.parse(JSON.stringify(item)),
			}
			useFormatTableColumnTypes(field, currentItem, fields)
			typeof callback === 'function' && callback(currentItem)
			havePermissionGroupFields.push(currentItem)
		}
	})

	const havePermissionFields = []

	fields
		.filter((f) => !repeatingFields.includes(f.name) && f.isListField && f.isDepartmentField)
		.forEach((field) => {
			let currentItem = {
				prop: field.name,
				label: field.displayName,
				sort: field.sort,
				raw: JSON.parse(JSON.stringify(field)),
			}
			useFormatTableColumnTypes(field, currentItem, fields)
			typeof callback === 'function' && callback(currentItem)
			havePermissionFields.push(currentItem)
		})

	// console.log('useFormatTableHeader', [...havePermissionFields, ...havePermissionGroupFields])

	return [...havePermissionFields, ...havePermissionGroupFields].sort((a, b) => a.sort - b.sort)
}

export {useFormatTableHeader}
