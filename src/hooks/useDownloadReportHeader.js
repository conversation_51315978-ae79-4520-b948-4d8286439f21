import ExcelJS from 'exceljs'

export const useDownloadReportHeader = () => {
	/**
	 * 将luckysheet配置转换为excel并下载
	 * @param {Object} luckysheetConfig - luckysheet配置对象
	 * @param {string} fileName - 下载文件名
	 */
	const downloadExcel = async (luckysheetConfig, fileName = 'report.xlsx') => {
		try {
			const workbook = new ExcelJS.Workbook()
			const worksheet = workbook.addWorksheet('Sheet1')

			// 处理单元格数据
			if (luckysheetConfig.celldata) {
				luckysheetConfig.celldata.forEach((cell) => {
					const {r, c, v} = cell
					const excelCell = worksheet.getCell(r + 1, c + 1)

					// 设置单元格值
					if (v.m !== undefined) {
						excelCell.value = v.m
					} else if (v.v !== undefined) {
						excelCell.value = v.v
					}

					// 设置单元格样式
					if (v.ct) {
						// 设置文本对齐
						excelCell.alignment = {
							vertical: v.vt === 0 ? 'middle' : v.vt === 1 ? 'top' : 'bottom',
							horizontal: v.ht === 0 ? 'center' : v.ht === 1 ? 'left' : 'right',
						}
					}

					// 设置字体
					if (v.ff) {
						excelCell.font = {
							name: v.ff,
							size: v.fs || 11,
							bold: v.bl === 1,
							italic: v.it === 1,
							underline: v.ul === 1,
							color: {argb: v.fc ? v.fc.replace('#', '') : undefined},
						}
					}

					// 设置边框
					if (v.bd) {
						excelCell.border = {
							top: {style: getBorderStyle(v.bd.t)},
							right: {style: getBorderStyle(v.bd.r)},
							bottom: {style: getBorderStyle(v.bd.b)},
							left: {style: getBorderStyle(v.bd.l)},
						}
					}

					// 设置背景色
					if (v.bg) {
						excelCell.fill = {
							type: 'pattern',
							pattern: 'solid',
							fgColor: {argb: v.bg.replace('#', 'eee')},
						}
					}
				})
			}

			// 设置列宽
			if (luckysheetConfig.config && luckysheetConfig.config.columnlen) {
				Object.entries(luckysheetConfig.config.columnlen).forEach(([col, width]) => {
					worksheet.getColumn(parseInt(col) + 1).width = width / 8 // 转换为excel列宽单位
				})
			}

			// 设置行高
			if (luckysheetConfig.config && luckysheetConfig.config.rowlen) {
				Object.entries(luckysheetConfig.config.rowlen).forEach(([row, height]) => {
					worksheet.getRow(parseInt(row) + 1).height = height
				})
			}

			// 处理合并单元格
			if (luckysheetConfig.config && luckysheetConfig.config.merge) {
				Object.values(luckysheetConfig.config.merge).forEach((merge) => {
					worksheet.mergeCells(
						merge.r + 1,
						merge.c + 1,
						merge.r + merge.rs,
						merge.c + merge.cs
					)
				})
			}

			// 生成并下载文件
			const buffer = await workbook.xlsx.writeBuffer()
			const blob = new Blob([buffer], {
				type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			})
			const url = URL.createObjectURL(blob)
			const link = document.createElement('a')
			link.href = url
			link.download = fileName
			document.body.appendChild(link)
			link.click()
			document.body.removeChild(link)
			URL.revokeObjectURL(url)

			return {success: true, fileName}
		} catch (error) {
			console.error('导出Excel失败:', error)
			return {success: false, fileName, error}
		}
	}

	/**
	 * 获取边框样式
	 * @param {number} borderType - luckysheet边框类型
	 * @returns {string} exceljs边框样式
	 */
	const getBorderStyle = (borderType) => {
		const borderStyles = {
			1: 'thin',
			2: 'medium',
			3: 'dashed',
			4: 'dotted',
			5: 'thick',
			6: 'double',
			7: 'hair',
			8: 'mediumDashed',
			9: 'dashDot',
			10: 'mediumDashDot',
			11: 'dashDotDot',
			12: 'mediumDashDotDot',
			13: 'slantDashDot',
		}
		return borderStyles[borderType] || 'thin'
	}

	return {
		downloadExcel,
	}
}
