export const ACTION_KEY = {
	//临时报表创建
	TempReportPermission: 'TEMPORARY_REPORT_PERMISSION', // - 权限配置
	BatchDeleteReport: 'BATCH_DELETE_REPORT', // - 批量删除 - 删除全部
	BatchStopReport: 'BATCH_STOP_REPORT', // - 批量终止
	BatchRevokeReport: 'BATCH_REVOKE_REPORT', // - 批量撤回 - 撤回全部
	BatchResetReport: 'BATCH_RESET_REPORT', // - 批量重置
	UpdateCycleEnable: 'UPDATE_CYCLE_ENABLE', // - 业务表更新周期启用停用
}

export const useLocalStorage = () => {
	/**
	 * 保存
	 * @param {ACTION_KEY} key
	 * @param {*} data
	 */
	const save = (key, data) => {
		localStorage.setItem(key, JSON.stringify(data))
	}

	/**
	 * 获取
	 * @param {ACTION_KEY} key
	 * @returns
	 */
	const get = (key) => {
		return JSON.parse(localStorage.getItem(key))
	}

	/**
	 * 获取属性值
	 * @param {ACTION_KEY} key
	 * @param {string} attr // 针对缓存是JSON对象的属性获取处理
	 * @returns
	 */
	const getByAttr = (key, attr) => {
		const data = get(key)
		return data[attr]
	}

	/**
	 * 获取数组元素
	 * @param {*} key
	 * @param {*} index
	 * @returns
	 */
	const getByIndex = (key, index) => {
		const data = get(key)
		return data[index]
	}

	/**
	 * 更新
	 * @param {ACTION_KEY} key
	 * @param {string} attr // 针对缓存是JSON对象的属性更新处理
	 * @param {*} value
	 */
	const setByAttr = (key, attr, value) => {
		const data = get(key)
		data[attr] = value
		save(key, data)
	}

	/**
	 * 更新
	 * @param {ACTION_KEY} key
	 * @param {number} index // 针对缓存是数组的更新处理
	 * @param {*} value
	 */
	const setByIndex = (key, index, value) => {
		const data = get(key)
		data[index] = value
		save(key, data)
	}

	/**
	 * 删除
	 * @param {ACTION_KEY} key
	 */
	const remove = (key) => {
		localStorage.removeItem(key)
	}

	/**
	 * 删除属性
	 * @param {ACTION_KEY} key
	 * @param {string} attr // 针对缓存是JSON对象的属性删除处理
	 */
	const removeByAttr = (key, attr) => {
		const data = get(key)
		delete data[attr]
		save(key, data)
	}

	/**
	 * 删除数组元素
	 * @param {ACTION_KEY} key
	 * @param {number} index // 针对缓存是数组的删除处理
	 */
	const removeByIndex = (key, index) => {
		const data = get(key)
		data.splice(index, 1)
		save(key, data)
	}

	return {
		save,
		get,
		getByAttr,
		getByIndex,
		setByAttr,
		setByIndex,
		remove,
		removeByAttr,
		removeByIndex,
	}
}
export default useLocalStorage
