import * as Signalr from '@microsoft/signalr'

let signalr = {}
let signalrPath = {}
let signalrTimer = null

const useSignalrStop = (key) => {
	if (key && signalr.hasOwnProperty(key)) {
		if (signalr[key]?.state !== Signalr.HubConnectionState.Disconnected) {
			console.log(`停止 SignalR 连接 [${key}]`)
			signalr[key]?.stop()
			signalr[key] = null
			signalrPath[key] = null
		}
	} else {
		// 停止所有连接
		console.log('停止所有 SignalR 连接')
		Object.keys(signalr).forEach((connectionKey) => {
			if (signalr[connectionKey]?.state !== Signalr.HubConnectionState.Disconnected) {
				console.log(`停止 SignalR 连接 [${connectionKey}]`)
				signalr[connectionKey]?.stop()
				signalr[connectionKey] = null
				signalrPath[connectionKey] = null
			}
		})
	}
	clearTimeout(signalrTimer)
}

const useSignalr = (key, path) => {
	// console.log('useSignalr', key, path, signalr[key])

	if (
		!signalr.hasOwnProperty(key) ||
		!signalr[key] ||
		signalr[key]?.state === Signalr.HubConnectionState.Disconnected
	) {
		// 验证 GOV_CONFIG 和 WS 配置的有效性
		if (!window.GOV_CONFIG || !window.GOV_CONFIG.WS) {
			console.error('SignalR 配置错误: window.GOV_CONFIG.WS 未定义')
			return null
		}

		// 构造完整的 URL
		const fullUrl = `${window.GOV_CONFIG.WS}${path}`

		// 验证 URL 的有效性
		try {
			new URL(fullUrl)
		} catch (error) {
			console.error('SignalR URL 无效:', fullUrl, error)
			return null
		}

		// console.log(
		// 	'Creating new signalr connection:',
		// 	window.GOV_CONFIG.WS,
		// 	localStorage.getItem('access_token')
		// )
		signalr[key] = new Signalr.HubConnectionBuilder()
			.withUrl(fullUrl, {
				transport: Signalr.HttpTransportType.WebSockets,
				accessTokenFactory() {
					return localStorage.getItem('access_token')
				},
			})
			.build()

		signalr[key]
			?.start()
			.then(() => {
				console.log(`SignalR 连接成功 [${key}]:`, fullUrl)
				signalrPath[key] = path
			})
			.catch((err) => {
				console.error(`SignalR 连接失败 [${key}]:`, {
					url: fullUrl,
					error: err.message || err,
					config: {
						WS: window.GOV_CONFIG?.WS,
						path: path
					}
				})
				signalr[key] = null
				signalrPath[key] = null
			})
	} else {
		// console.log('Signalr connection is still alive: ', signalr[key]?.state)
	}
	clearTimeout(signalrTimer)
	signalrTimer = setTimeout(
		() => Object.keys(signalr).forEach((key) => useSignalr(key, signalrPath[key])),
		10000
	)

	return signalr[key]
}

export {useSignalr, useSignalrStop}
