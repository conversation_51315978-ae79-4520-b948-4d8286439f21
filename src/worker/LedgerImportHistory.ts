self.addEventListener(
	'message',
	function (e) {
		const {url, token} = e.data

		if (!url || e.origin) {
			return
		}
		let fn = async () => {
			await fetch(url, {
				method: 'GET',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: token,
				},
			})
				.then((res) => res.json())
				.then((data) => {
					self.postMessage(data)
				})
				.catch((err) => {
					self.postMessage({error: err})
				})
		}
		fn()
	},
	false
)
