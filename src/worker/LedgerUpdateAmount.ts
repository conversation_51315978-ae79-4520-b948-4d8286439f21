/**
 * 我的业务表卡片更新量和数据总量
 * @param {String} url
 * @param {Array} ids
 */
self.addEventListener(
	'message',
	function (e) {
		let fn = async () => {
			const {url, ids, token} = e.data
			if (!url || !ids || ids.length === 0 || e.origin) {
				self.postMessage({isComplete: true})
				return
			}

			for (let i = 0; i < ids.length; i++) {
				const id = ids[i]
				const __url = url.replace('{id}', id)

				await fetch(`${__url}`, {
					method: 'GET',
					mode: 'cors',
					headers: {
						'Content-Type': 'application/json',
						Authorization: token,
					},
				})
					.then((res) => res.json())
					.then((data) => {
						self.postMessage({id, data, index: i, isComplete: i === ids.length - 1, url: __url})
					})
			}
		}
		fn()
	},
	false
)
