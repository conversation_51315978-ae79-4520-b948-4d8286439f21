/**
 * 获取后台进度worker
 * @param {String} url
 * @param {string} ledgerId
 * @param {string} token
 */
self.addEventListener(
	'message',
	function (e) {
		let fn = async () => {
			const {url, ledgerId, token} = e.data
			if (!url || e.origin) {
				self.postMessage({isComplete: true})
				return
			}

			const __url = url.replace('{ledgerId}', ledgerId)

			await fetch(`${__url}`, {
				method: 'DELETE',
				mode: 'cors',
				headers: {
					'Content-Type': 'application/json',
					Authorization: token,
				},
			})
				.then((res) => res.json())
				.then((data) => {
					self.postMessage({data})
				})
		}
		fn()
	},
	false
)
