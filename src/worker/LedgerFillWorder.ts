self.addEventListener(
	'message',
	function (e) {
		if (e.origin) {
			return
		}
		const {rows, minColumn, maxColumn} = e.data
		const result: any = []

		for (const element of rows) {
			const row = element
			const arr = row
				.map((f: any, index: number) => {
					if (index < maxColumn + 1 && index > minColumn - 1) {
						if (f === null) {
							return {
								m: '',
								ct: {
									fa: 'General',
									t: 'g',
								},
								v: '',
							}
						}
						return f
					}
				})
				.filter(Boolean)
			result.push(arr)
		}
		self.postMessage({...e.data, result})
	},
	false
)
