/**
 * 获取表格字段
 * @param {String} url
 * @param {Array} data
 */
// const Queue
self.addEventListener(
	'message',
	function (e) {
		let fn = async () => {
			const {url, datas, token} = e.data
			if (!url || !datas || datas.length === 0 || e.origin) {
				self.postMessage({isComplete: true})
				return
			}

			for (let i = 0; i < datas.length; i++) {
				const params = datas[i]
				// const __url = url.replace('{id}', id)

				await fetch(`${url}`, {
					method: 'POST',
					mode: 'cors',
					body: JSON.stringify(params),
					headers: {
						'Content-Type': 'application/json',
						Authorization: token,
					},
				})
					.then((res) => res.json())
					.then((data) => {
						self.postMessage({
							field: datas[i].field,
							data,
							index: i,
							isComplete: i === data.length - 1,
						})
					})
			}
		}
		fn()
	},
	false
)
