<script setup lang="ts">
import {ref, watch} from 'vue'

interface Props {
	title: string
	drawer: boolean
	width?: string
}

const props = withDefaults(defineProps<Props>(), {
	title: '搜索',
	drawer: true,
	width: '30%',
})

const emits = defineEmits(['closed'])

const drawerComp = ref()
let drawer = props.drawer

watch(
	() => props.drawer,
	(newVal) => {
		drawer = newVal
	}
)

const __closed = () => {
	drawer = false
	emits('closed')
}
</script>
<template>
	<el-drawer
		class="drawer-comp"
		ref="drawerComp"
		:title="title"
		v-model="drawer"
		@closed="__closed"
		:size="width"
		:append-to-body="true"
	>
		<slot name="body"></slot>
	</el-drawer>
</template>
