<script setup lang="ts">
import {STAFFROLEARRAY} from '@/define/organization.define'
import {
	statusTrans,
	reportStatusTrans,
	FillerFillingState,
	FillerFillingStateList,
} from '@/define/statement.define'
import {computed, inject, onMounted, ref, watch} from 'vue'
import {Check, Right} from '@element-plus/icons-vue'
const axios = inject('#axios') as any
const props = defineProps({
	// 1:计划任务 2:报表任务
	type: {type: Number, default: 1},
	// 1.下发填报 2.内部填报
	fillingMode: {type: Number},
	reportTaskId: {type: String, default: null},
	areaOrganizationUnitId: {type: String, default: null},
	planTaskId: {type: String, default: null},
	baseData: {type: Object},
	showTitle: {type: Boolean, default: true},
	title: {type: String, default: null},
	request: {type: Boolean, default: false},
})
const fillingMode = computed(() => props.fillingMode)
const timeLineData = ref<any[]>([])
function getScheduleProgress() {
	timeLineData.value = []
	return new Promise(async (resolve, reject) => {
		// 计划任务步骤
		if (props.type === 1) {
			const res = await axios.get(`/api/filling/step/plan-task?planTaskId=${props.planTaskId}`)
			if (res.status === 200) {
				console.log(res)
				const {data} = res
				// 计划任务下发填报
				if (data.fillingMode === 1) {
					timeLineData.value.push(
						{
							name: '创建任务',
							user: data.createUser?.name,
							time: data.creationTime,
							show: true,
							status: data.status,
							icon:
								data.status === 1 || data.status === 2 ? Right : data.creationTime ? Check : null,
							type:
								data.status === 1 || data.status === 2
									? 'warning'
									: data.creationTime
									? 'success'
									: null,
							size: 'large',
							isCurrent: data.status === 1 || data.status === 2,
							isReject: false,
							belongDepartment:
								data.createUser?.parentDepartmentName + '-' + data.createUser?.departmentName,
						},
						{
							name: '分管领导审核',
							user: data.inChargeLeaderAuditor?.name,
							time: data.inChargeLeaderAuditorTime,
							icon: data.status === 6 ? Right : data.inChargeLeaderAuditorTime ? Check : null,
							type:
								data.status === 6 ? 'warning' : data.inChargeLeaderAuditorTime ? 'success' : null,
							size: 'large',
							show: data.inChargeLeaderAuditor !== null,
							status: data.status,
							isCurrent: data.status === 6,
							isReject: data.status === 4 && !data.auditTime,
							belongDepartment: data?.inChargeLeaderAuditor
								? data?.inChargeLeaderAuditor?.department?.parent?.name +
								  '-' +
								  data?.inChargeLeaderAuditor?.department.name
								: null,
						},
						{
							name: '数据领导审核',
							user: data.dataLeader?.name,
							time: data.auditTime,
							icon: data.status === 7 ? Right : data.auditTime ? Check : null,
							type: data.status === 7 ? 'warning' : data.auditTime ? 'success' : null,
							size: 'large',
							show: !data.createUser.staffRole.includes(STAFFROLEARRAY[4]),
							status: data.status,
							isCurrent: data.status === 7,
							isReject: data.status === 4 && data.auditTime,
							belongDepartment:
								data.dataLeader === null
									? null
									: data.dataLeader?.department?.parent?.name +
									  '-' +
									  data.dataLeader?.department?.name,
						},
						{
							name: '填报部门',
							user: data.departments.map((v: any) => v.name).toString(),
							icon:
								data.status === 3
									? Right
									: data.status === 5 ||
									  data.status === 8 ||
									  data.status === 9 ||
									  data.status === 10
									? Check
									: null,
							type:
								data.status === 3
									? 'warning'
									: data.status === 5 ||
									  data.status === 8 ||
									  data.status === 9 ||
									  data.status === 10
									? 'success'
									: null,
							size: 'large',
							time: null,
							show: true,
							status: data.status,
							isCurrent: data.status === 3,
							belongDepartment: null,
						},
						{
							name: '结束任务',
							user: null,
							time: null,
							icon:
								data.status === 5 || data.status === 8 || data.status === 9 || data.status === 10
									? Check
									: null,
							type:
								data.status === 5 || data.status === 9 || data.status === 8 || data.status === 10
									? 'success'
									: null,
							size: 'large',
							show: true,
							status: data.status,
							isCurrent:
								data.status === 5 || data.status === 9 || data.status === 8 || data.status === 10,
							belongDepartment: null,
						}
					)
				}
				// 计划任务内部填报
				if (data.fillingMode === 2) {
					timeLineData.value.push(
						{
							name: '创建任务',
							user: data.createUser?.name,
							time: data.creationTime,
							icon:
								data.status === 1 || data.status === 2 ? Right : data.creationTime ? Check : null,
							type:
								data.status === 1 || data.status === 2
									? 'warning'
									: data.creationTime
									? 'success'
									: null,
							size: 'large',
							show: true,
							status: data.status,
							isCurrent: data.status === 1 || data.status === 2,
							belongDepartment:
								data.createUser?.parentDepartmentName + '-' + data.createUser?.departmentName,
						},
						{
							name: '填报人员',
							user: data.reportTaskAreaOrganizationUnitFillers,
							time: null,
							show: true,
							icon:
								data.status === 3
									? Right
									: data.status === 5 ||
									  data.status === 8 ||
									  data.status === 9 ||
									  data.status === 10
									? Check
									: null,
							type:
								data.status === 3
									? 'warning'
									: data.status === 5 ||
									  data.status === 8 ||
									  data.status === 9 ||
									  data.status === 10
									? 'success'
									: null,
							size: 'large',
							status: data.status,
							isCurrent: data.status === 3,
							belongDepartment: null,
							// data.filter?.parentDepartmentName + '-' + data.filter?.departmentName,
						},
						{
							name: '结束任务',
							user: null,
							time: null,
							show: true,
							icon:
								data.status === 5 || data.status === 8 || data.status === 9 || data.status === 10
									? Check
									: null,
							type:
								data.status === 5 || data.status === 9 || data.status === 8 || data.status === 10
									? 'success'
									: null,
							size: 'large',
							status: data.status,
							isCurrent: data.status === 9 || data.status === 8 || data.status === 10,
							belongDepartment: null,
						}
					)
				}
				resolve('')
			}
		}
		// 报表任务步骤
		if (props.type === 2) {
			const res = await axios.get(
				`/api/filling/step/report-task?reportTaskId=${props.reportTaskId}&areaOrgreportTaskIdanizationUnitId=${props.areaOrganizationUnitId}`
			)
			if (res.status === 200) {
				const data = res.data
				console.log('fillingMode', props.fillingMode)
				const fillingMode = props.fillingMode ? props.fillingMode : data.fillingMode
				// 内部填报
				if (fillingMode === 2) {
					timeLineData.value.push(
						{
							name: '创建任务',
							time: data.creationTime,
							user: data?.planTask.creator?.name,
							show: true,
							status: data?.status,
							icon: data?.reportTask.creationTime ? Check : Right,
							type: 'success',
							size: 'large',
							isCurrent: false,
							belongDepartment: null,
						},
						{
							name: '填报人员',
							user: data.reportTaskAreaOrganizationUnitFillers,
							// data?.status === 7
							// 	? data?.actualFillerName
							// 	: data?.status === 10
							// 	? data?.filler?.name
							// 		? data?.filler?.name
							// 		: data.fillers?.map((v) => v.name).toString()
							// 	: JSON.parse(localStorage.getItem('currentUserInfo') as string).name,
							show: true,
							status: data?.status,
							icon: data?.status === 7 || data?.status === 14 ? Check : Right,
							type: data.status === 7 || data?.status === 14 ? 'success' : 'warning',
							size: 'large',
							isCurrent: data.status !== 7 && data.status !== 14,
							belongDepartment: null,
						},
						{
							name: '数据审核',
							user:
								data?.status === 10
									? data?.inChargeLeaderAuditor?.name
									: data?.parentDataLeader?.name,
							show: true,
							status: data?.status,
							icon: data?.status === 7 || data?.status === 14 ? Check : null,
							type: data.status === 7 || data?.status === 14 ? 'success' : null,
							size: 'large',
							isCurrent: false,
							belongDepartment:
								data?.status === 10
									? data?.inChargeLeaderAuditor?.department?.parent?.name +
									  '-' +
									  data?.inChargeLeaderAuditor?.department?.name
									: data?.parentDataLeader
									? data?.parentDataLeader?.department?.parent?.name +
									  '-' +
									  data?.parentDataLeader?.department?.name
									: null,
						},
						{
							name: '结束任务',
							user: null,
							show: true,
							status: data?.status,
							icon: data?.status === 7 || data?.status === 14 ? Check : null,
							type: data?.status === 7 || data?.status === 14 ? 'success' : null,
							size: 'large',
							isCurrent: data?.status === 7 || data?.status === 14 || data?.status === 2,
							isStop: data.stop,
							belongDepartment: null,
						}
					)
				}
				// 下发填报
				if (fillingMode === 1) {
					timeLineData.value.push(
						{
							name: '创建任务',
							time: data.creationTime,
							user: data?.planTask.creator?.name,
							show: true,
							status: data?.status,
							icon: data?.reportTask.creationTime ? Check : Right,
							type: 'success',
							size: 'large',
							isCurrent: false,
							belongDepartment: null,
						},

						{
							name: '转发任务',
							time: data.operationTime,
							user: data?.dataLeader?.name,
							show: data?.childrenReportTaskAreaOrganizationUnits.length === 0,
							status: data?.status,
							icon: data.status === 8 ? Right : data.operationTime ? Check : null,
							type: data.status === 8 ? 'warning' : data.operationTime ? 'success' : null,
							size: 'large',
							isCurrent: data.status === 8,
							belongDepartment: data?.dataLeader
								? data?.dataLeader?.department?.parent?.name +
								  '-' +
								  data?.dataLeader?.department?.name
								: null,
						},
						{
							name:
								data.reportTaskAreaOrganizationUnitFillers.length !== 0 ? '填报人员' : '填报数据',
							time: data?.fillerSubmitTime,
							user:
								data.reportTaskAreaOrganizationUnitFillers.length !== 0
									? data.reportTaskAreaOrganizationUnitFillers
									: data?.waitingSendAreaOrganizationUnitNames,
							show: data?.childrenReportTaskAreaOrganizationUnits.length === 0,
							status: data?.status,
							icon:
								data.status === 0 || data.status === 10 || data?.status === 3 || data.status === 4
									? Right
									: data?.fillerSubmitTime
									? Check
									: null,
							type:
								data.status === 0 || data.status === 10 || data?.status === 3 || data.status === 4
									? 'warning'
									: data?.fillerSubmitTime
									? 'success'
									: null,
							size: 'large',
							isReject: data?.status === 3,
							isCurrent:
								data.status === 0 || data.status === 10 || data?.status === 3 || data.status === 4,
							belongDepartment:
								data?.filler?.parentDepartmentName && data?.filler?.departmentName
									? data?.filler?.parentDepartmentName + '-' + data?.filler?.departmentName
									: null,
						},
						{
							name: '分管领导审核数据',
							time: data?.inChargeLeaderAuditorTime,
							user: data?.inChargeLeaderAuditor?.name,
							show: data?.inChargeLeaderAuditor,
							status: data?.status,
							icon: data.status === 9 ? Right : data?.inChargeLeaderAuditorTime ? Check : null,
							type:
								data.status === 9 ? 'warning' : data?.inChargeLeaderAuditorTime ? 'success' : null,
							size: 'large',
							isCurrent: data.status === 9,
							belongDepartment: data?.inChargeLeaderAuditor
								? data?.inChargeLeaderAuditor?.department?.parent?.name +
								  data?.inChargeLeaderAuditor?.department.name
								: null,
						},
						{
							name: '填报数据',
							user: null,
							show: data?.childrenReportTaskAreaOrganizationUnits.length !== 0,
							status: data?.status,
							icon: data?.status === 2 || data?.status === 7 ? Check : Right,
							type: data?.status === 2 || data?.status === 7 ? 'success' : 'warning',
							size: 'large',
							isCurrent: data?.status !== 2 && data?.status !== 7,
							isStop: data.stop,
							belongDepartment: data?.childrenReportTaskAreaOrganizationUnits
								.map((v) =>
									v.department ? v.department?.parent?.name + '-' + v.department?.name : null
								)
								.toString(),
						},
						{
							name: '数据审核',
							time: data?.auditTime,
							user: data?.parentDataLeader?.name,
							show: data?.childrenReportTaskAreaOrganizationUnits.length === 0,
							status: data?.status,
							icon: data?.status === 1 ? Right : data?.auditTime ? Check : null,
							type: data?.status === 1 ? 'warning' : data?.auditTime ? 'success' : null,
							size: 'large',
							isCurrent: data?.status === 1,
							belongDepartment: data?.parentDataLeader
								? data?.parentDataLeader?.department?.parent?.name +
								  '-' +
								  data?.parentDataLeader?.department?.name
								: null,
						},
						{
							name: '结束任务',
							user: null,
							show: true,
							status: data?.status,
							icon: data?.auditTime || data?.status === 7 ? Check : null,
							type: data?.auditTime || data?.status === 7 ? 'success' : null,
							size: 'large',
							isCurrent: false,
							isStop: data.stop,
							belongDepartment: null,
						}
					)
					// debugger
				}
			}
		}
	})
}
onMounted(async () => {
	await getScheduleProgress()
})
watch(
	() => props.request,
	async (val) => {
		if (val) {
			await getScheduleProgress()
		}
		// console.log(11, val)
	}
)
</script>
<template>
	<!-- <div class="title" v-if="showTitle">
		{{ props.title ? props.title : '流程进度' }}
	</div> -->
	<el-timeline>
		<template v-for="(item, index) in timeLineData" :key="index">
			<el-timeline-item
				v-if="item.show"
				:icon="item.icon"
				:type="item.type"
				:size="item.size"
				:hide-timestamp="true"
				placement="top"
			>
				<div text="gray">{{ item.time }}</div>
				<div style="font-weight: bold" mt-5px>
					{{ item.name }}
					<span v-if="item.isCurrent" inline-block ml-10px
						>(当前节点:{{
							type === 1 ? statusTrans[item.status - 1].name : reportStatusTrans[item.status].name
						}})</span
					>
				</div>
				<div mt-5px>
					<div v-if="item.name === '填报人员'">
						<div
							v-for="iitem in item.user"
							:class="{
								isFinish:
									iitem.reportTaskAreaOrganizationUnitFillerStatus !== FillerFillingState.Submited,
							}"
						>
							<div inline-block>
								<el-icon
									color="green"
									v-if="
										iitem.reportTaskAreaOrganizationUnitFillerStatus === FillerFillingState.Submited
									"
									><SuccessFilled
								/></el-icon>
								{{
									iitem.filler?.name +
									' ' +
									iitem.filler?.department?.parent.name +
									iitem.filler?.department?.name
								}}
							</div>
							<div inline-block>
								{{
									FillerFillingStateList.filter(
										(v) => v.value === iitem.reportTaskAreaOrganizationUnitFillerStatus
									)[0].label
								}}
								<span
									v-if="
										iitem.reportTaskAreaOrganizationUnitFillerStatus === FillerFillingState.Submited
									"
									>{{ iitem.submitTime }}</span
								>
							</div>
						</div>
					</div>
					<div v-else>{{ item.user }}</div>
					<span text="gray" v-if="item.belongDepartment" inline-block ml-10px
						>({{ item.belongDepartment }})</span
					>
				</div>
				<div v-if="item.isReject" mt-10px text="red">已驳回</div>
				<div v-if="item.isStop" text="red">已终止</div>
			</el-timeline-item>
		</template>
	</el-timeline>
</template>
<style scoped lang="scss">
.title {
}
.isFinish {
	color: gray;
}
</style>
