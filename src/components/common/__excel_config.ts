export const __xls_config: any = {
	default: {
		gridKey: 'excelComp',
		container: 'excelComp',
		lang: 'zh',
		title: '-',

		enableAddRow: false,
		enableAddBackTop: false,
		showinfobar: false,
		showsheetbar: false,
		sheetFormulaBar: false,
		devicePixelRatio: 1.1,

		showtoolbar: false,
		showtoolbarConfig: {
			undoRedo: false,
			paintFormat: false,
			currencyFormat: false,
			percentageFormat: false,
			numberDecrease: false,
			numberIncrease: false,
			moreFormats: true,
			font: false,
			fontSize: true,
			bold: true,
			italic: true,
			strikethrough: true,
			underline: true,
			textColor: true,
			fillColor: true,
			border: true,
			mergeCell: true,
			horizontalAlignMode: true,
			verticalAlignMode: true,
			textWrapMode: false,
			textRotateMode: false,
			image: false,
			link: false,
			chart: false,
			postil: false,
			pivotTable: false,
			function: false,
			frozenMode: true,
			sortAndFilter: false,
			conditionalFormat: false,
			dataVerification: false,
			splitColumn: false,
			screenshot: false,
			findAndReplace: false,
			protection: false,
			print: false,
		},

		cellRightClickConfig: {
			clear: false,
			copy: true,
			copyAs: false,
			paste: true,
			insertRow: true,
			insertColumn: false,
			deleteRow: true,
			deleteColumn: false,
			deleteCell: false,
			hideRow: false,
			hideColumn: false,
			rowHeight: false,
			columnWidth: false,
			matrix: false,
			sort: false,
			filter: false,
			chart: false,
			image: false,
			link: false,
			data: false,
			cellFormat: false,
		},

		data: [
			{
				name: 'Sheet',
				color: '#000',
				index: 0,
				status: 1,
				order: 0,
				hide: 0,
				row: 100,
				column: 26,
				defaultRowHeight: 19,
				defaultColWidth: 130,
				celldata: [],
				config: {
					merge: {},
					rowlen: {},
					columnlen: {},
					customHeight: {},
					customWidth: {},
					rowhidden: {},
					colhidden: {},
					borderInfo: [],
					authority: {
						selectLockedCells: 1,
						selectunLockedCells: 1,
						formatCells: 1,
						formatColumns: 1,
						formatRows: 1,
						insertColumns: 0,
						insertRows: 1,
						insertHyperlinks: 0,
						deleteColumns: 0,
						deleteRows: 1,
						sort: 0,
						filter: 0,
						usePivotTablereports: 0,
						editObjects: 0,
						editScenarios: 0,
						sheet: 1,
						hintText: '当前工作表已开启保护,若要修改请先关闭保护',
						algorithmName: 'None',
						saltValue: null,

						// 设置可编辑和不可编辑区域
						allowRangeList: [
							{
								//不可编辑区域
								name: 'NotEditable',
								password: '123456',
								hintText: '区域不可编辑,请先输入密码解锁',
								algorithmName: 'None',
								saltValue: null,
								sqref: '$A$0:$B$0', //动态设置不可编辑区域范围
							},
							{
								//可编辑区域
								name: 'Editable',
								password: '',
								hintText: '',
								algorithmName: 'None',
								saltValue: null,
								sqref: '$A$1:$ZZ$100000',
							},
						],
					},
				},
				scrollLeft: 0,
				scrollTop: 0,
				luckysheet_select_save: [],
				calcChain: [],
				isPivotTable: false,
				pivotTable: {},
				filter_select: {},
				filter: null,
				luckysheet_alternateformat_save: [],
				luckysheet_alternateformat_save_modelCustom: [],
				luckysheet_conditionformat_save: {},
				frozen: {},
				chart: [],
				zoomRatio: 1,
				image: [],
				showGridLines: 1,
				dataVerification: {},
			},
		],

		// 钩子
		hook: {},
	},
	// end 默认初始化通用配置 =======================

	// xlsx-plus
	plus: {
		gridKey: 'xlsx-plus',
		container: 'xlsx-plus',
		lang: 'zh',
		title: '-',

		enableAddRow: false,
		enableAddBackTop: false,
		showinfobar: false,
		sheetFormulaBar: false,
		devicePixelRatio: 1.1,
		// 自定义工具栏
		showtoolbar: true,
		showtoolbarConfig: {
			undoRedo: true,
			paintFormat: true,
			currencyFormat: true,
			percentageFormat: true,
			numberDecrease: true,
			numberIncrease: true,
			moreFormats: true,
			font: true,
			fontSize: true,
			bold: true,
			italic: true,
			strikethrough: true,
			underline: true,
			textColor: true,
			fillColor: true,
			border: true,
			mergeCell: true,
			horizontalAlignMode: true,
			verticalAlignMode: true,
			textWrapMode: true,
			textRotateMode: true,
			image: true,
			link: true,
			chart: true,
			postil: true,
			pivotTable: true,
			function: true,
			frozenMode: true,
			sortAndFilter: true,
			conditionalFormat: true,
			dataVerification: true,
			splitColumn: true,
			screenshot: true,
			findAndReplace: true,
			protection: true,
			print: true,
		},

		// 自定义右键菜单
		cellRightClickConfig: {
			clear: false,
			copy: true,
			copyAs: false,
			paste: true,
			insertRow: true,
			insertColumn: false,
			deleteRow: true,
			deleteColumn: false,
			deleteCell: false,
			hideRow: false,
			hideColumn: false,
			rowHeight: false,
			columnWidth: false,
			matrix: false,
			sort: false,
			filter: false,
			chart: false,
			image: false,
			link: false,
			data: false,
			cellFormat: false,
		},

		// sheet 菜单
		showsheetbar: false,
		showsheetbarConfig: {
			add: false,
			menu: false,
			sheet: false,
		},
		sheetRightClickConfig: {
			delete: false,
			copy: false,
			rename: false,
			color: false,
			hide: false,
			move: false,
		},

		// 工作表配置
		data: [
			{
				name: 'Sheet',
				color: '#000',
				index: 0,
				status: 0,
				order: 0,
				hide: 0,
				row: 30,
				column: 26,
				defaultRowHeight: 24,
				defaultColWidth: 130,
				celldata: [],
				config: {
					merge: {},
					rowlen: {},
					columnlen: {},
					customHeight: {},
					customWidth: {},
					rowhidden: {},
					colhidden: {},
					borderInfo: [],
					authority: {
						selectLockedCells: 1,
						selectunLockedCells: 1,
						formatCells: 1,
						formatColumns: 1,
						formatRows: 1,
						insertColumns: 1,
						insertRows: 1,
						insertHyperlinks: 0,
						deleteColumns: 1,
						deleteRows: 1,
						sort: 0,
						filter: 0,
						usePivotTablereports: 0,
						editObjects: 0,
						editScenarios: 0,
						sheet: 0,
						hintText: '当前工作表已开启保护,若要修改请先关闭保护',
						algorithmName: 'None',
						saltValue: null,

						allowRangeList: [
							{
								name: 'NotEditable',
								password: '123456',
								hintText: '工作表已开启保护,请输入密码解锁',
								algorithmName: 'None',
								saltValue: null,
								sqref: '$A$0:$B$0',
							},
							{
								name: 'Editable',
								password: '',
								hintText: '',
								algorithmName: 'None',
								saltValue: null,
								sqref: '$A$1:$ZZ$100000',
							},
						],
					},
				},
				scrollLeft: 0,
				scrollTop: 0,
				luckysheet_select_save: [],
				calcChain: [],
				isPivotTable: false,
				pivotTable: {},
				filter_select: {},
				filter: null,
				luckysheet_alternateformat_save: [],
				luckysheet_alternateformat_save_modelCustom: [],
				luckysheet_conditionformat_save: {},
				frozen: {},
				chart: [],
				zoomRatio: 1,
				image: [],
				showGridLines: 1,
				dataVerification: {},
			},
		],

		// 钩子
		hook: {},
	},
}
