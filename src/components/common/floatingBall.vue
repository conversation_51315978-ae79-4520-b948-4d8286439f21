<!--
悬浮球组件，接受一个事件参数，当点击该悬浮球就会触发事件
-->
<template>
	<div
		:style="{right: `${right}px`, bottom: `${bottom}px`}"
		class="floating-ball"
		@click="handleClick"
		@mousedown="startDrag"
		@touchstart="startTouchDrag"
		relative
	>
		<div class="menu" v-if="isOpen" :style="{height: isOpen ? '160px' : '0px'}">
			<div class="menu-item" @click.stop="handleOpen(4)">发版日志</div>
			<div class="menu-item" @click.stop="handleOpen(0)">操作手册</div>
			<div class="menu-item" @click.stop="handleOpen(1)">答疑手册</div>
			<div class="menu-item" @click.stop="handleOpen(2)">问题反馈</div>
			<div class="menu-item" @click.stop="handleOpen(3)">智能助手</div>
		</div>
		<!-- <ul>
			<li @click.stop="handleOpen(0)">操作手册</li>
			<li @click.stop="handleOpen(1)">答疑手册</li>
			<li @click.stop="handleOpen(2)">问题反馈</li>
			<li @click.stop="handleOpen(3)">智能助手</li>
		</ul> -->
	</div>
</template>

<script lang="ts" setup>
import {ElMessage} from 'element-plus'
import {ref, defineProps, onMounted} from 'vue'

const props = defineProps({
	onClick: Function,
})
const emits = defineEmits(['handleClickItem'])
const isOpen = ref(false)
const ballSize = 50
const right = ref(50) // 初始距右边距离
const bottom = ref(70) // 初始距底部距离
const isDragging = ref(false)
let startX, startY, offsetX: any, offsetY: any, moved: any

const startDrag = (event: any) => {
	disableTextSelection()
	isDragging.value = true
	startX = event.clientX
	startY = event.clientY
	// 根据悬浮球的当前位置重新计算 offsetX 和 offsetY
	offsetX = window.innerWidth - right.value - 25 - startX
	offsetY = window.innerHeight - bottom.value - 30 - startY
	moved = false
	window.addEventListener('mousemove', onMouseMove)
	window.addEventListener('mouseup', stopDrag)
}

const startTouchDrag = (event: any) => {
	disableTextSelection()
	event.preventDefault()
	const touch = event.touches[0]
	isDragging.value = true
	startX = touch.clientX
	startY = touch.clientY
	// 同样重新计算 offsetX 和 offsetY
	offsetX = window.innerWidth - right.value - 30 - startX
	offsetY = window.innerHeight - bottom.value - 20 - startY
	moved = false

	window.addEventListener('touchmove', onTouchMove, {passive: false})
	window.addEventListener('touchend', stopTouchDrag)
}

let animationFrameId: any = null
const onMouseMove = (event: any) => {
	if (!isDragging.value) return
	if (animationFrameId) cancelAnimationFrame(animationFrameId)

	animationFrameId = requestAnimationFrame(() => {
		updatePosition(event.clientX, event.clientY)
	})
}
const onTouchMove = (event: any) => {
	event.preventDefault()
	if (!isDragging.value) return
	const touch = event.touches[0]

	if (animationFrameId) cancelAnimationFrame(animationFrameId)

	animationFrameId = requestAnimationFrame(() => {
		updatePosition(touch.clientX, touch.clientY)
	})
}
const updatePosition = (x, y) => {
	moved = true
	const halfBallSize = ballSize / 2
	right.value = Math.max(
		-halfBallSize,
		Math.min(window.innerWidth - x - offsetX - halfBallSize, window.innerWidth - halfBallSize)
	)
	bottom.value = Math.max(
		-halfBallSize,
		Math.min(window.innerHeight - y - offsetY - halfBallSize, window.innerHeight - halfBallSize)
	)
}
const stopDrag = () => {
	enableTextSelection()
	isDragging.value = false
	window.removeEventListener('mousemove', onMouseMove)
	window.removeEventListener('mouseup', stopDrag)

	if (!moved && props.onClick) {
		props.onClick()
	}
}

const stopTouchDrag = () => {
	enableTextSelection()
	isDragging.value = false
	window.removeEventListener('touchmove', onTouchMove)
	window.removeEventListener('touchend', stopTouchDrag)

	if (!moved && props.onClick) {
		props.onClick()
	}
}

function disableTextSelection() {
	document.body.style.userSelect = 'none'
}

function enableTextSelection() {
	document.body.style.userSelect = ''
}
const handleOpen = (type: number) => {
	if (type === 3) {
		ElMessage.warning('暂未开通')
	} else {
		emits('handleClickItem', type)
	}
}
const handleClick = () => {
	// 处理点击逻辑
	isOpen.value = !isOpen.value
}
onMounted(() => {
	document.body.addEventListener('click', (event: any) => {
		if (!event?.target?.matches('.floating-ball')) isOpen.value = false
	})
})
</script>

<style scoped>
.floating-ball {
	width: 40px;
	height: 40px;
	background-image: url('../../assets/image/aibot.png');
	background-size: 100% 100%;
	border-radius: 50%;
	position: absolute;
	cursor: pointer;
	text-align: center;
	line-height: 50px;
	scale: 1;
	transition: scale 0.3s ease, box-shadow 0.3s ease;
	z-index: 100;
	box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2); /* 黑色阴影 */
	/* @apply flex justify-center items-center; */
	display: flex;
	justify-content: center;
	align-items: center;
	.menu {
		position: absolute;
		bottom: 50px;
		width: 80px;
		left: -20px;
		background: #fff;
		/* transition: all 1s ease; */
		box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
		/* scale: 1; */
		.menu-item {
			border-bottom: 1px solid #e5e5e5;
			height: 40px;
			line-height: 40px;
			&:hover {
				background: var(--z-main);
				color: #fff;
			}
		}
	}
}

.floating-ball:active {
	cursor: grabbing;
}

.floating-ball:hover {
	box-shadow: 0 6px 12px 0 rgba(0, 0, 0, 0.3); /* 悬停时的阴影 */
	scale: 1;
}
</style>
