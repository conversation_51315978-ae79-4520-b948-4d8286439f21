<script setup lang="ts">
import {nextTick, ref} from 'vue'
import {useUserStore} from '@/stores/useUserStore'
import {ElMessage} from 'element-plus'

const emits = defineEmits(['update:modelValue', 'update:result'])
const user = useUserStore().getUserInfo
const department = useUserStore().getCurrentDepartment
const checked = ref(false)
const output = () => {
	if (!checked.value) {
		ElMessage.error('请先勾选“本人承诺”')
		return
	}

	emits('update:result', checked.value)
	emits('update:modelValue', false)
}
const onOpen = () => {
	checked.value = false
	emits('update:result', checked.value)
}
</script>
<template>
	<Dialog
		v-bind="$attrs"
		title="数据导出声明"
		width="700"
		@open="onOpen"
		@close="emits('update:modelValue', false)"
		@click-close="emits('update:modelValue', false)"
		@click-confirm="output"
	>
		<div class="declaration">
			<p>
				本人 <strong>[{{ user.name }}]</strong>，渝快政账号
				<strong>[{{ user.userName }}]</strong>，因工作需要从
				<strong>[“一表通”智能报表组件]</strong>
				导出数据在业务上使用。在此郑重承诺，严格遵守数据安全规定，绝不向任何未经授权的第三方泄露所导出数据。若因本人原因导致数据安全问题发生，本人愿意承担全部法律责任与赔偿义务。同时，
				<strong>[{{ department.departmentExtendName || '-' }}]</strong>
				也将依据相关法律法规与内部制度，对数据安全问题承担相应的单位责任并积极配合调查处理。
			</p>
			<div class="checkbox">
				<el-checkbox v-model="checked" label="本人承诺"></el-checkbox>
			</div>
		</div>
	</Dialog>
</template>
<style scoped lang="scss">
.declaration {
	p {
		color: var(--z-font-color);
		font-size: 15px;
		line-height: 2.5;
		text-align: justify;
		text-indent: 20px;
	}

	strong {
		color: var(--z-danger);
	}

	.checkbox {
		text-align: right;
	}
}
</style>
