<script setup lang="ts">
import {computed, onMounted, ref, watch} from 'vue'

interface Props {
	title?: string
	visible?: boolean
	visibleFooterButton?: boolean
	visibleCloseButton?: boolean
	visibleConfirmButton?: boolean
	width?: number | string
	showClose?: boolean
	closeOnClickModal?: boolean
	closeOnPressEscape?: boolean
	cancelText?: string
	confirmText?: string
	loading?: boolean
	modal?: boolean
	isDialog?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	title: '标题',
	visible: false,
	visibleFooterButton: true,
	visibleCloseButton: true,
	visibleConfirmButton: true,
	width: '30%',
	showClose: true,
	closeOnClickModal: false,
	closeOnPressEscape: true,
	cancelText: '取消',
	confirmText: '确定',
	loading: false,
	isDialog: true,
	modal: true,
})

const emits = defineEmits(['clickCancel', 'clickConfirm', 'closed'])

const visible = ref(props.visible)
const visibleFooterButton = props.visibleFooterButton

watch(
	() => [props.visible],
	(newVal) => {
		console.log(newVal)
		visible.value = newVal[0]
	}
)

const __title = computed(() => props.title)

const __closed = () => {
	// visible.value = false
	emits('closed')
}

const clickConfirm = () => {
	emits('clickConfirm')
	// __closed()
}

const clickCancel = () => {
	emits('clickCancel')
	// __closed()
}
onMounted(() => {})
</script>
<template>
	<el-dialog
		v-if="isDialog"
		v-model="visible"
		:title="__title"
		:width="width"
		:show-close="showClose"
		:close-on-click-modal="closeOnClickModal"
		:close-on-press-escape="closeOnPressEscape"
		draggable
		destroy-on-close
		@close="__closed"
		:append-to-body="true"
	>
		<!-- 内容插槽 -->
		<slot name="body"></slot>
		<slot name="default"></slot>
		<template #footer>
			<template v-if="visibleFooterButton">
				<el-button v-if="props.visibleCloseButton" @click="clickCancel">
					{{ cancelText }}
				</el-button>
				<el-button
					v-if="props.visibleConfirmButton"
					type="primary"
					@click="clickConfirm"
					:loading="loading"
				>
					{{ confirmText }}
				</el-button>
			</template>
			<slot name="footer"></slot>
		</template>
	</el-dialog>
	<div v-else :style="{width: width}">
		<slot name="body"></slot>
	</div>
</template>
<style lang="scss" scoped></style>
