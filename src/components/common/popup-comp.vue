<!-- 自定义弹窗组件 
    1.支持随意拖动
    2.支持弹窗内容随意填写
    3.支持拖动四周改变弹窗大小
-->
<script lang="ts" setup>
import {computed, onMounted, ref, watch} from 'vue'
import {CloseBold} from '@element-plus/icons-vue'
import {APIConfig} from '@/api/config'
import {uploadFile} from '@/plugin/upload'
// import jsPDF from 'jspdf'
// import html2canvas from 'html2canvas'
const {title, full, offset, w, h} = defineProps({
	title: {
		type: String,
		default: '',
	},
	full: {
		type: Boolean,
		default: true,
	},
	w: {
		type: Number,
		default: 480,
	},
	h: {
		type: Number,
		default: 500,
	},
	offset: {
		type: Array,
		default: () => ['80%', '65%'],
	},
})
const emit = defineEmits(['close', 'changeFull', 'clickDownLoad'])
const ml: any = ref(offset[0])
const mt: any = ref(offset[1])
const width = ref(w)
const height = ref(h)
const dialogEl = ref()
const leftRef = ref()
const dialogB = ref(false)
const isFull = ref(full)
const uploadModelVisible = ref(false)
const mouseDown = (e) => {
	e.preventDefault()
	e.stopPropagation()

	let windowH = window.innerHeight //获取浏览器的可用高度
	// let windowW = window.innerWidth;//获取浏览器的可用宽度
	let el = dialogEl.value //获取需要拖拽移动的容器
	var disX = e.clientX - el.offsetLeft
	var disY = e.clientY - el.offsetTop

	dialogB.value = true

	// 禁止文本选择
	document.onselectstart = () => false
	document.body.style.userSelect = 'none'

	document.onmousemove = (e) => {
		if (!dialogB.value) return false
		if (e.clientY < 0 || e.clientY > windowH - 40) {
			return false
		}
		let left = e.clientX - disX
		let top = e.clientY - disY

		if (top < 0 || top > windowH - 40) return false //当到达顶部或者底部时就不让继续拖动了
		ml.value = left + 'px'
		mt.value = top + 'px'
	}

	document.onmouseup = () => {
		if (dialogB.value) {
			dialogB.value = false
			// 恢复文本选择
			document.onselectstart = null
			document.body.style.userSelect = ''
		}
	}
}

const canResizeLeft = ref(false)
const mouseDownLeft = (e) => {
	e.preventDefault()
	e.stopPropagation()

	// 保存初始状态
	const initialX = e.clientX
	const initialWidth = width.value
	const initialLeft = ml.value

	canResizeLeft.value = true

	// 禁止文本选择
	document.onselectstart = () => false
	document.body.style.userSelect = 'none'

	document.onmousemove = (f) => {
		if (!canResizeLeft.value) return false

		// 计算鼠标移动距离
		const deltaX = f.clientX - initialX

		// 计算新宽度
		const newWidth = initialWidth - deltaX

		if (newWidth < w) {
			width.value = w
			return
		}

		// 更新宽度
		width.value = newWidth

		// 简单地使用固定位置，不使用calc累加
		if (initialLeft.includes('%')) {
			// 如果初始位置是百分比，保持百分比不变
			ml.value = initialLeft
		} else {
			// 如果初始位置是像素值，直接调整位置
			// 提取数字部分
			const match = initialLeft.match(/^(\d+)/)
			if (match) {
				const numValue = parseInt(match[1])
				ml.value = numValue + deltaX / 2 + 'px'
			}
		}
	}

	document.onmouseup = () => {
		if (canResizeLeft.value) {
			canResizeLeft.value = false
			// 恢复文本选择
			document.onselectstart = null
			document.body.style.userSelect = ''
		}
	}
}
const canResizeRight = ref(false)
const mouseDownRight = (e) => {
	e.preventDefault()
	e.stopPropagation()

	// 保存初始状态
	const initialX = e.clientX
	const initialWidth = width.value

	canResizeRight.value = true

	// 禁止文本选择
	document.onselectstart = () => false
	document.body.style.userSelect = 'none'

	document.onmousemove = (f) => {
		if (!canResizeRight.value) return false

		// 计算鼠标移动距离
		const deltaX = f.clientX - initialX

		// 计算新宽度
		const newWidth = initialWidth + deltaX

		if (newWidth < w) {
			width.value = w
			return false
		}

		// 获取窗口可见区域宽度
		const windowWidth = window.innerWidth

		// 获取当前弹窗左侧位置
		const rect = dialogEl.value.getBoundingClientRect()
		const leftPosition = rect.left

		// 计算右侧边缘位置
		const rightEdge = leftPosition + newWidth

		// 检查是否超出可见区域
		if (rightEdge > windowWidth) {
			// 限制在可见区域内
			width.value = windowWidth - leftPosition
			return false
		}

		// 检查是否接近右侧边缘（磁吸效果）
		if (windowWidth - rightEdge < 50 && windowWidth - rightEdge > 0) {
			// 磁吸到右侧边缘
			width.value = windowWidth - leftPosition
			return false
		}

		// 正常情况下更新宽度
		width.value = newWidth
	}

	document.onmouseup = () => {
		if (canResizeRight.value) {
			canResizeRight.value = false
			// 恢复文本选择
			document.onselectstart = null
			document.body.style.userSelect = ''
		}
	}
}
const canResizeTop = ref(false)
const mouseDownTop = (e) => {
	e.preventDefault()
	e.stopPropagation()

	// 保存初始状态
	const initialY = e.clientY
	const initialHeight = height.value
	const initialTop = mt.value

	canResizeTop.value = true

	// 禁止文本选择
	document.onselectstart = () => false
	document.body.style.userSelect = 'none'

	document.onmousemove = (f) => {
		if (!canResizeTop.value) return false

		// 计算鼠标移动距离
		const deltaY = f.clientY - initialY

		// 计算新高度
		const newHeight = initialHeight - deltaY

		if (newHeight < h) {
			height.value = h
			return false
		}

		// 更新高度
		height.value = newHeight

		// 简单地使用固定位置，不使用calc累加
		if (initialTop.includes('%')) {
			// 如果初始位置是百分比，保持百分比不变
			mt.value = initialTop
		} else {
			// 如果初始位置是像素值，直接调整位置
			// 提取数字部分
			const match = initialTop.match(/^(\d+)/)
			if (match) {
				const numValue = parseInt(match[1])
				mt.value = numValue + deltaY / 2 + 'px'
			}
		}
	}

	document.onmouseup = () => {
		if (canResizeTop.value) {
			canResizeTop.value = false
			// 恢复文本选择
			document.onselectstart = null
			document.body.style.userSelect = ''
		}
	}
}
const canResizeBottom = ref(false)
const mouseDownBottom = (e) => {
	e.preventDefault()
	e.stopPropagation()

	// 保存初始状态
	const initialY = e.clientY
	const initialHeight = height.value

	canResizeBottom.value = true

	// 禁止文本选择
	document.onselectstart = () => false
	document.body.style.userSelect = 'none'

	document.onmousemove = (f) => {
		if (!canResizeBottom.value) return false

		// 计算鼠标移动距离
		const deltaY = f.clientY - initialY

		// 计算新高度
		const newHeight = initialHeight + deltaY

		if (newHeight < h) {
			height.value = h
			return false
		}

		// 获取窗口可见区域高度
		const windowHeight = window.innerHeight

		// 获取当前弹窗顶部位置
		const rect = dialogEl.value.getBoundingClientRect()
		const topPosition = rect.top

		// 计算底部边缘位置
		const bottomEdge = topPosition + newHeight

		// 检查是否超出可见区域
		if (bottomEdge > windowHeight) {
			// 限制在可见区域内
			height.value = windowHeight - topPosition
			return false
		}

		// 检查是否接近底部边缘（磁吸效果）
		if (windowHeight - bottomEdge < 50 && windowHeight - bottomEdge > 0) {
			// 磁吸到底部边缘
			height.value = windowHeight - topPosition
			return false
		}

		// 正常情况下更新高度
		height.value = newHeight
	}

	document.onmouseup = () => {
		if (canResizeBottom.value) {
			canResizeBottom.value = false
			// 恢复文本选择
			document.onselectstart = null
			document.body.style.userSelect = ''
		}
	}
}
// 关闭
const close = () => {
	emit('close')
}
const getSize = (val: boolean) => {
	// console.log()

	if (val) {
		width.value = document.body.clientWidth
		height.value = document.body.clientHeight
		ml.value = '50%'
		mt.value = '50%'
	} else {
		width.value = w
		height.value = h
		ml.value = offset[0]
		mt.value = offset[1]
	}
	emit('changeFull', val)
}
onMounted(() => {
	getSize(isFull.value)

	window.onresize = () => {
		getSize(isFull.value)
	}
})
watch(
	() => isFull.value,
	(val) => {
		getSize(val)
	}
)
const dom = ref()
// 导出操作手册和答疑手册
const downLoad = () => {
	console.log(123)

	// const a = document.createElement('a')
	// a.href = `${APIConfig('base')}/api/files/public/p/handBook/${
	// 	title === '操作手册' ? '一表通操作手册' : '一表通答疑手册'
	// }.docx`
	// a.download = '一表通操作手册.docx'
	// document.body.appendChild(a)
	// a.click()
	// a.remove()
	emit('clickDownLoad')
}

const imgFile = ref<any[]>([])
const customUpload = async (options: any) => {
	console.log(111, '开始上传')
	const res = await uploadFile(options.file, 'handBook', false)
	if (res) {
		imgFile.value.push({
			objectId: options.file.uid.toString(),
			name: options.file.uid.toString() + '-' + options.file.name,
			path: `/api/files/public/p${res}`,
			extension: options.file.type,
			size: options.file.size,
		})
		console.log(imgFile.value)
	}
}
const onRemoveFile = (file: any) => {
	const findIndex = imgFile.value.findIndex((item: any) => item.objectId === file.uid.toString())
	if (findIndex !== -1) imgFile.value.splice(findIndex, 1)
	// debugger
}
</script>
<template>
	<div>
		<div
			ref="dialogEl"
			class="popup"
			:style="{left: ml, top: mt, width: width + 'px', height: height + 'px'}"
		>
			<div ref="leftRef" class="left" @mousedown="mouseDownLeft"></div>
			<div class="contain">
				<div class="top" @mousedown="mouseDownTop"></div>
				<div style="height: 100%">
					<div class="popup-tit" @mousedown="mouseDown">
						<div class="txt">{{ title }}</div>
						<div class="close">
							<slot name="handlebook"></slot>

							<!-- <el-icon
								mr-10px
								text="16px #fff"
								v-if="title === '答疑手册' || title === '操作手册'"
								title="上传"
								@click="uploadModelVisible = true"
								><Upload
							/></el-icon> -->
							<!-- v-if="title === '答疑手册' || title === '操作手册'" -->
							<!-- 20240511 隐藏下载 -->
							<!-- <el-icon
								mr-10px
								text="16px #fff"
								v-if="title === '答疑手册' || title === '操作手册'"
								title="下载"
								@click="downLoad"
								><Download
							/></el-icon> -->
							<el-icon
								style="color: #fff; margin-right: 10px"
								v-if="isFull === true"
								@click="isFull = false"
								><FullScreen
							/></el-icon>
							<el-icon
								style="color: #fff; margin-right: 10px"
								v-if="isFull === false"
								@click="isFull = true"
								><FullScreen
							/></el-icon>
							<el-icon text="#fff" style="color: #fff" title="关闭" @click="close"
								><CloseBold
							/></el-icon>
						</div>
					</div>
					<div class="popup-content">
						<slot></slot>
					</div>
				</div>
				<div class="bottom" @mousedown="mouseDownBottom"></div>
			</div>
			<div class="right" @mousedown="mouseDownRight"></div>
		</div>
	</div>
	<!-- 附件 -->
	<Dialog
		v-model="uploadModelVisible"
		title="上传附件"
		width="800"
		@close="uploadModelVisible = false"
		@click-cancel="uploadModelVisible = false"
		@click-confirm="uploadModelVisible = false"
	>
		<div class="uploadArea">
			<el-upload
				class="upload-demo"
				drag
				ref="upload"
				action=""
				:http-request="customUpload"
				accept=".doc,.docx"
				:limit="1"
				:on-remove="onRemoveFile"
				:file-list="imgFile"
			>
				<el-icon class="el-icon--upload"><upload-filled /></el-icon>
				<div class="el-upload__text">点击或将文件拖动到这里进行上传</div>
			</el-upload>
		</div>
		<!-- <div class="uploadList">123</div> -->
	</Dialog>
</template>
<style scoped lang="scss">
.popup {
	border-radius: 6px;
	position: absolute;
	z-index: 100;
	width: 280px;
	height: 300px;
	transform: translateX(-50%) translateY(-50%);

	display: flex;
	flex-direction: row;
}

.left,
.right {
	width: 6px; /* 增加宽度，使其更容易点击 */
	height: 100%;
	opacity: 0.5; /* 增加透明度，使其可见 */
	flex: 0 0 auto; /* 确保左右元素不伸缩 */
	cursor: col-resize;
	position: relative; /* 确保定位正确 */
	z-index: 10; /* 确保在上层，可以接收点击事件 */
}
.contain {
	height: calc(100% - 12px);
	width: 100%;
	display: flex;
	flex-direction: column;
	flex-grow: 1;
	justify-content: space-between;
}
.top,
.bottom {
	width: 100%;
	height: 6px; /* 增加高度，使其更容易点击 */
	// background-color: #e4e4e4;
	flex-shrink: 0; /* 防止上下元素收缩 */
	cursor: row-resize;
	position: relative; /* 确保定位正确 */
	z-index: 10; /* 确保在上层，可以接收点击事件 */
	opacity: 0.5; /* 增加透明度，使其可见 */
}
.popup-tit {
	border-radius: 6px 6px 0 0;
	position: relative;
	display: flex;
	box-sizing: border-box;
	width: 100%;
	height: 40px;
	padding-left: 10px;
	color: #fff;
	font-size: 14px;
	line-height: 40px;
	background-color: var(--z-main);
	cursor: all-scroll;
	box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.2);
	.close {
		position: absolute;
		right: 20px;
		cursor: pointer;
	}
}
.popup-content {
	border-radius: 0 0 6px 6px;
	flex: auto;
	background-color: #fff;
	color: #000;
	height: calc(100% - 40px);
	overflow: hidden;
	padding: 0 20px;
	box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
}
</style>
