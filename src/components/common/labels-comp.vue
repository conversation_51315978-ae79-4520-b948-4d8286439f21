<script setup lang="ts">
import {useViewStore} from '$/useViewStore'
import {computed, onMounted, ref, toRaw} from 'vue'
import {router} from '@/router'

const emtis = defineEmits(['click'])
const items = ref([]) as any
const activeName = ref('')
const store = useViewStore()

const toggleView = (item: any) => {
	activeName.value = item.displayName
	emtis('click', item)
}

const add = async (item: any) => {
	if (!item) return

	const hasItem = items.value.find((s: any) => s.displayName === item.displayName)
	if (hasItem) {
		activeName.value = hasItem.displayName
	} else {
		items.value.push(item)
		activeName.value = items.value[items.value.length - 1].displayName
		if (item.router) {
			router.push({path: item.path, query: item.query})
		}
		store.saveLabels(items.value)
	}
}
/**
 * 删除指定标签
 * @param e MouseEvent
 * @param index 索引
 */
const onDelete = async (item: any) => {
	const index = items.value.findIndex((s: any) => s.displayName === item.displayName)
	const prevItem = items.value[index - 1]
	const nextItem = items.value[index + 1]

	if (item.displayName === activeName.value) {
		let curItem = nextItem || prevItem || items.value[0]
		activeName.value = curItem?.displayName
		emtis('click', curItem)
	}

	items.value.splice(index, 1)
	store.saveLabels(items.value)
}

const clear = () => {
	items.value = items.value.slice(0, 1)
	activeName.value = ''
	store.saveLabels(items.value)
}

let dragstar = false
let dragCurName: string = ''
let dragTargetName: string = ''
let targetNode: HTMLElement | null = null
let enterNode: HTMLElement | null = null
let cloneNode: HTMLElement | null = null

const onDragstart = (event: any, item: any) => {
	dragCurName = item.displayName
	dragstar = true

	event.dataTransfer.setDragImage(new Image(), 10, 10)

	targetNode = event.currentTarget
	cloneNode = event.currentTarget.cloneNode(true)

	if (cloneNode) {
		cloneNode.classList.add('virtual')
		cloneNode.style.width = event.currentTarget.offsetWidth + 'px'
		document.body.appendChild(cloneNode)
	}

	targetNode?.classList.add('drag')
}

const onDrag = (event: any, item: any) => {
	if (dragstar && cloneNode) {
		const {clientY, clientX} = event
		if (clientY === 0 && clientX === 0) {
			onDragFinish()
			return
		}
		cloneNode.style.top = clientY + 20 + 'px'
		cloneNode.style.left = clientX + 20 + 'px'
	}
	event.preventDefault()
}

const onDragenter = (event: any, item: any) => {
	if (dragCurName === item.displayName || dragTargetName === item.displayName) {
		return
	}

	dragTargetName = item.displayName

	enterNode?.classList.remove('enter')
	enterNode = event.currentTarget
	enterNode?.classList.add('enter')

	event.preventDefault()
}

const onDragleave = (event: any, index: number) => {
	event.preventDefault()
}

const onDragover = (event: any) => {
	if (dragstar && cloneNode) {
		const {clientY, clientX} = event
		cloneNode.style.top = clientY + 20 + 'px'
		cloneNode.style.left = clientX + 20 + 'px'
	}
	event.preventDefault()
}

const onDrop = (item: any) => {
	if (dragstar && cloneNode) {
		const index = items.value.findIndex((s: any) => s.displayName === dragCurName)
		const targetIndex = items.value.findIndex((s: any) => s.displayName === item.displayName)
		const temp = items.value[index]
		items.value[index] = items.value[targetIndex]
		items.value[targetIndex] = temp

		onDragFinish()
	}
}

const onDragend = () => {
	dragstar = false
	dragCurName = ''
	dragTargetName = ''

	store.saveLabels(items.value)
}

const onDragFinish = () => {
	enterNode?.classList.remove('enter')
	targetNode?.classList.remove('drag')
	cloneNode?.remove()
	cloneNode = null
	enterNode = null
	targetNode = null
}

onMounted(() => {
	const labels = store.getLabels
	if (labels.length) {
		items.value = labels
	}
})

defineExpose({
	item: computed(() => items.value.find((s: any) => s.displayName === activeName.value)),
	length: computed(() => items.value.length),
	add,
	del: onDelete,
	clear,
})
</script>
<template>
	<div class="lables-comp">
		<template v-for="item, index of (items as any)">
			<div
				v-if="index === 0"
				class="item"
				:class="{active: activeName === '首页'}"
				@click.stop="toggleView(item)"
			>
				<span class="title">{{ item?.displayName }}</span>
			</div>
			<div
				:data-name="item.displayName"
				v-else
				draggable="true"
				@dragstart="onDragstart($event, item)"
				@drag="onDrag($event, item)"
				@dragend="onDragend"
				class="item"
				:class="{active: activeName === item.displayName}"
				@click.stop="toggleView(item)"
				@dragenter="onDragenter($event, item)"
				@dragleave="onDragleave($event, item)"
				@dragover="onDragover"
				@drop="onDrop(item)"
			>
				<span class="title">{{ item?.displayName }}</span>
				<span v-if="index > 1" class="close" @click.stop="onDelete(item)">&times;</span>
			</div>
		</template>
	</div>
</template>
