<script setup lang="ts">
import {ref} from 'vue'
import {FlowNodeTypes} from '@/define/Workflow'
import {GetWorkflowByCode} from '@/api/WorkflowApi'

enum DepartmentTypes {
	Same = 0, //填报人同部门,
	Upper = 1, //填报人上一级部门,
	UpperTwo = 2, //填报人上二级部门,
	UpperThree = 3, //填报人上三级部门,
	UpperFour = 4, //填报人上四级部门,
	UpperFive = 5, //填报人上五级部门,
}

const props = defineProps({
	code: String,
})

const departmentOptions = [
	{label: '填报人同部门', value: DepartmentTypes.Same},
	{label: '填报人上一级部门', value: DepartmentTypes.Upper},
	{label: '填报人上二级部门', value: DepartmentTypes.UpperTwo},
	{label: '填报人上三级部门', value: DepartmentTypes.UpperThree},
	{label: '填报人上四级部门', value: DepartmentTypes.UpperFour},
	{label: '填报人上五级部门', value: DepartmentTypes.UpperFive},
]

const flowHeight = ref(0)
const curEnterNode: any = ref(null)

const onFlowNodeMouseEnter = (_flowNodeInfo: any, currentNode: any) => {
	curEnterNode.value = currentNode
}

const onFlowResize = () => {
	const h = document.body.offsetHeight / 2 - 40
	flowHeight.value = h
}
</script>
<template>
	<Dialog
		class="dialog-flowview"
		v-bind="$attrs"
		title="查看流程"
		:enableConfirm="false"
		:enableClose="false"
		:enableButton="false"
		:fullScreen="false"
		width="1000"
		:destroyOnClose="true"
		@opened="onFlowResize"
	>
		<Flow
			ref="flowRef"
			v-resize="onFlowResize"
			:height="flowHeight"
			:template-code="code"
			:get-workflow-by-code="GetWorkflowByCode"
			:disabled="true"
			:show-node-info="true"
			:nodeInfoSize="['40%', 'auto']"
			width="100%"
			@node-mouse-enter="onFlowNodeMouseEnter"
			@node-mouse-leave="() => (curEnterNode = null)"
		>
			<template #node-info>
				<div
					class="node-info-item"
					v-if="curEnterNode?.type === FlowNodeTypes.Review && curEnterNode"
				>
					<span class="title">审核人</span>

					<el-tag
						type="primary"
						v-for="item of curEnterNode.auditUsers"
						class="mg-right-10 mg-top-10"
					>
						{{
							departmentOptions.find(
								(f: any) => f.value === item.additionalConditionValue
							)?.label || item.additionalConditionLabel
						}}
						- {{ item.type == 3 ? item.valueLabel : item.value }}
					</el-tag>

					<span class="title mg-top-20">审核方式</span>

					<el-tag type="primary" class="mg-right-10 mg-top-10">
						{{
							curEnterNode.isCountersign ? '全部审核人都需审核' : '任一审核人审核即可'
						}}
					</el-tag>
				</div>

				<div
					class="node-info-item"
					v-else-if="curEnterNode?.type === FlowNodeTypes.Condition && curEnterNode"
				>
					<span class="title">是否默认分支</span>
					<el-tag type="primary" class="mg-right-10 mg-top-10">{{
						curEnterNode.config?.isDefault ? '是' : '否'
					}}</el-tag>
					<span class="title mg-top-20">对应值</span>
					<el-tag v-if="curEnterNode.config" type="primary" class="mg-right-10 mg-top-10">
						{{ Object.values(curEnterNode.config?.value).join() }}
					</el-tag>
				</div>
			</template>
		</Flow>
	</Dialog>
</template>
<style lang="scss" scoped>
.node-info-item {
	.title {
		border-radius: 5px;
		background-color: var(--z-bg-secondary);
		font-weight: 500;
		font-size: 14px;
		padding: 10px;
		width: 100%;
	}
}
</style>
