<script setup lang="ts">
import {ref, watch} from 'vue'
import {useRouter} from 'vue-router'

interface Props {
	title?: string
	showBack?: boolean
	enableBack?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	title: '标题',
	showBack: true,
	enableBack: true,
})
const emits = defineEmits(['onBack'])

const router = useRouter()
const title = ref(props.title)
const onBack = () => {
	if (props.enableBack) {
		router.go(-1)
	}
	emits('onBack')
}

watch(
	() => props.title,
	(newVal) => {
		title.value = newVal
	}
)
</script>
<template>
	<div class="top-comp">
		<div class="left">
			<el-link class="back" :underline="false" @click="onBack" v-if="props.showBack">
				<el-icon>
					<ArrowLeftBold /> </el-icon
				>返回
			</el-link>

			<slot name="left"> </slot>
		</div>
		<div class="center">
			<slot name="center"></slot>
		</div>
		<div class="right">
			<slot name="right"></slot>
		</div>
	</div>
</template>
