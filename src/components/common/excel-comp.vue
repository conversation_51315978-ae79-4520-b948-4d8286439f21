<script setup lang="ts">
import {
	inject,
	nextTick,
	onActivated,
	onDeactivated,
	onMounted,
	onUnmounted,
	ref,
	toRaw,
	watch,
} from 'vue'
import {useViewStore} from '$/useViewStore'
import {ElNotification} from 'element-plus'
import {__xls_config} from './__excel_config'
import {Request} from '#/interface'
import util from '@/plugin/util'
import xlsx from '@/plugin/exceljs'

interface SheetData {
	title?: string
	sheetData?: [] | null
	headStartRow?: number
	headEndRow?: number
	config?: {} | null
	source?: any[] | null
	head?: [] | null
	reportList?: [] | null
}

// excel-comp 组件相关参数
interface Props {
	reload?: boolean
	data?: SheetData | any
	fillData?: any
	fillFields?: any
	fillLinked?: any
	permissions?: string
	useConfig?: string
	sheetId?: string
	visible?: boolean
	height?: string
	width?: string
	showTitle?: boolean

	compulsory?: boolean
	enableTools?: boolean
	enableImport?: boolean
	isProtection?: boolean
	isBlock?: boolean
	isAutoSave?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	data: {
		title: 'Default Title',
		sheetData: null,
		config: null,
		source: null,
		head: null,
		reportList: null,
		headStartRow: 0,
		headEndRow: 0,
	},
	fillData: [],
	fillFields: [],
	fillLinked: [],
	reload: false,
	useConfig: 'default',
	sheetId: '',
	height: 'calc(100% - 0px)',
	width: '70%',
	visible: false,
	showTitle: true,
	compulsory: false,
	enableTools: true,
	enableImport: false,
	isProtection: true,
	isBlock: false,
	isAutoSave: false,

	permissions: 'all',
})

const emits = defineEmits([
	'save',
	'closed',
	'onCellDragStop',
	'onImportbegin',
	'getFillData',
	'onToggleFillChecked',
	'onFillDelete',
	'getFillDataByFilter',
])

// 状态
const status = ref({
	import: false,
	importProgress: false,
	complate: false,
	autoSaveComplete: true,
	toggleProtect: props.isProtection,
})

// 参数
const useView = useViewStore()
const axios = inject('#axios') as Request

/** props data  */
const _sheetId = ref(props.sheetId)
const _data = ref<SheetData>(props.data)
const _visible = ref<boolean>(props.visible)

// 其他参数
/** 锁定区域解锁密码 */
const rangePassword = ref('')
/** 显示/隐藏设置密码框 */
const showRangePassword = ref(false)

// 接口参数
const theadCount = ref({
	theadStartRowNum: 0,
	theadEndRowNum: 0,
	dataBeginRowNum: 0,
	dataBeginColumnNum: 0,
})

const _sheet = ref()
const _hook: any = {} // sheet 钩子

let _: any = null
let _id = ref('')
let headRange = [{row: [0, 0], column: [0, 0]}]
let oldRange: any = []
let autoSaveList: any[] = []
let animationId: any = null
let startTime: any = null
let autoSaveTime = 5000
const showAutoSaveBtn = ref(false)
const progress = ref(0)
let pasteBeforeSheetData: any[] = []
let recordModification: any[] = []

_hook.cellUpdateBefore = function () {
	pasteBeforeSheetData = _sheet.value.getSheetData()
}

_hook.cellUpdated = function (r: any, c: any, oldValue: any, newValue: any, isRefresh: any) {}

_hook.updated = function () {
	if (props.isAutoSave && status.value.autoSaveComplete) {
		console.log('auto save ing...')

		startTime = null

		let b = pasteBeforeSheetData
		let n = _sheet.value.getSheetData()
		let range = _sheet.value.getRange()

		const c = n.map((_n: any, i: number) => {
			if (i + 1 > theadCount.value.theadEndRowNum) {
				if (!status.value.import) {
					const nl = _n.filter((f: any) => f).length
					const bl = b[i]?.filter((f: any) => f).length
					if (nl === bl) {
						if (
							!_n.every(
								(_ne: any, _nei: number) =>
									JSON.stringify(_ne).length === JSON.stringify(b[i][_nei]).length
							)
						) {
							return _n.map((v: any, c: number) => (v ? {r: i, c, v} : null)).filter((f: any) => f)
						}
					} else {
						return _n.map((v: any, c: number) => (v ? {r: i, c, v} : null)).filter((f: any) => f)
					}
				} else {
					return _n.map((v: any, c: number) => (v ? {r: i, c, v} : null)).filter((f: any) => f)
				}
			}
		})

		let _row = null
		c.forEach((_c: any, i: number) => {
			let _guid = util._guid()
			if (_c !== undefined) {
				const isRaw = _data.value.source?.some((s: any) => {
					if (+s.rowNum === i + 1) {
						_guid = s.id
						return true
					}
				})

				if (!recordModification.some((s) => +s.rowNum === i + 1)) {
					_row = {
						rowId: _guid,
						actionType: isRaw ? 2 : 1,
						rowNum: Number(`${i + 1}`),
						rawData: JSON.stringify(_c),
					}
					recordModification.push(_row)
					autoSaveList.push(_row)
				} else {
					const _old = recordModification.find((f) => +f.rowNum == i + 1)
					_old.actionType = 2
					_old.rawData = JSON.stringify(_c)
					autoSaveList.push(_old)
				}
			}
		})

		if (b.length === 0) {
			const [start, end] = range[0].row
			autoSaveList = autoSaveList.filter(
				(f: any, i: number) => +f.rowNum - 1 >= start && +f.rowNum - 1 <= end
			)
		}

		let filter: any = {}
		autoSaveList.forEach((val: any) => (filter[val.rowId] = val))

		if (status.value.import) {
			autoSaveList.forEach((f: any) => {
				f.rowId = util._guid()
				f.actionType = 1
			})
		}

		autoSaveList = Object.values(filter)
		// console.log('自动保存集合: ', autoSaveList)
	}
}

_hook.rangePasteBefore = function (range: any, data: any) {
	pasteBeforeSheetData = _sheet.value.getSheetData()
}

_hook.rowInsertBefore = function (rr: number, rc: number, rt: string) {
	console.log('insert before')
	const ud = (rt === 'lefttop' ? rr : rr + 1) - theadCount.value.theadEndRowNum
	for (let i = 0; i < rc; i++) {
		let guid = util._guid()
		let nr = {
			rowNum: `-`,
			actionType: 1,
			rawData: '[]',
			rowId: guid,
		}
		_data.value.source?.splice(ud, 0, nr)

		// 修改原始数据行号
		_data.value.source?.forEach((f: any, ii: number) => {
			if (ii >= ud) {
				f.rowNum = f.rowNum === '-' ? ii + 1 + theadCount.value.theadEndRowNum : `${+f.rowNum + 1}`

				if (guid === f.rowId) {
					recordModification.push(nr)
					autoSaveList.push(nr)
				}
			}
		})
	}
	onAutoSaveSheet()
}

_hook.cellDragStop = function (cell: any, postion: any, sheetFile: any, ctx: any, event: any) {
	console.log(postion)

	onDragFillDataToCell(cell, postion)
}

const init = () => {
	console.log('excel-comp init...')

	_ = JSON.parse(JSON.stringify(__xls_config[props.useConfig]), (key, value) => {
		return key === 'hook' ? _hook : value
	})

	_.gridKey = _id.value
	_.container = _id.value

	const sheetData: any = !!_data.value.sheetData
	if (sheetData) {
		const raw = toRaw(_data.value.sheetData) as Array<any>
		_.data[0] = JSON.parse(JSON.stringify(raw[0]), (key, value) => {
			return key === 'hook' ? _hook : value
		})
	} else {
		const {title, head, reportList, config} = _data.value
		let h = [] // 表头
		let l = [] // 列表

		_.data[0].name = title || '-'

		if (head) {
			h = toRaw(head).map((v: any) => (typeof v === 'string' ? JSON.parse(v) : v))
		}

		if (reportList) {
			l = toRaw(reportList).map((v: any) => (typeof v === 'string' ? JSON.parse(v) : v))
		}
		const len: number = _data.value.source?.length || 30
		_.data[0].row = (len < 30 ? 30 : len) + theadCount.value.theadEndRowNum
		_.data[0].celldata = [...h, ...l]
		_.data[0].config = Object.assign(_.data[0].config, toRaw(config))
	}

	if (props.compulsory || !props.enableTools) {
		Object.keys(_.showtoolbarConfig).forEach((key) => (_.showtoolbarConfig[key] = false))
		if (props.compulsory) {
			Object.keys(_.cellRightClickConfig).forEach((key) => (_.cellRightClickConfig[key] = false))
			_.data[0].config.authority.hintText = '报表已被锁定不允许修改'
			_.data[0].config.authority.allowRangeList = []
		}
	}

	if ('luckysheet' in window && !_sheet.value) {
		_sheet.value = util._deepCopy(window?.luckysheet)
		_sheet.value.create(_)
	} else {
		_sheet.value.create(_)
	}
	status.value.complate = true
}

let fiilToggle = false
watch(
	() => props,
	(newVal) => {
		if (fiilToggle) return

		const {data, visible, isAutoSave, sheetId} = newVal

		_visible.value = visible // 是否显示

		onAnimationEnd()

		if (isAutoSave) {
			onAnimationBegin()
		}

		if (props.reload) {
			status.value.complate = false
		}

		if (!status.value.complate || _sheetId.value !== sheetId) {
			_data.value = data
			_sheetId.value = sheetId
			theadCount.value.theadStartRowNum = _data.value.headStartRow || 0
			theadCount.value.theadEndRowNum = _data.value.headEndRow || 0

			if (!_id.value) {
				_id.value = 'Sheet' + Math.random().toString(36).slice(2)
			}

			if (_visible.value) {
				nextTick(() => init())
			}
		}
	},
	{deep: true}
)

watch(
	() => [props.fillData, props.fillFields, props.fillLinked],
	(newVal) => {
		fiilToggle = true

		newVal[2].forEach((link: any, i: number) => {
			const range = JSON.parse(link.columnName)
			_sheet.value.setRangeShow(range[0])
			cellValue(range, {bg: '#ff0'})
			newVal[2][i]['_column'] = _sheet.value.getRangeAxis(range[0])
		})

		nextTick(() => (fiilToggle = false))
	}
)

watch(
	() => useView.times,
	() => {
		if (_sheet.value) {
			setTimeout(() => nextTick(() => _sheet.value.resize()), 700)
		}
	}
)

const setLockRange = () => {
	const range = _sheet.value.getRange()
	const rangeAxis = _sheet.value.getRangeAxis()

	// 清除之前区域背景再设置新区域背景
	if (oldRange.length > 0) {
		cellValue(oldRange, {bg: null})
	}

	cellValue(range, {bg: '#eee'})

	const lock = rangeAxis.join()
	const [start, end] = lock.split(':')
	const free = [start, (end || start).replace(/\d+/g, '99999')].join(':')

	_.data[0].config.authority.allowRangeList[0].sqref = lock
	_.data[0].config.authority.allowRangeList[1].sqref = free

	oldRange = range

	showRangePassword.value = true

	headRange = range
	const [rb, re] = headRange[0].row

	theadCount.value.theadStartRowNum = rb + 1
	theadCount.value.theadEndRowNum = re + 1
	theadCount.value.dataBeginRowNum = re + 2
}

const setRangesPassword = () => {
	_.data[0].config.authority.allowRangeList[0].password = rangePassword.value || '123456'
	showRangePassword.value = false
}

const cellValue = (ranges: any, setting: any, getVal: boolean = false): Array<any> => {
	const res: Array<any> = []
	for (const {row, column} of ranges) {
		const [sr, er] = row
		const [sc, ec] = column
		for (let c = sc; c <= ec; c++) {
			for (let r = sr; r <= er; r++) {
				if (getVal) {
					res.push(_sheet.value.getCellValue(r, c, setting))
				} else {
					_sheet.value.setCellValue(r, c, setting)
				}
			}
		}
	}
	return res
}

const onProtectSheet = () => {
	_.data[0].config.authority.sheet = !status.value.toggleProtect
	status.value.toggleProtect = _.data[0].config.authority.sheet

	if (!status.value.toggleProtect) {
		_.data[0].config.authority.allowRangeList[0].password = ''
		_.data[0].config.authority.allowRangeList[0].sqref = '$A$0:$B$0'
		cellValue(oldRange, {bg: null})
	}
}

const onClosed = () => {
	_visible.value = false
	const inputBox = document.querySelector('#luckysheet-input-box')
	if (inputBox) {
		inputBox.setAttribute('style', '')
	}
	emits('closed', _sheet.value.getAllSheets())
	status.value.complate = false
}

const onSaveTemplate = () => {
	if (props.isProtection) {
		if (!status.value.toggleProtect || theadCount.value.theadEndRowNum === 0) {
			ElNotification({
				title: '警告',
				message: '请启用保护选择表头区域锁定',
				type: 'warning',
			})
			return
		}
	}

	const head: Array<string> = []
	_sheet.value.getAllSheets()[0].celldata.forEach((d: any) => {
		head.push(JSON.stringify(d))
	})

	const _table: Array<[]> = []
	_sheet.value.getSheetData().forEach((d: any) => {
		const _f = d.filter((f: any) => f !== null)
		if (_f.length > 0) {
			_table.push(_f.map((m: any) => m.v))
		}
	})

	const headRows = _table.splice(headRange[0].row[0], headRange[0].row[1] + 1)
	const maxCol = headRows.reduce((acc, cur) => Math.max(acc, cur.length), 0)

	const result: any = []
	for (let index = 0; index < headRows.length; index++) {
		const arr = Array.from({length: maxCol}, (_, c) => ({
			row: index,
			column: c,
			v: headRows[index][c],
		}))
		result.push(arr)
	}

	const tableTemplateColumn = result[result.length - 1].map((v: any) => ({
		name: v.v || `r${v.row}:c${v.column}`,
		displayName: v.v || `r${v.row}:c${v.column}`,
		rowIndex: v.row,
		columnIndex: v.column,
	}))

	axios
		?.post(
			'/api/filling/report-table-template',
			Object.assign(
				{
					name: _data.value.title,
					displayName: _data.value.title,
					globalStyle: JSON.stringify({
						useConfig: props.useConfig,
						head,
						config: _.data[0].config,
					}),
					tableTemplateColumn,
				},
				theadCount.value
			)
		)
		.then((res) => {
			const {id} = res.data
			if (id) {
				ElNotification({
					type: 'success',
					title: '提示',
					message: '保存成功',
				})
				emits('save', id)
				onClosed()
			}
		})
}

const onAutoSaveSheet = async () => {
	if (!props.isAutoSave) return

	if (!status.value.autoSaveComplete) return

	const list = JSON.parse(JSON.stringify(autoSaveList))
	autoSaveList = []

	status.value.autoSaveComplete = false
	useView.enableLoading = false

	await axios.put(`/api/filling/report-table/${_sheetId.value}/data`, list).then((res) => {
		status.value.autoSaveComplete = true
		useView.enableLoading = true
		status.value.import = false
		setTimeout(() => (showAutoSaveBtn.value = false), 1000)
	})
}

const onAnimation = (timestamp: any) => {
	if (!startTime) {
		startTime = timestamp
	}
	const elapsedTime = timestamp - startTime
	if (elapsedTime > autoSaveTime) {
		if (_sheetId.value && autoSaveList.length > 0) {
			onAutoSaveSheet()
			showAutoSaveBtn.value = true
		}
		startTime = null
	}
	animationId = requestAnimationFrame(onAnimation)
}

const onAnimationBegin = () => {
	startTime = null
	animationId = requestAnimationFrame(onAnimation)
}

const onAnimationEnd = () => {
	cancelAnimationFrame(animationId)
	startTime = null
	animationId = null

	autoSaveList = []
	recordModification = []
}

const getColName = (colIndex: number) => {
	let colName = ''
	while (colIndex >= 0) {
		let remainder = colIndex % 26
		colName = String.fromCharCode(65 + remainder) + colName
		colIndex = Math.floor(colIndex / 26) - 1
	}
	return colName
}

const onImport = async (file: File) => {
	let sheets: any = null
	let cr = 0

	const currentSheetMaxRow = _sheet.value.getSheetData().length - 1

	const range = `A${theadCount.value.theadEndRowNum + 1}:${getColName(
		_sheet.value.getSheetData()[0].length - 1
	)}${currentSheetMaxRow}`
	const maxCol = _sheet.value.getSheetData()[0].filter((f: any) => f).length

	_sheet.value.cancelRangeMerge({range})
	_sheet.value.clearRange({range})

	const xl = new xlsx(file, {
		onImportBegin: () => {
			progress.value = 0
			cr = 0
			status.value.import = true
			emits('onImportbegin')
		},

		onImportProgress: ({d, m, p, mr}: any) => {
			progress.value = p

			if (d.r >= theadCount.value.theadEndRowNum && d.c < maxCol) {
				let r = mr - currentSheetMaxRow
				let st = _.data[0].defaultRowHeight * (d.r - 5)

				if (cr < r) {
					// 新增行
					const last_row = _sheet.value.getSheetData().length - 1
					_sheet.value.insertRow(last_row)
					cr++
				}

				_sheet.value.setCellValue(d.r, d.c, {v: d.v})
				_sheet.value.scroll({scrollTop: st > 0 ? st : 0})
				_sheet.value.setRangeShow({row: [d.r + 1, 0], column: [999, 999]})

				if (m) {
					_sheet.value.setRangeMerge('all', {range: m.range})
				}
			}
		},

		onImportend: (sheets: any) => {
			const input = document.querySelector(`#${_id.value}File`) as HTMLInputElement
			input.value = ''

			ElNotification({
				title: '提示',
				message: '导入完成',
				type: 'success',
			})
		},
	})

	await xl.import()
}

const fillDataRef = ref()
const currentFillItem = ref()
const currentImport = ref('xlsx')
const toggleImport = ref([
	{label: 'Excel导入', value: 'xlsx'},
	{label: '业务表数据填充', value: 'fill'},
])
const onToggleImportType = () => {
	if (currentImport.value === 'fill') {
		emits('getFillData')
	} else {
		props.fillLinked.forEach((link: any) => {
			const range = JSON.parse(link.columnName)
			cellValue(range, {bg: '#eee'})
		})
	}
	nextTick(() => _sheet.value.resize())
}
const onToggleFillChecked = (data: any) => {
	emits('onToggleFillChecked', data)
}
const onFillDragStart = (item: any) => {
	currentFillItem.value = item
}
const onDragFillDataToCell = (cell: any, position: any) => {
	const {r, c} = position
	// 设置选中区域
	_sheet.value.setRangeShow({row: [r, r], column: [c, c]})

	const maxCol = _sheet.value.getSheetData()[0].filter((f: any) => f).length
	const range = _sheet.value.getRangeAxis().join()

	if (r > theadCount.value.theadEndRowNum - 1 || c > maxCol - 1) {
		ElNotification({
			title: '警告',
			message: '请拖动到的表头列关联数据',
			type: 'warning',
		})
		return
	}

	if (
		props.fillLinked.some((s: any) => s.ledgerField === currentFillItem.value.field) ||
		props.fillLinked.some((s: any) => s._column[0] === range)
	) {
		ElNotification({
			title: '警告',
			message: '该字段已关联过, 若要重新关联请先删除之前的关联',
			type: 'warning',
		})
		return
	}

	cellValue([{row: [r, r], column: [c, c]}], {bg: '#ff0'})
	fillDataRef.value.setField(currentFillItem, _sheet.value.getRangeAxis().join())

	emits('onCellDragStop', {item: toRaw(currentFillItem.value), position})
}

const onFillDelete = (item: any) => {
	_sheet.value.setRangeShow(item?._column[0])
	cellValue(_sheet.value.getRange(), {bg: '#eee'})
	emits('onFillDelete', item)
}

const setFillData = () => {
	if (props.fillLinked.length === 0) {
		ElNotification({
			title: '警告',
			message: '请先关联数据',
			type: 'warning',
		})
		return
	}

	emits('getFillDataByFilter', props.fillLinked)
}

const fixRowsInRawData = (rawData: any, r: number): Array<any> => {
	const data = JSON.parse(rawData)
	data.forEach((f: any) => (f.r = r - 1))
	return data
}

const fillLoops = (ms: any) => {
	return new Promise((resolve, reject) => {
		const timer = setTimeout(() => {
			clearInterval(timer)
			resolve('')
		}, ms)
	})
}

const setCellValue = async (rawData: any) => {
	status.value.import = true

	progress.value = 0

	const currentSheetMaxRow = _sheet.value.getSheetData().length - 1

	let cr = 0
	const max = rawData.reduce((acc: any, cur: any) => acc + cur.data.length, 0)

	rawData.forEach(async (f: any) => {
		const [range, data] = Object.values(f) as any

		_sheet.value.setRangeShow(range)
		const rg = _sheet.value.getRange()[0]
		const [r1, r2] = rg.row
		const [c1, c2] = rg.column
		console.log(r2, c2)

		for (let i = 0; i < data.length; i++) {
			progress.value = (((i + 1) / max) * 100 * rawData.length) | 0 || 1

			if (r1 + i >= theadCount.value.theadEndRowNum) {
				let r = data.length - currentSheetMaxRow
				let st = _.data[0].defaultRowHeight * (r1 + i - 5)

				if (cr < r) {
					// 新增行
					const last_row = _sheet.value.getSheetData().length - 1
					_sheet.value.insertRow(last_row)
					cr++
				}

				_sheet.value.setCellValue(r1 + i, c1, {v: data[i]})
				_sheet.value.scroll({scrollTop: st > 0 ? st : 0})
				_sheet.value.setRangeShow({row: [r1 + 1, 0], column: [999, 999]})

				await fillLoops(1)
				if (progress.value > 100) {
					progress.value = 0
				}
			}
		}
	})
}

onMounted(() => {
	setTimeout(() => onAnimationBegin(), 16.7)
	console.log('excel mounted')
})

onActivated(() => {
	setTimeout(() => (!animationId ? onAnimationBegin() : ''), 16.7)
	console.log('excel activated')
})

onUnmounted(() => {
	onAnimationEnd()
	// 离开页面之前在保存一次
	autoSaveList = recordModification
	autoSaveList.forEach((f: any) => (f.actionType = 2))
	onAutoSaveSheet()
	onAnimationEnd()
	status.value.complate = false
	_sheet.value?.destroy()
	_sheet.value = null
	console.log('excel unmounted')
})

onDeactivated(() => {
	console.log('excel unmounted')
})

defineExpose({
	fixRowsInRawData,
	setCellValue,
})
</script>
<template>
	<div
		class="excel-comp"
		:class="{open: _visible && !isBlock, 'excel-block': isBlock}"
		@click="onClosed"
		:style="{height: height}"
	>
		<div class="excel-layout" :class="{open: _visible && !isBlock}" :style="{width: width}">
			<div class="header" @click.stop>
				<span class="btns" :class="{'pl-0!': isBlock}">
					<el-button v-if="!isBlock" size="small" @click="onClosed">关闭</el-button>

					<el-select
						ml-0
						v-if="enableImport && !compulsory"
						v-model="currentImport"
						class="m-2"
						placeholder="选择关联数据方式"
						@change="onToggleImportType"
					>
						<el-option
							v-for="item in toggleImport"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>

					<el-button
						v-if="enableImport && currentImport === 'xlsx' && !compulsory"
						type="primary"
						size="small"
						class="input-file"
					>
						选择文件
						<input
							:id="`${_id}File`"
							class="file"
							type="file"
							accept=".xlsx"
							@change="onImport($event.target.files[0])"
						/>
					</el-button>

					<el-button
						type="primary"
						size="small"
						v-if="enableImport && currentImport === 'fill' && !compulsory"
						@click="setFillData"
					>
						填充数据
					</el-button>

					<!-- 仅启用保护才能创建保存模版 -->
					<template v-if="isProtection">
						<el-button size="small" type="primary" @click="onSaveTemplate">保存模版</el-button>
						<el-button size="small" type="primary" @click="onProtectSheet">{{
							status.toggleProtect ? '关闭保护' : '启用保护'
						}}</el-button>
						<el-button v-if="status.toggleProtect" size="small" type="primary" @click="setLockRange"
							>锁定区域</el-button
						>
					</template>
					<!-- 顶部按钮插槽 -->
					<div class="slot" :class="{'pl-0!': compulsory}">
						<slot name="header" :sheet="_sheet"></slot>
					</div>
					<el-button v-if="showAutoSaveBtn" size="small" loading>自动保存中...</el-button>
				</span>
				<h1 class="title">
					{{ showTitle ? data?.title : '&nbsp;' }}
				</h1>
			</div>
			<div class="fill-data" v-if="enableImport && currentImport === 'fill' && !props.compulsory">
				<fill-data-comp
					ref="fillDataRef"
					:linked="fillLinked"
					:data="fillData"
					:fields="fillFields"
					@onToggleChecked="onToggleFillChecked"
					@onDragStart="onFillDragStart"
					@onDelete="onFillDelete"
				></fill-data-comp>
			</div>
			<div :id="_id" class="sheet-main" @click.stop></div>
		</div>
	</div>

	<!-- 设置锁区密码 -->
	<DialogComp
		:closeOnClickModal="false"
		:closeOnPressEscape="false"
		:showClose="false"
		:visibleFooterButton="false"
		:visible="showRangePassword"
		title="设置锁定范围密码"
		width="300"
	>
		<template #body>
			<el-input type="password" v-model="rangePassword" placeholder="默认密码: 123456"></el-input>
		</template>
		<template #footer>
			<el-button type="primary" @click="setRangesPassword">确认</el-button>
		</template>
	</DialogComp>

	<ProgressBar :percentage="progress"></ProgressBar>
</template>
<style scoped lang="scss">
.excel-comp {
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	flex-direction: column;
	height: 100%;
	left: 0;
	opacity: 0;
	overflow: hidden;
	position: fixed;
	top: 0;
	transition: all 0.3s linear;
	width: 100%;
	visibility: hidden;
	z-index: 14;

	&.open {
		visibility: visible;
		opacity: 1;
	}

	:deep(.luckysheet) {
		max-width: 100%;
	}
}

.excel-layout {
	display: flex;
	flex-wrap: wrap;
	box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
	background-color: #fff;
	bottom: 0;
	position: absolute;
	top: 0;
	transition: all 0.2s linear;
	// transform: translateX(100%);
	right: -100%;
	width: 70%;

	&.open {
		right: 0;
	}

	.header {
		display: flex;
		flex-grow: 1;
		width: 100%;

		.title {
			flex: 1;
			font-size: 18px;
			line-height: 30px;
			padding: 10px;
			text-align: right;
		}

		.btns {
			align-items: center;
			display: flex;
			padding-left: 10px;
		}
	}
}

.fill-data {
	flex-grow: 1;
	width: 20%;
	height: calc(100% - 50px);
}

.sheet-main {
	flex-grow: 1;
	margin: 0px;
	padding: 0px;
	position: relative;
	width: 80%;
	height: calc(100% - 50px);
	left: 0px;
	top: 0px;
	z-index: 999;
}

.excel-block {
	height: 100%;
	position: relative;
	opacity: 1;
	visibility: visible;
	width: 100%;

	.excel-layout {
		height: 100%;
		position: relative;
		right: 0;
	}
}

.file {
	cursor: pointer;
	height: 400px;
	top: 0;
	position: absolute;
	left: 0;
	width: 400px;
	opacity: 0.01;
}

.input-file {
	input {
		cursor: pointer;
	}

	height: 24px;
	position: relative;
	overflow: hidden;
	width: 72px;
}

.slot {
	padding: 0 10px;
}
</style>
