<script setup lang="ts">
import { FormInstance } from "element-plus";
import { computed, nextTick, onMounted, ref, watch } from "vue";
import { useRoute } from "vue-router";
import { useUserStore } from "@/stores/useUserStore";

// 定义Element Plus表格span-method的类型
interface SpanMethodProps {
  row: any;
  column: any;
  rowIndex: number;
  columnIndex: number;
}
const route = useRoute();
const UserStore = useUserStore();
interface Props {
  // 数据 ========
  data?: Array<any>;
  colData?: Array<any>;
  selectedCount?: number;
  rowKey?: string;
  treeProps?: {};
  currentIndex?: Number;
  cureerntText?: String;

  // 搜索数据
  filterColData?: Array<any>;
  filterRules?: {};

  // 操作列按钮
  buttons?: Array<{
    type: "default" | "primary" | "success" | "warning" | "info" | "danger" | "text";
    code: string;
    title: string;
    icon: string;
    verify: string;
    more: boolean;
    showBtn: string;
  }>;

  // 样式 ========
  checkbox?: boolean;
  autoHeight?: boolean;
  height?: number;
  offsetHeight?: number; // 高度偏差
  visibleHeader?: boolean;
  visiblePage?: boolean;
  visibleSearch?: boolean;
  visibleExport?: boolean;
  visibleSetting?: boolean;
  visibleIndex?: boolean;
  visibleExpand?: boolean;
  hideHeader?: boolean;

  // 分页 ========
  currentPage?: number;
  pageSize?: number;
  pageSizeArray?: Array<number>;
  disabled?: boolean;
  total?: number;
  loading?: boolean;
  loadingText?: string;

  defaultExpandAll?: boolean;
  showSummary?: boolean;
  showBorder?: boolean;
  reserveSelection?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  selectedCount: 0,
  rowKey: "",

  autoHeight: false,
  checkbox: true,
  showBorder: true,
  offsetHeight: 0,
  visibleHeader: true,
  visiblePage: true,
  visibleSearch: false,
  visibleExport: false,
  visibleSetting: true,
  visibleIndex: true,

  visibleExpand: false,
  hideHeader: false,

  currentPage: 1,
  pageSize: 10,
  pageSizeArray: [10, 30, 50, 100] as any,
  disabled: false,
  total: 0,
  loading: false,
  defaultExpandAll: false,
  showSummary: false,
  reserveSelection: false,
});

const emits = defineEmits([
  "sizeChange",
  "currentChange",
  "clickButton",
  "rowClick",
  "selectionChange",
  "selectionAll",
  "filterChange",
  "filterSubmit",
  "handleCurrentChange",
  "sortableChange",
  "handleFilter",
  "spanMethod",
  "expandChange",
  "rowHover",
  "handleSelect",
]);

const baseTable = ref();
const only = "BaseTable" + Math.random().toString(36).slice(2);

let tableData = ref(props.data ?? []);
console.log(tableData.value);
let loading = computed(() => props.loading || false);
let loadingText = props.loadingText ?? "";
const height = ref(props.height);
const showBorder = props.showBorder === undefined ? true : props.showBorder;
const offsetHeight = computed(() => props.offsetHeight || 0);
const visibleHeader = props.visibleHeader;
const visiblePage = props.visiblePage;
const currentPage = ref(props.currentPage || 1);
const _pageSizeArray = props.pageSizeArray;
const pageSize = ref(props.pageSize || _pageSizeArray![0]);
const disabled = ref(props.disabled);
const total = ref(props.total || 0);
const buttons = ref(props.buttons ?? []);
const colData = ref(props.colData ?? []);
const buttonWidth = ref(200);
const checkbox = ref(props.checkbox);
const openFilter = ref(false);
const hasTopComp = ref(false);
const dropdownRef = ref();
let selectedCount = ref(props.selectedCount || 0);

let customSlots = Object.keys(tableData.value[0] || []);

const __selectedStatus = ref({
  all: { checked: false },
  local: { checked: false },
  emitChange: true,
});

watch(
  () => [
    // 数据
    props.data,
    props.selectedCount,
    props.buttons,
    props.colData,
    // 分页
    props.currentPage,
    props.pageSize,
    props.total,

    props.checkbox,
  ],
  (newVal: Array<any>) => {
    tableData.value = newVal[0] ?? [];
    customSlots = Object.keys(tableData.value[0] || []);

    selectedCount.value = newVal[1];
    buttons.value = newVal[2];
    colData.value = newVal[3];
    currentPage.value = newVal[4];
    pageSize.value = newVal[5];
    total.value = newVal[6];
    checkbox.value = newVal[7];

    console.log('watch triggered, buttons.value:', buttons.value);
    setButtonWidth();
  }
);

const __autoHeight = () => {
  nextTick(() => {
    const table = document.querySelector(`#${only}`) as HTMLElement;
    if (table) {
      const bodyHeight = document.body.offsetHeight;
      const header = document.querySelector(".el-header.header") as HTMLElement;
      const labels = document.querySelector(".labels-component") as HTMLElement;
      const topcomp = document.querySelector(".top-comp") as HTMLElement;

      const tHeader = table.querySelector(".header") as HTMLElement;
      const pages = table.querySelector(".pages") as HTMLElement;

      let __height =
        bodyHeight -
        header?.offsetHeight -
        (labels?.offsetHeight - header?.offsetHeight) -
        (topcomp ? topcomp.offsetHeight + 10 : 0) -
        (tHeader ? tHeader.offsetHeight : 0) -
        (pages ? pages.offsetHeight + 10 : 0);

      height.value = __height + offsetHeight.value - 40;

      hasTopComp.value = !!topcomp;
    }
  });
};

const sizeChange = (val: number) => {
  pageSize.value = val;
  emits("sizeChange", val);
};

const currentChange = (val: number) => {
  currentPage.value = val;
  emits("currentChange", val);
};

const clickButton = (btn: any, scope: any) => {
  emits("clickButton", { btn, scope });
};

const clickDropdownButton = (command: any) => {
  // emits('clickButton', command)
  dropdownRef.value?.handleClose();
  clickButton(command.btn, command.scope);
};

// 移除未使用的变量
// const datalsit = ref([2, 0, 1, 1, 2, 0]);
const arraySpanMethod = ({ rowIndex, columnIndex }: SpanMethodProps) => {
  if (UserStore.getUserInfo) {
    const { account } = UserStore.getUserInfo;

    if (
      route.query.id == "3a0e676f-1e8c-5281-0504-d353c2fd07b9" &&
      account == "ykz-***********" &&
      route.query.type == ""
    ) {
      if (rowIndex === 0) {
        if (columnIndex === 0) {
          return [1, 2];
        } else if (columnIndex === 1) {
          return [0, 0];
        }
      }
      // 判断第一列的行合并
      if (columnIndex === 0) {
        const _row = getSpanArr(tableData.value, "name", "parentKey")[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          rowspan: _row,
          colspan: _col,
        };
      }
    } else if (
      route.query.id == "3a0e676f-1e8c-5281-0504-d353c2fd07b9" &&
      account == "ykz-***********" &&
      route.query.type == ""
    ) {
      if (rowIndex === 23) {
        if (columnIndex === 0) {
          return [1, 3];
        } else if (columnIndex === 1 || columnIndex === 2) {
          return [0, 0];
        }
      }
    }
  }
};
const getSpanArr = (data: any, key: any, faKey: any) => {
  // 遍历数据
  let spanArr = [];
  let pos = 0;
  for (let i = 0; i < data.length; i++) {
    // 如果是第一个数据，就将列表spanArr添加一个1，表示暂时只有一个名字相同的、且将索引pos赋值为0
    if (i === 0) {
      spanArr.push(1);
      pos = 0;
    } else {
      // 判断当前元素与上一个元素是否相同（且只有同faKey 下的第二列才能合并，不同faKey 下的第二列重新计数,如果不需要这样判断，就去除&&后面的判断）

      if (data[i][key] === data[i - 1][key] && data[i][faKey] === data[i - 1][faKey]) {
        // 如果相同就将索引为 pos 的值加一
        spanArr[pos] += 1;
        // 且将数组添加 0
        spanArr.push(0);
      } else {
        // 如果元素不同了，就可以通过索引为 pos 的值知晓应该需要合并的行数
        // 同时，我们再次添加一个值1，表示重新开始判断重复姓名的次数
        spanArr.push(1);
        // 同时 索引加一
        pos = i;
      }
    }
  }
  console.log("索引数组：");
  console.log(spanArr, key);
  return spanArr;
};

const handleRowClick = (row: any, column: any, event: any) => {
  console.log("row click:", row, column, event);
  if (props.currentIndex == 1 && props.cureerntText == "任务创建") return;
  if (props.checkbox) {
    baseTable.value.toggleRowSelection(row);
  }
  emits("rowClick", row, column, event);
};

const selectionChange = (selection: []) => {
  if (__selectedStatus.value.emitChange) {
    emits("selectionChange", selection);
  }
};

const handleSelect = (selection: any, row: any) => {
  console.log("handleSelect里边的", baseTable.value.getSelectionRows());
  emits("handleSelect", { selection, row });
};

const selected = (isEmit: boolean = true) => {
  __selectedStatus.value.all.checked = false;

  __selectedStatus.value.local.checked = true;
  __selectedRow(true, true);
};

const selectedAll = () => {
  __selectedStatus.value.local.checked = false;

  let status = !__selectedStatus.value.all.checked;
  __selectedRow(status);
  emits("selectionAll", status);
  __selectedStatus.value.all.checked = status;
};

const __selectedRow = (selected: boolean, emit: boolean = false) => {
  __selectedStatus.value.emitChange = false;
  tableData.value?.forEach((row) => baseTable.value.toggleRowSelection(row, selected));
  if (emit) {
    emits("selectionChange", tableData.value);
  }
  __selectedStatus.value.emitChange = true;
};

const __openFilter = () => {
  openFilter.value = true;
};

const __filterChange = (val: any, field: string) => {
  emits("filterChange", val, field);
};
const __filterSubmit = (val: any, formRef: FormInstance | undefined) => {
  formRef?.validate().then((validate) => (openFilter.value = !validate));
  emits("filterSubmit", val, formRef);
};

const __handleCurrentChange = (val: any | undefined) => {
  emits("handleCurrentChange", val);
};
// 排序发生改变
const _handleSortChange = (val: any) => {
  emits("sortableChange", val);
};
// 展开项发生改变
const _handleExpandChange = (val: any) => {
  emits("expandChange", val);
};
const __handleScroll = (val: any) => {
  console.log(val);
};
// const __eval = (str: string, row: any) => eval(str)

const __eval = (str: string, row: any) => {
  if (!str || !row) {
    return true;
  }
  try {
    const func = new Function("row", `return ${str}`);
    return func(row);
  } catch (e) {
    console.error("Error: TableV2 Component setEval", e);
  }
  // return eval(str)
};

const setButtonWidth = () => {
  if (buttons.value?.length === 0) return;

  const isMore = buttons.value.some((f) => f.more)
  const unmore = buttons.value.filter((f) => !f.more)

  // 计算直接显示的按钮数量（包括"更多"按钮）
  const directButtonCount = unmore.length + (isMore ? 1 : 0)

  // 计算直接显示的按钮文字（包括"更多"）
  const directButtonText = unmore.map((m) => m.title).join('') + (isMore ? '更多' : '')

  // 优化宽度计算：更紧凑但仍能容纳所有按钮
  const w = directButtonText.length * 14 + directButtonCount * 24 + 30

  console.log('btns:', buttons.value)
  console.log('计算宽度:', w, '直接按钮数:', directButtonCount, '文字:', directButtonText)
  buttonWidth.value = w

  // 标记这是通过动态计算设置的宽度，避免被setFnWidth覆盖
  buttonWidth._isDynamicallyCalculated = true
};

const setFnWidth = (again = false) => {
  // 如果宽度是通过动态计算设置的，不要覆盖
  if (buttonWidth._isDynamicallyCalculated) {
    console.log('跳过setFnWidth，使用动态计算的宽度:', buttonWidth.value);
    return;
  }

  const el = baseTable.value?.$el;
  let width = 0;

  if (el) {
    let btns = el.querySelector(".table-component-btns")?.children;

    if (again) {
      el.querySelectorAll(".el-table__body tbody tr").forEach((tr: any) => {
        const buttons = tr.querySelector("td:last-child .cell")?.children;
        if (buttons?.length > btns?.length) {
          btns = buttons;
        }
      });
    }

    if (btns) {
      width += 24;
      for (const btn of btns) {
        width += btn.offsetWidth + 10;
      }
    }

    if (btns?.length === 0) {
      width = 164;
      setTimeout(() => setFnWidth(true), 1);
    }
  }
  buttonWidth.value = width;
};

const setSelectedByRowId = (id: string, bool: boolean) => {
  setTimeout(() => {
    tableData.value.forEach((row) => {
      if (row.id === id || row.Id === id) {
        baseTable.value.toggleRowSelection(row, bool);
      }
    });
  }, 0);
};
const callback = (row: any, index: number) => {
  if (props.currentIndex !== 1 && props.cureerntText !== "任务创建")
    return !row.callbackType;
  if (row.status == 13 || row.status == 3) return row.id.includes(row.id);
};

function handleFilter(value: any) {
  // console.log(value)
  emits("handleFilter", value);
}
onMounted(() => {
  if (!height.value) {
    __autoHeight();
    window.onresize = () => __autoHeight();
  }
  setButtonWidth();
  console.log("props.rowKey", props.rowKey);
  console.log("props.buttons", props.buttons);
  console.log("buttons.value", buttons.value);
});
function onscroll() {
  return {
    top: baseTable.value.scrollBarRef.wrapRef.scrollTop,
    left: baseTable.value.scrollBarRef.wrapRef.scrollLeft,
  };
}
function setScroll(top: number, left: number) {
  baseTable.value.setScrollLeft(left);
  baseTable.value.setScrollTop(top);
}
defineExpose({
  resize: () => __autoHeight(),
  selected: (id: string, bool: boolean) => setSelectedByRowId(id, bool),
  onscroll: () => onscroll(),
  setScroll: (top: number, left: number) => setScroll(top, left),
  getElTableRef: () => baseTable.value,
  clearSelection: () => baseTable.value.clearSelection(),
  toggleRowSelection: (row: any, bool: Boolean) =>
    baseTable.value.toggleRowSelection(row, bool),
});
</script>
<template>
  <div :id="only" class="base-table-comp">
    <!-- 功能位 -->
    <div class="header" v-if="visibleHeader" :class="{ notStyle: props.hideHeader }">
      <div class="layout" :class="{ 'mt-10px': !hasTopComp }">
        <div class="header-tip"></div>
        <div class="slot">
          <slot name="header"></slot>
        </div>
        <div class="setting">
          <el-button v-if="visibleExport" type="primary" size="small" title="导出">
            <!-- 导出 -->
            <i class="icon" i-majesticons-inbox-in-line></i>
            导出
          </el-button>

          <el-button
            v-if="visibleSearch"
            type="primary"
            size="small"
            @click="__openFilter"
            title="高级搜索"
          >
            <!-- 高级搜索 -->
            <i class="icon" i-majesticons-zoom-in-line></i>
            高级搜索
          </el-button>

          <el-button v-if="visibleSetting" type="primary" size="small" title="设置">
            <!-- 设置 -->
            <i class="icon" i-majesticons-settings-cog-line></i>
            设置
          </el-button>
        </div>
      </div>
      <slot name="headerRow"></slot>
    </div>
    <slot name="header-next"></slot>
    <!-- 表格 -->
    <el-table
      stripe
      v-bind="$attrs"
      v-loading="loading"
      ref="baseTable"
      :show-summary="showSummary"
      :default-expand-all="defaultExpandAll"
      :row-key="rowKey"
      :border="showBorder"
      :data="tableData"
      :height="autoHeight ? 'auto' : height"
      :span-method="arraySpanMethod"
      :element-loading-text="loadingText ?? '加载中...'"
      element-loading-background="rgba(255, 255, 255, 0.5)"
      :tree-props="treeProps"
      :show-overflow-tooltip="true"
      :scrollbar-always-on="true"
      @row-click="handleRowClick"
      @selection-change="selectionChange"
      @current-change="__handleCurrentChange"
      @sort-change="_handleSortChange"
      @expand-change="_handleExpandChange"
      @scroll="__handleScroll"
      @select="handleSelect"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        width="55"
        v-if="checkbox"
        :selectable="callback"
        :reserve-selection="reserveSelection"
        align="center"
      />

      <el-table-column label="序号" width="60" v-if="visibleIndex" fixed="left">
        <template #default="scope">
          {{ pageSize * (currentPage - 1) + scope.$index + 1 }}
          <!-- {{ scope.$index + 1 }}--{{ currentPage }}  -->
        </template>
      </el-table-column>
      <!-- <el-table-column type="expand" width="55"></el-table-column> -->
      <el-table-column type="expand" width="50" v-if="visibleExpand">
        <template #default="scope">
          <slot name="expand" v-bind:scope="scope"></slot>
        </template>
      </el-table-column>

      <template v-for="item of colData" :key="item.field">
        <TableColumnComp
          :col="item"
          :customSlots="customSlots"
          @handleFilter="handleFilter"
          v-if="item.hasOwnProperty('show') ? item.show : true"
        >
          <template v-for="slot of customSlots" #[slot]="scope">
            <slot :name="slot" v-bind="scope">{{
							(scope as any).rowData[slot] ?? '-'
            }}</slot>
          </template>
        </TableColumnComp>
      </template>

      <!-- 固定操作列 -->
      <el-table-column
        v-if="buttons?.length > 0 && tableData?.length > 0"
        fixed="right"
        label="操作"
        :header-align="buttons?.length === 1 ? 'center' : 'left'"
        :align="buttons?.length === 1 ? 'center' : 'left'"
        :width="buttonWidth"
        class="table-component-btns"
        :show-overflow-tooltip="false"
      >
        <template #default="scope">
          <slot name="buttons" :row="scope"></slot>

          <template v-for="btn of buttons.filter((f) => !f.more)">
            <el-popconfirm
              v-if="btn.type === 'danger'"
              :title="`确认${btn.title}吗?`"
              @confirm.stop="clickButton(btn, (scope as any).row)"
            >
              <template #reference>
                <el-button
                  size="small"
                  class="table-btn"
                  :title="btn.title"
                  :type="btn.type"
                  :disabled="!(true && __eval(btn.verify, (scope as any).row))"
                  @click.stop
                >
                  <span>{{ btn.title }}</span>
                </el-button>
              </template>
            </el-popconfirm>
            <el-button
              v-else
              size="small"
              class="table-btn"
              :title="btn.title"
              :type="btn.type"
              v-show="btn.hasOwnProperty('showBtn') ? btn.showBtn : true"
              :disabled="!(true && __eval(btn.verify, (scope as any).row))"
              @click.stop="clickButton(btn, (scope as any).row)"
            >
              <span v-if="btn.title == 'collect'">{{
                scope.row.ledgerCollection ? "取消收藏" : "收藏权限"
              }}</span>
              <span v-else>{{ btn.title }}</span>
            </el-button>
          </template>
          <el-dropdown
            style="margin-left: 10px"
            ref="dropdownRef"
            :key="scope.$index"
            v-if="buttons.some((btn) => btn.more)"
            trigger="hover"
            @command="clickDropdownButton"
          >
            <span class="el-dropdown-link">
              <el-button size="small">更多</el-button>
              <!-- <i class="i-ic-baseline-more-horiz" style="font-size: 20px; height: 24px"></i> -->
            </span>
            <template #dropdown>
              <el-dropdown-menu :key="scope.$index">
                <template v-for="btn of buttons.filter((f) => f.more)" :key="btn.code">
                  <!-- @click="clickButton(btn, (scope as any).row)" -->
                  <el-dropdown-item
                    v-if="__eval(btn.verify, (scope as any).row)"
                    :command="{btn, scope: (scope as any).row}"
                  >
                    {{ btn.title }}
                  </el-dropdown-item>
                </template>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>

      <template #empty>
        <el-empty
          :image-size="autoHeight ? 100 : 200"
          :description="loading && tableData.length === 0 ? '努力获取中...' : '暂无数据'"
        />
      </template>
    </el-table>

    <!-- 分页 -->
    <div class="pages pageination-component" v-if="visiblePage">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="currentPage"
        :default-page-size="10"
        :page-size="pageSize"
        :page-sizes="visiblePage ? _pageSizeArray : [99999]"
        size="small"
        :disabled="disabled"
        :background="true"
        :total="total"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </div>
    <!-- 筛选 -->
    <DrawerComp :drawer="openFilter" @closed="openFilter = false" title="高级搜索">
      <template #body>
        <FormComp
          :form="filterColData || []"
          :rules="filterRules"
          submitText="搜索"
          @onChange="__filterChange"
          @onSubmit="__filterSubmit"
        >
        </FormComp>
      </template>
    </DrawerComp>
  </div>
</template>
<style scoped lang="scss">
:deep(.el-popper) {
  font-size: 14px;
  max-width: 300px;
  max-height: 150px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 0px;
    height: 0px;
  }
}

// 修复操作列tooltip合并显示问题
:deep(.table-component-btns) {
  .cell {
    overflow: visible !important;
  }
}

.base-table-comp {
  :deep(.el-table),
  :deep(.el-table tr) {
    background-color: var(--z-theme);

    .el-popper {
      max-width: torem(500px);
    }
  }

  :deep(.el-table td.el-table__cell),
  :deep(.el-table--border th.el-table__cell) {
    border-color: var(--z-line) !important;
  }

  :deep(.el-table__cell) {
    color: var(--z-font-color);

    .link-click {
      display: block;
    }
  }

  :deep(.el-table__row) {
    .el-table__cell {
      padding: torem(5px);
    }

    /* .cell {
      padding: torem(8px);
    } */

    td:last-child:not(.el-table__expand-column) .cell {
      align-items: center;
      display: flex;
      flex-wrap: nowrap;
      height: torem(30px);
    }
  }

  :deep(.el-table--border::after),
  :deep(.el-table__border-left-patch),
  :deep(.el-table__inner-wrapper::before),
  :deep(.el-table--border .el-table__inner-wrapper::after) {
    background-color: var(--z-line);
  }

  :deep(.el-table__body tr.hover-row > td.el-table__cell) {
    background-color: rgba($color: var(--z-bg-secondary-rgb), $alpha: 1) !important;
  }

  :deep(.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell) {
    background-color: rgba($color: var(--z-table-even-bg-rgb), $alpha: 1);
  }

  :deep(.el-table th.el-table__cell),
  :deep(.el-table thead.is-group th.el-table__cell) {
    background-color: rgba($color: var(--z-table-even-bg-rgb), $alpha: 1);
    font-size: torem(13px);
    .el-tooltip__trigger {
      display: inline;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  :deep(.el-table.is-scrolling-right .el-table-fixed-column--left.is-last-column::before),
  :deep(.el-table.is-scrolling-middle
      .el-table-fixed-column--left.is-last-column::before) {
    box-shadow: inset 10px 0 10px -10px rgba(var(--z-font-color-rgb), 0.15);
  }

  :deep(.el-table.is-scrolling-left
      .el-table-fixed-column--right.is-first-column::before),
  :deep(.el-table.is-scrolling-middle
      .el-table-fixed-column--right.is-first-column::before) {
    box-shadow: inset -10px 0 10px -10px rgba(var(--z-font-color-rgb), 0.15);
  }

  :deep(.el-table__expanded-cell) {
    background-color: var(--z-bg-secondary) !important;
  }

  :deep(.form-item) > * {
    width: 100% !important;
  }
}

.pageination-component {
  align-items: center;
  display: flex;
  padding: torem(20px) 0 0 0;
  width: 100%;

  :deep(.el-pagination.is-background .btn-prev),
  :deep(.el-pagination.is-background .btn-next),
  :deep(.el-pagination.is-background .el-pager li) {
    background-color: var(--z-theme);
    color: var(--z-font-color);

    &.is-active {
      color: var(--z-nav-font-color);
    }
  }

  :deep(.el-pagination.is-background .btn-prev.is-active),
  :deep(.el-pagination.is-background .btn-next.is-active),
  :deep(.el-pagination.is-background .el-pager li.is-active) {
    background-color: var(--z-nav-hover);
  }

  :deep(.el-input__wrapper),
  :deep(.el-select__wrapper) {
    background-color: var(--z-theme);
    box-shadow: 0 0 0 1px var(--z-line, var(--z-line)) inset !important;
    input {
      color: var(--z-font-color);
    }
  }
}
</style>
