<script setup lang="ts">
import {useUserStore} from '@/stores/useUserStore'
import {computed} from 'vue'
import {MenuIcon} from '@/define/icon.define'

interface Props {
	data: any
	isCollapse?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	isCollapse: true,
})
const userStore = useUserStore()
const badgeValue = computed(() => userStore.badgeObj)
const auditbadgeValue = computed(() => userStore.auditObj)
const emits = defineEmits(['clickItem'])
function clickItem(item: any) {
	console.log(item)
	emits('clickItem', item)
}
</script>
<template>
	<el-sub-menu :index="data.id" v-if="data.child">
		<template #title>
			<template
				v-if="
					isCollapse &&
					data.displayName === '我的报表' &&
					badgeValue.totalPendingCount &&
					badgeValue.totalPendingCount !== 0
				"
			>
				<el-icon class="icons">
					<el-icon class="icon-svg" v-html="MenuIcon[data.displayName]"></el-icon>
					<div class="dot"></div>
				</el-icon>
			</template>
			<template v-else>
				<el-icon class="icon-svg" v-html="MenuIcon[data.displayName]"></el-icon>
			</template>

			<span> {{ data.displayName }}</span>
		</template>

		<el-menu-item-group>
			<template v-for="(sub, j) of data.child">
				<menu-item-comp
					v-if="sub.child"
					:data="sub"
					:is-collapse="isCollapse"
					@click-item="clickItem"
					class="sub-child-item"
				></menu-item-comp>
				<el-menu-item v-else :index="sub.id" @click="clickItem(sub)">
					<template v-if="sub.displayName === '我的待办'">
						<span>
							{{ sub.displayName }}
						</span>
						<span
							class="badge"
							v-if="badgeValue.totalPendingCount && badgeValue.totalPendingCount !== 0"
							>{{ badgeValue.totalPendingCount }}</span
						>
					</template>
					<template v-else-if="sub.displayName === '数据审核'">
						<span>{{ sub.displayName }}</span>
						<span
							class="badge"
							v-if="auditbadgeValue.totalCount && auditbadgeValue.totalCount !== 0"
							>{{ auditbadgeValue.totalCount }}</span
						>
					</template>
					<template v-else>
						<span class="tt">{{ sub.displayName }}</span>
					</template>
				</el-menu-item>
			</template>
		</el-menu-item-group>
	</el-sub-menu>
</template>
<style lang="scss" scoped>
.badge {
	margin-left: 10px;
	display: inline-block;
	height: 16px;
	line-height: 16px;
	text-align: center;
	border-radius: 10px;
	background-color: red;
	color: #fff;
	padding: 0 6px;
}

.icons {
	position: relative;

	.dot {
		position: absolute;
		top: 0;
		right: 0;
		width: 5px;
		height: 5px;
		border-radius: 50%;
		background-color: red;
	}
}

:deep(.el-menu-item-group__title) {
	padding: 0;
}

:deep(.icon-svg) {
	* {
		margin-top: 1px;
		margin-right: 5px;
		vertical-align: middle;
	}
}
</style>
