<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue';

type ListData = {
	id?: string
	name?: string
	checked?: boolean
	show?: boolean
}

interface Props {
	linked: any[] // 已关联的字段
	// ==========================
	data?: ListData[] // left list data
	fields?: any[],
	placeholder?: string // searach placeholder
	showSetting?: boolean // 是否显示设置按钮
}

const props: Props = withDefaults(defineProps<Props>(), {
	linked: [] as any,
	data: () => [
		{ id: '1', name: '数据一', checked: false },
		{ id: '2', name: '数据二', checked: false },
	],
	fields: [] as any,
	placeholder: '搜索',
	showSetting: false
})

const emits = defineEmits(['onToggleChecked', 'onDragStart', 'onDelete', 'onSettingClick'])
const hasLineked = computed(() => (item: any) => props.linked.some(s => s.ledgerId === item.id && props.linked.length > 0))

watch(() => [props.data, props.placeholder], (val) => {

	const [newData, newPlaceholder]: any = val

	refs.value.placeholder = newPlaceholder
	_list.value = newData?.map((item: ListData) => ({
		name: item.name,
		checked: false,
		id: item.id,
		show: true,
	}))

	if (_list.value?.length) {
		_list.value[0].checked = true
		onToggleChecked(_list.value[0])
	}

}, { deep: true })

watch(() => props.fields, (val) => {
	_fields.value = val
}, { deep: true })

const refs = ref({
	placeholder: props.placeholder
})

const _list = ref(props.data)
const _fields = ref(props.fields)
const searchVal = ref('')
const linkedScrollRef = ref()
const same = ref(false)
let curItem: any = null

const onToggleChecked = (itm: ListData) => {
	for (const item of _list.value || []) {
		if (item.name === itm.name) {
			if (item.checked) {
				emits('onToggleChecked', itm)
			}
		} else {
			item.checked = false
		}
	}

	same.value = curItem?.id === itm.id
	curItem = itm

	if (!same.value) {
		_fields.value = []
	}

	nextTick(() => {
		const el: HTMLElement | null = document.querySelector('.linked-active')
		if (el) {
			linkedScrollRef.value.scrollTo(el.offsetTop)
		}
	})
}

const onDragStart = (item: any) => {
	emits('onDragStart', item)
}

const onDelConfigById = (child: any) => {
	emits('onDelete', props.linked?.find((f: any) => f.ledgerField === child.field))
	child._column = ''
}

const onSetting = (item: any) => {
	item.checked = true
	same.value = curItem.id === item.id
	onToggleChecked(item)
	emits('onSettingClick', item)
}

const onFilterInput = (str: string) => {

	if (!_list.value) return

	for (const element of _list.value) {
		if (element.name?.includes(str) || str === '' || str.length === 0) {
			element.show = true
		} else {
			element.show = false
		}
	}
}

onMounted(() => {

})

defineExpose({

})

</script>
<template>
	<div class="linked-data-comp">
		<div class="left">
			<div class="search">
				<el-input v-model="searchVal" :placeholder="refs.placeholder" @input="onFilterInput">
					<template #prefix>
						<el-icon class="el-input__icon">
							<search />
						</el-icon>
					</template>
				</el-input>
			</div>

			<el-scrollbar ref="linkedScrollRef" style="height:calc(100% - 52px)">
				<div class="list">
					<el-skeleton v-if="_list?.length === 0" :rows="3" p10px style="border: none;" />
					<div v-for="item, index of _list" :key="index" class="item"
						:class="{ 'linked-active': item.checked, 'border-none': !item.show }">
						<template v-if="item.show">
							<el-checkbox v-model="item.checked" :label="item.name" @change="onToggleChecked(item)"
								class="linke-title">
								<span>{{ item.name }}</span>
								<el-icon v-if="props.linked.some(s => s.ledgerId === item.id)" title="有关联字段">
									<Link />
								</el-icon>
							</el-checkbox>
							<span class="svg-set" @click.stop="onSetting(item)" title="添加规则">
								<svg :key="index" v-if="props.showSetting" xmlns="http://www.w3.org/2000/svg" width="15"
									height="15" viewBox="0 0 24 24">
									<path fill="currentColor"
										d="M19.14 12.94c.04-.3.06-.61.06-.94c0-.32-.02-.64-.07-.94l2.03-1.58a.49.49 0 0 0 .12-.61l-1.92-3.32a.488.488 0 0 0-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54a.484.484 0 0 0-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58a.49.49 0 0 0-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6s3.6 1.62 3.6 3.6s-1.62 3.6-3.6 3.6z" />
								</svg>
							</span>
							<el-skeleton v-if="_fields?.length === 0 && item.checked" :rows="0" p10px />
							<div class="child" :class="{ open: item.checked }"
								:style="{ 'max-height': `${item.checked ? _fields!.length * 40 : 0}px` }">
								<!-- 拖动的元素 -->
								<div draggable="true" class="item" :class="{ linked: !!child._column }" v-for="child, i of _fields"
									:key="i" @dragstart="onDragStart(child)">
									<strong color="#f00" v-if="child._column">{{ child._column.split(':')[0] }}</strong>
									<span class="rule-svg"
										:class="{ 'js': child.type === 'calculation', 'tj': child.type === 'dimension' }">
										{{ child.title }}
									</span>
									<i v-if="child._column" @click="onDelConfigById(child)">&times;</i>
								</div>
							</div>
						</template>
					</div>
				</div>
			</el-scrollbar>
		</div>
	</div>
</template>