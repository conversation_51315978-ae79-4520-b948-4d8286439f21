<script setup lang="ts" name="PreviewDialog">
import { ref, computed, watch } from 'vue'
import { getConfigItemName } from '@/define/dataSourceConfig'
import { previewDataSourceConfig } from '@/api/DataSourceConfigApi'

interface Props {
  visible: boolean
  configId: string
  configKey: string
  language: string  // 新增语言参数
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
}>()

const loading = ref(false)
const previewContent = ref('')

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const isZh = computed(() => props.language === '中文')

const dialogTitle = computed(() => {
  return isZh.value ? '预览' : 'Preview'
})

const loadingText = computed(() => {
  return isZh.value
    ? '数据正在加载中，请耐心等待'
    : 'The data is loading. Please wait patiently'
})

const confirmText = computed(() => {
  return isZh.value ? '确定' : 'Confirm'
})

const cancelText = computed(() => {
  return isZh.value ? '取消' : 'Cancel'
})

// 显示配置项名称 - 根据传入的语言参数显示对应翻译
const displayName = computed(() => {
  if (!props.configKey) return ''
  return getConfigItemName(props.configKey, isZh.value ? 'zh' : 'en')
})



// 加载预览内容
const loadPreviewContent = async () => {
  if (!props.configId || !props.configKey) return

  loading.value = true
  previewContent.value = ''

  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 只显示配置项的翻译名称
    previewContent.value = displayName.value
  } catch (error) {
    console.error('加载预览内容失败:', error)
    previewContent.value = isZh.value ? '加载失败' : 'Loading failed'
  } finally {
    loading.value = false
  }
}

// 监听对话框打开
const handleOpen = () => {
  loadPreviewContent()
}

// 监听props变化，重新加载内容
watch(() => [props.visible, props.configId, props.language], ([visible, configId, language]) => {
  if (visible && configId) {
    loadPreviewContent()
  }
}, { immediate: true })

// 关闭对话框
const handleClose = () => {
  emit('close')
  previewContent.value = ''
}

// 确定按钮
const handleConfirm = () => {
  handleClose()
}
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :destroy-on-close="true"
    @open="handleOpen"
    @close="handleClose"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="preview-loading">
      <div class="loading-icon">
        <div class="dot dot1"></div>
        <div class="dot dot2"></div>
        <div class="dot dot3"></div>
        <div class="dot dot4"></div>
      </div>
      <p class="loading-text">{{ loadingText }}</p>
    </div>
    
    <!-- 预览内容 -->
    <div v-else class="preview-content">
      <div class="content-display">
        {{ previewContent }}
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ cancelText }}</el-button>
        <el-button type="primary" @click="handleConfirm">{{ confirmText }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.preview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  
  .loading-icon {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
    
    .dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: #409eff;
      animation: loading-bounce 1.4s infinite ease-in-out both;
      
      &.dot1 { animation-delay: -0.32s; }
      &.dot2 { animation-delay: -0.16s; }
      &.dot3 { animation-delay: 0s; }
      &.dot4 { animation-delay: 0.16s; }
    }
  }
  
  .loading-text {
    color: #666;
    font-size: 14px;
    margin: 0;
  }
}

.preview-content {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;

  .content-display {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    text-align: center;
    padding: 40px 20px;
    border: 1px dashed #ddd;
    border-radius: 4px;
    background-color: #fafafa;
    min-width: 200px;
  }
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
</style>
