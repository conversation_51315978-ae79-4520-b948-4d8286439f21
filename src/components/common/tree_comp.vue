<script setup lang="ts">
const props = defineProps({
	data: {
		type: Array,
		default: [],
	},
})
const emits = defineEmits(['handleNodeClick'])
const handleNodeClick = (data: any) => {
	// console.log(data)
	emits('handleNodeClick', data)
}

const defaultProps = {
	children: 'children',
	label: 'label',
}
</script>
<template>
	<el-tree :data="data" :props="defaultProps" @node-click="handleNodeClick">
		<template #default="{node, data}">
			<!-- 为了能绝对获取到id，此处三个元素都绑定有相同id -->
			<span class="custom-tree-node" :id="data.value">
				<span :id="data.value">
					<i v-if="data.areaOrganizationUnitType === 1" i-majesticons-map-marker-area-line>区域</i>
					<i v-if="data.areaOrganizationUnitType === 2" i-majesticons-home-line>部门</i>
					{{ node.label }}
				</span>
				<span>
					<slot name="extra" :data="data" :node="node"></slot>
				</span>
			</span>
		</template>
	</el-tree>
</template>
<style lang="scss" scoped>
.custom-tree-node {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	padding-right: 5px;
}
</style>
