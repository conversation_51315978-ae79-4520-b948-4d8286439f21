<script setup lang="ts">
import {
	ElNotification,
	TabPaneName,
	UploadFile,
	UploadFiles,
	UploadProgressEvent,
	UploadRawFile,
} from 'element-plus'
import {inject, ref, watch} from 'vue'
import {Request} from '#/interface'

interface Props {
	title?: string
	visible: boolean
	width?: string
	data: {}
	historyId?: string | number
	tempId?: string | number
	uploadUrl: string
}

const props = withDefaults(defineProps<Props>(), {
	title: '上传文件',
	visible: false,
	width: '70%',
})

const emits = defineEmits(['closed', 'onBeforeUpload', 'onErogress', 'onSuccess', 'onError'])

enum Status {
	start,
	ing,
	end,
	success,
	error,
	complete,
}

const status = ref(Status.start)
const axios = inject('#axios') as Request

const visible = ref(props.visible)
const activeName = ref('first')
const action = ref(props.uploadUrl)

const tableData = ref([])
const tableColData = [
	{field: 'wjmc', title: '文件名称'},
	{field: 'drr', title: '导入人'},
	{field: 'drsj', title: '导入时间'},
	{field: 'drzt', title: '导入状态'},
]
const pageConfig = ref({
	total: 0,
	currentPage: 1,
	pageSize: 10,
})

watch(
	() => [props.visible, props.uploadUrl],
	(newVal) => {
		visible.value = newVal[0] as boolean
		action.value = newVal[1] as string
		activeName.value = 'first'
	}
)

const __closed = () => {
	visible.value = false
	emits('closed')
}

const __clickTableButton = (column: any) => {
	// axios.buffer('/ledger/import/result', {})
	// .then( async (res) => {
	//   if (res.data) {
	//     axios.saveXls(res.data)
	//   } else {
	//     ElNotification({
	//       title: "警告",
	//       type: 'warning',
	//       message: '数据异常'
	//     })
	//   }
	// })
}

const onBeforeUpload = (rawFile: UploadRawFile) => {
	ElNotification({
		title: '提示',
		message: `${rawFile.name} 正在准备上传`,
		type: 'info',
		showClose: false,
	})
	emits('onBeforeUpload')
}

const onErogress = (evt: UploadProgressEvent, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
	status.value = Status.ing
	ElNotification({
		title: '提示',
		message: `${uploadFile.name} 文件正在上传中`,
		type: 'info',
		showClose: false,
	})
	emits('onErogress')
}

// 文件上传成功, 不代表导入成功
const onSuccess = (response: any, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
	status.value = Status.success
	ElNotification({
		title: '提示',
		message: `${uploadFile.name} 上传成功`,
		type: 'success',
		showClose: false,
	})
	emits('onSuccess')
}

const onError = (error: Error, uploadFile: UploadFile, uploadFiles: UploadFiles) => {
	status.value = Status.error
	ElNotification({
		title: '提示',
		message: `${uploadFile.name} 上传失败`,
		type: 'error',
		showClose: false,
	})
	emits('onError')
}

const __downloadTemplate = () => {
	// 下载模版
	axios?.downloadExcel('模版名称', {id: props.tempId})
}

const __getHistoryList = () => {
	// 查询上传历史
	axios
		?.post('/ledger/import/history/page', {
			id: props.historyId,
			pageNo: pageConfig.value.currentPage,
			pageSize: pageConfig.value.pageSize,
		})
		.then((res) => {
			const {code, data} = res.data
			if (code === 200) {
				pageConfig.value.currentPage = data.current
				pageConfig.value.pageSize = data.size
				pageConfig.value.total = data.total

				tableData.value = data.records
			}
		})
}

const changeTabs = (name: TabPaneName) => {
	if (name === 'second') {
		pageConfig.value.currentPage = 1
		pageConfig.value.pageSize = 10
		pageConfig.value.total = 0
		__getHistoryList()
	}
}

const __tableSizeChange = (val: number) => {
	pageConfig.value.pageSize = val
	__getHistoryList()
}

const __currentPageChange = (val: number) => {
	pageConfig.value.currentPage = val
	__getHistoryList()
}
</script>
<template>
	<Dialog v-model="visible" :title="title" :width="width" :enableButton="false" @close="__closed">
		<el-tabs v-model="activeName" class="tabs" @tab-change="changeTabs">
			<el-tab-pane label="导入文件" name="first">
				<!-- 上传 -->
				<el-upload
					class="upload-comp"
					drag
					:data="data"
					:multiple="false"
					:action="uploadUrl"
					:show-file-list="false"
					:before-upload="onBeforeUpload"
					:on-progress="onErogress"
					:on-error="onError"
					:on-success="onSuccess"
				>
					<el-icon class="el-icon--upload"><upload-filled /></el-icon>
					<div class="el-upload__text">
						<em>点击</em> 或将文件拖拽到这里上传
						<span pt10px pb10px display-block text="#999 12px"> 仅支持后缀名为xls或xlsx文件 </span>
					</div>
					<template #tip>
						<div class="tip-layout">
							<div>
								<span>导入步骤</span>
								<p>
									第一步：请下载<el-link type="primary" @click="__downloadTemplate"
										>导入模板.xls</el-link
									><br />
									第二步：上传本地文件导入<br />
									第三步：导入结束后，结果会显示在导入历史, 可查看成功/失败多少条数据
								</p>
							</div>
							<div>
								<span>导入要求</span>
								<p>
									1.导入文件后缀名必须为xls或xlsx<br />
									2.数据请勿放在合并的单元格中<br />
									3.文件大小请勿超过2M，数据量请勿超过3000条
								</p>
							</div>
						</div>
					</template>
				</el-upload>
			</el-tab-pane>

			<el-tab-pane label="导入文件历史" name="second">
				<!-- 导入历史 -->
				<BaseTableComp
					:colData="tableColData"
					:data="tableData"
					:visible-header="false"
					:visible-page="true"
					:checkbox="false"
					:auto-height="true"
					:total="pageConfig.total"
					:pageSize="pageConfig.pageSize"
					:currentPage="pageConfig.currentPage"
					@sizeChange="__tableSizeChange"
					@currentChange="__currentPageChange"
					@clickButton="__clickTableButton"
				>
				</BaseTableComp>
			</el-tab-pane>
		</el-tabs>
	</Dialog>
</template>
<style scoped lang="scss">
.tip-layout {
	display: flex;

	> div {
		flex: 1;

		&:nth-child(1) {
			margin-right: 20px;
		}
	}

	span {
		padding: 5px 0;
		margin-top: 10px;
	}

	p {
		border-radius: 5px;
		border: 1px solid #eee;
		font-size: 12px;
		height: calc(100% - 42px);
		line-height: 2;
		padding: 10px;

		a {
			font-size: 12px;
			position: relative;
			top: -1.5px;
		}
	}
}
</style>
