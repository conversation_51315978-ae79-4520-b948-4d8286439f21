<script lang="ts" setup>
const emits = defineEmits(['auth'])
const onClick = (str: string) => emits('auth', str)
</script>
<template>
	<div class="authorized-comp">
		<div class="box">
			<h1>“一表通”智能报表</h1>
			<p>系统将获取您的渝快政用户名称、用户组织信息，以确保系统正常运行，请确认是否授权。</p>
			<p>我们将严格遵守相关法律法规与监管要求，依法使用您的信息，并应对您的信息保密！</p>
			<div class="btns">
				<el-button type="default" @click="onClick('cancel')">取消</el-button>
				<el-button type="primary" @click="onClick('confirm')">授权</el-button>
			</div>
		</div>
	</div>
</template>
<style lang="scss" scoped>
@keyframes AuthScale {
	0% {
		opacity: 0;
		transform: scale(0);
	}
	70% {
		opacity: 0.7;
		transform: scale(1.2);
	}
	80% {
		opacity: 0.8;
		transform: scale(1.1);
	}
	100% {
		opacity: 1;
		transform: scale(1);
	}
}
.authorized-comp {
	background-color: #efefef;
	height: 100%;
	left: 0;
	position: fixed;
	top: 0;
	width: 100%;
	z-index: 99999;

	.box {
		background: #f5fff5;
		box-shadow: 0 0 30px 0 rgba($color: #000000, $alpha: 0.12);
		border: var(--z-border);
		border-radius: 5px;
		left: calc(50% - 361px);
		position: fixed;
		padding: 30px 40px;
		top: calc(50% - 150px);
		opacity: 0;
		transform: scale(0);
		transform-origin: right bottom;
		transition: all 0.3s linear;
		animation: AuthScale 0.3s linear forwards;
		width: 722px;
	}

	h1 {
		color: #333;
		font-size: 1.5rem;
		padding: 0 0 20px 0;
	}

	p {
		color: #666;
		font-size: 1rem;
		line-height: 2.3;
		white-space: nowrap;
	}

	.btns {
		padding: 30px 0 0 0;
		text-align: right;

		button {
			height: 40px;
			margin-left: 20px;
			width: 100px;
		}
	}
}
</style>
