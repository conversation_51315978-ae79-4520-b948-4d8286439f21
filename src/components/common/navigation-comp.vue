<script setup lang="ts">
import { useViewStore } from '$/useViewStore';
import { onMounted, ref, watch } from 'vue';

interface Props {
  data: Array<any>,
  isCollapse?: boolean,
  defaultActive?: string,
}

const props = withDefaults(defineProps<Props>(), {
  isCollapse: true,
  defaultActive: ''
})

const emits = defineEmits(['clickItem'])
const store = useViewStore()

// 菜单
const navigation = ref(props.data)
const subNavigation = ref([]) as any
const grade = ref(0)
watch(() => props.data, (newVal, oldVal) => {
  navigation.value = newVal
  if (navigation.value.length) {
    if (navigation.value[store.viewIndex]) {
      subNavigation.value = navigation.value[store.viewIndex].child
    }
  }

  console.log(subNavigation.value);
})

/**
 * 点击左侧菜单
 * @param item 菜单对象
 */
const clickItem = (item: any) => {
  emits('clickItem', item)
}

onMounted(() => {

})

/** 从后端获取菜单后格式化菜单数据 */
const format = (raw: Array<any>): Array<any> => {
  if (raw && raw.length) {
    const result = raw.filter(item => item.parentId === null)
    result.sort((a, b) => Number(a.meta.sort) - Number(b.meta.sort))

    const setChildRecursive = (arr: Array<any>, parent: any) => {
      arr.forEach(item => {
        if (item.parentId === parent.id) {
          if (!parent.child) {
            parent.child = []
          }
          item._topIndex = parent._topIndex
          parent.child.push(item);
          setChildRecursive(arr, item)
        }
      })
    }

    result.forEach((item, i: number) => {
      item._topIndex = i
      setChildRecursive(raw, item)
    })

    // 排序
    const sortRecursive = (arr: Array<any>) => {
      arr.sort((a, b) => Number(a.meta.sort) - Number(b.meta.sort))
      arr.forEach(item => {
        if (item.child && item.child.length) {
          sortRecursive(item.child)
        }
      })
    }
    sortRecursive(result)
    return JSON.parse(JSON.stringify(result))
  }
  return []
}

const getGrade = (grades: number) => {
  grade.value = grades
}

const toggleChild = (index: number, isEmit: boolean = true) => {
  // 判断顶级菜单是否有子集
  // 判断是否需要新开窗口显示
  if (isEmit) {
    if (!navigation.value[index].child) {
      return emits('clickItem', navigation.value[index])
    }
    subNavigation.value = navigation.value[index].child
    if (!subNavigation.value[0].child) {
      emits('clickItem', subNavigation.value[0])
    } else {
      emits('clickItem', subNavigation.value[0].child[0])
    }
  } else {
    subNavigation.value = navigation.value[index].child
  }
}

defineExpose({
  format,
  toggleChild,
  getGrade
})

</script>
<template>
  <el-menu :default-active="defaultActive" class="left-menu" :collapse="isCollapse">
    <template v-for="item, i of (subNavigation as Array<any>)">
      <!-- 多级菜单 -->
      <el-sub-menu :index="item.id" v-if="item.child">
        <template #title v-if="isCollapse">
          <svg class="icon" aria-hidden="true">
            <use :xlink:href="item.meta.icon"></use>
          </svg>
        </template>
        <template #title v-else>
          <span class="unoicon">
            <svg class="icon" aria-hidden="true">
              <use :xlink:href="item.meta.icon"></use>
            </svg>
          </span>
          {{ item.displayName }}
        </template>

        <el-menu-item-group>
          <template #title v-if="isCollapse">{{ item.displayName }}</template>
          <template v-for="sub, j of item.child">
            <el-menu-item :index="sub.id" @click="clickItem(sub)">
              {{ sub.displayName }}
            </el-menu-item>
          </template>
        </el-menu-item-group>
      </el-sub-menu>
      <!-- 单级菜单 -->
      <template v-else>
        <el-menu-item :index="item.id" @click="clickItem(item)">
          <span class="unoicon">
            <svg class="icon" aria-hidden="true">
              <use :xlink:href="item.meta.icon"></use>
            </svg>
          </span>
          <template #title>{{ item.displayName }}</template>
        </el-menu-item>
      </template>
    </template>
  </el-menu>
</template>

<style scoped lang="scss">
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  font-size: 16px;
  fill: currentColor;
  overflow: hidden;
}
</style>