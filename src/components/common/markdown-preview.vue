<script setup lang="ts">
import { ref, watch, computed, onMounted, nextTick } from 'vue';
import { processMarkdownWithToken, isAuthRequiredUrl, addTokenToUrl } from '@/utils/imageTokenUtils';

interface Props {
  text: string;
}

const props = defineProps<Props>();
const previewRef = ref<HTMLElement | null>(null);

// 处理后的Markdown文本
const processedText = computed(() => {
  return processMarkdownWithToken(props.text);
});

// 处理图片请求，添加token参数
const processImages = () => {
  if (!previewRef.value) return;

  console.log('Processing images in Markdown preview');

  // 获取token
  const token = localStorage.getItem('access_token');
  if (!token) return;

  // 获取所有图片元素
  const images = previewRef.value.querySelectorAll('img');

  // 处理每个图片
  images.forEach(img => {
    // 检查图片是否需要鉴权
    if (img.src && isAuthRequiredUrl(img.src)) {
      // 直接修改图片的src，添加token参数
      const originalSrc = img.src;
      const newSrc = addTokenToUrl(originalSrc, token);

      console.log('Replacing image src:', originalSrc);
      console.log('New image src:', newSrc);

      // 设置新的src
      img.src = newSrc;
    }
  });
};

// 添加scrollToTarget方法，用于滚动到指定目标
const scrollToTarget = (options: { target: HTMLElement, scrollContainer: HTMLElement, top?: number }) => {
  const { target, scrollContainer, top = 0 } = options;

  if (!target || !scrollContainer) {
    console.error('scrollToTarget: target or scrollContainer is null');
    return;
  }

  // 计算目标元素相对于滚动容器的偏移量
  const targetOffset = target.getBoundingClientRect().top;
  const containerOffset = scrollContainer.getBoundingClientRect().top;
  const offsetTop = targetOffset - containerOffset + scrollContainer.scrollTop - top;

  // 滚动到目标位置
  scrollContainer.scrollTo({
    top: offsetTop,
    behavior: 'smooth'
  });
};

// 暴露方法给父组件
defineExpose({
  scrollToTarget
});

// 监听文本变化，处理图片
watch(() => props.text, () => {
  nextTick(() => {
    processImages();
  });
});

// 组件挂载后处理图片
onMounted(() => {
  nextTick(() => {
    processImages();
  });
});
</script>

<template>
  <div ref="previewRef">
    <v-md-preview :text="processedText"></v-md-preview>
  </div>
</template>

<style scoped>
/* 可以添加自定义样式 */
</style>
