<script setup lang="ts">
import util from '@/plugin/util'
import {useUserStore} from '@/stores/useUserStore'
import {ElNotification, FormInstance, ElIcon, TreeNode} from 'element-plus'
import {inject, onMounted, ref, watch} from 'vue'
import {Close} from '@element-plus/icons-vue'
import {styleText} from 'util'
import {getDepartmentChildren} from '@/api/ReportApi'
import {useArrayToTree} from '@/hooks/useConvertHook'
import {GetBindUsers} from '@/api/PlatformApi'
import {STAFFROLEARRAY} from '@/define/organization.define'
import {log} from 'console'
import {Axios} from 'axios'
const axios = inject('#axios') as any
const props = defineProps({
	data: {type: Array},
	defaultCheckedData: {type: Array},
	defaultCheckUserData: {type: Array},
	disabled: {type: Boolean, default: false},
	placeholder: {type: String},
	type: {type: String, default: 'select'},
	minWidth: {type: String, default: '150px'},
	minHeight: {type: String, default: '32px'},
})
const hasCollection = ref(false)
const emits = defineEmits<{
	(eventName: 'change', departmentList: any, userList: any): void
}>()
// 控制下拉类型显示和隐藏
const openModalIsVisible = ref(false)
// 控制弹窗类型显示和隐藏
const openDialogVisible = ref(false)
const treeRef = ref<any>(null)
const selectKeys = ref<string[]>([])
const expandedKeys = ref<string[]>([])
const hasCheckList = ref<any>([])
const checkedDepartmentList = ref<any>([])

const filterText = ref('')

const regionLsit = ref<any[]>([])
const checkRegion = ref<any[]>([])
const departmentList = ref<any[]>([])
const departmentGroupList = ref<any[]>([])
const departmentTotal = ref()
const departmentPage = ref({
	MaxResultCount: 10,
	SkipCount: 0,
})
const closeTag = (data: any, index: number) => {
	console.log(data)

	if (currentRegionIndex.value === 2) {
		treeRef.value.setChecked(data.id ? data.id : data.departmentId, false, false)
	}
	checkedDepartmentList.value.splice(index, 1)
	departmentList.value.forEach((e) => {
		if (e.id === data.id) {
			e.checked = false
		}
	})
}
const removeUser = (data: any, index: number) => {
	// if()

	selectedUserList.value.splice(index, 1)
	if (userTreeRef.value) {
		userTreeRef.value.setChecked(data.value, false, false)
	}
}
const removeCollection = async (e: any, data: any, index: number) => {
	e.stopPropagation()
	console.log(data)

	// closeTag(data, index)
	await removeFavoriteGroup(data.id)
	getFavoriteGroupList()
	hasCollection.value = false
	ElNotification.success('已取消该收藏分组')
}
// 点击取消
const cancel = () => {
	selectKeys.value = []
	checkedDepartmentList.value = []
	hasCheckList.value = []
	hasCheckUserList.value = []
	openModalIsVisible.value = false
}
// 点击确定
const handleOk = () => {
	hasCheckList.value = JSON.parse(JSON.stringify(checkedDepartmentList.value))
	hasCheckUserList.value = JSON.parse(JSON.stringify(selectedUserList.value))
	// 抛出事件
	if (props.type === 'select') openModalIsVisible.value = false
	if (props.type === 'modal') openDialogVisible.value = false
	emits('change', hasCheckList.value, hasCheckUserList.value)
}
function handleClick(event: any) {
	const isSelf = document.getElementById('content')?.contains(event.target)
	const isModal = document.getElementById('select-modal')?.contains(event.target)
	const isInAddModal = document.getElementById('addModal')?.contains(event.target)
	// console.log(isInAddModal);

	if (!isSelf && !isModal) {
		openModalIsVisible.value = false
	}
	if (isSelf) {
		if (props.type === 'select') {
			openModalIsVisible.value = !openModalIsVisible.value
		}
		if (props.type === 'modal' && !props.disabled) {
			openDialogVisible.value = true
		}
	}
	if (isModal) {
		openModalIsVisible.value = true
	}
	if (isInAddModal) {
		openModalIsVisible.value = true
	}
}
// 树选择状态改变函数
function handleCheckChange(e: any, checked: boolean, childCheck: boolean) {
	if (checked) {
		checkedDepartmentList.value.push({
			...e,
			name: e.name ? e.name : e.displayName,
		})

		// 筛选掉type为区域的数据。区域不参与部门下发
		checkedDepartmentList.value = checkedDepartmentList.value.filter((v: any) => v.regionId)
	} else {
		let currentIndex = undefined
		checkedDepartmentList.value.forEach((x: any, index: number) => {
			if (x.id === e.id) {
				currentIndex = index
			}
		})
		if (currentIndex !== undefined) checkedDepartmentList.value.splice(currentIndex, 1)
	}
	// selectKeys.value = checkedDepartmentList.value.map(x => x.id)
}

const addDepartmentFavoriteModal = ref(false)
const collectionList = ref<any[]>([])
// 审核表单数据
const formArray = ref([
	{
		type: 'input',
		title: '分组名称',
		field: 'name',
		filterable: true,
	},
])
const formRules = {
	name: [{required: true, message: `请输入分组名称`, trigger: 'blur'}],
}
// 添加收藏分组
const formSubmit = (val: any, formRef: FormInstance | undefined) => {
	// 表单验证
	formRef?.validate().then(async (validate: boolean) => {
		const data = {
			name: val.name,
			userId: JSON.parse(localStorage.getItem('currentUserInfo') as string).id,
		}
		const res = await createFavoriteGroup(data)
		if (res) {
			const departmentCreateData = checkedDepartmentList.value.map(
				(v: any, index: number) => ({
					name: v.name ? v.name : v.displayName,
					departmentFavoriteGroupId: res.data.id,
					departmentId: v.id,
					sort: index + 1,
				})
			)
			try {
				await createDepartmentFavoriteByBatch(departmentCreateData)
				ElNotification.success('收藏成功')
				addDepartmentFavoriteModal.value = false
				hasCollection.value = false
				getFavoriteGroupList()
			} catch (error) {}
		}
	})
}
// 改变收藏按钮的状态
async function changeCollectionStatus() {
	if (!hasCollection.value) {
		if (checkedDepartmentList.value.length !== 0) {
			hasCollection.value = true
			addDepartmentFavoriteModal.value = true
		} else {
			ElNotification.warning('请选择需要加入收藏的部门')
		}
	} else {
		await removeFavoriteGroup(deparmentItemId.value)
		getFavoriteGroupList()
		hasCollection.value = false
		ElNotification.success('已取消该收藏分组')
		// 取消收藏
	}
	// hasCollection.value = !hasCollection.value
}
// 获取收藏分组列表
async function getFavoriteGroupList() {
	const res = await axios.request({
		method: 'get',
		url: '/api/platform/department-favorite-group?MaxResultCount=999&SkipCount=0',
		headers: {
			Urlkey: 'iframeCode',
		},
	})
	if (res) {
		const {data} = res
		collectionList.value = data.items.map((item: any) => ({...item, active: false}))
	}
}
async function removeFavoriteGroup(id: string) {
	return await axios.request({
		method: 'delete',
		url: `/api/platform/department-favorite-group/${id}`,
		headers: {
			Urlkey: 'iframeCode',
		},
	})
}
// 添加收藏分组
async function createFavoriteGroup(data: any) {
	return await axios.request({
		method: 'post',
		url: '/api/platform/department-favorite-group',
		headers: {
			Urlkey: 'iframeCode',
		},
		data,
	})
}
// 获取部门收藏列表
async function getDepartmentFavoriteList(groupId: string) {
	// /api/platform/department-favorite
	return await axios.request({
		method: 'get',
		url: `/api/platform/department-favorite?DepartmentFavoriteGroupId=${groupId}`,
		headers: {
			Urlkey: 'iframeCode',
		},
	})
}
const deparmentItemId = ref()
// 批量添加部门收藏
async function createDepartmentFavoriteByBatch(data: any) {
	return await axios.request({
		method: 'post',
		url: `/api/platform/department-favorite/batch`,
		headers: {
			Urlkey: 'iframeCode',
		},
		data,
	})
}
async function clickDropdownItem(item: any, e: any) {
	item.active = !item.active
	deparmentItemId.value = item.id
	e.stopPropagation()
	openModalIsVisible.value = true
	const res = await getDepartmentFavoriteList(item.id)
	if (res) {
		// treeRef.value.setCheckedKeys(res.data.items.map((x: any) => x.departmentId))
		setTimeout(() => {
			if (item.active) {
				checkedDepartmentList.value = res.data.items
					.map((v: any) => ({...v, id: v.departmentId}))
					.concat(checkedDepartmentList.value)
					.reduce((acc: any, current: any) => {
						const x = acc.find((item) => item.id === current.id)
						if (!x) {
							return acc.concat([current])
						} else {
							return acc
						}
					}, [])
			} else {
				checkedDepartmentList.value = checkedDepartmentList.value.filter(
					(v) => !res.data.items.map((x) => x.departmentId).includes(v.id)
				)
			}

			// checkedDepartmentList.value = Array.from(
			// new Set([...checkedDepartmentList.value, ...res.data.items.map((v: any) => v.departmentId)])
			// )
			console.log(4444, checkedDepartmentList.value)
			departmentList.value.forEach((e: any) => {
				if (checkedDepartmentList.value.find((v) => v.id === e.id) === undefined) {
					e.checked = false
				} else {
					e.checked = true
				}
			})
		}, 500)
		hasCollection.value = false
		if (collectionList.value.every((x) => !x.active)) {
			hasCollection.value = false
		}
	}
}
const filterMethod = (query: string, node: any) => {
	return node?.label!.includes(query)
}
async function getRegionList() {
	await axios
		?.request({
			method: 'get',
			url: '/api/platform/region/regions',
			headers: {
				Urlkey: 'iframeCode',
			},
		})
		.then(async (res: any) => {
			const promise: any[] = []
			const departmentList: any = []
			const {city, district, community, street} = useUserStore().getCurrentDepartment
			console.log(useUserStore().getCurrentDepartment)

			const role = community || street || district || city
			console.log(role)
			console.log(res.data)

			const node = res.data.find((f: any) => f.name === role)
			console.log(node)

			const nodeChild = res.data
				.filter((f: any) => f.parentId === node?.id || f.id === node?.id)
				.map((c) => ({...c, label: c.name, value: c.id}))
			regionLsit.value = util.arrayToTree(nodeChild)
			checkRegion.value = nodeChild.map((v: any) => v.id)

			if (checkRegion.value.length === regionLsit.value[0]?.children.length) {
				regionIds.value = checkRegion.value.concat(regionLsit.value[0].id)
			} else {
				regionIds.value = checkRegion.value
			}
			await getDepartmentList(regionIds.value)
		})
}
async function currentChange(e: number) {
	departmentPage.value.MaxResultCount = 10
	departmentPage.value.SkipCount = departmentPage.value.MaxResultCount * (e - 1)
	getDepartmentList(regionIds.value)
}
async function sizeChange(e: number) {
	departmentPage.value.SkipCount = 0
	departmentPage.value.MaxResultCount = e
	getDepartmentList(regionIds.value)
}
// 选中部门改变
function selectChange(checked: boolean, data: any) {
	if (checked) {
		checkedDepartmentList.value.push({
			...data,
			name: data.name ? data.name : data.displayName,
		})
	} else {
		let currentIndex = undefined
		checkedDepartmentList.value.forEach((x: any, index: number) => {
			if (x.id === data.id) {
				currentIndex = index
			}
		})
		if (currentIndex !== undefined) checkedDepartmentList.value.splice(currentIndex, 1)
	}
}
async function getDepartmentList(regionIds: string[], filter?: string) {
	await axios
		?.request({
			method: 'post',
			url: `/api/platform/departmentInternal/department-extend-list`,
			data: {
				regionIds: regionIds,
				filter: filter ? filter : filterText.value,
				maxResultCount: departmentPage.value.MaxResultCount,
				skipCount: departmentPage.value.SkipCount,
				IgnoreCurrentDepartment: true,
			},

			headers: {
				Urlkey: 'iframeCode',
			},
		})
		.then((res: any) => {
			const {city, district, community, street} = useUserStore().getCurrentDepartment
			const role = community || street || district || city
			departmentList.value = res.data.items.map((e) => {
				return {
					...e,
					checked:
						checkedDepartmentList.value.find((v: any) => v.id === e.id) === undefined
							? false
							: true,
					displayName: e.fullName
						.split('/')
						.slice(
							e.fullName.split('/').findIndex((v) => v.replace('※', '') === role),
							e.fullName.split('/').length
						)
						.join('/'),
				}
			})

			departmentTotal.value = res.data.totalCount
		})
}
async function getDepartmentGroupList() {
	await axios?.get('/api/filling/plan-task/departmentGroup').then((res) => {
		const {data} = res
		if (data[0]?.departments) {
			data[0].departments = data[0]?.departments?.map((v) => ({
				...v,
				name: v.region === null ? v.name : v.region?.name + '-' + v.name,
			}))
			departmentGroupList.value = util.arrayToTree(data[0].departments)
		} else {
			departmentGroupList.value = []
		}
	})
}
watch(
	() => props.defaultCheckedData,
	(val) => {
		console.log(2222, val)
		if (val && val !== null) {
			checkedDepartmentList.value = val
			hasCheckList.value = JSON.parse(JSON.stringify(checkedDepartmentList.value))
		} else {
			hasCheckList.value = []
		}
	}
)
watch(
	() => props.defaultCheckUserData,
	(val) => {
		if (val && val !== null) {
			selectedUserList.value = val
			hasCheckUserList.value = JSON.parse(JSON.stringify(selectedUserList.value))
			// 如果选择了用户的话，需要回显选中状态
			// 需要预先加载目标部门的数据
		} else {
			selectedUserList.value = []
			hasCheckUserList.value = []
		}
	}
)
onMounted(async () => {
	checkRegion.value = []
	document.addEventListener('click', handleClick)
	getFavoriteGroupList()
	getRegionList()
	if (props.defaultCheckedData) {
		checkedDepartmentList.value = props.defaultCheckedData
		selectKeys.value = checkedDepartmentList.value.map((v: any) => v.id)
		hasCheckList.value = JSON.parse(JSON.stringify(checkedDepartmentList.value))
		expandedKeys.value = Array.from(
			new Set(hasCheckList.value.map((v: any) => v.parentId ?? null))
		)
	}
})

const onQueryChanged = async () => {
	// treeRef.value!.filter(val)
	await getDepartmentList(regionIds.value, filterText.value)
}
const filterUserName = ref()
const onQuerySearch = async () => {
	// 如果有筛选数据
	if (filterUserName.value || filterUserName.value != '') {
		axios
			?.request({
				method: 'get',
				url: `/api/platform/department/department-bind-users?filter=${filterUserName.value}`,
			})
			.then((res: any) => {
				const {data} = res
				const arr = data.items
					.map((v) => ({
						...v,
						label: v.name,
						name: v.department?.parent?.name + '-' + v.department?.name + '-' + v.name,
						value: v?.department?.id + '/' + v.id,
						departmentId: v?.department?.id,
						isLeaf: true,
						disabled: false,
					}))
					.filter((user: any) => {
						return (
							(user.staffRole.includes(STAFFROLEARRAY[2]) ||
								user.staffRole.includes(STAFFROLEARRAY[0]) ||
								user.staffRole.includes(STAFFROLEARRAY[3])) &&
							user.id !==
								JSON.parse(localStorage.getItem('currentUserInfo') as string).id
						)
					})
				departmentGroupOrigin.value = useArrayToTree(
					arr,
					'id',
					'parentId',
					'name',
					true,
					'children'
				)
			})
	} else {
		if (selectedUserList.value.length > 0) {
			changeRegion({name: '人员列表', value: '2', isAcitve: true})
		} else {
			getUserDepartment()
		}
	}
}
const prop = {
	value: 'id',
	label: 'label',
	children: 'children',
}
// watch(
// () => filterText.value,
// (val) => {
// treeRef.value!.filter(val)
// }
// )
watch(
	() => openModalIsVisible.value,
	(val) => {
		if (val) {
			if (props.type === 'select') {
				deparmentItemId.value = null
			}
			// hasCollection.value = false
			if (hasCheckList.value.length !== 0) {
				// 获取默认选中的节点内容
				selectKeys.value = hasCheckList.value.map((v: any) => v.id)
				// 获取默认展开的树节点
				expandedKeys.value = Array.from(
					new Set(hasCheckList.value.map((v: any) => v.parentId ?? null))
				)
			} else {
				if (props.type === 'select') {
					checkedDepartmentList.value = []
					selectKeys.value = []
					expandedKeys.value = []
				}
			}
		}
	}
)
watch(
	() => openDialogVisible.value,
	(val) => {
		if (val) {
			deparmentItemId.value = null
			currentRegionIndex.value = 1
			regionList.value.forEach((x) => {
				if (x.value === '1') {
					x.isActive = true
				} else {
					x.isActive = false
				}
			})
			filterText.value = ''
			// hasCollection.value = false
			if (hasCheckList.value.length !== 0) {
				// 获取默认选中的节点内容
				selectKeys.value = hasCheckList.value.map((v: any) => v.id)
				// 获取默认展开的树节点
				expandedKeys.value = Array.from(
					new Set(hasCheckList.value.map((v: any) => v.parentId ?? null))
				)
			} else {
				checkedDepartmentList.value = []
				selectKeys.value = []
				expandedKeys.value = []
			}
		}
	}
)
const regionIds = ref<any[]>([])
const hasCheckUserList = ref<any[]>([])
const regionList = ref([
	{name: '部门列表', value: '1', isActive: true},
	{name: '人员列表', value: '2', isActive: false},
])
const currentRegionIndex = ref(1)
const activeNames = ref(['1', '2'])
async function changeRegion(item: any) {
	// checkedDepartmentList.value = []
	currentRegionIndex.value = Number(item.value)

	// departmentList.value = departmentList.value.map((v) => ({...v, checked: false}))
	regionList.value.forEach((x) => {
		if (x.value === item.value) {
			x.isActive = true
		} else {
			x.isActive = false
		}
	})

	if (currentRegionIndex.value === 2) {
		// 如果有选中的值 则默认加载值所在的父级列表
		if (selectedUserList.value.length > 0) {
			const departmentIds = Array.from(
				new Set(selectedUserList.value.map((v: any) => v.departmentId))
			)
			let users: any[] = []

			// 使用Promise.all等待所有异步请求完成
			const promises = departmentIds.map(async (id: string) => {
				const res1 = await GetBindUsers(id)
				const data1 = res1.data
				const filteredUsers = data1.items
					.filter((user: any) => {
						return (
							(user.staffRole.includes(STAFFROLEARRAY[2]) ||
								user.staffRole.includes(STAFFROLEARRAY[0]) ||
								user.staffRole.includes(STAFFROLEARRAY[3])) &&
							user.id !==
								JSON.parse(localStorage.getItem('currentUserInfo') as string).id
						)
					})
					.map((res2: any) => ({
						label: res2.name,
						departmentId: res2?.department?.id,
						value: res2?.department?.id + '/' + res2.id,
						isLeaf: true,
						parentId: id,
						id: res2.id,
						name: res2.name,
						checked: true,
						disabled: res2?.department?.id ? false : true,
					}))
				users.push(...filteredUsers)
				return filteredUsers
			})

			// 等待所有请求完成后再继续
			Promise.all(promises).then(async (res) => {
				await getUserDepartment()
				let arr: any = util.treeToArray(
					JSON.parse(JSON.stringify(departmentGroupOrigin.value))
				)
				defaultCheckedUserKeys.value = users.map((v) => v.value)
				// 基于id属性进行去重，而不是简单地使用Set
				arr = arr.concat(users).reduce((unique: any[], item: any) => {
					// 检查是否已经存在相同id的项
					const exists = unique.find((x: any) => x.value === item.value)
					if (!exists) {
						unique.push(item)
					}
					return unique
				}, [])
				departmentGroupOrigin.value = useArrayToTree(
					arr,
					'id',
					'parentId',
					'name',
					true,
					'children'
				)
			})

			return
		}
	}
}
// 人员列表中的初始项
const departmentGroupOrigin = ref<any[]>([])
const selectedUserList = ref<any[]>([])
const defaultCheckedUserKeys = ref<any>([])
const getUserDepartment = async () => {
	const res = await getDepartmentChildren()
	const {data} = res
	let arr = data.map((v: any) => ({
		...v,
		name: v.name,
		value: v?.department?.id + '/' + v.id,
		disabled: v?.department?.departmentId ? false : true,
		children: v.children === null ? [] : v.children,
	}))

	departmentGroupOrigin.value = useArrayToTree(arr, 'id', 'parentId', 'name', true, 'children')
}
const userTreeRef = ref<any>()
const loadNode = async (node: any, resolve: any) => {
	if (node.data.length === 0) {
		await getUserDepartment()
		return
	}

	if (node.data.id && (node.data.children === null || node.data.children.length === 0)) {
		try {
			if (node.data.children === null) {
				node.data.children = []
			}
			const users = await GetBindUsers(node.data.id)
			const userList =
				users.data.items
					.filter(
						(user: any) =>
							(user.staffRole.includes(STAFFROLEARRAY[2]) ||
								user.staffRole.includes(STAFFROLEARRAY[0]) ||
								user.staffRole.includes(STAFFROLEARRAY[3])) &&
							user.id !==
								JSON.parse(localStorage.getItem('currentUserInfo') as string).id
					)
					.map((res: any) => ({
						label: res.name,
						departmentId: res?.department?.id,
						value: res?.department?.id + '/' + res.id,
						isLeaf: true,
						disabled: res?.department?.id ? false : true,
					})) ?? []

			resolve(node.data.children.concat(userList))
		} catch (error) {
			console.error('加载用户数据失败:', error)
			resolve([])
		}
	} else {
		node.data.children ? resolve(node.data.children) : resolve(node.data)
		// if(node.data.children){}
		setTimeout(() => {
			if (userTreeRef.value && selectedUserList.value.length > 0) {
				userTreeRef.value.setCheckedKeys(selectedUserList.value.map((v: any) => v.value))
			}
		}, 300)
	}
}
function deduplicateById(arr: any) {
	const seen: any = {}
	return arr.filter((item: any) => {
		const id = item.value
		// 如果 id 未记录，则保留该对象并标记为已见
		if (!seen[id]) {
			seen[id] = true
			return true
		}
		return false
	})
}

const handleCheckUserChange = (_: any, checkedKeys: any) => {
	if (checkedKeys.checkedNodes.length === 0) {
		selectedUserList.value = selectedUserList.value.filter((v) => v.id !== _.id)
	} else {
		selectedUserList.value.push(...checkedKeys.checkedNodes.filter((v: any) => !v.regionId))

		selectedUserList.value = deduplicateById(selectedUserList.value)
	}
	// const currentChecked = checkedKeys.checkedNodes.filter((v: any) => !v.regionId)
	// const existsInCurrentChecked = selectedUserList.value.some((user: any) =>
	// 	currentChecked.some((checkedItem: any) => checkedItem.id === user.id)
	// )
	// if (existsInCurrentChecked) {
	// 	// 存在于 currentChecked 中
	// 	selectedUserList.value = checkedKeys.checkedNodes.filter((v: any) => !v.regionId)
	// } else {
	// 	// 不存在于 currentChecked 中
	// }
}
watch(
	() => checkRegion.value,
	async (val) => {
		if (val.length !== 0) {
			regionIds.value = []
			if (checkRegion.value.length === regionLsit.value[0].children.length) {
				regionIds.value = checkRegion.value.concat(regionLsit.value[0].id)
			} else {
				regionIds.value = checkRegion.value
			}
			await getDepartmentList(regionIds.value)
			await getDepartmentGroupList()
		} else {
			regionIds.value = util.treeToArray(regionLsit.value).map((v) => v.id)
		}
	}
)
</script>
<template>
	<div
		class="content"
		id="content"
		:class="{activeClass: openModalIsVisible}"
		:style="{
			height:
				hasCheckList.length > 0 || hasCheckUserList.length + hasCheckUserList.length > 0
					? 'auto'
					: '32px',
			minHeight: props.minHeight,
			minWidth: props.minWidth,
		}"
		bg="#fff"
	>
		<div
			flex="1"
			class="placeholder"
			v-if="hasCheckList.length === 0 && hasCheckUserList.length === 0"
		>
			{{ placeholder }}
		</div>
		<div flex="1" v-if="hasCheckList.length !== 0 || hasCheckUserList.length !== 0">
			<el-tag v-for="item in hasCheckList" :key="item.id" style="margin-right: 5px">{{
				item.displayName ? item.displayName : item.name
			}}</el-tag>
			<el-tag v-for="item in hasCheckUserList" :key="item.id" style="margin-right: 5px">{{
				item.label
			}}</el-tag>
		</div>
		<!-- <div w="20px" v-if="!openModalIsVisible">
<el-icon>
<ArrowDown />
</el-icon>
</div>
<div w="20px" v-if="openModalIsVisible">
<el-icon>
<ArrowUp />
</el-icon>
</div> -->
	</div>
	<div
		class="select-modal"
		id="select-modal"
		:class="{unfold: openModalIsVisible && type === 'select' && !disabled}"
		@click.stop="(e) => e.stopPropagation()"
	>
		<div class="top" v-if="openModalIsVisible && type === 'select'">
			<div class="left">
				<div class="title df aic">
					<div>部门选择</div>
					<el-dropdown trigger="click" style="width: 300px; text-align: end">
						<div class="cursor-pointer">
							已收藏分组
							<svg
								xmlns="http://www.w3.org/2000/svg"
								width="17"
								height="17"
								viewBox="10 2 15 15"
							>
								<path
									fill="none"
									stroke="currentColor"
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M12.005 11.995v.01m0-4.01v.01m0 7.99v.01"
								/>
							</svg>
						</div>
						<template #dropdown>
							<el-dropdown-menu id="dropdownMenu" v-if="collectionList.length !== 0">
								<el-dropdown-item
									v-for="(item, index) in collectionList"
									:key="index"
									@click="clickDropdownItem(item, $event)"
									>{{
										item.name ? item.name : item.displayName
									}}</el-dropdown-item
								>
							</el-dropdown-menu>
							<el-empty
								style="width: 200px; height: 250px"
								v-if="collectionList.length === 0"
								description="空数据"
							></el-empty>
						</template>
					</el-dropdown>
				</div>
				<div class="select">
					<el-input
						v-model="filterText"
						size="small"
						@input="onQueryChanged"
						placeholder="请输入关键字"
					/>
					<el-tree-v2
						:height="400"
						:data="data"
						ref="treeRef"
						node-key="id"
						:default-checked-keys="selectKeys"
						:default-expanded-keys="expandedKeys"
						show-checkbox
						@check-change="handleCheckChange"
						:filter-method="filterMethod"
					/>
				</div>
			</div>
			<div class="right">
				<div class="title df aic">
					<div>共选择({{ checkedDepartmentList?.length ?? 0 }})</div>
					<div style="text-align: end">
						<el-icon
							:size="18"
							:style="{color: hasCollection ? '#eebc4c' : '#dddfe5'}"
							@click="changeCollectionStatus"
							class="cursor-pointer"
						>
							<StarFilled />
						</el-icon>
					</div>
				</div>
				<div class="select" style="padding: 10px 10px" x>
					<!-- <el-tag v-for="(tag, index) in checkedDepartmentList" :key="tag.id" closable
						@close="closeTag(tag, index)">
						{{ tag.name ? tag.name : tag.displayName }}
					</el-tag> -->
					<div></div>
				</div>
			</div>
		</div>
		<div class="footer" v-if="openModalIsVisible && type === 'select'">
			<el-button size="small" type="primary" @click="handleOk">确认</el-button>
			<el-button size="small" @click="cancel">取消</el-button>
		</div>
	</div>
	<Dialog
		v-model="openDialogVisible"
		:key="openDialogVisible"
		title="部门选择"
		width="1000"
		class="select-modal"
		@clickConfirm="handleOk"
	>
		<!-- <template #body> -->
		<div class="top">
			<div class="left" style="width: 520px">
				<div
					class="title w-full"
					style="
						display: flex;
						align-items: center;
						justify-content: flex-end;
						text-align: end;
					"
				>
					<!-- <div></div> -->
					<el-dropdown trigger="click" style="text-align: end">
						<div class="cursor-pointer" style="display: flex; align-items: center">
							已收藏分组
							<svg
								style="margin-top: -6px; margin-left: 5px"
								xmlns="http://www.w3.org/2000/svg"
								width="17"
								height="17"
								viewBox="10 2 15 15"
							>
								<path
									fill="none"
									stroke="currentColor"
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M12.005 11.995v.01m0-4.01v.01m0 7.99v.01"
								/>
							</svg>
						</div>
						<template #dropdown>
							<el-dropdown-menu id="dropdownMenu" v-if="collectionList.length !== 0">
								<el-dropdown-item
									v-for="(item, index) in collectionList"
									:style="{
										color: item.active ? '#6991d7' : '',
										fontWeight: item.active ? 'bold' : '',
									}"
									:key="index"
									style="margin-top: 10px"
									@click="clickDropdownItem(item, $event)"
								>
									<div class="df aic jcb" style="width: 150px">
										<div style="width: 150px">
											{{ item.name ? item.name : item.displayName }}
										</div>
										<div class="df" style="text-align: end">
											<ElIcon
												class="cursor-pointer"
												size="12"
												@click="removeCollection($event, item, index)"
											>
												<Close />
											</ElIcon>
										</div>
									</div>
								</el-dropdown-item>
							</el-dropdown-menu>
							<el-empty
								style="width: 200px; height: 250px"
								v-if="collectionList.length === 0"
								description="空数据"
							></el-empty>
						</template>
					</el-dropdown>
				</div>
				<el-button-group
					style="width: 100%; height: 30px; margin-top: 10px"
					class="mg-bottom-5"
				>
					<el-button
						type="default"
						v-for="item in regionList"
						:style="{color: item.isActive ? '#5a9cf8' : '#000'}"
						@click="changeRegion(item)"
						>{{ item.name }}</el-button
					>
				</el-button-group>
				<div class="w-full df flx aic" style="height: 40px" v-if="currentRegionIndex === 1">
					<div class="w-full pd-right-5">
						<el-tree-select
							v-model="checkRegion"
							:data="regionLsit"
							show-checkbox
							check-on-click-node
							multiple
							collapse-tags
							collapse-tags-tooltip
							:max-collapse-tags="1"
							:render-after-expand="false"
							class="w-full"
						></el-tree-select>
					</div>
					<div class="df w-full h-full aic pd-right-5">
						<el-input
							style="margin: 10px 0; flex: 1"
							v-model="filterText"
							@keyup.enter="onQueryChanged"
							placeholder="请输入关键字"
						/>
						<el-button class="mg-left-5" type="primary" @click="onQueryChanged"
							>查询</el-button
						>
					</div>
				</div>
				<div v-if="currentRegionIndex === 1" class="w-full" style="height: 366px">
					<div class="w-full" style="height: 285px; overflow-y: auto">
						<div class="w-full">
							<el-checkbox
								:title="
									!item.hasDataLeader
										? '该部门未设置数据管理岗，无法接收任务'
										: ''
								"
								size="small"
								v-for="item in departmentList"
								v-model="item.checked"
								:disabled="!item.hasDataLeader"
								:label="item.displayName"
								@change="(e: boolean) => selectChange(e, item)"
								class="w-full"
							>
								<template #default>
									{{ item.displayName }}
								</template>
							</el-checkbox>
						</div>
					</div>
					<div class="w-full df aic jce" style="height: 48px">
						<el-pagination
							small
							layout="total,prev,pager,next"
							:total="departmentTotal"
							@current-change="currentChange"
							@size-change="sizeChange"
							class="df jce"
						></el-pagination>
					</div>
				</div>
				<div
					style="height: 350px; width: 100%; margin-top: 10px"
					v-if="currentRegionIndex === 2"
				>
					<div class="df w-full aic pd-right-5">
						<el-input
							style="margin: 10px 0; flex: 1"
							v-model="filterUserName"
							@keyup.enter="onQuerySearch"
							placeholder="请输入关键字"
						/>
						<el-button class="mg-left-5" type="primary" @click="onQuerySearch"
							>查询</el-button
						>
					</div>
					<el-tree
						style="max-height: 350px; overflow: auto"
						ref="userTreeRef"
						:data="departmentGroupOrigin"
						show-checkbox
						node-key="value"
						:props="{
							label: 'label',
							value: 'value',
							isLeaf: 'isLeaf',
						}"
						:load="loadNode"
						lazy
						:default-checked-keys="defaultCheckedUserKeys"
						@check="handleCheckUserChange"
					/>
					<!-- @check-change="handleCheckChange" -->
				</div>
			</div>
			<div class="right" style="flex: 1">
				<div class="title" flex="~" items-center>
					<div class="flx" style="font-size: 13px">
						共选择({{ checkedDepartmentList?.length + selectedUserList?.length }})
					</div>
					<div class="flx" style="text-align: end">
						<el-icon
							class="cursor-pointer"
							:size="18"
							:style="{color: hasCollection ? '#eebc4c' : '#dddfe5'}"
							@click="changeCollectionStatus"
						>
							<StarFilled />
						</el-icon>
					</div>
				</div>
				<div class="select" style="padding: 10px 10px">
					<el-collapse v-model="activeNames">
						<el-collapse-item title="已选部门" name="1">
							<div
								class="check-item"
								v-for="(item, index) in checkedDepartmentList"
								:key="item.id"
							>
								<div>{{ item.displayName ? item.displayName : item.name }}</div>
								<div @click="closeTag(item, index)">
									<el-icon><CloseBold /></el-icon>
								</div>
							</div>
						</el-collapse-item>
						<el-collapse-item title="已选人员" name="2">
							<div
								class="check-item"
								v-for="(user, index) in selectedUserList"
								:key="user.id"
							>
								<div>{{ user.label }}</div>
								<div @click="removeUser(user, index)">
									<el-icon><CloseBold /></el-icon>
								</div>
							</div>
						</el-collapse-item>
					</el-collapse>
					<!-- <el-tag v-for="(tag, index) in checkedDepartmentList" :key="tag.id" closable
						@close="closeTag(tag, index)">
						{{ tag.displayName ? tag.displayName : tag.name }}
					</el-tag> -->
				</div>
			</div>
		</div>
		<!-- </template> -->
	</Dialog>
	<Dialog
		v-model="addDepartmentFavoriteModal"
		:key="addDepartmentFavoriteModal"
		id="addModal"
		title="添加收藏"
		width="600"
		:enableButton="false"
		@close=";(addDepartmentFavoriteModal = false), (hasCollection = false)"
	>
		<FormComp
			:form="formArray"
			:rules="formRules"
			:showResetButton="false"
			submitText="添加"
			@onSubmit="formSubmit"
		>
		</FormComp>
	</Dialog>
</template>
<style lang="scss" scoped>
.content {
	border-radius: 4px;
	width: 100%;
	// height: 32px;
	border: 1px solid #d9d9d9;
	cursor: pointer;
	display: flex;
	align-items: center;
	font-size: 14px;
	padding: 0 5px;
	position: relative;

	.placeholder {
		color: #aaacb2;
		padding: 0 6px;
	}

	&:hover {
		border-color: #437bc3;
	}
}

.active {
	background-color: #437bc3;
	// color: #3063c7;
}

.select-modal {
	margin-top: 10px;
	width: 1000px;
	transition: height 0.1s ease;
	background-color: #fff;
	height: 0px;
	box-shadow: 0px 0px 20px 1px rgba(199, 210, 223, 0.5);
	position: absolute;
	top: 40px;
	z-index: 9999;

	&.unfold {
		height: 300px;
	}

	.top {
		width: 100%;
		height: calc(100% - 40px);
		// overflow-y: auto;
		display: flex;
		div {
			// flex: 1;
			.title {
				align-items: center;
				display: flex;
				height: 40px;
				line-height: 40px;
				padding: 0 10px;
				border-bottom: 1px solid #f0f0f0;
			}

			.select {
				height: 450px;
				overflow-y: auto;
				padding: 10px;
				width: 100%;

				:deep(.el-tree) {
					height: 100%;
				}
				.check-item {
					padding: 0px 10px;
					height: 30px;

					display: flex;
					justify-content: space-between;
					align-items: center;
					cursor: pointer;
					&:hover {
						background-color: #e8eff8;
					}
				}
			}

			// &:first-child {
			// }
		}

		.left {
			border-right: 1px solid #f0f0f0;
		}
	}

	.footer {
		width: 100%;
		display: flex;
		height: 40px;
		justify-content: flex-end;
		align-items: center;
		border-top: 1px solid #f0f0f0;

		button {
			font-size: 12px;
			margin-right: 10px;
		}
	}
}

.activeClass {
	border-color: #437bc3;
}

input {
	width: 100%;
	height: 100%;
}

.el-button {
	height: 30px !important;
}

:deep(.el-select__collapse-tags) {
	width: 600px !important;
}

:deep(.el-dropdown-menu) {
	padding: 0 !important;
}
</style>
