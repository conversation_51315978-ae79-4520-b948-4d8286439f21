<!-- 智能助手-问题反馈页面 -->

<script lang="ts" setup>
import {onMounted, ref, watch} from 'vue'
import {getFeedback, getFeedbackById, removeFeedbackByUser} from '@/api/feedback'
import {displayName, FeedBackStatusTrans, FeedbackTypeList} from '@/define/feedback.define'
import {APIConfig} from '@/api/config'
import feedBackComp from './feedback-comp.vue'
import {ElMessageBox, ElNotification} from 'element-plus'
import {router} from '@/router'
const feedbackInfo = ref()
const previewList = ref<string[]>([])
const inOpenModel = ref(false)
const getFeedbackInfo = async (id: string) => {
	const res = await getFeedbackById(id)
	if (res.data.attachments.length !== 0) {
		res.data.attachments.forEach((v: any) => {
			v.path = APIConfig('base') + v.path
		})
		previewList.value = res.data.attachments.map((v) => v.path)
	}
	feedbackInfo.value = res.data
	console.log(2323, feedbackInfo.value)
}
const previewIndex = ref(0)
const beforePreview = (e: number) => {
	previewIndex.value = e
}
import util from '@/plugin/util'
const props = defineProps({
	isFull: {
		type: Boolean,
		default: false,
	},
})
const tableData = ref<any[]>([])
const currentPage = ref(1)
const searchParams = ref({
	Category: null,
	Status: null,
})
const colData = [
	{
		title: '反馈类型',
		field: 'category',
	},
	{
		title: '详细描述',
		field: 'description',
	},
	{
		title: '有关截图',
		field: 'attachments',
	},
	{
		title: '反馈时间',
		field: 'creationTime',
	},
	{
		title: '处理状态',
		field: 'status',
	},
]
const buttons = [
	{code: 'detail', title: '查看', icon: '<i i-majesticons-eye-line></i>', verify: 'true'},
	{code: 'edit', title: '编辑', icon: '<i i-majesticons-eye-line></i>', verify: 'row.status !== 3'},
	{
		code: 'delete',
		type: 'danger',
		title: '删除',
		icon: '<i i-majesticons-eye-line></i>',
		verify: 'true',
	},
]
const pageParams = ref({
	MaxResultCount: 10,
	SkipCount: 0,
})
const currentSize = ref(10)
const total = ref(0)
const openFeedbackModel = ref(false)
// 表格按钮点击行为
const clickButton = (btn: {
	btn: {
		code: string
		title: string
		verify: string
	}
	scope: any
}) => {
	if (btn.btn.code === 'delete') {
		// 删除
		remove(btn.scope)
	}
	if (btn.btn.code === 'detail') {
		view(btn.scope)
		// router.push({path: '/feedback/detail', query: {id: btn.scope.id}})
	}
	if (btn.btn.code === 'edit') {
		edit(btn.scope)
	}
}
// 分页查询
const sizeChange = (val: number) => {
	pageParams.value.SkipCount = 0
	pageParams.value.MaxResultCount = val
	getFeedbackList()
}
// 分页查询
const currentChange = (val: number) => {
	pageParams.value.MaxResultCount = 10
	pageParams.value.SkipCount = pageParams.value.MaxResultCount * (val - 1)
	getFeedbackList()
}
const getFeedbackList = async () => {
	pageParams.value = props.isFull
		? {
				MaxResultCount: 10,
				SkipCount: 0,
		  }
		: {
				MaxResultCount: 999,
				SkipCount: 0,
		  }
	const params = {
		...util.extractNonEmptyValuesFromSearchParams(searchParams.value),
		...pageParams.value,
	}

	const res = await getFeedback(params)
	console.log(res)
	tableData.value = res.data.items
	total.value = res.data.totalCount
}
const remove = (data: any) => {
	ElMessageBox.confirm('请确认是否删除？删除后不可恢复！', '删除')
		.then(async (type) => {
			if (type === 'confirm') {
				// removeFeedbackByUser
				// 软删除-仅对当前登陆用户不可见
				const res = await removeFeedbackByUser(data.id)
				console.log(222, res)
				if (res.status === 204 || res.status === 200) {
					ElNotification.success('已删除')
					getFeedbackList()
				}
			}
		})
		.catch((err) => {
			console.log(err)
		})
}
const detailModalIsVisible = ref(false)
const view = (feedback: any) => {
	getFeedbackInfo(feedback.id)
	detailModalIsVisible.value = true
}
const edit = (feedback: any) => {
	getFeedbackInfo(feedback.id)
	inOpenModel.value = true
}
const feedbackcomp = ref<any>()
// 子组件返回关闭弹窗
const updateInpoenModel = () => {
	inOpenModel.value = false
	getFeedbackList()
}
const handleInopenModel = () => {
	feedbackcomp.value.opensubmit()
}
// 点击关闭
const handleClose = () => {
	feedbackcomp.value.onCancel()
}
onMounted(() => {
	// console.log('props', props)
	getFeedbackList()
})
watch(
	() => props.isFull,
	(val) => {
		getFeedbackList()
	}
)
defineExpose({
	getFeedbackList,
})
</script>
<template>
	<div w-full h-full overflow-y-auto>
		<div v-if="isFull" class="w-full h-full">
			<BaseTableComp
				:colData="colData"
				:data="tableData"
				:buttons="buttons"
				:checkbox="false"
				:total="total"
				:visible-search="false"
				:visible-export="false"
				:visible-setting="false"
				:current-page="currentPage"
				:page-size="currentSize"
				@size-change="sizeChange"
				@current-change="currentChange"
				@click-button="clickButton"
			>
				<template v-slot:header>
					<div style="display: flex; align-items: center; width: 100%; justify-content: flex-end">
						<el-select
							class="ml-2 mr-2"
							clearable
							v-model="searchParams.Category"
							placeholder="请选择反馈类型"
							size="small"
							style="width: 220px"
						>
							<el-option
								v-for="item in FeedbackTypeList"
								:key="item.value"
								:label="item.name"
								:value="item.value"
							/>
						</el-select>
						<el-select
							class="ml-2 mr-2"
							clearable
							v-model="searchParams.Status"
							placeholder="请选择反馈状态"
							size="small"
							style="width: 220px"
						>
							<el-option
								v-for="item in FeedBackStatusTrans"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
						<el-button type="primary" size="small" @click="getFeedbackList">搜索</el-button>
					</div>
				</template>
				<template #category="scope">
					{{ displayName(scope.rowData.category) }}
				</template>
				<template #contact="scope">
					{{ scope.rowData.contact ?? '-' }}
				</template>
				<template #attachments="scope">
					<el-popover
						placement="bottom"
						:width="scope.rowData.attachments.length * 110 + 20"
						trigger="hover"
						v-if="scope.rowData.attachments.length !== 0"
					>
						<template #reference>
							<el-link type="primary">附件</el-link>
						</template>
						<div w-full flex="~">
							<el-image
								v-for="item in scope.rowData.attachments"
								style="width: 100px; height: 100px; margin-right: 10px"
								:src="APIConfig('base') + item.path"
								fit="cover"
							/>
						</div>
					</el-popover>
					<span v-else>-</span>
				</template>
				<template #status="{rowData}">
					<!-- <el-tag type="success" v-if="rowData.status === true">已处理</el-tag>
					<el-tag type="warning" v-if="rowData.status === false">待处理</el-tag> -->
					<el-tag :type="FeedBackStatusTrans.filter((v) => v.value === rowData.status)[0].color">
						{{ FeedBackStatusTrans.filter((v) => v.value === rowData.status)[0].label }}
					</el-tag>
				</template>
			</BaseTableComp>
		</div>
		<div w-full v-else>
			<div
				style="
					border-bottom: 1px solid #e4e4e4;
					width: 100%;
					margin-top: 10px;
					padding-left: 20px;
					padding-right: 20px;
				"
				v-for="feedback in tableData"
				:key="feedback?.id"
			>
				<!-- {{ feedback }} -->
				<div class="title">
					<span style="display: inline-block; width: 130px; margin-right: 10px">{{
						displayName(feedback?.category)
					}}</span>
					<el-tag :type="FeedBackStatusTrans.filter((v) => v.value === feedback?.status)[0]?.color">
						{{ FeedBackStatusTrans.filter((v) => v.value === feedback?.status)[0]?.label }}
					</el-tag>
				</div>
				<div class="content" mt-5px style="margin-top: 5px">
					{{ feedback.description }}
				</div>
				<div class="attachments" mt-5px style="margin-top: 5px" v-if="feedback.attachments">
					<el-popover
						placement="bottom"
						:width="feedback.attachments.length * 110 + 20"
						trigger="hover"
					>
						<template #reference>
							<el-link v-if="feedback.attachments.length > 0" type="primary">附件</el-link>
						</template>
						<div w-full flex="~">
							<el-image
								v-for="item in feedback.attachments"
								style="width: 100px; height: 100px; margin-right: 10px"
								:src="APIConfig('base') + item.path"
								fit="cover"
							/>
						</div>
					</el-popover>
				</div>
				<div style="width: 100%; display: flex; margin-top: 50px; padding-bottom: 10px">
					<div style="flex: 1; text-align: start">{{ feedback.creationTime }}</div>
					<div style="text-align: end; width: 140px">
						<el-button link type="primary" size="small" @click="view(feedback)"> 查看 </el-button>
						<el-button
							link
							type="primary"
							size="small"
							v-if="feedback.status !== 3"
							@click="edit(feedback)"
						>
							编辑
						</el-button>
						<el-button link type="danger" size="small" @click="remove(feedback)"> 删除 </el-button>
					</div>
				</div>
			</div>
			<!-- <iframe  frameborder="0"></iframe> -->
		</div>
	</div>
	<!-- 详情弹窗 -->
	<!-- <DialogComp
		:visible="detailModalIsVisible"
		:key="detailModalIsVisible"
		title="问题反馈详情"
		width="700"
		:visibleFooterButton="false"
		@closed="detailModalIsVisible = false"
	>
		<template #body> -->
	<Dialog
		v-model="detailModalIsVisible"
		title="问题反馈详情"
		width="700"
		:enable-button="false"
		@close="detailModalIsVisible = false"
	>
		<div style="width: 100%; max-height: 600px; overflow: auto">
			<el-descriptions class="margin-top" :column="1" :label-align="'right'" size="default" border>
				<el-descriptions-item>
					<template #label>
						<div w-80px>反馈类型:</div>
					</template>
					{{ displayName(feedbackInfo?.category) }}
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div w-80px>反馈时间:</div>
					</template>
					{{ feedbackInfo?.creationTime }}
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div w-80px>联系方式:</div>
					</template>
					{{ feedbackInfo?.contact ?? '-' }}
				</el-descriptions-item>
				<el-descriptions-item>
					<template #label>
						<div w-80px>处理状态:</div>
					</template>
					<!-- {{ feedbackInfo?.status }}
						{{ FeedBackStatusTrans.filter((v) => v.value === feedbackInfo?.status)[0].color }} -->
					<el-tag
						:type="FeedBackStatusTrans.filter((v) => v.value === feedbackInfo?.status)[0]?.color"
					>
						{{ FeedBackStatusTrans.filter((v) => v.value === feedbackInfo?.status)[0]?.label }}
					</el-tag>
					<!-- <el-tag type="success" v-if="feedbackInfo?.status === true">已处理</el-tag>
						<el-tag type="warning" v-if="feedbackInfo?.status === false">待处理</el-tag> -->
					<!-- {{ feedbackInfo?.status ? '已处理' : '未处理' }} -->
				</el-descriptions-item>

				<el-descriptions-item>
					<template #label>
						<div class="cell-item" w-80px>详细描述:</div>
					</template>
					<!-- {{ info?.auditName }} -->
					<div w-full>
						{{ feedbackInfo?.description }}
					</div>
				</el-descriptions-item>
			</el-descriptions>

			<div
				style="
					font-weight: bold;
					padding-left: 10px;
					padding-top: 20px;
					font-size: 14px;
					color: #606266;
				"
			>
				相关截图
			</div>

			<div style="width: 100px; padding-left: 10px; display: flex; flex-wrap: wrap">
				<el-image
					v-for="(item, index) in feedbackInfo?.attachments"
					style="width: 200px; height: 150px; margin-top: 10px; margin-right: 10px"
					:src="item.path"
					:max-scale="7"
					:min-scale="0.2"
					fit="cover"
					:initial-index="previewIndex"
					:preview-src-list="previewList"
					:preview-teleported="true"
					@show="beforePreview(index)"
				/>
			</div>
			<div
				style="
					font-weight: bold;
					padding-left: 10px;
					padding-top: 20px;
					font-size: 14px;
					color: #606266;
				"
			>
				处理结果
			</div>
			<div
				pl-10px
				pt-10px
				pb-50px
				style="
					word-wrap: break-word;

					word-break: normal;
					padding-left: 10px;
					padding-top: 10px;
					padding-bottom: 50px;
				"
			>
				{{ feedbackInfo?.result ?? '暂无数据' }}
			</div>
		</div>
	</Dialog>
	<!-- </template>
	</DialogComp> -->
	<Dialog
		v-model="inOpenModel"
		title="问题反馈"
		width="600"
		@close="inOpenModel = false"
		@click-close="inOpenModel = false"
	>
		<feedBackComp
			:inopenModel="inOpenModel"
			ref="feedbackcomp"
			:data="feedbackInfo"
			@update="updateInpoenModel"
		></feedBackComp>
		<template #footer>
			<el-button style="margin-right: 5px; margin-top: -5px" type="smiall" @click="handleClose"
				><i class="icon i-ic-outline-cancel"></i>关闭</el-button
			>
			<el-button
				type="primary"
				@click="handleInopenModel"
				style="color: #fff !important; margin-right: 5px; margin-top: -5px"
				><i class="icon i-ic-round-task-alt"></i>反馈</el-button
			>
		</template>
	</Dialog>
</template>
<style lang="scss" scoped></style>
