<script setup lang="ts">
import {nextTick, onActivated, onDeactivated, onMounted, onUnmounted, ref, toRaw, watch} from 'vue'
import {__xls_config} from './__excel_config'
import XlsxPlus from '@/plugin/xlsx-plus'
import util from '@/plugin/util'
import {ElMessage} from 'element-plus'
import {useViewStore} from '$/useViewStore'
import Worker from '@/worker/LedgerFillWorder.ts?worker'

interface Props {
	sheets?: any[]
	showHeader?: boolean
	showAside?: boolean

	interval?: number
	enableReload?: boolean
	enableSave?: boolean
	enableAutoSave?: boolean
	enableProtect?: boolean
	enableImport?: boolean
	enableLinked?: boolean
	enableFreezeHeader?: boolean
	viewonly?: boolean
	isAlien?: boolean
	enableMergeCell?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	sheets: [] as any,
	showHeader: true,
	showAside: false,

	interval: 3000,
	enableSave: true,
	enableAutoSave: false,
	enableProtect: false,
	enableImport: true,
	enableLinked: true,
	enableFreezeHeader: true,
	viewonly: false,
	isAlien: false,
	enableMergeCell: false,
})

const emits = defineEmits([
	'onSave',
	'onAutoSave',
	'onImportBefore',
	'onImportFinish',
	'onAddRow',
	'onDeleteRow',
	'onDragToCell',
	'selectHightlight',
])
const useView = useViewStore()

watch(
	() => [props.sheets, props.showAside],
	(newVal) => {
		const [newSheets, newAside] = newVal
		sheets = toRaw(newSheets) as any[]
		console.log(11, sheets)
		if (sheets[0].data) {
			sheets[0].data.forEach((v: any) => {
				if (v.v?.ct) {
					// 去除时间格式
					if (v.v?.ct.t === 'd') {
						return
					} else {
						// 其他所有格式都会强制转为字符串
						if (v.v?.ct?.fa && v.v?.ct?.fa === 'General') {
							v.v.ct.fa = '@'
						}
						if (v.v?.ct?.fa && v.v?.ct?.fa === '#0.00000E+00') {
							v.v.ct.fa = '@'
							v.v.v = v.v.v.toString()
							v.v.m = v.v.v.toString()
						}
						if (v.v?.ct?.t && v.v?.ct?.t === 'g') {
							v.v.ct.t = 's'
						}
						if (v.v.v && v.v.v !== null) {
							v.v.m = v.v.v.toString()
						}
					}
				}
			})
		}
		refs.value.width = newAside ? 'calc(100% - 250px)' : '100%'
		if (_.lucky.resize) {
			setTimeout(() => _.lucky.resize(), 301)
		}

		// 重新加载
		if (props.enableReload) {
			refs.value.completed = false
		}

		if (refs.value.completed) return
		nextTick(() => {
			let timer = setTimeout(() => {
				init()
				clearTimeout(timer)
			}, 300)
		})
	},
	{deep: true}
)

watch(
	() => useView.times,
	() => {
		if (_.lucky) {
			let timer = setTimeout(() => {
				clearTimeout(timer)
				nextTick(() => _.lucky.resize())
			}, 700)
		}
	}
)

const id = ref('')
const _ = {lucky: {} as any, workbook: {} as any}
const hook = {
	initCompleted: () => {},
	cellUpdated: () => {},
	updated: (operate: any) => {
		if ((operate.type !== 'datachange' && refs.value.isFirstLoad) || refs.value.completed) {
			refs.value.isFirstLoad = false
		}

		// 不在导入中，也不是拖拽到单元格中, 不刷新
		if (
			refs.value.isFirstLoad ||
			refs.value.isImporting ||
			refs.value.isDragToCell ||
			!refs.value.isRefresh
		) {
			refs.value.isFirstLoad = false
			return
		}

		console.log('xlsx-plus updated...:', operate)

		const curdata = operate.curData || operate.curdata

		if (operate.type === 'datachange') {
			const [startRow, endRow] = operate.range[0].row
			_toModificData(startRow, endRow, curdata)
		}

		if (operate.type === 'addRC' && addRowsInfo) {
			insertRow(curdata)
		}

		if (operate.type === 'delRC') {
			deleteRow(operate.range[0].row)
		}
	},
	sheetActivate: (index: number, isPivotInitial: any, isNewSheet: any) => {
		console.log('xlsx-plus sheetActivate...')

		currentSheetIndex = index
	},
	rowInsertBefore: (rr: number, rc: number, rt: string) => {
		console.log('xlsx-plus rowInsertBefore...', rr, rc, rt)
		addRowsInfo = {rr, rc, rt}
	},
	insertCheck: (
		type: string,
		index: number,
		value: number,
		direction: string,
		sheetIndex: number,
		fn: Function
	) => {
		if (refs.value.isImporting) {
			fn(true)
		} else {
			console.log('xlsx-plus insertCheck...')
			let isInsert = true
			const curhead = refs.value.header[currentSheetIndex]
			if (curhead) {
				if (index <= curhead.rr && direction === 'lefttop') {
					ElMessage.warning('不能在表头插入行, 若已锁定, 请先解锁')
					isInsert = false
				}
			}
			if (refs.value.saving) {
				ElMessage.warning('正在保存中, 请稍后再试')
				isInsert = false
			}
			fn(isInsert)
		}
	},
	deleteCheck: (type: string, st: number, ed: number, sheetIndex: number, fn: Function) => {
		console.log('xlsx-plus deleteCheck...')
		let isDelete = true
		const curhead = refs.value.header[currentSheetIndex]
		if (curhead) {
			if (st <= curhead.rr) {
				ElMessage.warning('不能删除表头, 若已锁定, 请先解锁')
				isDelete = false
			}
		}
		if (refs.value.saving) {
			ElMessage.warning('正在保存中, 请稍后再试')
			isDelete = false
		}
		fn(isDelete)
	},
	cellDragStop: (cell: any, postion: any, sheetFile: any, ctx: any, event: any) => {
		console.log('xlsx-plus cellDragStop...', cell, postion)
		onDragToCell(cell, postion)
	},
	selectHightlightShow: (r1: number, c1: number, r2: number, c2: number, cellValue: any) => {
		emits('selectHightlight', r1, c1, r2, c2, cellValue)
	},
}

let currentSheetIndex: number = 0

let sheets = toRaw(props.sheets)
const progressBarRef = ref()
const refs = ref({
	enableProtect: false,
	showRangePwd: false,
	lockRangePwd: '',
	header: [{r: 0, c: 0, rr: 0, cc: 0}],

	modificData: [] as any[],
	modificDataOnSave: [] as any[],

	savingMessage: '正在保存...',
	height: '100%',
	width: '100%',

	completed: false,
	isImporting: false,
	saving: false,
	isRefresh: true,
	isModific: false,
	isDragToCell: false,
	isFirstLoad: true,
})

let times: any = 0
let cleanTimer: any = null
let addRowsInfo: any = null
let openCellList: any = []

const timer = (timestamp: any) => {
	if (!times) {
		times = timestamp
	}

	if (timestamp - times > props.interval) {
		if (
			props.enableAutoSave &&
			!refs.value.isImporting &&
			refs.value.isModific &&
			!refs.value.saving
		) {
			refs.value.saving = true
			refs.value.isModific = false

			if (refs.value.modificData.length > 0) {
				const data = _filterModificData()
				if (data.length > 0) {
					emits('onAutoSave', $getData())

					refs.value.modificData = []
				}
			}
		}
		times = timestamp
	}
	cleanTimer = requestAnimationFrame(timer)
}

const init = () => {
	if (!sheets?.length) return

	console.log('xlsx-plus init...')

	_.workbook = JSON.parse(JSON.stringify(__xls_config['plus']), (key, value) => {
		return key === 'hook' ? hook : value
	})

	_.workbook.gridKey = id.value
	_.workbook.container = id.value
	_.workbook.showtoolbarConfig.mergeCell = props.enableMergeCell
	const headers: any = []
	_.workbook.data = sheets.map((sheet: any, index: number) => {
		const _sheet = JSON.parse(JSON.stringify(_.workbook.data[0]))

		const head = toRaw(sheet.head || [])
		const data = toRaw(sheet.data || [])

		_sheet.name = sheet.name
		_sheet.celldata = [...head, ...data]
		_sheet.color = util.getRandomColor()
		_sheet.index = index
		_sheet.order = index
		_sheet.status = 0

		if (head.length) {
			const rows = _sheet.celldata.length / (head[head.length - 1]?.c + 1) - 30
			_sheet.row = rows < 100 ? 100 : rows

			const rs = head.map((item: any) => item.r)
			const cs = head.map((item: any) => item.c)
			const r = Math.min(...rs)
			const rr = Math.max(...rs)
			const c = Math.min(...cs)
			const cc = Math.max(...cs)

			headers.push({r, rr, c, cc})
		}

		if (sheet.config) {
			_sheet.config = {...sheet.config}
		}

		if (props.viewonly) {
			for (let key in _.workbook.showtoolbarConfig) {
				_.workbook.showtoolbarConfig[key] = false
			}

			for (let key in _.workbook.cellRightClickConfig) {
				_.workbook.cellRightClickConfig[key] = false
			}
			_sheet.config.authority.hintText = '报表已被锁定不允许修改'
		}

		return _sheet
	})
	refs.value.header = headers

	console.log('xlsx-plus sheets:', _.workbook.data)

	if ('luckysheet' in window) {
		_.lucky = util._deepCopy(window.luckysheet)
		_.lucky.create(_.workbook)

		let timer = setTimeout(() => {
			if (props.enableFreezeHeader && refs.value.header[currentSheetIndex]) {
				const merge = _.workbook.data[currentSheetIndex].config.merge
			}
			clearTimeout(timer)
		}, 200)
	}

	cancelAnimationFrame(cleanTimer)
	requestAnimationFrame(timer)

	refs.value.completed = true
	refs.value.isFirstLoad = true
}

const getColName = (colIndex: number) => {
	let colName = ''
	while (colIndex >= 0) {
		let remainder = colIndex % 26
		colName = String.fromCharCode(65 + remainder) + colName
		colIndex = Math.floor(colIndex / 26) - 1
	}
	return colName
}

const clearAllSheetRange = () => {
	_.lucky.getAllSheets().forEach((sheet: any, i: number) => {
		_.lucky.setSheetActive(i)
		_.lucky.deleteRow(refs.value.header[i].rr + 1, sheet.data.length - 1)
	})
	_.lucky.insertRow(refs.value.header[currentSheetIndex].rr + 1, {number: 100})
	_.lucky.setRangeShow('A1:Z1')
	_.lucky.scroll({targetRow: 0})
}

const openFile = () => {
	const file: HTMLElement | null = document.querySelector(`#${id.value}File`)
	if (file) file.click()
}

const onImportFromExcel = (file: any) => {
	let xl: any = new XlsxPlus({file, batch: 20, source: _.workbook, header: refs.value.header})

	xl.onBeginBefore = () => {
		progressBarRef.value.update('校验数据...', 1)
		emits('onImportBefore')
	}

	xl.onBegin = () => {
		times = null
		cancelAnimationFrame(cleanTimer)
		clearAllSheetRange()
		refs.value.isImporting = true
	}

	xl.onProgress = ({data, progress, range, order, maxr}: any) => {
		progressBarRef.value.update(`${range.row[1] + 1}/${maxr}`, progress)
		console.log('xlsx-plus onProgress:', progress)

		const rl = _.lucky.getAllSheets()[order].data.length
		_.lucky.setSheetActive(order)
		if (rl < maxr) {
			const nr = Math.ceil((maxr - rl) / 100)
			for (let i = 0; i < nr; i++) {
				_.lucky.insertRow(rl, {
					number: i === nr - 1 ? maxr - 100 * i - rl : 100,
					order,
				})
			}
		}

		_.lucky.setRangeValue(data, {
			range,
			order,
		})
		_.lucky.scroll({targetRow: range.row[1] - 15})
	}

	xl.onEnd = ({merges, mergeModel}: any) => {
		console.log('xlsx-plus onEnd')

		// console.log(merges)
		const ml = merges.length
		for (let i = 0; i < ml; i++) {
			if (merges[i]) {
				_.lucky.setRangeMerge('all', {range: merges[i]})
			}
			// 对象中的key为r + '_' + c的拼接值，value为左上角单元格信息: r:行数，c:列数，rs：合并的行数，cs:合并的列数
		}

		const model: any = {}
		mergeModel.forEach((m: any) => {
			model[`${m.top - 1}_${m.left - 1}`] = {
				r: m.top - 1,
				c: m.left - 1,
				rs: m.bottom - m.top + 1,
				cs: m.right - m.left + 1,
			}
		})

		_.workbook.data[currentSheetIndex].config.merge = Object.assign(
			_.workbook.data[currentSheetIndex].config.merge,
			model
		)

		progressBarRef.value.update('处理中...', 100)
	}

	xl.onError = (msg: any) => {
		console.log('xlsx-plus onError:', msg)
		ElMessage.error(msg)
	}

	xl.onFinish = (verify: boolean) => {
		console.log('xlsx-plus onFinish')
		emits('onImportFinish')

		const input: any = document.querySelector(`#${id.value}File`)
		if (input) {
			input.value = ''
		}

		progressBarRef.value.update('导入完成...', 0)
		currentSheetIndex = 0

		const data = _.lucky.getAllSheets()[currentSheetIndex].data
		_.lucky.setSheetActive(currentSheetIndex)
		_.lucky.setRangeShow({
			row: [data.length - 1, data.length - 1],
			column: [0, data[0].length - 1],
		})

		cleanTimer = requestAnimationFrame(timer)

		if (verify) {
			_toModificData(
				refs.value.header[currentSheetIndex].rr + 1,
				data.length - 1,
				data,
				props.interval
			)
			let timer = setTimeout(() => {
				refs.value.isImporting = false
				clearTimeout(timer)
			}, 1)
		}
	}
	xl.import()
	xl = null
}

const _setRangeVaule = (range: any, format: any, order?: number) => {
	if (range === '$A$0:$B$0' || range === 'A0:Z0') return
	const newCells = _.lucky
		.getRangeValue({range, order: order || currentSheetIndex})
		.map((cells: any) => cells.map((cell: any) => (cell ? {...cell, ...format} : format)))
	_.lucky.setRangeValue(newCells, {range, order: order || currentSheetIndex})
}

const _filterModificData = () => {
	const raw = refs.value.modificData.map(toRaw).flat()
	const filter: any = {}
	for (const element of raw) {
		const row = element
		if (row) {
			const key = row[0].r
			filter[key] = row
		}
	}

	return Object.values(filter).map((row: any) => row.filter((cell: any) => cell))
}

const _toModificData = async (startRow: number, endRow: number, data: any, ms: number = 0) => {
	const cellData = _.lucky.transToCellData(data.slice(startRow, endRow + 1))

	const result: any = {}

	for (const element of cellData) {
		const cell = element
		cell.r += startRow
		result[cell.r] = result[cell.r] ? [...result[cell.r], cell] : [cell]
	}

	if (!refs.value.saving) {
		refs.value.modificData.push(Object.values(result))
	} else {
		refs.value.modificDataOnSave.push(Object.values(result))
	}

	times = ms

	refs.value.isModific = true
}

const setLockRangePwd = () => {
	refs.value.showRangePwd = false
	ElMessage.success('已开启锁定')
}

const headerTemporary: any = {}
const onSpecifiedHeader = () => {
	const curRange = _.lucky.getRange()[0]
	const {row, column} = curRange
	if (!headerTemporary[_.workbook.data[currentSheetIndex].name]) {
		headerTemporary[_.workbook.data[currentSheetIndex].name] = {
			[currentSheetIndex]: {
				ranges: [],
				celldata: [],
			},
		}
	}
	let {ranges, celldata} =
		headerTemporary[_.workbook.data[currentSheetIndex].name][currentSheetIndex]

	const isExist = ranges.some((item: any) => {
		const [r, rr] = item.row
		const [c, cc] = item.column
		const msg = '当前范围已在表头区域内'

		if (row[0] >= r && row[1] <= rr && column[0] >= c && column[1] <= cc) {
			ElMessage.warning(msg)
			return true
		}

		if (row[0] >= r && row[0] <= rr) {
			if (column[0] >= c && column[0] <= cc) {
				ElMessage.warning(msg)
				return true
			}
			if (column[1] >= c && column[1] <= cc) {
				ElMessage.warning(msg)
				return true
			}
		}

		if (row[1] >= r && row[1] <= rr) {
			if (column[0] >= c && column[0] <= cc) {
				ElMessage.warning(msg)
				return true
			}
			if (column[1] >= c && column[1] <= cc) {
				ElMessage.warning(msg)
				return true
			}
		}
		return false
	})

	if (!isExist) {
		const temporaryVerify = _.lucky.getRangeValue({range: curRange})
		for (let i = 0; i < temporaryVerify.length; i++) {
			console.log(temporaryVerify[i])

			const cannotSave = temporaryVerify[i].some(
				(item: any) => item === null || item?.v === null
			)
			if (cannotSave) {
				ElMessage.warning('当前单元格无数据, 无法锁定表头')
				return
			}
		}

		_setRangeVaule(curRange, {bg: '#eee'})
		const rangeValues = _.lucky.getRangeValue({range: curRange})
		const cd = _.lucky.transToCellData(rangeValues)
		cd.forEach((item: any) => {
			item.r += row[0]
			item.c += column[0]
		})

		celldata.push(...cd)
		ranges.push(curRange)
	}

	console.log('xlsx-plus headerTemporary', headerTemporary)

	const ar = celldata.map((item: any) => item.r)
	const ac = celldata.map((item: any) => item.c)
	const header = refs.value.header[currentSheetIndex] || {}
	header.r = Math.min(...ar)
	header.c = Math.min(...ac)
	header.rr = Math.max(...ar)
	header.cc = Math.max(...ac)
	refs.value.header[currentSheetIndex] = header

	console.log('xlsx-plus refs.value.header', refs.value.header)
}

const onRresetSpecifiedheader = () => {
	const {ranges, celldata} =
		headerTemporary[_.workbook.data[currentSheetIndex].name][currentSheetIndex]

	if (ranges.length > 0) {
		ranges.forEach((item: any) => {
			_setRangeVaule(item, {bg: null})
		})
		ranges.length = 0
		celldata.length = 0
	}

	const authority = _.workbook.data[currentSheetIndex].config.authority
	const allowRangeList = authority.allowRangeList
	authority.sheet = 0
	if (allowRangeList[0].name === 'NotEditableDiy') {
		allowRangeList?.shift()
	}
	refs.value.header[currentSheetIndex] = {r: -1, c: -1, rr: -1, cc: -1}
}

const _lockHeaderHandler = () => {
	if (props.enableProtect) {
		const headerData =
			headerTemporary[_.workbook.data[currentSheetIndex].name]?.[currentSheetIndex]
		if (headerData && headerData.ranges.length > 0) {
			const authority = _.workbook.data[currentSheetIndex].config.authority
			const allowRangeList = authority.allowRangeList
			authority.sheet = 1
			headerData.ranges.forEach((f: any) => {
				_.lucky.setRangeShow(f)
				allowRangeList.unshift({
					sqref: _.lucky.getRangeAxis({range: f})[0],
					password: util._guid(),
					name: 'NotEditableDiy',
					hintText: '单元格不可编辑!',
					algorithmName: 'None',
					saltValue: null,
				})
			})
		}
	}
}

const onSave = () => {
	_lockHeaderHandler()
	emits('onSave', $getData())
}

const insertRow = (curdata: any) => {
	const {rr, rc, rt} = addRowsInfo
	const start = rt === 'rightbottom' ? rr + 1 : rr
	const end = rt === 'rightbottom' ? rr + rc + 1 : rr + rc
	let rows = []
	rows = curdata.slice(start, end)
	for (let i = 0; i < rows.length; i++) {
		rows[i][0] = {
			r: start + i,
			c: 0,
			v: {
				ct: {fa: 'General', t: 'g'},
				m: '',
				v: '',
			},
		}
	}
	emits(
		'onAddRow',
		rows.map((row: any) => row.filter(Boolean)),
		_.workbook.data[currentSheetIndex].config
	)
	addRowsInfo = null
}

const deleteRow = (range: Array<number>) => {
	emits(
		'onDeleteRow',
		range,
		refs.value.header[currentSheetIndex]?.rr + 1,
		_.workbook.data[currentSheetIndex].config
	)
}

const onDragToCell = (cell: any, position: any) => {
	const header = refs.value.header[currentSheetIndex]
	const {r, c} = position

	_.lucky.setRangeShow({row: [r, r], column: [c, c]})
	const axis = _.lucky.getRangeAxis().join()

	if (props.enableLinked) {
		let inHeader = r >= header.r && r <= header.rr && c >= header.c && c <= header.cc

		refs.value.isDragToCell = true
		let timer = setTimeout(() => {
			refs.value.isDragToCell = false
			clearTimeout(timer)
		}, 16.7)
		emits('onDragToCell', axis, position, inHeader)
	}
}

onMounted(() => {
	console.log('xlsx-plus onMounted...')
	id.value = 'Sheet' + Math.random().toString(36).slice(2)
	refs.value.height = props.showHeader ? 'calc(100% - 40px)' : '100%'
	refs.value.width = props.showAside ? 'calc(100% - 250px)' : '100%'
	// nextTick(() => setTimeout(() => init(), 600))
})

onActivated(() => {
	console.log('xlsx-plus onActivated...')
})

onUnmounted(() => {
	console.log('xlsx-plus onUnmounted...')
	cancelAnimationFrame(cleanTimer)

	if (_.lucky.destroy) {
		console.log('xlsx-plus destroy...')
		_.workbook = null
		_.lucky.destroy()
		_.lucky = null
		id.value = ''
	}
})

onDeactivated(() => {
	console.log('xlsx-plus onDeactivated...')
})

const $fixCellIndex = (cells: Array<any>, index: number): any[] =>
	cells.map((c: any) => ({...c, r: index}))

const $getData = (): Array<any> => {
	const result: any = []
	_.workbook.data.forEach((d: any, i: number) => {
		const {r, rr, c, cc} = refs.value.header?.[i] || {r: -1, rr: -1, c: -1, cc: -1}
		const celldata = _.lucky.transToCellData(d.data)

		const head = celldata.filter((r: any) => r.r <= rr)

		const data = props.isAlien
			? celldata.filter((v) => !v.v?.bg || v.v?.bg !== '#eee')
			: celldata.filter((r: any) => r.r > rr)

		const rawData = data.reduce((prev: any, cur: any) => {
			const index = prev.findIndex((p: any) => p[0].r === cur.r)
			if (index > -1) {
				prev[index].push(cur)
			} else {
				prev.push([cur])
			}
			return prev
		}, [])

		console.log('Sheet $getData Head', head)
		console.log('Sheet $getData Data', data)
		console.log('Sheet $getData RawData', rawData)

		result.push(
			// sheet
			{
				name: d.name,
				head,
				headRange: toRaw(refs.value.header[i]),
				data,
				config: d.config,
				rawData,
			}
		)
	})
	return result
}

const $isLock = (i?: number) => {
	if (i !== undefined) {
		if (i >= _.workbook.data.length) {
			throw new Error('Sheet index out of range!!')
		}
		return _.workbook.data[i].config.authority.sheet
	}

	return _.workbook.data.every((e: any) => e.config.authority.sheet)
}

const $getRangeAxis = (range: {row: []; column: []}) => {
	_.lucky.setRangeShow(range)
	return _.lucky.getRangeAxis(range)
}

const $setCellValue = (r: number, c: number, v: {}, isRefresh: boolean = true) => {
	refs.value.isRefresh = isRefresh
	_.lucky.setCellValue(r, c, v)
	let timer = setTimeout(() => {
		refs.value.isRefresh = true
		clearTimeout(timer)
	}, 16.7)
}

const $setData = async (data: Array<any>, linked: Array<any>, fn?: Function) => {
	clearAllSheetRange()

	refs.value.isImporting = true
	progressBarRef.value.update('正在准备数据, 请稍等...', 1)
	await util.sleep(16.7)

	const lkdRange = linked.map((m: any) => m.__columnRaw[0])
	const lc = Math.min(...lkdRange.map(({column}) => column[0]))
	const lcc = Math.max(...lkdRange.map(({column}) => column[1]))

	const {r, rr, c, cc} = refs.value.header[currentSheetIndex]
	const count = data.length / linked.length
	const rc = count + rr + 1
	const batch = Math.ceil(data.length / 100)
	const size = Math.ceil(count / batch)

	const rows = _.lucky.transToData(data).slice(rr)

	const rl = _.lucky.getAllSheets()[currentSheetIndex].data.length
	if (rl < rc) {
		const nr = Math.ceil((rc - rl) / 100)
		for (let i = 0; i < nr; i++) {
			_.lucky.insertRow(rl, {
				number: i === nr - 1 ? rc - 100 * i - rl : 100,
				currentSheetIndex,
			})
		}
	}

	let loop: any = (i: number) => {
		if (i >= size) {
			progressBarRef.value.update('数据填充完成', 100)
			let curdata = _.lucky.getAllSheets()[currentSheetIndex].data
			const length = curdata.length

			_toModificData(refs.value.header[currentSheetIndex].rr, length - 1, curdata)
			let timer = setTimeout(() => {
				progressBarRef.value.update('-', 0)
				refs.value.isImporting = false
				_.lucky.setRangeShow({row: [length - 1, length - 1], column: [0, 0]})
				clearTimeout(timer)
			}, 1)
			fn && fn()
			loop = null
			return
		}

		let start = i * batch
		let end = start + batch

		if (end > count) {
			end = count
		}

		const d = rows.slice(start, end)
		const worker = new Worker()

		if (d[0] && d[0].length > 0) {
			worker.postMessage({
				rows: d,
				minColumn: lc,
				maxColumn: lcc,
				range: {
					row: [start + rr + 1, end + rr],
					column: [lc, lcc],
				},
				index: i,
			})

			worker.onmessage = async (e) => {
				console.log('xlsx-plus worker onmessage:', e.data)
				progressBarRef.value.update(
					'正在填充数据, 请稍等...',
					Math.ceil(((e.data.index + 1) / size) * 100)
				)
				_.lucky.setRangeValue(e.data.result, {
					range: e.data.range,
					success: () => {
						_.lucky.scroll({targetRow: e.data.range.row[1] - 5})
						worker.terminate()
						loop(i + 1)
					},
				})
			}
		}
	}
	loop(0)
}

defineExpose({
	lucky: () => _.lucky,
	$resize() {
		nextTick(() => {
			console.log('xlsx-plus resize...', _, _.lucky)
			try {
				_?.lucky?.resize()
			} catch (e) {}
		})
	},
	$reload() {
		console.log('xlsx-plus reload...')
		nextTick(() => {
			refs.value.completed = false
			init()
		})
	},
	$clear() {
		clearAllSheetRange()
	},
	$isLock,
	$fixCellIndex,

	$getData,
	$getRangeAxis,
	$getHeaderInfo: () => refs.value.header[currentSheetIndex],

	$setData,
	$setCellValue,

	$saveDone: () => {
		times = 0
		if (refs.value.modificDataOnSave.length > 0) {
			refs.value.modificData = refs.value.modificDataOnSave
			refs.value.modificDataOnSave = []
		}
		refs.value.saving = false
		refs.value.savingMessage = '保存完成'
		let timer = setTimeout(() => {
			refs.value.savingMessage = ''
			clearTimeout(timer)
		}, 3000)
	},
})
</script>
<template>
	<div class="xlsx-plus" v-show="refs.completed">
		<div class="header" v-if="props.showHeader">
			<div v-if="props.viewonly" style="align-items: center; display: flex">
				<el-icon mr-3px style="color: #f00"><WarningFilled /></el-icon>
				当前表仅供查看
			</div>
			<div class="slot" v-if="!props.viewonly">
				<slot name="headerLeft"></slot>
			</div>
			<div class="slot" v-if="!props.viewonly">
				<el-button
					v-if="props.enableSave"
					type="primary"
					size="small"
					class="unoicon"
					@click="onSave"
					title="保存"
				>
					<i i-majesticons-save-line></i>
					<span ml-5px>保存</span>
				</el-button>
				<slot name="headerCenter"></slot>

				<el-button
					v-if="props.enableImport && !props.isAlien"
					type="primary"
					size="small"
					class="input-file unoicon"
					@click="openFile"
					title="选择Excel文件"
				>
					<i i-majesticons-cloud-upload-line></i>
					<input
						:id="`${id}File`"
						class="file"
						type="file"
						accept=".xlsx"
						@change="onImportFromExcel(($event.target as any)?.files[0])"
					/>
					<span ml-5px>选择</span>
				</el-button>

				<!-- 启用保护 -->
				<template v-if="props.enableProtect">
					<el-button type="primary" size="small" @click="onSpecifiedHeader"
						>指定表头</el-button
					>
					<el-button type="primary" size="small" @click="onRresetSpecifiedheader"
						>重置</el-button
					>
				</template>
				<slot name="headerButtonRight"></slot>
			</div>

			<div class="slot">
				<slot name="headerRight"></slot>
			</div>
		</div>

		<div v-if="showAside" class="aside">
			<slot name="aside">-aside</slot>
		</div>
		<div :id="id" class="sheet-main" :style="{height: refs.height, width: refs.width}"></div>

		<!-- 进度组件 -->
		<progress-bar ref="progressBarRef"></progress-bar>

		<!-- 设置锁区密码 -->
		<DialogComp
			:closeOnClickModal="false"
			:closeOnPressEscape="false"
			:showClose="false"
			:visibleFooterButton="false"
			:visible="refs.showRangePwd"
			title="设置锁区密码"
			width="300"
		>
			<template #body>
				<el-input
					type="password"
					v-model="refs.lockRangePwd"
					placeholder="默认密码:123456"
				></el-input>
			</template>
			<template #footer>
				<el-button type="primary" @click="setLockRangePwd">确认</el-button>
			</template>
		</DialogComp>

		<!-- 自动保存提示 -->
		<div
			class="auto-save"
			:class="{on: refs.saving || refs.savingMessage === '保存完成'}"
			:style="{left: props.showAside ? '250px' : '1px'}"
		>
			<template v-if="refs.savingMessage === '正在保存...'">
				<el-button type="primary" loading>{{ refs.savingMessage }}</el-button>
			</template>
			<template v-else>
				<el-button type="success">
					<el-icon mr-10px> <Check /> </el-icon>{{ refs.savingMessage }}
				</el-button>
			</template>
		</div>
	</div>
</template>
