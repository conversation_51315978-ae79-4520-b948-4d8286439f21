<script setup lang="ts">
import { onMounted } from 'vue'

function getRandomInt(min:any, max:any) {
  return Math.floor(Math.random() * (max - min)) + min
}
function particlesInit() {
  const generator = document.getElementById('particleGenerator')
  const particleCount = 200
  for (let i = 0; i < particleCount; i++) {
    const size = getRandomInt(2, 6)
    const n = `<div class="particle" style="z-index:3;top:${getRandomInt(15, 95)}%; left:${getRandomInt(5, 95)}%; width:${size}px; height:${size}px; animation-delay:${getRandomInt(0, 30) / 10}s; background-color:rgba(${getRandomInt(80, 160)},${getRandomInt(185, 255)},${getRandomInt(160, 255)},${getRandomInt(2, 8) / 10});"></div>`
    const node = document.createElement('div')
    node.innerHTML = n
    generator!.appendChild(node)
  }
}
onMounted(() => {
  particlesInit()
})
</script>

<template>
  <div id="particleGenerator" w-full h-full />
</template>

<style>
#particleGenerator {
  pointer-events: none;
}

.particle {
  opacity: 0;
  position: absolute;
  background-color: rgba(255, 255, 255, 0.5);
  -webkit-animation: particleAnim 3s ease-in-out infinite;
  animation: particleAnim 3s ease-in-out infinite;
  border-radius: 100%;
}

@-webkit-keyframes particleAnim {
  0% {
    opacity: 0;
    transform: translateY(-0%);
  }

  15% {
    opacity: 1;
  }

  100% {
    opacity: 0;
    transform: translateY(-1500%);
  }
}

@keyframes particleAnim {
  0% {
    opacity: 0;
    transform: translateY(-0%);
  }

  25% {
    opacity: 1;
  }

  100% {
    opacity: 0;
    transform: translateY(-1500%);
  }
}
</style>
