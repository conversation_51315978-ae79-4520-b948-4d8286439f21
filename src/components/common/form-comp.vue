<script setup lang="ts">
import {FormInstance} from 'element-plus'
import {onMounted, ref, watch, computed} from 'vue'
import departmentFavoriteComp from './department-favorite-comp.vue'
interface Props {
	form: Array<{
		type: string
		datetype: string
		field: string
		title: string
		default?: string | [] | boolean | number | null
		disabled?: boolean
		full?: boolean
		multiple?: boolean
		middel?: boolean
		placeholder?: string
		showPassword?: boolean
		showCheckBox?: boolean
		filterable?: boolean
		data?: any
		raw?: any
	}>
	size?: string
	rules?: {}
	inline?: boolean
	showResetButton?: boolean
	clearClick?:boolean
	showButton?: boolean
	submitIcon?: string
	submitText?: string
	width?: string
	loading?: boolean
	labelWidth?: string
	labelPosition?: string
	enableEnterSearch?: boolean
	conditionList?: Array<any>
	itemWidth?: any
}

const props = withDefaults(defineProps<Props>(), {
	size: 'large',
	inline: false,
	showButton: true,
	showResetButton: true,
	clearClick:true,
	submitText: '提交',
	submitIcon: 'i-ic-baseline-send',
	width: '100%',
	loading: false,
	labelWidth: '80',
	labelPosition: 'top',
	enableEnterSearch: true,
	conditionList: [] as any,
	itemWidth: '100%',
})

console.log('props.conditionList', props.conditionList)

const emtis = defineEmits(['onChange', 'onSubmit', 'onClean', 'enter'])

interface Form {
	[key: string]: any
}

// 表单配置
let formArray = ref(props.form)
console.log('form234', props.form)

const formItemRows = computed(() => {
	return formArray.value?.length || 0
})

const needFixedButtons = computed(() => {
	return formItemRows.value > 4
})

// 表单 ref
const formComp = ref<FormInstance>()

// 表单对象
let form = ref({} as Form)
let isFocus = ref(false)
let formCondition = ref({} as any)

watch(
	() => formCondition,
	(val) => {
		console.log('formCondition', val)
	},
	{deep: true}
)

watch(
	() => props.form,
	(newVal) => {
		formArray.value = newVal
		console.log('formArray233', newVal)
		// onDefault()
	},
	{deep: true}
)

const onDefault = (isClean: boolean = false) => {
	console.log('onDefault333', form)

	// 设置默认值 或 重置 或 清空
	formArray.value?.forEach((item) => {
		// console.log(typeof item.default)

		// 只清空重置非禁用的
		let __defalut: string | [] | boolean | number | null = isClean ? null : item.default ?? ''

		if (
			item.datetype === 'days' ||
			item.datetype === 'months' ||
			item.type === 'checkbox' ||
			item.multiple ||
			(item.type === 'string' && item.raw.multiple)
		) {
			__defalut = isClean ? [] : item.default ?? []
		}
		console.log(item.type)

		if (item.type === 'int') {
			__defalut = isClean ? null : item.default ?? null
		}
		console.log('__defalut233', __defalut)
		form.value[item.field] = __defalut ?? null

		console.log(form.value[item.field])

		// 如果是disabled字段不做任何清空重置操作
		if (item.disabled) {
			form.value[item.field] = item.default
		}
	})
	console.log(form.value)
}
onDefault()

const onResetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
}

const onCleanForm = () => {
	onDefault(true)
	emtis('onClean')
}
const closeForm=()=>{
    emtis('closeClick')
}

const onClearFormCondition = () => {
	console.log('子组件的formCondition', formCondition)
	formCondition.value = {}
}

const onChangeForm = (val: any, field: string, type?: string, raws?: any) => {
	if (Array.isArray(val)) {
		form.value[field] = val.filter(Boolean)
	}
	// debugger
	console.log('输入了222', form)
	emtis('onChange', val, field, type, raws)
}

const onSubmitForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	emtis('onSubmit', form.value, formEl, formCondition.value)
}

onMounted(() => {})
defineExpose({
	getElFormRef: () => formComp.value,
	getForm: () => JSON.parse(JSON.stringify(form.value)),
	setValue: (field: string, value: any) => {
		console.log('Set Form Value: ', field, value, form.value)
		form.value[field] = value
	},
	getValue: (key: string) => form.value[key],
	clear: () => {
		onCleanForm()
	},
	clearFormCondition: () => onClearFormCondition(),
	formCondition: () => formCondition.value,
	form: () => form.value,
})
const cardType = {
	identification_number: '居民身份证号',
	passport: '护照号',
	hk_macao_permit: '港澳居民来往内地通行证',
	taiwan_permit: '台湾居民来往内地通行证',
}

function onChangeSel(val: any, item: any, formItem: any) {
	console.log('e', val, item, form)
	if (val === '为空' || 0) {
		const keyVal = form.value[item.field]

		if (typeof keyVal == 'number') {
			form.value[item.field] = null
		} else if (typeof keyVal == 'string') {
			form.value[item.field] = ''
		} else if (Array.isArray(form.value[item.field])) {
			form.value[item.field] = []
		}
	}
	// debugger
	if (val === '为空') {
		formItem.disabled = true
	} else {
		formItem.disabled = false
	}
}
</script>
<template>
	<el-form
		ref="formComp"
		v-bind="$attrs"
		:model="form"
		:rules="rules"
		:inline="inline"
		:label-position="labelPosition"
		:label-width="labelWidth"
		class="form-comp"
		:class="{'form-comp-scrollable': needFixedButtons}"
		:style="{width: width}"
	>
		<div :class="{'form-items-container': needFixedButtons}">
			<el-form-item
				v-for="(item, index) of formArray"
				:key="index"
				:prop="item.field"
				:class="{full: item.full && item.data?.length >= 1, middel: item.middel}"
			>
				<template #label>
					<el-tooltip
						class="box-item"
						effect="dark"
						:content="item.title"
						placement="top-start"
					>
						<strong>{{ item.title }}:</strong>
					</el-tooltip>
				</template>

				<template #default>
					<div style="display: flex; align-items: center; width: 100%">
						<!-- 条件选择器部分 -->
						<div
							style=""
							v-if="
								conditionList.length &&
								conditionList[index] &&
								conditionList[index].options
							"
						>
							<slot name="condition">
								<el-select
									v-model="formCondition[item.field]"
									clearable
									placeholder="请选择"
									:style="{width: '86px !important', marginRight: '4px'}"
									v-if="
										conditionList.length &&
										conditionList[index] &&
										conditionList[index].options.length
									"
									@change="onChangeSel($event, conditionList[index], item)"
								>
									<el-option
										v-for="item2 in conditionList[index].options"
										:key="item2.value"
										:label="item2.label"
										:value="item2.value"
									/>
								</el-select>
							</slot>
						</div>

						<slot :name="[item.field]" :item="item">
							<div style="flex: 1">
								<!-- 文本 -->
								<el-input
									clearable
									v-if="
										item.type === 'input' ||
										item.type === 'string' ||
										[
											'identification_number',
											'disability_certificate_number',
											'email',
										].includes(item.type)
									"
									v-model="form[item.field]"
									size="default"
									:show-password="item.showPassword ?? false"
									:disabled="item.disabled"
									:placeholder="item.placeholder || `请输入${item.title}`"
									@change="onChangeForm($event, item.field)"
									@focus="isFocus = true"
									@blur="isFocus = false"
								/>
								<el-input
									v-mousewheel
									v-if="item.type === 'decimal' || item.type === 'phone'"
									v-model="form[item.field]"
									size="default"
									type="number"
									controls-position="right"
									:disabled="item.disabled"
									:placeholder="item.placeholder || `请输入${item.title}`"
									@change="onChangeForm($event, item.field)"
									@focus="isFocus = true"
									@blur="isFocus = false"
								/>

								<!-- 数字 -->
								<el-input-number
									v-mousewheel
									style="width: 100%"
									v-if="item.type === 'int' || item.type === 'age'"
									v-model="form[item.field]"
									size="default"
									controls-position="right"
									:disabled="item.disabled"
									:placeholder="item.placeholder || `请输入${item.title}`"
									@change="onChangeForm($event, item.field)"
									@focus="isFocus = true"
									@blur="isFocus = false"
								/>

								<!-- 下拉选择 -->
								<el-select
									clearable
									v-else-if="item.type === 'select'"
									v-model="form[item.field]"
									size="default"
									:filterable="item.filterable || true"
									:disabled="item.disabled"
									:placeholder="item.placeholder || `请选择${item.title}`"
									:multiple="item.multiple"
									:collapse-tags="item.multiple"
									:collapse-tags-tooltip="item.multiple"
									@change="onChangeForm($event, item.field, item.type, item)"
									@focus="isFocus = true"
									@blur="isFocus = false"
									@keyup.enter="emtis('enter')"
								>
									<template v-if="item.data">
										<!-- {{itemWidth}} -->
										<el-option
											v-for="selectItem of item.data"
											:label="selectItem.label"
											:value="selectItem.value"
										/>
									</template>
								</el-select>
								<!-- 时间配置后的时间组件 -->
								<el-date-picker
									v-if="item.type === 'datetime' && item.raw.displayForm === 1"
									style="width: 100% !important"
									format="YYYY年MM月DD日"
									v-model="form[item.field]"
									type="daterange"
									value-format="YYYY年MM月DD日"
									:start-placeholder="item.placeholder || `请选择${item.title}`"
									:end-placeholder="item.placeholder || `请选择${item.title}`"
									:placeholder="item.placeholder || `请选择${item.title}`"
									@change="onChangeForm($event, item.field)"
									@focus="isFocus = true"
									@blur="isFocus = false"
									@keydown.enter="emtis('enter')"
								/>

								<el-date-picker
									v-if="item.type === 'datetime' && item.raw.displayForm === 2"
									style="width: 100% !important"
									format="YYYY年MM月"
									v-model="form[item.field]"
									type="daterange"
									value-format="YYYY年MM月"
									:start-placeholder="item.placeholder || `请选择${item.title}`"
									:end-placeholder="item.placeholder || `请选择${item.title}`"
									:placeholder="item.placeholder || `请选择${item.title}`"
									@change="onChangeForm($event, item.field)"
									@focus="isFocus = true"
									@blur="isFocus = false"
									@keydown.enter="emtis('enter')"
								/>

								<el-date-picker
									v-if="item.type === 'datetime' && item.raw.displayForm === 0"
									style="width: 100% !important"
									format="YYYY/MM/DD HH:mm:ss"
									v-model="form[item.field]"
									type="datetimerange"
									:start-placeholder="item.placeholder || `请选择${item.title}`"
									:end-placeholder="item.placeholder || `请选择${item.title}`"
									value-format="YYYY/MM/DD HH:mm:ss"
									:placeholder="item.placeholder || `请选择${item.title}`"
									@change="onChangeForm($event, item.field)"
									@focus="isFocus = true"
									@blur="isFocus = false"
									@keydown.enter="emtis('enter')"
								/>

								<el-date-picker
									v-if="item.type === 'datetime' && item.raw.displayForm === 3"
									style="width: 100% !important"
									format="YYYY年"
									v-model="form[item.field]"
									type="year"
									value-format="YYYY年"
									:placeholder="item.placeholder || `请选择${item.title}`"
									@change="onChangeForm($event, item.field)"
									@focus="isFocus = true"
									@blur="isFocus = false"
									@keydown.enter="emtis('enter')"
								/>

								<TimePicker
									v-if="item.type === 'datetime' && item.raw.displayForm === 4"
									ref="quarterPickerRef"
									v-model="form[item.field]"
									format="YYYY年第q季度"
									value-format="YYYY-MM-DD HH:mm:ss"
									@change="onChangeForm($event, item.field)"
								/>
								<!-- 单选框 -->
								<el-radio-group
									v-if="item.type === 'radio'"
									v-model="form[item.field]"
									:disabled="item.disabled"
									@change="onChangeForm($event, item.field)"
									@focus="isFocus = true"
									@blur="isFocus = false"
								>
									<template v-if="item.data">
										<el-radio
											border
											v-for="radio of item.data"
											:label="radio.value"
											:valule="radio.value"
										>
											{{ radio.label }}
										</el-radio>
									</template>
								</el-radio-group>

								<!-- 复选框 -->
								<el-checkbox-group
									v-if="item.type === 'checkbox'"
									v-model="form[item.field]"
									:disabled="item.disabled"
									@change="onChangeForm($event, item.field)"
								>
									<template v-if="item.data">
										<el-checkbox
											border
											v-for="checkbox of item.data"
											:label="checkbox.value"
											:name="`checkbox${index}`"
										>
											{{ checkbox.label }}
										</el-checkbox>
									</template>
								</el-checkbox-group>

								<!-- 文本域 -->
								<!-- <el-input v-if="item.type === 'textarea'" v-model="form[item.field]" type="textarea" :disabled="item.disabled" show-word-limit @change="onChangeForm($event, item.field)" /> -->

								<!-- 开关 -->
								<el-switch
									border
									v-if="item.type === 'switch'"
									v-model="form[item.field]"
									size="default"
									:disabled="item.disabled"
									@change="onChangeForm($event, item.field)"
								/>
								<el-date-picker
									style="width: 100% !important"
									clearable
									:size="size"
									type="date"
									format="YYYY-MM-DD"
									value-format="YYYY-MM-DD"
									v-if="item.type === 'birthday'"
									v-model="form[item.field]"
									:disabled="item.disabled"
									:placeholder="item.placeholder || `请选择${item.title}`"
									@change="onChangeForm($event, item.field)"
								/>
								<!-- 文本域 -->
								<el-input
									v-if="item.type === 'textarea'"
									v-model="form[item.field]"
									type="textarea"
									:placeholder="item.placeholder || `请选择${item.title}`"
									:disabled="item.disabled"
									show-word-limit
									@change="onChangeForm($event, item.field)"
									@focus="isFocus = true"
									@blur="isFocus = false"
								/>

								<!-- 开关 -->
								<el-switch
									border
									v-if="item.type === 'switch'"
									v-model="form[item.field]"
									size="default"
									:disabled="item.disabled"
									@change="onChangeForm($event, item.field)"
								/>

								<!-- 单天年月日时分秒 -->
								<el-date-picker
									style="width: 100% !important"
									v-if="
										item.type === 'datetime' &&
										item.datetype === 'time' &&
										!item.raw.displayForm &&
										item.raw.displayForm !== 0
									"
									v-model="form[item.field]"
									type="datetime"
									value-format="YYYY-MM-DD HH:mm:ss"
									:placeholder="item.placeholder || `请选择${item.title}`"
									@change="onChangeForm($event, item.field)"
									@focus="isFocus = true"
									@blur="isFocus = false"
									@keydown.enter="emtis('enter')"
								/>

								<!-- <div style="width: 70%; display: inline-block;"> -->
								<el-date-picker
									style="width: 100% !important"
									v-if="
										item.type === 'datetime' &&
										item.datetype === 'times' &&
										!item.raw.displayForm &&
										item.raw.displayForm !== 0
									"
									v-model="form[item.field]"
									type="datetimerange"
									value-format="YYYY-MM-DD HH:mm:ss"
									:placeholder="item.placeholder || `请选择${item.title}`"
									:start-placeholder="item.placeholder || `请选择${item.title}`"
									:end-placeholder="item.placeholder || `请选择${item.title}`"
									@change="onChangeForm($event, item.field)"
									@focus="isFocus = true"
									@blur="isFocus = false"
									@keydown.enter="emtis('enter')"
								/>
								<!-- </div> -->

								<!-- 时间: 单天 -->
								<el-date-picker
									style="width: 100% !important"
									clearable
									size="default"
									type="date"
									value-format="YYYY-MM-DD"
									v-if="item.type === 'date' && item.datetype === 'day'"
									v-model="form[item.field]"
									:disabled="item.disabled"
									:placeholder="item.placeholder || `请选择${item.title}`"
									@change="onChangeForm($event, item.field)"
									@focus="isFocus = true"
									@blur="isFocus = false"
									@keydown.enter="emtis('enter')"
								/>

								<!-- 时间: 天范围 -->
								<el-date-picker
									style="width: 100% !important"
									clearable
									size="default"
									type="daterange"
									value-format="YYYY-MM-DD"
									v-if="item.type === 'date' && item.datetype === 'days'"
									v-model="form[item.field]"
									:disabled="item.disabled"
									:placeholder="item.placeholder || `请选择${item.title}`"
									@change="onChangeForm($event, item.field)"
									@focus="isFocus = true"
									@blur="isFocus = false"
									@keydown.enter="emtis('enter')"
								/>
								<!-- 证件号 -->
								<el-select
									clearable
									v-if="item.type === 'certificate'"
									v-model="form[item.field]"
									:size="size"
									:disabled="item.disabled"
									:placeholder="item.placeholder || `请选择${item.title}`"
									:filterable="item.filterable ?? true"
									:multiple="item.multiple"
									@change="onChangeForm($event, item.field, item.type, formComp)"
								>
									<template v-if="item.raw.customValueOptions">
										<el-option
											v-for="selectItem of item.raw.customValueOptions.map((it:any)=> {
																	return { label :cardType[it.name], value:it.name}
																})"
											:label="selectItem.label || selectItem.value"
											:value="selectItem.value"
										/>
									</template>
								</el-select>
								<!-- 时间: 月 -->
								<el-date-picker
									style="width: 100% !important"
									clearable
									size="default"
									type="month"
									value-format="YYYY-MM-DD"
									v-if="item.type === 'date' && item.datetype === 'month'"
									v-model="form[item.field]"
									:disabled="item.disabled"
									:placeholder="item.placeholder || `请选择${item.title}`"
									@change="onChangeForm($event, item.field)"
									@focus="isFocus = true"
									@blur="isFocus = false"
									@keydown.enter="emtis('enter')"
								/>

								<!-- 时间: 月范围 -->
								<el-date-picker
									style="width: 100% !important"
									clearable
									size="default"
									type="monthrange"
									value-format="YYYY-MM-DD"
									v-if="item.type === 'date' && item.datetype === 'months'"
									v-model="form[item.field]"
									:disabled="item.disabled"
									:placeholder="item.placeholder || `请选择${item.title}`"
									@change="onChangeForm($event, item.field)"
									@focus="isFocus = true"
									@blur="isFocus = false"
									@keydown.enter="emtis('enter')"
								/>

								<el-tree-select
									v-if="item.type === 'treeSelect'"
									v-model="form[item.field]"
									:data="item.data"
									:render-after-expand="false"
									:show-checkbox="item.showCheckBox ?? false"
									:multiple="item.showCheckBox ?? false"
									check-strictly
									:filterable="item.filterable ?? false"
									@change="onChangeForm($event, item.field)"
									@focus="isFocus = true"
									@blur="isFocus = false"
									@keydown.enter="emtis('enter')"
									w-full
								/>

								<departmentFavoriteComp
									v-if="item.type === 'departmentSelect'"
									placeholder="请选择"
									:default-checked-data="form[item.field]"
									:disabled="item.disabled"
									:data="item.data"
									:type="'modal'"
									@change="onChangeForm($event, item.field)"
								></departmentFavoriteComp>
							</div>
						</slot>
					</div>
				</template>
			</el-form-item>
		</div>

		<el-form-item
			:class="[{full: inline}, {'fixed-buttons': needFixedButtons}]"
			v-if="showButton"
		>
			<div :class="{'flex flex-justify-center w-full': inline}" class="form-comp-btns">
				<el-button @click="onResetForm(formComp)" v-if="showResetButton">
					<Icons name="Reset" color="var(--z-font-color)"></Icons>
					重置
				</el-button>
				<el-button @click="closeForm" v-if='!clearClick'>
					<el-icon><Close /></el-icon>
					取消
				</el-button>
				<el-button @click="onCleanForm" v-if='clearClick'>
					<Icons name="Clear" color="var(--z-font-color)"></Icons>
					清空
				</el-button>
				<el-button type="primary" :loading="loading" @click="onSubmitForm(formComp)">
					<Icons name="Send" color="var(--z-nav-font-color)"></Icons>
					{{ submitText }}
				</el-button>
			</div>
		</el-form-item>
	</el-form>
</template>
<style scoped lang="scss">
:deep(.el-date-editor--datetimerange.el-input__inner) {
	// border: 1px solid red;
	width: 100% !important;
}

:deep(.el-date-editor.el-date-editor--datetimerange) {
	// border: 1px solid red;
	width: 100% !important;
}

.form-comp-scrollable {
	position: relative;
}

.form-items-container {
	max-height: calc(4 * 40px);
	overflow-y: auto;
	// margin-bottom: 10px;
}

.fixed-buttons {
	position: relative;
	margin-top: 10px;
	background-color: var(--el-bg-color);
	// z-index: 1;
}
</style>
