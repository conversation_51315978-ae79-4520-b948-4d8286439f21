<script setup lang="ts">
import {computed, watch, ref} from 'vue'
import {useViewStore} from '@/stores/useViewStore'

let timer: any = null
let pervProgress = 0
const progress = ref(0)
const loadingRef = ref()
const useStore = useViewStore()
const enable = computed(() => useStore.getEnableLoading)

const over = () => {
	console.log('请求完成')
	progress.value = 100
	setTimeout(() => {
		loadingRef.value?.classList.remove('show')
		useStore.setLoadCount('', 0)
		pervProgress = 0
		progress.value = 0
	}, 201)
}

watch(
	() => useStore.getLoadCount,
	(val) => {
		const {finish, count} = val

		if (finish === 0 && count === 0) {
			return
		}

		clearTimeout(timer)
		if (finish === count || finish > count) {
			timer = setTimeout(() => over(), 501)
		} else if (finish !== 0 && count !== 0) {
			const currentProgress = ((finish / count) * 100) | 0
			if (currentProgress >= pervProgress) {
				pervProgress = currentProgress
			}
			if (pervProgress < 1) {
				pervProgress = 1
			} else if (pervProgress > 100) {
				pervProgress = 100
			}
			progress.value = pervProgress
			loadingRef.value.classList.add('show')
		}

		// console.log('请求进度:', finish, count, pervProgress)
	},
	{deep: true}
)
</script>
<template>
	<div ref="loadingRef" class="loading-mask" v-if="enable">
		<div class="loading-line" :style="{width: `${progress}%`}"></div>
		<span class="count shadow-12" :class="{on: progress > 0 && progress < 100}">
			<el-icon>
				<Loading />
			</el-icon>
			<small>{{ progress }}%</small>
		</span>
	</div>
</template>
<style scoped lang="scss">
@keyframes loading-dot {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

.loading-line {
	background-color: var(--z-nav-hover);
	height: 3px;
	left: 0;
	position: fixed;
	top: 0;
	transition: width 0.2s linear;
	width: 0;
}

.loading-mask {
	background-color: rgba(0, 0, 0, 0.01);
	height: 100%;
	left: 0;
	position: fixed;
	opacity: 0;
	top: 0;
	width: 100%;
	z-index: -1;

	&.show {
		opacity: 1;
		z-index: 999999;

		i {
			animation: loading-dot 2s linear infinite;
			font-size: 16px;
		}
	}

	.count {
		align-items: center;
		border-radius: 5px;
		background-color: var(--z-nav-hover);
		color: var(--z-nav-font-color);
		display: flex;
		font-size: 13px;
		left: calc(50% - 30px);
		opacity: 0;
		position: fixed;
		padding: 5px;
		top: 3px;
		transition: all 0.15s ease-in-out;
		width: 70px;
		white-space: nowrap;

		&.on {
			top: 15px;
			opacity: 1;
		}

		small {
			font-size: 14px;
			margin-top: -1px;
			position: absolute;
			right: 5px;
		}
	}
}
</style>
