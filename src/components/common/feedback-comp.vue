<script setup lang="ts">
import {APIConfig} from '@/api/config'
import {createFeedbackList, updateFeedBack} from '@/api/feedback'
import {FeedbackStatus, FeedbackTypeList} from '@/define/feedback.define'
import {uploadFile} from '@/plugin/upload'
import {ElNotification, FormRules} from 'element-plus'
import {reactive, ref, watch} from 'vue'

const props = defineProps<{
	inopenModel: boolean
	data: any
}>()
const emit = defineEmits(['update', 'reload'])
const formLabelAlign = reactive({
	type: 1,
	description: null,
	attachment: null,
	phone: null,
})
const feedbackId = ref<string>('')
const FeedbackStatus = ref<number>()
const rules = reactive<FormRules>({
	description: [{required: true, message: '请输入问题描述', trigger: 'blur'}],
	type: [{required: true, message: '请选择', trigger: 'change'}],
	// phone: [{required: true, message: '请输入手机号', trigger: 'blur'}],
})
// const inopenModel = ref(false)
// const openFeedback = () => {
// 	// FeedbackTypeList
// 	inopenModel.value = true
// }
const imgFile = ref<any[]>([])
const customUpload = async (options: any) => {
	const res = await uploadFile(options.file, 'feedback')
	if (res) {
		imgFile.value.push({
			objectId: options.file.uid.toString(),
			name: options.file.name,
			path: `/api/files/public/p${res}`,
			extension: options.file.type,
			size: options.file.size,
		})
		console.log(imgFile.value)
	}
}
const onRemoveFile = (file: any) => {
	if (imgFile.value.filter((v) => v.objectId === file.objectId.toString()).length !== 0) {
		const findIndex = imgFile.value.findIndex((item: any) => {
			return item.objectId == file.objectId.toString()
		})
		imgFile.value.splice(findIndex, 1)
	}
}
const formRef = ref()
const opensubmit = () => {
	formRef?.value.validate(async (valid: any) => {
		if (valid) {
			const col: any = {}
			if (imgFile.value.length !== 0) {
				imgFile.value.forEach((v, i) => {
					col[`attachment${i + 1}`] = v
				})
			}
			const params: any = {
				category: Number(formLabelAlign.type),
				description: formLabelAlign.description,
				contact: formLabelAlign.phone,
				...col,
				// attachment: imgFile.value[0],
			}
			// 未完待续
			const res =
				feedbackId.value !== ''
					? await updateFeedBack(feedbackId.value, {...params, status: FeedbackStatus.value})
					: await createFeedbackList(params)
			if (res) {
				ElNotification.success('反馈成功')
				// inopenModel.value = false
				emit('update')
				feedbackId.value = ''
				formLabelAlign.type = 1
				formLabelAlign.description = null
				formLabelAlign.phone = null
				imgFile.value = []
				emit('reload')
			}
		}
	})
}
const onCancel = () => {
	emit('update')
	formLabelAlign.type = 1
	formLabelAlign.description = null
	formLabelAlign.phone = null
	imgFile.value = []
}

watch(
	() => props.data,
	(val) => {
		console.log(val)
		if (val) {
			feedbackId.value = val.id
			FeedbackStatus.value = val.status
			formLabelAlign.type = val.category
			formLabelAlign.description = val.description
			formLabelAlign.phone = val.contact

			imgFile.value = val.attachments.map((att: any) => ({
				...att,
				path: att.path.replace(APIConfig('base'), ''),
				url: att.path,
			}))
		}
	}
)
defineExpose({
	opensubmit,
	onCancel,
})
</script>
<template>
	<!-- <DialogComp
		:visible="inopenModel"
		:key="inopenModel"
		title="问题反馈"
		width="600"
		:visibleFooterButton="true"
		@closed="onCancel"
		:confirmText="'反馈'"
		@clickConfirm="submit"
		@clickCancel="onCancel"
	> -->
	<div>
		<el-form
			ref="formRef"
			:label-position="'right'"
			label-width="80px"
			p="20px"
			:rules="rules"
			:model="formLabelAlign"
		>
			<el-form-item label="问题类型" prop="type">
				<el-select v-model="formLabelAlign.type" w-full>
					<el-option v-for="item in FeedbackTypeList" :label="item.name" :value="item.value" />
				</el-select>
			</el-form-item>
			<el-form-item label="问题描述" prop="description">
				<el-input v-model="formLabelAlign.description" placeholder="请输入问题描述" />
			</el-form-item>

			<el-form-item label="联系方式" prop="phone">
				<el-input v-model="formLabelAlign.phone" placeholder="请输入联系方式" />
			</el-form-item>
			<el-form-item label="附件" prop="attachment">
				<el-upload
					v-model="imgFile"
					ref="upload"
					class="upload"
					w-full
					action=""
					accept=".jpg,.jpeg,.png"
					list-type="picture"
					:http-request="customUpload"
					:limit="5"
					:on-remove="onRemoveFile"
					:file-list="imgFile"
				>
					<el-button type="primary" v-if="imgFile.length < 5">上传</el-button>
					<template #tip>
						<div class="el-upload__tip">
							仅支持.jpg,.jpeg,.png格式文件上传,最多只能上传5张图片。
						</div>
					</template>
				</el-upload>
			</el-form-item>
		</el-form>
	</div>
	<!-- </DialogComp> -->
</template>
<style lang="scss" scoped>
.feedback-btn {
	transition: all 0.3s;
	&:hover {
		transform: scale(1.1);
	}
}
</style>
