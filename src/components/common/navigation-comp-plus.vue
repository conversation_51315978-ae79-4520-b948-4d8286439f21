<script setup lang="ts">
import {useViewStore} from '$/useViewStore'
import {computed, onMounted, ref, watch} from 'vue'
import menuItemComp from './menu-item-comp.vue'
import {MenuIcon} from '@/define/icon.define'
import {useRoute} from 'vue-router'
import {useUserStore} from '@/stores/useUserStore'

interface Props {
	data: Array<any>
	isCollapse?: boolean
	defaultActive?: string
}

const props = withDefaults(defineProps<Props>(), {
	isCollapse: true,
	defaultActive: '',
})

const emits = defineEmits(['clickItem'])
const store = useViewStore()
const route = useRoute()
const userStore = useUserStore()
// 菜单
const navigation = ref(props.data)
const subNavigation = ref([]) as any
const todoBadgeValue = computed(() => userStore.workCount)

watch(
	() => props.data,
	(newVal) => {
		console.log('NavigationCompPlus watch', newVal, store.viewIndex)

		navigation.value = newVal
		if (navigation.value.length) {
			if (navigation.value[store.viewIndex]) {
				subNavigation.value = navigation.value[store.viewIndex].child
			}
		}
	},
	{deep: true}
)

/**
 * 点击左侧菜单
 * @param item 菜单对象
 */
const clickItem = (item: any) => {
	emits('clickItem', item)
}

onMounted(() => {})

/** 从后端获取菜单后格式化菜单数据 */
const format = (raw: Array<any>): Array<any> => {
	if (raw && raw.length) {
		const result = raw.filter((item) => item.parentId === null)
		result.sort((a, b) => Number(a.meta.sort) - Number(b.meta.sort))

		const setChildRecursive = (arr: Array<any>, parent: any) => {
			arr.forEach((item) => {
				if (item.parentId === parent.id) {
					if (!parent.child) {
						parent.child = []
					}
					item._topIndex = parent._topIndex
					parent.child.push(item)
					setChildRecursive(arr, item)
				}
			})
		}

		result.forEach((item, i: number) => {
			item._topIndex = i
			setChildRecursive(raw, item)
		})

		// 排序
		const sortRecursive = (arr: Array<any>) => {
			arr.sort((a, b) => Number(a.meta.sort) - Number(b.meta.sort))
			arr.forEach((item) => {
				if (item.child && item.child.length) {
					sortRecursive(item.child)
				}
			})
		}
		sortRecursive(result)
		return JSON.parse(JSON.stringify(result))
	}
	return []
}

const toggleChild = (index: number, isEmit: boolean = true) => {
	// 判断顶级菜单是否有子集
	// 判断是否需要新开窗口显示
	if (isEmit) {
		if (!navigation.value[index].child) {
			return emits('clickItem', navigation.value[index])
		}
		subNavigation.value = navigation.value[index].child
		if (!subNavigation.value[0].child) {
			emits('clickItem', subNavigation.value[0])
		} else {
			emits('clickItem', subNavigation.value[0].child[0])
		}
	} else {
		subNavigation.value = navigation.value[index === undefined ? 1 : index].child
	}
}

const findByModuel = (module: string): any => {
	const findRecursive = (arr: Array<any>): any => {
		for (const element of arr) {
			if (element.path.includes(module) && route.fullPath === element.path) {
				console.log(element.path, module)
				return element
			}
			if (element.child && element.child.length) {
				const result = findRecursive(element.child)
				if (result) {
					return result
				}
			}
		}
	}
	return findRecursive(navigation.value)
}

defineExpose({
	format,
	toggleChild,
	findByModuel,
})
</script>
<template>
	<el-menu :default-active="defaultActive" class="left-menu" :collapse="isCollapse">
		<template v-for="item, i of (navigation as Array<any>)">
			<template v-if="item.child">
				<menuItemComp :data="item" :is-collapse="isCollapse" @click-item="clickItem"></menuItemComp>
			</template>

			<template v-else>
				<el-menu-item :index="item.id" @click="clickItem(item)" class="only">
					<el-icon class="icon-svg" v-html="MenuIcon[item.displayName]"></el-icon>
					<template #title>
						<template v-if="item.displayName === '工作待办'">
							<span>{{ item.displayName }}</span>
							<span class="badge" v-if="todoBadgeValue && todoBadgeValue !== 0">{{
								todoBadgeValue
							}}</span>
						</template>
						<template v-else>
							<span>{{ item.displayName }}</span>
						</template>
					</template>
				</el-menu-item>
			</template>
		</template>
	</el-menu>
</template>

<style scoped lang="scss">
.icon {
	width: 1em;
	height: 1em;
	vertical-align: -0.15em;
	font-size: 16px;
	fill: currentColor;
	overflow: hidden;
}
.badge {
	margin-left: 10px;
	display: inline-block;
	height: 16px;
	line-height: 16px;
	text-align: center;
	border-radius: 10px;
	background-color: red;
	color: #fff;
	padding: 0 6px;
}
:deep(.el-menu-item.only) {
	color: #303133;
	padding: 0 13px !important;
}
</style>
