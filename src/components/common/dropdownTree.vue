<script setup lang="ts">
import {computed, onMounted, onUnmounted, ref, toRaw, watch} from 'vue'
import {useSource} from '@/stores/useSource'
import {useArrayToTree} from '@/hooks/useConvertHook'

interface Props {
	single?: boolean
	data?: any[]
	keyField?: string
	title?: string
	labelField?: string
	parentField?: string
	disabled?: boolean
	placeholder?: string
	defaultData?: any[]
}

const props = withDefaults(defineProps<Props>(), {
	keyField: 'id',
	title: '全部业务表',
	labelField: 'name',
	parentField: 'parentId',
	single: false,
	disabled: false,
	placeholder: '请选择类型',
	defaultData: [] as any,
})
const emits = defineEmits(['selected'])
const source = useSource()

watch(
	() => props.data,
	(newVal) => {
		setData(newVal)
	}
)

watch(
	() => source.getLedgerType,
	(newVal) => {
		if (props.defaultData && props.defaultData.length > 0) {
			const loops = (arr: any) => {
				for (const element of arr) {
					const item = element
					if (item.child && item.child.length > 0) {
						loops(item.child)
					}
					if (props.defaultData?.includes(item[props.keyField])) {
						onClickChild(item)
						return
					}
				}
			}
			loops(dataTree.value)
		}
	}
)

watch(
	() => props.defaultData,
	(newVal) => {
		if (source.getLedgerType.items.length === 0) {
			return
		}
		const loops = (arr: any) => {
			for (const element of arr) {
				const item = element
				if (item.child && item.child.length > 0) {
					loops(item.child)
				}
				if (newVal?.includes(item[props.keyField])) {
					onClickChild(item)
					return
				}
			}
		}
		if (newVal && newVal.length > 0) {
			if (newVal.length === 1 && !newVal[0]) {
				singleCheckedTitle.value = ''
				current.value.selected = []
				current.value.checkAll = false
				current.value.indeterminate = false
				currentChild.value.forEach((child: any) => (child.checked = false))
				emits('selected', [])
			} else {
				loops(dataTree.value)
			}
		}
	}
)

const dataTree = ref<any>([])
const current: any = ref(null)
const currentChild: any = ref([])
const singleCheckedTitle = ref('')
const dropHeight = computed(() => {
	const len =
		currentChild.value?.length > dataTree.value.length
			? currentChild.value.length
			: dataTree.value.length
	return len * 42 + 12
})
const isOpen = ref(false)

const onOpenDropdown = () => {
	if (props.disabled) {
		return
	}
	if (!isOpen.value) {
		source.pullLedgerType()
	}
	isOpen.value = !isOpen.value
}

const onClickItem = (item: any) => {
	current.value = item
	currentChild.value = item.child

	setTimeout(() => {
		if (current.value.checkAll) {
			current.value.selected = currentChild.value.map((child: any) => child[props.labelField])
			current.value.indeterminate = false
		} else if (!current.value.indeterminate) {
			current.value.selected.length = 0
		}
		currentChild.value.forEach((child: any) => (child.checked = current.value.checkAll))
		emits('selected', toRaw(dataTree.value))
	}, 0)
}

const onClickChild = (child: any) => {
	child.checked = !child.checked

	setTimeout(() => {
		// 单选
		if (props.single) {
			dataTree.value.forEach((f: any) => {
				f.checkAll = false
				f.selected.length = 0
				f.indeterminate = false
			})
			current.value.selected = [child[props.labelField]]
			singleCheckedTitle.value = current.value.selected[0]
			isOpen.value = false
		}

		if (current.value.selected.length === currentChild.value.length) {
			current.value.checkAll = true
			current.value.indeterminate = false
		} else if (current.value.selected.length === 0) {
			current.value.checkAll = false
			current.value.indeterminate = false
		} else {
			current.value.checkAll = false
			current.value.indeterminate = true
		}
		emits('selected', props.single ? toRaw(child) : toRaw(dataTree.value))
	}, 0)
}

const onCloseDropdown = () => {
	isOpen.value = false
}

const setData = (data: any) => {
	dataTree.value = useArrayToTree(
		JSON.parse(JSON.stringify(data)),
		props.keyField,
		props.parentField,
		props.labelField
	)
	current.value = dataTree.value[0]

	if (current.value?.child) {
		currentChild.value = current.value.child
	}
}

onMounted(async () => {
	source.pullLedgerType()
	setData(props.data)
	document.body.addEventListener('click', onCloseDropdown)
})
onUnmounted(() => {
	document.body.removeEventListener('click', onCloseDropdown)
})
</script>
<template>
	<div class="dropdown-tree" @click.stop :class="{single: props.single, open: isOpen}">
		<el-button
			v-if="!props.single"
			size="small"
			type="primary"
			@click="onOpenDropdown"
			:disabled="props.disabled"
		>
			{{ props.title }}
			<el-icon class="el-icon--right"><arrow-down /></el-icon>
		</el-button>
		<div
			v-else
			class="single-title"
			:class="{disabled: props.disabled, placeholder: singleCheckedTitle === ''}"
			@click="onOpenDropdown"
		>
			<span>{{ singleCheckedTitle || props.placeholder }}</span>
			<el-icon><ArrowDown /></el-icon>
		</div>

		<div
			class="dropdown"
			:class="{open: isOpen}"
			:style="{height: isOpen ? dropHeight - 10 + 'px' : 0}"
		>
			<div class="dropdown-box">
				<div class="left">
					<el-scrollbar :height="`${dropHeight - 22 > 290 ? 290 : dropHeight - 22}px`">
						<div
							class="dropdown-item"
							:class="{
								active: current?.id === item.id,
								disabled: !item.child || item.child?.length === 0,
							}"
							v-for="(item, index) of (dataTree as any)"
							:key="`aldm${index}`"
							@click.self=";(current = item), (currentChild = item?.child)"
						>
							<el-checkbox
								v-model="item.checkAll"
								:indeterminate="item.indeterminate"
								:disabled="!item.child || item.child?.length === 0 || props.single"
								@change="onClickItem(item)"
							>
								{{ item.label }}
							</el-checkbox>
							<span
								@click=";(current = item), (currentChild = item?.child)"
								class="icon i-ic-baseline-keyboard-arrow-right"
							></span>
						</div>
					</el-scrollbar>
				</div>
				<el-scrollbar class="right" :height="`${dropHeight - 22}px`" v-if="current">
					<el-checkbox-group
						v-model="current.selected"
						v-if="currentChild && currentChild.length > 0"
					>
						<el-checkbox
							class="dropdown-child"
							v-for="child of (currentChild as any)"
							:key="child.label ?? child.name"
							:label="child.label ?? child.name"
							@change="onClickChild(child)"
						>
							{{ child.label ?? child.name }}
						</el-checkbox>
					</el-checkbox-group>
					<el-empty
						v-else
						description="暂无类型"
						style="padding: 30px; width: 160px"
						:image-size="80"
					/>
				</el-scrollbar>
			</div>
		</div>
	</div>
</template>
<style scoped lang="scss">
.dropdown-tree {
	align-items: center;
	display: flex;
	padding: 0 10px;
	position: relative;

	.label {
		height: 16px;
		line-height: 18px;
	}
	.icon {
		font-size: 16px;
		margin-left: 5px;
	}

	&::after {
		content: '';
		border: 6px solid transparent;
		border-bottom-color: #fff;
		left: 20px;
		opacity: v-bind('isOpen ? 1 : 0');
		position: absolute;
		transition: all 0.15s ease-in-out;
		top: 33px;
		z-index: 5;
	}

	&::before {
		content: '';
		border: 6px solid transparent;
		border-bottom-color: #dcdfe6;
		left: 20px;
		opacity: v-bind('isOpen ? 1 : 0');
		position: absolute;
		transition: all 0.15s ease-in-out;
		top: 32px;
		z-index: 5;
	}

	:deep(&.single) {
		padding: 0;
		.el-checkbox__label {
			color: #333;
		}
	}

	&.open {
		.single-title i {
			rotate: -180deg;
		}
	}
}

.dropdown {
	border-radius: 5px;
	background: #fff;
	box-shadow: 0 2px 4px -1px #0003, 0 4px 5px #00000024, 0 1px 10px #0000001f;
	height: 0;
	max-height: 300px;
	opacity: 0;
	overflow: hidden;
	position: absolute;
	top: 45px;
	transition: all 0.15s ease-in-out;
	z-index: 4;

	&.open {
		height: auto;
		opacity: 1;
	}
}

.dropdown-box {
	display: flex;
	max-height: 100px;
	position: relative;
	padding: 5px;
	width: 100%;

	.left {
		border-radius: 5px 0 0 5px;
		border: var(--z-border);
		border-right: none;
		cursor: pointer;
		height: 100%;
		min-width: 150px;
		max-height: 290px;
		overflow: hidden;
		position: relative;
		z-index: 1;
		.el-scrollbar {
			width: 100%;
		}
	}
	.right {
		border-radius: 0 5px 5px 0;
		border: var(--z-border);
		display: flex;
		flex-direction: column;
		height: 100%;
		min-width: 100px;
		max-height: 290px;
		overflow: hidden;
		transition: all 0.15s ease-in-out;

		:deep(.el-skeleton) {
			padding: 5px;
			.el-skeleton__item {
				margin-top: 0;
			}
		}

		:deep(.el-checkbox-group) {
			flex-direction: column;
		}
	}
	:deep(.el-checkbox) {
		margin-right: 0;
	}
	:deep(.el-checkbox__label) {
		font-weight: 400;
		font-size: 13px;
		max-width: 200px;
		overflow: hidden;
		text-overflow: ellipsis;
	}
}

.dropdown-item {
	align-items: center;
	background-color: #fdfdfd;
	border-bottom: var(--z-border);
	display: flex;
	height: 40px;
	justify-content: space-between;
	padding: 0 10px;
	position: relative;

	&:last-child {
		border-bottom: none;
	}

	&.active {
		background-color: #f7f7f7;

		.icon {
			opacity: 1;
			rotate: 90deg;
		}
	}

	&.disabled {
		:deep(.el-checkbox__label) {
			color: #ccc;
		}
	}

	.icon {
		flex: none;
		opacity: 0.3;
		transition: all 0.15s ease-in-out;
	}
}

.dropdown-child {
	align-items: center;
	cursor: pointer;
	display: flex;
	height: 38px;
	line-height: 1;
	min-width: 150px;
	padding: 0 10px;
}

.single-title {
	align-items: center;
	border-radius: 4px;
	border: var(--z-border);
	background-color: #fff;
	color: #303133;
	cursor: pointer;
	display: flex;
	font-size: 13px;
	height: 32px;
	padding: 0 8px;
	min-width: 200px;

	&.placeholder {
		color: #a8abb2;
	}

	span {
		flex: 1;
	}
	i {
		color: #a8abb2;
		transition: all 0.15s linear;
	}

	&.disabled {
		background-color: #f5f7fa;
		color: #bbb;
		cursor: no-drop;
	}
}
</style>
