<script setup lang="ts">
import { ref } from 'vue';

interface Props {
	message: string
	percentage: number
}

const props: Props = defineProps({
	message: {
		type: String,
		default: '-'
	},
	percentage: {
		type: Number,
		default: 0
	}
})

const _percentage = ref(0)
const _message = ref('进度...')

defineExpose({
	update: (message: string, percentage: number) => {
		_message.value = message
		_percentage.value = percentage
	}
})

</script>
<template>
	<div class="progress-bar-comp"
		:class="{ begin: _percentage > 0 && _percentage <= 100, 'end-before': _percentage > 98 && _percentage < 100 }">
		<div class="message">{{ _message }}</div>
		<div class="percentage">
			<div class="bar" :style="{ width: `${_percentage}%` }">
				<div class="bar-animtion"></div>
			</div>
			<span class="txt" :class="{ half: _percentage > 45 }">
				{{ _percentage }}%
			</span>
		</div>
	</div>
</template>