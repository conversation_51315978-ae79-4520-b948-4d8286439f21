<script setup lang="ts">
import {Filter} from '@element-plus/icons-vue'
import {ref} from 'vue'
const props = defineProps({
	col: {} as any,
	customSlots: Array<string>,
})
const checkList = ref<any[]>([])
const emits = defineEmits(['handleFilter'])
const col = props.col
const handleFilter = (value: any) => {
	emits('handleFilter', value)
}
const fixedFields = ['City', 'District', 'Street', 'Community']
</script>
<template>
	<el-table-column
		v-if="!col.children"
		:label="col.title"
		:prop="col.field"
		:align="col.align"
		:width="col.width || 'auto'"
		:sortable="col.sortable ? 'custom' : false"
		:fixed="col.fixed"
		min-width="130px"
		show-overflow-tooltip
		eillipsis
	>
		<template #header>
			<slot :name="col.field + 'Title'" :rowData="col">
				<!-- <template v-if="col.title && col.title.length > 8">
					<el-tooltip :content="col.title" raw-content placement="top">
						{{ col.title.substring(0, 7) + '...' }}
					</el-tooltip>
				</template>
				<template v-else>{{ col.title }}</template> -->
				<!-- {{ fixedFields.includes(col.field) ? '*' : '' }} -->
				<el-tooltip :content="col.title" raw-content placement="top-start">
					{{ col.title }}
				</el-tooltip>
				<span v-if="col.filters" cursor-pointer>
					<el-popover placement="bottom" :width="80" trigger="click">
						<template #reference>
							<span
								><el-icon><Filter /></el-icon
							></span>
						</template>
						<el-checkbox-group pl="25px" v-model="checkList" @change="handleFilter">
							<el-checkbox
								v-for="filter in col.filters"
								:label="filter.text"
								:checked="filter.checked"
							/>
						</el-checkbox-group>
					</el-popover>
				</span>
			</slot>
		</template>
		<template #default="scope">
			<slot :name="col.field" :rowData="(scope as any).row" :rowIndex="(scope as any).$index">
				{{ (scope as any).row[col.field] || '-' }}
			</slot>
		</template>
	</el-table-column>

	<el-table-column v-else :label="col.title" align="center">
		<TableColumnComp
			v-for="t in col.children"
			:key="t.field"
			:col="t"
			:customSlots="customSlots"
		>
			<template v-for="slot in customSlots" #[slot]="scope">
				<slot :name="slot" v-bind="scope" />
			</template>
		</TableColumnComp>
	</el-table-column>
</template>
