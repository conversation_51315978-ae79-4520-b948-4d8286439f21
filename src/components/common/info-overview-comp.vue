<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from "vue";
import util from "@/plugin/util";
import { APIConfig } from "@/api/config";
const textValue = ref(
  "指标组件平台的数据。因指标组件平台还有可能接受其他系统的推送,故显示的数值可能与下面一表通算出来的数据不一致"
);
interface Props {
  titles: Array<{
    width: string;
    title: string;
    key: string;
  }>;
  data: any;
}

const props = withDefaults(defineProps<Props>(), { data: {} });
const emits = defineEmits(["onToggleVisible"]);

watch(
  () => props.data,
  (val) => {
    setTimeout(() => {
      const el = document.querySelector(`#${onlyId.value}`) as HTMLElement;
      if (el) {
        height.value = el.offsetHeight;
      }
    }, 200);
  }
);

const onlyId = ref(`InfoOverview${util._guid()}`);
const isShow = ref(false);
const height = ref(-1);
const imageUrl = ref("");
const onSwitch = () => {
  isShow.value = !isShow.value;
  emits("onToggleVisible", isShow.value, height.value);
};
const handlePreviewOrDownload = (att: any, key: string) => {
  // imageUrl.value = APIConfig('base') + att.path
  // window.open(APIConfig('base') + att.path, '_blank')
  util.downloadAttachment(att, key === "fileInfos" ? 1 : 2);
};
onMounted(() => {
  console.log(props.data);
});
</script>
<template>
  <div
    :id="onlyId"
    class="info-overview-comp w-full"
    :class="{ show: isShow }"
    :style="{ height: height === -1 ? 'auto' : height + 'px' }"
  >
    <div
      v-for="item in titles"
      :style="{
        width: item.width,
        height: item.key !== 'attachments' && item.key !== 'fileInfos' ? '40px' : 'unset',
      }"
      class="df"
    >
      <div style="line-height: 40px; text-align: center; width: 130px; height: 100%">
        {{ item.title }}
      </div>

      <el-tooltip
        class="box-item"
        effect="dark"
        :content="
          data[item.key] && (item.key == 'value' || item.key == 'idxValue')
            ? textValue
            : data[item.key] ?? '-'
        "
        placement="bottom"
        v-if="item.key !== 'attachments' && item.key !== 'fileInfos'"
      >
        <slot :name="item.key">
          <div
            style="
              width: calc(100% - 130px);
              text-overflow: ellipsis;
              overflow: hidden;
              line-height: 40px;
              height: 100%;
              padding-left: 5px;
              white-space: nowrap;
            "
          >
            {{ data[item.key] ?? "-" }}
          </div>
        </slot>
      </el-tooltip>
      <div
        v-else
        style="
          align-items: center;
          width: calc(100% - 130px);
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          flex-wrap: wrap;
        "
        class="h-full pd-left-5 pd-top-5 pd-bottom-5 df"
      >
        <template v-if="data[item.key] && data[item.key].length">
          <span v-for="att in data[item.key]" class="mg-right-10">
            <el-link type="primary" @click="handlePreviewOrDownload(att, item.key)">
              {{
                att.name.includes("-") ? att.name.split("-").slice(1).join() : att.name
              }}
            </el-link>
          </span>
        </template>
        <template v-else>-</template>
      </div>
    </div>
    <!-- <span class="switch" @click="onSwitch">
			{{ isShow ? '展开' : '收起' }}
			<i i-ic-baseline-keyboard-double-arrow-up></i>
		</span> -->
  </div>
  <ViewImage :src="imageUrl"></ViewImage>
</template>
<style lang="scss" scoped>
.info-overview-comp {
  transition: height 0.3s ease;
}
.isFold {
  height: 41px !important;
  overflow: hidden;
}
</style>
