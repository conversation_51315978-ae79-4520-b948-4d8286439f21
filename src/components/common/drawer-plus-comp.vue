<script setup lang="ts">
import {ref, watch} from 'vue'

const props = defineProps({
	visible: {
		type: Boolean,
		default: false,
	},
	title: {
		type: String,
		default: 'Drawer',
	},
})

const emits = defineEmits(['onClose'])
const drawerRef = ref()
const show = ref(false)
let parentNode: any = null

watch(
	() => props.visible,
	(newVal) => {
		if (newVal) {
			parentNode = drawerRef.value.parentNode
			document.body.appendChild(drawerRef.value)
		} else {
			if (parentNode) {
				parentNode.appendChild(drawerRef.value)
				parentNode = null
			}
		}
		setTimeout(() => {
			show.value = newVal
		}, 16.7)
	}
)

const onClose = () => {
	emits('onClose')
}
</script>
<template>
	<div ref="drawerRef" class="drawer-plus-comp" :class="{open: show}" @click.stop="onClose">
		<div class="drawer-layout" :class="{open: show}" :style="{width: '70%'}" @click.stop>
			<div class="drawer-header">
				<div>
					<h1>{{ title }}</h1>
				</div>
				<div class="slot"><slot name="header"> </slot></div>
				<span class="close" @click="onClose">&times;</span>
			</div>
			<div class="drawer-content">
				<slot name="content"></slot>
			</div>
		</div>
	</div>
</template>
