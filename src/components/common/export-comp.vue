<script lang="ts" setup>
import {onMounted, ref, toRaw, watch} from 'vue'
import {ExportExcel} from '@/plugin/export'

interface Props {
	sheets: any[]
	fileName?: string // 文件名
	creator?: string // 导出人
}

const props = withDefaults(defineProps<Props>(), {
	sheets: [] as any,
	fileName: +new Date() + '',
	creator: 'System',
})

watch(
	() => props.sheets,
	(newVal) => {
		console.log(8888, newVal)
		if (newVal !== null) {
			sheet.value = newVal
			const exp = ExportExcel.getInstance()
			exp.download(toRaw(sheet.value), props.fileName, props.creator)
		}
	}
)

const sheet = ref(props.sheets)

const onClick = (data?: any) => {
	if (sheet.value.length === 0) return
	if (data) {
		sheet.value = data
	}
}

const refreshList = (list: []) => {
	if (props.sheets[0]) {
		const arr = list.map((row: any) => JSON.parse(row.rawData))
		props.sheets[0].data = arr.flat()
	}
}

onMounted(() => {})

defineExpose({
	refreshList,
	onClick,
})
</script>
<template>
	<div class="export">
		<el-button type="primary" size="small" @click="onClick">
			<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 256 256">
				<g fill="currentColor">
					<path d="M208 104v112H48V104Z" opacity=".2" />
					<path
						d="M216 112v96a16 16 0 0 1-16 16H56a16 16 0 0 1-16-16v-96a16 16 0 0 1 16-16h24a8 8 0 0 1 0 16H56v96h144v-96h-24a8 8 0 0 1 0-16h24a16 16 0 0 1 16 16ZM93.66 69.66L120 43.31V136a8 8 0 0 0 16 0V43.31l26.34 26.35a8 8 0 0 0 11.32-11.32l-40-40a8 8 0 0 0-11.32 0l-40 40a8 8 0 0 0 11.32 11.32Z"
					/>
				</g>
			</svg>
			导出
		</el-button>
	</div>
</template>
<style scoped lang="scss">
.export {
	svg {
		margin-right: 5px;
	}
	button {
		align-items: center;
		display: flex;
		margin: 0 10px;
	}
}
</style>
