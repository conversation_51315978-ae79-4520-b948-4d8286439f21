<script setup lang="ts">
import {watch, ref, nextTick} from 'vue'
import {ReportsFlowStatusEnum} from '@/define/statement.define'
import {urging} from '@/api/ReportApi'
import {ElMessage} from 'element-plus'
import {useRoute, useRouter} from 'vue-router'
const route = useRoute()
enum TaskType {
	PlanTask = 1, // 下发任务
	InternalTask = 2, // 内部任务
}

enum UnitStatus {
	PendingExecution = 0, // 待执行
	Executing = 1, // 执行中
	Executed = 2, // 已执行
}

enum StaffStatus {
	PendingWrite = '待填写',
	Submited = '已提交',
	Rejected = '已驳回',
}

enum FillingDepartmentStatus {
	PendingWrite = 1, // 待填写
	Rejected = 2, // 已驳回
	PendingAudit = 3, // 待审核
	Audited = 4, // 已审核
}

enum FillingTaskState {
	Active = 1, // 激活
	PendingActivation = 2, // 待激活
	Completed = 3, // 完成
	Closed = 4, // 关闭
	SignState = 5, // 加签状态
	TransferToOthers = 6, // 转移给他人
	Invalid = 7, // 作废
	SubprocessRunning = 8, // 子流程运行中
}

enum StatusType {
	Success = 'success',
	Warning = 'warning',
	Danger = 'danger',
}

const emits = defineEmits(['update:end', 'agree', 'clickText'])
const props = defineProps({
	data: {type: Object, default: () => ({})},
	isDetail: {type: Boolean, default: false},
	isTodo: {type: Boolean, default: false},
	type: {type: String, default: ''},
	isLedgerRecode: {type: Boolean, default: false},
	isExport: {type: Boolean, default: false},
	departmentJump: {type: Boolean, default: false},
})
const params = ref<any>({
	areaOrganizationUnitIds: [],
	userIds: [],
})
const recodeList: any = ref([])

const getStartNode = (data: any) => {
	const isCurrentUnit =
		ReportsFlowStatusEnum.PendingExecution === data.status ||
		ReportsFlowStatusEnum.Rejected === data.status
	return {
		label: `发起任务 ${isCurrentUnit ? '(当前节点:待发起)' : ''}`,
		text: `${data.creatorDepartment.parent.name}-${data.creatorDepartment.name}-${data.creatorStaff.name}`,
		type: isCurrentUnit ? StatusType.Warning : StatusType.Success,
	}
}

const getIssueNode = (data: any) => {
	let issueList: any = []
	if (data.issueAuditProcess) {
		issueList = data.issueAuditProcess.units.map((item: any) => {
			const hasInvalid = item.tasks.some((x: any) => !x.isAgree && x.isAgree !== null)
			const hasCompleted = item.isCountersign
				? item.tasks.every((x: any) => x.isAgree)
				: item.tasks.some((x: any) => x.isAgree)

			let status = ''
			if (item.isCurrentUnit) {
				status = StatusType.Warning
			} else if (hasInvalid) {
				status = StatusType.Danger
			} else if (hasCompleted) {
				status = StatusType.Success
			}

			console.log(item)
			let pendingReview: any = ''
			if (item.isCurrentUnit) {
				item.tasks.forEach((x: any) => {
					params.value.userIds.push(x.auditor.id)
				})
			}
			return {
				label: `下发审核${item.isCurrentUnit ? '(当前节点: 执行中)' : ''}`,
				text:
					item.tasks
						.map((subitem: any) => {
							let statusHtml = ''
							if (subitem.isAgree) {
								statusHtml = '已通过'
							} else if (subitem.state == 4 && data.status == 11) {
								statusHtml = '已撤回'
							} else if (!subitem.isAgree && subitem.isAgree !== null) {
								statusHtml = '已驳回'
							} else {
								statusHtml = '待审核'
								pendingReview = '待审核'
							}
							return `
								${subitem.auditor.largeDepartmentName}-${subitem.auditor.departmentName}-${subitem.auditor.name}
								${statusHtml ? `<p class="time description">${statusHtml} ${subitem.operationTime || ''}</p>` : ''}
								${subitem.description ? '<p class="description">审核说明：' + subitem.description + '</p>' : ''}
							`
						})
						.join('<br />') +
					`<p class="fb-1 mg-top-5 red">${
						item.isCountersign ? '全部审核人都需要审核' : '任意一人审核通过'
					}</p>` +
					`${
						item.isCurrentUnit
							? `<p class='urging'>催一下<span class="urgingImg"></span></p>`
							: ''
					}`,
				type: status,
			}
		})
	}
	return issueList
}
const getFill = (data: any) => {
	let fillList: any = []
	let statusText = '待填报'
	let fillsStatus = data.isCurrentUnit ? StatusType.Warning : ''
	const showJump = data.isCurrentUnit === null || data.isCurrentUnit

	if (data.staffs.length) {
		if (data.isCurrentUnit) {
			data.staffs.forEach((x) => {
				if (x.reportTaskAreaOrganizationUnitFillerStatus !== 2) {
					params.value.userIds.push(x.id)
				}
			})
		}
		// 下发
		fillList =
			data.staffs
				.map((item: any) => {
					return `
					<p ${props.departmentJump && showJump ? 'data-id="' + item.id + '"' : ''}  data-type="staff">${
						item.department.parent.name
					}-${item.department.name}-${item.name}</p>
					<p class="description time">
						${Object.entries(StaffStatus)[item.reportTaskAreaOrganizationUnitFillerStatus - 1][1]}
					</p>
				`
				})
				.join('<br />') +
			`${
				data.isCurrentUnit
					? `<p class='urging'>催一下<span class="urgingImg"></span></p>`
					: ''
			}`
	} else if (data.fillingDepartments.length) {
		// 部门
		if (data.isCurrentUnit) {
			data.fillingDepartments.forEach((x) => {
				if (x.status !== FillingDepartmentStatus.Audited) {
					params.value.areaOrganizationUnitIds.push(x.id)
				}
			})
		}

		fillList =
			data.fillingDepartments
				.map((item: any) => {
					let currentStatusText = ''

					if (item.status === FillingDepartmentStatus.Rejected) {
						statusText = '已驳回'
						currentStatusText = '已驳回'
						fillsStatus = StatusType.Danger
					} else if (item.status === FillingDepartmentStatus.Audited) {
						statusText = '待全部汇总'
						currentStatusText = '已汇总'
						// fillsStatus = StatusType.Success
					} else if (item.status === FillingDepartmentStatus.PendingAudit) {
						statusText = '待审核'
						currentStatusText = '待审核'
						fillsStatus = StatusType.Warning
					} else {
						currentStatusText = '待填写'
					}

					let statusHtml = ''
					if (item.status === FillingDepartmentStatus.Audited) {
						statusHtml = '' + currentStatusText + ' ' + item.mergeTime
					} else if (item.status === FillingDepartmentStatus.Rejected) {
						statusHtml = '' + currentStatusText
					} else {
						statusHtml = currentStatusText
					}

					return `
					<p ${
						props.departmentJump && showJump
							? 'data-id="' + item.reportTaskAreaOrganizationUnitId + '"'
							: ''
					} data-type="department">${item.parent.name}-${item.name}</p>
					${statusHtml ? '<p class="description time">' + statusHtml + '</p>' : ''}
				`
				})
				.join('<br />') +
			`${
				data.isCurrentUnit
					? `<p class='urging'>催一下<span class="urgingImg"></span></p>`
					: ''
			}`
	}

	if (data.isCurrentUnit === null) {
		fillsStatus = StatusType.Success
	}

	const fills = {
		label: `填报数据 ${data.isCurrentUnit ? '(当前节点:' + statusText + ')' : ''}`,
		text: fillList,
		type: fillsStatus,
	}
	return fills
}

const getFillDetail = (data: any) => {
	let fillList: any = []
	let status = ''
	let text = ''

	if (data.isCurrentUnit === null) {
		status = StatusType.Success
	} else if (data.isCurrentUnit) {
		status = StatusType.Warning
	}

	if (data.fillingMode === TaskType.InternalTask) {
		if (data.reportTaskAreaOrganizationUnitFillers.length && !data.isShowDepartment) {
			// 内部

			if (data.isCurrentUnit) {
				data.reportTaskAreaOrganizationUnitFillers.forEach((x) => {
					if (x.reportTaskAreaOrganizationUnitFillerStatus !== 2) {
						params.value.userIds.push(x.filler.id)
					}
				})
			}
			fillList.push({
				label: `填报数据${data.isCurrentUnit ? '(当前节点:待填报)' : ''}`,
				text:
					data.reportTaskAreaOrganizationUnitFillers
						.map((item: any) => {
							return `
							${item.fillerDepartment?.parent.name + '-' + item.fillerDepartment?.name + '-' + item.filler.name}
							<p class="description">${
								Object.entries(StaffStatus)[
									item.reportTaskAreaOrganizationUnitFillerStatus - 1
								][1]
							}</p>
						`
						})
						.join('<br />') +
					`${
						data.isCurrentUnit
							? `<p class='urging'>催一下<span class="urgingImg"></span></p>`
							: ''
					}`,
				type: status,
			})
		} else {
			if (data.isCurrentUnit) {
				params.value.areaOrganizationUnitIds.push(data.fillingDepartment.id)
			}
			text =
				data.fillingDepartment.parent.name +
				'-' +
				data.fillingDepartment.name +
				'<br />' +
				`${
					data.isCurrentUnit
						? `<p class='urging'>催一下<span class="urgingImg"></span></p>`
						: ''
				}`
			fillList.push({
				label: `填报数据${data.isCurrentUnit ? '(当前节点:待填报)' : ''}`,
				text,
				type: status,
			})
		}
	} else if (data.fillingMode === TaskType.PlanTask) {
		// 下发
		if (props.isTodo) {
			if (data.reportTaskAreaOrganizationUnitFillers.length && !data.isShowDepartment) {
				if (data.isCurrentUnit) {
					data.reportTaskAreaOrganizationUnitFillers.forEach((v) => {
						params.value.userIds.push(v.filler.id)
					})
				}
				text =
					data.reportTaskAreaOrganizationUnitFillers
						.map((item: any) => {
							return `
							${item.fillerDepartment.parent.name + item.fillerDepartment.name + item.filler.name}
						`
						})
						.join('<br />') +
					`${
						data.isCurrentUnit
							? `<p class='urging'>催一下<span class="urgingImg"></span></p>`
							: ''
					}`
			} else {
				if (data.isCurrentUnit) {
					params.value.areaOrganizationUnitIds.push(data.fillingDepartment.id)
				}
				text =
					data.fillingDepartment.parent.name +
					'-' +
					data.fillingDepartment.name +
					'<br />' +
					`${
						data.isCurrentUnit
							? `<p class='urging'>催一下<span class="urgingImg"></span></p>`
							: ''
					}`
			}

			fillList.push({
				label: `填报数据${data.isCurrentUnit ? '(当前节点:待填报)' : ''}`,
				text,
				type: status,
			})
		} else {
			let statusText = ''
			if (data.fillingDepartments?.length) {
				if (data.isCurrentUnit) {
					data.fillingDepartments.forEach((v) => {
						params.value.areaOrganizationUnitIds.push(v.id)
					})
				}
				text =
					data.fillingDepartments
						.map((item: any) => {
							let statusHtml = ''
							if (item.status === FillingDepartmentStatus.Rejected) {
								statusText = '已驳回'
								statusHtml = `${statusText}`
							} else if (item.status === FillingDepartmentStatus.Audited) {
								statusText = '已审核'
								statusHtml = `${statusText}`
							} else if (item.status === FillingDepartmentStatus.PendingAudit) {
								statusText = '待审核'
								statusHtml = `${statusText}`
							} else if (item.status === FillingDepartmentStatus.PendingWrite) {
								statusText = '待填写'
								statusHtml = `${statusText}`
							}
							return `
							${item.parent.name + '-' + item.name}
							<p class="description time">${statusHtml} ${item.mergeTime || ''}</p>
						`
						})
						.join('<br />') +
					`${
						data.isCurrentUnit
							? `<p class='urging'>催一下<span class="urgingImg"></span></p>`
							: ''
					}`
			} else {
				if (data.isCurrentUnit) {
					params.value.areaOrganizationUnitIds.push(data.fillingDepartment.id)
				}
				text =
					data.fillingDepartment.parent.name +
					'-' +
					data.fillingDepartment.name +
					'<br />' +
					`${
						data.isCurrentUnit
							? `<p class='urging'>催一下<span class="urgingImg"></span></p>`
							: ''
					}`
			}

			if (data.isCurrentUnit) {
				if (statusText) {
					statusText = '(当前节点:' + statusText + ')'
				} else {
					statusText = '(当前节点)'
				}
			}

			fillList.push({
				label: `填报数据${statusText}`,
				text,
				type: status,
			})
		}
	}

	return fillList
}

const getAudits = (data: any) => {
	let auditdata: any = []
	if (data.dataAuditProcess) {
		auditdata = data.dataAuditProcess.units.map((item: any) => {
			const hasInvalid = item.tasks.some((x: any) => !x.isAgree && x.isAgree !== null)
			const hasParentInvalid = data.parentDataAuditProcess
				? data.parentDataAuditProcess.units.some((x: any) =>
						x.tasks.some((y: any) => !y.isAgree && y.isAgree !== null)
				  )
				: false
			const hasCompleted = item.isCountersign
				? item.tasks.every((x: any) => x.isAgree)
				: item.tasks.some((x: any) => x.isAgree)

			let status = ''
			if (item.isCurrentUnit) {
				status = StatusType.Warning
			} else if (hasInvalid || hasParentInvalid) {
				status = StatusType.Danger
			} else if (hasCompleted) {
				status = StatusType.Success
			}
			if (item.isCurrentUnit) {
				item.tasks.forEach((x) => {
					params.value.userIds.push(x.auditor.id)
				})
			}
			return {
				label: `数据审核${item.isCurrentUnit ? '(当前节点)' : ''}`,
				text:
					item.tasks
						.map((subitem: any) => {
							let statusHtml = ''
							if (subitem.isAgree) {
								statusHtml = '已通过'
							} else if (!subitem.isAgree && subitem.isAgree !== null) {
								statusHtml = '已驳回'
							} else {
								statusHtml = '待审核'
							}

							return `
							${subitem.auditor.largeDepartmentName}-${subitem.auditor.departmentName}-${subitem.auditor.name}
							${statusHtml ? `<p class="time description">${statusHtml} ${subitem.operationTime || ''}</p>` : ''}
							${subitem.description ? '<p class="description">审核说明：' + subitem.description + '</p>' : ''}
						`
						})
						.join('<br />') +
					`<p class="fb-1 mg-top-5 red">${
						item.isCountersign ? '全部审核人都需要审核' : '任意一人审核通过'
					}</p>` +
					`${
						item.isCurrentUnit
							? `<p class='urging'>催一下<span class="urgingImg"></span></p>`
							: ''
					}`,
				type: status,
			}
		})
	}
	return auditdata
}

const getAuditParnets = (data: any) => {
	let auditdata: any = []
	if (data.parentDataAuditProcess) {
		auditdata = data.parentDataAuditProcess.units.map((item: any) => {
			const hasInvalid = item.tasks.some((x: any) => !x.isAgree && x.isAgree !== null)
			const hasCompleted = item.isCountersign
				? item.tasks.every((x: any) => x.isAgree)
				: item.tasks.some((x: any) => x.isAgree)

			let status = ''
			if (item.isCurrentUnit) {
				status = StatusType.Warning
			} else if (hasInvalid) {
				status = StatusType.Danger
			} else if (hasCompleted) {
				status = StatusType.Success
			}
			if (item.isCurrentUnit) {
				item.tasks.forEach((x: any) => {
					if (x.isAgree) {
						return
					} else {
						params.value.userIds.push(x.auditor.id)
					}
				})
			}
			return {
				label: `数据审核${item.isCurrentUnit ? '(当前节点)' : ''}`,
				text:
					item.tasks
						.map((subitem: any) => {
							let statusHtml = ''
							if (subitem.isAgree) {
								statusHtml = '已通过'
							} else if (!subitem.isAgree && subitem.isAgree !== null) {
								statusHtml = '已驳回'
							} else {
								statusHtml = '待审核'
							}
							return `
							${subitem.auditor.largeDepartmentName}-${subitem.auditor.departmentName}-${subitem.auditor.name}
							<p class="time description">${statusHtml} ${subitem.operationTime || ''}</p>
							${subitem.description ? '<p class="description">审核说明：' + subitem.description + '</p>' : ''}
						`
						})
						.join('<br />') +
					`<p class="fb-1 mg-top-5 red">${
						item.isCountersign ? '全部审核人都需要审核' : '任意一人审核通过'
					}</p>` +
					`${
						item.isCurrentUnit
							? `<p class='urging'>催一下<span class="urgingImg"></span></p>`
							: ''
					}`,
				type: status,
			}
		})
	}
	return auditdata
}

const getEnd = (data: any) => {
	const end = data.status === ReportsFlowStatusEnum.Completed
	emits('update:end', end)
	return {
		label: '任务完成',
		type:
			// (data.dataAuditProcess?.isEnd && data.dataAuditProcess?.isApproved) ||
			// (data.fillingDepartments.length && data.isCurrentUnit === null && data.fillingDepartments) ||
			// (!data.dataAuditProcess && data.isCurrentUnit === null)
			end ? StatusType.Success : '',
	}
}

const getEndDetail = (data: any) => {
	let completed = false

	if (data.dataAuditProcess) {
		completed =
			data.dataAuditProcess.isEnd &&
			data.dataAuditProcess.isApproved &&
			data.isCurrentUnit === null
	}

	if (!data.dataAuditProcess && data.isCurrentUnit === null) {
		completed = true
	}

	if (data.parentDataAuditProcess) {
		completed =
			data.parentDataAuditProcess.isEnd &&
			data.parentDataAuditProcess.isApproved &&
			data.isCurrentUnit === null
	}

	return {
		label: '任务完成',
		type: completed ? StatusType.Success : '',
	}
}

const getExportProcessStart = () => {
	return {
		label: '当前环节',
		text: '您正在导出数据',
		type: StatusType.Warning,
	}
}

const getExportEnd = () => {
	return {
		label: '导出文件生成',
		text: '下载文件导出完成',
		type: 'primary',
	}
}

// 业务表填报流程
const getLedgerProcessStart = () => {
	return {
		label: '流程开始',
		text: '提交流程',
		type: 'success',
	}
}
const getLedgerProcessAuditors = (data: any) => {
	let auditdata: any = []
	let agreeUserIds: any = []
	let currentIds: any = []
	if (data.units) {
		auditdata = data.units.map((item: any) => {
			const hasInvalid = item.tasks.some((x: any) => !x.isAgree && x.isAgree !== null)
			const hasCompleted = item.isCountersign
				? item.tasks.every((x: any) => x.isAgree)
				: item.tasks.some((x: any) => x.isAgree)

			let status = ''
			if (item.isCurrentUnit) {
				status = StatusType.Warning
			} else if (hasInvalid) {
				status = StatusType.Danger
			} else if (hasCompleted) {
				status = StatusType.Success
			}

			return {
				label: `数据审核${item.isCurrentUnit ? '(当前节点)' : ''}`,
				text:
					item.tasks
						.map((subitem: any) => {
							let statusHtml = ''
							if (subitem.isAgree) {
								statusHtml = '已通过'
								if (item.isCurrentUnit) {
									agreeUserIds.push(subitem.auditor.id)
								}
							} else if (!subitem.isAgree && subitem.isAgree !== null) {
								statusHtml = '已驳回'
							} else {
								statusHtml = '待审核'
							}

							if (item.isCurrentUnit) {
								currentIds.push(subitem.auditor.id)
							}

							return `
								${subitem.auditor.largeDepartmentName}-${subitem.auditor.departmentName}-${subitem.auditor.name}
								${statusHtml ? `<p class="time description">${statusHtml} ${subitem.operationTime || ''}</p>` : ''}
								${subitem.description ? '<p class="description">审核说明：' + subitem.description + '</p>' : ''}
							`
						})
						.join('<br />') +
					`<p class="fb-1 mg-top-5 red">${
						item.isCountersign ? '全部审核人都需要审核' : '任意一人审核通过'
					}</p>`,
				// +
				// // && props.type !=="audit"
				// `${
				// 	item.isCurrentUnit
				// 		? `<p class='urging'>催一下<span class="urgingImg"></span></p>`
				// 		: ''
				// }`
				type: status,
			}
		})
	}
	emits('agree', agreeUserIds, currentIds)
	return auditdata
}
const getLedgerProcessEnd = (data: any) => {
	emits('update:end', data.isEnd)
	return {
		label: '流程结束',
		type: data.isEnd ? 'success' : '',
	}
}

const handleLedgerRecode = (data: any) => {
	const start = getLedgerProcessStart()
	const auditdata = getLedgerProcessAuditors(data)
	const end = getLedgerProcessEnd(data)
	recodeList.value = [start, ...auditdata, end]
	nextTick(() => {
		const buttons = document.getElementsByClassName('urging')
		console.log(props.type)
		buttons[0].addEventListener('click', setIsUrging, true)
	})
}

const handleReportRecode = (data: any) => {
	console.log(12333, data)
	const start = getStartNode(data)
	const issue = getIssueNode(data)
	const fills = getFill(data)
	const auditdata = data.fillingMode === TaskType.InternalTask ? getAudits(data) : []
	const end = getEnd(data)

	recodeList.value = [start, ...issue, fills, ...auditdata, end]
	nextTick(() => {
		try {
			const buttons = document.getElementsByClassName('urging')
			buttons[0].addEventListener('click', setIsUrging, true)
		} catch (err) {
			console.log(err)
		}
	})
}
const isUrging = ref(false)
const setIsUrging = () => {
	isUrging.value = true
}
const urgingList = ref(['0', '1'])
// 催办确定
const onUrging = () => {
	if (urgingList.value.length == 0) return ElMessage.error('请选择通知方式')
	urgeingLoading.value = true
	let urgingMessageTypes: any = []
	urgingList.value.forEach((item: any) => {
		if (item == '0') {
			urgingMessageTypes.push(0)
		} else {
			urgingMessageTypes.push(Number(item))
		}
	})
	let urgingId: any = route.query.id || route.query.reportTaskId
	// if(statusText.value == 3){
	// 	urgingId = route.query.id
	// }
	console.log(currentData.value)
	console.log(params.value)

	urging(urgingId, {
		urgingMessageTypes: urgingMessageTypes,
		reportTaskType: route.query.id ? 0 : 1,
		...params.value,
	}).then((res) => {
		console.log(res)
		ElMessage.success('催办成功')
		isUrging.value = false
		urgeingLoading.value = false
	})
}
// 催办取消
const closeUrging = () => {
	urgingList.value = ['0', '1']
	urgeingLoading.value = false
	isUrging.value = false
}
const handleReportRecodeDetail = (data: any) => {
	console.log(45666, data)
	const fills = getFillDetail(data)
	const audits = getAudits(data)
	const auditParnets = getAuditParnets(data)
	const end = getEndDetail(data)

	recodeList.value = [...fills, ...audits, ...auditParnets, end]
	nextTick(() => {
		try {
			const buttons = document.getElementsByClassName('urging')
			buttons[0].addEventListener('click', setIsUrging, true)
		} catch (err) {
			console.log(err)
		}
	})
}

const handleExportRecode = (data: any) => {
	const start = getExportProcessStart()
	const auditdata = getLedgerProcessAuditors(data)
	const end = getExportEnd()
	recodeList.value = [start, ...auditdata, end]
	nextTick(() => {
		const buttons = document.getElementsByClassName('urging')
		buttons[0].addEventListener('click', setIsUrging, true)
	})
}

const handleRecode = (data: any) => {
	if (props.isLedgerRecode) {
		handleLedgerRecode(data)
	} else if (props.isExport) {
		handleExportRecode(data)
	} else if (!props.isDetail) {
		handleReportRecode(data)
	} else {
		handleReportRecodeDetail(data)
	}
}
const currentData = ref()
const onClickText = (e: any, item: any) => {
	emits('clickText', e, item)
}
const urgeingLoading = ref(false)
watch(
	() => props.data,
	(newVal) => {
		currentData.value = newVal
		handleRecode(newVal)
	}
)
</script>
<template>
	<div class="business-process">
		<div v-if="!recodeList.length" class="df aic jcc pd-10 w-full">
			<LoadingTransition />
		</div>
		<Record
			sort="ace"
			:data="recodeList"
			:fadeOut="false"
			:lastLine="false"
			@click-text="onClickText"
		></Record>
		<Dialog
			v-model="isUrging"
			title="报表催办"
			width="600"
			@clickConfirm="onUrging"
			@close="closeUrging"
			:loading="urgeingLoading"
		>
			<div>
				<p>将批量通知各未完成填报部门尽快完成填报</p>
				<el-checkbox-group v-model="urgingList" style="margin-top: 10px">
					<el-checkbox label="发送系统内消息" value="0" />
					<el-checkbox label="发送渝快政工作通知" value="1" />
					<el-checkbox label="发送渝快政Ding消息" value="2" />
				</el-checkbox-group>
			</div>
		</Dialog>
	</div>
</template>
<style scoped lang="scss">
:deep(p[data-id]) {
	cursor: pointer;
	color: var(--z-main);
	text-decoration: underline;
}
:deep(.description) {
	&.time {
		align-items: center;
		display: flex;
		padding-top: 10px;
		padding-bottom: 10px;
		i {
			font-size: 14px;
			padding-right: 5px;
			&.success {
				color: var(--z-success);
			}
			&.danger {
				color: var(--z-danger);
			}
		}
	}
	padding-bottom: 10px;
}
:deep(.urging) {
	display: flex;
	align-items: center;
	color: #1764ce;
	cursor: pointer;
}
:deep(.urgingImg) {
	height: 16px;
	width: 16px;
	background: url('../assets/image/urging.png');
	background-size: cover;
}
</style>
