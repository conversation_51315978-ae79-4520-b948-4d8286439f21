<script setup lang="ts">
import Bar from './charts/Bar.vue'
import Pie from './charts/Pie.vue'
import Line from './charts/Line.vue'

const props = defineProps({
	item: {
		type: Object,
		default: () => ({}),
	},
})

const intentionMap = {
	Analysis: 'analysis',
	Bar: 'bar',
	Pie: 'pie',
	Line: 'line',
}
</script>
<template>
	<!-- 柱状图 -->
	<Bar v-if="item.type === intentionMap.Bar" :data="item.data" width="100%" height="300px"></Bar>
	<!-- 折线图 -->
	<Line
		v-else-if="item.type === intentionMap.Line"
		:data="item.data"
		width="100%"
		height="300px"
	></Line>
	<!-- 饼图 -->
	<Pie
		v-else-if="item.type === intentionMap.Pie"
		:data="item.data"
		width="100%"
		height="300px"
	></Pie>
</template>
<style scoped></style>
