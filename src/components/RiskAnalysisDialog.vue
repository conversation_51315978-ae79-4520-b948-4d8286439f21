<!-- 风险分析弹窗组件 -->
<script setup lang="ts" name="RiskAnalysisDialog">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
interface Props {
  visible: boolean
  taskId?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  taskId: ''
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
}>()

// 响应式状态
const loading = ref(false)
const riskProcessingVisible = ref(false)
const knowledgeSearchLoading = ref(false)
const knowledgeSearchResult = ref('')
const knowledgeSearchKeyword = ref('')

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 风险分析数据
const riskAnalysisData = ref([
  {
    id: 1,
    riskType: '进度风险',
    riskLevel: '高',
    riskDescription: '子任务依赖关系复杂，可能导致进度延误',
    probability: '70%',
    impact: '高',
    riskScore: 8.5,
    status: '待处理'
  },
  {
    id: 2,
    riskType: '资源风险',
    riskLevel: '中',
    riskDescription: '关键人员可能存在时间冲突',
    probability: '50%',
    impact: '中',
    riskScore: 6.0,
    status: '已识别'
  },
  {
    id: 3,
    riskType: '技术风险',
    riskLevel: '低',
    riskDescription: '数据接口可能存在兼容性问题',
    probability: '30%',
    impact: '低',
    riskScore: 3.5,
    status: '监控中'
  }
])

// 风险处理数据
const riskProcessingData = ref([
  {
    id: 1,
    riskId: 1,
    processingMeasure: '调整任务优先级，增加关键路径监控',
    responsiblePerson: '项目经理',
    expectedCompletionTime: '2024-01-15',
    status: '进行中'
  },
  {
    id: 2,
    riskId: 2,
    processingMeasure: '提前协调资源，制定备用方案',
    responsiblePerson: '资源协调员',
    expectedCompletionTime: '2024-01-10',
    status: '待开始'
  }
])

// 子任务处理选项
const subTaskProcessingOptions = ref([
  { label: '继续执行', value: 'continue' },
  { label: '暂停执行', value: 'pause' },
  { label: '调整优先级', value: 'adjust_priority' },
  { label: '重新分配资源', value: 'reallocate_resources' }
])

const selectedProcessingOption = ref('')

// 方法
const handleClose = () => {
  dialogVisible.value = false
  emit('close')
}

const handleRiskProcessing = () => {
  riskProcessingVisible.value = true
}

const handleExportRiskAnalysis = () => {
  ElMessage.success('风险分析报告导出成功')
}

const handleKnowledgeSearch = async () => {
  if (!knowledgeSearchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }

  knowledgeSearchLoading.value = true
  knowledgeSearchResult.value = ''

  try {
    // 模拟搜索延迟
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 1000))
    
    // 模拟搜索结果
    knowledgeSearchResult.value = `基于关键词"${knowledgeSearchKeyword.value}"的搜索结果：
    
1. 风险管理最佳实践：建议采用PMBOK风险管理流程，包括风险识别、定性分析、定量分析、风险应对规划和风险监控。

2. 类似项目经验：在类似的数据收集项目中，主要风险点集中在数据质量、时间进度和人员协调方面。

3. 应对建议：
   - 建立风险预警机制
   - 制定应急预案
   - 加强沟通协调
   - 定期风险评估更新`
  } catch (error) {
    ElMessage.error('搜索失败，请重试')
  } finally {
    knowledgeSearchLoading.value = false
  }
}

const handleRiskProcessingConfirm = () => {
  if (!selectedProcessingOption.value) {
    ElMessage.warning('请选择子任务处理方式')
    return
  }

  ElMessage.success('风险处理方案已确认')
  riskProcessingVisible.value = false
}

// 获取风险等级标签类型
const getRiskLevelType = (level: string) => {
  const typeMap = {
    '高': 'danger',
    '中': 'warning', 
    '低': 'success'
  }
  return typeMap[level as keyof typeof typeMap] || 'info'
}

// 获取状态标签类型
const getStatusType = (status: string) => {
  const typeMap = {
    '待处理': 'danger',
    '已识别': 'warning',
    '监控中': 'primary',
    '已处理': 'success'
  }
  return typeMap[status as keyof typeof typeMap] || 'info'
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 弹窗打开时的初始化逻辑
    loading.value = true
    setTimeout(() => {
      loading.value = false
    }, 1000)
  }
})
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="子任务风险分析"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="risk-analysis-content" v-loading="loading" element-loading-text="正在分析风险...">
      <!-- 操作按钮区域 -->
      <div class="risk-analysis-header">
        <el-button type="primary" @click="handleRiskProcessing">风险处理</el-button>
        <el-button @click="handleExportRiskAnalysis">导出</el-button>
      </div>

      <!-- 风险分析表格 -->
      <div class="risk-analysis-table">
        <el-table :data="riskAnalysisData" style="width: 100%" height="300">
          <el-table-column prop="riskType" label="风险类型" width="100" />
          <el-table-column prop="riskLevel" label="风险等级" width="100">
            <template #default="{ row }">
              <el-tag :type="getRiskLevelType(row.riskLevel)">
                {{ row.riskLevel }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="riskDescription" label="风险描述" min-width="200" />
          <el-table-column prop="probability" label="发生概率" width="100" />
          <el-table-column prop="impact" label="影响程度" width="100" />
          <el-table-column prop="riskScore" label="风险评分" width="100" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>

    <!-- 风险处理弹窗 -->
    <el-dialog
      v-model="riskProcessingVisible"
      title="风险处理"
      width="700px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="risk-processing-content">
        <!-- 风险处理表格 -->
        <div class="risk-processing-table">
          <h4>风险处理措施</h4>
          <el-table :data="riskProcessingData" style="width: 100%">
            <el-table-column prop="processingMeasure" label="处理措施" min-width="200" />
            <el-table-column prop="responsiblePerson" label="责任人" width="120" />
            <el-table-column prop="expectedCompletionTime" label="预期完成时间" width="140" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.status === '进行中' ? 'primary' : 'info'">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 子任务处理 -->
        <div class="subtask-processing">
          <div class="processing-title">
            <span class="required-mark">*</span>
            子任务处理
          </div>
          <div class="processing-options">
            <span class="options-label">请选择处理方式：</span>
            <el-radio-group v-model="selectedProcessingOption">
              <el-radio
                v-for="option in subTaskProcessingOptions"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </div>
        </div>

        <!-- 知识搜索 -->
        <div class="knowledge-search">
          <div class="search-title">知识搜索</div>
          <div class="search-input-group">
            <el-input
              v-model="knowledgeSearchKeyword"
              placeholder="请输入搜索关键词"
              style="flex: 1; margin-right: 12px;"
            />
            <el-button 
              type="primary" 
              @click="handleKnowledgeSearch"
              :loading="knowledgeSearchLoading"
            >
              搜索
            </el-button>
          </div>
          <div v-if="knowledgeSearchResult" class="search-result">
            {{ knowledgeSearchResult }}
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="riskProcessingVisible = false">取消</el-button>
          <el-button type="primary" @click="handleRiskProcessingConfirm">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<style lang="scss" scoped>
.risk-analysis-content {
  .risk-analysis-header {
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
  }

  .risk-analysis-table {
    min-height: 200px;
  }
}

.risk-processing-content {
  .risk-processing-table {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }
  }

  .subtask-processing {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    .processing-title {
      margin-bottom: 12px;
      font-size: 14px;
      font-weight: 500;
      color: #303133;

      .required-mark {
        color: #f56c6c;
        margin-right: 4px;
      }
    }

    .processing-options {
      display: flex;
      align-items: center;
      gap: 12px;

      .options-label {
        font-size: 14px;
        color: #606266;
      }
    }
  }

  .knowledge-search {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    .search-title {
      margin-bottom: 12px;
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }

    .search-input-group {
      margin-bottom: 16px;
      display: flex;
      align-items: center;
    }

    .search-result {
      padding: 12px;
      background: white;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      font-size: 14px;
      color: #606266;
      line-height: 1.5;
      white-space: pre-line;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
