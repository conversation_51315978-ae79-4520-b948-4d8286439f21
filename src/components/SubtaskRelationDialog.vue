
<!-- 子任务关系展示弹窗 -->
<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { VueFlow, MarkerType } from '@vue-flow/core'
import type { Node, Edge } from '@vue-flow/core'
import {
  Setting,
  RefreshLeft,
  CopyDocument,
  FolderOpened,
  User,
  Lock,
  Unlock,
  Management,
  Download,
  Upload,
  Check,
  Close,
  Document
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import ParticipantPermissionDialog from '@/views/index/taskObjectiveDecomposition/components/ParticipantPermissionDialog.vue'
import type { PersonnelData } from '@/stores/taskObjectiveStore'

interface Props {
  visible?: boolean
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  title: '子任务关系展示'
})

const emit = defineEmits(['update:visible', 'close'])

// 弹窗状态
const dialogVisible = ref(props.visible)
const activeTab = ref('business')

// 工具栏控制变量
const zoomValue = ref(120)
const rotationValue = ref(23)
const panX = ref(0)
const panY = ref(0)

// Vue Flow 实例引用
const vueFlowRef = ref(null)

// 节点选中状态管理
const selectedNodeId = ref<string | null>(null)

// 操作历史管理
interface HistoryRecord {
  type: 'move' | 'copy' | 'batch_copy'
  timestamp: number
  data: any
}

const operationHistory = ref<HistoryRecord[]>([])
const MAX_HISTORY_SIZE = 20

// 节点拖拽前的位置记录
const nodeDragStartPositions = ref<Record<string, { x: number; y: number }>>({})

// 权限设置相关
const showPermissionDialog = ref(false)
const mockPersonnelData = ref<PersonnelData[]>([
  { id: '1', name: '张三', department: '技术部', role: '开发工程师', permissions: ['新增', '编辑'] },
  { id: '2', name: '李四', department: '产品部', role: '产品经理', permissions: ['编辑'] },
  { id: '3', name: '王五', department: '运营部', role: '运营专员', permissions: ['新增', '删除'] },
  { id: '4', name: '赵六', department: '管理部', role: '项目经理', permissions: ['新增', '编辑', '删除'] }
])

// 节点权限数据
const nodePermissions = ref<Record<string, any>>({})

// 复制按钮状态
const copyButtonDisabled = computed(() => !selectedNodeId.value)
const batchCopyButtonDisabled = computed(() => !selectedNodeId.value)

// 锁定状态管理
const isLocked = ref(false)
const isSubmitted = ref(false)
const isLoading = ref(false)

// 弹窗状态管理
const showUnlockManageDialog = ref(false)
const showHistoryDialog = ref(false)

// 解锁管理数据
const unlockManageData = ref([
  { id: '01', name: '张三', hasPermission: true },
  { id: '02', name: '李四', hasPermission: true },
  { id: '03', name: '王五', hasPermission: true },
  { id: '04', name: 'XXX', hasPermission: true }
])

// 解锁条件
const unlockConditions = ref({
  taskPending: true,
  taskNotExist: true
})

// 历史记录数据
const historyData = ref([
  { id: '01', operator: '张三', time: '2025-06-11', content: '解除任务横向关联' },
  { id: '02', operator: '李四', time: '2025-05-09', content: '解除任务横向关联' },
  { id: '03', operator: '王五', time: '2025-05-05', content: '解除任务横向关联' }
])

// 监听props变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
})

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  emit('update:visible', false)
  emit('close')
}

// 确认操作
const handleConfirm = () => {
  // 这里可以添加确认逻辑
  handleClose()
}

// Vue Flow 事件处理
const onViewportChange = (viewport: any) => {
  zoomValue.value = Math.round(viewport.zoom * 100)
  panX.value = viewport.x
  panY.value = viewport.y
}

const onZoomChange = (zoom: number) => {
  zoomValue.value = Math.round(zoom * 100)
}

const onPanChange = (pan: { x: number; y: number }) => {
  panX.value = pan.x
  panY.value = pan.y
}

// 监听滑块变化并应用到Vue Flow
watch(zoomValue, (newZoom) => {
  if (vueFlowRef.value) {
    try {
      const instance = vueFlowRef.value as any
      // 使用 zoomTo 方法而不是 setZoom
      if (instance.zoomTo) {
        instance.zoomTo(newZoom / 100)
      } else if (instance.setViewport) {
        // 如果 zoomTo 不可用，使用 setViewport
        instance.setViewport({
          x: panX.value,
          y: panY.value,
          zoom: newZoom / 100
        })
      }
    } catch (error) {
      console.warn('Failed to set zoom:', error)
    }
  }
})

watch(rotationValue, (newRotation) => {
  if (vueFlowRef.value) {
    try {
      const container = (vueFlowRef.value as any).$el
      if (container) {
        container.style.transform = `rotate(${newRotation}deg)`
      }
    } catch (error) {
      console.warn('Failed to set rotation:', error)
    }
  }
})

// 手动缩放方法
const applyZoom = () => {
  if (vueFlowRef.value) {
    try {
      const instance = vueFlowRef.value as any
      const zoomLevel = zoomValue.value / 100

      // 尝试多种方法来设置缩放
      if (instance.zoomTo) {
        instance.zoomTo(zoomLevel)
      } else if (instance.setViewport) {
        instance.setViewport({
          x: panX.value,
          y: panY.value,
          zoom: zoomLevel
        })
      } else if (instance.fitView) {
        // 如果其他方法都不行，先fitView然后调整
        instance.fitView()
        setTimeout(() => {
          if (instance.zoomTo) {
            instance.zoomTo(zoomLevel)
          }
        }, 100)
      }
      console.log('Applied zoom:', zoomLevel)
    } catch (error) {
      console.warn('Failed to apply zoom:', error)
    }
  }
}

// 操作历史管理方法
const addToHistory = (type: 'move' | 'copy' | 'batch_copy', data: any) => {
  const record: HistoryRecord = {
    type,
    timestamp: Date.now(),
    data: JSON.parse(JSON.stringify(data)) // 深拷贝
  }

  operationHistory.value.unshift(record)

  // 限制历史记录数量
  if (operationHistory.value.length > MAX_HISTORY_SIZE) {
    operationHistory.value = operationHistory.value.slice(0, MAX_HISTORY_SIZE)
  }
}

// 撤销移动操作
const undoLastMove = () => {
  // 找到最近的移动操作记录
  const lastMoveRecord = operationHistory.value.find(record => record.type === 'move')

  if (!lastMoveRecord) {
    ElMessage.warning('没有可撤销的移动操作')
    return
  }

  try {
    // 恢复节点位置
    const { nodeId, oldPosition } = lastMoveRecord.data
    const node = businessNodes.value.find(node => node.id === nodeId)

    if (node) {
      // 直接修改节点位置
      node.position.x = oldPosition.x
      node.position.y = oldPosition.y

      // 从历史记录中移除已撤销的操作
      const recordIndex = operationHistory.value.indexOf(lastMoveRecord)
      operationHistory.value.splice(recordIndex, 1)

      console.log(`撤销节点 ${nodeId} 移动，恢复到位置:`, oldPosition)
      ElMessage.success('撤销移动操作成功')
    } else {
      ElMessage.error('未找到要撤销的节点')
    }
  } catch (error) {
    console.error('撤销操作失败:', error)
    ElMessage.error('撤销操作失败')
  }
}

// 节点选中处理
const onNodeClick = (event: any) => {
  const nodeId = event.node?.id
  if (nodeId) {
    selectedNodeId.value = selectedNodeId.value === nodeId ? null : nodeId
  }
}

// 节点拖拽开始处理
const onNodeDragStart = (event: any) => {
  const { node } = event
  if (node) {
    // 记录拖拽开始时的位置
    nodeDragStartPositions.value[node.id] = {
      x: node.position.x,
      y: node.position.y
    }
  }
}

// 节点拖拽结束处理
const onNodeDragStop = (event: any) => {
  const { node } = event
  if (node && nodeDragStartPositions.value[node.id]) {
    const oldPosition = nodeDragStartPositions.value[node.id]
    const newPosition = { x: node.position.x, y: node.position.y }

    // 只有位置真正发生变化时才记录历史
    if (oldPosition.x !== newPosition.x || oldPosition.y !== newPosition.y) {
      addToHistory('move', {
        nodeId: node.id,
        oldPosition: oldPosition,
        newPosition: newPosition
      })
    }

    // 清除临时记录
    delete nodeDragStartPositions.value[node.id]
  }
}

// 生成新的节点ID
const generateNodeId = () => {
  return `node_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

// 计算新节点位置
const calculateNewNodePosition = (originalPosition: { x: number; y: number }) => {
  return {
    x: originalPosition.x + 200, // 向右偏移200px
    y: originalPosition.y + 50   // 向下偏移50px
  }
}

// 单个节点复制
const copySelectedNode = () => {
  if (!selectedNodeId.value) {
    ElMessage.warning('请先选择要复制的节点')
    return
  }

  try {
    const originalNode = businessNodes.value.find(node => node.id === selectedNodeId.value)
    if (!originalNode) {
      ElMessage.error('未找到选中的节点')
      return
    }

    // 创建新节点
    const newNodeId = generateNodeId()
    const newNode: Node = {
      ...JSON.parse(JSON.stringify(originalNode)), // 深拷贝
      id: newNodeId,
      position: calculateNewNodePosition(originalNode.position)
    }

    // 添加新节点
    businessNodes.value.push(newNode)

    // 复制相关的连线
    const relatedEdges = businessEdges.value.filter(edge =>
      edge.source === selectedNodeId.value || edge.target === selectedNodeId.value
    )

    relatedEdges.forEach(edge => {
      const newEdge: Edge = {
        ...JSON.parse(JSON.stringify(edge)),
        id: `${edge.id}_copy_${Date.now()}`,
        source: edge.source === selectedNodeId.value ? newNodeId : edge.source,
        target: edge.target === selectedNodeId.value ? newNodeId : edge.target
      }
      businessEdges.value.push(newEdge)
    })

    // 记录复制操作到历史
    addToHistory('copy', {
      originalNodeId: selectedNodeId.value,
      newNodeId,
      newEdges: relatedEdges.map(edge => `${edge.id}_copy_${Date.now()}`)
    })

    // 选中新创建的节点
    selectedNodeId.value = newNodeId

    ElMessage.success('节点复制成功')
  } catch (error) {
    console.error('复制节点失败:', error)
    ElMessage.error('复制节点失败')
  }
}

// 获取节点的所有直接下级节点
const getDirectChildNodes = (nodeId: string): string[] => {
  return businessEdges.value
    .filter(edge => edge.source === nodeId)
    .map(edge => edge.target)
}

// 批量复制节点及其下级节点
const batchCopySelectedNode = () => {
  if (!selectedNodeId.value) {
    ElMessage.warning('请先选择要复制的节点')
    return
  }

  try {
    const nodesToCopy = [selectedNodeId.value]
    const childNodes = getDirectChildNodes(selectedNodeId.value)
    nodesToCopy.push(...childNodes)

    const nodeIdMapping: Record<string, string> = {}
    const newNodes: Node[] = []
    const newEdges: Edge[] = []

    // 复制所有节点
    nodesToCopy.forEach((nodeId, index) => {
      const originalNode = businessNodes.value.find(node => node.id === nodeId)
      if (originalNode) {
        const newNodeId = generateNodeId()
        nodeIdMapping[nodeId] = newNodeId

        const newNode: Node = {
          ...JSON.parse(JSON.stringify(originalNode)),
          id: newNodeId,
          position: {
            x: originalNode.position.x + 250 + (index * 20), // 批量复制时稍微错开位置
            y: originalNode.position.y + 80 + (index * 20)
          }
        }
        newNodes.push(newNode)
      }
    })

    // 复制相关连线
    businessEdges.value.forEach(edge => {
      if (nodeIdMapping[edge.source] && nodeIdMapping[edge.target]) {
        const newEdge: Edge = {
          ...JSON.parse(JSON.stringify(edge)),
          id: `${edge.id}_batch_copy_${Date.now()}`,
          source: nodeIdMapping[edge.source],
          target: nodeIdMapping[edge.target]
        }
        newEdges.push(newEdge)
      }
    })

    // 添加到流程图
    businessNodes.value.push(...newNodes)
    businessEdges.value.push(...newEdges)

    // 记录批量复制操作到历史
    addToHistory('batch_copy', {
      originalNodeIds: nodesToCopy,
      newNodeIds: Object.values(nodeIdMapping),
      newEdgeIds: newEdges.map(edge => edge.id)
    })

    // 选中主节点的复制版本
    selectedNodeId.value = nodeIdMapping[selectedNodeId.value]

    ElMessage.success(`批量复制成功，共复制了 ${newNodes.length} 个节点`)
  } catch (error) {
    console.error('批量复制失败:', error)
    ElMessage.error('批量复制失败')
  }
}

// 打开权限设置对话框
const openPermissionDialog = () => {
  if (!selectedNodeId.value) {
    ElMessage.warning('请先选择要设置权限的节点')
    return
  }
  showPermissionDialog.value = true
}

// 保存权限设置
const handlePermissionSave = (participants: any[]) => {
  if (selectedNodeId.value) {
    nodePermissions.value[selectedNodeId.value] = participants
    ElMessage.success('权限设置保存成功')

    // 这里可以添加持久化逻辑
    saveToStorage()
  }
}

// 锁定功能
const handleLock = () => {
  isLocked.value = true
  ElMessage.success('流程图已锁定，所有节点变为只读模式')
}

// 解锁功能
const handleUnlock = () => {
  isLocked.value = false
  ElMessage.success('流程图已解锁，可以进行编辑操作')
}

// 打开解锁管理弹窗
const openUnlockManageDialog = () => {
  showUnlockManageDialog.value = true
}

// 关闭解锁管理弹窗
const closeUnlockManageDialog = () => {
  showUnlockManageDialog.value = false
}

// 确认解锁管理设置
const confirmUnlockManage = () => {
  ElMessage.success('解锁管理设置已保存')
  closeUnlockManageDialog()
}

// 保存功能
const handleSave = async () => {
  isLoading.value = true
  try {
    // 模拟保存操作
    await new Promise(resolve => setTimeout(resolve, 1000))
    saveToStorage()
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    isLoading.value = false
  }
}

// 导出功能
const handleExport = async () => {
  isLoading.value = true
  try {
    // 模拟导出操作
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 创建下载链接
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    canvas.width = 800
    canvas.height = 600

    if (ctx) {
      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      ctx.fillStyle = '#333333'
      ctx.font = '16px Arial'
      ctx.fillText('流程图导出 - ' + new Date().toLocaleString(), 20, 30)
    }

    canvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `流程图_${new Date().getTime()}.png`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
        ElMessage.success('导出成功')
      }
    }, 'image/png')
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    isLoading.value = false
  }
}

// 提交功能
const handleSubmit = async () => {
  isLoading.value = true
  try {
    // 模拟提交操作
    await new Promise(resolve => setTimeout(resolve, 1000))
    isSubmitted.value = true
    ElMessage.success('提交成功')
  } catch (error) {
    ElMessage.error('提交失败')
  } finally {
    isLoading.value = false
  }
}

// 撤销提交功能
const handleRevokeSubmit = async () => {
  isLoading.value = true
  try {
    // 模拟撤销提交操作
    await new Promise(resolve => setTimeout(resolve, 1000))
    isSubmitted.value = false
    ElMessage.success('撤销提交成功')
  } catch (error) {
    ElMessage.error('撤销提交失败')
  } finally {
    isLoading.value = false
  }
}

// 打开历史记录弹窗
const openHistoryDialog = () => {
  showHistoryDialog.value = true
}

// 关闭历史记录弹窗
const closeHistoryDialog = () => {
  showHistoryDialog.value = false
}

// 业务报表流程图数据
const businessNodes = ref<Node[]>([
  {
    id: 'start',
    type: 'input',
    position: { x: 300, y: 30 },
    data: { label: '流程开始' },
    style: {
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      border: 'none',
      borderRadius: '25px',
      width: '100px',
      height: '50px',
      fontSize: '13px',
      fontWeight: '500',
      boxShadow: '0 4px 12px rgba(102, 126, 234, 0.4)'
    }
  },
  {
    id: 'task1',
    position: { x: 80, y: 130 },
    data: {
      title: '任务',
      content: '填报人员部门工作人员或\n分管领导第一审核',
      type: 'task'
    },
    style: {
      background: 'linear-gradient(135deg, #FFA726 0%, #FF8A65 100%)',
      color: 'white',
      border: 'none',
      borderRadius: '8px',
      width: '160px',
      height: '70px',
      fontSize: '11px',
      fontWeight: '500',
      boxShadow: '0 4px 12px rgba(255, 167, 38, 0.4)'
    }
  },
  {
    id: 'task2',
    position: { x: 360, y: 130 },
    data: {
      title: '任务',
      content: '填报人员部门工作人员或\n分管领导第一审核',
      type: 'task'
    },
    style: {
      background: 'linear-gradient(135deg, #FFA726 0%, #FF8A65 100%)',
      color: 'white',
      border: 'none',
      borderRadius: '8px',
      width: '160px',
      height: '70px',
      fontSize: '11px',
      fontWeight: '500',
      boxShadow: '0 4px 12px rgba(255, 167, 38, 0.4)'
    }
  },
  {
    id: 'review1',
    position: { x: 80, y: 250 },
    data: {
      title: '审核节点',
      content: '填报人员部门工作人员及分\n管领导和部门负责',
      type: 'review'
    },
    style: {
      background: 'linear-gradient(135deg, #42A5F5 0%, #1E88E5 100%)',
      color: 'white',
      border: 'none',
      borderRadius: '8px',
      width: '160px',
      height: '70px',
      fontSize: '11px',
      fontWeight: '500',
      boxShadow: '0 4px 12px rgba(66, 165, 245, 0.4)'
    }
  },
  {
    id: 'task3',
    position: { x: 360, y: 250 },
    data: {
      title: '任务',
      content: '填报人员部门工作人员或\n分管领导第一审核',
      type: 'task'
    },
    style: {
      background: 'linear-gradient(135deg, #FFA726 0%, #FF8A65 100%)',
      color: 'white',
      border: 'none',
      borderRadius: '8px',
      width: '160px',
      height: '70px',
      fontSize: '11px',
      fontWeight: '500',
      boxShadow: '0 4px 12px rgba(255, 167, 38, 0.4)'
    }
  },
  {
    id: 'end',
    type: 'output',
    position: { x: 300, y: 370 },
    data: { label: '流程结束' },
    style: {
      background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      color: 'white',
      border: 'none',
      borderRadius: '25px',
      width: '100px',
      height: '50px',
      fontSize: '13px',
      fontWeight: '500',
      boxShadow: '0 4px 12px rgba(250, 112, 154, 0.4)'
    }
  }
])

const businessEdges = ref<Edge[]>([
  {
    id: 'e-start-task1',
    source: 'start',
    target: 'task1',
    animated: true,
    style: {
      stroke: '#667eea',
      strokeWidth: 3,
      strokeDasharray: '5,5'
    },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#667eea'
    }
  },
  {
    id: 'e-start-task2',
    source: 'start',
    target: 'task2',
    animated: true,
    style: {
      stroke: '#667eea',
      strokeWidth: 3,
      strokeDasharray: '5,5'
    },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#667eea'
    }
  },
  {
    id: 'e-task1-review1',
    source: 'task1',
    target: 'review1',
    animated: true,
    style: {
      stroke: '#4facfe',
      strokeWidth: 3,
      strokeDasharray: '5,5'
    },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#4facfe'
    }
  },
  {
    id: 'e-task2-task3',
    source: 'task2',
    target: 'task3',
    animated: true,
    style: {
      stroke: '#f093fb',
      strokeWidth: 3,
      strokeDasharray: '5,5'
    },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#f093fb'
    }
  },
  {
    id: 'e-review1-end',
    source: 'review1',
    target: 'end',
    animated: true,
    style: {
      stroke: '#fa709a',
      strokeWidth: 3,
      strokeDasharray: '5,5'
    },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#fa709a'
    }
  },
  {
    id: 'e-task3-end',
    source: 'task3',
    target: 'end',
    animated: true,
    style: {
      stroke: '#fa709a',
      strokeWidth: 3,
      strokeDasharray: '5,5'
    },
    markerEnd: {
      type: MarkerType.ArrowClosed,
      color: '#fa709a'
    }
  }
])

// 临时报表表格数据
const horizontalTableData = ref([
  {
    id: '01',
    department: '经济生态板块工作人员',
    level: '镇街',
    status: '已响应',
    time: '2025-06-01 13:00:01'
  },
  {
    id: '02',
    department: '公共服务工作人员',
    level: '镇街',
    status: '已响应',
    time: '2025-05-28 15:32:12'
  },
  {
    id: '03',
    department: '社会治理工作人员',
    level: '镇街',
    status: '待响应',
    time: '2025-05-27 14:20:30'
  },
  {
    id: '04',
    department: '城市建设工作人员',
    level: '镇街',
    status: '已响应',
    time: '2025-05-26 10:15:45'
  }
])

const verticalTableData = ref([
  {
    id: '01',
    department: '金汤街社区',
    level: '村（社区）',
    status: '已响应',
    time: '2025-06-01 13:00:01'
  },
  {
    id: '02',
    department: '华一路社区',
    level: '村（社区）',
    status: '已响应',
    time: '2025-05-28 15:32:12'
  },
  {
    id: '03',
    department: '掌卫路社区',
    level: '村（社区）',
    status: '已响应',
    time: '2025-05-27 09:04:35'
  },
  {
    id: '04',
    department: '新华路社区',
    level: '村（社区）',
    status: '待响应',
    time: '2025-05-26 16:22:18'
  },
  {
    id: '05',
    department: '建设路社区',
    level: '村（社区）',
    status: '已响应',
    time: '2025-05-25 11:30:25'
  }
])

// 分页数据
const horizontalPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 4
})

const verticalPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 5
})

// 倾向关系分析数据
const analysisData = reactive({
  dependency: {
    direct: 3,
    indirect: 1
  },
  duration: {
    average: '25分钟',
    longest: '45分钟'
  }
})

// 数据持久化
const STORAGE_KEY = 'subtask-relation-data'

// 保存数据到localStorage
const saveToStorage = () => {
  const data = {
    horizontalTableData: horizontalTableData.value,
    verticalTableData: verticalTableData.value,
    analysisData: analysisData,
    businessNodes: businessNodes.value,
    businessEdges: businessEdges.value,
    nodePermissions: nodePermissions.value,
    operationHistory: operationHistory.value,
    isLocked: isLocked.value,
    isSubmitted: isSubmitted.value,
    unlockManageData: unlockManageData.value,
    unlockConditions: unlockConditions.value,
    historyData: historyData.value,
    timestamp: Date.now()
  }
  localStorage.setItem(STORAGE_KEY, JSON.stringify(data))
}

// 从localStorage加载数据
const loadFromStorage = () => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    if (stored) {
      const data = JSON.parse(stored)
      if (data.horizontalTableData) {
        horizontalTableData.value = data.horizontalTableData
      }
      if (data.verticalTableData) {
        verticalTableData.value = data.verticalTableData
      }
      if (data.analysisData) {
        Object.assign(analysisData, data.analysisData)
      }
      if (data.businessNodes) {
        businessNodes.value = data.businessNodes
      }
      if (data.businessEdges) {
        businessEdges.value = data.businessEdges
      }
      if (data.nodePermissions) {
        nodePermissions.value = data.nodePermissions
      }
      if (data.operationHistory) {
        operationHistory.value = data.operationHistory
      }
      if (data.isLocked !== undefined) {
        isLocked.value = data.isLocked
      }
      if (data.isSubmitted !== undefined) {
        isSubmitted.value = data.isSubmitted
      }
      if (data.unlockManageData) {
        unlockManageData.value = data.unlockManageData
      }
      if (data.unlockConditions) {
        unlockConditions.value = data.unlockConditions
      }
      if (data.historyData) {
        historyData.value = data.historyData
      }
    }
  } catch (error) {
    console.warn('Failed to load data from localStorage:', error)
  }
}

// 监听数据变化并自动保存
watch([
  horizontalTableData,
  verticalTableData,
  analysisData,
  businessNodes,
  businessEdges,
  nodePermissions,
  operationHistory,
  isLocked,
  isSubmitted,
  unlockManageData,
  unlockConditions,
  historyData
], () => {
  saveToStorage()
}, { deep: true })

onMounted(() => {
  // 组件挂载后加载数据
  loadFromStorage()
})
</script>

<template>
  <Dialog
    v-model="dialogVisible"
    :title="title"
    width="1200px"
    height="800px"
    :destroy-on-close="true"
    @close="handleClose"
    class="subtask-relation-dialog"
  >
    <div class="dialog-content">
      <!-- Tab切换 -->
      <el-tabs v-model="activeTab" class="demo-tabs">
        <el-tab-pane label="业务报表子任务" name="business">
          <div class="business-content">
            <!-- 左侧流程图区域 -->
            <div class="flow-container">
              <div class="flow-toolbar">
                <span class="toolbar-item primary">
                  <el-icon><Setting /></el-icon>
                  可视化布局设置
                </span>
                <span class="toolbar-divider"></span>
                <span class="toolbar-item">
                  缩放:
                  <el-slider
                    v-model="zoomValue"
                    :min="50"
                    :max="200"
                    :step="10"
                    :show-tooltip="false"
                    size="small"
                    style="width: 80px; margin: 0 8px;"
                  />
                  <span class="zoom-value">{{ zoomValue }}%</span>
                  <el-button size="small" @click="applyZoom" style="margin-left: 5px;">应用</el-button>
                </span>
                <span class="toolbar-item">
                  平移X: <span class="position-value">{{ Math.round(panX) }}</span>
                </span>
                <span class="toolbar-item">
                  平移Y: <span class="position-value">{{ Math.round(panY) }}</span>
                </span>
                <span class="toolbar-item">
                  旋转:
                  <el-slider
                    v-model="rotationValue"
                    :min="0"
                    :max="360"
                    :step="1"
                    :show-tooltip="false"
                    size="small"
                    style="width: 60px; margin: 0 8px;"
                  />
                  <span class="rotation-value">{{ rotationValue }}°</span>
                </span>
              </div>
              
              <div class="flow-main-area">
                <div class="vue-flow-wrapper">
                  <!-- 浮动功能按钮面板 -->
                  <div class="floating-action-panel">
                    <el-button
                      type="primary"
                      size="small"
                      :icon="RefreshLeft"
                      @click="undoLastMove"
                      :disabled="!operationHistory.length || isLocked"
                      title="撤销移动"
                      class="floating-button"
                    >
                      撤销移动
                    </el-button>

                    <el-button
                      type="success"
                      size="small"
                      :icon="CopyDocument"
                      @click="copySelectedNode"
                      :disabled="copyButtonDisabled || isLocked"
                      title="复制选中节点"
                      class="floating-button"
                    >
                      复制
                    </el-button>

                    <el-button
                      type="warning"
                      size="small"
                      :icon="FolderOpened"
                      @click="batchCopySelectedNode"
                      :disabled="batchCopyButtonDisabled || isLocked"
                      title="批量复制节点及下级"
                      class="floating-button"
                    >
                      批量复制
                    </el-button>

                    <el-button
                      type="info"
                      size="small"
                      :icon="User"
                      @click="openPermissionDialog"
                      :disabled="!selectedNodeId || isLocked"
                      title="编辑权限"
                      class="floating-button"
                    >
                      编辑权限
                    </el-button>
                  </div>

                  <VueFlow
                  ref="vueFlowRef"
                  :nodes="businessNodes"
                  :edges="businessEdges"
                  :fit-view-on-init="true"
                  :nodes-draggable="!isLocked"
                  :zoom-on-scroll="!isLocked"
                  :pan-on-scroll="!isLocked"
                  :zoom-on-pinch="!isLocked"
                  :pan-on-drag="!isLocked"
                  :default-zoom="1.2"
                  :min-zoom="0.5"
                  :max-zoom="3"
                  @viewport-change="onViewportChange"
                  @zoom="onZoomChange"
                  @pan="onPanChange"
                  @node-click="onNodeClick"
                  @node-drag-start="onNodeDragStart"
                  @node-drag-stop="onNodeDragStop"
                  class="vue-flow-container"
                  :class="{ 'locked': isLocked }"
                >
                  <template #node-default="{ data, id }">
                    <div
                      class="custom-node"
                      :class="[
                        data.type,
                        { 'selected': selectedNodeId === id }
                      ]"
                    >
                      <div v-if="data.title" class="node-title">
                        {{ data.title }}
                      </div>
                      <div class="node-content">
                        {{ data.content || data.label }}
                      </div>
                      <!-- 权限指示器 -->
                      <div v-if="nodePermissions[id]" class="permission-indicator">
                        <el-icon><User /></el-icon>
                      </div>
                    </div>
                  </template>
                  </VueFlow>
                </div>
              </div>

              <!-- 流程图操作按钮组 -->
              <div class="flow-action-buttons">
                <div class="action-buttons-container">
                  <el-button
                    type="warning"
                    :icon="Lock"
                    @click="handleLock"
                    :disabled="isLocked || isLoading"
                    size="small"
                  >
                    锁定
                  </el-button>

                  <el-button
                    type="success"
                    :icon="Unlock"
                    @click="handleUnlock"
                    :disabled="!isLocked || isLoading"
                    size="small"
                  >
                    解锁
                  </el-button>

                  <el-button
                    type="info"
                    :icon="Management"
                    @click="openUnlockManageDialog"
                    :disabled="isLoading"
                    size="small"
                  >
                    解锁管理
                  </el-button>

                  <el-button
                    type="primary"
                    :icon="Download"
                    @click="handleSave"
                    :loading="isLoading"
                    size="small"
                  >
                    保存
                  </el-button>

                  <el-button
                    type="default"
                    :icon="Upload"
                    @click="handleExport"
                    :loading="isLoading"
                    size="small"
                  >
                    导出
                  </el-button>

                  <el-button
                    type="success"
                    :icon="Check"
                    @click="handleSubmit"
                    :disabled="isSubmitted || isLoading"
                    size="small"
                  >
                    提交
                  </el-button>

                  <el-button
                    type="danger"
                    :icon="Close"
                    @click="handleRevokeSubmit"
                    :disabled="!isSubmitted || isLoading"
                    size="small"
                  >
                    撤销提交
                  </el-button>

                  <el-button
                    type="default"
                    :icon="Document"
                    @click="openHistoryDialog"
                    :disabled="isLoading"
                    size="small"
                  >
                    子任务调整历史
                  </el-button>
                </div>

                <!-- 锁定状态提示 -->
                <div v-if="isLocked" class="lock-status-tip">
                  <el-icon><Lock /></el-icon>
                  <span>点击锁定后无法调整流程连线，解锁后可继续调整</span>
                </div>
              </div>
            </div>

            <!-- 右侧分析面板 -->
            <div class="analysis-panel">
              <div class="analysis-section">
                <h4>倾向关系分析</h4>
                <div class="analysis-item">
                  <span class="label">依赖任务数</span>
                  <div class="value">
                    <div>直接依赖: {{ analysisData.dependency.direct }}个</div>
                    <div>间接依赖: {{ analysisData.dependency.indirect }}个</div>
                  </div>
                </div>
                <div class="analysis-item">
                  <span class="label">完成时间</span>
                  <div class="value">
                    <div>平均: {{ analysisData.duration.average }}</div>
                    <div>最长: {{ analysisData.duration.longest }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="临时报表子任务" name="temporary">
          <div class="temporary-content">
            <!-- 左侧表格区域 -->
            <div class="table-container">
              <!-- 横向关系表格 -->
              <div class="table-section">
                <h4>横向关系</h4>
                <el-table :data="horizontalTableData" style="width: 100%" size="small">
                  <el-table-column prop="id" label="序号" width="80" />
                  <el-table-column prop="department" label="横向部门名称" min-width="150" />
                  <el-table-column prop="level" label="部门层级" width="100" />
                  <el-table-column prop="status" label="响应状态" width="100">
                    <template #default="scope">
                      <el-tag
                        :type="scope.row.status === '已响应' ? 'success' : 'warning'"
                        size="small"
                      >
                        {{ scope.row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="time" label="响应时间" width="150" />
                </el-table>
                <el-pagination
                  v-model:current-page="horizontalPagination.currentPage"
                  v-model:page-size="horizontalPagination.pageSize"
                  :total="horizontalPagination.total"
                  layout="prev, pager, next"
                  small
                  class="pagination"
                />
              </div>
              
              <!-- 纵向关系表格 -->
              <div class="table-section">
                <h4>纵向关系</h4>
                <el-table :data="verticalTableData" style="width: 100%" size="small">
                  <el-table-column prop="id" label="序号" width="80" />
                  <el-table-column prop="department" label="纵向部门名称" min-width="150" />
                  <el-table-column prop="level" label="部门层级" width="120" />
                  <el-table-column prop="status" label="响应状态" width="100">
                    <template #default="scope">
                      <el-tag
                        :type="scope.row.status === '已响应' ? 'success' : 'warning'"
                        size="small"
                      >
                        {{ scope.row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="time" label="响应时间" width="150" />
                </el-table>
                <el-pagination
                  v-model:current-page="verticalPagination.currentPage"
                  v-model:page-size="verticalPagination.pageSize"
                  :total="verticalPagination.total"
                  layout="prev, pager, next"
                  small
                  class="pagination"
                />
              </div>
            </div>
            
            <!-- 右侧分析面板 -->
            <div class="analysis-panel">
              <div class="analysis-section">
                <h4>倾向关系分析</h4>
                <div class="analysis-item">
                  <span class="label">依赖任务数</span>
                  <div class="value">
                    <div>直接依赖: {{ analysisData.dependency.direct }}个</div>
                    <div>间接依赖: {{ analysisData.dependency.indirect }}个</div>
                  </div>
                </div>
                <div class="analysis-item">
                  <span class="label">完成时间</span>
                  <div class="value">
                    <div>平均: {{ analysisData.duration.average }}</div>
                    <div>最长: {{ analysisData.duration.longest }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </Dialog>

  <!-- 权限设置对话框 -->
  <ParticipantPermissionDialog
    v-model="showPermissionDialog"
    :personnel-data="mockPersonnelData"
    @save="handlePermissionSave"
  />

  <!-- 解锁管理弹窗 -->
  <el-dialog
    v-model="showUnlockManageDialog"
    title="子任务关系解锁管理"
    width="600px"
    :destroy-on-close="true"
    @close="closeUnlockManageDialog"
  >
    <div class="unlock-manage-content">
      <el-table :data="unlockManageData" style="width: 100%">
        <el-table-column prop="id" label="序号" width="80" />
        <el-table-column prop="name" label="参与人" width="150" />
        <el-table-column label="子任务关系解锁权限" width="200">
          <template #default="scope">
            <el-switch
              v-model="scope.row.hasPermission"
              active-color="#13ce66"
              inactive-color="#ff4949"
            />
          </template>
        </el-table-column>
      </el-table>

      <div class="unlock-conditions">
        <h4>子任务关系解锁条件</h4>
        <div class="condition-items">
          <el-checkbox v-model="unlockConditions.taskPending">任务待执行</el-checkbox>
          <el-checkbox v-model="unlockConditions.taskNotExist">任务不存在子任务</el-checkbox>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeUnlockManageDialog">取消</el-button>
        <el-button type="primary" @click="confirmUnlockManage">确认</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 历史记录弹窗 -->
  <el-dialog
    v-model="showHistoryDialog"
    title="子任务关系调整历史"
    width="700px"
    :destroy-on-close="true"
    @close="closeHistoryDialog"
  >
    <div class="history-content">
      <el-table :data="historyData" style="width: 100%">
        <el-table-column prop="id" label="序号" width="80" />
        <el-table-column prop="operator" label="操作人" width="150" />
        <el-table-column prop="time" label="操作时间" width="150" />
        <el-table-column prop="content" label="调整内容" min-width="200" />
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeHistoryDialog">取消</el-button>
        <el-button type="primary" @click="closeHistoryDialog">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
/* 导入 Vue Flow 必要样式 */
@import '@vue-flow/core/dist/style.css';
@import '@vue-flow/core/dist/theme-default.css';
.subtask-relation-dialog {
  .dialog-content {
    height: 700px;

    .demo-tabs {
      height: 100%;

      :deep(.el-tabs__content) {
        height: calc(100% - 40px);
        padding: 0;
      }

      :deep(.el-tab-pane) {
        height: 100%;
      }
    }
    
    .business-content,
    .temporary-content {
      display: flex;
      height: 100%;
      gap: 20px;
    }
    
    .flow-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      border: 1px solid #e4e7ed;
      border-radius: 4px;

      .flow-main-area {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      
      .flow-toolbar {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        border-bottom: 1px solid #e4e7ed;
        font-size: 12px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);

        .toolbar-item {
          display: flex;
          align-items: center;
          gap: 6px;
          color: #606266;
          padding: 4px 8px;
          border-radius: 6px;
          transition: all 0.3s ease;

          &.primary {
            color: #409eff;
            font-weight: 600;
            background: rgba(64, 158, 255, 0.1);
            border: 1px solid rgba(64, 158, 255, 0.2);
          }

          &:hover {
            background: rgba(64, 158, 255, 0.05);
          }

          .zoom-value,
          .position-value,
          .rotation-value {
            font-weight: 600;
            color: #409eff;
            background: rgba(64, 158, 255, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            min-width: 35px;
            text-align: center;
          }
        }

        .toolbar-divider {
          width: 1px;
          height: 20px;
          background: #e4e7ed;
          margin: 0 5px;
        }
      }
      
      .vue-flow-wrapper {
        flex: 1;
        position: relative;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        background-size: 20px 20px;
        background-image:
          radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);

        .floating-action-panel {
          position: absolute;
          top: 15px;
          left: 15px;
          z-index: 1000;
          display: flex;
          flex-direction: column;
          gap: 8px;
          padding: 12px;
          background: rgba(255, 255, 255, 0.95);
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);

          .floating-button {
            min-width: 100px;
            justify-content: flex-start;
            font-size: 12px;
            white-space: nowrap;

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }
        }

        .vue-flow-container {
          width: 100%;
          height: 100%;
          background: transparent;

          &.locked {
            cursor: not-allowed;
            opacity: 0.8;
          }
        }
      }

      .flow-action-buttons {
        padding: 15px 20px;
        border-top: 1px solid #e4e7ed;
        background: #fafafa;

        .action-buttons-container {
          display: flex;
          gap: 10px;
          flex-wrap: wrap;
          justify-content: center;
          margin-bottom: 10px;
        }

        .lock-status-tip {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          color: #e6a23c;
          font-size: 12px;
          background: rgba(230, 162, 60, 0.1);
          padding: 8px 15px;
          border-radius: 4px;
          border: 1px solid rgba(230, 162, 60, 0.2);

          .el-icon {
            font-size: 14px;
          }
        }
      }
    }
    
    .table-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 20px;
      
      .table-section {
        flex: 1;
        
        h4 {
          margin: 0 0 10px 0;
          font-size: 14px;
          font-weight: 500;
        }
        
        .pagination {
          margin-top: 10px;
          justify-content: center;
        }
      }
    }
    
    .analysis-panel {
      width: 250px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 15px;
      background: #fafafa;
      
      .analysis-section {
        h4 {
          margin: 0 0 15px 0;
          font-size: 14px;
          font-weight: 500;
          color: #303133;
        }
        
        .analysis-item {
          margin-bottom: 20px;
          
          .label {
            display: block;
            font-size: 12px;
            color: #606266;
            margin-bottom: 8px;
          }
          
          .value {
            font-size: 12px;
            color: #303133;
            
            div {
              margin-bottom: 4px;
            }
          }
        }
      }
    }
  }
  
  .dialog-footer {
    text-align: right;
  }
}

/* Vue Flow 样式 */
:deep(.vue-flow__node) {
  border-radius: 12px;
  font-size: 12px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  white-space: pre-line;
  line-height: 1.3;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
  }
}

:deep(.vue-flow__edge) {
  stroke-width: 3;
  transition: all 0.3s ease;
}

:deep(.vue-flow__edge.animated) {
  stroke-dasharray: 8 4;
  animation: dashdraw 1s linear infinite;
}

:deep(.vue-flow__edge:hover) {
  stroke-width: 4;
}

:deep(.vue-flow__controls) {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

:deep(.vue-flow__controls-button) {
  border-radius: 6px;
  transition: all 0.3s ease;

  &:hover {
    background: #409eff;
    color: white;
  }
}

:deep(.vue-flow__minimap) {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

@keyframes dashdraw {
  to {
    stroke-dashoffset: -12;
  }
}

/* 自定义节点样式 */
.custom-node {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  text-align: center;
  white-space: pre-line;
  line-height: 1.3;
  font-weight: 500;
  padding: 8px;
  box-sizing: border-box;
  position: relative;
  transition: all 0.3s ease;

  &.selected {
    box-shadow: 0 0 0 3px #409eff !important;
    transform: scale(1.05);
    z-index: 10;
  }

  .node-title {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    padding: 2px 8px;
    font-size: 10px;
    font-weight: 600;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .node-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    line-height: 1.2;
  }

  .permission-indicator {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 16px;
    height: 16px;
    background: #67c23a;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }

  &.task .node-title {
    background: rgba(255, 255, 255, 0.25);
  }

  &.review .node-title {
    background: rgba(255, 255, 255, 0.25);
  }
}

/* 解锁管理弹窗样式 */
.unlock-manage-content {
  .unlock-conditions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;

    h4 {
      margin: 0 0 15px 0;
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }

    .condition-items {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .el-checkbox {
        margin-right: 0;
      }
    }
  }
}

/* 历史记录弹窗样式 */
.history-content {
  .el-table {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
  }
}

/* 弹窗通用样式 */
.dialog-footer {
  text-align: right;

  .el-button {
    margin-left: 10px;
  }
}
</style>
