<!-- 子任务历史弹窗组件 -->
<script setup lang="ts" name="SubTaskHistoryDialog">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
interface Props {
  visible: boolean
  taskId?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  taskId: ''
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
}>()

// 响应式状态
const loading = ref(false)
const activeTab = ref('operation')

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 操作历史数据
const operationHistoryData = ref([
  {
    id: 1,
    operationType: '创建',
    operationContent: '创建子任务"永川区民政局填报流程"',
    operator: '张三',
    operationTime: '2024-01-01 09:00:00',
    operationResult: '成功',
    remark: '初始创建'
  },
  {
    id: 2,
    operationType: '编辑',
    operationContent: '修改子任务责任人为李四',
    operator: '项目经理',
    operationTime: '2024-01-02 14:30:00',
    operationResult: '成功',
    remark: '人员调整'
  },
  {
    id: 3,
    operationType: '状态变更',
    operationContent: '状态从"未开始"变更为"执行中"',
    operator: '李四',
    operationTime: '2024-01-03 10:15:00',
    operationResult: '成功',
    remark: '开始执行'
  },
  {
    id: 4,
    operationType: '进度更新',
    operationContent: '进度从0%更新为30%',
    operator: '李四',
    operationTime: '2024-01-05 16:45:00',
    operationResult: '成功',
    remark: '阶段性进展'
  },
  {
    id: 5,
    operationType: '提交审核',
    operationContent: '提交子任务进行审核',
    operator: '李四',
    operationTime: '2024-01-08 11:20:00',
    operationResult: '成功',
    remark: '等待审核'
  }
])

// 状态变更历史数据
const statusHistoryData = ref([
  {
    id: 1,
    fromStatus: '未开始',
    toStatus: '执行中',
    changeTime: '2024-01-03 10:15:00',
    operator: '李四',
    reason: '开始执行任务',
    duration: '2天'
  },
  {
    id: 2,
    fromStatus: '执行中',
    toStatus: '待审核',
    changeTime: '2024-01-08 11:20:00',
    operator: '李四',
    reason: '任务完成，提交审核',
    duration: '5天'
  }
])

// 进度变更历史数据
const progressHistoryData = ref([
  {
    id: 1,
    fromProgress: 0,
    toProgress: 10,
    changeTime: '2024-01-04 09:30:00',
    operator: '李四',
    milestone: '需求分析完成',
    remark: '完成初步需求梳理'
  },
  {
    id: 2,
    fromProgress: 10,
    toProgress: 30,
    changeTime: '2024-01-05 16:45:00',
    operator: '李四',
    milestone: '数据收集完成',
    remark: '完成基础数据收集工作'
  },
  {
    id: 3,
    fromProgress: 30,
    toProgress: 60,
    changeTime: '2024-01-07 14:20:00',
    operator: '李四',
    milestone: '数据整理完成',
    remark: '完成数据清洗和整理'
  },
  {
    id: 4,
    fromProgress: 60,
    toProgress: 100,
    changeTime: '2024-01-08 10:00:00',
    operator: '李四',
    milestone: '任务完成',
    remark: '所有工作项完成，准备提交'
  }
])

// 方法
const handleClose = () => {
  dialogVisible.value = false
  emit('close')
}

const handleExport = () => {
  ElMessage.success('历史记录导出成功')
}

const handleRefresh = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据刷新成功')
  }, 1000)
}

// 获取操作结果标签类型
const getResultType = (result: string) => {
  const typeMap = {
    '成功': 'success',
    '失败': 'danger',
    '警告': 'warning'
  }
  return typeMap[result as keyof typeof typeMap] || 'info'
}

// 格式化进度显示
const formatProgress = (progress: number) => {
  return `${progress}%`
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 弹窗打开时的初始化逻辑
    loading.value = true
    setTimeout(() => {
      loading.value = false
    }, 1500)
  }
})
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="子任务历史"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="subtask-history-content" v-loading="loading" element-loading-text="正在加载历史记录...">
      <!-- 操作按钮区域 -->
      <div class="history-header">
        <el-button type="primary" @click="handleRefresh">刷新</el-button>
        <el-button @click="handleExport">导出</el-button>
      </div>

      <!-- 标签页 -->
      <el-tabs v-model="activeTab" class="history-tabs">
        <!-- 操作历史 -->
        <el-tab-pane label="操作历史" name="operation">
          <el-table :data="operationHistoryData" style="width: 100%" height="350">
            <el-table-column prop="operationType" label="操作类型" width="100" />
            <el-table-column prop="operationContent" label="操作内容" min-width="200" />
            <el-table-column prop="operator" label="操作人" width="100" />
            <el-table-column prop="operationTime" label="操作时间" width="160" />
            <el-table-column prop="operationResult" label="操作结果" width="100">
              <template #default="{ row }">
                <el-tag :type="getResultType(row.operationResult)">
                  {{ row.operationResult }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" min-width="120" />
          </el-table>
        </el-tab-pane>

        <!-- 状态变更历史 -->
        <el-tab-pane label="状态变更历史" name="status">
          <el-table :data="statusHistoryData" style="width: 100%" height="350">
            <el-table-column prop="fromStatus" label="原状态" width="100" />
            <el-table-column prop="toStatus" label="新状态" width="100" />
            <el-table-column prop="changeTime" label="变更时间" width="160" />
            <el-table-column prop="operator" label="操作人" width="100" />
            <el-table-column prop="reason" label="变更原因" min-width="150" />
            <el-table-column prop="duration" label="持续时长" width="100" />
          </el-table>
        </el-tab-pane>

        <!-- 进度变更历史 -->
        <el-tab-pane label="进度变更历史" name="progress">
          <el-table :data="progressHistoryData" style="width: 100%" height="350">
            <el-table-column prop="fromProgress" label="原进度" width="100">
              <template #default="{ row }">
                {{ formatProgress(row.fromProgress) }}
              </template>
            </el-table-column>
            <el-table-column prop="toProgress" label="新进度" width="100">
              <template #default="{ row }">
                {{ formatProgress(row.toProgress) }}
              </template>
            </el-table-column>
            <el-table-column prop="changeTime" label="变更时间" width="160" />
            <el-table-column prop="operator" label="操作人" width="100" />
            <el-table-column prop="milestone" label="里程碑" width="120" />
            <el-table-column prop="remark" label="备注" min-width="150" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.subtask-history-content {
  min-height: 400px;

  .history-header {
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
  }

  .history-tabs {
    :deep(.el-tabs__content) {
      padding: 0;
    }

    :deep(.el-tab-pane) {
      padding-top: 16px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
