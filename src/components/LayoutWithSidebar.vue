<template>
	<div class="layout-container">
		<!-- 顶部导航栏 -->
		<div class="header">
			<div class="header-left">
				<img src="/logo.png" alt="Logo" class="logo" />
				<span class="title">"一表通"智能报表</span>
			</div>
			<div class="header-right">
				<el-icon class="header-icon"><Bell /></el-icon>
				<el-icon class="header-icon"><Message /></el-icon>
				<el-dropdown>
					<span class="user-info">
						<el-avatar size="small" :src="userAvatar" />
						<span class="username">{{ username }}</span>
					</span>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item>个人设置</el-dropdown-item>
							<el-dropdown-item>退出登录</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
			</div>
		</div>

		<div class="main-container">
			<!-- 左侧导航菜单 -->
			<div class="sidebar">
				<el-menu
					:default-active="activeMenu"
					class="sidebar-menu"
					background-color="#4A90E2"
					text-color="#ffffff"
					active-text-color="#ffffff"
				>
					<el-menu-item index="dashboard">
						<el-icon><House /></el-icon>
						<span>首页数据管理中心</span>
					</el-menu-item>
					
					<el-sub-menu index="business-table">
						<template #title>
							<el-icon><Document /></el-icon>
							<span>业务表主键定义</span>
						</template>
						<el-menu-item index="primary-key-definition">主键定义管理</el-menu-item>
						<el-menu-item index="primary-key-rules">主键规则配置</el-menu-item>
					</el-sub-menu>

					<el-menu-item index="data-source">
						<el-icon><DataBoard /></el-icon>
						<span>数据源配置中心</span>
					</el-menu-item>

					<el-menu-item index="report-config">
						<el-icon><Document /></el-icon>
						<span>可视化报表配置中心</span>
					</el-menu-item>

					<el-menu-item index="system-config">
						<el-icon><Setting /></el-icon>
						<span>系统配置中心</span>
					</el-menu-item>

					<el-menu-item index="user-management">
						<el-icon><User /></el-icon>
						<span>用户权限管理中心</span>
					</el-menu-item>

					<el-menu-item index="log-center">
						<el-icon><Document /></el-icon>
						<span>日志中心</span>
					</el-menu-item>

					<el-menu-item index="help">
						<el-icon><QuestionFilled /></el-icon>
						<span>帮助中心</span>
					</el-menu-item>
				</el-menu>

				<!-- 侧边栏折叠按钮 -->
				<div class="sidebar-toggle">
					<el-button 
						type="text" 
						@click="toggleSidebar"
						class="toggle-btn"
					>
						<el-icon><Fold /></el-icon>
					</el-button>
				</div>
			</div>

			<!-- 主内容区域 -->
			<div class="content-area">
				<slot />
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
	Bell,
	Message,
	House,
	Document,
	DataBoard,
	Setting,
	User,
	QuestionFilled,
	Fold
} from '@element-plus/icons-vue'

// Props
interface Props {
	activeMenu?: string
}

const props = withDefaults(defineProps<Props>(), {
	activeMenu: 'primary-key-definition'
})

// 响应式数据
const username = ref('admin-七七四市-第一')
const userAvatar = ref('/avatar.png')
const sidebarCollapsed = ref(false)

// 方法
const toggleSidebar = () => {
	sidebarCollapsed.value = !sidebarCollapsed.value
}
</script>

<style scoped>
.layout-container {
	height: 100vh;
	display: flex;
	flex-direction: column;
}

.header {
	height: 60px;
	background: #ffffff;
	border-bottom: 1px solid #e4e7ed;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 20px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
	display: flex;
	align-items: center;
}

.logo {
	width: 32px;
	height: 32px;
	margin-right: 12px;
}

.title {
	font-size: 18px;
	font-weight: 600;
	color: #303133;
}

.header-right {
	display: flex;
	align-items: center;
	gap: 16px;
}

.header-icon {
	font-size: 20px;
	color: #606266;
	cursor: pointer;
}

.user-info {
	display: flex;
	align-items: center;
	gap: 8px;
	cursor: pointer;
}

.username {
	color: #303133;
	font-size: 14px;
}

.main-container {
	flex: 1;
	display: flex;
	overflow: hidden;
}

.sidebar {
	width: 250px;
	background: #4A90E2;
	position: relative;
	transition: width 0.3s ease;
}

.sidebar.collapsed {
	width: 64px;
}

.sidebar-menu {
	border: none;
	height: 100%;
}

.sidebar-menu .el-menu-item {
	color: #ffffff !important;
	border-radius: 0;
}

.sidebar-menu .el-menu-item:hover {
	background-color: rgba(255, 255, 255, 0.1) !important;
}

.sidebar-menu .el-menu-item.is-active {
	background-color: rgba(255, 255, 255, 0.2) !important;
}

.sidebar-menu .el-sub-menu__title {
	color: #ffffff !important;
}

.sidebar-menu .el-sub-menu__title:hover {
	background-color: rgba(255, 255, 255, 0.1) !important;
}

.sidebar-toggle {
	position: absolute;
	bottom: 20px;
	left: 50%;
	transform: translateX(-50%);
}

.toggle-btn {
	color: #ffffff !important;
}

.content-area {
	flex: 1;
	background: #f5f7fa;
	overflow: auto;
	padding: 20px;
}
</style>
