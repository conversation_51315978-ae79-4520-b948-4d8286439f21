<script setup lang="ts">
import {nextTick, onMounted, onUnmounted, ref, toRaw, watch} from 'vue'
import {getOrganize} from '@/api/OrganizeApi'
import {useArrayToTree, useTreeLastChecked} from '@/hooks/useConvertHook'
import {useSource} from '@/stores/useSource'
import type {
	TreeNode,
	TreeNodeData,
	TreeKey,
	TreeData,
} from 'element-plus/es/components/tree-v2/src/types'
import {useUserStore} from '@/stores/useUserStore'

interface Props {
	key?: string
	labelKey?: string
	parentKey?: string
	showCheckbox?: boolean
	height?: string
	width?: string
	placeholder?: string
	grade?: number
	single?: boolean
}

const userStore = useUserStore()
const source = useSource()
const props = withDefaults(defineProps<Props>(), {
	key: 'id',
	labelKey: 'name',
	parentKey: 'parentId',
	showCheckbox: true,
	height: '24px',
	width: '166px',
	placeholder: '请选择区域',
	grade: 3,
	single: false,
})
const emits = defineEmits(['checked'])
const treeArray = ref<any>([])
const treeRef = ref<any>(null)
const query = ref('')
const open = ref(false)
const label = ref('')
const checkedCount = ref(0)

watch(
	() => userStore.userInfo,
	() => {
		getOrganizeData()
	}
)

const filterMethod = (query: string, node: TreeNode) => {
	return node.label!.includes(query)
}

const onQueryChanged = (query: string) => {
	treeRef.value.filter(query)
}

const onOpenTree = (e?: MouseEvent) => {
	if (e) {
		const el = e.target as HTMLElement
		if (el?.closest('.organize-comp')) {
			open.value = !open.value
		} else {
			open.value = false
		}
	}
}

const getOrganizeData = () => {
	if (source.getOrganize.length > 0) {
		return
	}
	getOrganize(props.grade).then((res: any) => {
		// 根据当前登陆用户过滤部门数据
		const {city, district, community, street} = useUserStore().getUserInfo
		console.log(11111, useUserStore().userInfo)

		const role = street || community || district || city
		console.log(role)

		if (!role) {
			console.log('当前用户未绑定任何区域！')
			return
		}

		const node = res.data.items.find((f: any) => f.name === role)
		const nodeChild = res.data.items.filter((f: any) => f.parentId === node.id)

		nodeChild.unshift(node)
		const __arr: any = useArrayToTree(nodeChild, props.key, props.parentKey, props.labelKey, false)

		// 单选时，禁用所有父级节点
		if (props.single) {
			nodeChild.forEach((f: any) => (f.disabled = true))

			const loops = (arr: any) => {
				for (const element of arr) {
					const item = element
					if (item.child && item.child.length > 0) {
						loops(item.child)
					} else {
						item.disabled = false
					}
				}
			}
			loops(__arr)
		}

		treeArray.value = __arr
		source.setOrganize(JSON.parse(JSON.stringify(treeArray.value)))
	})
}

const onCheck = (
	data: TreeNodeData,
	info: {
		checkedKeys: TreeKey[]
		checkedNodes: TreeData
		halfCheckedKeys: TreeKey[]
		halfCheckedNodes: TreeData
	}
) => {
	if (props.single) {
		treeRef.value.setCheckedKeys([])
		if (info.checkedNodes.length > 0) {
			treeRef.value.setChecked(info.checkedNodes[info.checkedNodes.length - 1][props.key], true)
		}
	}

	const checked = treeRef.value.getCheckedNodes()
	label.value = checked[checked.length - 1]?.name ?? ''
	checkedCount.value = checked.length

	nextTick(() => {
		emits('checked', JSON.parse(JSON.stringify(checked)))
	})
}

const reset = () => {
	label.value = ''
	checkedCount.value = 0
	treeRef.value.setCheckedKeys([])
	emits('checked', null)
}

onMounted(() => {
	getOrganizeData()
	document.body.addEventListener('click', onOpenTree)
})

onUnmounted(() => {
	document.body.removeEventListener('click', onOpenTree)
})

defineExpose({
	reset,
})
</script>
<template>
	<div
		class="organize-comp"
		:style="{width: props.width, height: props.height}"
		@click.stop
		:class="{open}"
	>
		<div class="label" @click="onOpenTree">
			<div class="values" :class="{placeholder: label === ''}">
				<span :class="{val: label !== ''}">{{ label === '' ? props.placeholder : label }}</span>
				<span v-if="label !== ''" class="values-count" :class="{val: label !== ''}">
					+{{ checkedCount }}
				</span>
			</div>
			<el-icon><ArrowDown /></el-icon>
		</div>
		<div class="tree" :style="{top: parseInt(props.height) + 11 + 'px'}" :class="{open}">
			<el-input
				class="filter-input"
				v-model="query"
				placeholder="您可以输入关键字筛选节点"
				@input="onQueryChanged"
			/>
			<el-tree-v2
				v-if="treeArray.length > 0"
				ref="treeRef"
				v-bind="$attrs"
				:data="treeArray"
				:show-checkbox="props.showCheckbox"
				:filter-method="filterMethod"
				:props="{id: props.key, label: props.labelKey, children: 'child'}"
				@check="onCheck"
			/>
			<el-skeleton v-else :animated="true" :rows="5" />
		</div>
	</div>
</template>
<style scoped lang="scss">
.organize-comp {
	border: var(--z-border);
	border-radius: 4px;
	background-color: #fff;
	cursor: pointer;
	height: 24px;
	margin: 0 10px;
	position: relative;
	transition: all 0.15s linear;
	z-index: 1;

	&.open {
		.label {
			i {
				rotate: 180deg;
			}
		}
	}

	&.open::after {
		content: '';
		bottom: -24px;
		border: 8px solid transparent;
		border-bottom-color: #fff;
		left: 10px;
		position: absolute;
		transform: translateY(-50%);
		z-index: 2;
	}

	&.open::before {
		content: '';
		bottom: -23px;
		border: 8px solid transparent;
		border-bottom-color: #dcdfe6;
		left: 10px;
		position: absolute;
		transform: translateY(-50%);
		z-index: 2;
	}

	&:hover {
		border-color: rgba(168, 171, 178, 0.8);
	}

	.tree {
		border: var(--z-border);
		border-radius: 5px;
		box-shadow: 0 2px 4px -1px #0003, 0 4px 5px #00000024, 0 1px 10px #0000001f;
		background: #fff;
		height: 0;
		left: 0;
		opacity: 0;
		overflow: hidden;
		position: absolute;
		padding: 5px;
		transition: all 0.15s linear;
		width: 200%;
		z-index: -1;

		&.open {
			height: 248px;
			opacity: 1;
			z-index: 2;
		}
	}

	.filter-input {
		margin-bottom: 5px;
	}

	.label {
		align-items: center;
		display: flex;
		height: 100%;
		.values {
			align-items: center;
			display: flex;
			flex: 1;

			&.placeholder {
				color: #a8abb2;
				padding: 0 6px;
			}

			span.val {
				border-radius: 4px;
				background-color: #f1f1f1;
				padding: 0 5px;
				scale: 0.85;
			}
		}

		i {
			color: #a8abb2;
			font-size: 14px;
			margin-right: 5px;
			transition: all 0.15s linear;
		}
	}

	.el-skeleton {
		display: flex;
		flex-wrap: wrap;
	}
}
</style>
