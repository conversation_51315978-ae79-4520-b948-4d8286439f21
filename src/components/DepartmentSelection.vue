<script setup lang="ts">
import {ref, watch, computed} from 'vue'
import {ElMessage} from 'element-plus'

const emits = defineEmits(['change', 'confirm', 'update:modelValue'])
const props = defineProps({
	modelValue: {
		type: Boolean,
		default: false,
	},
	departments: {
		type: Array,
		default: () => [],
	},
})

const department = ref('')

let currentItem: any = null

// 检查是否为测试模式
const isFreeMode = computed(() => {
	const urlParams = new URLSearchParams(window.location.search)
	return urlParams.get('free') === 'true'
})

const handleFormChange = (val: any, item: any) => {
	currentItem = item
	console.log(currentItem)
	emits('change', val, item)
}

const onClickConfirm = () => {
	if (!department.value) {
		ElMessage.warning('请选择部门')
		return
	}
	emits('update:modelValue', false)
	emits(
		'confirm',
		department.value,
		currentItem.options.find((item: any) => item.value === department.value)._raw
	)
	localStorage.setItem(
		'departmentforStreet',
		JSON.stringify({
			...currentItem.options.find((item: any) => item.value === department.value)._raw,
			userId: JSON.parse(localStorage.getItem('currentPlatformInfo') || '{}').id,
		})
	)
	console.log(
		'confirm',
		department.value,
		currentItem.options.find((item: any) => item.value === department.value)._raw
	)
}
</script>

<template>
	<div class="department-selection" v-if="modelValue && !isFreeMode">
		<div class="box">
			<div class="logo"></div>
			<template v-if="departments.length">
				<FormItem
					v-model="department"
					:items="[
					{
						prop: 'department',
						type: 'select',
						placeholder: '请选择部门',
						options: props.departments.map((item:any) => ({
							label: `${(item.community || item.street || item.district || item.city) + '-' +item.parentName +'-' +item.name}`,
							value: item.id,
							_raw: JSON.parse(JSON.stringify(item)),
						})),
					},
				]"
					class="mg-top-40 mg-bottom-30"
					@change="handleFormChange"
					style="width: 300px"
				></FormItem>
				<el-button type="primary" size="large" class="confirm" @click="onClickConfirm">
					确 认
				</el-button>
			</template>
			<div v-else class="no-department">当前用户尚未绑定任何部门</div>
		</div>
	</div>
</template>
<style scoped lang="scss">
.department-selection {
	align-items: center;
	background: rgba(var(--z-theme-rgb), 0.9);
	display: flex;
	height: 100vh;
	left: 0;
	justify-content: center;
	overflow: hidden;
	position: fixed;
	top: 0;
	width: 100%;
	z-index: 1000;

	.box {
		align-items: center;
		display: flex;
		flex-direction: column;
		flex-wrap: wrap;
		height: 250px;
		justify-content: center;
	}

	.logo {
		background: url(/src/assets/image/logo.png) no-repeat center;
		background-size: cover;
		height: 100px;
		width: 100px;
	}

	.no-department {
		font-size: 16px;
		padding: 40px;
	}

	.confirm {
		width: 150px;
	}
}
</style>
