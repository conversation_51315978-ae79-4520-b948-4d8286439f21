<script setup lang="ts">
import {computed, ref, reactive, nextTick, onMounted, toRaw} from 'vue'
import {useRouter} from 'vue-router'
import {useUserStore} from '@/stores/useUserStore'
import {useGuid} from '@/hooks/useGuid'
import {EventSourcePolyfill} from 'event-source-polyfill'
import {GetLLM, GetLLMMessage, DeleteLLMessage} from '@/api/CommonApi'
import {ElMessage} from 'element-plus'
import AICharts from './AICharts.vue'
import Popup from '@/components/common/popup-comp.vue'

const emits = defineEmits(['update:modelValue'])
const props = defineProps({
	modelValue: {
		type: Boolean,
		default: false,
	},
})

const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)
const writing = ref(false)
const dloading = ref(false)
const hloading = ref(false)

const input = ref('')
const messageMaps: any = reactive({})
const newContent = ref('')
const renderTableValue = ref('')
const dataJSON: any = ref('')
const contentRef: any = ref(null)

const groups: any = ref([])
const services = ref([])
const history: any = ref([])
const data: any = ref([])
const dataHeader: any = ref([])

const currentService: any = ref(null)
const currentGroup: any = ref(null)
const currentWritingGroupId: any = ref('') // 正在输出的 groupId
const chatCompletionId: any = ref(null)

const intentionMap = {
	Analysis: 'analysis',
	Bar: 'bar',
	Pie: 'pie',
	Line: 'line',
}

const onClose = () => {
	stopSSE(false)
	emits('update:modelValue', false)
}

const showAI = computed(() => {
	if (props.modelValue) {
		Promise.all([getChartServices(), getChartGroup()])
	}
	return props.modelValue
})

// 防抖函数
let scrollTimer: number | null = null
const scrollToBottom = (force = false) => {
	// 清除之前的定时器，实现防抖
	if (scrollTimer) {
		clearTimeout(scrollTimer)
	}

	scrollTimer = setTimeout(() => {
		nextTick(() => {
			if (contentRef.value) {
				// 判断是否滚动到底部或接近底部
				const {scrollTop, scrollHeight, clientHeight} = contentRef.value
				// 如果距离底部不超过50px，则滚动到底部
				if (scrollTop + clientHeight >= scrollHeight - 100 || force) {
					// 使用 requestAnimationFrame 确保在下一帧渲染前执行，减少抖动
					requestAnimationFrame(() => {
						contentRef.value.scrollTop = contentRef.value.scrollHeight
					})
				}
			}
		})
	}, 100) as unknown as number // 100ms的防抖延迟
}

const getChartServices = async () => {
	const {data} = await GetLLM(
		'/services',
		{},
		{
			headers: {
				Urlkey: 'llm',
			},
		}
	)
	services.value = data
	currentService.value = services.value[1]
}

const getChartGroup = async () => {
	const {data} = await GetLLMMessage(
		'/my-chat-group',
		{},
		{
			headers: {
				Urlkey: 'llm',
			},
		}
	)

	if (data && data.length > 0) {
		groups.value = data
	} else if (groups.value.length === 0) {
		groups.value.push({groupId: useGuid(), groupName: '新聊天'})
	}

	groups.value.forEach((group: any) => {
		messageMaps[group.groupId] = reactive(new Map())
	})
	currentGroup.value = groups.value[0]

	await getChartHistory()
}

const getChartHistory = async () => {
	try {
		hloading.value = true
		const {data} = await GetLLMMessage(
			`/my-chat-history`,
			{
				chatId: currentGroup.value.groupId,
			},
			{
				headers: {
					Urlkey: 'llm',
				},
			}
		)
		history.value = data
		hloading.value = false
		renderHistory()
	} catch (err) {
		hloading.value = false
		window.errMsg(err, '获取记录')
	}
}

const renderHistory = async () => {
	const groupId = currentGroup.value.groupId

	messageMaps[groupId].clear()

	if (!history.value || !history.value.items || !history.value.items.length) {
		return
	}

	let count = 0

	for (let i = 0; i < history.value?.items.length; i++) {
		const item = history.value?.items[i]
		messageMaps[groupId].set(++count, {role: 'user', content: item.inputs})
		messageMaps[groupId].set(++count, {
			role: 'assistant',
			content: item.fullMessage || '服务器繁忙, 请稍后再试',
		})
		if (item.db) {
			try {
				const db = JSON.parse(item.db)
				const jsonResultContent = JSON.parse(item.jsonResultContent)

				if (jsonResultContent.intention === intentionMap.Analysis) {
					data.value = {db}
					const table = await renderTable(true)
					messageMaps[groupId].set(++count, {
						role: 'assistant',
						content: table,
					})
				} else {
					messageMaps[groupId].set(++count, {
						role: 'assistant',
						content: '正在绘制图表',
						data: {
							db,
							content: item.content,
						},
						type: jsonResultContent.intention,
					})
				}
			} catch (err) {
				console.log('解析JSON失败:', err)
				messageMaps[groupId].set(++count, {
					role: 'assistant',
					content: '数据解析失败',
				})
			}
		}
	}
	scrollToBottom(true)
}

const renderTable = (force = false) => {
	return new Promise((resolve) => {
		// 检查data.value是否存在
		if (!data.value?.db || !Array.isArray(data.value.db) || data.value.db.length === 0) {
			resolve('')
			return
		}

		const tableData = data.value.db

		// 获取表头（所有对象的键的并集）
		const headers = Object.keys(tableData[0])

		// 创建表头行
		let mdTableHeader = '| ' + headers.join(' | ') + ' |\n'

		// 创建分隔行
		let mdTableSeparator = '| ' + headers.map(() => '---').join(' | ') + ' |\n'

		// 创建数据行
		let mdTableBody = ''
		for (const item of tableData) {
			const rowValues = headers.map((header) => {
				const value = item[header as keyof typeof item]
				return value !== null && value !== undefined ? String(value) : '-'
			})
			mdTableBody += '| ' + rowValues.join(' | ') + ' |\n'
		}

		// 组合成完整的Markdown表格
		let mdTable = '\n\n#### 查询结果\n\n' + mdTableHeader + mdTableSeparator + mdTableBody

		console.log('生成的Markdown表格:', mdTable)

		if (force) {
			renderTableValue.value = mdTable
			resolve(mdTable)
			return
		}

		let count = 0
		renderTableValue.value = ''

		const loops = () => {
			if (count < mdTable.length) {
				const char = mdTable.charAt(count)

				// 添加字符到newContent
				// newContent.value += char
				renderTableValue.value += char

				count++
				scrollToBottom(true)
				setTimeout(() => loops(), 20)
			} else {
				resolve(mdTable)
			}
		}

		loops()
	})
}

let tempStr = ''
const renderMessage = (json: any) => {
	if (tempStr.length > 10) {
		writing.value = true
	}
	const chars = json.choices
	if (chars.length > 0) {
		chars.forEach((c: any) => {
			if (c.delta.content) {
				tempStr += c.delta.content
			}
		})

		if (currentGroup.value.groupId === currentWritingGroupId.value) {
			scrollToBottom()
		}
	}

	setTimeout(() => {
		if (tempStr.length > 10) {
			newContent.value = tempStr.replace(/<think>|<\/think>/g, '')
		}
	}, 32)
}

const sqlSSE: any = ref(null)
const startSQLSEE = async () => {
	return new Promise((resolve, reject) => {
		try {
			const token = userStore.getToken
			if (!token || !currentService.value || !currentGroup.value || !input.value) return
			const url = `${window.GOV_CONFIG.LLM}/llm-api/v1/chat/stream/sql/${currentService.value.id}/${currentGroup.value.groupId}/${chatCompletionId.value}`

			sqlSSE.value = new EventSourcePolyfill(url, {
				heartbeatTimeout: 600000,
				headers: {
					Accept: 'text/event-stream',
					Authorization: `Bearer ${token}`,
					'Content-Type': 'text/event-stream',
				},
				withCredentials: true,
			})

			if (!sqlSSE.value) return

			sqlSSE.value.onmessage = async (event: any) => {
				if (event.data === '[DONE]') {
					sqlSSE.value.close()
					// 可以获取数据了
					console.log('可以获取数据了')
					const res = await GetLLMMessage(
						`/chat-data/${chatCompletionId.value}`,
						{},
						{
							headers: {
								Urlkey: 'llm',
							},
						}
					)
					data.value = res.data

					if (data.value.db && data.value.db.length > 0) {
						dataHeader.value = Object.keys(data.value.db[0])
					}

					resolve('')
				}
			}

			sqlSSE.value.onerror = (error: any) => {
				sqlSSE.value.close()
				sseError(error, 'startSQLSEE')
				reject(error)
			}
		} catch (error) {
			reject(error)
		}
	})
}

const firstSSE: any = ref(null)
const startSSE = async () => {
	try {
		const token = userStore.getToken
		if (!token || !currentService.value || !currentGroup.value) {
			ElMessage.warning('正在获取相关数据, 请稍等')
			return
		}

		if (!input.value) {
			ElMessage.warning('请输入问题')
			return
		}

		let url = `${window.GOV_CONFIG.LLM}/llm-api/v1/chat/stream/${currentService.value.id}/${currentGroup.value.groupId}?message=${input.value}{cols:`

		if (dataHeader.value.length > 0) {
			url += `'${dataHeader.value.join('|')}'`
		}

		if (currentGroup.value.groupName.includes('新聊天')) {
			const group = groups.value.find(
				(item: any) => item.groupId === currentGroup.value.groupId
			)
			group.groupName = input.value
			currentGroup.value.groupName = input.value
		}

		// 使用 SSE 连接
		firstSSE.value = new EventSourcePolyfill(url, {
			heartbeatTimeout: 600000,
			headers: {
				Accept: 'text/event-stream',
				Authorization: `Bearer ${token}`,
				'Content-Type': 'text/event-stream',
			},
			withCredentials: true,
		})

		if (!firstSSE.value) return

		const groupId = currentGroup.value.groupId
		const time = Date.now()

		currentWritingGroupId.value = groupId
		messageMaps[groupId].set(time, {role: 'user', content: input.value})

		loading.value = true
		scrollToBottom(true)

		firstSSE.value.onmessage = async (event: any) => {
			if (event.data === '[DONE]') {
				firstSSE.value.close()

				try {
					// 尝试找到最后一个完整的JSON对象
					const text = newContent.value
					let jsonStart = -1
					let jsonEnd = -1
					let bracketCount = 0

					// 从后向前查找最后一个完整的JSON对象
					for (let i = text.length - 1; i >= 0; i--) {
						if (text[i] === '}') {
							bracketCount++
							if (jsonEnd === -1) jsonEnd = i
						} else if (text[i] === '{') {
							bracketCount--
							if (bracketCount === 0 && jsonEnd !== -1) {
								jsonStart = i
								break
							}
						}
					}

					if (jsonStart !== -1 && jsonEnd !== -1) {
						const jsonStr = text.substring(jsonStart, jsonEnd + 1)
						dataJSON.value = JSON.parse(jsonStr)
					}
				} catch (error) {
					console.error('解析JSON失败:', error)
				}

				const satisfy = dataJSON.value && dataJSON.value.filter_res === '满足'

				if (satisfy) {
					dloading.value = true
					scrollToBottom()
					await startSQLSEE()
				}

				writing.value = false

				const now = Date.now()
				const nowPlus = now + 1

				if (newContent.value) {
					messageMaps[groupId].set(now, {
						role: 'assistant',
						content: newContent.value,
					})
				}

				if (satisfy) {
					if (!data.value?.db || data.value?.db?.length === 0) {
						messageMaps[groupId].set(nowPlus, {
							role: 'assistant',
							content: '没有找到相关数据',
						})
					} else if (dataJSON.value.intention === intentionMap.Analysis) {
						await renderTable()
						messageMaps[groupId].set(nowPlus, {
							role: 'assistant',
							content: renderTableValue.value,
						})
						dloading.value = false
					} else if (
						dataJSON.value.intention === intentionMap.Bar ||
						dataJSON.value.intention === intentionMap.Pie ||
						dataJSON.value.intention === intentionMap.Line
					) {
						messageMaps[groupId].set(nowPlus, {
							role: 'assistant',
							content: '正在绘制图表',
							type: dataJSON.value.intention,
							data: toRaw(data.value),
							dataJSON: toRaw(dataJSON.value),
						})
					}
				}
				loading.value = false
				resetSSEStatus()
			} else {
				const json = JSON.parse(event.data)

				if (json.error) {
					sseError(json.error, 'startSSE')
					return
				}

				renderMessage(json)
				chatCompletionId.value = json.id
			}
		}

		firstSSE.value.onerror = (error: any) => {
			sseError(error, 'startSSE')
			firstSSE.value.close()
			resetSSEStatus()
		}

		firstSSE.value.onopen = () => {
			console.log('SSE 连接已打开')
		}
	} catch (error) {
		console.error('获取认证信息失败:', error)
	}
}

const resetSSEStatus = () => {
	tempStr = ''
	input.value = ''
	newContent.value = ''
	renderTableValue.value = ''
	currentWritingGroupId.value = ''
	data.value = []
	dataJSON.value = ''
	// dataHeader.value = []
	writing.value = false
	loading.value = false
	dloading.value = false

	scrollToBottom()
}

const stopSSE = (force = true) => {
	const writingGroupId = currentWritingGroupId.value

	if (firstSSE.value) {
		firstSSE.value.close()
	}

	if (sqlSSE.value) {
		sqlSSE.value.close()
	}

	if (newContent.value) {
		messageMaps[writingGroupId].set(Date.now(), {
			role: 'assistant',
			content: newContent.value,
		})
	}
	force &&
		messageMaps[writingGroupId].set(Date.now() + 1, {
			role: 'assistant',
			content: '好的, 会话已结束',
		})
	firstSSE.value = null
	sqlSSE.value = null
	resetSSEStatus()
}

const sseError = (error: any, type: string) => {
	console.log(`${type} 连接出错:`, error)
	const writingGroupId = currentWritingGroupId.value
	messageMaps[writingGroupId].set(Date.now(), {
		role: 'assistant',
		content: '服务器繁忙, 请稍后再试',
	})
	resetSSEStatus()
}

const createNewChat = () => {
	const newGroup = {groupId: useGuid(), groupName: '新聊天'}
	groups.value.unshift(newGroup)
	currentGroup.value = newGroup
	messageMaps[newGroup.groupId] = reactive(new Map())
}

const onSelectChat = (item: any) => {
	if (currentGroup.value.groupId === item.groupId) {
		return
	}
	messageMaps[currentGroup.value.groupId].clear()
	currentGroup.value = item
	getChartHistory()
}

const onRemoveChat = (item: any) => {
	DeleteLLMessage(
		`/chat-data/delete/${item.groupId}`,
		{},
		{
			headers: {
				Urlkey: 'llm',
			},
		}
	).then(() => {
		if (loading.value || dloading.value || writing.value || hloading.value) {
			return
		}

		const index = groups.value.findIndex((g: any) => g.groupId === item.groupId)
		groups.value.splice(index, 1)

		delete messageMaps[item.groupId]

		// 更新当前组
		if (groups.value.length > 0) {
			// 如果后面还有数据则选择下一个
			if (index < groups.value.length) {
				currentGroup.value = groups.value[index]
			}
			// 如果没有下一个，则选择上一个
			else if (index > 0) {
				currentGroup.value = groups.value[index - 1]
			}
			// 更新历史记录
			getChartHistory()
		} else {
			// 如果没有聊天组了，清空消息
			currentGroup.value = null
			history.value = []
		}
	})
}

onMounted(() => {})
</script>
<template>
	<Popup
		v-if="showAI"
		title="一表通智能问数"
		:w="800"
		:h="600"
		:full="false"
		:offset="['50%', '50%']"
		@close="onClose"
	>
		<div class="df h-full chat-box">
			<div class="history">
				<div class="new-chat">
					<el-button type="primary" @click="createNewChat" class="w-full">
						<Icons name="Chat" size="18" color="#fff" style="margin-right: 3px" />
						新聊天
					</el-button>
				</div>
				<div class="groups">
					<ul>
						<li
							v-for="(item, index) in groups"
							:key="index"
							:class="{active: item.groupId === currentGroup.groupId}"
							@click="onSelectChat(item)"
						>
							{{ item?.groupName?.replace(/\{cols:.*/, '') }}
							<span class="remove" @click.stop="onRemoveChat(item)">&times;</span>
						</li>
					</ul>
				</div>
			</div>
			<div class="ai" v-action:enter="startSSE">
				<div class="content" ref="contentRef">
					<div class="history-loading shadow-12" :class="{on: hloading}">
						<LoadingTransition color="#fff" />
					</div>

					<div v-if="messageMaps[currentGroup?.groupId]?.size === 0" class="empty">
						<div>输入类似以下内容:</div>
						<div>安置帮教人员风险等级为三级的人员有多少？</div>
						<div>今年被释放的安置帮教人员有几个？</div>
						<div>各区县有多少安置帮教人员未落实最低生活保障？</div>
					</div>

					<!-- 信息输出 -->
					<template
						v-if="currentGroup && messageMaps[currentGroup.groupId]"
						v-for="[key, value] in Array.from(messageMaps[currentGroup.groupId]) as any[]"
						:key="key"
					>
						<div v-if="value.role === 'user'" class="item">
							<div class="message">{{ value.content }}</div>
						</div>
						<div v-else-if="value.role === 'assistant'" class="item jcs">
							<div class="ai">
								<img
									src="@/assets/image/aibot2.png"
									alt="ai"
									width="30"
									height="34"
								/>
							</div>

							<div
								class="message"
								:class="{
									'w-full': value.type,
								}"
							>
								<AICharts v-if="value.type" :item="value"></AICharts>
								<!-- 标准输出 -->
								<v-md-editor
									v-else-if="value.content"
									v-model="value.content"
									mode="preview"
								/>
							</div>
						</div>
					</template>

					<template v-if="currentWritingGroupId === currentGroup?.groupId">
						<!-- 思维链输出 -->
						<div v-if="writing" class="item jcs">
							<div class="ai">
								<img
									src="@/assets/image/aibot2.png"
									alt="ai"
									width="30"
									height="34"
								/>
							</div>
							<div class="message">
								<v-md-editor
									v-if="newContent"
									v-model="newContent"
									mode="preview"
								/>
								<LoadingTransition
									v-if="!dloading"
									text="正在思考"
									align="left"
									:class="{'mg-top-15': writing}"
								/>
							</div>
						</div>
						<!-- 数据查询 -->
						<div v-if="dloading" class="item jcs">
							<div class="ai">
								<img
									src="@/assets/image/aibot2.png"
									alt="ai"
									width="30"
									height="34"
								/>
							</div>
							<div class="message">
								<v-md-editor
									v-if="renderTableValue"
									v-model="renderTableValue"
									mode="preview"
									class="mg-bottom-15"
								/>
								<LoadingTransition text="正在查询结果" align="left" />
							</div>
						</div>
					</template>
				</div>

				<div class="footer">
					<input
						v-model="input"
						type="text"
						placeholder="问我任何事..."
						:disabled="loading || writing"
					/>

					<Icons
						name="Stop"
						color="#f00"
						size="36px"
						@click="stopSSE"
						class="stop"
						:class="{on: loading || writing || dloading}"
					/>

					<el-button
						type="primary"
						:loading="loading || writing"
						@click="startSSE"
						class="send"
					>
						<Icons v-if="!loading" name="UpArrow" color="#fff" size="30px" />
					</el-button>
				</div>
			</div>
		</div>
	</Popup>
</template>
<style scoped lang="scss">
@keyframes zoom {
	0% {
		transform: scale(1);
	}
	20% {
		transform: scale(1.1);
	}
	40% {
		transform: scale(1.2);
	}
	60% {
		transform: scale(1.1);
	}
	80% {
		transform: scale(0.9);
	}
	100% {
		transform: scale(1);
	}
}
.chat-box {
	margin: 0 -20px 0 -10px;
}
.history {
	border-right: 1px solid var(--z-line);
	flex: none;
	width: 200px;

	.new-chat {
		padding: 10px 5px 10px 0;
		text-align: center;
	}

	.groups {
		height: calc(100% - 52px);
		overflow-y: auto;
		border-top: 1px solid var(--z-line);
	}

	ul {
		padding: 5px 5px 5px 0;
		li {
			border-radius: 5px;
			cursor: pointer;
			color: var(--z-font-color);
			font-size: 14px;
			height: 40px;
			line-height: 40px;

			overflow: hidden;
			padding: 0 25px 0 10px;
			position: relative;
			text-overflow: ellipsis;
			transition: all 0.3s ease;
			white-space: nowrap;

			&.active {
				background-color: rgba(var(--z-main-rgb), 0.1);
				font-weight: 500;
			}

			&:hover {
				.remove {
					opacity: 1;
				}
			}

			.remove {
				color: var(--z-font-color);
				height: 15px;
				line-height: 13px;
				opacity: 0;
				position: absolute;
				right: 5px;
				top: 50%;
				transform: translateY(-50%);
				transition: all 0.15s ease;
				text-align: center;
				width: 15px;

				&:hover {
					border-radius: 15px;
					background-color: rgba(var(--z-danger-rgb), 1);
					color: var(--z-nav-font-color);
				}
			}
		}
	}
}
.ai {
	flex: 1;
	height: 100%;

	.content {
		height: calc(100% - 70px);
		overflow-y: auto;
		padding: 15px;
		position: relative;
	}

	.history-loading {
		border-radius: 5px;
		background-color: var(--z-main);
		left: 50%;
		opacity: 0;
		padding: 5px;
		position: sticky;
		top: 10px;
		transform: translate3D(-50%, -10px, 0);
		transition: all 0.15s ease;
		z-index: -1;
		width: 100px;

		&.on {
			opacity: 1;
			transform: translate3D(-50%, 0, 0);
			z-index: 1;
		}
	}

	.empty {
		div:first-child {
			color: #333;
			font-size: 16px;
			text-align: center;
			padding: 0 15px 15px 15px;
		}
		div:not(:first-child) {
			border-radius: 10px;
			background-color: rgb(242, 242, 242);
			color: #b7b7b7;
			font-size: 14px;
			padding: 15px;
			margin: 0 auto;
			margin-bottom: 15px;
			width: 70%;
		}
	}

	.item {
		align-items: flex-start;
		display: flex;
		justify-content: flex-end;
		margin-bottom: 15px;

		&.jcs {
			justify-content: flex-start;
			margin-bottom: 25px;
		}

		.message {
			border-radius: 15px 15px 5px 15px;
			background-color: #00bd45;
			color: #fff;
			font-size: 14px;
			line-height: 1.5;
			padding: 15px;
			text-align: left;
			word-break: break-all;
			max-width: calc(100% - 70px);

			&.w-full {
				padding: 5px;
				width: 80%;
			}
		}

		.ai {
			flex: none;
			height: 34px;
			margin-right: 10px;
			width: 30px;

			& + .message {
				background-color: rgb(245, 245, 245);
				color: #333;
				border-radius: 5px 15px 15px 15px;
				transform: translateY(8px);
			}
		}
	}

	.footer {
		align-items: center;
		border-top: 1px solid var(--z-line);
		background-color: #fff;
		display: flex;
		height: 70px;
		position: relative;
		padding: 0 10px;

		input {
			width: 100%;
			height: 40px;
			border-radius: 20px;
			border: 1px solid #ebebeb;
			padding: 0 10px;
			padding-right: 55px;
		}

		.send {
			border-radius: 40px;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-left: 10px;
			height: 40px;
			color: #fff;
			width: 40px;
			flex: none;
			border: none;
			transition: all 0.15s ease;

			&:hover {
				animation: zoom 0.15s ease;
			}
			:deep(> span) {
				margin-left: 0;
			}
		}

		.stop {
			cursor: pointer;
			position: absolute;
			right: 65px;
			opacity: 0;
			transition: opacity 0.3s ease;
			z-index: -1;

			&.on {
				opacity: 1;
				z-index: 1;
			}
		}

		:deep(i),
		:deep(svg) {
			font-size: 25px;
		}
	}

	:deep(.v-md-editor) {
		.github-markdown-body {
			font-size: 14px;
			padding: 0;
			margin-bottom: -15px;

			> * {
				margin-bottom: 15px;
			}

			.extra-class {
				display: none;
			}

			table {
				border: 1px solid #e0e0e0;
				border-collapse: collapse;
				border-spacing: 0;
				border-radius: 10px;
				margin: 15px 0;
				overflow: auto;
				// width: 100%;

				tr,
				td,
				th {
					border: none;
					word-break: keep-all;
					white-space: normal;
				}

				thead {
					th {
						border-right: 1px solid #e0e0e0;
						border-bottom: 1px solid #e0e0e0;
						background: #f5f5f5;

						&:first-child {
							width: 100%;
						}

						&:last-child {
							border-right: none;
						}
					}
				}

				tbody {
					tr {
						td {
							border-right: 1px solid #e0e0e0;
							border-bottom: 1px solid #e0e0e0;

							&:last-child {
								border-right: none;
							}
						}

						&:last-child {
							td {
								border-bottom: none;
							}
						}
					}
				}
			}
		}

		.v-md-editor__left-area,
		.v-md-editor__right-area {
			width: 100%;
		}
	}
}
</style>
