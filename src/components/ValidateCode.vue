<script setup>
import {ref} from 'vue'
import {ElMessage} from 'element-plus'
const emits = defineEmits(['send', 'confirm'])

const props = defineProps({
	// 验证码长度
	length: {
		type: Number,
		default: 4,
		validator: (value) => value > 0,
	},
})

const codeRef = ref()
const code = ref(new Array(props.length).fill(''))

const onInput = (idx) => {
	// 如果输入的不是数字和字母，则清空
	if (code.value[idx] && !/^\d|[a-z]|[A-Z]$/.test(code.value[idx])) {
		code.value[idx] = ''
		return
	}

	// 确保只保留第一个字符
	if (code.value[idx] && code.value[idx].length > 0) {
		code.value[idx] = code.value[idx].substring(0, 1)
	}

	// 如果当前输入框有值且不是最后一个输入框，则自动聚焦下一个输入框
	if (code.value[idx] !== '' && idx < props.length - 1) {
		// 获取下一个输入框元素并聚焦
		setTimeout(() => {
			const nextInput = codeRef.value.querySelector(`input[idx="${idx + 1}"]`)
			if (nextInput) {
				nextInput.focus()
			}
		}, 0)
	}
}

const onBackspace = (e) => {
	e.preventDefault()
	e.stopPropagation()

	const el = e.target
	const idx = Math.abs(el.getAttribute('idx'))

	if (el.nodeName === 'INPUT') {
		if (el.value) {
			// 如果当前输入框有值，则清空当前输入框
			code.value[idx] = ''
		} else {
			// 如果当前输入框已经为空，则处理上一个输入框
			if (idx > 0) {
				// 如果不是第一个输入框，清空上一个输入框并聚焦
				code.value[idx - 1] = ''
				// 获取上一个输入框元素并聚焦
				const prevInput = codeRef.value.querySelector(`input[idx="${idx - 1}"]`)
				if (prevInput) {
					prevInput.focus()
				}
			} else {
				// 如果是第一个输入框，保持焦点在当前输入框
				code.value[0] = ''
			}
		}
	}
}

const sendCode = () => {
	emits('send')
}

const confirm = () => {
	if (code.value.some((c) => c === '')) {
		ElMessage.warning('请先输入验证码')
		return
	}
	emits('confirm', code.value.join().replace(/,/g, ''))
}
</script>
<template>
	<Dialog
		v-bind="$attrs"
		:destroy-on-close="true"
		:width="length * 100"
		@click-confirm="confirm"
		@close="code = new Array(props.length).fill('')"
	>
		<div ref="codeRef" class="validate-code" v-action:backspace="onBackspace">
			<template v-for="(_, index) in code" :key="index">
				<el-input v-model="code[index]" :idx="index" @input="onInput(index)"></el-input>
			</template>
			<el-button type="primary" size="" @click="sendCode" style="height: 50px">
				发送验证码
			</el-button>
		</div>
	</Dialog>
</template>
<style scoped lang="scss">
.validate-code {
	display: flex;
	justify-content: space-between;
	padding: 15px 0;

	:deep(.el-input) {
		font-size: 20px;
		height: 50px;

		width: 50px;

		input {
			text-align: center;
		}
	}
}
</style>
