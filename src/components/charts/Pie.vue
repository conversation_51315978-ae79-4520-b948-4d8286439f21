<script setup>
import {computed} from 'vue'

const props = defineProps({
	data: {
		type: Array,
		default: () => [],
	},
})

const option = computed(() => {
	if (!props.data?.db || props.data.db.length === 0) return null

	const content = JSON.parse(props.data.content)
	const {db} = props.data

	const o = {
		tooltip: {
			trigger: 'item',
			formatter: '{a} <br/>{b} : {c} ({d}%)',
		},
		series: [
			{
				type: 'pie',
				radius: [20, 100],
				center: ['50%', '50%'],
				roseType: 'area',
				itemStyle: {
					borderRadius: 8,
				},
				data: db.map((d) => ({value: d[content.y], name: d[content.x]})),
			},
		],
	}
	return o
})
</script>
<template>
	<div class="chart-pie">
		<template v-if="option">
			<h4>统计结果</h4>
			<Charts
				v-if="option.series[0].data.every((d) => typeof d === 'number')"
				v-bind="$attrs"
				:option="option"
			></Charts>
			<div v-else class="pd-10">当前数据无法生成柱状图</div>
		</template>
		<div v-else>
			<LoadingTransition text="正在生成图表..." align="left" />
		</div>
	</div>
</template>
<style scoped lang="scss">
.chart-pie {
	h4 {
		font-size: 14px;
		padding: 10px 0;
		text-align: center;
	}
}
</style>
