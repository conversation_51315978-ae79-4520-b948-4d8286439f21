<script setup>
import {computed} from 'vue'

const props = defineProps({
	data: {
		type: Array,
		default: () => [],
	},
})

const option = computed(() => {
	if (!props.data?.db || props.data.db.length === 0) return null

	const content = JSON.parse(props.data.content)
	const {db} = props.data

	const keys = db.map((d) => d[content.x])
	const values = db.map((d) => d[content.y])

	const o = {
		tooltip: {
			trigger: 'axis',
		},
		grid: {
			left: '3%',
			right: '5%',
			bottom: '3%',
			top: '5%',
			containLabel: true,
		},
		xAxis: {
			type: 'category',
			boundaryGap: false,
			data: keys,
		},
		yAxis: {
			type: 'value',
		},
		series: [
			{
				type: 'line',
				stack: 'Total',
				data: values,
			},
		],
	}
	return o
})
</script>
<template>
	<div class="chart-line">
		<template v-if="option">
			<h4>统计结果</h4>
			<Charts v-bind="$attrs" :option="option"></Charts>
		</template>
		<div v-else>
			<LoadingTransition text="正在生成图表..." align="left" />
		</div>
	</div>
</template>
<style scoped lang="scss">
.chart-line {
	h4 {
		font-size: 14px;
		padding: 10px 0;
		text-align: center;
	}
}
</style>
