<script setup lang="ts" name="LeaderEfficiencyAnalysis">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// 数据接口定义
interface LeaderEfficiencyData {
  id: string
  leaderName: string // 分管领导
  todayApprovalCount: number // 今日审批量
  avgProcessTime: string // 平均审核耗时
  rejectCount: number // 驳回次数
  sensitiveOperationCount: number // 敏感操作次数
}

// 本地存储key
const STORAGE_KEY = 'leader_efficiency_data'

// 加载状态
const loading = ref(false)

// 表格ref
const tableRef = ref()
const tableHeight = ref(0)

// 表头配置
const columns = [
  { prop: 'leaderName', label: '分管领导', minWidth: 120, align: 'center'},
  { prop: 'todayApprovalCount', label: '今日审批量', minWidth: 120, align: 'center'},
  { prop: 'avgProcessTime', label: '平均审核耗时', minWidth: 140, align: 'center'},
  { prop: 'rejectCount', label: '驳回次数', minWidth: 120, align: 'center'},
  { prop: 'sensitiveOperationCount', label: '敏感操作次数', minWidth: 150, align: 'center'}
]

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 列表请求参数
const reqParams = reactive({
  skipCount: 0,
  maxResultCount: 10
})

// 表格数据
const tableData = ref<LeaderEfficiencyData[]>([])

// 生成模拟数据
const generateMockData = (): LeaderEfficiencyData[] => {
  const leaders = [
    '张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十',
    '郑十一', '王十二', '冯十三', '陈十四', '褚十五', '卫十六', '蒋十七',
    '沈十八', '韩十九', '杨二十', '朱二十一', '秦二十二', '尤二十三',
    '许二十四', '何二十五', '吕二十六', '施二十七', '张二十八', '孔二十九', '曹三十'
  ]
  
  return leaders.map((name, index) => ({
    id: `leader_${index + 1}`,
    leaderName: name,
    todayApprovalCount: Math.floor(Math.random() * 50) + 1, // 1-50
    avgProcessTime: `${Math.floor(Math.random() * 8) + 1}h`, // 1-8小时
    rejectCount: Math.floor(Math.random() * 10), // 0-9次
    sensitiveOperationCount: Math.floor(Math.random() * 5) // 0-4次
  }))
}

// 从本地存储加载数据
const loadDataFromStorage = (): LeaderEfficiencyData[] => {
  const stored = localStorage.getItem(STORAGE_KEY)
  if (stored) {
    try {
      return JSON.parse(stored)
    } catch (error) {
      console.error('解析本地存储数据失败:', error)
    }
  }
  return []
}

// 保存数据到本地存储
const saveDataToStorage = (data: LeaderEfficiencyData[]) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data))
  } catch (error) {
    console.error('保存数据到本地存储失败:', error)
    ElMessage.error('保存数据失败')
  }
}

// 初始化数据
const initializeData = () => {
  let data = loadDataFromStorage()
  if (data.length === 0) {
    // 如果本地存储没有数据，生成模拟数据
    data = generateMockData()
    saveDataToStorage(data)
  }
  return data
}

// 加载表格数据
const loadTableData = () => {
  loading.value = true
  try {
    const allData = initializeData()
    const startIndex = reqParams.skipCount
    const endIndex = startIndex + reqParams.maxResultCount
    
    tableData.value = allData.slice(startIndex, endIndex)
    pagination.total = allData.length
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type === 'page') {
    pagination.page = val
    reqParams.skipCount = (val - 1) * pagination.size
  } else {
    pagination.size = val
    reqParams.maxResultCount = pagination.size
  }
  loadTableData()
}

// 块高度变化事件
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 75
}

// 初始化
onMounted(() => {
  loadTableData()
})
</script>

<template>
  <div class="leader-efficiency-analysis">
    <Block title="分管领导效率分析" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
      <!-- 列表 -->
      <TableV2
        ref="tableRef"
        :defaultTableData="tableData"
        :columns="columns"
        :req-params="reqParams"
        :enable-toolbar="false"
        :enable-own-button="false"
        :height="tableHeight"
        :loading="loading"
        @loading="loading = $event"
        @completed="() => {}"
      />
      
      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.size"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      />
    </Block>
  </div>
</template>

<route>
{
  meta: {
    title: '分管领导效率分析'
  }
}
</route>

<style scoped lang="scss">
.leader-efficiency-analysis {
  width: 100%;
  height: 100%;

  :deep(.el-table) {
    width: 100%;
  }

  :deep(.el-table__body-wrapper) {
    width: 100%;
  }

  :deep(.el-table__header-wrapper) {
    width: 100%;
  }
}
</style>
