<script setup lang="ts" name="DataSourceConfig">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

import { DataSourceConfigItem, DATA_SOURCE_CONFIG_MAP, getConfigItemName } from '@/define/dataSourceConfig'
import { 
  getDataSourceConfigs, 
  createDataSourceConfig, 
  updateDataSourceConfig, 
  deleteDataSourceConfig 
} from '@/api/DataSourceConfigApi'
import PreviewDialog from '@/components/common/PreviewDialog.vue'



// 搜索表单
const searchFormProp = ref([
  { label: '配置名称', prop: 'name', type: 'text' }
])
const searchForm = ref({ name: '' })

// 加载状态
const loading = ref(false)

// 表格ref
const tableRef = ref()
const tableHeight = ref(0)
const currentRow = ref<DataSourceConfigItem | null>(null)

// 操作按钮
const buttons = [
  { label: '预览', type: 'info' as const, code: 'preview' },
  { label: '编辑', type: 'primary' as const, code: 'edit' },
  { label: '删除', type: 'danger' as const, code: 'delete', popconfirm: '确认删除吗?' }
]

// 表头配置
const columns = [
  { prop: 'index', label: '序号', width: 80 },
  { prop: 'nameZh', label: '名称', minWidth: 150 },
  { prop: 'language', label: '语言', width: 120 },  // 改为单一语言字段
  { prop: 'creator', label: '创建人', width: 120 },
  { prop: 'createTime', label: '创建时间', width: 150 }
]

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 列表请求参数
const reqParams = reactive({
  name: '',
  skipCount: 0,
  maxResultCount: 10
})

// 表格数据类型（扩展了序号和格式化的创建时间）
interface TableDataItem extends Omit<DataSourceConfigItem, 'createTime'> {
  index: number
  createTime: string
}

// 表格数据
const tableData = ref<TableDataItem[]>([])

// 弹窗表单数据类型
interface DialogFormData {
  configKey?: string
  language?: string
  creator?: string
}

// 弹窗相关
const showDialogForm = ref(false)
const dialogFormRef = ref()
const dialogForm = ref<DialogFormData>({})

// 预览相关
const showPreview = ref(false)
const previewConfigId = ref('')
const previewConfigKey = ref('')
const previewLanguage = ref('中文')  // 新增预览语言变量

// 弹窗表单属性
const dialogFormProps = computed(() => [
  {
    label: '配置名称',
    prop: 'configKey',
    type: 'select',
    options: Object.entries(DATA_SOURCE_CONFIG_MAP).map(([key, config]) => ({
      label: config.nameZh,  // 固定显示中文名称
      value: key
    }))
  },
  {
    label: '支持语言',
    prop: 'language',  // 改为单一字段
    type: 'radio',     // 改为单选按钮
    options: [
      { label: '中文', value: '中文' },
      { label: '英文', value: '英文' }
    ]
  },
  { label: '创建人', prop: 'creator', type: 'text' }
])

// 表单验证规则
const dialogFormRules = {
  configKey: [{ required: true, message: '请选择配置名称', trigger: 'change' }],
  language: [{ required: true, message: '请选择支持语言', trigger: 'change' }],  // 改为单一字段
  creator: [{ required: true, message: '请输入创建人', trigger: 'blur' }]
}

// 初始化
onMounted(() => {
  loadTableData()
})

// 加载表格数据
const loadTableData = async () => {
  loading.value = true
  try {
    const response = await getDataSourceConfigs(reqParams)
    tableData.value = response.items.map((item, index) => ({
      ...item,
      index: reqParams.skipCount + index + 1,
      createTime: new Date(item.createTime).toLocaleDateString()
    }))
    pagination.total = response.totalCount
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const onSearch = () => {
  pagination.page = 1
  reqParams.skipCount = 0
  reqParams.maxResultCount = pagination.size
  reqParams.name = searchForm.value.name
  loadTableData()
}

// 表格操作点击事件
const onTableClickButton = ({ row, btn }: any) => {
  if (btn.code === 'preview') {
    previewConfigId.value = row.id
    previewConfigKey.value = row.id
    previewLanguage.value = row.language || '中文'  // 设置预览语言
    showPreview.value = true
  } else if (btn.code === 'edit') {
    currentRow.value = row
    dialogForm.value = {
      configKey: row.id,
      language: row.language,  // 改为单一语言字段
      creator: row.creator
    }
    showDialogForm.value = true
  } else if (btn.code === 'delete') {
    handleDelete(row.id)
  }
}

// 删除操作
const handleDelete = async (id: string) => {
  try {
    await deleteDataSourceConfig(id)
    ElMessage.success('删除成功')
    loadTableData()
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

// 新增
const onClickAdd = () => {
  currentRow.value = null
  dialogForm.value = {
    language: '中文'  // 改为单一字段，默认选中中文
  }
  showDialogForm.value = true
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type === 'page') {
    pagination.page = val
    reqParams.skipCount = (val - 1) * pagination.size
  } else {
    pagination.size = val
    reqParams.maxResultCount = pagination.size
  }
  loadTableData()
}

// 块高度变化事件
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 75
}

// 弹框表单提交
const onDialogConfirm = () => {
  dialogFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true
      try {
        const configKey = dialogForm.value.configKey!
        const configInfo = DATA_SOURCE_CONFIG_MAP[configKey]
        
        const formData = {
          nameZh: configInfo.nameZh,
          nameEn: configInfo.nameEn,
          category: configInfo.category,
          language: dialogForm.value.language!,  // 改为单一语言字段
          creator: dialogForm.value.creator!
        }
        
        if (currentRow.value) {
          await updateDataSourceConfig(currentRow.value.id, formData)
          ElMessage.success('编辑成功')
        } else {
          await createDataSourceConfig(formData)
          ElMessage.success('新增成功')
        }
        
        showDialogForm.value = false
        loadTableData()
      } catch (error) {
        console.error('保存失败:', error)
        ElMessage.error('保存失败')
      } finally {
        loading.value = false
      }
    }
  })
}


</script>

<template>
  <div class="data-source-config">
    <Block title="数据源配置管理" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
      <template #topRight>
        <el-button size="small" type="primary" @click="onClickAdd">新增</el-button>
      </template>
      
      <template #expand>
        <!-- 搜索 -->
        <div class="search">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="2"
            :label-width="74"
            :enable-reset="false"
            confirm-text="查询"
            button-vertical="flowing"
            @submit="onSearch"
          />
        </div>
      </template>
      
      <!-- 列表 -->
      <el-table
        ref="tableRef"
        :data="tableData"
        :height="tableHeight"
        v-loading="loading"
        stripe
        border
      >
        <el-table-column
          v-for="column in columns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
        />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              v-for="btn in buttons"
              :key="btn.code"
              :type="btn.type"
              size="small"
              @click="onTableClickButton({ row, btn })"
            >
              {{ btn.label }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.size"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      />
    </Block>

    <!-- 新增/编辑弹窗 -->
    <Dialog
      v-model="showDialogForm"
      :title="currentRow ? '编辑配置' : '新增配置'"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      @closed="currentRow = null; dialogForm = {}"
      @click-confirm="onDialogConfirm"
    >
      <Form
        ref="dialogFormRef"
        v-model="dialogForm"
        :props="dialogFormProps"
        :rules="dialogFormRules"
        :enable-button="false"
      />
    </Dialog>

    <!-- 预览弹窗 -->
    <PreviewDialog
      v-model:visible="showPreview"
      :config-id="previewConfigId"
      :config-key="previewConfigKey"
      :language="previewLanguage"
      @close="showPreview = false"
    />
  </div>
</template>

<route>
{
  meta: {
    title: '数据源配置管理'
  }
}
</route>

<style scoped lang="scss">
.data-source-config {
  .search {
    margin-bottom: 16px;
  }
}
</style>
