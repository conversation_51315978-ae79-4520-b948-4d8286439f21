<script setup lang="ts" name="DataAnalysisEvaluation">
import { useEvaluationStore, EVALUATION_NAME_OPTIONS } from '@/stores/useEvaluationStore'

// Store
const evaluationStore = useEvaluationStore()

// 搜索表单配置
const searchFormProp = ref([
  {
    label: '评估名称',
    prop: 'name',
    type: 'text',
    placeholder: '请输入评估名称搜索'
  }
])
const searchForm = ref({ name: '' })

// 监听搜索表单变化，实现实时搜索
watch(() => searchForm.value.name, (newValue) => {
  // 立即设置Loading状态，然后执行搜索
  searchLoading.value = true
  debouncedSearch(newValue || '')
}, { immediate: false })

// 初始化数据
onMounted(() => {
  // 强制重新初始化数据，确保时间格式正确
  evaluationStore.forceReinitialize()
  // 初始化当前表格数据
  currentTableData.value = evaluationStore.getFilteredEvaluations('')
  pagination.total = currentTableData.value.length
})

// 加载状态
const loading = ref(false)
const searchLoading = ref(false)
const paginationLoading = ref(false)

// 表格ref
const tableRef = ref()
const tableHeight = ref(0) // 表格高度
const currentRow = ref(null)

// 表格按钮配置
const buttons = [
  { label: '编辑', type: 'primary', code: 'edit' },
  { label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除吗?' }
]

// 表格列配置
const columns = [
  { prop: '名称', label: '名称' },
  { prop: '评估得分', label: '评估得分' },
  { prop: '创建时间', label: '创建时间' },
  { prop: '创建人', label: '创建人' }
]

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 列表请求参数
const reqParams = reactive({
  name: '',
  skipCount: 0,
  maxResultCount: 10
})

// 弹窗相关
const showDialogForm = ref(false) // 新增或编辑弹窗
const dialogFormRef = ref() // 弹窗表单ref

// 弹窗表单数据
const dialogForm: any = ref({})

// 弹窗表单配置
const dialogFormProps = ref([
  {
    label: '评估名称',
    prop: '名称',
    type: 'select',
    options: EVALUATION_NAME_OPTIONS.map(item => ({
      label: item.label,
      value: item.value
    }))
  },
  {
    label: '评估得分(%)',
    prop: '评估得分',
    type: 'text',
    inputType: 'number'
  }
])

// 弹窗表单校验规则
const dialogFormRules = {
  名称: [{ required: true, message: '请选择评估名称', trigger: 'change' }],
  评估得分: [
    { required: true, message: '请输入评估得分', trigger: 'blur' },
    { type: 'number', min: 0, max: 100, message: '评估得分必须在0-100之间', trigger: 'blur' }
  ]
}

// 防抖函数
const debounce = (func: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout
  return (...args: any[]) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(null, args), delay)
  }
}

// 实时搜索函数
const performSearch = async (keyword: string) => {
  try {
    // 注意：searchLoading.value = true 已在watch中设置

    // 模拟搜索延迟 - 先显示Loading再获取数据
    await new Promise(resolve => setTimeout(resolve, 800))

    // 在Loading期间获取过滤数据
    const filteredData = evaluationStore.getFilteredEvaluations(keyword)

    // 更新分页总数
    pagination.total = filteredData.length
    pagination.page = 1 // 重置到第一页

    // 确保数据更新完成后再关闭Loading
    await nextTick()

    return filteredData
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
    return []
  } finally {
    searchLoading.value = false
  }
}

// 防抖搜索
const debouncedSearch = debounce(performSearch, 500)

// 存储当前显示的数据
const currentTableData = ref<any[]>([])

// 计算属性 - 获取过滤后的表格数据
const tableData = computed(() => {
  // 如果正在搜索，返回当前数据，避免闪烁
  if (searchLoading.value) {
    return currentTableData.value
  }

  const keyword = searchForm.value.name || ''
  const filteredData = evaluationStore.getFilteredEvaluations(keyword)

  // 更新当前数据
  currentTableData.value = filteredData

  // 更新分页总数
  nextTick(() => {
    pagination.total = filteredData.length
  })

  return filteredData
})

// 块高度变化事件时修改表格高度
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 75
}

// 查询
const onSearch = async () => {
  try {
    // 设置Loading状态
    loading.value = true

    // 模拟查询延迟，增加真实性
    await new Promise(resolve => setTimeout(resolve, 1200))

    pagination.page = 1
    reqParams.skipCount = 0
    reqParams.maxResultCount = pagination.size
    // 其他查询参数
    reqParams.name = searchForm.value.name
    tableRef.value.reload()
  } catch (error) {
    console.error('查询失败:', error)
    ElMessage.error('查询失败，请重试')
  } finally {
    loading.value = false
  }
}

// 表格操作点击事件, row 当前行数据
const onTableClickButton = ({ row, btn }: any) => {
  if (btn.code === 'edit') {
    currentRow.value = row
    Object.assign(dialogForm.value, {
      名称: row.名称,
      评估得分: parseInt(row.评估得分.replace('%', ''))
    })
    showDialogForm.value = true
  } else if (btn.code === 'delete') {
    const success = evaluationStore.deleteEvaluation(row.id)
    if (success) {
      ElMessage.success('删除成功')
      tableRef.value.reload()
      // 强制更新分页总数
      nextTick(() => {
        pagination.total = tableData.value.length
      })
    } else {
      ElMessage.error('删除失败')
    }
  }
}

// 新增
const onClickAdd = () => {
  currentRow.value = null
  dialogForm.value = {}
  showDialogForm.value = true
}

// 分页事件
const onPaginationChange = async (val: any, type: any) => {
  try {
    paginationLoading.value = true

    // 模拟分页加载延迟
    await new Promise(resolve => setTimeout(resolve, 600))

    if (type == 'page') {
      pagination.page = val
      reqParams.skipCount = (val - 1) * pagination.size
    } else {
      pagination.size = val
      reqParams.maxResultCount = pagination.size
      pagination.page = 1 // 改变每页大小时重置到第一页
    }

    tableRef.value.reload()
  } catch (error) {
    console.error('分页操作失败:', error)
    ElMessage.error('分页操作失败，请重试')
  } finally {
    paginationLoading.value = false
  }
}

// 弹窗表单提交
const onDialogConfirm = () => {
  dialogFormRef.value.validate((valid: boolean) => {
    if (valid) {
      // 处理新增或编辑
      loading.value = true

      try {
        if (currentRow.value) {
          // 编辑
          const result = evaluationStore.updateEvaluation(currentRow.value.id, {
            名称: dialogForm.value.名称,
            评估得分: dialogForm.value.评估得分
          })

          if (result) {
            ElMessage.success('编辑成功')
            showDialogForm.value = false
            tableRef.value.reload()
            // 强制更新分页总数
            nextTick(() => {
              pagination.total = tableData.value.length
            })
          } else {
            ElMessage.error('编辑失败')
          }
        } else {
          // 新增
          evaluationStore.addEvaluation({
            名称: dialogForm.value.名称,
            评估得分: dialogForm.value.评估得分
          })

          ElMessage.success('新增成功')
          showDialogForm.value = false
          tableRef.value.reload()
          // 强制更新分页总数
          nextTick(() => {
            pagination.total = tableData.value.length
          })
        }
      } catch (error) {
        ElMessage.error('操作失败')
        console.error(error)
      } finally {
        loading.value = false
      }
    }
  })
}

// 初始化数据
onMounted(() => {
  evaluationStore.initializeData()
})
</script>

<template>
  <div class="data-analysis-evaluation">
    <Block title="数据分析评估" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
      <template #topRight>
        <el-button size="small" type="primary" @click="onClickAdd">新建评估</el-button>
      </template>
      <template #expand>
        <!-- 搜索 -->
        <div class="search">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="2"
            :label-width="74"
            :enable-reset="false"
            confirm-text="查询"
            button-vertical="flowing"
            :loading="loading"
            @submit="onSearch"
          ></Form>
        </div>
      </template>

      <!-- 主内容区域 - 应用搜索Loading -->
      <div class="main-content" v-loading="searchLoading" element-loading-background="rgba(255, 255, 255, 0.8)">
        <!-- 列表 -->
        <TableV2
          ref="tableRef"
          :columns="columns"
          :req-params="reqParams"
          :enable-toolbar="false"
          :enable-own-button="false"
          :height="tableHeight"
          :buttons="buttons"
          :defaultTableData="tableData"
          :loading="loading || paginationLoading"
          @loading="loading = $event"
          @click-button="onTableClickButton"
        ></TableV2>

        <!-- 分页 -->
        <div class="pagination-container">
          <Pagination
            :total="pagination.total"
            :current-page="pagination.page"
            :page-size="pagination.size"
            :disabled="paginationLoading || searchLoading"
            @current-change="onPaginationChange($event, 'page')"
            @size-change="onPaginationChange($event, 'size')"
          ></Pagination>
        </div>
      </div>
    </Block>

    <Dialog
      v-model="showDialogForm"
      :title="currentRow ? '编辑评估' : '新建评估'"
      :destroy-on-close="true"
      :loading="loading"
      @closed="currentRow = null; dialogForm = {}"
      @click-confirm="onDialogConfirm"
    >
      <Form
        ref="dialogFormRef"
        v-model="dialogForm"
        :props="dialogFormProps"
        :rules="dialogFormRules"
        :enable-button="false"
      ></Form>
    </Dialog>
  </div>
</template>
<route>
  {
    meta: {
      title: '数据分析评估',
    },
  }
</route>
<style scoped lang="scss">
.data-analysis-evaluation {
  .search {
    position: relative;
    min-height: 60px;
    margin-bottom: 16px;
  }

  .main-content {
    position: relative;
    min-height: 400px;

    // 主内容区域Loading样式优化
    :deep(.el-loading-mask) {
      background-color: rgba(255, 255, 255, 0.9);
      border-radius: 8px;
      z-index: 1000;
    }

    :deep(.el-loading-spinner) {
      margin-top: -50px;
    }

    :deep(.el-loading-text) {
      color: #409eff;
      font-size: 14px;
      margin-top: 10px;
    }
  }

  .pagination-container {
    position: relative;
    padding: 20px 0;

    // 分页禁用状态样式
    :deep(.el-pagination.is-disabled) {
      opacity: 0.6;
      pointer-events: none;
    }

    :deep(.el-pagination__total),
    :deep(.el-pagination__sizes),
    :deep(.el-pagination__jump) {
      color: #606266;
    }
  }
}
</style>
