<script setup lang="ts" name="fieldTemplate">
import { ref, onMounted, computed, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { router } from '@/router'

// 字段信息模板数据接口
interface FieldTemplate {
  id: string
  templateName: string
  templateDescription: string
  relatedFields: string[]
  relatedFieldsCount: number
  createTime: string
  status: boolean
}

// 本地缓存key
const TEMPLATE_CACHE_KEY = 'fieldTemplateList'
const FIELD_TYPE_CACHE_KEY = 'fieldTypeList'

// 响应式数据
const loading = ref(false)
const tableData = ref<FieldTemplate[]>([])
const filteredData = ref<FieldTemplate[]>([])
const selectedRows = ref<FieldTemplate[]>([])
const showDialogForm = ref(false)
const showDetailDialog = ref(false)
const currentRow = ref<FieldTemplate | null>(null)

// 搜索表单
const searchForm = ref({
  templateName: '',
  status: ''
})

// 分页
const pagination = ref({
  page: 1,
  size: 10,
  total: 0
})

// 表格高度
const tableHeight = ref(800)

// 弹窗表单
const dialogForm = ref<Partial<FieldTemplate>>({
  templateName: '',
  templateDescription: '',
  relatedFields: [],
  status: true
})

// 表单验证规则
const dialogFormRules = {
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  templateDescription: [
    { required: true, message: '请输入模板描述', trigger: 'blur' },
    { min: 5, max: 200, message: '长度在 5 到 200 个字符', trigger: 'blur' }
  ]
}

// 表格列定义
const columns = [
  { field: 'templateName', title: '模板名称', minWidth: 200 },
  { field: 'templateDescription', title: '模板描述', minWidth: 300 },
  { field: 'relatedFieldsCount', title: '关联字段数', minWidth: 120 },
  { field: 'createTime', title: '创建时间', minWidth: 180 },
  { field: 'status', title: '状态', minWidth: 100 }
]

// 操作按钮
const buttons = [
  { title: '详情', code: 'detail', type: 'info' },
  { title: '编辑', code: 'edit', type: 'primary' },
  { title: '删除', code: 'delete', type: 'danger' }
]

// 详情表单配置
const detailFormProps = [
  { prop: 'templateName', label: '模板名称', type: 'text', readonly: true },
  { prop: 'templateDescription', label: '模板描述', type: 'textarea', readonly: true },
  { prop: 'relatedFieldsCount', label: '关联字段数', type: 'text', readonly: true },
  { prop: 'createTime', label: '创建时间', type: 'text', readonly: true },
  { prop: 'statusText', label: '状态', type: 'text', readonly: true }
]

// 编辑表单配置
const dialogFormProps = [
  { prop: 'templateName', label: '模板名称', type: 'text', placeholder: '请输入模板名称' },
  { prop: 'templateDescription', label: '模板描述', type: 'textarea', placeholder: '请输入模板描述' },
  { prop: 'relatedFields', label: '关联字段', type: 'select', multiple: true, options: [] },
  { prop: 'status', label: '状态', type: 'switch' }
]

// 从缓存加载数据
const loadFromCache = (): FieldTemplate[] => {
  const cached = localStorage.getItem(TEMPLATE_CACHE_KEY)
  if (cached) {
    return JSON.parse(cached)
  }

  // 返回一些测试数据来验证表格显示
  return [
    {
      id: '1',
      templateName: '基础信息模板',
      templateDescription: '包含姓名、年龄、性别等基础字段的模板',
      relatedFields: ['name', 'age', 'gender'],
      relatedFieldsCount: 3,
      createTime: '2024-01-15 10:30:00',
      status: true
    },
    {
      id: '2',
      templateName: '联系信息模板',
      templateDescription: '包含电话、邮箱、地址等联系方式字段的模板',
      relatedFields: ['phone', 'email', 'address'],
      relatedFieldsCount: 3,
      createTime: '2024-01-16 14:20:00',
      status: true
    }
  ]
}

// 保存到缓存
const saveToCache = (data: FieldTemplate[]) => {
  localStorage.setItem(TEMPLATE_CACHE_KEY, JSON.stringify(data))
}

// 加载字段类型选项
const loadFieldTypeOptions = () => {
  const cached = localStorage.getItem(FIELD_TYPE_CACHE_KEY)
  if (cached) {
    const fieldTypes = JSON.parse(cached)
    dialogFormProps[2].options = fieldTypes.map((item: any) => ({
      label: item.name,
      value: item.id
    }))
  }
}

// 应用过滤器
const applyFilters = () => {
  let filtered = [...tableData.value]
  
  if (searchForm.value.templateName) {
    filtered = filtered.filter(item => 
      item.templateName.toLowerCase().includes(searchForm.value.templateName.toLowerCase())
    )
  }
  
  if (searchForm.value.status !== '') {
    const status = searchForm.value.status === 'true'
    filtered = filtered.filter(item => item.status === status)
  }
  
  filteredData.value = filtered
  pagination.value.total = filtered.length
  pagination.value.page = 1
}

// 获取当前页数据
const getCurrentPageData = () => {
  const start = (pagination.value.page - 1) * pagination.value.size
  const end = start + pagination.value.size
  return filteredData.value.slice(start, end)
}

// 搜索
const onSearch = () => {
  applyFilters()
}

// 重置搜索
const onReset = () => {
  searchForm.value = {
    templateName: '',
    status: ''
  }
  applyFilters()
}

// 新增
const onClickAdd = () => {
  currentRow.value = null
  dialogForm.value = {
    templateName: '',
    templateDescription: '',
    relatedFields: [],
    status: true
  }
  showDialogForm.value = true
}

// 返回
const onBack = () => {
  router.push('/fieldType')
}

// 表格操作
const onTableClickButton = ({ btn, scope }: any) => {
  const row = scope?.row || scope
  
  if (btn.code === 'detail') {
    currentRow.value = {
      ...row,
      statusText: row.status ? '启用' : '停用'
    }
    showDetailDialog.value = true
  } else if (btn.code === 'edit') {
    currentRow.value = row
    Object.assign(dialogForm.value, row)
    showDialogForm.value = true
  } else if (btn.code === 'delete') {
    deleteRecord(row.id)
  }
}

// 删除记录
const deleteRecord = (id: string) => {
  ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = tableData.value.findIndex(item => item.id === id)
    if (index > -1) {
      tableData.value.splice(index, 1)
      saveToCache(tableData.value)
      applyFilters()
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 保存表单
const onDialogConfirm = () => {
  if (currentRow.value) {
    // 编辑
    const index = tableData.value.findIndex(item => item.id === currentRow.value!.id)
    if (index > -1) {
      tableData.value[index] = {
        ...tableData.value[index],
        ...dialogForm.value,
        relatedFieldsCount: dialogForm.value.relatedFields?.length || 0
      }
    }
  } else {
    // 新增
    const newRecord: FieldTemplate = {
      id: Date.now().toString(),
      templateName: dialogForm.value.templateName!,
      templateDescription: dialogForm.value.templateDescription!,
      relatedFields: dialogForm.value.relatedFields || [],
      relatedFieldsCount: dialogForm.value.relatedFields?.length || 0,
      createTime: new Date().toLocaleString(),
      status: dialogForm.value.status !== false
    }
    tableData.value.unshift(newRecord)
  }
  
  saveToCache(tableData.value)
  applyFilters()
  showDialogForm.value = false
  ElMessage.success(currentRow.value ? '编辑成功' : '新增成功')
}

// 状态切换
const onStatusChange = (row: FieldTemplate) => {
  const index = tableData.value.findIndex(item => item.id === row.id)
  if (index > -1) {
    tableData.value[index].status = row.status
    saveToCache(tableData.value)
    ElMessage.success(`已${row.status ? '启用' : '停用'}`)
  }
}

// 选择变化
const onSelectionChange = (selection: FieldTemplate[]) => {
  selectedRows.value = selection
}

// 分页变化
const onPaginationChange = (value: number, type: 'page' | 'size') => {
  pagination.value[type] = value
}

// Block高度变化
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 200
}

// 初始化
onMounted(() => {
  loadFieldTypeOptions()
  tableData.value = loadFromCache()
  applyFilters()
})
</script>
<route>
{
meta: {
title:'字段信息模板',
ignoreLabel:false
}
}
</route>
<template>
  <div class="field-template">
    <Block title="字段信息模板" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
      <template #topRight>
        <el-button size="small" type="primary" @click="onClickAdd">新增模板</el-button>
        <el-button size="small" @click="onBack">返回</el-button>
      </template>
      
      <template #expand>
        <!-- 搜索区域 -->
        <div class="search-area">
          <el-form :model="searchForm" inline>
            <el-form-item label="模板名称">
              <el-input 
                v-model="searchForm.templateName" 
                placeholder="请输入模板名称"
                clearable
              />
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
                <el-option label="启用" value="true" />
                <el-option label="停用" value="false" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">查询</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </template>

      <!-- 列表 -->
      <BaseTableComp
        ref="tableRef"
        :data="getCurrentPageData()"
        :colData="columns"
        :buttons="buttons"
        :loading="loading"
        :checkbox="true"
        :row-key="'id'"
        :reserve-selection="true"
        :auto-height="false"
        :height="tableHeight"
        :currentPage="pagination.page"
        :pageSize="pagination.size"
        :total="pagination.total"
        :visibleSetting="false"
        :visibleIndex="true"
        @clickButton="onTableClickButton"
        @selection-change="onSelectionChange"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      >
        <template #status="{ rowData }">
          <el-switch
            v-model="rowData.status"
            @change="onStatusChange(rowData)"
            active-color="#13ce66"
            inactive-color="#ff4949"
          />
        </template>
      </BaseTableComp>
    </Block>

    <!-- 新增/编辑弹窗 -->
    <Dialog
      v-model="showDialogForm"
      :title="currentRow ? '编辑模板' : '新增模板'"
      width="500px"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      @closed="currentRow = null; Object.assign(dialogForm, {})"
      @click-confirm="onDialogConfirm"
    >
      <Form
        ref="dialogFormRef"
        v-model="dialogForm"
        :props="dialogFormProps"
        :rules="dialogFormRules"
        :enable-button="false"
      />
    </Dialog>

    <!-- 详情弹窗 -->
    <Dialog
      v-model="showDetailDialog"
      title="模板详情"
      width="500px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="showDetailDialog = false"
    >
      <Form
        v-if="currentRow"
        v-model="currentRow"
        :props="detailFormProps"
        :enable-button="false"
      />
    </Dialog>
  </div>
</template>

<style scoped>
.field-template {
  height: 100%;
}

.search-area {
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 16px;
}
</style>
