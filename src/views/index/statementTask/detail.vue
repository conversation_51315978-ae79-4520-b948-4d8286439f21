<route>
	{
		meta: {
			childTitle: '',
		},
	}
</route>
<script setup lang="ts" name="detail">
import {GetBatch, periodList, reportTastStatus, surplusDate} from '@/define/statement.define'
import querystring from 'querystring'
import {ElMessage, ElMessageBox, ElNotification, TabsPaneContext, dayjs} from 'element-plus'
import {inject, onMounted, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import ScheduleProgressComp from '@/components/common/schedule-progress-comp.vue'
import util from '@/plugin/util'
import {APIConfig} from '@/api/config'
import {PushReportFlowTask, PushReportAudit, GetPlanTaskProcess, urging} from '@/api/ReportApi'
import ViewFlow from '@/components/common/ViewFlow.vue'
import {ReportsFlowStatusEnum, ReportsFlowStatus} from '@/define/statement.define'
import {GetWorkflowProcess} from '@/api/workflowTaskApi'
import {GetReportLogList} from '@/api/WorkflowApi'
import {useFlowRecord} from '@/hooks/useFlowRecord'
import BusinessProcess from '@/components/BusinessProcess.vue'
import {updateLabelTitle} from '@/hooks/useLabels'
const route = useRoute()
const router = useRouter()
const showFlow = ref(false)
const flowCode = ref('')
const currentReportInfo: any = ref(null)
const currentTaskInfo: any = ref(null)

// const axios = inject<Axios>('#axios')
const axios = inject('#axios') as Request
const pageParams = ref({
	MaxResultCount: 10,
	SkipCount: 0,
})
const titleList = ref([
	{
		width: '25%',
		title: '任务名称',
		key: 'taskName',
	},
	{
		width: '25%',
		title: '创建部门',
		key: 'creationOrganizationUnitName',
	},
	{
		width: '25%',
		title: '创建人',
		key: 'creatorName',
	},
	// {
	// 	width: '25%',
	// 	title: '填报周期',
	// 	key: 'period',
	// },
	{
		width: '25%',
		title: '填报期限',
		key: 'timeLimit',
	},
	{
		width: '25%',
		title: '填报范围',
		key: 'range',
	},
	{
		width: '25%',
		title: '审核提交时间',
		key: 'creationTime',
	},
	{
		width: '25%',
		title: '填报截止时间',
		key: 'endTime',
	},
	// {
	// 	width: '75%',
	// 	title: '填报说明',
	// 	key: 'description',
	// },
	// {
	// 	width: '50%',
	// 	title: '来件部门',
	// 	key: 'parentAreaOrganizationUnitName',
	// },
	// {
	// 	width: '25%',
	// 	title: '审核人',
	// 	key: 'auditor',
	// },
])
const infoOverViewHeight = ref(0)
const data = ref()
// 获取计划任务的子报表任务列表
const colData = [
	// {
	// 	title: '所属批次',
	// 	field: 'planTaskId',
	// },
	{
		title: '数据量',
		field: 'totalDataRows',
		width: '120',
	},
	{
		title: '状态',
		field: 'reportTaskStatus',
		width: '120',
	},
	{
		title: '完成情况',
		field: 'departmentReportingStatus',
	},
	// {
	// 	title: '下发/分发时间',
	// 	field: 'creationTime',
	// },
	{
		title: '填报截止时间',
		field: 'newToDay',
		width: '150',
	},
]
// 获取报表任务所属子任务
const childColData = ref([
	{
		title: '填报部门',
		field: 'filler',
	},
	{
		title: '填报人',
		field: 'actualFillerName',
	},
	{
		title: '数据量',
		field: 'totalDataRows',
		width: '100',
	},
	// {
	// 	title: '提交时间',
	// 	field: 'fillerSubmitTime',
	// 	width: '150',
	// },
	{
		title: '汇总时间',
		field: 'mergeTime',
		width: '150',
	},
	{
		title: '附件',
		field: 'attachments',
		width: '100',
	},
	{
		title: '补充说明',
		field: 'fillingDescription',
	},
])
const childButtons = [
	{
		type: 'primary',
		code: 'detail',
		title: '查看数据',
		icon: '<i i-majesticons-eye-line></i>',
		verify: 'true',
		// verify: 'row.childrenStatus === 2 && !row.isAlien && row.status !== 1 && row.status !== 0',
	},
	// {
	// 	type: 'primary',
	// 	code: 'childAudit',
	// 	title: '审核',
	// 	verify: 'row.status === 1',
	// },
	// {
	// 	type: 'primary',
	// 	code: 'urge',
	// 	title: '催办',
	// 	icon: '<i i-majesticons-clock-line></i>',
	// 	verify: 'row.fillingMode == 2 || (row.fillingMode == 1 && (row.status == -1 || row.status == 1 || row.status == 3 || row.status == 4))',
	// },
	{
		type: 'primary',
		code: 'export',
		title: '导出',
		icon: '<i i-majesticons-inbox-in-line></i>',
		verify: 'row.childrenStatus === 2 && !row.isAlien && row.status !== 1 && row.status !== 0',
	},
]
const tableData = ref<any>()
const totalCount = ref(10)
const buttons = [
	{
		type: 'primary',
		code: 'detail',
		title: '查看',
		icon: '<i i-majesticons-eye-line></i>',
		verify: 'true',
	},
]
const clickButton = (btn: {
	btn: {
		code: string
		title: string
		verify: string
	}
	scope: any
}) => {
	router.push({
		path: '/statementTask/report-task-detail',
		query: {
			reportTaskId: btn.scope.id,
			areaOrganizationUnitId: btn.scope.creatingAreaOrganizationUnitId,
			type: 'detail',
		},
	})
}

const sizeChange = (val: number) => {
	pageSize.value = val
	pageParams.value.MaxResultCount = val
	pageParams.value.SkipCount = 0
	getChildReportTasks(data.value.id)
}
const currentPage = ref(1)
const pageSize = ref(10)
const currentFillMode = ref(0)
const statusText = ref(0)
const currentChange = (val: number) => {
	currentPage.value = val
	pageParams.value.SkipCount = pageParams.value.MaxResultCount * (val - 1)
	getChildReportTasks(data.value.id)
}
const getChildReportTasks = async (id: string) => {
	await axios
		?.get(
			`/api/filling/plan-task/${id}/child-report-tasks?${querystring.stringify(
				pageParams.value
			)}`
		)
		.then((res: any) => {
			console.log(1111, res.data, data.value)

			if (res) {
				currentFillMode.value = res.data.items[0]?.fillingMode
				tableData.value = res.data.items
				totalCount.value = res.data.totalCount
				statusText.value = data.value.status
				// 如果是一次性
				if (
					data.value.fillingPeriodType === 1 &&
					data?.value.status !== 2 &&
					data?.value.status !== 1 &&
					// data?.value.status !== 4 &&
					data?.value.status !== 6 &&
					data?.value.status !== 7 &&
					data?.value.status !== 11
				) {
					data.value = {
						...data.value,
						// statusText: reportTastStatus[tableData.value[0]?.reportTaskStatus - 1]?.name,
						statusText: ReportsFlowStatus.filter((f: any) => f.type === 'plan').find(
							(f: any) => f.value === data.value.status
						)?.label,
						departmentReportingStatus: tableData.value[0]?.departmentReportingStatus,
					}
					expandChange(tableData.value[0])
				}
			}
		})
}

const getTaskInfo = async () => {
	return new Promise((resolve, reject) => {
		axios?.get(`/api/filling/plan-task/${route.query.id}`).then((res) => {
			currentTaskInfo.value = res.data
			data.value = {
				// ...res.data,
				creationOrganizationUnitName: res.data.createdDepartment ?? '-',
				creatorName: res.data.creatorName ?? '-',
				creationTime: dayjs(res.data.creationTime).format('YYYY-MM-DD'),
				taskName: res.data.name,
				status: res.data.status,
				id: res.data.id,
				description: res.data.description,
				fillingMode: res.data.fillingMode,
				enabledTime: dayjs(res.data.enabledTime).format('YYYY-MM-DD'),
				period: periodList[res.data.fillingPeriodType - 1].label,
				timeLimit: `${
					res.data.status === 5
						? '已完结'
						: surplusDate(
								res.data.fillingPeriodType,
								res.data.newToDays,
								res.data.endDate
						  ) === false
						? '已截止'
						: '剩余' +
						  surplusDate(
								res.data.fillingPeriodType,
								res.data.newToDays,
								res.data.endDate
						  ) +
						  '天'
				}`,
				range: res.data.fillingRange,
				endTime: dayjs(res.data?.endDate).format('YYYY-MM-DD HH:mm:ss'),
				newToDay: dayjs(res.data?.newToDay).format('YYYY-MM-DD HH:mm:ss'),
				auditor: '-',
				// 预留来件部门
				// 预留审核提交时间
				// auditor:
				// 	res.data.auditor === null
				// 		? res.data.inChargeLeader === null
				// 			? '-'
				// 			: res.data.inChargeLeader.name
				// 		: res.data.auditor?.name,
				auditReason: res.data.auditReason,
				totalDataRows: res.data.totalDataRows,
				reportTaskStatus: res.data.reportTaskStatus,
				departmentReportingStatus: res.data.departmentReportingStatus,
				fillingPeriodType: res.data.fillingPeriodType,
				fileInfos: res.data.fileInfos ?? [],
				isAlien: res.data.isAlien,
				// 预留审核提交人
			}
			// getChildReportTasks(res.data.id)
			// 待审核状态详情添加启用时间字段
			if (res.data.enabledTime !== null && res.data.status === 2) {
				titleList.value.push({
					width: '100%',
					title: '启用时间',
					key: 'enabledTime',
				})
			}
			if (res.data.status === 3 || res.data.status === 4) {
				// titleList.value.push({
				// 	width: '100%',
				// 	title: res.data.status === 3 ? '审核意见' : '驳回原因',
				// 	key: 'auditReason',
				// })
			}
			if (res.data.fileInfos && res.data.fileInfos.length !== 0) {
				titleList.value.push({
					width: '100%',
					title: '相关附件',
					key: 'fileInfos',
				})
			}

			console.log('当前任务详情', currentTaskInfo.value)
			getBusinessProcess()
			updateLabelTitle({
				path: router.currentRoute.value.fullPath,
				title: `${route.query.type === 'detail' ? '查看' : '编辑'}-报表创建-${
					data.value.taskName
				}`,
			})
			resolve('success')
		})
	})
}

const logs: any = ref({})
const getLogList = async (val: string, type: any = null) => {
	if (type === 'process') {
		await GetWorkflowProcess(val).then((res) => {
			const _logs = res.data.logs
			const tasks = res.data.tasks.filter(
				(v: any) =>
					(v.type === 1 || v.type === 5 || v.type === 9) &&
					(v.state === 1 || v.state === 5)
			)

			logs.value[type] = useFlowRecord().toList(
				[
					..._logs,
					{
						unitName: tasks[0]?.unitName,
						des: '数据审核',
						userName: Array.from(new Set(tasks.map((v: any) => v.userName))).join(','),
						creationTime: tasks[0]?.creationTime,
					},
				],
				['unitName', 'des', 'userName', 'creationTime']
			) as any
		})
	} else if (type === 'issue' || type === 'data') {
		await GetReportLogList(val).then((res) => {
			let isDepartment = currentTaskInfo.value.departments.length > 0
			const names = res.data.map((v: any) => `${v.departmentName}-${v.name}`)

			if (type === 'issue') {
				logs.value[type] = [
					{
						label: '创建人',
						text: '',
						time: '',
						user: currentTaskInfo.value.creatorName,
					},
					{label: '下发审核', text: '', time: '', user: names.join(',')},
				]
			}

			if (type === 'data') {
				let arr = isDepartment
					? currentTaskInfo.value.departments
					: currentTaskInfo.value.staffs

				logs.value[type] = [
					{
						label: '填报数据',
						text: `${arr.map((v: any) => `${v.name}<br />`).join('')}`,
						time: '',
						user: '',
					},
					{label: '审核数据', text: '', time: '', user: names.join(',')},
				]
			}
		})
	}
	console.log('流程记录', logs.value)
}

const _detailInfo = ref()
// 获取报表任务详情
const _getReportInfo = async (__reportId: string, __areaOrganizationUnitId: string) => {
	return new Promise((resolve, reject) => {
		axios?.get(`/api/filling/report-task-ou/${__reportId}`).then((res) => {
			const {data} = res

			currentReportInfo.value = data

			if (
				logs.value['data'] &&
				currentReportInfo.value.reportTaskAreaOrganizationUnitFillers.length > 0
			) {
				const status: any = {
					1: '待填写',
					2: '已提交',
					3: '已驳回',
				}

				logs.value['data'][0] = {
					label: '填报数据',
					text: currentReportInfo.value.reportTaskAreaOrganizationUnitFillers
						.map(
							(v: any) =>
								v.filler.name +
								' - ' +
								status[v.reportTaskAreaOrganizationUnitFillerStatus] +
								'<br />' +
								v.submitTime +
								'<br />'
						)
						.join(''),
				}
			}
			if (data) {
				_detailInfo.value = {
					taskName: data.reportTask.name,
					creationOrganizationUnitName: data?.createdDepartment ?? '-',
					department: data.department,
					creatorName: data.creatorName,
					endDate: dayjs(data.reportTask.newToDay).format('YYYY-MM-DD'),
					description: data.reportTask.description,
					fillingDescription: data.fillingDescription,
					id: data.id,
					batch: GetBatch(data.reportTask.fillingPeriodType, data.creationTime),
					status: data.status,
					reportTaskId: data.reportTaskId,
					areaOrganizationUnitId: data.areaOrganizationUnitId,
					submitTime: data.submitTime,
					auditTime: data.auditTime,
					areaOrganizationUnit: data.areaOrganizationUnit,
					totalDataRows: data.totalDataRows,
					reportTask: data.reportTask,
					attachments: data.reportTask.attachments,
				}
			}
			resolve('success')
		})
	})
}
const dataVisible = ref(false)
const notifyType = ref<number>()
const dataAll = () => {
	dataVisible.value = true
	headerDataVisible.value = true
	// __reportHeadList()
	// sheetClick(__reportHeadList.value[0].id)

	__getReportList2(_detailInfo.value.id)
}

const __getReportList2 = async (id?: string) => {
	let sheet
	const res = await axios?.get(`/api/filling/report-task-ou/${id}/report-tables`)
	const {data} = res
	// 选项卡
	// for (let x of data) {
	sheet = data[0]
	__reportHeadList2.value = data
	__currentHead2.value = sheet.id
	// await __getReportListById(sheet.id, true, sheet)
	const result = await axios.batch(`/api/filling/report-table/${sheet.id}/data`)
	console.log(111, result)

	const celldata = []
	for (let i = 0; i < result.length; i++) {
		const item = result[i]
		const cells = JSON.parse(item.rawData)
		// 根据后端排序修复当前行列 r
		celldata.push(xlsxRef.value.$fixCellIndex(cells, item.rowNum - 1))
	}
	// Xlsx - plus sheets
	__excel_data.value = [
		{
			name: sheet.name,
			head: JSON.parse(sheet.header || '[]'),
			data: celldata.flat(),
			config: JSON.parse(sheet.globalStyle || '{}'),
		},
	] as any
	//
}
const __onCloseSheet = (sheet: any) => {
	dataVisible.value = false
}
const childclickButton = async (btn: {
	btn: {
		code: string
		title: string
		verify: string
	}
	scope: any
}) => {
	if (btn.btn.code === 'detail') {
		console.log(1111, btn)

		if (btn.scope && !btn.scope.parentId && btn.scope.parentId === undefined) {
			dataAll()
		} else {
			router.push({
				path: '/statementTask/report-task-detail',
				query: {
					reportTaskId: btn.scope.reportTaskId,
					areaOrganizationUnitId: btn.scope.areaOrganizationUnitId,
					id: btn.scope.id,
					type: 'detail',
					from: 'mycreate',
				},
			})
		}
	} else if (btn.btn.code === 'urge') {
		console.log(btn)
		if (!btn.scope.children) {
			if (data.value.fillingMode === 2) {
				// 内部填报
				innerUserIds.value = btn.scope.users.map((v) => v.id)
			} else {
				// 下发填报
				areaOrganizationId.value = btn.scope.department?.id
			}
		} else {
			areaOrganizationId.value = ''
		}
		isUrging.value = true
		// ElMessageBox({
		// 	title: '任务催办',
		// 	showCancelButton: true,
		// 	message: `将发送消息提示${btn.scope.status === 3 ? '填报人' : '审核人'}进行${
		// 		btn.scope.status === 3 ? '填报' : '审核'
		// 	}操作`,
		//  h('div', {style: 'margin-left:10px'}, [
		// 	h(
		// 		'p',
		// 		{},
		// 		`将发送消息提示${btn.scope.status === 3 ? '填报人' : '审核人'}进行${
		// 			btn.scope.status === 3 ? '填报' : '审核'
		// 		}操作`
		// 	),
		// 	h(
		// 		'div',
		// 		{
		// 			style:
		// 				'display:flex;align-items:center;padding-right:60px;flex:1;justify-content: space-between;',
		// 		},
		// 		[
		// 			h('div', {}, [
		// 				h(ElCheckbox, {
		// 					modelValue: messageModalNotify.value.NotifyDing,
		// 					'onUpdate:modelValue': (val: any) => {
		// 						messageModalNotify.value.NotifyDing = val
		// 						messageModalNotify.value.messageDing = false
		// 						messageModalNotify.value.phoneDing = false
		// 						notifyType.value = val ? 1 : undefined
		// 					},
		// 				}),
		// 				h('span', {style: 'display:inline-block;margin-left:5px'}, '消息通知DING'),
		// 			]),
		// 			h('div', {}, [
		// 				h(ElCheckbox, {
		// 					modelValue: messageModalNotify.value.messageDing,
		// 					'onUpdate:modelValue': (val: any) => {
		// 						messageModalNotify.value.messageDing = val
		// 						messageModalNotify.value.NotifyDing = false
		// 						messageModalNotify.value.phoneDing = false
		// 						notifyType.value = val ? 2 : undefined
		// 					},
		// 				}),
		// 				h('span', {style: 'display:inline-block;margin-left:5px'}, '短信DING'),
		// 			]),
		// 			h('div', {}, [
		// 				h(ElCheckbox, {
		// 					modelValue: messageModalNotify.value.phoneDing,
		// 					'onUpdate:modelValue': (val: any) => {
		// 						messageModalNotify.value.phoneDing = val
		// 						messageModalNotify.value.messageDing = false
		// 						messageModalNotify.value.NotifyDing = false
		// 						notifyType.value = val ? 3 : undefined
		// 					},
		// 				}),
		// 				h('span', {style: 'display:inline-block;margin-left:5px'}, '电话DING'),
		// 			]),
		// 		]
		// 	),
		// ]),
		// })
		// 	.then(() => {
		// 		axios
		// 			?.post(`/api/filling/message-notice/part-urge`, {
		// 				reportTaskId: btn.scope.reportTaskId,
		// 				areaOrganizationUnitId: btn.scope.areaOrganizationUnitId,
		// 				dingMessageType: notifyType.value ?? 1,
		// 			})
		// 			.then((res) => {
		// 				// if (res.status === 200 || res.status === 204) {
		// 				ElNotification.success({
		// 					message: '已通知相关人员',
		// 					duration: 2000,
		// 				})
		// 				// }
		// 			})
		// 	})
		// 	.catch((action) => {
		// 		// console.log(action)
		// 	})
	} else if (btn.btn.code === 'export') {
		await getChildReportList(btn.scope.realId ? btn.scope.realId : btn.scope.id)
	} else if (btn.btn.code === 'revoke') {
		ElMessageBox({
			title: '任务撤回',
			showCancelButton: true,
			message: `将撤回该任务，该任务下的所有填报任务将自动撤回，请确认操作`,
		}).then(() => {
			axios
				?.delete(
					`/api/filling/report-task-ou/${btn.scope.reportTaskId}/${btn.scope.areaOrganizationUnitId}`
				)
				.then(async (res) => {
					ElNotification.success({
						title: '通知',
						message: '已撤回该任务',
					})
					// await _getReportInfo()
				})
		})
	} else if (btn.btn.code === 'childAudit') {
		PushReportAudit({
			reportTaskId: btn.scope.reportTaskId,
			areaOrganizationUnitId: btn.scope.areaOrganizationUnitId,
		})
			.then(() => {
				ElMessage.success('审核成功')
				getChildReportTasks(data.value.id)
			})
			.catch((err) => {
				window.errMsg(err, '审核')
			})
	}
}
const isUrging = ref(false)
const areaOrganizationId = ref('')
const innerUserIds = ref([])
const urgingList = ref(['0', '1'])
// 催办确定
const onUrging = () => {
	let urgingMessageTypes: any = []
	if (urgingList.value.length == 0) {
		return ElMessage.error('请选择通知方式')
	}
	urgeingLoading.value = true
	urgingList.value.forEach((item: any) => {
		if (item == '0') {
			urgingMessageTypes.push(0)
		} else {
			urgingMessageTypes.push(Number(item))
		}
	})
	let urgingId: any = ''
	// if (statusText.value == 3) {
	urgingId = route.query.id
	// }
	let data: any = {
		urgingMessageTypes: urgingMessageTypes,
		areaOrganizationId: '',
		reportTaskType: 0,
		userIds: [],
	}
	if (areaOrganizationId.value) {
		data.areaOrganizationId = areaOrganizationId.value
	} else {
		delete data.areaOrganizationId
	}
	if (innerUserIds.value.length !== 0) {
		data.userIds = innerUserIds.value
	} else {
		delete data.userIds
	}
	urging(urgingId, data).then((res) => {
		console.log(res)
		ElMessage.success('催办成功')
		urgeingLoading.value = false
		isUrging.value = false
	})
	// if (!newLedgerName.value) return ElMessage.error('请填写业务表名称')
	// updateLedgerName(currentDialogRow.value.id, newLedgerName.value).then((res: any) => {
	// 	getLedgerData()

	// 	openChangeLedgerName.value = false
	// 	ElMessage.success('更新成功')
	// })
}
const urgeingLoading = ref(false)
// 催办取消
const closeUrging = () => {
	urgingList.value = ['0', '1']
	isUrging.value = false
	urgeingLoading.value = false
}
const __reportHeadList = ref<any[]>([])
const _data = ref<any>()
const __excel_data = ref([])
const __currentHead = ref('')
const __currentHead2 = ref('')
const __excel_sheetId = ref('')

const headerDataVisible = ref(false)
const sheets = ref([])
const __getReportList = () => {
	axios?.get(`/api/filling/plan-task/${route.query.id}/table-templates`).then((res) => {
		const {data} = res
		if (data && data[0]) {
			// sheet
			const {data} = res
			if (data && data[0]) {
				const [sheet] = data // 默认显示第一个

				// 选项卡
				__reportHeadList.value = data
				__currentHead.value = sheet.id
				// Xlsx - plus sheets
				sheets.value = [
					{
						name: sheet.name,
						head: JSON.parse(sheet.header || '[]'),
						data: JSON.parse(sheet.statisticCells || '[]'),
						config: JSON.parse(sheet.globalStyle || '{}'),
					},
				] as any
			}
		}
	})
}
const __tabClick = (pane: TabsPaneContext, ev: Event) => {
	const headData = __reportHeadList.value.filter((f: any) => f.id == (pane.paneName as string))[0]
	// 表头, 权限配置
	// const { head, config } = JSON.parse(headData.globalStyle)
	__excel_sheetId.value = headData.id
	sheets.value = [
		{
			name: headData.displayName,
			head: JSON.parse(headData.header || '[]'),
			config: JSON.parse(headData.globalStyle || '{}'),
			data: JSON.parse(headData.statisticCells || '[]'),
		},
	] as any
}
const __tabClickAll = async (pane: TabsPaneContext, ev: Event) => {
	const headData = __reportHeadList2.value.filter(
		(f: any) => f.id == (pane.paneName as string)
	)[0]

	const result = await axios.batch(`/api/filling/report-table/${headData.id}/data`)
	console.log(result)
	const celldata: any = []

	if (result.length > 0) {
		for (const element of result) {
			const item = element
			const cells = JSON.parse(item.rawData)
			// 根据后端排序修复当前行列 r
			celldata.push(xlsxRef.value?.$fixCellIndex(cells, item.rowNum - 1))
		}
	}

	__excel_data.value = [
		{
			name: headData.displayName,
			head: JSON.parse(headData.header || '[]'),
			config: JSON.parse(headData.globalStyle || '{}'),
			data: celldata.flat(),
		},
	] as any
	xlsxRef.value?.$reload()
}
const sheetClick = (id: string) => {
	headerDataVisible.value = true
	const headData = __reportHeadList.value.filter((f: any) => f.id == (id as string))[0]
	// 表头, 权限配置

	__excel_sheetId.value = headData.id
	__excel_data.value = [
		{
			name: headData.displayName,
			head: JSON.parse(headData.header || '[]'),
			config: JSON.parse(headData.globalStyle || '{}'),
			data: [],
		},
	] as any
}
const xlsxPlusRef = ref()
const tableRef = ref()
const showProgress = ref(true)
const onToggleInfoOverViewVisible = (hide: boolean, height: number) => {
	if (hide) {
		infoOverViewHeight.value = height
	} else {
		infoOverViewHeight.value = 0
	}

	// if()
	if (xlsxPlusRef.value) xlsxPlusRef.value?.$resize()
	if (tableRef.value) {
		setTimeout(() => {
			tableRef.value.resize()
		}, 500)
	}
	setTimeout(() => {
		showProgress.value = !hide
	}, 100)
}
// const _reportId = ref()
const childTableData = ref<any[]>([])
const getChildrenReportTask = async (req: {
	reportTaskId: string
	areaOrganizationUnitId: string
	id: string
}) => {
	await axios?.get(`/api/filling/report-task-ou/${req.id}/child-report-tasks`).then((res) => {
		console.log(11222, currentFillMode.value)

		const resData = res.data.items
			.map((re: any, index: number) => ({
				...re,
				id: re.id,
				parentId: re.reportTaskId,
				totalDataRows: re.totalDataRows === null ? 0 : re.totalDataRows,
				filler:
					re.directlyAssignedToFillerName === null
						? re.department?.parent?.region?.name +
						  '-' +
						  re.department?.parent?.name +
						  '-' +
						  re.department?.name
						: re.department?.parent?.region?.name +
						  '-' +
						  re.department?.parent?.name +
						  '-' +
						  re.department?.name +
						  '-' +
						  re.directlyAssignedToFillerName,
				status: currentFillMode.value === 1 ? re.status : data.value?.status,
				childrenStatus: re.status,
				attachments:
					_detailInfo.value.status === 2 || re.status === 14
						? re.reportTask.attachments
						: [],
			}))
			.concat([
				{
					..._detailInfo.value,
					// status: -1,
					totalDataRows: _detailInfo.value.totalDataRows,
					childrenStatus: 2,
					id: _detailInfo.value.reportTaskId,
					realId: _detailInfo.value.id,
					filler: _detailInfo.value?.department?.region?.name,
					isAlien: data.value.isAlien,
					attachments: [],
					mergeTime: _detailInfo.value.mergeTime,
					submitTime: _detailInfo.value.submitTime,
				},
			])
		const index = resData.some((item) => item.status === 0 || item.status === 2) ? -2 : -1
		resData[0].status = index
		childTableData.value = util.arrayToTree(resData)
		console.log('表格数据11--', childTableData.value)
	})
}
const expandChange = (val) => {
	if (!val) return
	setTimeout(async () => {
		await _getReportInfo(
			val.parentReportTaskAreaOrganizationUnitId,
			val.creatingAreaOrganizationUnitId
		)

		if (val.fillingMode === 1) {
			childColData.value = [
				{
					title: '填报部门',
					field: 'filler',
				},
				{
					title: '填报人',
					field: 'actualFillerName',
				},
				{
					title: '数据量',
					field: 'totalDataRows',
					width: '100',
				},
				// {
				// 	title: '提交时间',
				// 	field: 'fillerSubmitTime',
				// 	width: '150',
				// },
				{
					title: '汇总时间',
					field: 'mergeTime',
					width: '150',
				},
				{
					title: '附件',
					field: 'attachments',
					width: '100',
				},
				{
					title: '补充说明',
					field: 'fillingDescription',
				},
			]
			await getChildrenReportTask({
				reportTaskId: _detailInfo.value.reportTaskId,
				areaOrganizationUnitId: _detailInfo.value.areaOrganizationUnitId,
				id: _detailInfo.value.id,
			})
		} else {
			childColData.value = [
				{
					title: '填报部门',
					field: 'filler',
				},
				{
					title: '数据量',
					field: 'totalDataRows',
					width: '100',
				},
				// {
				// 	title: '提交时间',
				// 	field: 'fillerSubmitTime',
				// 	width: '150',
				// },
				{
					title: '汇总时间',
					field: 'mergeTime',
					width: '150',
				},
				{
					title: '附件',
					field: 'attachments',
					width: '100',
				},
				{
					title: '补充说明',
					field: 'fillingDescription',
				},
			]
			await getInnerChildTask({
				reportTaskId: _detailInfo.value.reportTaskId,
				areaOrganizationUnitId: _detailInfo.value.areaOrganizationUnitId,
			})
		}
	}, 256)
}
const getInnerChildTask = async (params: {
	reportTaskId: string
	areaOrganizationUnitId: string
}) => {
	await axios
		?.get(
			`/api/filling/report-task-ou-filler/inner-writer-fillers?AreaOrganizationUnitId=${params.areaOrganizationUnitId}&ReportTaskId=${params.reportTaskId}`
		)
		.then((res: any) => {
			const resData = res.data
			console.log(1111, resData, currentFillMode.value, _detailInfo.value, data.value)
			childTableData.value = [
				{
					id: 'childReportTask1',
					totalDataRows: resData.totalDataRows,
					parentId: 1,
					filler:
						resData.fillers.length !== 0
							? resData.fillers.map((v) => v.name).toString()
							: resData.actualFillerName,
					status: resData.status,
					users: resData.fillers,
					childrenStatus: 2,
					attachments:
						_detailInfo.value.status === 2 || _detailInfo.value.status === 14
							? resData.reportTask.attachments
							: [],
					reportTaskId: params.reportTaskId,
					areaOrganizationUnitId: params.areaOrganizationUnitId,
					auditTime: resData.auditTime,
					submitTime: resData.submitTime,
					mergeTime: resData.mergeTime,
					department: resData.department,
					fillingMode: resData.fillingMode,
				},
			]
			console.log('表格数据22--', childTableData.value)
		})
}
const handlePreviewOrDownload = (att: any) => {
	// window.open(APIConfig('base') + att.path, '_blank')
	util.downloadAttachment(att, 1)
}
const __reportHeadList2 = ref()
const getChildReportList = async (id: string) => {
	const res: any = await axios?.get(`/api/filling/report-task-ou/${id}/report-tables`)
	const {data} = res
	__reportHeadList2.value = data
	if (res) {
		getDownloadData()
	}
}
const _downloadData = ref([])
const xlsxRef = ref()
const exportRef = ref()
let reportList: any = []
async function getDownloadData() {
	for (let sheet of __reportHeadList2.value) {
		const result = await axios?.batch(`/api/filling/report-table/${sheet.id}/data`)
		reportList = result
		//viewStore.enableLoading = true
		if (sheet) {
			const celldata: any = []
			// 创建行索引映射，确保每行数据都有值
			const rowMap = {}

			// 第一步：解析表头数据并添加到行映射中
			const headerData = JSON.parse(sheet.header || '[]')
			headerData.forEach((cell: any) => {
				if (!rowMap[cell.r]) {
					rowMap[cell.r] = []
				}
				rowMap[cell.r].push(cell)
			})

			// 第二步：收集所有行数据
			for (let i = 0; i < reportList.length; i++) {
				const item = reportList[i]
				const cells = JSON.parse(item.rawData)
				// 根据后端排序修复当前行列 r
				const fixedCells = xlsxRef.value.$fixCellIndex(cells, item.rowNum - 1)
				celldata.push(fixedCells)

				// 记录每行的数据
				fixedCells.forEach((cell: any) => {
					if (!rowMap[cell.r]) {
						rowMap[cell.r] = []
					}
					// 检查是否已存在相同位置的单元格
					const existingCellIndex = rowMap[cell.r].findIndex((c: any) => c.c === cell.c)
					if (existingCellIndex >= 0) {
						// 如果已存在，则更新值（优先使用非空值）
						if (
							cell.v &&
							(!rowMap[cell.r][existingCellIndex].v ||
								rowMap[cell.r][existingCellIndex].v === '-')
						) {
							rowMap[cell.r][existingCellIndex] = cell
						}
					} else {
						rowMap[cell.r].push(cell)
					}
				})
			}

			// 第三步：处理合并单元格（包括表头和内容）
			const config = JSON.parse(sheet.globalStyle || '{}')
			const merges = config.merge || {}

			// 确保合并单元格的所有单元格都有值
			Object.values(merges).forEach((merge: any) => {
				const {r, c, rs, cs} = merge
				// 获取左上角单元格的值
				const topLeftCell = rowMap[r]?.find((cell: any) => cell.c === c)
				if (topLeftCell) {
					const topLeftValue = topLeftCell.v
						? JSON.parse(JSON.stringify(topLeftCell.v))
						: {
								v: '',
								m: '',
								ct: {fa: '@', t: 's'},
						  }

					// 确保合并区域内的所有单元格都有相同的值
					for (let i = 0; i < rs; i++) {
						for (let j = 0; j < cs; j++) {
							if (i === 0 && j === 0) continue // 跳过左上角单元格

							const currentRow = r + i
							const currentCol = c + j

							// 确保行存在
							if (!rowMap[currentRow]) {
								rowMap[currentRow] = []
							}

							// 检查该位置是否已有单元格
							const existingCellIndex = rowMap[currentRow].findIndex(
								(cell: any) => cell.c === currentCol
							)
							if (existingCellIndex >= 0) {
								// 更新值，即使原单元格有值也更新，确保合并单元格内容一致
								rowMap[currentRow][existingCellIndex].v = topLeftValue
							} else {
								// 创建新单元格并复制值
								const newCell = {
									r: currentRow,
									c: currentCol,
									v: topLeftValue, // 使用左上角单元格的值
								}
								rowMap[currentRow].push(newCell)
							}
						}
					}
				}
			})

			// 第四步：确保所有单元格都有值（包括空白单元格）
			// 找出所有行和列的最大值
			let maxRow = 0
			let maxCol = 0

			Object.keys(rowMap).forEach((rowIdx: string) => {
				const rowIndex = parseInt(rowIdx)
				const row = rowMap[rowIdx] as any[]
				maxRow = Math.max(maxRow, rowIndex)
				row.forEach((cell: any) => {
					maxCol = Math.max(maxCol, cell.c)
				})
			})

			// 确保每个单元格位置都有数据
			for (let rowIndex = 0; rowIndex <= maxRow; rowIndex++) {
				if (!rowMap[rowIndex.toString()]) {
					rowMap[rowIndex.toString()] = []
				}

				const row = rowMap[rowIndex.toString()] as any[]

				// 确保每一列都有单元格
				for (let col = 0; col <= maxCol; col++) {
					const hasCell = row.some((cell: any) => cell.c === col)
					if (!hasCell) {
						// 如果该位置没有单元格，创建一个空单元格
						row.push({
							r: rowIndex,
							c: col,
							v: {
								v: '',
								m: '',
								ct: {fa: '@', t: 's'},
							},
						})
					}
				}

				// 按列排序
				rowMap[rowIndex.toString()] = row.sort((a: any, b: any) => a.c - b.c)
			}

			// 将所有行数据合并为一个扁平数组，并按行号排序
			const sortedRows = Object.keys(rowMap)
				.map(Number)
				.sort((a, b) => a - b)
				.map((rowIndex) => rowMap[rowIndex])

			const formatCelldata = sortedRows.flat()

			if (formatCelldata.length > 0) {
				formatCelldata.forEach((v: any) => {
					if (v.v?.ct) {
						// 去除时间格式
						if (v.v?.ct.t === 'd') {
							return
						} else {
							// 其他所有格式都会强制转为字符串
							if (v.v?.ct?.fa && v.v?.ct?.fa === 'General') {
								v.v.ct.fa = '@'
							}

							if (v.v?.ct?.fa && v.v?.ct?.fa === '#0.00000E+00') {
								v.v.ct.fa = '@'
								v.v.v = v.v.v ? v.v.v.toString() : ''
								v.v.m = v.v.v ? v.v.v.toString() : ''
							}
							if (v.v?.ct?.t && v.v?.ct?.t === 'g') {
								v.v.ct.t = 's'
							}
							if (v.v.v !== null && v.v.v !== undefined) {
								v.v.m = v.v.v.toString()
								if (v.v.v === 0) {
									v.v.v = '0'
								}
							} else {
								// 确保空值也有正确的格式
								v.v.v = ''
								v.v.m = ''
							}
						}
					} else if (v.v) {
						// 确保v.v有ct属性
						v.v.ct = v.v.ct || {fa: '@', t: 's'}
					}

					// 确保0值正确显示
					if (v.v && v.v.v === 0) {
						v.v.v = '0'
					}
				})
			}

			// Xlsx - plus sheets
			_downloadData.value = [
				{
					name: sheet.name.replace(/[*?:\\\/\[\]]/g, ''),
					head: [], // 不再单独处理表头，所有数据都在formatCelldata中
					data: formatCelldata,
					config: JSON.parse(sheet.globalStyle || '{}'),
				},
			] as any
			exportRef.value?.onClick(_downloadData.value)
		}
	}
}

let xlsxPlusParent: any = null
const isFull = ref(false)
const onFull = () => {
	isFull.value = !isFull.value

	const xlsxPlus = document.querySelector('.xlsx-plus-component') as HTMLElement
	if (isFull.value) {
		xlsxPlusParent = xlsxPlus.parentElement
		xlsxPlus.classList.add('xlsx-plus-component-full')
		document.body.appendChild(xlsxPlus)
	} else {
		xlsxPlus.classList.remove('xlsx-plus-component-full')
		xlsxPlusParent.appendChild(xlsxPlus)
		xlsxPlusParent = null
	}
	if (xlsxPlusRef.value) xlsxPlusRef.value?.$resize()
}

const autoHeight = ref(0)
const getAutoHeight = () => {
	const height = document.documentElement.clientHeight - 350
	autoHeight.value = height
}
const blockHistoryRef = ref<any>(null)
const blockRef = ref<any>(null)
const openHeight = ref(0)
const openOrCloseSchedule = (type: number) => {
	openHeight.value = document.body.clientHeight - 140
	if (type === 1) {
		showProgress.value = true
		if (xlsxPlusRef.value) xlsxPlusRef.value?.$resize()
	}
	if (type === 2) {
		showProgress.value = false
		if (xlsxPlusRef.value) xlsxPlusRef.value?.$resize()
	}
}

const onClickViewFlow = (n: number) => {
	if (n === 1) {
		flowCode.value = currentTaskInfo.value?.issuedAuditWorkflowSchemeCode
	} else if (n === 2) {
		flowCode.value = currentTaskInfo.value?.dataAuditWorkflowSchemeCode
	}
	if (!flowCode.value) {
		ElMessage.error('未找到流程')
		return
	}
	showFlow.value = true
}

const businessProcessData: any = ref({})
const getBusinessProcess = () => {
	GetPlanTaskProcess(currentTaskInfo.value.id).then((res: any) => {
		businessProcessData.value = res.data
	})
}

const onClickText = (e: any, item: any) => {
	console.log(111, childTableData.value)

	const btn = {
		code: 'detail',
		title: '查看数据',
		verify: 'true',
	}
	if (e.target.dataset.type === 'staff') {
		childclickButton({btn, scope: childTableData.value[0]})
	} else if (e.target.dataset.type === 'department') {
		const scope = childTableData.value[0].children.find(
			(v: any) => v.id === e.target.dataset.id
		)
		childclickButton({btn, scope})
	}
}

onMounted(async () => {
	updateLabelTitle({
		path: router.currentRoute.value.fullPath,
		title: route.query.type === 'detail' ? '查看-报表创建' : '编辑-报表创建',
	})
	getAutoHeight()
	await getTaskInfo()
	__getReportList()
	if (data.value && data.value.status !== 1) {
		titleList.value.push(
			{
				width: '25%',
				title: '任务状态',
				key: 'statusText',
			},
			{
				width: '25%',
				title: '填报情况',
				key: 'departmentReportingStatus',
			}
			// {
			// 	width: '25%',
			// 	title: '下发审核流程',
			// 	key: '_issueFlow',
			// },
			// {
			// 	width: '75%',
			// 	title: '数据审核流程',
			// 	key: '_dataFlow',
			// }
		)
		await getChildReportTasks(data.value.id)
	}
	//
	setTimeout(() => {
		blockHistoryRef.value?.resize()
		blockRef.value?.resize()
	}, 500)
	onToggleInfoOverViewVisible(false, 0)
})
</script>
<template>
	<div class="report-task-detail">
		<div
			v-show="!showProgress"
			class="open-history"
			:style="{height: openHeight + 'px'}"
			@click="openOrCloseSchedule(1)"
		>
			<span
				>展开流程记录
				<i
					class="icon i-ic-baseline-keyboard-double-arrow-right"
					style="position: relative; left: -1px"
				></i
			></span>
		</div>

		<div v-show="showProgress" class="right">
			<Block
				ref="blockHistoryRef"
				title="流程记录"
				:enable-back-button="false"
				:enable-expand-content="false"
				:enable-fixed-height="true"
				:enable-close-button="false"
			>
				<template #topRight>
					<span
						style="
							cursor: pointer;
							display: flex;
							align-items: center;
							color: var(--z-main);
						"
						@click="openOrCloseSchedule(2)"
					>
						<i class="icon i-ic-baseline-keyboard-double-arrow-left"></i>
						收起
					</span>
				</template>

				<BusinessProcess
					:data="businessProcessData"
					:department-jump="true"
					@click-text="onClickText"
				></BusinessProcess>
			</Block>
		</div>

		<div class="left">
			<Block
				ref="blockRef"
				title="任务详情"
				:enable-fixed-height="true"
				:enable-expand-content="false"
			>
				<template #topRight>
					<ExportComp
						ref="exportRef"
						:sheets="_downloadData"
						:fileName="_data?.name"
						style="visibility: hidden"
					></ExportComp>
				</template>
				<div class="df mg-bottom-15">
					<InfoOverViewComp
						:data="data"
						:titles="titleList"
						@onToggleVisible="onToggleInfoOverViewVisible"
					>
						<template #_dataFlow>
							<div style="display: flex; align-items: center; padding: 0 10px">
								<el-button type="primary" size="small" @click="onClickViewFlow(2)"
									>查看流程</el-button
								>
							</div>
						</template>
						<template #_issueFlow>
							<div style="display: flex; align-items: center; padding: 0 10px">
								<el-button type="primary" size="small" @click="onClickViewFlow(1)"
									>查看流程</el-button
								>
							</div>
						</template>
					</InfoOverViewComp>
				</div>
				<div
					class="info-overview-comp df flex mg-top-15 mg-bottom-15 aic"
					style="height: 40px"
					v-if="data?.status === 3"
				>
					<div
						class="df h-full aic jcc"
						style="
							border-top: none;
							border-right: 1px solid #dcdfe6;
							border-left: none;
							width: 130px;
						"
					>
						收集报表
					</div>
					<div class="df flx h-full aic cursor-pointer mg-left-5">
						<el-link
							:underline="false"
							type="primary"
							v-for="item in __reportHeadList"
							@click="sheetClick(item.id)"
							>{{ item.displayName }}</el-link
						>
					</div>
				</div>
				<!-- 若状态为已创建或待审核或已驳回则查看报表 否则查看表格列表 -->
				<el-tabs
					v-model="__currentHead"
					type="card"
					@tab-click="__tabClick"
					v-if="
						data?.status === ReportsFlowStatusEnum.PendingAudit ||
						data?.status === ReportsFlowStatusEnum.Created ||
						data?.status === ReportsFlowStatusEnum.Rejected ||
						data?.status === ReportsFlowStatusEnum.InChargeLeaderPendingAudit ||
						data?.status === ReportsFlowStatusEnum.DataLeaderPendingAudit ||
						data?.status === ReportsFlowStatusEnum.Revoke ||
						data?.status === ReportsFlowStatusEnum.PendingReview ||
						data?.status === ReportsFlowStatusEnum.PendingExecution
					"
				>
					<el-tab-pane
						v-for="item of __reportHeadList"
						:label="item?.displayName"
						:name="item?.id"
					></el-tab-pane>
				</el-tabs>

				<div
					class="excel-box"
					:style="{height: `calc(100% - 220px + ${infoOverViewHeight}px)`}"
					v-if="
						data?.status === ReportsFlowStatusEnum.PendingAudit ||
						data?.status === ReportsFlowStatusEnum.Created ||
						data?.status === ReportsFlowStatusEnum.Rejected ||
						data?.status === ReportsFlowStatusEnum.InChargeLeaderPendingAudit ||
						data?.status === ReportsFlowStatusEnum.DataLeaderPendingAudit ||
						data?.status === ReportsFlowStatusEnum.Revoke ||
						data?.status === ReportsFlowStatusEnum.PendingReview ||
						data?.status === ReportsFlowStatusEnum.PendingExecution
					"
				>
					<XlsxPlusComp
						class="xlsx-plus-component"
						ref="xlsxPlusRef"
						:sheets="sheets"
						:enableReload="true"
						:style="{height: autoHeight - 30 + 'px'}"
						:viewonly="route.query.type === 'detail' || route.query.type === 'audit'"
					>
						<template #headerRight>
							<div class="excel_tips"></div>
							<el-button
								type="primary"
								size="small"
								style="display: flex; align-items: center"
								class="mg-left-10"
								@click="onFull"
							>
								<template v-if="isFull">
									<i
										i-ic-sharp-fullscreen-exit
										mr-5px
										style="font-size: 18px"
									></i>
									<span>收起</span>
								</template>
								<template v-else>
									<i i-ic-baseline-fullscreen mr-5px style="font-size: 18px"></i>
									<span>全屏</span>
								</template>
							</el-button>
						</template>
					</XlsxPlusComp>
				</div>
				<!-- 此处为了兼容以前的多批次任务 -->

				<BaseTableComp
					v-if="
						data?.status !== 2 &&
						data?.status !== 1 &&
						data?.status !== ReportsFlowStatusEnum.Rejected &&
						// data?.status !== 4 &&
						data?.status !== 6 &&
						data?.status !== 7 &&
						data?.status !== 11 &&
						data?.status !== 13 &&
						data?.fillingPeriodType !== 1
					"
					:colData="colData"
					ref="tableRef"
					:data="tableData"
					:checkbox="false"
					:buttons="buttons"
					:total="totalCount"
					:current-page="currentPage"
					:page-size="pageSize"
					height="auto"
					:visible-setting="false"
					:visible-search="false"
					:visible-header="false"
					:visible-page="true"
					:visible-export="false"
					:visibleExpand="true"
					@click-button="clickButton"
					@size-change="sizeChange"
					@current-change="currentChange"
					@expand-change="expandChange"
				>
					<template #planTaskId="scope">
						{{ GetBatch(scope.rowData.fillingPeriodType, scope.rowData.creationTime) }}
					</template>
					<template #newToDay="scope">
						{{ dayjs(scope.rowData.newToDay).format('YYYY-MM-DD') }}
					</template>
					<template #reportTaskStatus="scope">
						<span
							:style="{
								color: reportTastStatus[scope.rowData.reportTaskStatus - 1].color,
							}"
						>
							{{ reportTastStatus[scope.rowData.reportTaskStatus - 1].name }}
						</span>
					</template>

					<template #expand="scope">
						<div class="w-full h-full pd-left-19 pd-right-10">
							<BaseTableComp
								style="margin-top: 15px"
								:colData="childColData"
								:data="childTableData"
								row-key="id"
								:checkbox="false"
								:buttons="childButtons"
								:total="totalCount"
								:visible-setting="false"
								:visible-search="false"
								:visible-export="false"
								:visible-header="false"
								:visible-page="false"
								@click-button="childclickButton"
							>
								<template #attachments="scope">
									<!-- {{ scope.rowData.attachments }} -->
									<el-popover
										placement="bottom"
										:width="150"
										trigger="hover"
										v-if="scope.rowData.attachments.length !== 0"
									>
										<template #reference>
											<el-link type="primary">附件</el-link>
										</template>
										<div v-for="att of scope.rowData.attachments" mb="5px">
											<el-link
												type="primary"
												@click="handlePreviewOrDownload(att)"
											>
												{{
													att.name.includes('-')
														? att.name.split('-').slice(1).join()
														: att.name
												}}</el-link
											>
										</div>
									</el-popover>
									<span v-else>-</span>
								</template>
								<!-- <template #fillingDescription="scope">
				{{ scope.rowData.status }}
			</template> -->
							</BaseTableComp>
						</div>
					</template>
				</BaseTableComp>

				<BaseTableComp
					v-if="
						data?.status !== 2 &&
						data?.status !== 1 &&
						data?.status !== ReportsFlowStatusEnum.Rejected &&
						// data?.status !== 4 &&
						data?.status !== 6 &&
						data?.status !== 7 &&
						data?.status !== 11 &&
						data?.status !== 13 &&
						data?.status !== ReportsFlowStatusEnum.PendingExecution &&
						data?.fillingPeriodType === 1
					"
					class="mg-top-20"
					height="auto"
					:colData="childColData"
					:data="childTableData"
					row-key="id"
					:checkbox="false"
					:buttons="childButtons"
					:total="totalCount"
					:visible-index="false"
					:visible-setting="false"
					:visible-search="false"
					:visible-export="false"
					:visible-header="false"
					:visible-page="false"
					@click-button="childclickButton"
					default-expand-all
				>
					<template #attachments="scope">
						<!-- {{ scope.rowData.attachments }} -->
						<el-popover
							placement="bottom"
							:width="150"
							trigger="hover"
							v-if="scope.rowData.attachments.length !== 0"
						>
							<template #reference>
								<el-link type="primary">附件</el-link>
							</template>
							<div v-for="att of scope.rowData.attachments" mb="5px">
								<el-link type="primary" @click="handlePreviewOrDownload(att)">
									{{
										att.name.includes('-')
											? att.name.split('-').slice(1).join()
											: att.name
									}}</el-link
								>
							</div>
						</el-popover>
						<span v-else>-</span>
					</template>
					<!-- <template #fillingDescription="scope">
				{{ scope.rowData.status }}
			</template> -->
				</BaseTableComp>

				<DrawerPlusComp
					:visible="headerDataVisible"
					@onClose="headerDataVisible = false"
					title="表格模板"
				>
					<template #content>
						<el-tabs v-model="__currentHead2" type="card" @tab-click="__tabClickAll">
							<el-tab-pane
								v-for="item of __reportHeadList2"
								:label="item?.displayName"
								:name="item?.id"
							></el-tab-pane>
						</el-tabs>
						<XlsxPlusComp
							ref="xlsxRef"
							:sheets="__excel_data"
							:enableReload="true"
							:viewonly="true"
						>
						</XlsxPlusComp>
					</template>
				</DrawerPlusComp>
			</Block>
		</div>

		<ViewFlow v-model="showFlow" :code="flowCode"></ViewFlow>
		<Dialog
			v-model="isUrging"
			title="报表催办"
			width="600"
			@clickConfirm="onUrging"
			@close="closeUrging"
			:loading="urgeingLoading"
		>
			<div>
				<p>将批量通知各未完成填报部门尽快完成填报</p>
				<el-checkbox-group v-model="urgingList" style="margin-top: 10px">
					<el-checkbox label="发送系统内消息" value="0" />
					<el-checkbox label="发送渝快政工作通知" value="1" />
					<el-checkbox label="发送渝快政Ding消息" value="2" />
				</el-checkbox-group>
			</div>
		</Dialog>
	</div>
</template>
<style lang="scss" scoped>
.report-task-detail {
	display: flex;
	.left {
		flex: 1;
		width: calc(100% - 320px);
	}

	.right {
		margin-right: 20px;
		width: 300px;
		flex: none;
	}

	.open-history {
		align-items: center;
		border-bottom: 3px solid rgba(var(--z-line-rgb), 0.5);
		background-color: #fff;
		cursor: pointer;
		display: flex;
		line-height: 1.5;
		margin-right: 20px;
		justify-content: center;
		span {
			color: var(--z-main);
			font-size: 16px;
			width: 20px;
		}
		width: 50px;
	}
}

:deep(.el-tabs__header) {
	margin-top: 10px;
	margin-bottom: 0;
}

.excel-box {
	border-radius: 0 5px 5px 5px;
	box-shadow: 0 -6px 6px -6px rgba(0, 0, 0, 0.12);
	border: 1px solid #e4e7ed;
	border-top: none;
	// height: calc(100% - 230px);
	padding: 0 10px 10px 10px;
}

.excel_tips {
	align-items: center;
	display: flex;

	i {
		color: var(--z-warning);
	}

	span {
		color: var(--z-warning);
		padding: 0 10px;
	}
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
	border-radius: 5px;
	background: var(--z-theme);
}
.xlsx-plus-component-full {
	position: fixed !important;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	padding: 0 10px 10px 10px !important;
	height: 100% !important;
	z-index: 9;
	background-color: #fff;
}
</style>
