<route>
	{
		meta: {
			childTitle: '新增-临时报表创建',
		},
	}
</route>
<script setup lang="ts" name="create">
import {useRoute, useRouter} from 'vue-router'
import {
	computed,
	h,
	inject,
	onActivated,
	onDeactivated,
	onMounted,
	reactive,
	ref,
	toRaw,
	watch,
} from 'vue'
import {
	ElCheckbox,
	ElMessage,
	ElMessageBox,
	ElNotification,
	ElImageViewer,
	FormInstance,
	FormRules,
	dayjs,
} from 'element-plus'

import departmentFavoriteComp from '@/components/common/department-favorite-comp.vue'
import util from '@/plugin/util'
import {useTaskManageStore} from '@/stores/taskManageStore'
import {STAFFROLEARRAY} from '@/define/organization.define'
import {periodEnum, week} from '@/define/statement.define'
import {useArrayToTree} from '@/hooks/useConvertHook'
import {useUserStore} from '@/stores/useUserStore'
import {uploadFile} from '@/plugin/upload'
import {FlowType} from '@/define/Workflow'
import {deleteLabel} from '@/hooks/useLabels'
import DataDistribution from './components/DataDistribution.vue'

const route = useRoute()
const router = useRouter()
const axios = inject('#axios') as any
const taskManageStore = useTaskManageStore()
const defaultCheckedData = ref<any[]>()
const defaultCheckUserData = ref<any[]>()
const currentLoginUserInfo = computed(() =>
	JSON.parse(localStorage.getItem('currentUserInfo') as string)
)
const currentDepartmentInfo = computed(() =>
	JSON.parse(localStorage.getItem('currentDepartmentInfo') as string)
)
// const currentLoginPlatformUserInfo = computed(()=>JSON.parse)
const periodList = ref([
	{label: '一次性', value: periodEnum.once, dateRange: null},
	// {label: '每日一次', value: periodEnum.day, dateRange: null},
	// {label: '每周一次', value: periodEnum.week, dateRange: null},
	// {label: '每半月一次', value: periodEnum.halfMonth, dateRange: null},
	// {label: '每月一次', value: periodEnum.month, dateRange: dayjs().daysInMonth()},
	// {label: '每季度一次', value: periodEnum.quarter, dateRange: 90},
	// {label: '每半年一次', value: periodEnum.halfYear, dateRange: null},
	// {label: '每年一次', value: periodEnum.year, dateRange: 365},
])
const toDaysPlaceholder = ref<any>(1)
const typeList = ref([
	{label: '内部填报', value: 2},
	{label: '下发填报', value: 1},
])
const ruleFormRef = ref<FormInstance>()
const formLabelAlign = ref({
	taskName: '',
	fillingPeriodType: 1,
	fromDays: 1,
	toDays: 1,
	endDate: undefined,
	fromDate: null,
	treeSelect: null as any,
	fillingMode: 2,
	description: null,
	auditorId: null,
	dataLeaderId: null,
	tableList: [
		{
			modalValue: '',
			tableId: '',
			sheet: null,
			visible: false,
			isAlien: undefined as any,
		},
	],
	issuedAuditWorkflowSchemeCode: null, // 下发审核
	dataAuditWorkflowSchemeCode: null, // 数据审核
})
const rules = reactive<FormRules>({
	taskName: [{required: true, message: '请输入任务名称', trigger: 'blur'}],
	// fillingPeriodType: [{required: true, message: '请选择填报周期', trigger: 'change'}],
	// fromDays: [{required: true, message: '请输入开始日期', trigger: 'blur'}],
	// todays: [{required: true, message: '请输入结束日期', trigger: 'blur'}],
	fillingMode: [{required: true, message: '请选择填报方式', trigger: 'change'}],
	// treeSelect: [{required: true, message: '请选择', trigger: 'change'}],
	auditorId: [{required: true, message: '请选择审核领导', trigger: 'change'}],
	dataLeaderId: [{required: true, message: '请选择数据审核人', trigger: 'change'}],
	fromDate: [{required: true, message: '请选择开始日期', trigger: 'change'}],
	endDate: [{required: true, message: '请选择最终截止日期', trigger: 'change'}],
})
// 人员选择数据源
const checkStaffList = ref<any[]>()
const auditLeaderList = ref<any[]>()
const dataLeaderList = ref<any[]>()
const checkDepartmentList = ref()

const openSLModalVisible = ref(false)
// 图片预览相关变量
const showRegularPreview = ref(false)
const showIrregularPreview = ref(false)
const previewIndex = ref(0)

// 图片URL列表
const regularTableImage = ref(new URL('@/assets/image/demo2.png', import.meta.url).href)
const irregularTableImages = ref([
	new URL('@/assets/image/demo1.png', import.meta.url).href,
	new URL('@/assets/image/demo3.png', import.meta.url).href,
])

// 打开不规则表格预览
const openIrregularPreview = (index: number) => {
	previewIndex.value = index
	showIrregularPreview.value = true
}
const props = {
	label: 'label',
	value: 'value',
	isLeaf: 'isLeaf',
}
const openCzxz = ref(false)
const blockRef = ref<any>(null)
const viewFlowCode = ref('')
const showViewFlow = ref(false)

const addTable = () => {
	formLabelAlign.value.tableList.push({
		modalValue: '',
		tableId: '',
		sheet: null,
		visible: false,
		isAlien: formLabelAlign.value.tableList[0].isAlien,
	})
	// blockRef.value?.resize()
}
const removeTable = (index: number) => {
	formLabelAlign.value.tableList.splice(index, 1)
	// blockRef.value?.resize()
}

const datePickerChange = (e: any) => {
	if (formLabelAlign.value.fillingPeriodType === 1) {
		formLabelAlign.value.endDate = e
	}
}
const saveTaskVisible = ref(false)
const isAudit = ref(false)
function saveTask(type?: number) {
	console.log(formLabelAlign.value)

	if (!formLabelAlign.value.issuedAuditWorkflowSchemeCode) {
		ElMessage.warning('请选择下发审核流程')
		return
	}
	if (!formLabelAlign.value.dataAuditWorkflowSchemeCode) {
		ElMessage.warning('请选择数据审核流程')
		return
	}

	if (formLabelAlign.value.tableList.some((e: any) => e.tableId === null || e.tableId === '')) {
		saveTaskVisible.value = false
		ElMessage.warning('请完成表格配置')
		return
	}

	if (!formLabelAlign.value.treeSelect) {
		saveTaskVisible.value = false
		return ElMessage.warning('请选择')
	}

	saveTaskVisible.value = true
	ElMessageBox({
		title: '确认',
		message: type === 1 ? '是否完成报表创建工作？' : '是否完成报表创建并提交审核？',
		showCancelButton: true,
	})
		.then(() => {
			let params: any = {
				name: formLabelAlign.value.taskName,
				fillingPeriodType: formLabelAlign.value.fillingPeriodType,
				fillingMode: formLabelAlign.value.fillingMode,
				description: formLabelAlign.value.description,
				// endDate: dayjs(formLabelAlign.value.endDate).format('YYYY-MM-DD 23:59:59'),
				endDate: formLabelAlign.value.endDate,
				reportTemplateIds: formLabelAlign.value.tableList.map((v: any) => v.tableId),
				areaOrganizationUnitId: JSON.parse(
					localStorage.getItem('currentDepartmentInfo') as string
				).id,
				attachments: imgFile.value,
				issuedAuditWorkflowSchemeCode: formLabelAlign.value.issuedAuditWorkflowSchemeCode,
				dataAuditWorkflowSchemeCode: formLabelAlign.value.dataAuditWorkflowSchemeCode,
			}
			if (formLabelAlign.value.fillingMode === 2) {
				// 人员选择
				const col: any = {}
				if (formLabelAlign.value.treeSelect !== null) {
					formLabelAlign.value.treeSelect.forEach((v: any) => {
						if (typeof v === 'string') {
							col[v.split('/')[1]] = v.split('/')[0]
						} else {
							col[v.value.split('/')[1]] = v.value.split('/')[0]
						}
					})
				}
				params.staffs = formLabelAlign.value.treeSelect === null ? null : col
				params.ActualAreaOrganizationUnitId = currentCheckUsersDepartment.value
			} else {
				// 下发
				// params.areaOrganizationUnitIds = formLabelAlign.value.treeSelect
				// // 分管领导审核
				// if (formLabelAlign.value.auditorId !== null) {
				// 	params.auditorId = (formLabelAlign.value.auditorId as string).split('/')[1]
				// 	params.ActualInChargeLeaderAreaOrganizationUnitId =
				// 		selectAuditorDepartmentId.value
				// }
				// // 数据领导审核
				// if (
				// 	formLabelAlign.value.dataLeaderId !== null &&
				// 	formLabelAlign.value.dataLeaderId !== undefined
				// ) {
				// 	params.dataLeaderId = (formLabelAlign.value.dataLeaderId as string).split(
				// 		'/'
				// 	)[1]
				// 	params.ActualDataLeaderAreaOrganizationUnitId = (
				// 		formLabelAlign.value.dataLeaderId as string
				// 	).split('/')[0]
				// }
				if (formLabelAlign.value.treeSelect !== null) {
					const datas = formLabelAlign.value.treeSelect
					let ids: any = []
					if (datas.departmentList.length !== 0) {
						datas.departmentList
							.map((v: any) => v.id)
							.forEach((v: any) => {
								ids.push({areaOrganizationUnitId: v})
							})
					}
					if (datas.userList.length !== 0) {
						datas.userList
							.map((v: any) => v.value)
							.forEach((e: any) => {
								ids.push({
									staffId: e.split('/')[1],
									staffAreaOrganizationUnitId: e.split('/')[0],
								})
							})
					}
					params.planTaskStaffAreaOrganizationUnits = ids
				}
			}

			// 开始批量设置填报截止时间
			switch (formLabelAlign.value.fillingPeriodType) {
				case periodEnum.once:
					params.newToDays = []
					break
				case periodEnum.month:
					params.newToDays = [cycleDate.value.fullDate]
					break
				case periodEnum.quarter:
					params.newToDays = [
						cycleDate.value.quarter1Date,
						cycleDate.value.quarter2Date,
						cycleDate.value.quarter3Date,
						cycleDate.value.quarter4Date,
					]
					break
				case periodEnum.year:
					params.newToDays = [cycleDate.value.fullDate]
					break
				case periodEnum.day:
					params.newToDays = []
					break
				case periodEnum.week:
					params.newToDays = [cycleDate.value.weekDay]
					break
				case periodEnum.halfMonth:
					params.newToDays = [cycleDate.value.topHalfDate, cycleDate.value.bottomHalfDate]
					break
				case periodEnum.halfYear:
					params.newToDays = [cycleDate.value.topHalfDate, cycleDate.value.bottomHalfDate]
					break
			}

			if (ruleFormRef.value) {
				ruleFormRef.value
					.validate((valid, fields) => {
						console.log(44, valid, fields)

						if (valid) {
							if (route.query.id) {
								axios
									?.put(`/api/filling/plan-task/${route.query.id}`, params)
									.then((res: any) => {
										if (res) {
											if (type === 2) {
												ElMessageBox({
													title: '提交审核',
													message: () =>
														h(ElCheckbox, {
															modelValue: isAudit.value,
															'onUpdate:modelValue': (val: any) => {
																isAudit.value = val
															},
															label: '通知审核人！',
														}),
												})
													.then(() => {
														axios
															?.post(
																`/api/filling/plan-task/set-active?id=${res.data.id}&enabled=true&notice=${isAudit.value}`
															)
															.then((res) => {
																ElMessage.success('已提交领导审核')
															})
														saveTaskVisible.value = false
														getTaskList(10)
													})
													.catch((action) => {
														isAudit.value = false
														// console.log(action)
													})
											} else {
												ElMessage.success('保存成功')
												saveTaskVisible.value = false
												getTaskList(10)
											}
										}
									})
							} else {
								axios
									?.post('/api/filling/plan-task', params)
									.then((res: any) => {
										if (res) {
											if (type === 2) {
												ElMessageBox({
													title: '提交审核',
													message: () =>
														h(ElCheckbox, {
															modelValue: isAudit.value,
															'onUpdate:modelValue': (val: any) => {
																isAudit.value = val
															},
															label: '通知审核人！',
														}),
												})
													.then(() => {
														axios
															?.post(
																`/api/filling/plan-task/set-active?id=${res.data.id}&enabled=true&notice=${isAudit.value}`
															)
															.then((res) => {
																ElMessage.success('已提交领导审核')
															})
														saveTaskVisible.value = false
														getTaskList(10)
													})
													.catch((action) => {
														isAudit.value = false
														// console.log(action)
													})
											} else {
												ElMessage.success('创建成功')
												saveTaskVisible.value = false
												getTaskList(10)
											}
										}
									})
									.catch((e) => {
										saveTaskVisible.value = false
									})
							}
						} else {
							saveTaskVisible.value = false
						}
					})
					.catch((err) => {
						console.log(44, err)
						saveTaskVisible.value = false
					})
			}
		})
		.catch((action) => {
			// console.log(action)
			console.log(55, action)
			saveTaskVisible.value = false
		})
}

const getTaskInfo = () => {
	// /api/filling/plan-task/${route.query.id}
	axios?.get(`/api/filling/plan-task/edit-detail?id=${route.query.id}`).then((res: any) => {
		const data = res.data

		const {staffs, departments} = data

		const staffsList = checkStaffList.value?.filter((f: any) =>
			staffs.some((s: any) => f.value.includes(s.id))
		)
		console.log(889, staffsList, checkStaffList.value, staffs)

		formLabelAlign.value = {
			issuedAuditWorkflowSchemeCode: data.issuedAuditWorkflowSchemeCode,
			dataAuditWorkflowSchemeCode: data.dataAuditWorkflowSchemeCode,
			taskName: data.name,
			fillingPeriodType: data.fillingPeriodType,
			fromDays: data.fromDays,
			toDays: data.toDays,
			endDate: data.endDate,
			fromDate: data.fromDate,
			treeSelect: data.isMixed
				? {
						departmentList: data.planTaskStaffAreaOrganizationUnits
							.filter((v: any) => v.department !== null)
							.map((x: any) => ({...x.department})),
						userList: data.planTaskStaffAreaOrganizationUnits
							.filter((v: any) => v.staff !== null)
							.map((x: any) => ({
								departmentId: x.staff.department.id,
								disabled: false,
								isLeaf: true,
								label: x.staff.name,
								value: x.staff.department.id + '/' + x.staff.id,
							})),
				  }
				: data.fillingMode === 2
				? staffsList
				: departments.map((v: any) => v.id),
			fillingMode: data.fillingMode,
			description: data.description,
			auditorId: (data.inChargeLeader?.department?.id + '/' + data.inChargeLeader?.id) as any,
			dataLeaderId: data?.dataLeaderId,
			tableList:
				data.planTaskReportTableTemplates.length === 0
					? [
							{
								modalValue: '',
								tableId: '',
								sheet: null,
								visible: false,
								isAlien: undefined,
							},
					  ]
					: data.planTaskReportTableTemplates.map((v: any) => ({
							modalValue: v.reportTableTemplateName,
							tableId: v.reportTableTemplateId,
							sheet: {
								name: v.reportTableTemplateName,
								head: [],
								config: {},
							},
							visible: false,
							isAlien: data.reportTableTemplates.filter(
								(x: any) => x.id === v.reportTableTemplateId
							)[0].isAlien,
					  })),
		}

		imgFile.value = data.fileInfos.map((v) => ({
			// ...v,
			name: v.name,
			objectId: v.objectId,
			path: v.path,
			extension: v.extension,
			size: v.size,
		}))
		defaultCheckedData.value =
			data.departments.length !== 0
				? data.departments
				: data.planTaskStaffAreaOrganizationUnits
						.filter((v: any) => v.department !== null)
						.map((x: any) => ({...x.department}))

		defaultCheckUserData.value = data.planTaskStaffAreaOrganizationUnits
			.filter((v: any) => v.staff !== null)
			.map((x: any) => ({
				departmentId: x.staff.department.id,
				disabled: false,
				isLeaf: true,
				label: x.staff.name,
				value: x.staff.department.id + '/' + x.staff.id,
			}))
	})
}
const getTaskList = async (FilterMode: number) => {
	axios
		?.get(
			`/api/filling/plan-task?FilterMode=${FilterMode}&MaxResultCount=10&SkipCount=0&isSelf=true`
		)
		.then((res: any) => {
			taskManageStore.$patch({taskList: res.data.items})
			taskManageStore.$patch({totalCount: res.data.totalCount})
			// router.push('/statementTask')
			deleteLabel({path: router.currentRoute.value.fullPath})
		})
}
// 获取同一部门下的人员
const getSameOrganizationStaff = async () => {
	return new Promise((resolve) => {
		axios
			.request({
				method: 'get',
				url: `/api/platform/departmentInternal/department-extend-bind-users`,
				headers: {
					Urlkey: 'iframeCode',
				},
			})
			.then((users: any) => {
				// users.data.items = users.data.items.map(v=>({...v,staffRole:v.staffRole.map(x=>x)}))

				checkStaffList.value =
					users.data
						.filter(
							(user: any) =>
								(user.staffRole.includes(STAFFROLEARRAY[2]) ||
									user.staffRole.includes(STAFFROLEARRAY[0]) ||
									user.staffRole.includes(STAFFROLEARRAY[3])) &&
								user.id !==
									JSON.parse(localStorage.getItem('currentUserInfo') as string).id
						)
						.map((res: any) => ({
							label: res.name,
							departmentId: res?.department?.id,
							value: res?.department?.id + '/' + res.id,
						})) ?? []
				auditLeaderList.value =
					users.data
						.filter((user: any) => user.staffRole.includes(STAFFROLEARRAY[1]))
						.map((res: any) => ({
							label:
								res.department?.region?.name +
								'-' +
								res.department?.parentName +
								'-' +
								res.department?.name +
								'-' +
								res.name,
							departmentId: res?.department?.id,
							value: res?.department?.id + '/' + res.id,
						})) ?? []

				dataLeaderList.value =
					users.data
						.filter((user: any) => user.staffRole.includes(STAFFROLEARRAY[4]))
						.map((res: any) => ({
							label:
								res.department?.region?.name +
								'-' +
								res.department?.parentName +
								'-' +
								res.department?.name +
								'-' +
								res.name,
							departmentId: res?.department?.id,
							value: res?.department?.id + '/' + res.id,
						})) ?? []

				resolve('')
			})
			.catch((error: any) => {
				if (error.response?.status === 500) {
					ElNotification.error('当前进行报表创建使用量人员较多，请5分钟后再试')
				}
			})
	})
}

const selectAuditorDepartmentId = ref()
const handleSelectChange = (e: any) => {
	auditLeaderList.value?.forEach((item: any) => {
		if (item.value === e) {
			selectAuditorDepartmentId.value = item.departmentId
		}
	})

	console.log(1111, selectAuditorDepartmentId.value)
}
const selectDepartmentGroup = ref()
const loading = ref(false)
const filterMethod = (query: string) => {
	if (query) {
		loading.value = true
		axios
			.request({
				method: 'get',
				url: `/api/platform/department/department-bind-users?filter=${query}`,
				headers: {
					Urlkey: 'iframeCode',
				},
			})
			.then((res: any) => {
				loading.value = false
				const {data} = res

				const arr = data.items
					.map((v) => ({
						...v,
						label: v.name,
						name: v.department?.parent?.name + '-' + v.department?.name + '-' + v.name,
						value: v?.department?.id + '/' + v.id,
						departmentId: v?.department?.id,
						isLeaf: true,
						disabled: false,
					}))
					.filter(
						(user: any) =>
							(user.staffRole.includes(STAFFROLEARRAY[2]) ||
								user.staffRole.includes(STAFFROLEARRAY[0]) ||
								user.staffRole.includes(STAFFROLEARRAY[3])) &&
							user.id !==
								JSON.parse(localStorage.getItem('currentUserInfo') as string).id
					)
				selectDepartmentGroup.value = useArrayToTree(
					arr,
					'id',
					'parentId',
					'name',
					true,
					'children'
				)

				// selectDepartmentGroup.value = arr
				// console.log(111, res)
			})
	} else {
		getDepartmentChildren()
	}
}
const getDepartmentChildren = async () => {
	axios
		.request({
			method: 'get',
			url: '/api/platform/departmentInternal/get-department-children',
			headers: {
				Urlkey: 'iframeCode',
			},
		})
		.then((res) => {
			const {data} = res
			const arr = data.map((v: any) => ({
				...v,
				disabled: v?.department?.departmentId ? false : true,
				children: v.children === null ? [] : v.children,
			}))
			selectDepartmentGroup.value = useArrayToTree(
				arr,
				'id',
				'parentId',
				'name',
				true,
				'children'
			)
		})
		.catch((error: any) => {
			if (error.response?.status === 500) {
				ElNotification.error('当前进行报表创建使用量人员较多，请5分钟后再试')
			}
		})
}
const treeRef = ref()
const currentCheckUsersDepartment = ref()
const loadNode = (node: any, resolve: any) => {
	console.log(node.data)

	if (node.data.length === 0) return
	axios
		.request({
			method: 'get',
			url: `/api/platform/departmentInternal/${node.data.id}/bind-users`,
			headers: {
				Urlkey: 'iframeCode',
			},
		})
		.then((users: any) => {
			const userList =
				users.data.items
					.filter(
						(user: any) =>
							(user.staffRole.includes(STAFFROLEARRAY[2]) ||
								user.staffRole.includes(STAFFROLEARRAY[0]) ||
								user.staffRole.includes(STAFFROLEARRAY[3])) &&
							user.id !==
								JSON.parse(localStorage.getItem('currentUserInfo') as string).id
					)
					.map((res: any) => ({
						label: res.name,
						value: res?.department?.id + '/' + res.id,
						departmentId: res?.department?.id,
						isLeaf: true,
						disabled: res?.department?.id ? false : true,
					})) ?? []
			resolve(node.data.children.concat(userList))
		})
}
const handleCurrentChange = (node: any) => {
	console.log(node)
	currentCheckUsersDepartment.value = node.departmentId
}
// 获取当前登录人所在区域的下级区域部门
const getNextOrganizationTree = async () => {
	if (localStorage.getItem('nextChildren') !== null) {
		checkDepartmentList.value = JSON.parse(localStorage.getItem('nextChildren') as string)
	} else {
		await axios
			?.request({
				method: 'get',
				url: '/api/platform/region/all?grade=5',
				headers: {
					Urlkey: 'iframeCode',
				},
			})
			.then((res: any) => {
				const promise: any[] = []
				const departmentList: any = []
				const {city, district, community, street} = useUserStore().getCurrentDepartment
				const role = community || street || district || city
				const node = res.data.items.find((f: any) => f.name === role)
				const nodeChild = res.data.items
					.filter((f: any) => f.parentId === node.id || f.id === node.id)
					.map((e: any) => ({...e, disabled: true}))
				// allNodeChild = allNodeChild.concat(nodeChild)
				nodeChild.forEach(async (data: any) => {
					promise.push(
						new Promise(async (resolve, reject) => {
							const res = await axios.request({
								method: 'get',
								url: `/api/platform/departmentInternal?RegionId=${data.id}`,
								headers: {
									Urlkey: 'iframeCode',
								},
							})
							if (res) {
								res.data.items.forEach((v: any) => (v.parentId = data.id))
								resolve(res.data.items)
							} else {
								reject()
							}
						})
					)
				})
				Promise.all(promise).then((resList) => {
					resList.forEach((dd) => {
						if (dd.length !== 0) {
							dd.forEach((ee: any) => {
								departmentList.push(ee)
							})
						}
					})
					const newNodeChild = nodeChild.concat(departmentList)
					checkDepartmentList.value = util.arrayToTree(newNodeChild)
					window.localStorage.setItem(
						'nextChildren',
						JSON.stringify(checkDepartmentList.value)
					)
				})
			})
	}
}
async function getReportTemplate(id: string) {
	const res = await axios?.get(`/api/filling/report-table-template/${id}`)
	const {data} = res
	return {
		name: data.name,
		head: JSON.parse(data.header),
		config: JSON.parse(data.globalStyle),
		tableType: data.tableType,
		data: JSON.parse(data.statisticCells),
		alien: data.isAlien as boolean,
	}
}

function departmentListChange(departmentList: any, userList: any) {
	// formLabelAlign.value.treeSelect = departmentList.map((v: any) => v.id)
	formLabelAlign.value.treeSelect = {
		departmentList,
		userList,
	}
}
// Xlsx - Plus
const curTableList: any = ref(null)
const xlsxRef = ref()
const xlConfig = ref({
	visible: false,
	enableProtect: true,
	sheets: [],
}) as any
const openXlsxIndex = ref(0)
const openXlsxPlus = async (item: any | null, index: number) => {
	let currentSheet = null
	if (item.tableId === '') {
		currentSheet = toRaw(formLabelAlign.value.tableList[index].sheet)
		IsAlien.value = formLabelAlign.value.tableList[index].isAlien
	} else {
		const {name, head, config, tableType, data, alien} = await getReportTemplate(item.tableId)
		IsAlien.value = alien
		currentSheet = {
			name,
			head,
			data,
			config,
		}
		curReportType.value = tableType
	}
	if (currentSheet) {
		currentSheet.name = item.modalValue
	}
	xlConfig.value.sheets = currentSheet
		? [currentSheet]
		: [
				{
					name: item.modalValue,
					head: [],
					data: [],
				},
		  ]
	openXlsxIndex.value = index
	xlConfig.value.visible = true

	curTableList.value = toRaw(formLabelAlign.value.tableList[index])
	// dialogRef.value?.resize()
	// 操作须知
	const isLook = localStorage.getItem('CZXZ')
	if (!isLook) {
		openCzxz.value = true
	}
}
// 是否异型表标识
const IsAlien = ref(false)
// 保存模版
const onXlsxSave = (sheets: any) => {
	console.log('onAutoSave', sheets)

	if (!xlsxRef.value.$isLock()) {
		// ElNotification.warning(`保护已启用, 锁定保护${curReportType.value === 0 ? '区域' : '工作表'}!`)
		// cze新需求
		ElNotification.warning(`请指定表头!`)

		return
	}

	const sheet = sheets[0]
	let head: any = []
	let data: any = []
	// 根据模版类型处理不同数据结构

	if (curReportType.value === 0) {
		// 明细表
		head = sheet.head.filter((m: any) => m.v.v || m.v?.ct?.s?.length > 0)
		data = sheet.data.filter((m: any) => m.v.v || m.v?.ct?.s?.length > 0)
	} else {
		//统计表
		for (let i = 0; i < sheet.data.length; i++) {
			if (sheet.data[i].v?.bg) {
				data.push(sheet.data[i])
			} else {
				if (sheet.data[i].v.v.indexOf('&nbsp')) {
					sheet.data[i].v.v = sheet.data[i].v.v.replace('&nbsp', '')
				}
				head.push(sheet.data[i])
			}
		}
		console.log(data)
		console.log(head)
	}

	formLabelAlign.value.tableList[openXlsxIndex.value].sheet = sheet
	// 判断如果tableId为空 则新增表格模板 若不为空，则编辑该表格模板
	if (formLabelAlign.value.tableList[openXlsxIndex.value].tableId === '') {
		const ruleForm: any = allDataDistribution.value[openXlsxIndex.value]
		console.log(999, ruleForm)

		const createData = Object.assign({
			name: sheet.name,
			displayName: sheet.name,
			globalStyle: JSON.stringify(sheet.config),
			header: JSON.stringify(head),
			tableType: curReportType.value,
			statisticCells: JSON.stringify(data),
			theadBeginRowNum: sheet.headRange.r + 1,
			theadEndRowNum: sheet.headRange.rr + 1,
			tableTemplateColumn:
				curReportType.value === 0
					? head
							.filter((x: any) => {
								return x.v.v && x.v.v.length !== 1
							})
							.map((m: any) => ({
								name: m.v.v + '',
								displayName: m.v.v + '',
								rowIndex: m.r,
								columnIndex: m.c,
							}))
					: [],
			// 下发数据----示例数据
			tableTemplateDataRow: Object.values(
				data.reduce((acc: any, obj: any) => {
					const {r} = obj
					if (!acc[r]) {
						acc[r] = [obj]
					} else {
						acc[r].push(obj)
					}
					return acc
				}, {})
			).map((v: any) => ({
				rowNum: v[0].r + 1,
				rowData: JSON.stringify(v),
			})),
			IsAlien: IsAlien.value,
			tableTemplateDataRowFilterConfigs: !ruleForm
				? null
				: ruleForm.rules.map((item: any) => {
						const data: any = {
							areaOrganizationUnitId: item.rv,
							rowNums: [],
							colNum: null,
							type: ruleForm.form.distribution,
						}
						if (ruleForm.form.distribution === 1) {
							data.rowNums.push(item.lv + 1)
						} else {
							const sheet = xlsxRef.value.$getData()[0]
							const hl = sheet.head.length
							data.colNum = ruleForm.form.column
							item.lv.forEach((v: any) => data.rowNums.push(v + hl + 1))
						}
						return data
				  }),
		})

		axios?.post('/api/filling/report-table-template', createData).then((res: any) => {
			//TODO: 重复保存会保存多个相同的模版
			if (res.status === 200) {
				formLabelAlign.value.tableList[openXlsxIndex.value].tableId = res.data.id
				ElNotification.success('保存成功!')
				xlConfig.value.visible = false
				localStorage.removeItem
			}
		})
	} else {
		axios
			?.put(
				`/api/filling/report-table-template/${
					formLabelAlign.value.tableList[openXlsxIndex.value].tableId
				}`,
				Object.assign({
					name: sheet.name,
					displayName: sheet.name,
					globalStyle: JSON.stringify(sheet.config),
					header: JSON.stringify(sheet.head),
					tableType: curReportType.value,
					statisticCells: JSON.stringify(data),
					theadBeginRowNum: sheet.headRange.r + 1,
					theadEndRowNum: sheet.headRange.rr + 1,
					tableTemplateColumn: head
						.filter((x: any) => {
							return x.v.v && x.v.v.length !== 1
						})
						.map((m: any) => ({
							name: m.v.v + '',
							displayName: m.v.v + '',
							rowIndex: m.r, // 后端从1开始
							columnIndex: m.c,
						})),
					IsAlien: IsAlien.value,
					// 下发数据----示例数据
					tableTemplateDataRow: Object.values(
						data.reduce((acc: any, obj: any) => {
							const {r} = obj
							if (!acc[r]) {
								acc[r] = [obj]
							} else {
								acc[r].push(obj)
							}
							return acc
						}, {})
					).map((v: any) => ({
						rowNum: v[0].r + 1,
						rowData: JSON.stringify(v),
						tableTemplateDataRowFilterConfigs: null,
					})),
				})
			)
			.then((res: any) => {
				//TODO: 重复保存会保存多个相同的模版
				if (res.status === 200) {
					formLabelAlign.value.tableList[openXlsxIndex.value].tableId = res.data.id
					ElNotification.success('保存成功!')
					xlConfig.value.visible = false
				}
			})
	}
}
const onDrawerClose = () => {
	formLabelAlign.value.tableList[openXlsxIndex.value].sheet = xlsxRef.value.$getData()[0]
	xlConfig.value.visible = false

	const focusInput = document.getElementById('luckysheet-input-box')
	if (focusInput) {
		focusInput.style.display = 'none'
	}
}

// 禁止选择时间
const disabledDate = (time: Date) => {
	switch (formLabelAlign.value.fillingPeriodType) {
		case 2:
			return (
				dayjs(time).isBefore(dayjs().startOf('month').format('YYYY-MM-DD')) ||
				dayjs(time).isAfter(dayjs().endOf('month').format('YYYY-MM-DD'))
			)
		case 3:
			return
		case 4:
			return (
				dayjs().isBefore(dayjs(time).startOf('year').format('YYYY-MM-DD')) ||
				dayjs().isAfter(dayjs(time).endOf('year').format('YYYY-MM-DD'))
			)
		case 7:
			return (
				dayjs(time).isAfter(dayjs().format('YYYY-MM-15')) ||
				dayjs(time).isBefore(dayjs().startOf('month').format('YYYY-MM-DD'))
			)

		case 8:
			return (
				dayjs(time).isBefore(dayjs().startOf('year').format('YYYY-MM-DD')) ||
				dayjs(time).isAfter(dayjs().format('YYYY-06-30'))
			)
	}
	// return time.getTime() < Date.now() - 8.64e7
}
// 每半月和每半年一次时的下半月和下半年的日期禁用
const disabledDateHalfMonAndYear = (time: Date) => {
	switch (formLabelAlign.value.fillingPeriodType) {
		case 7:
			return (
				dayjs(time).isBefore(dayjs().format('YYYY-MM-15')) ||
				dayjs(time).isAfter(dayjs().endOf('month').format('YYYY-MM-DD'))
			)
		case 8:
			return (
				dayjs(time).isBefore(dayjs().format('YYYY-06-30')) ||
				dayjs(time).isAfter(dayjs().endOf('year').format('YYYY-MM-DD'))
			)
	}
}
// 第一季度截止时间的日期禁用
const firstQuarterDateDisable = (time: Date) => {
	return (
		dayjs(time).isBefore(dayjs().format('YYYY-01-01')) ||
		dayjs(time).isAfter(dayjs().format('YYYY-03-31'))
	)
}
// 第二季度截止时间的日期禁用
const secondQuarterDateDisable = (time: Date) => {
	return (
		dayjs(time).isBefore(dayjs().format('YYYY-04-01')) ||
		dayjs(time).isAfter(dayjs().format('YYYY-06-30'))
	)
}
// 第三季度截止时间的日期禁用
const thirdQuarterDateDisable = (time: Date) => {
	return (
		dayjs(time).isBefore(dayjs().format('YYYY-06-01')) ||
		dayjs(time).isAfter(dayjs().format('YYYY-09-30'))
	)
}
// 第四季度截止时间的日期禁用
const fourceQuarterDateDisable = (time: Date) => {
	return (
		dayjs(time).isBefore(dayjs().format('YYYY-10-01')) ||
		dayjs(time).isAfter(dayjs().format('YYYY-12-31'))
	)
}
const lastEndData = (time: Date) => {
	return time.getTime() < Date.now() - 8.64e7
}
// 设置默认填报截止时间
const fillingPeriodTypeChange = (e: number) => {
	switch (e) {
		case periodEnum.once:
			// 一次性填报 不设置
			break
		case periodEnum.month:
			cycleDate.value.fullDate = dayjs().endOf('month').format('YYYY-MM-DD')
			break
		case periodEnum.quarter:
			// 每季度填报，已设置默认固定时间
			break
		case periodEnum.year:
			cycleDate.value.fullDate = dayjs().endOf('year').format('YYYY-MM-DD')
			break
		case periodEnum.day:
			// 每日一次 不设置
			break
		case periodEnum.week:
			// 每周一次 初始化时已设置
			break
		case periodEnum.halfMonth:
			cycleDate.value.topHalfDate = dayjs().format('YYYY-MM-15')
			cycleDate.value.bottomHalfDate = dayjs().format('YYYY-MM-28')
			break
		case periodEnum.halfYear:
			cycleDate.value.topHalfDate = dayjs().format('YYYY-06-30')
			cycleDate.value.bottomHalfDate = dayjs().endOf('year').format('YYYY-MM-DD')
			break
	}
}
const tableTmpBlur = (item: any) => {
	if (item.tableId && route.query.id) {
		axios?.put(
			`/api/filling/plan-task-report-table-template/${route.query.id}/${item.tableId}/update-template-name`,
			{
				name: item.modalValue,
			}
		)
	}
}
const curReportType = ref(0)
const reportTypeOption = [
	{label: '明细表', value: 0},
	{label: '统计表', value: 1},
]

const cycleDate = ref({
	weekDay: dayjs().day(0).add(7, 'day').format('YYYY-MM-DD'),
	topHalfDate: '',
	bottomHalfDate: '',
	fullDate: '',
	quarter1Date: dayjs().format('YYYY-03-31'),
	quarter2Date: dayjs().format('YYYY-06-30'),
	quarter3Date: dayjs().format('YYYY-09-30'),
	quarter4Date: dayjs().format('YYYY-12-31'),
})
function displayLeadgerComp() {
	if (currentLoginUserInfo.value.staffRole.includes(STAFFROLEARRAY[4])) {
		return false
	}
	if (
		!currentLoginUserInfo.value.staffRole.includes(STAFFROLEARRAY[4]) &&
		currentLoginUserInfo.value.staffRole.includes(STAFFROLEARRAY[1])
	) {
		return true
	}
	if (
		!currentLoginUserInfo.value.staffRole.includes(STAFFROLEARRAY[4]) &&
		!currentLoginUserInfo.value.staffRole.includes(STAFFROLEARRAY[1]) &&
		currentLoginUserInfo.value.staffRole.includes(STAFFROLEARRAY[2])
	) {
		return false
	}
	return true
}

const onClickCzxzConfirm = () => {
	localStorage.setItem('CZXZ', 'true')
	openCzxz.value = false
}
const fillingModeChange = () => {
	formLabelAlign.value.treeSelect = null
	blockRef.value.resize()
}
// 相关附件

const imgFile = ref<any[]>([])
const upload = ref()
const beforeUpload = (file: any) => {
	console.log(222, '上传之前')
	const limitSize = 20971520
	const arr = JSON.parse(JSON.stringify(imgFile.value))
	console.log(arr)
	console.log(file)

	const alSize =
		arr.length === 0
			? file.size
			: arr.length === 1
			? arr[0].size + file.size
			: arr.map((v: any) => v.size).reduce((a: any, b: any) => a + b) + file.size

	console.log(alSize)
	if (alSize > limitSize) {
		ElNotification.warning('上传文件大小不能超过20M')
		upload.value?.handleRemove(file)
		return false
	}
}
const customUpload = async (options: any) => {
	console.log(111, '开始上传')

	const res = await uploadFile(options.file, 'planTask')
	if (res) {
		imgFile.value.push({
			objectId: options.file.uid.toString(),
			name: options.file.name,
			path: `/api/files/public/p${res}`,
			extension: options.file.type,
			size: options.file.size,
		})
	}
}
const onRemoveFile = (file: any) => {
	// 处理编辑模式和新增模式的附件删除
	let findIndex = -1
	if (file.raw) {
		// 新上传的附件有 uid 属性
		findIndex = imgFile.value.findIndex((item: any) => item.objectId === file.uid.toString())
	} else if (file.objectId) {
		// 编辑模式下的已有附件
		findIndex = imgFile.value.findIndex((item: any) => item.objectId === file.objectId)
	} else {
		// 尝试通过路径匹配
		findIndex = imgFile.value.findIndex((item: any) => item.path === file.path)
	}

	if (findIndex !== -1) {
		imgFile.value.splice(findIndex, 1)
		console.log('删除附件成功', imgFile.value)
	}
}
const tableTypeChange = async (val: any) => {
	formLabelAlign.value.tableList.forEach((x) => {
		x.isAlien = val
	})
	console.log(formLabelAlign.value.tableList)
	const promiseList: any[] = []
	formLabelAlign.value.tableList
		.filter((x) => x.tableId !== null && x.tableId !== '')
		.forEach((v) => {
			promiseList.push(
				axios.put(
					`/api/filling/report-table-template/change-type?id=${v.tableId}&isAlien=${v.isAlien}`
				)
			)
		})

	const res = await Promise.all(promiseList)
}

const onViewFlow = (code: string | null) => {
	if (!code) {
		ElMessage.warning('请选择一个流程')
		return
	}
	showViewFlow.value = true
	viewFlowCode.value = code
}
const onJumpCreateFlow = () => {
	router.push({
		path: '/flow/step',
		query: {
			category: '临时报表流程',
		},
	})
}

/// end
onMounted(async () => {})
watch(
	() => formLabelAlign.value.fillingMode,
	(v) => {
		console.log(222, v)
		selectDepartmentGroup.value = []
		getDepartmentChildren()
	}
)
const pageHeight = ref(0)
const dialogRef = ref<any>(null)

onActivated(async () => {
	await getSameOrganizationStaff()
	await getDepartmentChildren()
	// await getNextOrganizationTree()

	pageHeight.value = document.documentElement.clientHeight - 130
	formLabelAlign.value = {
		taskName: '',
		fillingPeriodType: 1,
		fromDays: 1,
		toDays: 1,
		endDate: undefined,
		fromDate: null,
		treeSelect: null as any,
		fillingMode: 1,
		description: null,
		auditorId: null,
		dataLeaderId: null,
		tableList: [
			{
				modalValue: '',
				tableId: '',
				sheet: null,
				visible: false,
				isAlien: undefined,
			},
		],
		issuedAuditWorkflowSchemeCode: null,
		dataAuditWorkflowSchemeCode: null,
	}
	imgFile.value = []

	if (route.query.id) getTaskInfo()
	saveTaskVisible.value = false
})
const defaultAttachmentCheck = ref(true)
const showDataDistribution = ref(false)
const columnData = ref([])
const allDataDistribution = ref([])

const onChangeColumn = (val: any, item: any) => {
	const sheet = xlsxRef.value.$getData()[0]
	if (sheet.head.length === 0) {
		columnData.value.length = 0
		ElMessage.warning('请先指定表头再设置分发数据列')
		return
	}
	columnData.value = sheet.data
		.filter((item: any) => item.c === val)
		.map((item: any) => ({
			label: item.v.v,
			value: item.v.v,
			_raw: JSON.parse(JSON.stringify(item)),
		}))
}

const onDataDistributionConfirm = (val: any) => {
	allDataDistribution.value = val
	console.log(allDataDistribution.value)
	showDataDistribution.value = false
}
</script>
<template>
	<div class="report-create-task">
		<Block
			:title="route.query.id ? '任务编辑' : '任务创建'"
			ref="blockRef"
			style="overflow: auto"
			:enable-expand-content="false"
			:enableExpand="false"
			:enable-fixed-height="true"
		>
			<template #topRight>
				<el-button
					type="primary"
					size="small"
					@click="saveTask(1)"
					:disabled="saveTaskVisible"
					class="mg-left-10"
				>
					<!-- <el-icon mr-5px><CircleCheckFilled /></el-icon> -->
					创建完成
				</el-button>
				<!-- <el-button
					v-if="
						formLabelAlign.fillingMode === 1 &&
						currentLoginUserInfo?.staffRole.includes(STAFFROLEARRAY[2]) &&
						!currentLoginUserInfo?.staffRole.includes(STAFFROLEARRAY[1]) &&
						!currentLoginUserInfo?.staffRole.includes(STAFFROLEARRAY[4])
					"
					type="primary"
					size="small"
					ml-10px
					@click="saveTask(2)"
					:disabled="saveTaskVisible"
				>
					<el-icon mr-5px><CircleCheckFilled /></el-icon>
					创建完成并提交
				</el-button> -->
			</template>
			<el-form
				ref="ruleFormRef"
				label-position="top"
				label-width="120px"
				:rules="rules"
				:model="formLabelAlign"
				status-icon
				class="create-form"
			>
				<div class="step">
					<div class="tip">
						<span>1</span>
						<span>任务信息</span>
					</div>
					<div class="left mg-right-40">
						<el-form-item prop="taskName">
							<template #label>
								<strong>任务名称:</strong>
							</template>
							<el-input
								v-model="formLabelAlign.taskName"
								placeholder="请输入任务名称"
							/>
						</el-form-item>
						<!-- <el-form-item prop="fillingPeriodType">
							<template #label>
								<strong>填报周期:</strong>
							</template>
							<el-select
								w-full
								v-model="formLabelAlign.fillingPeriodType"
								@change="fillingPeriodTypeChange"
							>
								<el-option v-for="item in periodList" :label="item.label" :value="item.value"
									>{{ item.label }}
								</el-option>
							</el-select>
						</el-form-item>
						<el-form-item
							v-if="
								formLabelAlign.fillingPeriodType !== periodEnum.once &&
								formLabelAlign.fillingPeriodType !== periodEnum.day
							"
						>
							<template #label>
								<strong>每周期填报截止日期(截止日期为选择当日的23:59:59):</strong>
							</template>

							<el-select
								class="w-full"
								v-if="formLabelAlign.fillingPeriodType === 6"
								v-model="cycleDate.weekDay"
							>
								<el-option v-for="item in week" :label="item.name" :value="item.value"
									>{{ item.name }}
								</el-option>
							</el-select>

							<el-date-picker
								v-model="cycleDate.fullDate"
								v-if="
									formLabelAlign.fillingPeriodType === periodEnum.month ||
									formLabelAlign.fillingPeriodType === periodEnum.year ||
									true
								"
								:disabled-date="disabledDate"
								type="date"
								value-format="YYYY-MM-DD"
								placeholder="周期截止日期"
								style="width: 100%"
							/>

							<el-form-item
								w-full
								v-if="
									formLabelAlign.fillingPeriodType === periodEnum.halfMonth ||
									formLabelAlign.fillingPeriodType === periodEnum.halfYear
								"
							>
								<el-col :span="11">
									<el-form-item w-full>
										<template #label>
											<span v-if="formLabelAlign.fillingPeriodType === periodEnum.halfMonth"
												>上半月:
											</span>
											<span v-if="formLabelAlign.fillingPeriodType === periodEnum.halfYear"
												>上半年:
											</span>
										</template>

										<el-date-picker
											v-model="cycleDate.topHalfDate"
											:disabled-date="disabledDate"
											type="date"
											value-format="YYYY-MM-DD"
											placeholder="截止日期"
											style="width: 100%"
										/>
									</el-form-item>
								</el-col>
								<el-col :span="2"> </el-col>
								<el-col :span="11">
									<el-form-item>
										<template #label>
											<span v-if="formLabelAlign.fillingPeriodType === periodEnum.halfMonth"
												>下半月:
											</span>
											<span v-if="formLabelAlign.fillingPeriodType === periodEnum.halfYear"
												>下半年:
											</span>
										</template>
										<el-date-picker
											v-model="cycleDate.bottomHalfDate"
											:disabled-date="disabledDateHalfMonAndYear"
											type="date"
											value-format="YYYY-MM-DD"
											placeholder="截止日期"
											style="width: 100%"
										/>
									</el-form-item>
								</el-col>
							</el-form-item>

							<el-form-item
								class="w-full"
								v-if="formLabelAlign.fillingPeriodType === periodEnum.quarter"
							>
								<el-col :span="11">
									<el-form-item class="w-full">
										<template #label>第一季度</template>
										<el-date-picker
											v-model="cycleDate.quarter1Date"
											:disabled-date="firstQuarterDateDisable"
											type="date"
											value-format="YYYY-MM-DD"
											placeholder="第一季度"
											style="width: 100%"
										/>
									</el-form-item>
								</el-col>
								<el-col :span="2"> </el-col>
								<el-col :span="11">
									<el-form-item>
										<template #label>第二季度</template>
										<el-date-picker
											v-model="cycleDate.quarter2Date"
											:disabled-date="secondQuarterDateDisable"
											type="date"
											value-format="YYYY-MM-DD"
											placeholder="第二季度"
											style="width: 100%"
										/>
									</el-form-item>
								</el-col>
							</el-form-item>

							<el-form-item
								class="w-full mg-top-10"
								v-if="formLabelAlign.fillingPeriodType === periodEnum.quarter"
							>
								<el-col :span="11">
									<el-form-item class="w-full">
										<template #label>第三季度</template>
										<el-date-picker
											v-model="cycleDate.quarter3Date"
											:disabled-date="thirdQuarterDateDisable"
											type="date"
											value-format="YYYY-MM-DD"
											placeholder="第三季度"
											style="width: 100%"
										/>
									</el-form-item>
								</el-col>
								<el-col :span="2"> </el-col>
								<el-col :span="11">
									<el-form-item>
										<template #label>第四季度</template>
										<el-date-picker
											v-model="cycleDate.quarter4Date"
											:disabled-date="fourceQuarterDateDisable"
											type="date"
											value-format="YYYY-MM-DD"
											placeholder="第四季度"
											style="width: 100%"
										/>
									</el-form-item>
								</el-col>
							</el-form-item>
						</el-form-item> -->

						<el-form-item prop="endDate">
							<template #label>
								<strong>最终截止时间:</strong>
							</template>
							<el-date-picker
								style="width: 100%"
								type="datetime"
								value-format="YYYY-MM-DD HH:mm:ss"
								placeholder="请选择"
								:disabled-date="lastEndData"
								v-model="formLabelAlign.endDate"
							/>
						</el-form-item>

						<!-- <el-form-item prop="fillingMode">
							<template #label>
								<strong>填报选择:</strong>
							</template>
							<el-select
								w-full
								v-model="formLabelAlign.fillingMode"
								@change="fillingModeChange"
							>
								<el-option
									v-for="item in typeList"
									:label="item.label"
									:value="item.value"
									>{{ item.label }}</el-option
								>
							</el-select>
						</el-form-item> -->
						<el-form-item prop="treeSelect">
							<template #label>
								<strong
									><span style="color: red"> *</span>
									<!-- {{
										formLabelAlign.fillingMode === 2 ? '填报人员:' : '填报部门:'
									}} -->
									填报范围
								</strong>
							</template>
							<!-- <el-tree-select
								ref="treeRef"
								w-full
								class="no-style"
								v-model="formLabelAlign.treeSelect"
								:data="selectDepartmentGroup"
								:load="loadNode"
								:props="props"
								:remote-method="filterMethod"
								:loading="loading"
								filterable
								multiple
								remote
								@current-change="handleCurrentChange"
								lazy
								v-if="formLabelAlign.fillingMode === 2"
							>
						
							</el-tree-select> -->
							<!-- v-if="formLabelAlign.fillingMode === 1" -->
							<departmentFavoriteComp
								placeholder="请选择"
								:data="checkDepartmentList"
								:type="'modal'"
								:defaultCheckedData="defaultCheckedData"
								:defaultCheckUserData="defaultCheckUserData"
								@change="departmentListChange"
							></departmentFavoriteComp>
						</el-form-item>
						<!-- <el-form-item
								prop="auditorId"
								v-if="
									formLabelAlign.fillingMode === 1 &&
									!currentLoginUserInfo?.staffRole.includes(STAFFROLEARRAY[4]) &&
									!currentLoginUserInfo?.staffRole.includes(STAFFROLEARRAY[1])
								"
							>
								<template #label>
									<strong>审核领导:</strong>
								</template>
								<el-select w-full v-model="formLabelAlign.auditorId" @change="handleSelectChange">
									<el-option v-for="item in auditLeaderList" :label="item.label" :value="item.value"
										>{{ item.label }}
									</el-option>
								</el-select>
							</el-form-item> -->

						<!-- <el-form-item
								prop="dataLeaderId"
								v-if="formLabelAlign.fillingMode === 1 && displayLeadgerComp()"
							>
								<template #label>

									<strong>数据审核人:</strong>
								</template>
								<el-select w-full v-model="formLabelAlign.dataLeaderId">
									<el-option v-for="item in dataLeaderList" :label="item.label" :value="item.value"
										>{{ item.label }}
									</el-option>
								</el-select>
							</el-form-item> -->

						<el-form-item>
							<template #label>
								<strong>填报说明:</strong>
							</template>
							<el-input
								type="textarea"
								v-model="formLabelAlign.description"
								placeholder="请输入填报说明"
							/>
						</el-form-item>
					</div>

					<div class="right">
						<el-form-item>
							<template #label>
								<strong>表格名称:</strong>
							</template>
							<div
								class="mg-bottom-10"
								v-for="(item, index) in formLabelAlign.tableList"
								style="width: 100%; display: flex"
							>
								<el-input
									v-model="item.modalValue"
									placeholder="请输入表格名称"
									@change="tableTmpBlur(item)"
								/>
								<el-select
									v-model="item.isAlien"
									:disabled="index !== 0"
									placeholder="请选择类型"
									style="width: 220px"
									@change="tableTypeChange"
									class="mg-left-10"
								>
									<el-option label="规则表格" :value="false" />
									<el-option label="不规则表格" :value="true" />
								</el-select>
								<el-button
									type="danger"
									plain
									v-if="index !== 0 || formLabelAlign.tableList.length > 1"
									@click="removeTable(index)"
									class="mg-left-10"
									>删除</el-button
								>
								<el-button
									type="primary"
									v-if="item.modalValue !== '' && item.isAlien !== undefined"
									@click="openXlsxPlus(item, index)"
									class="mg-left-10"
									>表格配置</el-button
								>
							</div>
							<button type="button" @click="addTable" class="create-table">
								+ &nbsp; <span text="14px">添加表格</span>
							</button>

							<div class="mg-top-10 df aic" style="width: 100%">
								<el-icon class="mg-top-10"><InfoFilled /></el-icon>
								<div class="mg-top-10 fs-12 df aic">
									请确认当前表格是否为不规则表格。不规则表格指行列都可能为表头，这类表格暂时不支持汇总数据，你可单独创建任务收集数据。<el-link
										type="primary"
										:underline="false"
										@click="openSLModalVisible = true"
										><span
											class="fs-12"
											style="display: inline-block; width: 65px"
											>查看示例</span
										></el-link
									>
								</div>
							</div>
						</el-form-item>

						<el-form-item style="width: 80%">
							<template #label>
								<strong>相关附件:</strong>
							</template>
							<el-upload
								action=""
								multiple
								ref="upload"
								:http-request="customUpload"
								accept=".jpg,.jpeg,.png,.gif,.rar,.zip,.doc,.docx,.pdf,.xlsx,.xls"
								:limit="5"
								:on-remove="onRemoveFile"
								:before-upload="beforeUpload"
								:file-list="imgFile"
							>
								<el-button
									type="primary"
									size="small"
									:disabled="imgFile.length >= 5"
								>
									<el-icon class="fs-14 mg-right-5"><upload-filled /></el-icon
									>点击上传
								</el-button>
								<!-- <el-icon class="el-icon--upload"><upload-filled /></el-icon>
							<div class="el-upload__text">点击或将文件拖动到这里进行上传</div> -->
								<template #tip>
									<div class="el-upload__tip">
										支持格式:.png.jpg.gif.rar.zip.doc.docx.pdf,
										最多支持上传5个附件。
									</div>
								</template>
							</el-upload>
						</el-form-item>
					</div>
				</div>
				<div class="step mg-right-40">
					<div class="tip">
						<span>2</span>
						<span>任务下发前配置</span>
					</div>

					<FormItem
						v-model="formLabelAlign.issuedAuditWorkflowSchemeCode"
						:items="[
							{
								prop: 'issuedAuditWorkflowSchemeCode',
								label: '下发审核流程',
								type: 'selectRemote',
								useBaseUrl: true,
								remoteUrl: '/api/workflow/workflowSchemeInfo',
								remoteParams: {enabledMark: true, category: FlowType.Fill},
								remoteFilterKey: 'keyword',
								remoteInit: true,
								remoteValue: 'code',
								formItemProps: {
									rules: [{required: true}],
								},
							},
						]"
					>
						<template #form-issuedAuditWorkflowSchemeCode-right>
							<el-button
								type="primary"
								class="mg-left-10"
								@click="onViewFlow(formLabelAlign.issuedAuditWorkflowSchemeCode)"
								>查看流程</el-button
							>
							<!-- <el-button type="primary" @click="onJumpCreateFlow">新建流程</el-button> -->
						</template>
					</FormItem>
				</div>
				<div class="step">
					<div class="tip">
						<span>3</span>
						<span>任务收集后配置</span>
					</div>
					<FormItem
						v-model="formLabelAlign.dataAuditWorkflowSchemeCode"
						:items="[
							{
								prop: 'dataAuditWorkflowSchemeCode',
								label: '数据审核流程',
								type: 'selectRemote',
								remoteUrl: '/api/workflow/workflowSchemeInfo',
								remoteParams: {enabledMark: true, category: FlowType.Fill},
								remoteFilterKey: 'keyword',
								remoteInit: true,
								remoteValue: 'code',
								formItemProps: {
									rules: [{required: true}],
								},
							},
						]"
					>
						<template #form-dataAuditWorkflowSchemeCode-right>
							<el-button
								type="primary"
								@click="onViewFlow(formLabelAlign.dataAuditWorkflowSchemeCode)"
								class="mg-left-10"
								>查看流程</el-button
							>
							<!-- <el-button type="primary" @click="onJumpCreateFlow">新建流程</el-button> -->
						</template>
					</FormItem>
				</div>
			</el-form>

			<ViewFlow v-model="showViewFlow" :code="viewFlowCode"></ViewFlow>
		</Block>

		<DrawerPlusComp :visible="xlConfig.visible" @onClose="onDrawerClose" title="创建模版">
			<template #header>
				<el-tooltip
					class="box-item"
					effect="dark"
					content="<p>1、设置表头时请勿在单元格中换行。</p>
					<p>2、设置完成后，选中所有表头，点击【指定表头】完成锁定。对于不规则的表格支持选取不连续的单元格【指定表头】。</p>
					<p>
						3、如还需编辑表头，请先点击【重置】按钮。
					</p>"
					placement="bottom"
					raw-content
				>
					<div
						style="
							display: flex;
							align-items: center;
							margin-left: 30px;
							width: 80px;
							cursor: pointer;
						"
					>
						<el-icon class="mg-right-5"><WarningFilled /></el-icon>
						<strong>操作须知</strong>
					</div>
				</el-tooltip>
			</template>
			<template #content>
				<XlsxPlusComp
					ref="xlsxRef"
					:sheets="xlConfig.sheets"
					:enableProtect="xlConfig.enableProtect"
					:enableAutoSave="false"
					:isAlien="IsAlien"
					:enableImport="false"
					:enableReload="true"
					:enableLockAll="curReportType === 1"
					@onSave="onXlsxSave"
					:enableMergeCell="true"
				>
					<template #headerButtonRight>
						<!-- <div ml-20px>
							<el-checkbox v-model="IsAlien" label="异形表" size="small"></el-checkbox>
						</div> -->
						<!-- <el-button
							v-if="!route.query.id"
							type="primary"
							size="small"
							@click="showDataDistribution = true"
							>数据分发</el-button
						> -->
					</template>
				</XlsxPlusComp>
			</template>
		</DrawerPlusComp>

		<Dialog
			title="操作须知"
			ref="dialogRef"
			v-model="openCzxz"
			:enableClose="false"
			@clickConfirm="onClickCzxzConfirm"
		>
			<!-- <template #body> -->
			<div class="tips">
				<p>1、设置表头时请勿在单元格中换行。</p>
				<p>
					2、设置完成后，选中所有表头，点击【指定表头】完成锁定。对于不规则的表格支持选取不连续的单元格【指定表头】。
				</p>
				<p>3、如还需编辑表头，请先点击【重置】按钮。</p>
			</div>
			<!-- </template> -->
		</Dialog>

		<Dialog
			title="表格示例"
			ref="slDialogRef"
			v-model="openSLModalVisible"
			:enableButton="false"
			:full-screen="true"
			@clickConfirm="openSLModalVisible = false"
		>
			<div class="table-example-container">
				<div class="table-example-left">
					<div class="table-example-title">规则表格</div>
					<div class="table-example-image">
						图例1:
						<img
							src="@/assets/image/demo2.png"
							alt="规则表格示例"
							class="clickable-image responsive-image"
							@click="showRegularPreview = true"
						/>
					</div>
				</div>
				<div class="table-example-right">
					<div class="table-example-title">不规则表格</div>
					<div class="table-example-image">
						图例1:
						<img
							src="@/assets/image/demo1.png"
							alt="不规则表格示例1"
							class="clickable-image responsive-image"
							@click="openIrregularPreview(0)"
						/>
						图例2:
						<img
							src="@/assets/image/demo3.png"
							alt="不规则表格示例2"
							class="clickable-image responsive-image"
							@click="openIrregularPreview(1)"
						/>
					</div>
				</div>
			</div>

			<!-- 规则表格图片预览 -->
			<el-image-viewer
				v-if="showRegularPreview"
				:url-list="[regularTableImage]"
				:initial-index="0"
				@close="showRegularPreview = false"
			/>

			<!-- 不规则表格图片预览 -->
			<el-image-viewer
				v-if="showIrregularPreview"
				:url-list="irregularTableImages"
				:initial-index="previewIndex"
				@close="showIrregularPreview = false"
			/>
		</Dialog>

		<DataDistribution
			title="数据分发规则"
			v-model="showDataDistribution"
			:column-data="columnData"
			:department-data="checkDepartmentList"
			:xlsx-index="openXlsxIndex"
			@change-column="onChangeColumn"
			@confirm="onDataDistributionConfirm"
		></DataDistribution>
	</div>
</template>
<style lang="scss" scoped>
.report-create-task {
	height: 100%;
	width: 100%;
}

.table-example-container {
	display: flex;
	justify-content: space-between;
	gap: 20px;
	width: 100%;
	overflow-x: hidden;

	@media (max-width: 768px) {
		flex-direction: column;
	}
}

.table-example-left,
.table-example-right {
	flex: 1;
	padding: 15px;
	border: 1px solid #ebeef5;
	border-radius: 4px;
	overflow: hidden;
	min-width: 0; /* 防止flex子项溢出 */
}

.table-example-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 15px;
	padding-bottom: 10px;
	border-bottom: 1px solid #ebeef5;
}

.table-example-image {
	display: flex;
	flex-direction: column;
	gap: 15px;
	overflow: hidden;
}

.clickable-image {
	cursor: pointer;
	transition: all 0.3s;

	&:hover {
		transform: scale(1.02);
		box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	}
}

.responsive-image {
	max-width: 100%;
	height: auto;
	object-fit: contain;
	display: block;
}

:deep(.el-form .el-form-item .el-form-item__label) {
	height: unset;
}

.tips {
	padding: 10px 20px;
	p {
		line-height: 2;
	}
}

.el-select-dropdown__item.is-disabled {
	color: #000 !important;
	text-decoration: none !important;
	cursor: pointer !important;
}

.toggle-report-type {
	align-items: center;
	display: flex;
	position: relative;
}

.create-form {
	display: flex;
	flex-wrap: wrap;
	> :first-child {
		display: flex;
		flex: auto !important;
		flex-wrap: wrap;
		width: 100%;
		> div {
			flex: 1;
		}
	}

	.step {
		flex: 1;
	}

	.tip {
		align-items: center;
		border-radius: 3px;
		background: linear-gradient(to right, var(--z-bg-secondary), transparent);
		display: flex;
		flex: auto !important;
		margin-bottom: 20px;
		padding: 10px;
		width: 100%;

		span:nth-child(1) {
			border-radius: 25px;
			background-color: var(--z-main);
			color: var(--z-nav-font-color);
			height: 25px;
			line-height: 25px;
			text-align: center;
			width: 25px;
		}

		span:nth-child(2) {
			font-size: 16px;
			font-weight: bold;
			margin-left: 10px;
		}
	}
}

.create-table {
	width: 100%;
	height: 35px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #fff;
	border: 1px dashed #409eff;
	color: #409eff;
	font-size: 16px;
	&:hover {
		color: #fff;
		background-color: #79bbff;
	}
}
</style>
