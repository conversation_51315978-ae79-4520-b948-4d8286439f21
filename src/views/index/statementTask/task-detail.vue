<script lang="ts" setup name="taskdetail">
import {ref, onMounted} from 'vue'
import {useRoute} from 'vue-router'
import {GetReportInfo} from '@/api/ReportApi'

const route = useRoute()
const infoFormProps = [
	{prop: 'taskName', label: '任务名称'},
	{prop: 'startTime', label: '填报开始时间'},
	{prop: 'endTime', label: '填报截止时间'},
	{prop: 'status', label: '任务状态'},
	{prop: 'reporting', label: '填报情况'},
	{prop: 'reportNams', label: '报表名称'},
	{prop: 'departments', label: '填报部门'},
	{prop: 'dataReviewProcess', label: '数据审核流程'},
	{prop: 'mark', label: '填报说明'},
]

const reportTaskId = ref('')
const GetReportInfoData = () => {
	GetReportInfo(reportTaskId.value).then((res) => {})
}

onMounted(() => {
	reportTaskId.value = route.query.reportTaskId
	GetReportInfoData()
})
</script>
<template>
	<div class="report-info">
		<Block title="任务信息" :enableExpandContent="false">
			<Form :props="infoFormProps" :grid="true" :columnCount="3"></Form>
		</Block>
	</div>
</template>
<style scoped lang="scss"></style>
