<script setup lang="ts" name="reporttaskdetail">
import {computed, h, inject, onActivated, onMounted, onUpdated, ref, watch} from 'vue'
import {Request} from '#/interface'
import {useRoute, useRouter} from 'vue-router'
import {
	ElCheckbox,
	ElMessage,
	ElMessageBox,
	ElNotification,
	FormInstance,
	TabsPaneContext,
	dayjs,
} from 'element-plus'
import {useUserStore} from '@/stores/useUserStore'
import {GetBatch} from '@/define/statement.define'
import {useViewStore} from '@/stores/useViewStore'
import util from '@/plugin/util'
import {getLedgerTableListByLedgerConfig} from '@/api/LedgerApi'
import {APIConfig} from '@/api/config'
import {PushReportAudit} from '@/api/ReportApi'
import {GetWorkflowProcess} from '@/api/workflowTaskApi'
import {useFlowRecord} from '@/hooks/useFlowRecord'
import {GetReportLogList} from '@/api/WorkflowApi'
import {GetPlanTaskProcessDetail, GetTurnTaskProcess, urging} from '@/api/ReportApi'
import {updateLabelTitle} from '@/hooks/useLabels'
import {id} from 'element-plus/es/locale'

const router = useRouter()
const axios = inject('#axios') as Request
const route = useRoute()
const userStore = useUserStore()
const viewStore = useViewStore()
const sheetRef = ref()
const sheetRefView = ref()
const showFlow = ref(false)
const flowCode = ref(null)

// url 参数
const __reportId = route.query.reportTaskId
const __areaOrganizationUnitId = route.query.areaOrganizationUnitId
const __id = route.query.id as string
const _excel_canview = computed(() =>
	_detailInfo.value ? _detailInfo.value.status !== 4 && _detailInfo.value.status !== 7 : false
)
// 组件参数
const __excel_data = ref({})
const __excel_useConfig = ref('')
const __excel_sheetId = ref('')
// 详情组件title参数 可变化
const titleList = ref([
	{
		width: '25%',
		title: '任务名称',
		key: 'taskName',
	},
	{
		width: '25%',
		title: '创建部门',
		key: 'creationOrganizationUnitName',
	},
	{
		width: '25%',
		title: '创建人',
		key: 'creatorName',
	},
	{
		width: '25%',
		title: '填报截止时间',
		key: 'endDate',
	},
	{
		width: '100%',
		title: '填报说明',
		key: 'description',
	},
	// {
	// 	width: '50%',
	// 	title: '所属批次',
	// 	key: 'batch',
	// },
	// {
	// 	width: '25%',
	// 	title: '数据审核流程',
	// 	key: '_dataflow',
	// },
])

// 详情信息
const _detailInfo = ref()
/**
 * 获取报表模版集合
 */
const __currentHead = ref('')
const __reportHeadList = ref<any[]>([])
const sheets = ref([])
const xlsxPlusRef = ref()
const __getReportList = async () => {
	let sheet
	if (__reportHeadList.value.length !== 0) {
		sheet = __reportHeadList.value.find((item: any) => item.id === __currentHead.value)
		// console.log(sheet)
		await __getReportListById(__currentHead.value)
	} else {
		const res = await axios.get(`/api/filling/report-task-ou/${__id}/report-tables`)
		const {data} = res
		// 选项卡
		sheet = data[0] // 默认显示第一个
		__reportHeadList.value = data
		__currentHead.value = sheet.id
		// 获取第一个报表的列表数据
		await __getReportListById(sheet.id)
	}
	const celldata = []
	for (let i = 0; i < reportList.length; i++) {
		const item = reportList[i]
		const cells = JSON.parse(item.rawData)
		// 根据后端排序修复当前行列 r
		celldata.push(xlsxPlusRef.value?.$fixCellIndex(cells, item.rowNum - 1))
	}
	// console.log(sheet)

	// Xlsx - plus sheets
	sheets.value = [
		{
			name: sheet.name,
			head: JSON.parse(sheet.header || '[]'),
			data:
				route.query.from === 'myturn'
					? _detailInfo.value.status === 0 ||
					  _detailInfo.value.status === 3 ||
					  _detailInfo.value.status === 2 ||
					  _detailInfo.value.status === 14
						? celldata.flat()
						: []
					: _detailInfo.value.status === 2 || _detailInfo.value.status === 14
					? celldata.flat()
					: [],
			config: JSON.parse(sheet.globalStyle || '{}'),
		},
	] as any
	// xlsxPlusRef.value.$reload()
}
// 获取报表任务所属子任务
const colData = [
	{
		title: '填报部门',
		field: 'filler',
	},
	{
		title: '数据量',
		field: 'totalDataRows',
	},
	{
		title: '提交时间',
		field: 'submitTime',
	},
	{
		title: '汇总时间',
		field: 'auditTime',
	},
	{
		title: '附件',
		field: 'attachments',
	},
	{
		title: '补充说明',
		field: 'fillingDescription',
	},
]
const tableData = ref<any>()
const totalCount = ref(10)
const exportRef = ref()
const buttons = [
	{
		type: 'primary',
		code: 'detail',
		title: '查看数据',
		icon: '<i i-majesticons-eye-line></i>',
		// verify: 'row.childrenStatus === 2',
		verify: 'true',
	},
	// {
	// 	type: 'primary',
	// 	code: 'urge',
	// 	title: '催办',
	// 	icon: '<i i-majesticons-clock-line></i>',
	// 	verify: 'row.status !== 4 && row.status !== 3 && row.status !== 2 && row.isTime',
	// },
	// {
	// 	type: 'primary',
	// 	code: 'childAudit',
	// 	title: '审核',
	// 	verify: 'row.status === 1',
	// },
	// {
	// 	type: 'primary',
	// 	code: 'urge',
	// 	title: '催办',
	// 	icon: '<i i-majesticons-clock-line></i>',
	// 	verify: 'row.status !== 4 && row.status !== 3 && row.status !== 2',
	// },
	{
		type: 'primary',
		code: 'export',
		title: '导出',
		icon: '<i i-majesticons-inbox-in-line></i>',
		verify: 'row.childrenStatus === 2',
	},
	// {
	// 	type: 'default',
	// 	code: 'revoke',
	// 	title: '撤回',
	// 	icon: '<i i-majesticons-inbox-out-line></i>',
	// 	verify: '!row.submitTime && row.childrenStatus !== 2',
	// },
]
const messageModalNotify = ref({
	messageDing: false,
	NotifyDing: false,
	phoneDing: false,
})
const notifyType = ref<number>()
const clickButton = async (btn: {
	btn: {
		code: string
		title: string
		verify: string
	}
	scope: any
}) => {
	console.log(btn.scope)
	if (btn.btn.code === 'detail') {
		if (!btn.scope.parentId) {
			dataAll()
		} else {
			router.push({
				path: '/statementTask/report-task-detail',
				query: {
					reportTaskId: btn.scope.reportTaskId,
					areaOrganizationUnitId: btn.scope.areaOrganizationUnitId,
					id: btn.scope.id,
					type: 'detail',
				},
			})
		}
	} else if (btn.btn.code === 'urge') {
		isUrging.value = true
		// ElMessageBox({
		// 	title: '任务催办',
		// 	showCancelButton: true,
		// 	message: `将发送消息提示${btn.scope.status === 3 ? '填报人' : '审核人'}进行${
		// 		btn.scope.status === 3 ? '填报' : '审核'
		// 	}操作`,
		// 	//  h('div', {style: 'margin-left:10px'}, [
		// 	// 	h(
		// 	// 		'p',
		// 	// 		{},
		// 	// 		`将发送消息提示${btn.scope.status === 3 ? '填报人' : '审核人'}进行${
		// 	// 			btn.scope.status === 3 ? '填报' : '审核'
		// 	// 		}操作`
		// 	// 	),
		// 	// 	h(
		// 	// 		'div',
		// 	// 		{
		// 	// 			style:
		// 	// 				'display:flex;align-items:center;padding-right:60px;flex:1;justify-content: space-between;',
		// 	// 		},
		// 	// 		[
		// 	// 			h('div', {}, [
		// 	// 				h(ElCheckbox, {
		// 	// 					modelValue: messageModalNotify.value.NotifyDing,
		// 	// 					'onUpdate:modelValue': (val: any) => {
		// 	// 						messageModalNotify.value.NotifyDing = val
		// 	// 						messageModalNotify.value.messageDing = false
		// 	// 						messageModalNotify.value.phoneDing = false
		// 	// 						notifyType.value = val ? 1 : undefined
		// 	// 					},
		// 	// 				}),
		// 	// 				h('span', {style: 'display:inline-block;margin-left:5px'}, '消息通知DING'),
		// 	// 			]),
		// 	// 			h('div', {}, [
		// 	// 				h(ElCheckbox, {
		// 	// 					modelValue: messageModalNotify.value.messageDing,
		// 	// 					'onUpdate:modelValue': (val: any) => {
		// 	// 						messageModalNotify.value.messageDing = val
		// 	// 						messageModalNotify.value.NotifyDing = false
		// 	// 						messageModalNotify.value.phoneDing = false
		// 	// 						notifyType.value = val ? 2 : undefined
		// 	// 					},
		// 	// 				}),
		// 	// 				h('span', {style: 'display:inline-block;margin-left:5px'}, '短信DING'),
		// 	// 			]),
		// 	// 			h('div', {}, [
		// 	// 				h(ElCheckbox, {
		// 	// 					modelValue: messageModalNotify.value.phoneDing,
		// 	// 					'onUpdate:modelValue': (val: any) => {
		// 	// 						messageModalNotify.value.phoneDing = val
		// 	// 						messageModalNotify.value.messageDing = false
		// 	// 						messageModalNotify.value.NotifyDing = false
		// 	// 						notifyType.value = val ? 3 : undefined
		// 	// 					},
		// 	// 				}),
		// 	// 				h('span', {style: 'display:inline-block;margin-left:5px'}, '电话DING'),
		// 	// 			]),
		// 	// 		]
		// 	// 	),
		// 	// ]),
		// })
		// 	.then(() => {
		// 		axios
		// 			?.post(`/api/filling/message-notice/part-urge`, {
		// 				reportTaskId: btn.scope.reportTaskId,
		// 				areaOrganizationUnitId: btn.scope.areaOrganizationUnitId,
		// 				dingMessageType: notifyType.value ?? 1,
		// 			})
		// 			.then((res) => {
		// 				// if (res.status === 200 || res.status === 204) {
		// 				ElNotification.success({
		// 					message: '已通知相关人员',
		// 					duration: 2000,
		// 				})
		// 				// }
		// 			})
		// 	})
		// 	.catch((action) => {
		// 		// console.log(action)
		// 	})
	} else if (btn.btn.code === 'export') {
		hasExport.value = true
		console.log(__reportHeadList2.value)
		// await __getReportList2(btn.scope.reportTaskId, btn.scope.areaOrganizationUnitId)
		await getChildReportList(btn.scope.reportTaskId, btn.scope.areaOrganizationUnitId)
		// await getDownloadData()
		// await __getReportList2(btn.scope.reportTaskId, btn.scope.areaOrganizationUnitId)

		// hasExport.value = false
	} else if (btn.btn.code === 'revoke') {
		ElMessageBox({
			title: '任务撤回',
			showCancelButton: true,
			message: `将撤回该任务，该任务下的所有填报任务将自动撤回，请确认操作`,
		}).then(() => {
			axios
				.delete(
					`/api/filling/report-task-ou/${btn.scope.reportTaskId}/${btn.scope.areaOrganizationUnitId}`
				)
				.then(async (res) => {
					ElNotification.success({
						title: '通知',
						message: '已撤回该任务',
					})
					await _getReportInfo()
					if (!_excel_canview.value) {
						await getChildrenReportTask({
							reportTaskId: _detailInfo.value.reportTaskId,
							areaOrganizationUnitId: _detailInfo.value.areaOrganizationUnitId,
							id: __id,
						})
					}
				})
		})
	} else if (btn.btn.code === 'childAudit') {
		PushReportAudit({
			reportTaskId: btn.scope.reportTaskId,
			areaOrganizationUnitId: btn.scope.areaOrganizationUnitId,
		})
			.then(() => {
				ElMessage.success('审核成功')
				getChildrenReportTask({
					reportTaskId: _detailInfo.value.reportTaskId,
					areaOrganizationUnitId: _detailInfo.value.areaOrganizationUnitId,
					id: __id,
				})
			})
			.catch((err) => {
				window.errMsg(err, '审核')
			})
	}
}
const isUrging = ref(false)
const urgingList = ref(['0', '1'])
const urgeingLoading = ref(false)
// 催办确定
const onUrging = () => {
	let urgingMessageTypes: any = []
	urgeingLoading.value = true
	urgingList.value.forEach((item: any) => {
		if (item == '0') {
			urgingMessageTypes.push(0)
		} else {
			urgingMessageTypes.push(Number(item))
		}
	})
	let urgingId: any = route.query.reportTaskId
	// if(statusText.value == 3){
	// 	urgingId = route.query.id
	// }
	urging(urgingId, {
		urgingMessageTypes: urgingMessageTypes,
		reportTaskType: 1,
	}).then((res) => {
		console.log(res)
		ElMessage.success('催办成功')
		urgeingLoading.value = false
		isUrging.value = false
	})
	// if (!newLedgerName.value) return ElMessage.error('请填写业务表名称')
	// updateLedgerName(currentDialogRow.value.id, newLedgerName.value).then((res: any) => {
	// 	getLedgerData()

	// 	openChangeLedgerName.value = false
	// 	ElMessage.success('更新成功')
	// })
}
// 催办取消
const closeUrging = () => {
	urgingList.value = ['0', '1']
	urgeingLoading.value = false
	isUrging.value = false
}
const getChildrenReportTask = async (data: {
	reportTaskId: string
	areaOrganizationUnitId: string
	id: string
}) => {
	await axios?.get(`/api/filling/report-task-ou/${data.id}/child-report-tasks`).then((res) => {
		const resData = res.data.items
			.map((re: any, index: number) => ({
				...re,
				isTime: isTime.value,
				id: re.id,
				parentId: re.reportTaskId,
				totalDataRows: re.totalDataRows === null ? 0 : re.totalDataRows,
				filler:
					re.department?.parent?.region?.name +
					'-' +
					re.department?.parent?.name +
					'-' +
					re.department?.name,
				status: re.status,
				childrenStatus: re.status,
				attachments: re.reportTask.attachments,
			}))
			.concat([
				{
					..._detailInfo.value,
					status: -1,
					totalDataRows:
						route.query.from === 'myturn'
							? _detailInfo.value.status === 2 ||
							  _detailInfo.value.status === 14 ||
							  _detailInfo.value.status === 4
								? _detailInfo.value.totalDataRows
								: 0
							: _detailInfo.value.status === 2 || _detailInfo.value.status === 14
							? _detailInfo.value.totalDataRows
							: 0,
					childrenStatus: 2,
					id: _detailInfo.value.reportTaskId,
					filler: _detailInfo.value?.department?.region?.name,
					attachments: [],
				},
			])
		tableData.value = util.arrayToTree(resData)
	})
}
const isTime = ref(false)
// 获取报表任务详情
const currentTaskInfo: any = ref(null)
const _getReportInfo = async () => {
	await axios.get(`/api/filling/report-task-ou/${__id}?type=2`).then(async (res) => {
		const {data} = res
		flowCode.value = data.dataAuditWorkflowSchemeCode
		currentTaskInfo.value = data
		console.log('当前任务详情', currentTaskInfo.value)
		if (data) {
			const currentTime = new Date()
			const userTimeString = data.reportTask.newToDay // 示例时间
			const userTime = new Date(Date.parse(userTimeString))
			isTime.value = userTime > currentTime
			_detailInfo.value = {
				taskName: data.reportTask.name,
				creationOrganizationUnitName: data?.createdDepartment ?? '-',
				department: data.department,
				creatorName: data.creatorName,
				// endDate: dayjs(data.reportTask.newToDay).format('YYYY-MM-DD'),
				endDate: data.reportTask.newToDay,
				description: data.reportTask.description,
				fillingDescription: data.fillingDescription,
				batch: GetBatch(data.reportTask.fillingPeriodType, data.creationTime),
				status: data.status,
				reportTaskId: data.reportTaskId,
				areaOrganizationUnitId: data.areaOrganizationUnitId,
				submitTime: data.fillerSubmitTime, //data.submitTime,
				auditTime: data.auditTime,
				areaOrganizationUnit: data.areaOrganizationUnit,
				totalDataRows: data.totalDataRows,
				reportTask: data.reportTask,
				attachments: data.reportTask.attachments,
				fileInfos: data.planTask?.fileInfos,
			}
			if (data.planTask?.fileInfos && data.planTask?.fileInfos.length !== 0) {
				titleList.value.push({
					width: '100%',
					title: '相关附件',
					key: 'fileInfos',
				})
			}
			if (data.status !== 4 && data.status !== 7 && data.status !== 16) {
				titleList.value.push({
					width: '100%',
					title: '提交时间',
					key: 'submitTime',
				})
			}
			if (data.status === 2 || data.status === 14) {
				titleList.value.push({
					width: '100%',
					title: '填报附件',
					key: 'attachments',
				})
			}

			updateLabelTitle({
				path: router.currentRoute.value.fullPath,
				title: `查看数据-报表创建-${data.reportTask.name}`,
			})
		}

		// if (currentTaskInfo.value) {
		// 	if (currentTaskInfo.value.dataAuditWorkflowSchemeCode) {
		// 		await getLogList(currentTaskInfo.value.dataAuditWorkflowSchemeCode, 'data')
		// 	}

		// 	if (currentTaskInfo.value.reportTaskDataAuditProcessId) {
		// 		await getLogList(currentTaskInfo.value.reportTaskDataAuditProcessId, 'issue')
		// 	}
		// }
	})
}

/**
 * 获取当前报表是否有权限编辑
 * @param id
 */
const __getPermissionsByReportId = (id: string) => {
	return new Promise((resolve, reject) => {
		resolve(false)
	})
}

/**
 * 根据报表模版id获取列表数据
 * @param id
 */
let reportList: any = []
const __getReportListById = async (id: string, loading: boolean = true, sheet?: any) => {
	// sheetData.value = sheet;
	// _data.value = null
	viewStore.enableLoading = loading
	const result = await axios.batch(`/api/filling/report-table/${id}/data?type=2`)
	reportList = result
	viewStore.enableLoading = true
	if (sheet) {
		const celldata: any = []
		for (let i = 0; i < reportList.length; i++) {
			const item = reportList[i]
			const cells = JSON.parse(item.rawData)
			// 根据后端排序修复当前行列 r
			celldata.push(xlsxRef.value.$fixCellIndex(cells, item.rowNum - 1))
		}
		// Xlsx - plus sheets
		_data.value = [
			{
				name: sheet.name,
				head: JSON.parse(sheet.header || '[]'),
				data:
					_detailInfo.value.status === 2 || _detailInfo.value.status === 14
						? celldata.flat()
						: [],
				config: JSON.parse(sheet.globalStyle || '{}'),
			},
		] as any
	}
}

/**
 * 选项卡切换的时候获取表格数据, 并且清空excel
 * @param pane
 * @param ev
 */
const __tabClick = (pane: TabsPaneContext, ev: Event) => {
	// __getReportListById(pane.paneName as string)
	__currentHead.value = pane.paneName as string
	__getReportList()
}
const __tabClick2 = (pane: TabsPaneContext, ev: Event) => {
	// __getReportListById(pane.paneName as string)
	__currentHead2.value = pane.paneName as string
	__getReportList2(_detailInfo.value.reportTaskId, _detailInfo.value.areaOrganizationUnitId)
}
const dataVisible = ref(false)
const _data = ref()
// 数据汇总
const __reportHeadList2 = ref()
const __currentHead2 = ref()
const xlsxRef = ref()
const hasExport = ref(false)
// _detailInfo.value.
const __getReportList2 = async (reportTaskId?: string, areaOrganizationUnitId?: string) => {
	let sheet
	if (__reportHeadList2.value) {
		sheet = __reportHeadList2.value.find((item: any) => item.id === __currentHead2.value)
		await __getReportListById(__currentHead2.value)
	} else {
		// _detailInfo.value.reportTaskId
		const res = await axios.get(`/api/filling/report-task-ou/${__id}/report-tables`)
		const {data} = res
		// 选项卡
		// for (let x of data) {
		sheet = data[0]
		__reportHeadList2.value = data
		console.log(__reportHeadList2.value)

		__currentHead2.value = sheet.id
		await __getReportListById(sheet.id, true, sheet)
		// }
		// data.forEach( (x: any) => {

		// })
		// hasExport.value = false
		return
	}
	const celldata = []
	for (let i = 0; i < reportList.length; i++) {
		const item = reportList[i]
		const cells = JSON.parse(item.rawData)
		// 根据后端排序修复当前行列 r
		celldata.push(xlsxRef.value.$fixCellIndex(cells, item.rowNum - 1))
	}
	// Xlsx - plus sheets
	_data.value = [
		{
			name: sheet.name,
			head: JSON.parse(sheet.header || '[]'),
			data:
				route.query.from === 'myturn'
					? celldata.flat()
					: _detailInfo.value.status === 0 ||
					  _detailInfo.value.status === 3 ||
					  _detailInfo.value.status === 2 ||
					  _detailInfo.value.status === 14
					? celldata.flat()
					: [],
			config: JSON.parse(sheet.globalStyle || '{}'),
		},
	] as any
	//
}
const getChildReportList = async (reportTaskId?: string, areaOrganizationUnitId?: string) => {
	const res = await axios.get(`/api/filling/report-task-ou/${__id}/report-tables`)
	const {data} = res
	__reportHeadList2.value = data
	if (res) {
		getDownloadData()
	}
}
const _downloadData = ref([])
async function getDownloadData() {
	for (let sheet of __reportHeadList2.value) {
		const result = await axios.batch(`/api/filling/report-table/${sheet.id}/data`)
		reportList = result
		//viewStore.enableLoading = true
		if (sheet) {
			const celldata: any = []
			for (let i = 0; i < reportList.length; i++) {
				const item = reportList[i]
				const cells = JSON.parse(item.rawData)
				// 根据后端排序修复当前行列 r
				celldata.push(xlsxRef.value.$fixCellIndex(cells, item.rowNum - 1))
			}

			// Xlsx - plus sheets
			_downloadData.value = [
				{
					name: sheet.name.replace(/[*?:\\\/\[\]]/g, ''),
					head: JSON.parse(sheet.header || '[]'),
					data: celldata.flat(),
					config: JSON.parse(sheet.globalStyle || '{}'),
				},
			] as any
			console.log(777, _downloadData.value)
			exportRef.value?.onClick(_downloadData.value)
		}
	}
}
let reportList2: any = []
const __getReportListById2 = async (id: string, loading: boolean = true) => {
	viewStore.enableLoading = loading
	const result = await axios.batch(`/api/filling/report-table/${id}/data`)
	reportList2 = result
	viewStore.enableLoading = true
}
const dataAll = () => {
	dataVisible.value = true
	__getReportList2(_detailInfo.value.reportTaskId, _detailInfo.value.areaOrganizationUnitId)
}
const __onCloseSheet = (sheet: any) => {
	dataVisible.value = false
}
const tableOffsetHeight = ref(0)
const tableRef = ref()
const onToggleInfoOverViewVisible = (hide: boolean, height: number) => {
	if (hide) {
		tableOffsetHeight.value = height
	} else {
		tableOffsetHeight.value = 0
	}

	// if()
	if (xlsxPlusRef.value) xlsxPlusRef.value?.$resize()
	if (tableRef.value) {
		setTimeout(() => {
			tableRef.value.resize()
		}, 500)
	}
}
const autoHeight = ref(0)
const getAutoHeight = () => {
	tableOffsetHeight.value = Math.ceil(titleList.value.length / 4) * 40 + 15
	const height = document.documentElement.clientHeight - tableOffsetHeight.value - 320
	autoHeight.value = height
}
const blockRef = ref<any>(null)
const autoWidth = ref(0)

const openHeight = ref(0)
const showProgress = ref(true)

const openOrCloseSchedule = (type: number) => {
	openHeight.value = document.body.clientHeight - 140
	if (type === 1) {
		showProgress.value = true
		if (xlsxPlusRef.value) xlsxPlusRef.value?.$resize()
	}
	if (type === 2) {
		showProgress.value = false
		if (xlsxPlusRef.value) xlsxPlusRef.value?.$resize()
	}
}

const logs: any = ref({})
const getLogList = async (val: string, type: any = null) => {
	const promise = []

	if (type === 'data') {
		promise.push(GetReportLogList(val))
	}

	if (type === 'issue') {
		promise.push(GetWorkflowProcess(val))
	}

	const res = await Promise.all(promise)
	const {data} = res[0]
	console.log('流程记录', res)

	if (type === 'data') {
		const status: any = {
			1: '待填写',
			2: '已提交',
			3: '已驳回',
		}

		logs.value['data'] = []
		if (currentTaskInfo.value.reportTaskAreaOrganizationUnitFillers.length > 0) {
			logs.value['data'].push({
				label: '填报数据',
				text: currentTaskInfo.value.reportTaskAreaOrganizationUnitFillers
					.map(
						(v: any) =>
							v.filler?.name +
							' - ' +
							status[v.reportTaskAreaOrganizationUnitFillerStatus] +
							'<br />' +
							`${v.submitTime ? v.submitTime + '<br />' : ''}`
					)
					.join(''),
			})
		}

		if (!currentTaskInfo.value.reportTaskDataAuditProcessId) {
			logs.value['data'].push({
				label: '审核数据',
				text: data.map((v: any) => `${v.departmentName} - ${v.name}`).join('<br />'),
			})
		}
	} else if (type === 'issue') {
		const auditors = data.currentNodeAuditors?.map((v: any) => v.auditors).flat()
		const names = auditors.map((v: any) => `${v.departmentName} - ${v.name}`).join('<br />')
		logs.value['data'] = []
		logs.value['data'].push({
			label: '审核数据',
			text: names,
		})
	}

	console.log('流程记录列表', logs.value)
}
const handlePreviewOrDownload = (att: any) => {
	// window.open(APIConfig('base') + att.path, '_blank')
	util.downloadAttachment(att, 1)
}

let xlsxPlusParent: any = null
const isFull = ref(false)
const onFull = () => {
	isFull.value = !isFull.value

	const xlsxPlus = document.querySelector('.xlsx-plus-component') as HTMLElement
	if (isFull.value) {
		xlsxPlusParent = xlsxPlus.parentElement
		xlsxPlus.classList.add('xlsx-plus-component-full')
		document.body.appendChild(xlsxPlus)
	} else {
		xlsxPlus.classList.remove('xlsx-plus-component-full')
		xlsxPlusParent.appendChild(xlsxPlus)
		xlsxPlusParent = null
	}

	xlsxPlusRef.value.$resize()
}

const businessProcessData: any = ref({})
const getBusinessProcess = () => {
	const from = route.query.from
	if (from === 'mycreate' || !from) {
		GetPlanTaskProcessDetail({
			reportTaskId: __reportId,
			areaOrganizationUnitId: __areaOrganizationUnitId,
			id: __id,
		}).then((res: any) => {
			businessProcessData.value = res.data
		})
	} else if (from === 'myturn') {
		GetTurnTaskProcess({
			reportTaskId: __reportId,
			areaOrganizationUnitId: __areaOrganizationUnitId,
			id: __id,
		}).then((res: any) => {
			businessProcessData.value = res.data
		})
	}
}

onMounted(async () => {
	autoWidth.value = document.body.clientWidth
	getAutoHeight()

	await _getReportInfo()
	await __getReportList2(_detailInfo.value.reportTaskId, _detailInfo.value.areaOrganizationUnitId)
	if (!_excel_canview.value) {
		await getChildrenReportTask({
			reportTaskId: _detailInfo.value.reportTaskId,
			areaOrganizationUnitId: _detailInfo.value.areaOrganizationUnitId,
			id: __id,
		})
	} else {
		__getReportList()
	}

	getBusinessProcess()

	setTimeout(() => {
		blockRef.value?.resize()
	}, 500)
})
</script>
<template>
	<div class="report-task-detail">
		<div
			v-show="!showProgress"
			class="open-history"
			:style="{height: openHeight + 'px'}"
			@click="openOrCloseSchedule(1)"
		>
			<span
				>展开流程记录
				<i
					class="icon i-ic-baseline-keyboard-double-arrow-right"
					style="position: relative; left: -1px"
				></i
			></span>
		</div>

		<div v-show="showProgress" class="right">
			<Block
				ref="blockHistoryRef"
				title="流程记录"
				:enable-back-button="false"
				:enable-expand-content="false"
				:enable-fixed-height="true"
				:enable-close-button="false"
			>
				<template #topRight>
					<span
						style="
							cursor: pointer;
							display: flex;
							align-items: center;
							color: var(--z-main);
						"
						@click="openOrCloseSchedule(2)"
					>
						<i class="icon i-ic-baseline-keyboard-double-arrow-left"></i>
						收起
					</span>
				</template>

				<BusinessProcess :data="businessProcessData" :isDetail="true"></BusinessProcess>
				<!-- <Record v-if="logs['data']" sort="asc" :data="logs['data']"></Record>
				<div v-if="!logs['data']" class="df aic jcc pd-20">未配置下发审核人</div> -->
				<!-- <ScheduleProgressComp v-if="data" :type="1" :plan-task-id="data?.id"></ScheduleProgressComp> -->
			</Block>
		</div>

		<div class="left">
			<Block ref="blockRef" title="任务详情" :delay="256" :enable-expand-content="false">
				<div class="w-full dfw flx mg-bottom-15">
					<InfoOverViewComp
						:data="_detailInfo"
						:titles="titleList"
						@onToggleVisible="onToggleInfoOverViewVisible"
					>
						<template #_dataflow>
							<div class="df aic pd-10">
								<el-button type="primary" size="small" @click="showFlow = true"
									>查看流程</el-button
								>
							</div>
						</template>
					</InfoOverViewComp>
				</div>
				<el-tabs
					v-model="__currentHead"
					type="card"
					@tab-click="__tabClick"
					v-if="
						_excel_canview &&
						_detailInfo?.status !== 4 &&
						_detailInfo?.status !== 7 &&
						_detailInfo?.status !== 16
					"
				>
					<el-tab-pane
						v-for="item of __reportHeadList"
						:label="item?.displayName"
						:name="item?.id"
					></el-tab-pane>
				</el-tabs>
				<div
					class="excel-box"
					v-if="_excel_canview"
					:style="{height: `calc(100% - ${tableOffsetHeight + 100}px)`}"
				>
					<XlsxPlusComp
						class="xlsx-plus-component"
						v-if="
							_detailInfo?.status !== 4 &&
							_detailInfo?.status !== 7 &&
							_detailInfo?.status !== 16
						"
						ref="xlsxPlusRef"
						:enableReload="true"
						:sheets="sheets"
						:viewonly="route.query.type === 'detail' || route.query.type === 'audit'"
						:style="{height: autoHeight + 'px'}"
					>
						<template #headerRight>
							<div class="excel_tips"></div>
							<el-button
								type="primary"
								size="small"
								style="display: flex; align-items: center"
								@click="onFull"
								class="mg-left-10"
							>
								<template v-if="isFull">
									<i
										i-ic-sharp-fullscreen-exit
										class="mg-left-5"
										style="font-size: 18px"
									></i>
									<span>收起</span>
								</template>
								<template v-else>
									<i
										i-ic-baseline-fullscreen
										class="mg-left-5"
										style="font-size: 18px"
									></i>
									<span>全屏</span>
								</template>
							</el-button>
						</template>
					</XlsxPlusComp>
					<div v-else-if="_detailInfo?.status === 16" class="waiting-review">
						<i class="icon i-pajamas-review-warning"></i>
						<span>该数据等待审核中, 无法查看详情</span>
					</div>
				</div>
				<BaseTableComp
					v-if="!_excel_canview"
					:colData="colData"
					ref="tableRef"
					:data="tableData"
					row-key="id"
					:offsetHeight="tableOffsetHeight === 0 ? 120 : 30"
					:checkbox="false"
					:buttons="buttons"
					:total="totalCount"
					:visible-setting="false"
					:visible-search="false"
					:visible-export="false"
					:visible-header="true"
					:visible-page="true"
					@click-button="clickButton"
				>
					<template v-slot:header>
						<div w-full flex="~" items-center justify-end>
							<!-- <el-button type="primary" size="small" @click="dataAll">数据汇总</el-button> -->
							<ExportComp
								ref="exportRef"
								:sheets="_downloadData"
								:fileName="_data?.name"
								style="visibility: hidden"
							></ExportComp>
						</div>
					</template>
					<template #attachments="scope">
						<!-- {{ scope.rowData.attachments }} -->
						<el-popover
							placement="bottom"
							:width="150"
							trigger="hover"
							v-if="scope.rowData.attachments.length !== 0"
						>
							<template #reference>
								<el-link type="primary">附件</el-link>
							</template>
							<div w-full>
								<div v-for="att of scope.rowData.attachments" mb="5px">
									<el-link type="primary" @click="handlePreviewOrDownload(att)">
										{{
											att.name.includes('-')
												? att.name.split('-').slice(1).join()
												: att.name
										}}</el-link
									>
								</div>
							</div>
						</el-popover>
						<span v-else>-</span>
					</template>
					<!-- <template #fillingDescription="scope">
				{{ scope.rowData.status }}
			</template> -->
				</BaseTableComp>
			</Block>
		</div>

		<!-- <TopComp title="任务详情"> </TopComp> -->

		<DrawerPlusComp :visible="dataVisible" @onClose="__onCloseSheet" title="数据汇总">
			<template #content>
				<div relative>
					<el-tabs v-model="__currentHead2" type="card" @tab-click="__tabClick2">
						<el-tab-pane
							v-for="item of __reportHeadList2"
							:label="item?.displayName"
							:name="item?.id"
						></el-tab-pane>
					</el-tabs>
					<!-- <ExportComp absolute right-0 top-0 ref="exportRef" :sheets="_data"></ExportComp> -->
				</div>

				<XlsxPlusComp ref="xlsxRef" :sheets="_data" viewonly enableReload> </XlsxPlusComp>
			</template>
		</DrawerPlusComp>
		<Dialog
			v-model="isUrging"
			title="报表催办"
			width="600"
			@clickConfirm="onUrging"
			@close="closeUrging"
			:loading="urgeingLoading"
		>
			<div>
				<p>将批量通知各未完成填报部门尽快完成填报</p>
				<el-checkbox-group v-model="urgingList" style="margin-top: 10px">
					<el-checkbox label="发送系统内消息" value="0" />
					<el-checkbox label="发送渝快政工作通知" value="1" />
					<el-checkbox label="发送渝快政Ding消息" value="2" />
				</el-checkbox-group>
			</div>
		</Dialog>
		<ViewFlow v-model="showFlow" :code="flowCode"></ViewFlow>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-tabs__header) {
	margin-top: 10px;
	margin-bottom: 0;
}

.report-task-detail {
	display: flex;
	.left {
		flex: 1;
		width: calc(100% - 320px);
	}

	.right {
		margin-right: 20px;
		width: 300px;
		flex: none;
	}

	.open-history {
		align-items: center;
		border-bottom: 3px solid rgba(var(--z-line-rgb), 0.5);
		background-color: #fff;
		cursor: pointer;
		display: flex;
		line-height: 1.5;
		margin-right: 20px;
		justify-content: center;
		span {
			color: var(--z-main);
			font-size: 16px;
			width: 20px;
		}
		width: 50px;
	}
}

.excel-box {
	border-radius: 0 5px 5px 5px;
	box-shadow: 0 -6px 6px -6px rgba(0, 0, 0, 0.12);
	border: 1px solid #e4e7ed;
	border-top: none;
	padding: 0 10px 10px 10px;
}

.excel_tips {
	align-items: center;
	display: flex;

	i {
		color: var(--z-warning);
	}

	span {
		color: var(--z-warning);
		padding: 0 10px;
	}
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item.is-active) {
	border-radius: 5px;
	background: var(--z-theme);
}
.xlsx-plus-component-full {
	position: fixed !important;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	padding: 0 10px 10px 10px !important;
	height: 100% !important;
	z-index: 9;
	background-color: #fff;
}
.waiting-review {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	height: 100%;
	font-size: 20px;
	color: #999;
	padding: 100px 30px;

	i {
		font-size: 48px;
		margin-bottom: 30px;
	}
}
</style>
