<script setup lang="ts" name="copyreport">
const emits = defineEmits(['update:modelValue'])
const copyReportList = ref([0, 1])
const handleConfirm = () => {
	ElMessage.success('复制成功')
	onClose()
}
const onClose = () => {
	emits('update:modelValue', false)
}
</script>
<template>
	<Dialog v-bind="$attrs" @click-confirm="handleConfirm" @click-close="onClose" @close="onClose">
		<div class="copy-report fs-14">
			是否复制该报表？仅复制报表中的填写内容，不复制报表状态，复制后将批量通知选择的这些任务正在进行环节的处理人。只催办待审核、进行中的任务。
		</div>
		<div class="copy-report-checkbox">
			<el-checkbox-group v-model="copyReportList">
				<el-checkbox label="系统通知" :value="0" />
				<el-checkbox label="渝快政工作通知" :value="1" />
				<el-checkbox label="渝快政Ding通知" :value="2" />
			</el-checkbox-group>
		</div>
	</Dialog>
</template>
<style scoped lang="scss">
.copy-report {
	line-height: 1.5;
}
.copy-report-checkbox {
	margin-top: 10px;
}
</style>
