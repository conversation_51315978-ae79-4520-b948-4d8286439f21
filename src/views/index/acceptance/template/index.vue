<script setup lang="ts" name="template">
import {TestPost, TestPut, TestDelete} from '@/api/AcceptanceApi'
// 搜索表单
const searchFormProp = ref([{label: '任务名称', prop: 'name', type: 'text'}])
const searchForm = ref({name: ''})

// 加载状态
const loading = ref(false)

// 表格ref
const tableRef = ref()
const tableHeight = ref(0) // 表格高度
const currentRow = ref(null)
const buttons = [
	{label: '编辑', type: 'primary', code: 'edit'},
	{label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除吗?'},
] // 操作按钮
const columns = [{prop: 'name', label: '名称'}] // 表头

// 分页
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})

// 列表请求参数
const reqParams = reactive({
	name: '',
	skipCount: 0,
	maxResultCount: 10,
})

const showDialogForm = ref(false) // 新增或编辑弹窗
const dialogFormRef = ref() // 弹窗表单ref
// 弹窗表单数据
const dialogForm: any = ref({
	text: '设置默认值',
})
// 弹窗表单属性
const dialogFormProps = ref([
	{label: '名称', prop: 'name', type: 'text'},
	{label: '文本', prop: 'text', type: 'text'},
	{label: '数字', prop: 'number', type: 'text', inputType: 'number'},

	{label: '文本域', prop: 'textarea', type: 'textarea'},
	{
		label: '下拉',
		prop: 'select',
		type: 'select',
		options: [
			{label: '1', value: '1'},
			{label: '2', value: '2'},
		],
	},
	{
		label: '单选',
		prop: 'radio',
		type: 'radio',
		options: [
			{label: '1', value: '1'},
			{label: '2', value: '2'},
		],
	},
	{
		label: '多选',
		prop: 'checkbox',
		type: 'checkbox',
		options: [
			{label: '1', value: '1'},
			{label: '2', value: '2'},
		],
	},
	{label: '日期', prop: 'date', type: 'date'},
	{label: '时间', prop: 'time', type: 'datetime'},
	{label: '日期范围', prop: 'daterange', type: 'daterange'},
	{label: '日期时间范围', prop: 'datetimerange', type: 'datetimerange'},
	{label: '开关', prop: 'switch', type: 'switch'},
])
// 弹框表单校验规则
const dialogFormRules = {
	name: [{required: true, message: '请输入名称', trigger: 'blur'}],
}

// 查询
const onSearch = () => {
	pagination.page = 1
	reqParams.skipCount = 0
	reqParams.maxResultCount = pagination.size
	// 其他查询参数
	reqParams.name = searchForm.value.name
}

// 表格操作点击事件, row 当前行数据
const onTableClickButton = ({row, btn}: any) => {
	if (btn.code === 'edit') {
		currentRow.value = row
		Object.assign(dialogForm.value, row)
		showDialogForm.value = true
	} else if (btn.code === 'delete') {
		TestDelete(row.id).then(() => {
			ElMessage.success('删除成功')
		})
	}
}

// 新增
const onClickAdd = () => {
	showDialogForm.value = true
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.page = val
		reqParams.skipCount = (val - 1) * pagination.size
	} else {
		pagination.size = val
		reqParams.maxResultCount = pagination.size
	}
}

// 块高度变化事件时修改表格高度
const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}

// 弹框表单提交
const onDialogConfirm = () => {
	dialogFormRef.value.validate((valid: boolean) => {
		if (valid) {
			// 处理新增或编辑
			loading.value = true
			if (currentRow.value) {
				TestPut({
					name: dialogForm.value.name,
					// ...其他字段
				})
					.then(() => {
						ElMessage.success('编辑成功')
						showDialogForm.value = false
						tableRef.value.reload()
					})
					.finally(() => {
						loading.value = false
					})
			} else {
				TestPost({
					name: dialogForm.value.name,
					// ...其他字段
				})
					.then(() => {
						ElMessage.success('新增成功')
						showDialogForm.value = false
						tableRef.value.reload()
					})
					.finally(() => {
						loading.value = false
					})
			}
		}
	})
}
</script>
<template>
	<div class="template">
		<Block title="模版" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
			<template #topRight>
				<el-button size="small" type="primary" @click="onClickAdd">新增</el-button>
			</template>
			<template #expand>
				<!-- 搜索 -->
				<div class="search">
					<Form
						:props="searchFormProp"
						v-model="searchForm"
						:column-count="2"
						:label-width="74"
						:enable-reset="false"
						confirm-text="查询"
						button-vertical="flowing"
						@submit="onSearch"
					></Form>
				</div>
			</template>
			<!-- 列表 -->
			<TableV2
				ref="tableRef"
				url="/api/ledger-service/ledger/my-management"
				:columns="columns"
				:req-params="reqParams"
				:enable-toolbar="false"
				:enable-own-button="false"
				:height="tableHeight"
				:buttons="buttons"
				@loading="loading = $event"
				@click-button="onTableClickButton"
				@completed="
					() => {
						pagination.total = tableRef.getTotal()
					}
				"
			></TableV2>
			<!-- 分页 -->
			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			></Pagination>
		</Block>

		<Dialog
			v-model="showDialogForm"
			:title="currentRow ? '编辑' : '新增'"
			:destroy-on-close="true"
			:loading="loading"
			loading-text="保存中"
			@closed=";(currentRow = null), (dialogForm = {})"
			@click-confirm="onDialogConfirm"
		>
			<Form
				ref="dialogFormRef"
				v-model="dialogForm"
				:props="dialogFormProps"
				:rules="dialogFormRules"
				:enable-button="false"
			></Form>
		</Dialog>
	</div>
</template>
<route>
    {
        meta: {
            title: '模板',
        },
    }
</route>
<style scoped lang="scss"></style>
