<script setup lang="ts" name="warningAlert">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Search } from '@element-plus/icons-vue'
import * as ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'
import { useUserStore } from '@/stores/useUserStore'

// 路由实例
const router = useRouter()

// 用户store
const userStore = useUserStore()

// 获取当前操作人姓名
const getCurrentOperatorName = (): string => {
  const userInfo = userStore.getUserInfo
  if (userInfo) {
    // 优先使用 displayName，其次使用 name，最后使用 account
    return userInfo.displayName || userInfo.name || userInfo.account || '未知用户'
  }
  return '未知用户'
}

// 预警任务接口
interface WarningTask {
  id: string
  index: number
  taskName: string
  warningValueType: string
  warningNotification: boolean
  escalationCondition: string
  notificationChannels: string[]
  notificationFrequency: string
  createTime: string
  updateTime: string
  status: string
  reportGenerated: boolean // 是否已生成升级原因分析报告
  reportData?: any // 报告数据（生成后存储）
  hasDataAnomalyIssue: boolean // 是否有数据异常问题
}

// 预警指标阈值接口
interface WarningThreshold {
  id: string
  name: string
  range: string
}

// 预警指标接口
interface WarningIndicator {
  id: string
  name: string
  thresholds: WarningThreshold[]
}

// 预警值类型接口
interface WarningValueType {
  id: string
  name: string
  indicators: WarningIndicator[]
  createTime: string
}

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const tableHeight = ref(0)
const tableRef = ref()
const selectedRows = ref<WarningTask[]>([])

// 预警值类型配置相关
const warningValueTypes = ref<WarningValueType[]>([])
const showWarningValueTypeDialog = ref(false)
const showWarningValueTypeFormDialog = ref(false)
const warningValueTypeFormDialogRef = ref()
const warningValueTypeForm = ref({
  name: '',
  indicators: [] as WarningIndicator[]
})

// 数据异常问题相关
interface DataAnomalyIssue {
  id: string
  sequence: number
  source: string // 数据异常来源（关联的抽查任务名称）
  content: string // 数据异常内容
  createTime: string
}

const showDataAnomalyDialog = ref(false)
const dataAnomalyIssues = ref<DataAnomalyIssue[]>([])
const dataAnomalySearchText = ref('')
const dataAnomalyPagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 编辑数据异常问题相关
const showEditDataAnomalyDialog = ref(false)
const editDataAnomalyFormRef = ref()
const currentEditDataAnomalyRow = ref<DataAnomalyIssue | null>(null)
const editDataAnomalyForm = ref({
  source: '',
  content: ''
})

// 编辑数据异常问题表单验证规则
const editDataAnomalyFormRules = {
  source: [
    { required: true, message: '请输入数据异常来源', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入数据异常内容', trigger: 'blur' },
    { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
  ]
}

// 预警升级原因分析报告导出记录相关
interface ExportRecord {
  id: string
  sequence: number
  exportTime: string // 导出时间
  operator: string // 操作人员
  reportType: string // 报告类型
  exportStatus: 'success' | 'failed' | 'in-progress' // 导出状态
  fileSize: string // 文件大小
  createTime: string
}

const showExportRecordDialog = ref(false)
const exportRecords = ref<ExportRecord[]>([])
const exportRecordSearchText = ref('')
const exportRecordPagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})
const currentWarningValueType = ref<WarningValueType | null>(null)
const isWarningValueTypeViewMode = ref(false)
const warningValueTypeSearchText = ref('')
const activeIndicators = ref<string[]>([])
const indicatorSearchText = ref('')
const indicatorSearchKeyword = ref('')

// 搜索表单
const searchFormProp = computed(() => [
  { label: '预警任务名称', prop: 'taskName', type: 'text' },
  { label: '预警值类型', prop: 'warningValueType', type: 'select', options: warningValueTypes.value.map(type => ({
    label: type.name,
    value: type.name
  }))}
])
const searchForm = ref({ taskName: '', warningValueType: '' })

// 表头配置
const columns = [
  { prop: 'taskName', label: '预警任务名称', minWidth: 200 },
  { prop: 'warningValueType', label: '预警值类型', minWidth: 150 },
  { prop: 'warningNotification', label: '预警通知', width: 100 },
  { prop: 'escalationCondition', label: '预警升级条件', minWidth: 200 },
  { prop: 'notificationChannels', label: '通知渠道', minWidth: 150 },
  { prop: 'notificationFrequency', label: '通知频率', width: 120 },
  { prop: 'status', label: '状态', width: 100, sortable: true },
  { prop: 'hasDataAnomalyIssue', label: '是否有数据异常问题', width: 150 },
  { prop: 'createTime', label: '创建时间', minWidth: 150, sortable: true },
  { prop: 'updateTime', label: '更新时间', minWidth: 150 },
  { prop: 'reportAction', label: '生成升级原因分析报告', width: 180 }
]

// 分页配置
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 表格数据
const tableData = ref<WarningTask[]>([])

// 排序配置
const sortConfig = ref({
  prop: 'createTime',
  order: 'descending'
})

// 处理排序变化
const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  sortConfig.value = { prop, order }

  if (!prop || !order) {
    // 如果没有排序，恢复默认排序（创建时间倒序）
    sortConfig.value = { prop: 'createTime', order: 'descending' }
  }

  // 执行排序
  applySorting()
}

// 应用排序
const applySorting = () => {
  const { prop, order } = sortConfig.value

  if (!prop || !order) return

  tableData.value.sort((a, b) => {
    let aValue: any = a[prop as keyof WarningTask]
    let bValue: any = b[prop as keyof WarningTask]

    // 处理不同数据类型的排序
    if (prop === 'createTime' || prop === 'updateTime') {
      // 时间排序
      aValue = new Date(aValue).getTime()
      bValue = new Date(bValue).getTime()
    } else if (prop === 'status') {
      // 状态排序：定义状态优先级
      const statusOrder = { '无预警': 1, '已预警': 2, '预警已升级': 3 }
      aValue = statusOrder[aValue as keyof typeof statusOrder] || 0
      bValue = statusOrder[bValue as keyof typeof statusOrder] || 0
    }

    // 执行排序比较
    if (order === 'ascending') {
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
    } else {
      return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
    }
  })
}

// 操作按钮配置
const buttons = [
  { label: '查看', type: 'info', code: 'view' },
  { label: '编辑', type: 'primary', code: 'edit' },
  { label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除该预警任务吗?' }
]

// 对话框相关
const showDialogForm = ref(false)
const dialogFormRef = ref()
const currentRow = ref<WarningTask | null>(null)
const isViewMode = ref(false) // 是否为查看模式

// 对话框表单数据
const dialogForm = ref({
  taskName: '',
  warningValueType: '',
  warningNotification: true,
  escalationCondition: '',
  notificationChannels: [] as string[],
  notificationFrequency: ''
})

// 对话框表单属性配置（基础配置）
const baseDialogFormProps = computed(() => [
  { label: '预警任务名称', prop: 'taskName', type: 'text', required: true },
  {
    label: '预警值类型',
    prop: 'warningValueType',
    type: 'select',
    required: true,
    options: warningValueTypes.value.map(type => ({
      label: type.name,
      value: type.name
    }))
  },
  { label: '预警通知', prop: 'warningNotification', type: 'radio', required: true, options: [
    { label: '是', value: true },
    { label: '否', value: false }
  ]},
  { label: '预警升级条件', prop: 'escalationCondition', type: 'text' },
  {
    label: '通知渠道',
    prop: 'notificationChannels',
    type: 'checkbox',
    options: [
      { label: '邮件', value: '邮件' },
      { label: '短信', value: '短信' },
      { label: '系统通知', value: '系统通知' },
      { label: '微信', value: '微信' },
      { label: '钉钉', value: '钉钉' }
    ]
  },
  {
    label: '通知频率',
    prop: 'notificationFrequency',
    type: 'select',
    options: [
      { label: '实时', value: '实时' },
      { label: '每小时', value: '每小时' },
      { label: '每日', value: '每日' },
      { label: '每周', value: '每周' },
      { label: '每月', value: '每月' }
    ]
  }
])

// 动态表单属性配置（根据查看模式调整）
const dialogFormProps = computed(() => {
  return baseDialogFormProps.value.map(prop => ({
    ...prop,
    disabled: isViewMode.value
  }))
})

// 对话框表单校验规则
const dialogFormRules = {
  taskName: [
    { required: true, message: '请输入预警任务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '任务名称长度应在2-50个字符之间', trigger: 'blur' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (value) {
          // 检查是否与现有任务名称重复（编辑时排除自己）
          const existingTask = tableData.value.find(task =>
            task.taskName === value && task.id !== currentRow.value?.id
          )
          if (existingTask) {
            callback(new Error('预警任务名称已存在，请使用其他名称'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  warningValueType: [{ required: true, message: '请选择预警值类型', trigger: 'change' }],
  warningNotification: [{ required: true, message: '请选择是否启用预警通知', trigger: 'change' }],
  escalationCondition: [
    { max: 200, message: '预警升级条件不能超过200个字符', trigger: 'blur' }
  ],
  notificationChannels: [
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (dialogForm.value.warningNotification && (!value || value.length === 0)) {
          callback(new Error('启用预警通知时，请至少选择一个通知渠道'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  notificationFrequency: [
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (dialogForm.value.warningNotification && !value) {
          callback(new Error('启用预警通知时，请选择通知频率'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  if (isViewMode.value) {
    return '查看预警任务'
  }
  return currentRow.value ? '编辑预警任务' : '新增预警任务'
})

// 初始化预警值类型数据
const initWarningValueTypes = () => {
  const defaultTypes: WarningValueType[] = [
    {
      id: 'type_1',
      name: '数值范围',
      indicators: [
        {
          id: 'indicator_1_1',
          name: '数值上限检测',
          thresholds: [
            { id: 'threshold_1_1_1', name: '警告阈值', range: '80-100' },
            { id: 'threshold_1_1_2', name: '严重阈值', range: '100-120' }
          ]
        }
      ],
      createTime: new Date().toLocaleString('zh-CN')
    },
    {
      id: 'type_2',
      name: '数据完整性',
      indicators: [
        {
          id: 'indicator_2_1',
          name: '数据缺失率',
          thresholds: [
            { id: 'threshold_2_1_1', name: '轻微缺失', range: '0-5' },
            { id: 'threshold_2_1_2', name: '严重缺失', range: '5-20' }
          ]
        }
      ],
      createTime: new Date().toLocaleString('zh-CN')
    },
    {
      id: 'type_3',
      name: '时间序列分析',
      indicators: [
        {
          id: 'indicator_3_1',
          name: '趋势偏差',
          thresholds: [
            { id: 'threshold_3_1_1', name: '轻微偏差', range: '0-10' },
            { id: 'threshold_3_1_2', name: '显著偏差', range: '10-30' }
          ]
        }
      ],
      createTime: new Date().toLocaleString('zh-CN')
    },
    {
      id: 'type_4',
      name: '数据分布',
      indicators: [
        {
          id: 'indicator_4_1',
          name: '分布偏度',
          thresholds: [
            { id: 'threshold_4_1_1', name: '正常范围', range: '-1-1' },
            { id: 'threshold_4_1_2', name: '异常范围', range: '1-3' }
          ]
        }
      ],
      createTime: new Date().toLocaleString('zh-CN')
    },
    {
      id: 'type_5',
      name: '异常值检测',
      indicators: [
        {
          id: 'indicator_5_1',
          name: '异常值比例',
          thresholds: [
            { id: 'threshold_5_1_1', name: '可接受范围', range: '0-2' },
            { id: 'threshold_5_1_2', name: '需关注范围', range: '2-10' }
          ]
        }
      ],
      createTime: new Date().toLocaleString('zh-CN')
    }
  ]

  const stored = localStorage.getItem('warningValueTypes')
  if (!stored) {
    localStorage.setItem('warningValueTypes', JSON.stringify(defaultTypes))
    warningValueTypes.value = defaultTypes
  } else {
    warningValueTypes.value = JSON.parse(stored)
  }
}

// 生成模拟数据
const generateMockData = (): WarningTask[] => {
  // 预警任务名称模板
  const taskNameTemplates = [
    '数据完整性监控', '异常值检测任务', '数值范围监控', '时间序列分析', '数据分布监控',
    '数据质量检查', '实时数据监控', '批量数据验证', '数据一致性检查', '数据准确性验证',
    '数据及时性监控', '数据可用性检查', '数据安全性监控', '数据合规性检查', '数据完备性验证',
    '业务数据监控', '系统数据检查', '用户数据验证', '交易数据监控', '日志数据分析',
    '性能数据监控', '错误数据检测', '重复数据检查', '缺失数据监控', '格式数据验证',
    '关联数据检查', '历史数据监控', '增量数据验证', '全量数据检查', '核心数据监控',
    '敏感数据检测', '关键数据验证', '基础数据监控', '元数据检查', '配置数据验证',
    '统计数据监控', '报表数据检查', '接口数据验证', '文件数据监控', '数据库数据检查',
    '缓存数据验证', '队列数据监控', '流数据检查', '批处理数据验证', '实时流数据监控',
    '数据同步检查', '数据备份验证', '数据恢复监控', '数据迁移检查', '数据清洗验证'
  ]

  // 从配置中获取预警值类型
  const warningValueTypeNames = warningValueTypes.value.map(type => type.name)

  // 预警升级条件模板
  const escalationConditions = [
    '连续3次检测失败', '异常值超过阈值10%', '超出预设范围', '趋势异常持续24小时', '分布偏差超过标准值',
    '数据缺失率超过5%', '响应时间超过3秒', '错误率超过1%', '数据延迟超过30分钟', '连续5次验证失败',
    '异常数据量超过阈值', '数据质量分数低于80分', '关键字段为空', '数据格式不符合规范', '业务规则验证失败',
    '数据一致性检查失败', '重复数据超过阈值', '数据更新频率异常', '数据源连接失败', '数据传输中断'
  ]

  // 通知渠道组合
  const notificationChannelOptions = [
    ['邮件'], ['短信'], ['系统通知'], ['微信'], ['钉钉'],
    ['邮件', '短信'], ['邮件', '系统通知'], ['邮件', '微信'], ['短信', '系统通知'],
    ['邮件', '短信', '系统通知'], ['邮件', '微信', '钉钉'], ['短信', '微信'], ['系统通知', '钉钉']
  ]

  // 通知频率
  const frequencies = ['实时', '每5分钟', '每15分钟', '每30分钟', '每小时', '每2小时', '每6小时', '每12小时', '每日', '每周']

  // 状态
  const statuses = ['无预警', '已预警', '预警已升级']

  // 生成50条数据
  const mockTasks: WarningTask[] = []

  for (let i = 0; i < 50; i++) {
    const randomTaskName = taskNameTemplates[Math.floor(Math.random() * taskNameTemplates.length)]
    const randomWarningValueType = warningValueTypeNames[Math.floor(Math.random() * warningValueTypeNames.length)]
    const randomEscalationCondition = escalationConditions[Math.floor(Math.random() * escalationConditions.length)]
    const randomNotificationChannels = notificationChannelOptions[Math.floor(Math.random() * notificationChannelOptions.length)]
    const randomFrequency = frequencies[Math.floor(Math.random() * frequencies.length)]
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)]
    const randomWarningNotification = Math.random() > 0.2 // 80%概率启用预警通知
    const randomHasDataAnomalyIssue = Math.random() > 0.6 // 40%概率有数据异常问题

    // 创建时间：过去30天内的随机时间
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
    // 更新时间：创建时间之后到现在的随机时间
    const updateTime = new Date(createTime.getTime() + Math.random() * (Date.now() - createTime.getTime()))

    mockTasks.push({
      id: `task_${Date.now()}_${i}_${Math.random().toString(36).substring(2, 11)}`,
      index: i + 1,
      taskName: `${randomTaskName}${i > 0 ? `_${String(i + 1).padStart(2, '0')}` : ''}`,
      warningValueType: randomWarningValueType,
      warningNotification: randomWarningNotification,
      escalationCondition: randomEscalationCondition,
      notificationChannels: randomWarningNotification ? randomNotificationChannels : [],
      notificationFrequency: randomWarningNotification ? randomFrequency : '',
      createTime: createTime.toLocaleString('zh-CN'),
      updateTime: updateTime.toLocaleString('zh-CN'),
      status: randomStatus,
      hasDataAnomalyIssue: randomHasDataAnomalyIssue,
      reportGenerated: randomStatus === '预警已升级' ? Math.random() > 0.5 : false, // 预警已升级状态50%概率已生成报告
      reportData: undefined
    })
  }

  return mockTasks
}

// 初始化数据
const initData = () => {
  // 初始化预警值类型配置
  initWarningValueTypes()

  // 清除旧数据，重新生成50条新数据
  localStorage.removeItem('warningAlertTasks')
  tableData.value = generateMockData()

  // 应用默认排序（创建时间倒序）
  applySorting()

  localStorage.setItem('warningAlertTasks', JSON.stringify(tableData.value))
  updatePagination()
}

// 更新分页信息
const updatePagination = () => {
  pagination.total = tableData.value.length
}

// 返回上一页
const handleGoBack = () => {
  router.push('/reportIntegrationSpotCheck')
}

// Block高度变化处理
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 120
}

// 搜索功能
const onSearch = async () => {
  searchLoading.value = true
  try {
    // 模拟搜索延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    let filteredData = [...tableData.value]
    
    if (searchForm.value.taskName) {
      filteredData = filteredData.filter(item => 
        item.taskName.toLowerCase().includes(searchForm.value.taskName.toLowerCase())
      )
    }
    
    if (searchForm.value.warningValueType) {
      filteredData = filteredData.filter(item => 
        item.warningValueType === searchForm.value.warningValueType
      )
    }
    
    // 更新表格数据（这里简化处理，实际应该更新分页数据）
    pagination.page = 1
    pagination.total = filteredData.length
    
    ElMessage.success(`搜索完成，找到 ${filteredData.length} 条记录`)
  } catch (error) {
    ElMessage.error('搜索失败')
  } finally {
    searchLoading.value = false
  }
}

// 重置搜索
const onReset = () => {
  searchForm.value = { taskName: '', warningValueType: '' }
  pagination.page = 1
  updatePagination()
}

// 获取当前页数据
const getCurrentPageData = () => {
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return tableData.value.slice(start, end)
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
}

const handlePageChange = (page: number) => {
  pagination.page = page
}

// 选择变化处理
const handleSelectionChange = (selection: WarningTask[]) => {
  selectedRows.value = selection
}

// 新增任务
const onClickAdd = () => {
  currentRow.value = null
  isViewMode.value = false
  dialogForm.value = {
    taskName: '',
    warningValueType: '',
    warningNotification: true,
    escalationCondition: '',
    notificationChannels: [] as string[],
    notificationFrequency: ''
  }
  showDialogForm.value = true
}

// 表格操作点击事件
const onTableClickButton = ({ row, btn }: any) => {
  switch (btn.code) {
    case 'view':
      onView(row)
      break
    case 'edit':
      onEdit(row)
      break
    case 'delete':
      onDelete(row)
      break
  }
}

// 查看任务
const onView = (row: WarningTask) => {
  currentRow.value = row
  isViewMode.value = true
  // 填充表单数据
  dialogForm.value = {
    taskName: row.taskName,
    warningValueType: row.warningValueType,
    warningNotification: row.warningNotification,
    escalationCondition: row.escalationCondition,
    notificationChannels: [...row.notificationChannels],
    notificationFrequency: row.notificationFrequency
  }
  showDialogForm.value = true
}

// 编辑任务
const onEdit = (row: WarningTask) => {
  currentRow.value = row
  isViewMode.value = false
  dialogForm.value = {
    taskName: row.taskName,
    warningValueType: row.warningValueType,
    warningNotification: row.warningNotification,
    escalationCondition: row.escalationCondition,
    notificationChannels: [...row.notificationChannels],
    notificationFrequency: row.notificationFrequency
  }
  showDialogForm.value = true
}

// 删除任务
const onDelete = (row: WarningTask) => {
  const index = tableData.value.findIndex(item => item.id === row.id)
  if (index !== -1) {
    tableData.value.splice(index, 1)

    // 应用当前排序
    applySorting()

    localStorage.setItem('warningAlertTasks', JSON.stringify(tableData.value))
    updatePagination()
    ElMessage.success('删除成功')
  }
}

// 生成升级原因分析报告
const generateReport = async (row: WarningTask) => {
  try {
    ElMessage.info(`正在为"${row.taskName}"生成升级原因分析报告...`)

    // 模拟生成报告数据
    await new Promise(resolve => setTimeout(resolve, 2000)) // 模拟生成时间

    const reportData = {
      taskId: row.id,
      taskName: row.taskName,
      reportTime: new Date().toLocaleString('zh-CN'),
      escalationReason: '连续检测到异常数据，触发预警升级条件',
      analysisDetails: [
        {
          timePoint: '2025-01-20 10:30:00',
          dataValue: '异常值：125.6',
          threshold: '正常范围：80-120',
          deviation: '偏差：+5.6',
          impact: '轻微影响'
        },
        {
          timePoint: '2025-01-20 11:00:00',
          dataValue: '异常值：132.8',
          threshold: '正常范围：80-120',
          deviation: '偏差：+12.8',
          impact: '中等影响'
        },
        {
          timePoint: '2025-01-20 11:30:00',
          dataValue: '异常值：145.2',
          threshold: '正常范围：80-120',
          deviation: '偏差：+25.2',
          impact: '严重影响'
        }
      ],
      recommendations: [
        '立即检查数据源连接状态',
        '验证数据采集设备是否正常工作',
        '联系相关技术人员进行系统诊断',
        '考虑临时调整预警阈值以减少误报'
      ],
      conclusion: '根据分析结果，建议立即采取相应措施以防止问题进一步恶化。'
    }

    // 更新任务状态
    const taskIndex = tableData.value.findIndex(item => item.id === row.id)
    if (taskIndex > -1) {
      tableData.value[taskIndex].reportGenerated = true
      tableData.value[taskIndex].reportData = reportData
      localStorage.setItem('warningAlertTasks', JSON.stringify(tableData.value))
    }

    ElMessage.success('升级原因分析报告生成成功！')
  } catch (error) {
    console.error('生成报告失败:', error)
    ElMessage.error('生成报告失败，请重试')
  }
}

// 导出升级原因分析报告为Excel
const exportReport = async (row: WarningTask) => {
  if (!row.reportGenerated || !row.reportData) {
    ElMessage.warning('请先生成报告')
    return
  }

  try {
    ElMessage.info('正在导出报告...')

    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    workbook.creator = 'Warning Alert System'
    workbook.lastModifiedBy = 'System'
    workbook.created = new Date()
    workbook.modified = new Date()

    // 创建工作表
    const worksheet = workbook.addWorksheet('升级原因分析报告')

    // 设置列宽
    worksheet.columns = [
      { width: 20 },
      { width: 30 },
      { width: 20 },
      { width: 15 },
      { width: 15 }
    ]

    // 添加标题
    worksheet.mergeCells('A1:E1')
    const titleCell = worksheet.getCell('A1')
    titleCell.value = '预警升级原因分析报告'
    titleCell.font = { size: 16, bold: true }
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' }
    titleCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    }

    // 添加基本信息
    worksheet.addRow([])
    worksheet.addRow(['任务名称:', row.reportData.taskName])
    worksheet.addRow(['报告生成时间:', row.reportData.reportTime])
    worksheet.addRow(['升级原因:', row.reportData.escalationReason])
    worksheet.addRow([])

    // 添加分析详情标题
    worksheet.addRow(['分析详情'])
    const detailHeaderRow = worksheet.addRow(['时间点', '数据值', '阈值范围', '偏差', '影响程度'])
    detailHeaderRow.font = { bold: true }
    detailHeaderRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'E7E6E6' }
    }

    // 添加分析详情数据
    row.reportData.analysisDetails.forEach((detail: any) => {
      worksheet.addRow([
        detail.timePoint,
        detail.dataValue,
        detail.threshold,
        detail.deviation,
        detail.impact
      ])
    })

    worksheet.addRow([])

    // 添加建议
    worksheet.addRow(['处理建议'])
    row.reportData.recommendations.forEach((rec: string, index: number) => {
      worksheet.addRow([`${index + 1}.`, rec])
    })

    worksheet.addRow([])
    worksheet.addRow(['结论:', row.reportData.conclusion])

    // 设置边框
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) { // 跳过标题行
        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          }
        })
      }
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 下载文件
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const fileName = `${row.taskName}_升级原因分析报告_${timestamp}.xlsx`
    saveAs(blob, fileName)

    // 创建成功的导出记录
    createExportRecord(row.taskName, 'success')

    ElMessage.success('报告导出成功')
  } catch (error) {
    console.error('导出失败:', error)

    // 创建失败的导出记录
    createExportRecord(row.taskName, 'failed')

    ElMessage.error('导出失败，请重试')
  }
}





// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '无预警':
      return 'success'
    case '已预警':
      return 'warning'
    case '预警已升级':
      return 'danger'
    default:
      return 'info'
  }
}

// 对话框确认
const onDialogConfirm = () => {
  dialogFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true

      // 模拟保存延迟，提供更好的用户反馈
      setTimeout(() => {
        try {
          if (currentRow.value) {
            // 编辑模式
            const index = tableData.value.findIndex(item => item.id === currentRow.value!.id)
            if (index !== -1) {
              tableData.value[index] = {
                ...tableData.value[index],
                taskName: dialogForm.value.taskName,
                warningValueType: dialogForm.value.warningValueType,
                warningNotification: dialogForm.value.warningNotification,
                escalationCondition: dialogForm.value.escalationCondition,
                notificationChannels: [...dialogForm.value.notificationChannels],
                notificationFrequency: dialogForm.value.notificationFrequency,
                updateTime: new Date().toLocaleString('zh-CN')
              }

              // 应用当前排序
              applySorting()

              ElMessage.success('预警任务编辑成功')
            }
          } else {
            // 新增模式
            const newTask: WarningTask = {
              id: `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
              index: tableData.value.length + 1,
              taskName: dialogForm.value.taskName,
              warningValueType: dialogForm.value.warningValueType,
              warningNotification: dialogForm.value.warningNotification,
              escalationCondition: dialogForm.value.escalationCondition,
              notificationChannels: [...dialogForm.value.notificationChannels],
              notificationFrequency: dialogForm.value.notificationFrequency,
              createTime: new Date().toLocaleString('zh-CN'),
              updateTime: new Date().toLocaleString('zh-CN'),
              status: '无预警',
              hasDataAnomalyIssue: false, // 新增任务默认无数据异常问题
              reportGenerated: false,
              reportData: undefined
            }
            tableData.value.push(newTask)
            ElMessage.success('预警任务创建成功')
          }

          // 应用当前排序
          applySorting()

          localStorage.setItem('warningAlertTasks', JSON.stringify(tableData.value))
          updatePagination()
          showDialogForm.value = false
        } catch (error) {
          console.error('保存失败:', error)
          ElMessage.error('保存失败，请重试')
        } finally {
          loading.value = false
        }
      }, 800) // 模拟保存延迟
    } else {
      ElMessage.warning('请检查表单填写是否正确')
    }
  })
}

// 监听预警通知状态变化，动态调整表单验证
const onWarningNotificationChange = (value: boolean) => {
  // 当关闭预警通知时，清空相关字段
  if (!value) {
    dialogForm.value.notificationChannels = []
    dialogForm.value.notificationFrequency = ''
  }
  // 触发相关字段的验证
  nextTick(() => {
    if (dialogFormRef.value) {
      dialogFormRef.value.validateField('notificationChannels')
      dialogFormRef.value.validateField('notificationFrequency')
    }
  })
}

// 预警值类型配置相关方法
const openWarningValueTypeConfig = () => {
  showWarningValueTypeDialog.value = true
}

// 数据异常问题相关方法
const openDataAnomalyDialog = () => {
  initDataAnomalyIssues()
  showDataAnomalyDialog.value = true
}

// 初始化数据异常问题数据
const initDataAnomalyIssues = () => {
  const storedData = localStorage.getItem('dataAnomalyIssues')
  if (storedData) {
    dataAnomalyIssues.value = JSON.parse(storedData)
  } else {
    // 生成模拟数据
    dataAnomalyIssues.value = generateMockDataAnomalyIssues()
    localStorage.setItem('dataAnomalyIssues', JSON.stringify(dataAnomalyIssues.value))
  }
  updateDataAnomalyPagination()
}

// 生成模拟数据异常问题数据
const generateMockDataAnomalyIssues = (): DataAnomalyIssue[] => {
  const mockSources = [
    '财务数据抽查任务_20241201',
    '人事数据抽查任务_20241202',
    '业务数据抽查任务_20241203',
    '系统数据抽查任务_20241204',
    '统计数据抽查任务_20241205',
    '档案数据抽查任务_20241206',
    '合同数据抽查任务_20241207',
    '项目数据抽查任务_20241208'
  ]

  const mockContents = [
    '数据格式不符合规范，缺少必要的格式验证',
    '必填字段缺失，影响数据完整性',
    '数据类型不匹配，存在类型转换错误',
    '数据重复，违反唯一性约束',
    '数据范围超出预期，存在异常值',
    '关联数据不一致，存在引用完整性问题',
    '时间戳格式错误，无法正确解析',
    '编码格式不统一，存在乱码问题',
    '数据长度超出限制，可能导致截断',
    '空值处理不当，影响业务逻辑'
  ]

  const issues: DataAnomalyIssue[] = []
  for (let i = 1; i <= 8; i++) {
    issues.push({
      id: `anomaly_${Date.now()}_${i}`,
      sequence: i,
      source: mockSources[i - 1],
      content: mockContents[Math.floor(Math.random() * mockContents.length)],
      createTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toLocaleString('zh-CN')
    })
  }

  return issues
}

// 更新数据异常问题分页信息
const updateDataAnomalyPagination = () => {
  dataAnomalyPagination.value.total = filteredDataAnomalyIssues.value.length
}

// 过滤数据异常问题
const filteredDataAnomalyIssues = computed(() => {
  if (!dataAnomalySearchText.value) {
    return dataAnomalyIssues.value
  }
  return dataAnomalyIssues.value.filter(issue =>
    issue.source.toLowerCase().includes(dataAnomalySearchText.value.toLowerCase()) ||
    issue.content.toLowerCase().includes(dataAnomalySearchText.value.toLowerCase())
  )
})

// 分页后的数据异常问题
const paginatedDataAnomalyIssues = computed(() => {
  const start = (dataAnomalyPagination.value.currentPage - 1) * dataAnomalyPagination.value.pageSize
  const end = start + dataAnomalyPagination.value.pageSize
  return filteredDataAnomalyIssues.value.slice(start, end)
})

// 数据异常问题分页变化
const onDataAnomalyPageChange = (page: number) => {
  dataAnomalyPagination.value.currentPage = page
}

// 数据异常问题页面大小变化
const onDataAnomalyPageSizeChange = (size: number) => {
  dataAnomalyPagination.value.pageSize = size
  dataAnomalyPagination.value.currentPage = 1
}

// 搜索数据异常问题
const searchDataAnomalyIssues = () => {
  dataAnomalyPagination.value.currentPage = 1
  updateDataAnomalyPagination()
}

// 处理数据异常问题 - 打开编辑对话框
const handleDataAnomalyProcess = (row: DataAnomalyIssue) => {
  currentEditDataAnomalyRow.value = row
  editDataAnomalyForm.value = {
    source: row.source,
    content: row.content
  }
  showEditDataAnomalyDialog.value = true
}

// 保存编辑的数据异常问题
const saveEditDataAnomaly = async () => {
  if (!editDataAnomalyFormRef.value) return

  try {
    // 验证表单
    await editDataAnomalyFormRef.value.validate()

    if (!currentEditDataAnomalyRow.value) return

    // 更新数据
    const index = dataAnomalyIssues.value.findIndex(item => item.id === currentEditDataAnomalyRow.value!.id)
    if (index !== -1) {
      dataAnomalyIssues.value[index] = {
        ...dataAnomalyIssues.value[index],
        source: editDataAnomalyForm.value.source,
        content: editDataAnomalyForm.value.content
      }

      // 保存到localStorage
      localStorage.setItem('dataAnomalyIssues', JSON.stringify(dataAnomalyIssues.value))

      // 更新分页信息
      updateDataAnomalyPagination()

      // 关闭对话框
      showEditDataAnomalyDialog.value = false

      // 显示成功提示
      ElMessage.success('数据异常问题修改成功')
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消编辑数据异常问题
const cancelEditDataAnomaly = () => {
  showEditDataAnomalyDialog.value = false
  currentEditDataAnomalyRow.value = null
  editDataAnomalyForm.value = {
    source: '',
    content: ''
  }
  // 清除表单验证状态
  if (editDataAnomalyFormRef.value) {
    editDataAnomalyFormRef.value.clearValidate()
  }
}

// 预警升级原因分析报告导出记录相关方法
const openExportRecordDialog = () => {
  initExportRecords()
  showExportRecordDialog.value = true
}

// 初始化导出记录数据
const initExportRecords = () => {
  const storedData = localStorage.getItem('exportRecords')
  if (storedData) {
    exportRecords.value = JSON.parse(storedData)
  } else {
    // 生成模拟数据
    exportRecords.value = generateMockExportRecords()
    localStorage.setItem('exportRecords', JSON.stringify(exportRecords.value))
  }
  updateExportRecordPagination()
}

// 生成模拟导出记录数据
const generateMockExportRecords = (): ExportRecord[] => {
  const mockOperators = [
    '张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十',
    '郑十一', '王十二', '冯十三', '陈十四', '褚十五'
  ]

  const mockTaskNames = [
    '数据异常预警任务',
    '系统性能预警任务',
    '网络连接预警任务',
    '数据库预警任务',
    '服务器预警任务',
    '应用程序预警任务',
    '安全预警任务',
    '用户访问预警任务'
  ]

  const records: ExportRecord[] = []
  const recordCount = 12 // 生成12条记录

  for (let i = 1; i <= recordCount; i++) {
    // 导出时间：最近30天内的随机时间
    const exportTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)

    // 随机选择操作人员和任务名称
    const operator = mockOperators[Math.floor(Math.random() * mockOperators.length)]
    const taskName = mockTaskNames[Math.floor(Math.random() * mockTaskNames.length)]
    const reportType = `${taskName} - 升级原因分析报告`

    // 导出状态：80%成功，20%失败
    const exportStatus: 'success' | 'failed' | 'in-progress' = Math.random() > 0.2 ? 'success' : 'failed'

    // 文件大小：成功时1-50MB，失败时0MB
    const fileSize = exportStatus === 'success'
      ? `${(Math.random() * 50 + 1).toFixed(2)}MB`
      : '0MB'

    records.push({
      id: `export_${Date.now()}_${i}`,
      sequence: i,
      exportTime: exportTime.toLocaleString('zh-CN'),
      operator,
      reportType,
      exportStatus,
      fileSize,
      createTime: exportTime.toLocaleString('zh-CN')
    })
  }

  // 按导出时间倒序排列
  return records.sort((a, b) => new Date(b.exportTime).getTime() - new Date(a.exportTime).getTime())
}

// 更新导出记录分页信息
const updateExportRecordPagination = () => {
  exportRecordPagination.value.total = filteredExportRecords.value.length
}

// 过滤导出记录
const filteredExportRecords = computed(() => {
  if (!exportRecordSearchText.value) {
    return exportRecords.value
  }
  return exportRecords.value.filter(record =>
    record.operator.toLowerCase().includes(exportRecordSearchText.value.toLowerCase()) ||
    record.reportType.toLowerCase().includes(exportRecordSearchText.value.toLowerCase())
  )
})

// 分页后的导出记录
const paginatedExportRecords = computed(() => {
  const start = (exportRecordPagination.value.currentPage - 1) * exportRecordPagination.value.pageSize
  const end = start + exportRecordPagination.value.pageSize
  return filteredExportRecords.value.slice(start, end)
})

// 导出记录分页变化
const onExportRecordPageChange = (page: number) => {
  exportRecordPagination.value.currentPage = page
}

// 导出记录页面大小变化
const onExportRecordPageSizeChange = (size: number) => {
  exportRecordPagination.value.pageSize = size
  exportRecordPagination.value.currentPage = 1
}

// 搜索导出记录
const searchExportRecords = () => {
  exportRecordPagination.value.currentPage = 1
  updateExportRecordPagination()
}

// 获取导出状态标签类型
const getExportStatusTagType = (status: string) => {
  switch (status) {
    case 'success':
      return 'success'
    case 'failed':
      return 'danger'
    case 'in-progress':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取导出状态文本
const getExportStatusText = (status: string) => {
  switch (status) {
    case 'success':
      return '成功'
    case 'failed':
      return '失败'
    case 'in-progress':
      return '进行中'
    default:
      return '未知'
  }
}

// 创建导出记录（只记录导出操作）
const createExportRecord = (taskName: string, status: 'success' | 'failed' | 'in-progress' = 'success') => {
  // 确保导出记录数据已初始化
  if (exportRecords.value.length === 0) {
    initExportRecords()
  }

  const now = new Date()

  const newRecord: ExportRecord = {
    id: `export_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    sequence: exportRecords.value.length + 1,
    exportTime: now.toLocaleString('zh-CN'),
    operator: getCurrentOperatorName(), // 使用当前用户名称
    reportType: `${taskName} - 升级原因分析报告`,
    exportStatus: status,
    fileSize: status === 'success' ? `${(Math.random() * 10 + 5).toFixed(2)}MB` : '0MB', // 失败时文件大小为0
    createTime: now.toLocaleString('zh-CN')
  }

  // 添加到记录列表开头（最新的在前面）
  exportRecords.value.unshift(newRecord)

  // 重新分配序号
  exportRecords.value.forEach((record, index) => {
    record.sequence = index + 1
  })

  // 保存到localStorage
  localStorage.setItem('exportRecords', JSON.stringify(exportRecords.value))

  // 更新分页信息
  updateExportRecordPagination()
}

const filteredWarningValueTypes = computed(() => {
  if (!warningValueTypeSearchText.value) {
    return warningValueTypes.value
  }
  return warningValueTypes.value.filter(type =>
    type.name.toLowerCase().includes(warningValueTypeSearchText.value.toLowerCase())
  )
})

// 过滤预警指标
const filteredIndicators = computed(() => {
  if (!indicatorSearchKeyword.value) {
    return warningValueTypeForm.value.indicators
  }
  return warningValueTypeForm.value.indicators.filter(indicator =>
    indicator.name.toLowerCase().includes(indicatorSearchKeyword.value.toLowerCase())
  )
})

// 搜索预警指标
const searchIndicators = () => {
  indicatorSearchKeyword.value = indicatorSearchText.value
}

// 重置预警指标搜索
const resetIndicatorSearch = () => {
  indicatorSearchText.value = ''
  indicatorSearchKeyword.value = ''
}

const onAddWarningValueType = () => {
  currentWarningValueType.value = null
  isWarningValueTypeViewMode.value = false
  warningValueTypeForm.value = {
    name: '',
    indicators: []
  }
  showWarningValueTypeFormDialog.value = true
}

const onEditWarningValueType = (row: WarningValueType) => {
  currentWarningValueType.value = row
  isWarningValueTypeViewMode.value = false
  warningValueTypeForm.value = {
    name: row.name,
    indicators: JSON.parse(JSON.stringify(row.indicators)) // 深拷贝
  }
  showWarningValueTypeFormDialog.value = true
}

const onDeleteWarningValueType = (row: WarningValueType) => {
  ElMessageBox.confirm(
    `确认删除预警值类型"${row.name}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger'
    }
  ).then(() => {
    const index = warningValueTypes.value.findIndex(type => type.id === row.id)
    if (index !== -1) {
      warningValueTypes.value.splice(index, 1)
      localStorage.setItem('warningValueTypes', JSON.stringify(warningValueTypes.value))
      ElMessage.success('预警值类型删除成功')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 预警值类型表单验证规则
const warningValueTypeFormRules = {
  name: [
    { required: true, message: '请输入预警值类型名称', trigger: 'blur' },
    { min: 1, max: 50, message: '预警值类型名称长度应在1-50个字符之间', trigger: 'blur' },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (value) {
          // 检查是否与现有类型名称重复（编辑时排除自己）
          const existingType = warningValueTypes.value.find(type =>
            type.name === value && type.id !== currentWarningValueType.value?.id
          )
          if (existingType) {
            callback(new Error('预警值类型名称已存在，请使用其他名称'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 预警值类型表单标题
const warningValueTypeFormTitle = computed(() => {
  if (isWarningValueTypeViewMode.value) {
    return '查看预警值类型'
  }
  return currentWarningValueType.value ? '编辑预警值类型' : '新增预警值类型'
})

// 预警值类型表单确认
const onWarningValueTypeFormConfirm = () => {
  warningValueTypeFormDialogRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true

      setTimeout(() => {
        try {
          if (currentWarningValueType.value) {
            // 编辑模式
            const index = warningValueTypes.value.findIndex(type => type.id === currentWarningValueType.value!.id)
            if (index !== -1) {
              warningValueTypes.value[index] = {
                ...warningValueTypes.value[index],
                name: warningValueTypeForm.value.name,
                indicators: JSON.parse(JSON.stringify(warningValueTypeForm.value.indicators))
              }
              ElMessage.success('预警值类型编辑成功')
            }
          } else {
            // 新增模式
            const newType: WarningValueType = {
              id: `type_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
              name: warningValueTypeForm.value.name,
              indicators: JSON.parse(JSON.stringify(warningValueTypeForm.value.indicators)),
              createTime: new Date().toLocaleString('zh-CN')
            }
            warningValueTypes.value.push(newType)
            ElMessage.success('预警值类型创建成功')
          }

          localStorage.setItem('warningValueTypes', JSON.stringify(warningValueTypes.value))
          showWarningValueTypeFormDialog.value = false
        } catch (error) {
          console.error('保存失败:', error)
          ElMessage.error('保存失败，请重试')
        } finally {
          loading.value = false
        }
      }, 800)
    } else {
      ElMessage.warning('请检查表单填写是否正确')
    }
  })
}

// 预警指标管理方法
const addWarningIndicator = () => {
  const newIndicator: WarningIndicator = {
    id: `indicator_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    name: '',
    thresholds: []
  }
  warningValueTypeForm.value.indicators.push(newIndicator)
}

const removeWarningIndicator = (indicatorId: string) => {
  const index = warningValueTypeForm.value.indicators.findIndex(indicator => indicator.id === indicatorId)
  if (index !== -1) {
    warningValueTypeForm.value.indicators.splice(index, 1)
  }
}

const addWarningThreshold = (indicatorId: string) => {
  const indicatorIndex = warningValueTypeForm.value.indicators.findIndex(indicator => indicator.id === indicatorId)
  if (indicatorIndex !== -1) {
    const newThreshold: WarningThreshold = {
      id: `threshold_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      name: '',
      range: ''
    }
    warningValueTypeForm.value.indicators[indicatorIndex].thresholds.push(newThreshold)
  }
}

const removeWarningThreshold = (indicatorId: string, thresholdId: string) => {
  const indicatorIndex = warningValueTypeForm.value.indicators.findIndex(indicator => indicator.id === indicatorId)
  if (indicatorIndex !== -1) {
    const thresholdIndex = warningValueTypeForm.value.indicators[indicatorIndex].thresholds.findIndex(threshold => threshold.id === thresholdId)
    if (thresholdIndex !== -1) {
      warningValueTypeForm.value.indicators[indicatorIndex].thresholds.splice(thresholdIndex, 1)
    }
  }
}

// 初始化
onMounted(() => {
  initData()
})
</script>

<route>
{
  meta: {
    title: '异常数据预警',
    ignoreLabel: false
  }
}
</route>

<template>
  <div class="warning-alert">
    <Block
      title="异常数据预警"
      :enable-expand-content="true"
      :enableBackButton="false"
      :enable-fixed-height="true"
      :enable-close-button="false"
      @content-expand="() => {}"
      @height-changed="onBlockHeightChanged"
    >
      <template #topRight>
        <el-button 
          size="small" 
          type="default" 
          @click="handleGoBack"
          style="margin-right: 8px"
        >
          <el-icon style="margin-right: 4px">
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>
        
        <el-button size="small" type="primary" @click="onClickAdd">
          新增预警任务
        </el-button>

        <el-button size="small" type="primary" @click="openWarningValueTypeConfig">
          预警值类型配置
        </el-button>

        <el-button size="small" type="primary" @click="openDataAnomalyDialog">
          数据异常问题
        </el-button>

        <el-button size="small" type="primary" @click="openExportRecordDialog">
          预警升级原因分析报告导出记录
        </el-button>
      </template>

      <template #expand>
        <!-- 搜索区域 -->
        <div class="search" v-loading="searchLoading" element-loading-background="rgba(255, 255, 255, 0.8)">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="3"
            :label-width="100"
            :enable-reset="false"
            confirm-text="查询"
            button-vertical="flowing"
            @submit="onSearch"
            @reset="onReset"
          />
        </div>
      </template>

      <!-- 表格列表 -->
      <TableV2
        ref="tableRef"
        :columns="columns"
        :defaultTableData="getCurrentPageData()"
        :enable-toolbar="false"
        :enable-own-button="false"
        :height="tableHeight"
        :buttons="buttons"
        :loading="loading"
        :default-sort="{ prop: 'createTime', order: 'descending' }"
        @click-button="onTableClickButton"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >


        <!-- 预警通知列 -->
        <template #warningNotification="{ row }">
          <el-tag :type="row.warningNotification ? 'success' : 'info'" size="small">
            {{ row.warningNotification ? '是' : '否' }}
          </el-tag>
        </template>

        <!-- 通知渠道列 -->
        <template #notificationChannels="{ row }">
          <div class="notification-channels">
            <el-tag 
              v-for="channel in row.notificationChannels" 
              :key="channel" 
              size="small" 
              style="margin-right: 4px; margin-bottom: 2px;"
            >
              {{ channel }}
            </el-tag>
          </div>
        </template>

        <!-- 状态列 -->
        <template #status="{ row }">
          <el-tag
            :type="getStatusType(row.status)"
            size="small"
          >
            {{ row.status }}
          </el-tag>
        </template>

        <!-- 是否有数据异常问题列 -->
        <template #hasDataAnomalyIssue="{ row }">
          <el-tag
            :type="row.hasDataAnomalyIssue ? 'danger' : 'success'"
            size="small"
          >
            {{ row.hasDataAnomalyIssue ? '是' : '否' }}
          </el-tag>
        </template>

        <!-- 生成升级原因分析报告列 -->
        <template #reportAction="{ row }">
          <div v-if="row.status === '预警已升级'">
            <el-button
              v-if="!row.reportGenerated"
              type="primary"
              size="small"
              @click="generateReport(row)"
              :loading="loading"
            >
              生成
            </el-button>
            <el-button
              v-else
              type="success"
              size="small"
              @click="exportReport(row)"
            >
              导出
            </el-button>
          </div>
          <span v-else class="text-gray-400">-</span>
        </template>
      </TableV2>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </Block>

    <!-- 创建/编辑对话框 -->
    <Dialog
      v-model="showDialogForm"
      :title="dialogTitle"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      width="600px"
      :enable-confirm="!isViewMode"
      @closed="currentRow = null; isViewMode = false; dialogForm = { taskName: '', warningValueType: '', warningNotification: true, escalationCondition: '', notificationChannels: [] as string[], notificationFrequency: '' }"
      @click-confirm="onDialogConfirm"
    >
      <Form
        ref="dialogFormRef"
        v-model="dialogForm"
        :props="dialogFormProps"
        :rules="dialogFormRules"
        :enable-button="false"
        :column-count="1"
        :label-width="120"
      />
    </Dialog>

    <!-- 预警值类型配置对话框 -->
    <Dialog
      v-model="showWarningValueTypeDialog"
      title="预警值类型配置"
      :destroy-on-close="true"
      width="700px"
      :enable-confirm="false"
    >
      <div class="warning-value-type-config">
        <!-- 搜索和新增区域 -->
        <div class="config-header">
          <el-input
            v-model="warningValueTypeSearchText"
            placeholder="请输入预警值类型名称搜索"
            style="width: 300px; margin-right: 16px;"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button type="primary" @click="onAddWarningValueType">
            新增预警值类型
          </el-button>
        </div>

        <!-- 预警值类型列表表格 -->
        <el-table
          :data="filteredWarningValueTypes"
          style="width: 100%; margin-top: 16px;"
          height="400px"
        >
          <el-table-column prop="name" label="预警值类型名称" min-width="200" />
          <el-table-column prop="createTime" label="创建时间" min-width="150" />
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="onEditWarningValueType(row)"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="onDeleteWarningValueType(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </Dialog>

    <!-- 预警值类型表单对话框 -->
    <Dialog
      v-model="showWarningValueTypeFormDialog"
      :title="warningValueTypeFormTitle"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      width="800px"
      :enable-confirm="!isWarningValueTypeViewMode"
      @closed="currentWarningValueType = null; isWarningValueTypeViewMode = false; warningValueTypeForm = { name: '', indicators: [] }"
      @click-confirm="onWarningValueTypeFormConfirm"
    >
      <div class="warning-value-type-form">
        <!-- 基本信息 -->
        <el-form
          ref="warningValueTypeFormDialogRef"
          :model="warningValueTypeForm"
          :rules="warningValueTypeFormRules"
          label-width="120px"
        >
          <el-form-item label="预警值类型名称" prop="name">
            <el-input
              v-model="warningValueTypeForm.name"
              placeholder="请输入预警值类型名称"
              :disabled="isWarningValueTypeViewMode"
            />
          </el-form-item>
        </el-form>

        <!-- 预警指标配置 -->
        <div class="indicators-section">
          <div class="section-header">
            <h4>预警指标配置</h4>
            <el-button
              v-if="!isWarningValueTypeViewMode"
              type="primary"
              size="small"
              @click="addWarningIndicator"
            >
              添加预警指标
            </el-button>
          </div>

          <!-- 预警指标搜索框 -->
          <div class="indicator-search">
            <el-input
              v-model="indicatorSearchText"
              placeholder="请输入预警指标名称搜索"
              style="width: 300px; margin-right: 12px;"
              clearable
              @keyup.enter="searchIndicators"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button type="primary" @click="searchIndicators">
              搜索
            </el-button>
            <el-button @click="resetIndicatorSearch">
              重置
            </el-button>
          </div>

          <!-- 预警指标列表 -->
          <el-collapse v-model="activeIndicators" class="indicators-collapse">
            <el-collapse-item
              v-for="(indicator, indicatorIndex) in filteredIndicators"
              :key="indicator.id"
              :name="indicator.id"
              class="indicator-item"
            >
              <template #title>
                <div class="indicator-title">
                  <span>{{ indicator.name || `预警指标 ${indicatorIndex + 1}` }}</span>
                  <el-button
                    v-if="!isWarningValueTypeViewMode"
                    type="danger"
                    size="small"
                    @click.stop="removeWarningIndicator(indicator.id)"
                    style="margin-left: auto; margin-right: 16px;"
                  >
                    删除指标
                  </el-button>
                </div>
              </template>

              <div class="indicator-content">
                <!-- 预警指标名称 -->
                <el-form-item label="预警指标名称" style="margin-bottom: 16px;">
                  <el-input
                    v-model="indicator.name"
                    placeholder="请输入预警指标名称"
                    :disabled="isWarningValueTypeViewMode"
                  />
                </el-form-item>

                <!-- 预警指标阈值 -->
                <div class="thresholds-section">
                  <div class="thresholds-header">
                    <h5>预警指标阈值</h5>
                    <el-button
                      v-if="!isWarningValueTypeViewMode"
                      type="primary"
                      size="small"
                      @click="addWarningThreshold(indicator.id)"
                    >
                      添加阈值
                    </el-button>
                  </div>

                  <div class="thresholds-list">
                    <div
                      v-for="(threshold, thresholdIndex) in indicator.thresholds"
                      :key="threshold.id"
                      class="threshold-item"
                    >
                      <el-form-item label="阈值名称" class="threshold-field">
                        <el-input
                          v-model="threshold.name"
                          placeholder="请输入阈值名称"
                          :disabled="isWarningValueTypeViewMode"
                        />
                      </el-form-item>
                      <el-form-item label="阈值范围" class="threshold-field">
                        <el-input
                          v-model="threshold.range"
                          placeholder="请输入阈值范围，如：80-120"
                          :disabled="isWarningValueTypeViewMode"
                        />
                      </el-form-item>
                      <el-button
                        v-if="!isWarningValueTypeViewMode"
                        type="danger"
                        size="small"
                        @click="removeWarningThreshold(indicator.id, threshold.id)"
                        class="threshold-remove-btn"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </Dialog>

    <!-- 数据异常问题对话框 -->
    <Dialog
      v-model="showDataAnomalyDialog"
      title="数据异常问题管理"
      :destroy-on-close="true"
      width="800px"
      :enable-confirm="false"
    >
      <div class="data-anomaly-config">
        <!-- 搜索区域 -->
        <div class="config-header">
          <el-input
            v-model="dataAnomalySearchText"
            placeholder="请输入抽查任务名称搜索"
            style="width: 300px; margin-right: 16px;"
            clearable
            @input="searchDataAnomalyIssues"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 数据异常问题列表表格 -->
        <el-table
          :data="paginatedDataAnomalyIssues"
          style="width: 100%; margin-top: 16px;"
          height="400px"
          v-loading="loading"
          stripe
          border
        >
          <el-table-column prop="sequence" label="序号" width="80" align="center" />
          <el-table-column prop="source" label="数据异常来源" min-width="200" show-overflow-tooltip />
          <el-table-column prop="content" label="数据异常内容" min-width="300" show-overflow-tooltip />
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="handleDataAnomalyProcess(row)"
              >
                处理
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页器 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="dataAnomalyPagination.currentPage"
            v-model:page-size="dataAnomalyPagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="dataAnomalyPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="onDataAnomalyPageSizeChange"
            @current-change="onDataAnomalyPageChange"
          />
        </div>
      </div>
    </Dialog>

    <!-- 编辑数据异常问题对话框 -->
    <Dialog
      v-model="showEditDataAnomalyDialog"
      title="处理数据异常问题"
      :destroy-on-close="true"
      width="600px"
      :enable-confirm="true"
      :loading="loading"
      loading-text="保存中"
      @closed="cancelEditDataAnomaly"
      @click-confirm="saveEditDataAnomaly"
    >
      <el-form
        ref="editDataAnomalyFormRef"
        :model="editDataAnomalyForm"
        :rules="editDataAnomalyFormRules"
        label-width="120px"
        class="edit-data-anomaly-form"
      >
        <el-form-item label="数据异常来源" prop="source">
          <el-input
            v-model="editDataAnomalyForm.source"
            placeholder="请输入数据异常来源"
            maxlength="100"
            show-word-limit
            clearable
          />
        </el-form-item>

        <el-form-item label="数据异常内容" prop="content">
          <el-input
            v-model="editDataAnomalyForm.content"
            type="textarea"
            placeholder="请输入数据异常内容"
            :rows="6"
            maxlength="500"
            show-word-limit
            resize="none"
          />
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 预警升级原因分析报告导出记录对话框 -->
    <Dialog
      v-model="showExportRecordDialog"
      title="预警升级原因分析报告导出记录"
      :destroy-on-close="true"
      width="800px"
      :enable-confirm="false"
    >
      <div class="export-record-config">
        <!-- 搜索区域 -->
        <div class="config-header">
          <el-input
            v-model="exportRecordSearchText"
            placeholder="请输入操作人员姓名搜索"
            style="width: 300px; margin-right: 16px;"
            clearable
            @input="searchExportRecords"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button type="primary" @click="searchExportRecords">
            查询
          </el-button>
        </div>

        <!-- 导出记录列表表格 -->
        <el-table
          :data="paginatedExportRecords"
          style="width: 100%; margin-top: 16px;"
          height="400px"
          v-loading="loading"
          stripe
          border
          :default-sort="{ prop: 'exportTime', order: 'descending' }"
        >
          <el-table-column prop="sequence" label="序号" width="80" align="center" />
          <el-table-column prop="exportTime" label="导出时间" min-width="150" sortable show-overflow-tooltip />
          <el-table-column prop="operator" label="操作人员" min-width="120" show-overflow-tooltip />
          <el-table-column prop="reportType" label="报告类型" min-width="200" show-overflow-tooltip />
          <el-table-column prop="exportStatus" label="导出状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag
                :type="getExportStatusTagType(row.exportStatus)"
                size="small"
              >
                {{ getExportStatusText(row.exportStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="fileSize" label="文件大小" width="100" align="center" />
        </el-table>

        <!-- 分页器 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="exportRecordPagination.currentPage"
            v-model:page-size="exportRecordPagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="exportRecordPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="onExportRecordPageSizeChange"
            @current-change="onExportRecordPageChange"
          />
        </div>
      </div>
    </Dialog>
  </div>
</template>

<style lang="scss" scoped>
.warning-alert {
  height: 100%;

  .notification-channels {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    max-width: 200px;

    .el-tag {
      font-size: 12px;
      height: 20px;
      line-height: 18px;
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    padding: 20px 0;
    border-top: 1px solid #ebeef5;
    margin-top: 16px;
  }

  // 搜索区域样式
  .search {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 16px;
  }

  // 表格响应式优化
  :deep(.el-table) {
    .el-table__cell {
      padding: 8px 0;
    }

    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #fafafa;
          color: #606266;
          font-weight: 600;
        }
      }
    }
  }

  // 状态标签样式
  .el-tag {
    border-radius: 4px;

    &.el-tag--success {
      background-color: #f0f9ff;
      border-color: #67c23a;
      color: #67c23a;
    }

    &.el-tag--warning {
      background-color: #fdf6ec;
      border-color: #e6a23c;
      color: #e6a23c;
    }

    &.el-tag--danger {
      background-color: #fef0f0;
      border-color: #f56c6c;
      color: #f56c6c;
    }

    &.el-tag--info {
      background-color: #f4f4f5;
      border-color: #909399;
      color: #909399;
    }
  }

  // 操作按钮区域
  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;

    .el-button {
      padding: 4px 8px;
      font-size: 12px;

      &.el-button--small {
        height: 28px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .notification-channels {
      max-width: 120px;
    }

    :deep(.el-table) {
      font-size: 12px;

      .el-table__cell {
        padding: 6px 0;
      }
    }

    .action-buttons {
      flex-direction: column;
      gap: 4px;

      .el-button {
        width: 100%;
        margin: 0;
      }
    }
  }
}

// 对话框内容样式
:deep(.el-dialog__body) {
  padding: 20px;

  .el-form {
    .el-form-item {
      margin-bottom: 20px;

      .el-form-item__label {
        font-weight: 500;
        color: #606266;
      }

      .el-form-item__content {
        .el-input,
        .el-select,
        .el-textarea {
          width: 100%;
        }

        .el-checkbox-group,
        .el-radio-group {
          width: 100%;

          .el-checkbox,
          .el-radio {
            margin-right: 16px;
            margin-bottom: 8px;
          }
        }
      }
    }
  }
}

// 预警值类型配置对话框样式
.warning-value-type-config {
  .config-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .el-table {
    .el-button {
      margin-right: 8px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

// 数据异常问题配置对话框样式
.data-anomaly-config {
  .config-header {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 16px;
  }

  .el-table {
    .el-button {
      margin-right: 8px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
  }
}

// 编辑数据异常问题表单样式
.edit-data-anomaly-form {
  .el-form-item {
    margin-bottom: 24px;

    .el-form-item__label {
      font-weight: 600;
      color: #303133;
    }

    .el-input__inner,
    .el-textarea__inner {
      border-radius: 4px;
      transition: border-color 0.2s ease-in-out;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }

    .el-textarea {
      .el-textarea__inner {
        font-family: inherit;
        line-height: 1.5;
      }
    }

    .el-input__count {
      color: #909399;
      font-size: 12px;
    }
  }
}

// 导出记录配置对话框样式
.export-record-config {
  .config-header {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 16px;
  }

  .el-table {
    .el-button {
      margin-right: 8px;

      &:last-child {
        margin-right: 0;
      }
    }

    // 状态标签样式优化
    .el-tag {
      border-radius: 4px;
      font-weight: 500;

      &.el-tag--success {
        background-color: #f0f9ff;
        border-color: #67c23a;
        color: #67c23a;
      }

      &.el-tag--danger {
        background-color: #fef0f0;
        border-color: #f56c6c;
        color: #f56c6c;
      }

      &.el-tag--warning {
        background-color: #fdf6ec;
        border-color: #e6a23c;
        color: #e6a23c;
      }
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
  }
}

// 预警值类型表单对话框样式
.warning-value-type-form {
  .indicators-section {
    margin-top: 24px;

    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .indicator-search {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px;
      background: #f5f7fa;
      border-radius: 4px;
      border: 1px solid #e4e7ed;

      .el-button {
        margin-left: 8px;
      }
    }

    .indicators-collapse {
      .indicator-item {
        margin-bottom: 16px;

        .indicator-title {
          display: flex;
          align-items: center;
          width: 100%;
          font-weight: 500;
        }

        .indicator-content {
          padding: 16px;
          background: #f8f9fa;
          border-radius: 4px;

          .thresholds-section {
            .thresholds-header {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 12px;

              h5 {
                margin: 0;
                font-size: 14px;
                font-weight: 500;
                color: #606266;
              }
            }

            .thresholds-list {
              .threshold-item {
                display: flex;
                align-items: flex-start;
                gap: 16px;
                margin-bottom: 12px;
                padding: 12px;
                background: #ffffff;
                border-radius: 4px;
                border: 1px solid #e4e7ed;

                .threshold-field {
                  flex: 1;
                  margin-bottom: 0;
                }

                .threshold-remove-btn {
                  margin-top: 32px; // 对齐到输入框位置
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
