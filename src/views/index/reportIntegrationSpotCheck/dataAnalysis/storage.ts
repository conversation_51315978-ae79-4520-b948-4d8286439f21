/**
 * 数据分析管理本地存储工具类
 */

import type { 
  DataAnalysisItem, 
  HistoryRecord, 
  AlgorithmConfig 
} from './types'
import { STORAGE_KEYS } from './types'
import { mockData, generateHistoryRecords } from './mockData'

/**
 * 通用本地存储工具类
 */
class LocalStorageManager<T> {
  private key: string

  constructor(key: string) {
    this.key = key
  }

  /**
   * 获取所有数据
   */
  getAll(): T[] {
    try {
      const data = localStorage.getItem(this.key)
      return data ? JSON.parse(data) : []
    } catch (error) {
      console.error(`获取${this.key}数据失败:`, error)
      return []
    }
  }

  /**
   * 保存所有数据
   */
  saveAll(data: T[]): boolean {
    try {
      localStorage.setItem(this.key, JSON.stringify(data))
      return true
    } catch (error) {
      console.error(`保存${this.key}数据失败:`, error)
      return false
    }
  }

  /**
   * 根据ID获取单条数据
   */
  getById(id: string): T | undefined {
    const data = this.getAll()
    return data.find((item: any) => item.id === id)
  }

  /**
   * 添加数据
   */
  add(item: T): boolean {
    try {
      const data = this.getAll()
      data.unshift(item) // 添加到开头
      return this.saveAll(data)
    } catch (error) {
      console.error(`添加${this.key}数据失败:`, error)
      return false
    }
  }

  /**
   * 更新数据
   */
  update(id: string, updates: Partial<T>): boolean {
    try {
      const data = this.getAll()
      const index = data.findIndex((item: any) => item.id === id)
      
      if (index === -1) {
        console.warn(`未找到ID为${id}的数据`)
        return false
      }
      
      data[index] = { ...data[index], ...updates }
      return this.saveAll(data)
    } catch (error) {
      console.error(`更新${this.key}数据失败:`, error)
      return false
    }
  }

  /**
   * 删除数据
   */
  delete(id: string): boolean {
    try {
      const data = this.getAll()
      const filteredData = data.filter((item: any) => item.id !== id)
      
      if (filteredData.length === data.length) {
        console.warn(`未找到ID为${id}的数据`)
        return false
      }
      
      return this.saveAll(filteredData)
    } catch (error) {
      console.error(`删除${this.key}数据失败:`, error)
      return false
    }
  }

  /**
   * 批量删除数据
   */
  batchDelete(ids: string[]): boolean {
    try {
      const data = this.getAll()
      const filteredData = data.filter((item: any) => !ids.includes(item.id))
      return this.saveAll(filteredData)
    } catch (error) {
      console.error(`批量删除${this.key}数据失败:`, error)
      return false
    }
  }

  /**
   * 清空所有数据
   */
  clear(): boolean {
    try {
      localStorage.removeItem(this.key)
      return true
    } catch (error) {
      console.error(`清空${this.key}数据失败:`, error)
      return false
    }
  }

  /**
   * 获取数据总数
   */
  count(): number {
    return this.getAll().length
  }

  /**
   * 检查数据是否存在
   */
  exists(id: string): boolean {
    return this.getAll().some((item: any) => item.id === id)
  }
}

/**
 * 数据分析项存储管理器
 */
export class DataAnalysisStorage extends LocalStorageManager<DataAnalysisItem> {
  constructor() {
    super(STORAGE_KEYS.DATA_ANALYSIS_LIST)
  }

  /**
   * 初始化数据（如果本地没有数据则使用模拟数据）
   */
  initializeData(): void {
    if (this.count() === 0) {
      console.log('初始化数据分析项数据...')
      this.saveAll(mockData.dataAnalysisItems)
    }
  }

  /**
   * 根据状态筛选数据
   */
  getByStatus(status: string): DataAnalysisItem[] {
    return this.getAll().filter(item => item.status === status)
  }

  /**
   * 根据类型筛选数据
   */
  getByType(type: string): DataAnalysisItem[] {
    return this.getAll().filter(item => item.type === type)
  }

  /**
   * 根据创建人筛选数据
   */
  getByCreator(creator: string): DataAnalysisItem[] {
    return this.getAll().filter(item => item.creator === creator)
  }

  /**
   * 搜索数据（支持名称模糊搜索）
   */
  search(keyword: string): DataAnalysisItem[] {
    if (!keyword.trim()) return this.getAll()
    
    const lowerKeyword = keyword.toLowerCase()
    return this.getAll().filter(item => 
      item.name.toLowerCase().includes(lowerKeyword) ||
      item.description?.toLowerCase().includes(lowerKeyword) ||
      item.creator.toLowerCase().includes(lowerKeyword)
    )
  }

  /**
   * 分页获取数据
   */
  getPage(page: number, size: number, filters?: any): {
    data: DataAnalysisItem[]
    total: number
  } {
    let allData = this.getAll()
    
    // 应用筛选条件
    if (filters) {
      if (filters.id) {
        allData = allData.filter(item => item.id.toLowerCase().includes(filters.id.toLowerCase()))
      }
      if (filters.status) {
        allData = allData.filter(item => item.status === filters.status)
      }
      if (filters.creator) {
        allData = allData.filter(item => item.creator.toLowerCase().includes(filters.creator.toLowerCase()))
      }
      if (filters.dateRange && filters.dateRange.length === 2) {
        const [startDate, endDate] = filters.dateRange
        allData = allData.filter(item => {
          const createTime = new Date(item.createTime)
          return createTime >= new Date(startDate) && createTime <= new Date(endDate)
        })
      }
    }
    
    const total = allData.length
    const startIndex = (page - 1) * size
    const endIndex = startIndex + size
    const data = allData.slice(startIndex, endIndex)
    
    return { data, total }
  }
}

/**
 * 历史记录存储管理器
 */
export class HistoryStorage extends LocalStorageManager<HistoryRecord> {
  constructor() {
    super(STORAGE_KEYS.HISTORY_RECORDS)
  }

  /**
   * 初始化数据
   */
  initializeData(analysisItems: DataAnalysisItem[]): void {
    if (this.count() === 0) {
      console.log('初始化历史记录数据...')
      const historyRecords = generateHistoryRecords(analysisItems)
      this.saveAll(historyRecords)
    }
  }

  /**
   * 根据分析项ID获取历史记录
   */
  getByAnalysisId(analysisId: string): HistoryRecord[] {
    return this.getAll().filter(record => record.analysisId === analysisId)
  }

  /**
   * 添加操作记录
   */
  addRecord(
    analysisId: string, 
    analysisName: string, 
    operation: string, 
    operator: string,
    result: 'success' | 'failed' = 'success',
    description?: string,
    errorMessage?: string
  ): boolean {
    const record: HistoryRecord = {
      id: Math.random().toString(36).substr(2, 9),
      analysisId,
      analysisName,
      operation,
      operator,
      operateTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
      result,
      description,
      errorMessage
    }
    
    return this.add(record)
  }
}

/**
 * 算法配置存储管理器
 */
export class AlgorithmConfigStorage extends LocalStorageManager<AlgorithmConfig> {
  constructor() {
    super(STORAGE_KEYS.ALGORITHM_CONFIGS)
  }

  /**
   * 初始化数据
   */
  initializeData(): void {
    if (this.count() === 0) {
      console.log('初始化算法配置数据...')
      this.saveAll(mockData.algorithmConfigs)
    }
  }

  /**
   * 根据分析类型获取算法配置
   */
  getByType(type: string): AlgorithmConfig[] {
    return this.getAll().filter(config => config.type === type && config.isEnabled)
  }

  /**
   * 启用/禁用算法
   */
  toggleEnabled(id: string): boolean {
    const config = this.getById(id)
    if (!config) return false
    
    return this.update(id, { isEnabled: !config.isEnabled })
  }
}

// 创建存储管理器实例
export const dataAnalysisStorage = new DataAnalysisStorage()
export const historyStorage = new HistoryStorage()
export const algorithmConfigStorage = new AlgorithmConfigStorage()

// 初始化所有数据
export const initializeAllData = () => {
  dataAnalysisStorage.initializeData()
  const analysisItems = dataAnalysisStorage.getAll()
  historyStorage.initializeData(analysisItems)
  algorithmConfigStorage.initializeData()
}
