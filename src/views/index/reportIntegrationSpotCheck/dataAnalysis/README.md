# 数据分析管理系统

基于 Vue 3 + TypeScript + Element Plus 开发的数据分析管理系统，支持数据分析任务的创建、监控、管理等功能。

## 🚀 功能特性

### 核心功能
- ✅ **数据分析任务管理** - 创建、编辑、删除、状态管理
- ✅ **动态操作按钮** - 根据任务状态显示不同操作（查看、导出、打印、分享、暂停、终止等）
- ✅ **实时状态监控** - 任务进度显示、状态变更
- ✅ **历史操作记录** - 完整的操作日志追踪
- ✅ **算法配置管理** - 支持多种分析算法配置
- ✅ **数据持久化** - 基于 localStorage 的数据存储
- ✅ **搜索和筛选** - 多维度数据筛选
- ✅ **分页展示** - 大数据量分页处理
- ✅ **响应式设计** - 适配不同屏幕尺寸

### 技术特性
- 🎯 **纯前端演示** - 无需后端API，完全基于前端实现
- 💾 **数据持久化** - 页面刷新数据不丢失
- 🔄 **完整CRUD** - 支持增删改查所有操作
- 📱 **响应式布局** - 移动端友好
- 🧪 **自动化测试** - Playwright 端到端测试覆盖

## 📁 项目结构

```
dataAnalysis/
├── components/                 # 组件目录
│   ├── CreateAnalysisDialog.vue   # 新建/编辑分析任务弹窗
│   ├── HistoryDialog.vue          # 历史操作记录弹窗
│   └── AlgorithmDialog.vue        # 算法配置管理弹窗
├── __tests__/                  # 测试目录
│   ├── data-analysis.test.ts      # Playwright 测试用例
│   ├── playwright.config.ts       # Playwright 配置
│   └── run-tests.sh              # 测试运行脚本
├── index.vue                   # 主页面组件
├── test.vue                    # 测试页面
├── types.ts                    # TypeScript 类型定义
├── mockData.ts                 # 模拟数据生成
├── storage.ts                  # 本地存储工具类
└── README.md                   # 项目说明文档
```

## 🛠️ 快速开始

### 1. 访问测试页面

直接访问测试页面（跳过登录）：
```
http://localhost:5173/reportIntegrationSpotCheck/dataAnalysis/test?free=true
```

### 2. 功能测试

#### 基础功能测试
1. **数据初始化** - 首次访问自动生成25条模拟数据
2. **表格展示** - 查看数据分析任务列表
3. **搜索筛选** - 按名称、类型、状态等条件筛选
4. **分页功能** - 测试分页切换

#### 任务管理测试
1. **新建任务** - 点击"新建数据分析"按钮
2. **编辑任务** - 点击表格中的"编辑"按钮
3. **状态管理** - 测试启动、暂停、恢复、终止等操作
4. **删除任务** - 删除不需要的任务

#### 高级功能测试
1. **历史记录** - 点击"数据分析历史操作记录"查看操作日志
2. **算法配置** - 点击"数据分析算法设置"管理算法
3. **数据持久化** - 刷新页面验证数据是否保存

### 3. 自动化测试

#### 安装依赖
```bash
npm install -D @playwright/test
npx playwright install
```

#### 运行测试
```bash
# 确保开发服务器运行
npm run dev

# 运行自动化测试
cd src/views/index/reportIntegrationSpotCheck/dataAnalysis/__tests__
./run-tests.sh
```

#### 测试覆盖
- ✅ 页面基本元素加载测试
- ✅ 数据初始化测试
- ✅ 搜索功能测试
- ✅ 新建数据分析功能测试
- ✅ 任务状态操作测试
- ✅ 历史操作记录功能测试
- ✅ 算法设置功能测试
- ✅ 分页功能测试
- ✅ 数据持久化测试
- ✅ 响应式布局测试

## 📊 数据结构

### 数据分析任务 (DataAnalysisItem)
```typescript
interface DataAnalysisItem {
  id: string                    // 唯一标识
  name: string                  // 分析名称
  type: DataAnalysisType        // 分析类型
  status: DataAnalysisStatus    // 状态
  progress: number              // 进度 (0-100)
  creator: string               // 创建人
  createTime: string            // 创建时间
  startTime?: string            // 开始时间
  endTime?: string              // 结束时间
  description?: string          // 描述
  dataSource?: string           // 数据源
  algorithm?: string            // 算法
  parameters?: Record<string, any> // 参数配置
}
```

### 分析类型 (DataAnalysisType)
- 统计分析
- 预测分析
- 聚类分析
- 分类分析
- 回归分析
- 时间序列分析
- 文本挖掘
- 关联分析

### 任务状态 (DataAnalysisStatus)
- 待开始
- 进行中
- 已暂停
- 已完成
- 已失败
- 已终止

## 🎨 UI 设计

### 主界面布局
- **顶部操作区** - 3个管理按钮（新建、历史记录、算法设置）
- **搜索区域** - 多条件筛选表单
- **数据表格** - 任务列表展示
- **操作列** - 动态按钮（根据状态变化）
- **分页组件** - 数据分页展示

### 弹窗设计
- **新建/编辑弹窗** - 表单验证、参数配置
- **历史记录弹窗** - 操作日志查看
- **算法配置弹窗** - 算法管理界面

## 🔧 开发说明

### 本地存储
- 使用 localStorage 存储所有数据
- 支持完整的 CRUD 操作
- 数据持久化，页面刷新不丢失

### 状态管理
- 基于 Vue 3 Composition API
- 响应式数据更新
- 统一的状态管理模式

### 组件设计
- 模块化组件结构
- 可复用的业务组件
- 统一的样式规范

## 🧪 测试策略

### 单元测试
- 组件功能测试
- 工具类测试
- 数据处理测试

### 集成测试
- 页面交互测试
- 数据流测试
- 状态管理测试

### 端到端测试
- 完整业务流程测试
- 用户操作路径测试
- 跨浏览器兼容性测试

## 📈 性能优化

- 虚拟滚动（大数据量）
- 分页加载
- 组件懒加载
- 数据缓存策略

## 🔮 扩展计划

- [ ] 实时数据更新
- [ ] 数据可视化图表
- [ ] 导出功能增强
- [ ] 多用户协作
- [ ] 权限管理
- [ ] 国际化支持

## 📝 更新日志

### v1.0.0 (2024-01-18)
- ✅ 完成基础功能开发
- ✅ 实现数据分析任务管理
- ✅ 添加历史操作记录
- ✅ 完成算法配置管理
- ✅ 实现数据持久化
- ✅ 完成自动化测试覆盖

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License
