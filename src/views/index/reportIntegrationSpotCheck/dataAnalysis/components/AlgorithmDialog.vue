<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 加载状态
const loading = ref(false)

// 算法配置表单数据
const algorithmForm = ref({
  dataRelationAnalysis: '', // 数据关联关系统计分析
  dataClusterAnalysis: '',  // 数据聚类统计分析
  dataAnomalyAnalysis: '',  // 数据异常值分析
  dataMissingAnalysis: '',  // 数据缺失值检测分析
  dataAccuracyAnalysis: ''  // 数据准确性符合分析
})

// 初始化表单数据
const initializeForm = () => {
  algorithmForm.value = {
    dataRelationAnalysis: '',
    dataClusterAnalysis: '',
    dataAnomalyAnalysis: '',
    dataMissingAnalysis: '',
    dataAccuracyAnalysis: ''
  }
}

// 保存算法配置
const handleSave = async () => {
  loading.value = true

  try {
    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 这里可以调用API保存配置
    console.log('保存算法配置:', algorithmForm.value)

    ElMessage.success('保存成功')
    handleClose()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 取消操作
const handleCancel = () => {
  handleClose()
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initializeForm()
  }
})
</script>

<template>
  <DialogComp
    :visible="visible"
    title="数据分析算法设置"
    width="600px"
    :visibleFooterButton="false"
    @closed="handleClose"
  >
    <div class="algorithm-dialog">
      <!-- 算法配置表单 -->
      <div class="algorithm-form">
        <!-- 1. 数据关联关系统计分析 -->
        <div class="form-item">
          <div class="form-label">1. 数据关联关系统计分析</div>
          <div class="form-sublabel">关联项统计方法：</div>
          <el-input
            v-model="algorithmForm.dataRelationAnalysis"
            placeholder="请输入设置"
            class="form-input"
          />
        </div>

        <!-- 2. 数据聚类统计分析 -->
        <div class="form-item">
          <div class="form-label">2. 数据聚类统计分析</div>
          <div class="form-sublabel">数据聚类规则：</div>
          <el-input
            v-model="algorithmForm.dataClusterAnalysis"
            placeholder="请输入设置"
            class="form-input"
          />
        </div>

        <!-- 3. 数据异常值分析 -->
        <div class="form-item">
          <div class="form-label">3. 数据异常值分析</div>
          <div class="form-sublabel">数据异常检测标准：</div>
          <el-input
            v-model="algorithmForm.dataAnomalyAnalysis"
            placeholder="请输入设置"
            class="form-input"
          />
        </div>

        <!-- 4. 数据缺失值检测分析 -->
        <div class="form-item">
          <div class="form-label">4. 数据缺失值检测分析</div>
          <div class="form-sublabel">数据缺失类型：</div>
          <el-input
            v-model="algorithmForm.dataMissingAnalysis"
            placeholder="请输入设置"
            class="form-input"
          />
        </div>

        <!-- 5. 数据准确性符合分析 -->
        <div class="form-item">
          <div class="form-label">5. 数据准确性符合分析</div>
          <div class="form-sublabel">数据准确性检查标准：</div>
          <el-input
            v-model="algorithmForm.dataAccuracyAnalysis"
            placeholder="请输入设置"
            class="form-input"
          />
        </div>
      </div>
    </div>

    <!-- 自定义底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="loading"
        >
          {{ loading ? '保存中...' : '保存' }}
        </el-button>
      </div>
    </template>
  </DialogComp>
</template>

<style scoped lang="scss">
.algorithm-dialog {
  padding: 20px;

  .algorithm-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .form-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .form-label {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
  }

  .form-sublabel {
    font-size: 13px;
    color: #606266;
    margin-bottom: 8px;
  }

  .form-input {
    width: 100%;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0 0 0;
}
</style>
