<template>
  <DialogComp
    :visible="visible"
    title="数据分析结果"
    width="1200px"
    :visibleFooterButton="false"
    @closed="handleClose"
  >
    <div class="analysis-result-dialog">
      <div class="result-grid">
        <!-- 1. 报表整合率的数据项重复查分析 -->
        <div class="result-item">
          <div class="item-title">1. 报表整合率的数据项重复查分析</div>
          <div class="item-content">
            <div class="data-row">
              <span class="label">总记录数：</span>
              <span class="value">12345</span>
              <span class="label">重复记录数：</span>
              <span class="value">600</span>
            </div>
            <div class="progress-row">
              <span class="label">重复率：</span>
              <el-progress :percentage="60" :stroke-width="8" />
              <span class="percentage">60%</span>
            </div>
          </div>
        </div>

        <!-- 2. 数据使用率分析 -->
        <div class="result-item">
          <div class="item-title">2. 数据使用率分析</div>
          <div class="item-content">
            <div class="data-row">
              <span class="label">数据集名称/字段名：</span>
              <el-select v-model="selectedDataset" size="small" style="width: 120px;">
                <el-option label="姓名" value="姓名" />
                <el-option label="年龄" value="年龄" />
                <el-option label="地址" value="地址" />
              </el-select>
              <span class="label">使用次数：</span>
              <span class="value">600</span>
            </div>
            <div class="progress-row">
              <span class="label">使用频率：</span>
              <el-progress :percentage="60" :stroke-width="8" />
              <span class="percentage">60%</span>
            </div>
          </div>
        </div>

        <!-- 3. 数据关联关系统计分析 -->
        <div class="result-item">
          <div class="item-title">3. 数据关联关系统计分析</div>
          <div class="item-content">
            <div class="data-row">
              <span class="label">关联字段A：</span>
              <el-select v-model="relationFieldA" size="small" style="width: 100px;">
                <el-option label="身份证号" value="身份证号" />
              </el-select>
              <span class="label">关联字段B：</span>
              <el-select v-model="relationFieldB" size="small" style="width: 100px;">
                <el-option label="户籍" value="户籍" />
              </el-select>
            </div>
            <div class="progress-row">
              <span class="label">关联强度：</span>
              <el-progress :percentage="43" :stroke-width="8" />
              <span class="percentage">4.3</span>
            </div>
          </div>
        </div>

        <!-- 4. 数据异常值分析 -->
        <div class="result-item">
          <div class="item-title">4. 数据异常值分析</div>
          <div class="item-content">
            <div class="data-row">
              <span class="label">异常值内容：</span>
              <el-input v-model="abnormalValue" size="small" style="width: 80px;" />
              <span class="label">异常值位置：</span>
              <span class="value"></span>
            </div>
            <div class="data-row">
              <span class="label">阈值设定异常范围：</span>
              <span class="value">0~120</span>
              <span class="label">来源：</span>
              <el-select v-model="abnormalSource" size="small" style="width: 120px;">
                <el-option label="合川区养老保险登记表_字段：体重" value="合川区养老保险登记表_字段：体重" />
              </el-select>
            </div>
          </div>
        </div>

        <!-- 5. 数据聚类统计分析 -->
        <div class="result-item">
          <div class="item-title">5. 数据聚类统计分析</div>
          <div class="item-content">
            <div class="data-row">
              <span class="label">聚类编号：</span>
              <span class="value">003124</span>
              <span class="label">数据类别：</span>
              <span class="value">村地面积</span>
            </div>
            <div class="data-row">
              <span class="label">中心点坐标：</span>
              <span class="value">-3</span>
              <span class="label">包含样本数：</span>
              <span class="value">500</span>
            </div>
            <div class="data-row">
              <span class="label">到中心点距离：</span>
              <span class="value">-3</span>
              <span class="label">离散程度：</span>
              <span class="value">-3</span>
            </div>
            <!-- 聚类图表占位 -->
            <div class="chart-placeholder">
              <div class="scatter-chart">聚类分析图表</div>
            </div>
          </div>
        </div>

        <!-- 6. 数据缺失值检测分析 -->
        <div class="result-item">
          <div class="item-title">6. 数据缺失值检测分析</div>
          <div class="item-content">
            <div class="data-row">
              <span class="label">缺失值数量：</span>
              <span class="value">600</span>
              <span class="label">缺失值所在位置：</span>
              <el-select v-model="missingLocation" size="small" style="width: 120px;">
                <el-option label="来源：合川区养老保险登记表 字段" value="来源：合川区养老保险登记表 字段" />
              </el-select>
            </div>
            <div class="progress-row">
              <span class="label">缺失值占比：</span>
              <el-progress :percentage="60" :stroke-width="8" />
              <span class="percentage">60%</span>
            </div>
          </div>
        </div>

        <!-- 7. 数据重复值检测分析 -->
        <div class="result-item">
          <div class="item-title">7. 数据重复值检测分析</div>
          <div class="item-content">
            <div class="data-row">
              <span class="label">重复值：</span>
              <el-select v-model="duplicateDate" size="small" style="width: 120px;">
                <el-option label="2021年3月1日" value="2021年3月1日" />
              </el-select>
              <span class="label">出现次数：</span>
              <span class="value">600</span>
            </div>
            <div class="data-row">
              <span class="label">首次出现位置：</span>
              <span class="value">报表1</span>
              <span class="label">最后出现位置：</span>
              <span class="value">报表5</span>
            </div>
          </div>
        </div>

        <!-- 8. 数据一致性检测分析 -->
        <div class="result-item">
          <div class="item-title">8. 数据一致性检测分析</div>
          <div class="item-content">
            <div class="data-row">
              <span class="label">比较源A：</span>
              <el-select v-model="compareSourceA" size="small" style="width: 100px;">
                <el-option label="报表1" value="报表1" />
              </el-select>
              <span class="label">比较源B：</span>
              <el-select v-model="compareSourceB" size="small" style="width: 100px;">
                <el-option label="报表3" value="报表3" />
              </el-select>
            </div>
            <div class="data-row">
              <span class="label">不一致记录数：</span>
              <span class="value">212</span>
              <span class="label">不一致字段明细：</span>
              <span class="value">比较源A：年龄 比较源B：年龄</span>
            </div>
          </div>
        </div>

        <!-- 9. 数据准确性检测分析 -->
        <div class="result-item">
          <div class="item-title">9. 数据准确性检测分析</div>
          <div class="item-content">
            <div class="data-row">
              <span class="label">错误记录数：</span>
              <span class="value">600</span>
              <span class="label">ID：</span>
              <span class="value">235456</span>
              <span class="label">错误详情：</span>
              <span class="value">"生日"字段不能为负</span>
            </div>
            <div class="data-row">
              <span class="label">修正建议：</span>
              <span class="value">重新输入合适范围内的数据</span>
            </div>
          </div>
        </div>

        <!-- 10. 数据完整性检测分析 -->
        <div class="result-item">
          <div class="item-title">10. 数据完整性检测分析</div>
          <div class="item-content">
            <div class="data-row">
              <span class="label">预期记录数：</span>
              <span class="value">235266</span>
              <span class="label">实际记录数：</span>
              <span class="value">129024</span>
            </div>
            <div class="progress-row">
              <span class="label">完整率：</span>
              <el-progress :percentage="60" :stroke-width="8" />
              <span class="percentage">60%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  visible: boolean
  analysisData?: any
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单数据
const selectedDataset = ref('姓名')
const relationFieldA = ref('身份证号')
const relationFieldB = ref('户籍')
const abnormalValue = ref('-3')
const abnormalSource = ref('合川区养老保险登记表_字段：体重')
const missingLocation = ref('来源：合川区养老保险登记表 字段')
const duplicateDate = ref('2021年3月1日')
const compareSourceA = ref('报表1')
const compareSourceB = ref('报表3')

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped lang="scss">
.analysis-result-dialog {
  padding: 20px;
  max-height: 600px;
  overflow-y: auto;
}

.result-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.result-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 12px;
}

.item-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.progress-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.label {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
}

.value {
  font-size: 12px;
  color: #303133;
  font-weight: 500;
}

.percentage {
  font-size: 12px;
  color: #409eff;
  font-weight: 500;
  min-width: 35px;
}

.chart-placeholder {
  margin-top: 8px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scatter-chart {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #f0f9ff 25%, transparent 25%), 
              linear-gradient(-45deg, #f0f9ff 25%, transparent 25%), 
              linear-gradient(45deg, transparent 75%, #f0f9ff 75%), 
              linear-gradient(-45deg, transparent 75%, #f0f9ff 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 12px;
}
</style>
