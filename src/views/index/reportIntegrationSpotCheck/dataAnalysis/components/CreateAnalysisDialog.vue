<script setup lang="ts">
import type { CreateAnalysisForm, DataAnalysisItem } from '../types'
import { DataAnalysisType } from '../types'
import { dataAnalysisStorage, historyStorage, algorithmConfigStorage } from '../storage'

interface Props {
  visible: boolean
  editData?: DataAnalysisItem | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  editData: null
})

const emit = defineEmits<Emits>()

// 表单ref
const dialogFormRef = ref()

// 加载状态
const loading = ref(false)

// 表单数据
const dialogForm = ref({
  creator: '',
  timeRange: {
    start: '',
    end: ''
  },
  dataSources: [],
  generateLog: true,
  filterConditions: ''
})

// 创建人选项
const creatorOptions = [
  { label: '张三', value: '张三' },
  { label: '李四', value: '李四' },
  { label: '王五', value: '王五' },
  { label: '赵六', value: '赵六' },
  { label: '钱七', value: '钱七' },
  { label: '孙八', value: '孙八' },
  { label: '周九', value: '周九' },
  { label: '吴十', value: '吴十' }
]

// 数据源选项
const dataSourceOptions = [
  { label: '报表一', value: '报表一' },
  { label: '报表二', value: '报表二' },
  { label: '报表三', value: '报表三' },
  { label: '报表四', value: '报表四' },
  { label: '报表五', value: '报表五' }
]

// 表单配置
const dialogFormProps = computed(() => [
  {
    label: '创建人',
    prop: 'creator',
    type: 'select',
    options: creatorOptions,
    placeholder: '请选择创建人'
  },
  {
    label: '时间范围',
    prop: 'timeRange',
    type: 'custom',
    component: 'timeRange'
  },
  {
    label: '数据源选择',
    prop: 'dataSources',
    type: 'custom',
    component: 'multiSelect'
  },
  {
    label: '生成日志',
    prop: 'generateLog',
    type: 'radio',
    options: [
      { label: '是', value: true },
      { label: '否', value: false }
    ]
  },
  {
    label: '过滤条件',
    prop: 'filterConditions',
    type: 'textarea',
    placeholder: '请输入过滤条件，如：出生日期 >= 2025-01-01 AND 出生日期 <= 2025-12-31',
    rows: 3
  }
])

// 表单验证规则
const dialogFormRules = {
  creator: [
    { required: true, message: '请选择创建人', trigger: 'change' }
  ],
  'timeRange.start': [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  'timeRange.end': [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  dataSources: [
    { required: true, message: '请至少选择一个数据源', trigger: 'change' }
  ],
  generateLog: [
    { required: true, message: '请选择是否生成日志', trigger: 'change' }
  ]
}

// 弹窗标题
const dialogTitle = computed(() => {
  return props.editData ? '编辑数据分析' : '新建数据分析'
})

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 弹窗打开时初始化表单
    if (props.editData) {
      // 编辑模式 - 暂时不支持编辑，使用默认值
      resetForm()
    } else {
      // 新建模式
      resetForm()
    }
  }
})

// 重置表单
const resetForm = () => {
  const now = new Date()
  const startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 13, 0, 21)
  const endTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 13, 0, 21)

  dialogForm.value = {
    creator: '',
    timeRange: {
      start: startTime.toISOString().slice(0, 19).replace('T', ' '),
      end: endTime.toISOString().slice(0, 19).replace('T', ' ')
    },
    dataSources: [],
    generateLog: true,
    filterConditions: '出生日期 >= 2025-01-01 AND 出生日期 <= 2025-12-31'
  }

  nextTick(() => {
    dialogFormRef.value?.clearValidate()
  })
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 确认提交
const handleConfirm = () => {
  dialogFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      submitForm()
    }
  })
}

// 提交表单
const submitForm = async () => {
  loading.value = true

  try {
    // 模拟提交延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    const now = new Date()
    const formattedTime = now.toISOString().slice(0, 19).replace('T', ' ')

    // 生成分析任务名称
    const analysisName = `数据分析_${now.getTime().toString().slice(-6)}`

    // 新建模式
    const newAnalysis: DataAnalysisItem = {
      id: Math.random().toString(36).substr(2, 9),
      name: analysisName,
      type: '数据分析',
      status: '进行中',
      progress: 5,
      creator: dialogForm.value.creator,
      createTime: formattedTime,
      startTime: formattedTime,
      description: `时间范围：${dialogForm.value.timeRange.start} 至 ${dialogForm.value.timeRange.end}`,
      dataSource: dialogForm.value.dataSources.join(', '),
      algorithm: '默认算法',
      parameters: {
        timeRange: dialogForm.value.timeRange,
        dataSources: dialogForm.value.dataSources,
        generateLog: dialogForm.value.generateLog,
        filterConditions: dialogForm.value.filterConditions
      }
    }

    const success = dataAnalysisStorage.add(newAnalysis)

    if (success) {
      historyStorage.addRecord(
        newAnalysis.id,
        newAnalysis.name,
        '创建',
        dialogForm.value.creator,
        'success',
        '创建了新的数据分析任务'
      )

      historyStorage.addRecord(
        newAnalysis.id,
        newAnalysis.name,
        '启动',
        dialogForm.value.creator,
        'success',
        '启动数据分析任务'
      )

      ElMessage.success('创建成功')
    } else {
      ElMessage.error('创建失败')
      return
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}


</script>

<template>
  <DialogComp
    :visible="visible"
    :title="dialogTitle"
    width="600px"
    :loading="loading"
    :visibleFooterButton="false"
    @closed="handleClose"
  >
    <!-- 自定义表单 -->
    <el-form
      ref="dialogFormRef"
      :model="dialogForm"
      :rules="dialogFormRules"
      label-width="100px"
    >
      <!-- 创建人 -->
      <el-form-item label="创建人" prop="creator">
        <el-select
          v-model="dialogForm.creator"
          placeholder="请选择创建人"
          style="width: 100%"
        >
          <el-option
            v-for="option in creatorOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <!-- 时间范围 -->
      <el-form-item label="时间范围">
        <div style="display: flex; align-items: center; gap: 8px; width: 100%">
          <span>从</span>
          <el-date-picker
            v-model="dialogForm.timeRange.start"
            type="datetime"
            placeholder="开始时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="flex: 1"
          />
          <span>至</span>
          <el-date-picker
            v-model="dialogForm.timeRange.end"
            type="datetime"
            placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="flex: 1"
          />
        </div>
      </el-form-item>

      <!-- 数据源选择 -->
      <el-form-item label="数据源选择" prop="dataSources">
        <el-select
          v-model="dialogForm.dataSources"
          multiple
          placeholder="请选择数据源"
          style="width: 100%"
        >
          <el-option
            v-for="option in dataSourceOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <!-- 生成日志 -->
      <el-form-item label="生成日志" prop="generateLog">
        <el-radio-group v-model="dialogForm.generateLog">
          <el-radio :value="true">是</el-radio>
          <el-radio :value="false">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 过滤条件 -->
      <el-form-item label="过滤条件">
        <el-input
          v-model="dialogForm.filterConditions"
          type="textarea"
          :rows="3"
          placeholder="请输入过滤条件，如：出生日期 >= 2025-01-01 AND 出生日期 <= 2025-12-31"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="loading"
        >
          {{ loading ? '保存中...' : '确定' }}
        </el-button>
      </div>
    </template>
  </DialogComp>
</template>

<style scoped lang="scss">
.dialog-footer {
  display: flex;
  align-items: center;
  gap: 12px;
}
</style>
