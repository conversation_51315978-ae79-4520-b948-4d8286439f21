<script setup lang="ts">
import type { HistoryRecord } from '../types'
import { historyStorage } from '../storage'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref<HistoryRecord[]>([])

// 分页配置
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 搜索表单
const searchForm = ref({
  analysisName: '',
  operation: '',
  operator: '',
  result: ''
})

// 搜索表单配置
const searchFormProps = [
  { label: '分析名称', prop: 'analysisName', type: 'text' },
  { 
    label: '操作类型', 
    prop: 'operation', 
    type: 'select',
    options: [
      { label: '创建', value: '创建' },
      { label: '启动', value: '启动' },
      { label: '暂停', value: '暂停' },
      { label: '恢复', value: '恢复' },
      { label: '终止', value: '终止' },
      { label: '删除', value: '删除' },
      { label: '修改参数', value: '修改参数' },
      { label: '重新运行', value: '重新运行' }
    ]
  },
  { label: '操作人', prop: 'operator', type: 'text' },
  { 
    label: '操作结果', 
    prop: 'result', 
    type: 'select',
    options: [
      { label: '成功', value: 'success' },
      { label: '失败', value: 'failed' }
    ]
  }
]

// 表格列配置
const columns = [
  { prop: 'analysisName', label: '分析名称', minWidth: '150px' },
  { prop: 'operation', label: '操作类型', width: '100px', align: 'center' },
  { prop: 'operator', label: '操作人', width: '100px', align: 'center' },
  { prop: 'operateTime', label: '操作时间', width: '160px', align: 'center' },
  { prop: 'result', label: '操作结果', width: '100px', align: 'center' },
  { prop: 'description', label: '操作描述', minWidth: '200px' }
]

// 加载历史记录数据
const loadHistoryData = async () => {
  loading.value = true
  
  try {
    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 300))
    
    let allRecords = historyStorage.getAll()
    
    // 应用搜索过滤
    if (searchForm.value.analysisName) {
      allRecords = allRecords.filter(record => 
        record.analysisName.toLowerCase().includes(searchForm.value.analysisName.toLowerCase())
      )
    }
    
    if (searchForm.value.operation) {
      allRecords = allRecords.filter(record => record.operation === searchForm.value.operation)
    }
    
    if (searchForm.value.operator) {
      allRecords = allRecords.filter(record => 
        record.operator.toLowerCase().includes(searchForm.value.operator.toLowerCase())
      )
    }
    
    if (searchForm.value.result) {
      allRecords = allRecords.filter(record => record.result === searchForm.value.result)
    }
    
    // 分页处理
    pagination.total = allRecords.length
    const startIndex = (pagination.page - 1) * pagination.size
    const endIndex = startIndex + pagination.size
    tableData.value = allRecords.slice(startIndex, endIndex)
    
  } catch (error) {
    console.error('加载历史记录失败:', error)
    ElMessage.error('加载历史记录失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const onSearch = () => {
  pagination.page = 1
  loadHistoryData()
}

// 重置搜索
const onReset = () => {
  searchForm.value = {
    analysisName: '',
    operation: '',
    operator: '',
    result: ''
  }
  onSearch()
}

// 分页事件
const onPaginationChange = (val: number, type: 'page' | 'size') => {
  if (type === 'page') {
    pagination.page = val
  } else {
    pagination.size = val
    pagination.page = 1
  }
  loadHistoryData()
}

// 获取操作结果标签类型
const getResultTagType = (result: string) => {
  return result === 'success' ? 'success' : 'danger'
}

// 获取操作结果文本
const getResultText = (result: string) => {
  return result === 'success' ? '成功' : '失败'
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadHistoryData()
  }
})
</script>

<template>
  <DialogComp
    :visible="visible"
    title="数据分析历史操作记录"
    width="1200px"
    :visibleConfirmButton="false"
    cancelText="关闭"
    @closed="handleClose"
    @clickCancel="handleClose"
  >
    <div class="history-dialog">
      <!-- 搜索区域 -->
      <div class="search-section">
        <Form
          :props="searchFormProps"
          v-model="searchForm"
          :column-count="4"
          :label-width="80"
          :enable-reset="true"
          confirm-text="查询"
          reset-text="重置"
          button-vertical="flowing"
          @submit="onSearch"
          @reset="onReset"
        />
      </div>
      
      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        border
        height="400px"
        element-loading-text="加载中..."
        style="width: 100%"
      >
        <el-table-column
          v-for="column in columns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :align="column.align || 'left'"
        >
          <template #default="{ row }">
            <!-- 操作结果列自定义渲染 -->
            <template v-if="column.prop === 'result'">
              <el-tag 
                :type="getResultTagType(row.result)"
                size="small"
              >
                {{ getResultText(row.result) }}
              </el-tag>
            </template>
            
            <!-- 操作描述列自定义渲染 -->
            <template v-else-if="column.prop === 'description'">
              <el-tooltip
                v-if="row.description"
                :content="row.description"
                placement="top"
              >
                <span class="description-text">{{ row.description }}</span>
              </el-tooltip>
              <span v-else>-</span>
            </template>
            
            <!-- 普通列 -->
            <template v-else>
              {{ row[column.prop] }}
            </template>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-section">
        <Pagination
          :total="pagination.total"
          :current-page="pagination.page"
          :page-size="pagination.size"
          :disabled="loading"
          @current-change="onPaginationChange($event, 'page')"
          @size-change="onPaginationChange($event, 'size')"
        />
      </div>
    </div>
  </DialogComp>
</template>

<style scoped lang="scss">
.history-dialog {
  .search-section {
    margin-bottom: 16px;
    padding: 16px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
  
  .pagination-section {
    margin-top: 16px;
    display: flex;
    justify-content: center;
  }
  
  .description-text {
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
  }
}
</style>
