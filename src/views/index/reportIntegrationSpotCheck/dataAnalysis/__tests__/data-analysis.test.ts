import { test, expect } from '@playwright/test'

// 测试配置
const BASE_URL = 'http://localhost:5173'
const TEST_PAGE_URL = `${BASE_URL}/reportIntegrationSpotCheck/dataAnalysis/test?free=true`

test.describe('数据分析管理功能测试', () => {
  
  test.beforeEach(async ({ page }) => {
    // 清除localStorage确保测试环境干净
    await page.goto(TEST_PAGE_URL)
    await page.evaluate(() => {
      localStorage.clear()
    })
    await page.reload()
    
    // 等待页面加载完成
    await page.waitForSelector('.data-analysis')
    await page.waitForTimeout(1000) // 等待数据初始化
  })

  test('页面基本元素加载测试', async ({ page }) => {
    // 检查页面标题
    await expect(page.locator('.el-card__header')).toContainText('数据分析')
    
    // 检查顶部操作按钮
    await expect(page.locator('text=新建数据分析')).toBeVisible()
    await expect(page.locator('text=数据分析历史操作记录')).toBeVisible()
    await expect(page.locator('text=数据分析算法设置')).toBeVisible()
    
    // 检查搜索表单
    await expect(page.locator('input[placeholder*="分析名称"]')).toBeVisible()
    
    // 检查表格
    await expect(page.locator('.el-table')).toBeVisible()
    
    // 检查分页组件
    await expect(page.locator('.el-pagination')).toBeVisible()
  })

  test('数据初始化测试', async ({ page }) => {
    // 等待表格数据加载
    await page.waitForSelector('.el-table tbody tr')
    
    // 检查是否有数据行
    const rows = await page.locator('.el-table tbody tr').count()
    expect(rows).toBeGreaterThan(0)
    
    // 检查表格列
    await expect(page.locator('th:has-text("序号")')).toBeVisible()
    await expect(page.locator('th:has-text("分析名称")')).toBeVisible()
    await expect(page.locator('th:has-text("分析类型")')).toBeVisible()
    await expect(page.locator('th:has-text("状态")')).toBeVisible()
    await expect(page.locator('th:has-text("进度")')).toBeVisible()
    await expect(page.locator('th:has-text("创建人")')).toBeVisible()
    await expect(page.locator('th:has-text("创建时间")')).toBeVisible()
    await expect(page.locator('th:has-text("操作")')).toBeVisible()
  })

  test('搜索功能测试', async ({ page }) => {
    // 等待数据加载
    await page.waitForSelector('.el-table tbody tr')
    
    // 记录初始行数
    const initialRows = await page.locator('.el-table tbody tr').count()
    
    // 测试按名称搜索
    await page.fill('input[placeholder*="分析名称"]', '统计分析')
    await page.click('button:has-text("查询")')
    
    // 等待搜索结果
    await page.waitForTimeout(1000)
    
    // 检查搜索结果
    const searchRows = await page.locator('.el-table tbody tr').count()
    expect(searchRows).toBeLessThanOrEqual(initialRows)
    
    // 重置搜索
    await page.click('button:has-text("重置")')
    await page.waitForTimeout(1000)
    
    // 检查是否恢复到初始状态
    const resetRows = await page.locator('.el-table tbody tr').count()
    expect(resetRows).toBe(initialRows)
  })

  test('新建数据分析功能测试', async ({ page }) => {
    // 点击新建按钮
    await page.click('button:has-text("新建数据分析")')
    
    // 等待弹窗出现
    await expect(page.locator('.el-dialog__title:has-text("新建数据分析")')).toBeVisible()
    
    // 填写表单
    await page.fill('input[placeholder*="分析名称"]', '测试分析任务')
    await page.selectOption('select', { label: '统计分析' })
    await page.fill('textarea[placeholder*="分析描述"]', '这是一个测试分析任务的描述')
    
    // 选择数据源
    const dataSourceSelect = page.locator('.el-select').nth(1)
    await dataSourceSelect.click()
    await page.click('.el-option:has-text("用户行为数据库")')
    
    // 选择算法
    const algorithmSelect = page.locator('.el-select').nth(2)
    await algorithmSelect.click()
    await page.click('.el-option').first()
    
    // 提交表单
    await page.click('button:has-text("确定")')
    
    // 等待提交完成
    await page.waitForTimeout(2000)
    
    // 检查是否创建成功
    await expect(page.locator('.el-message--success')).toBeVisible()
    
    // 检查表格中是否有新数据
    await expect(page.locator('td:has-text("测试分析任务")')).toBeVisible()
  })

  test('任务状态操作测试', async ({ page }) => {
    // 等待数据加载
    await page.waitForSelector('.el-table tbody tr')
    
    // 找到一个"待开始"状态的任务
    const pendingRow = page.locator('tr:has(.el-tag:has-text("待开始"))').first()
    
    if (await pendingRow.count() > 0) {
      // 点击启动按钮
      await pendingRow.locator('button:has-text("启动")').click()
      
      // 等待状态更新
      await page.waitForTimeout(1000)
      
      // 检查状态是否变为"进行中"
      await expect(pendingRow.locator('.el-tag:has-text("进行中")')).toBeVisible()
    }
    
    // 找到一个"进行中"状态的任务
    const runningRow = page.locator('tr:has(.el-tag:has-text("进行中"))').first()
    
    if (await runningRow.count() > 0) {
      // 点击暂停按钮
      await runningRow.locator('button:has-text("暂停")').click()
      
      // 等待状态更新
      await page.waitForTimeout(1000)
      
      // 检查状态是否变为"已暂停"
      await expect(runningRow.locator('.el-tag:has-text("已暂停")')).toBeVisible()
    }
  })

  test('历史操作记录功能测试', async ({ page }) => {
    // 点击历史记录按钮
    await page.click('button:has-text("数据分析历史操作记录")')
    
    // 等待弹窗出现
    await expect(page.locator('.el-dialog__title:has-text("数据分析历史操作记录")')).toBeVisible()
    
    // 检查历史记录表格
    await expect(page.locator('.history-dialog .el-table')).toBeVisible()
    
    // 检查是否有历史记录数据
    const historyRows = await page.locator('.history-dialog .el-table tbody tr').count()
    expect(historyRows).toBeGreaterThan(0)
    
    // 关闭弹窗
    await page.click('button:has-text("关闭")')
    await expect(page.locator('.el-dialog__title:has-text("数据分析历史操作记录")')).not.toBeVisible()
  })

  test('算法设置功能测试', async ({ page }) => {
    // 点击算法设置按钮
    await page.click('button:has-text("数据分析算法设置")')
    
    // 等待弹窗出现
    await expect(page.locator('.el-dialog__title:has-text("数据分析算法设置")')).toBeVisible()
    
    // 检查算法配置表格
    await expect(page.locator('.algorithm-dialog .el-table')).toBeVisible()
    
    // 检查是否有算法配置数据
    const algorithmRows = await page.locator('.algorithm-dialog .el-table tbody tr').count()
    expect(algorithmRows).toBeGreaterThan(0)
    
    // 关闭弹窗
    await page.click('button:has-text("关闭")')
    await expect(page.locator('.el-dialog__title:has-text("数据分析算法设置")')).not.toBeVisible()
  })

  test('分页功能测试', async ({ page }) => {
    // 等待数据加载
    await page.waitForSelector('.el-table tbody tr')
    
    // 检查分页组件
    await expect(page.locator('.el-pagination')).toBeVisible()
    
    // 检查总数显示
    const totalText = await page.locator('.el-pagination__total').textContent()
    expect(totalText).toContain('共')
    
    // 如果有多页，测试翻页
    const nextButton = page.locator('.el-pagination .btn-next')
    if (await nextButton.isEnabled()) {
      await nextButton.click()
      await page.waitForTimeout(1000)
      
      // 检查页码是否变化
      await expect(page.locator('.el-pagination .number.is-active')).toContainText('2')
    }
  })

  test('数据持久化测试', async ({ page }) => {
    // 创建一个新任务
    await page.click('button:has-text("新建数据分析")')
    await page.fill('input[placeholder*="分析名称"]', '持久化测试任务')
    await page.selectOption('select', { label: '统计分析' })
    await page.fill('textarea[placeholder*="分析描述"]', '测试数据持久化功能')
    
    // 选择数据源和算法
    const dataSourceSelect = page.locator('.el-select').nth(1)
    await dataSourceSelect.click()
    await page.click('.el-option:has-text("用户行为数据库")')
    
    const algorithmSelect = page.locator('.el-select').nth(2)
    await algorithmSelect.click()
    await page.click('.el-option').first()
    
    await page.click('button:has-text("确定")')
    await page.waitForTimeout(2000)
    
    // 刷新页面
    await page.reload()
    await page.waitForSelector('.el-table tbody tr')
    await page.waitForTimeout(1000)
    
    // 检查数据是否仍然存在
    await expect(page.locator('td:has-text("持久化测试任务")')).toBeVisible()
  })

  test('响应式布局测试', async ({ page }) => {
    // 测试不同屏幕尺寸
    await page.setViewportSize({ width: 1200, height: 800 })
    await page.waitForTimeout(500)

    // 检查表格是否正常显示
    await expect(page.locator('.el-table')).toBeVisible()

    // 测试较小屏幕
    await page.setViewportSize({ width: 800, height: 600 })
    await page.waitForTimeout(500)

    // 检查表格是否仍然可见
    await expect(page.locator('.el-table')).toBeVisible()
  })

  test('表单验证测试', async ({ page }) => {
    // 点击新建按钮
    await page.click('button:has-text("新建数据分析")')

    // 不填写任何内容直接提交
    await page.click('button:has-text("确定")')

    // 检查验证错误信息
    await expect(page.locator('.el-form-item__error')).toBeVisible()

    // 关闭弹窗
    await page.click('button:has-text("取消")')
  })

  test('批量操作测试', async ({ page }) => {
    // 等待数据加载
    await page.waitForSelector('.el-table tbody tr')

    // 测试删除操作
    const firstRow = page.locator('.el-table tbody tr').first()
    const deleteButton = firstRow.locator('button:has-text("删除")')

    if (await deleteButton.count() > 0) {
      await deleteButton.click()

      // 确认删除
      await page.click('.el-popconfirm__action button:has-text("确定")')

      // 等待删除完成
      await page.waitForTimeout(1000)

      // 检查成功消息
      await expect(page.locator('.el-message--success')).toBeVisible()
    }
  })

  test('错误处理测试', async ({ page }) => {
    // 模拟网络错误或其他异常情况
    await page.route('**/*', route => {
      if (route.request().url().includes('api')) {
        route.abort()
      } else {
        route.continue()
      }
    })

    // 尝试执行操作，应该有错误处理
    await page.reload()
    await page.waitForSelector('.data-analysis')
  })

  test('键盘导航测试', async ({ page }) => {
    // 测试Tab键导航
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab')

    // 测试Enter键操作
    await page.keyboard.press('Enter')

    // 测试Escape键关闭弹窗
    if (await page.locator('.el-dialog').count() > 0) {
      await page.keyboard.press('Escape')
    }
  })

  test('数据导出功能测试', async ({ page }) => {
    // 等待数据加载
    await page.waitForSelector('.el-table tbody tr')

    // 找到已完成状态的任务
    const completedRow = page.locator('tr:has(.el-tag:has-text("已完成"))').first()

    if (await completedRow.count() > 0) {
      // 点击导出按钮
      const exportButton = completedRow.locator('button:has-text("导出")')
      if (await exportButton.count() > 0) {
        await exportButton.click()

        // 检查导出成功消息
        await expect(page.locator('.el-message--success')).toBeVisible()
      }
    }
  })

  test('性能测试', async ({ page }) => {
    // 记录页面加载时间
    const startTime = Date.now()

    await page.goto(TEST_PAGE_URL)
    await page.waitForSelector('.el-table tbody tr')

    const loadTime = Date.now() - startTime

    // 页面加载时间应该在合理范围内（5秒内）
    expect(loadTime).toBeLessThan(5000)

    // 测试大量数据操作的性能
    for (let i = 0; i < 5; i++) {
      await page.click('button:has-text("查询")')
      await page.waitForTimeout(100)
    }
  })

  test('无障碍访问测试', async ({ page }) => {
    // 检查页面是否有适当的ARIA标签
    await expect(page.locator('[role="table"]')).toBeVisible()

    // 检查按钮是否有适当的标签
    await expect(page.locator('button[aria-label]')).toHaveCount(0) // 如果没有aria-label，这个测试会失败，提醒我们添加

    // 检查表单元素是否有标签
    const inputs = page.locator('input')
    const inputCount = await inputs.count()

    for (let i = 0; i < inputCount; i++) {
      const input = inputs.nth(i)
      // 检查是否有关联的label或placeholder
      const hasLabel = await input.locator('..').locator('label').count() > 0
      const hasPlaceholder = await input.getAttribute('placeholder') !== null

      expect(hasLabel || hasPlaceholder).toBeTruthy()
    }
  })
})
