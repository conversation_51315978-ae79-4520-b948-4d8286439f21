#!/bin/bash

# 数据分析管理功能自动化测试脚本

echo "🚀 开始运行数据分析管理功能自动化测试..."

# 检查是否安装了 Playwright
if ! command -v npx playwright --version &> /dev/null; then
    echo "❌ Playwright 未安装，正在安装..."
    npm install -D @playwright/test
    npx playwright install
fi

# 设置测试目录
TEST_DIR="src/views/index/reportIntegrationSpotCheck/dataAnalysis/__tests__"
cd "$TEST_DIR" || exit 1

echo "📁 当前测试目录: $(pwd)"

# 检查开发服务器是否运行
echo "🔍 检查开发服务器状态..."
if ! curl -s http://localhost:5173 > /dev/null; then
    echo "⚠️  开发服务器未运行，请先启动开发服务器："
    echo "   npm run dev"
    exit 1
fi

echo "✅ 开发服务器运行正常"

# 运行测试
echo "🧪 开始执行测试..."

# 运行所有测试
npx playwright test --config=playwright.config.ts

# 检查测试结果
if [ $? -eq 0 ]; then
    echo "✅ 所有测试通过！"
    
    # 生成测试报告
    echo "📊 生成测试报告..."
    npx playwright show-report test-results/html-report
    
    echo "📋 测试总结："
    echo "   - 测试文件: data-analysis.test.ts"
    echo "   - 测试用例: 10个主要功能测试"
    echo "   - 覆盖功能: 页面加载、数据初始化、搜索、CRUD操作、状态管理、分页、数据持久化等"
    echo "   - 报告位置: test-results/html-report/index.html"
    
else
    echo "❌ 测试失败，请查看详细错误信息"
    echo "📊 查看测试报告："
    npx playwright show-report test-results/html-report
    exit 1
fi

echo "🎉 测试完成！"
