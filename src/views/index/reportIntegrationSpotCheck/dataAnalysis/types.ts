/**
 * 数据分析管理相关类型定义
 */

// 数据分析状态枚举
export enum DataAnalysisStatus {
  PENDING = '待开始',
  RUNNING = '进行中', 
  PAUSED = '已暂停',
  COMPLETED = '已完成',
  FAILED = '已失败',
  TERMINATED = '已终止'
}

// 数据分析类型枚举
export enum DataAnalysisType {
  STATISTICAL = '统计分析',
  PREDICTIVE = '预测分析',
  CLUSTERING = '聚类分析',
  CLASSIFICATION = '分类分析',
  REGRESSION = '回归分析',
  TIME_SERIES = '时间序列分析',
  TEXT_MINING = '文本挖掘',
  ASSOCIATION = '关联分析'
}

// 数据分析项接口
export interface DataAnalysisItem {
  id: string
  name: string // 分析名称
  type: DataAnalysisType // 分析类型
  status: DataAnalysisStatus // 状态
  progress: number // 进度百分比 (0-100)
  creator: string // 创建人
  createTime: string // 创建时间
  startTime?: string // 开始时间
  endTime?: string // 结束时间
  description?: string // 描述
  dataSource?: string // 数据源
  algorithm?: string // 算法
  parameters?: Record<string, any> // 参数配置
  resultPath?: string // 结果路径
  errorMessage?: string // 错误信息
}

// 搜索表单接口
export interface SearchForm {
  name: string // 分析名称
  type: string // 分析类型
  status: string // 状态
  creator: string // 创建人
  dateRange: [string, string] | null // 创建时间范围
}

// 新建数据分析表单接口
export interface CreateAnalysisForm {
  name: string // 分析名称
  type: DataAnalysisType // 分析类型
  description: string // 描述
  dataSource: string // 数据源
  algorithm: string // 算法
  parameters: Record<string, any> // 参数配置
  scheduleType: 'immediate' | 'scheduled' // 执行类型：立即执行或定时执行
  scheduledTime?: string // 定时执行时间
}

// 历史操作记录接口
export interface HistoryRecord {
  id: string
  analysisId: string // 关联的数据分析ID
  analysisName: string // 数据分析名称
  operation: string // 操作类型：创建、启动、暂停、终止、删除等
  operator: string // 操作人
  operateTime: string // 操作时间
  description?: string // 操作描述
  result: 'success' | 'failed' // 操作结果
  errorMessage?: string // 错误信息
}

// 算法配置接口
export interface AlgorithmConfig {
  id: string
  name: string // 算法名称
  type: DataAnalysisType // 适用的分析类型
  description: string // 算法描述
  parameters: AlgorithmParameter[] // 参数配置
  isEnabled: boolean // 是否启用
  createTime: string // 创建时间
  updateTime: string // 更新时间
}

// 算法参数接口
export interface AlgorithmParameter {
  name: string // 参数名称
  type: 'string' | 'number' | 'boolean' | 'select' | 'multiSelect' // 参数类型
  label: string // 参数标签
  defaultValue: any // 默认值
  required: boolean // 是否必填
  description?: string // 参数描述
  options?: { label: string; value: any }[] // 选项（用于select类型）
  min?: number // 最小值（用于number类型）
  max?: number // 最大值（用于number类型）
  validation?: string // 验证规则
}

// 分页参数接口
export interface PaginationParams {
  page: number
  size: number
  total: number
}

// 表格按钮配置接口
export interface TableButton {
  label: string
  type: 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger'
  code: string
  popconfirm?: string
}

// 表格列配置接口
export interface TableColumn {
  prop: string
  label: string
  width?: string
  minWidth?: string
  align?: 'left' | 'center' | 'right'
  formatter?: (row: any, column: any, cellValue: any) => string
}

// 本地存储键名常量
export const STORAGE_KEYS = {
  DATA_ANALYSIS_LIST: 'dataAnalysisList',
  HISTORY_RECORDS: 'dataAnalysisHistoryRecords', 
  ALGORITHM_CONFIGS: 'dataAnalysisAlgorithmConfigs'
} as const
