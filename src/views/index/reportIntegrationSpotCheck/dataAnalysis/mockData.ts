/**
 * 数据分析管理模拟数据
 */

import type { 
  DataAnalysisItem, 
  HistoryRecord, 
  AlgorithmConfig,
  AlgorithmParameter 
} from './types'
import { DataAnalysisStatus, DataAnalysisType } from './types'

// 生成随机ID
const generateId = () => Math.random().toString(36).substr(2, 9)

// 生成随机日期
const generateRandomDate = (start: Date, end: Date) => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
}

// 格式化日期
const formatDate = (date: Date) => {
  return date.toISOString().slice(0, 19).replace('T', ' ')
}

// 创建人列表
const creators = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']

// 数据源列表
const dataSources = [
  '用户行为数据库',
  '销售数据仓库', 
  '财务系统数据',
  '客户关系管理系统',
  '产品数据库',
  '日志文件系统',
  '第三方API数据',
  'Excel文件导入'
]

// 算法列表
const algorithms = [
  'K-Means聚类',
  '线性回归',
  '逻辑回归',
  '决策树',
  '随机森林',
  'SVM支持向量机',
  '神经网络',
  'ARIMA时间序列',
  'TF-IDF文本分析',
  'Apriori关联规则'
]

// 生成数据分析项模拟数据
export const generateDataAnalysisItems = (count: number = 25): DataAnalysisItem[] => {
  const items: DataAnalysisItem[] = []
  const now = new Date()
  const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
  
  const statusList = Object.values(DataAnalysisStatus)
  const typeList = Object.values(DataAnalysisType)
  
  for (let i = 0; i < count; i++) {
    const createTime = generateRandomDate(oneMonthAgo, now)
    const status = statusList[Math.floor(Math.random() * statusList.length)]
    const type = typeList[Math.floor(Math.random() * typeList.length)]
    
    // 根据状态生成进度
    let progress = 0
    let startTime: string | undefined
    let endTime: string | undefined
    
    switch (status) {
      case DataAnalysisStatus.PENDING:
        progress = 0
        break
      case DataAnalysisStatus.RUNNING:
        progress = Math.floor(Math.random() * 80) + 10 // 10-90%
        startTime = formatDate(new Date(createTime.getTime() + Math.random() * 60 * 60 * 1000))
        break
      case DataAnalysisStatus.PAUSED:
        progress = Math.floor(Math.random() * 70) + 20 // 20-90%
        startTime = formatDate(new Date(createTime.getTime() + Math.random() * 60 * 60 * 1000))
        break
      case DataAnalysisStatus.COMPLETED:
        progress = 100
        startTime = formatDate(new Date(createTime.getTime() + Math.random() * 60 * 60 * 1000))
        endTime = formatDate(new Date(createTime.getTime() + Math.random() * 24 * 60 * 60 * 1000))
        break
      case DataAnalysisStatus.FAILED:
        progress = Math.floor(Math.random() * 60) + 10 // 10-70%
        startTime = formatDate(new Date(createTime.getTime() + Math.random() * 60 * 60 * 1000))
        break
      case DataAnalysisStatus.TERMINATED:
        progress = Math.floor(Math.random() * 80) + 10 // 10-90%
        startTime = formatDate(new Date(createTime.getTime() + Math.random() * 60 * 60 * 1000))
        break
    }
    
    const item: DataAnalysisItem = {
      id: generateId(),
      name: `${type}任务_${String(i + 1).padStart(3, '0')}`,
      type,
      status,
      progress,
      creator: creators[Math.floor(Math.random() * creators.length)],
      createTime: formatDate(createTime),
      startTime,
      endTime,
      description: `这是一个${type}任务，用于分析相关业务数据并生成分析报告。`,
      dataSource: dataSources[Math.floor(Math.random() * dataSources.length)],
      algorithm: algorithms[Math.floor(Math.random() * algorithms.length)],
      parameters: {
        batchSize: Math.floor(Math.random() * 1000) + 100,
        maxIterations: Math.floor(Math.random() * 100) + 50,
        learningRate: (Math.random() * 0.1 + 0.01).toFixed(3)
      }
    }
    
    // 为失败状态添加错误信息
    if (status === DataAnalysisStatus.FAILED) {
      const errorMessages = [
        '数据源连接超时',
        '内存不足，无法处理大数据集',
        '算法参数配置错误',
        '数据格式不匹配',
        '权限不足，无法访问数据源'
      ]
      item.errorMessage = errorMessages[Math.floor(Math.random() * errorMessages.length)]
    }
    
    // 为已完成状态添加结果路径
    if (status === DataAnalysisStatus.COMPLETED) {
      item.resultPath = `/results/analysis_${item.id}_${Date.now()}.json`
    }
    
    items.push(item)
  }
  
  return items.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
}

// 生成历史操作记录模拟数据
export const generateHistoryRecords = (analysisItems: DataAnalysisItem[]): HistoryRecord[] => {
  const records: HistoryRecord[] = []
  const operations = ['创建', '启动', '暂停', '恢复', '终止', '删除', '修改参数', '重新运行']
  const operators = creators
  
  analysisItems.forEach(item => {
    // 每个分析项生成1-5条历史记录
    const recordCount = Math.floor(Math.random() * 5) + 1
    
    for (let i = 0; i < recordCount; i++) {
      const baseTime = new Date(item.createTime)
      const operateTime = new Date(baseTime.getTime() + i * 60 * 60 * 1000 + Math.random() * 60 * 60 * 1000)
      
      const record: HistoryRecord = {
        id: generateId(),
        analysisId: item.id,
        analysisName: item.name,
        operation: operations[Math.floor(Math.random() * operations.length)],
        operator: operators[Math.floor(Math.random() * operators.length)],
        operateTime: formatDate(operateTime),
        result: Math.random() > 0.1 ? 'success' : 'failed', // 90%成功率
        description: `对数据分析任务"${item.name}"执行了相关操作`
      }
      
      if (record.result === 'failed') {
        record.errorMessage = '操作执行失败，请检查系统状态后重试'
      }
      
      records.push(record)
    }
  })
  
  return records.sort((a, b) => new Date(b.operateTime).getTime() - new Date(a.operateTime).getTime())
}

// 生成算法配置模拟数据
export const generateAlgorithmConfigs = (): AlgorithmConfig[] => {
  const configs: AlgorithmConfig[] = []
  const now = new Date()
  
  // K-Means聚类算法配置
  configs.push({
    id: generateId(),
    name: 'K-Means聚类',
    type: DataAnalysisType.CLUSTERING,
    description: 'K-Means是一种无监督学习算法，用于将数据分成k个簇',
    parameters: [
      {
        name: 'k',
        type: 'number',
        label: '簇数量',
        defaultValue: 3,
        required: true,
        description: '要分成的簇的数量',
        min: 2,
        max: 20
      },
      {
        name: 'maxIterations',
        type: 'number', 
        label: '最大迭代次数',
        defaultValue: 100,
        required: true,
        min: 10,
        max: 1000
      },
      {
        name: 'tolerance',
        type: 'number',
        label: '收敛阈值',
        defaultValue: 0.001,
        required: false,
        min: 0.0001,
        max: 0.1
      }
    ],
    isEnabled: true,
    createTime: formatDate(new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)),
    updateTime: formatDate(new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000))
  })
  
  // 线性回归算法配置
  configs.push({
    id: generateId(),
    name: '线性回归',
    type: DataAnalysisType.REGRESSION,
    description: '线性回归用于建立因变量与自变量之间的线性关系模型',
    parameters: [
      {
        name: 'learningRate',
        type: 'number',
        label: '学习率',
        defaultValue: 0.01,
        required: true,
        min: 0.001,
        max: 1.0
      },
      {
        name: 'regularization',
        type: 'select',
        label: '正则化方法',
        defaultValue: 'none',
        required: false,
        options: [
          { label: '无', value: 'none' },
          { label: 'L1正则化', value: 'l1' },
          { label: 'L2正则化', value: 'l2' }
        ]
      }
    ],
    isEnabled: true,
    createTime: formatDate(new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000)),
    updateTime: formatDate(new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000))
  })
  
  return configs
}

// 默认导出所有模拟数据
export const mockData = {
  dataAnalysisItems: generateDataAnalysisItems(),
  historyRecords: [] as HistoryRecord[], // 将在组件中根据分析项生成
  algorithmConfigs: generateAlgorithmConfigs()
}
