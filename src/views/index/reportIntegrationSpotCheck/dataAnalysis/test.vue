<script setup lang="ts" name="dataAnalysisTest">
import DataAnalysisIndex from './index.vue'

// 测试页面，用于验证数据分析管理功能
</script>

<template>
  <div class="data-analysis-test">
    <!-- 测试说明 -->
    <el-alert
      title="数据分析管理功能测试页面"
      type="info"
      :closable="false"
      show-icon
      style="margin-bottom: 20px"
    >
      <template #default>
        <div>
          <p><strong>测试功能包括：</strong></p>
          <ul style="margin: 10px 0; padding-left: 20px;">
            <li>✅ 数据分析任务列表展示（25条模拟数据）</li>
            <li>✅ 搜索和筛选功能（按名称、类型、状态、创建人等）</li>
            <li>✅ 分页功能</li>
            <li>✅ 动态操作按钮（根据任务状态显示不同操作）</li>
            <li>✅ 新建数据分析任务</li>
            <li>✅ 编辑数据分析任务</li>
            <li>✅ 任务状态管理（启动、暂停、恢复、终止等）</li>
            <li>✅ 历史操作记录查看</li>
            <li>✅ 算法配置管理</li>
            <li>✅ 本地数据持久化（页面刷新数据不丢失）</li>
          </ul>
          <p><strong>测试说明：</strong></p>
          <ul style="margin: 10px 0; padding-left: 20px;">
            <li>所有数据存储在浏览器localStorage中</li>
            <li>首次访问会自动初始化25条模拟数据</li>
            <li>可以通过浏览器开发者工具查看localStorage数据</li>
            <li>支持完整的CRUD操作</li>
          </ul>
        </div>
      </template>
    </el-alert>

    <!-- 数据分析管理组件 -->
    <DataAnalysisIndex />
  </div>
</template>

<route>
{
  meta: {
    title: '数据分析管理测试',
  },
}
</route>

<style scoped lang="scss">
.data-analysis-test {
  padding: 20px;
}
</style>
