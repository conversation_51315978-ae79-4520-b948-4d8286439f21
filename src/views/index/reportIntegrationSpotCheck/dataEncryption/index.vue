<script setup lang="ts" name="dataEncryption">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'

// 路由实例
const router = useRouter()

// 数据加密规则接口
interface EncryptionRule {
  id: string
  index: number
  ruleName: string
  ruleDescription: string
  ruleLogic: string
  createTime: string
  updateTime: string
}

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const tableHeight = ref(0)
const tableRef = ref()
const selectedRows = ref<EncryptionRule[]>([])

// 搜索表单
const searchFormProp = ref([
  { label: '加密规则名称', prop: 'ruleName', type: 'text' }
])
const searchForm = ref({ ruleName: '' })

// 表头配置
const columns = [
  { prop: 'index', label: '序号', width: 80 },
  { prop: 'ruleName', label: '加密规则名称', minWidth: 200 },
  { prop: 'ruleDescription', label: '加密规则说明', minWidth: 250 },
  { prop: 'ruleLogic', label: '加密规则逻辑', minWidth: 300 },
  { prop: 'createTime', label: '创建时间', minWidth: 150 },
  { prop: 'updateTime', label: '更新时间', minWidth: 150 }
]

// 分页配置
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 表格数据
const tableData = ref<EncryptionRule[]>([])

// 对话框相关
const showDialogForm = ref(false)
const dialogTitle = ref('创建数据加密规则')
const dialogForm = ref<Partial<EncryptionRule>>({})
const dialogFormRef = ref()
const dialogLoading = ref(false)

// 查看对话框相关
const showViewDialog = ref(false)
const viewDialogData = ref<EncryptionRule | null>(null)

// 表单配置
const dialogFormProps = [
  { label: '加密规则名称', prop: 'ruleName', type: 'text', required: true },
  { label: '加密规则说明', prop: 'ruleDescription', type: 'textarea', required: false },
  { label: '加密规则逻辑', prop: 'ruleLogic', type: 'textarea', required: true }
]

// 表单验证规则
const dialogFormRules = {
  ruleName: [
    { required: true, message: '请输入加密规则名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  ruleLogic: [
    { required: true, message: '请输入加密规则逻辑', trigger: 'blur' }
  ]
}

// 操作按钮配置
const buttons = [
  { label: '查看', type: 'info', code: 'view' },
  { label: '编辑', type: 'primary', code: 'edit' },
  { label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除吗?' }
]

// 计算分页数据
const paginatedData = computed(() => {
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return tableData.value.slice(start, end)
})

// 生成模拟数据
const generateMockData = (): EncryptionRule[] => {
  const mockRules = [
    {
      ruleName: '用户密码加密规则',
      ruleDescription: '对用户登录密码进行哈希加密处理，确保密码安全存储',
      ruleLogic: 'def encrypt_password(password, salt):\n    return hashlib.pbkdf2_hmac("sha256", password.encode(), salt, 100000)'
    },
    {
      ruleName: '身份证号加密规则',
      ruleDescription: '对身份证号码进行AES-256对称加密，保护个人隐私信息',
      ruleLogic: 'def encrypt_id_card(id_number, key):\n    cipher = AES.new(key, AES.MODE_CBC)\n    return cipher.encrypt(pad(id_number, AES.block_size))'
    },
    {
      ruleName: '银行卡号加密规则',
      ruleDescription: '对银行卡号进行RSA非对称加密，确保金融数据安全',
      ruleLogic: 'def encrypt_bank_card(card_number, public_key):\n    return rsa.encrypt(card_number.encode(), public_key)'
    },
    {
      ruleName: '手机号码加密规则',
      ruleDescription: '对手机号码进行AES加密，保护用户联系方式',
      ruleLogic: 'def encrypt_phone(phone_number, key):\n    cipher = AES.new(key, AES.MODE_ECB)\n    return cipher.encrypt(pad(phone_number, AES.block_size))'
    },
    {
      ruleName: '邮箱地址加密规则',
      ruleDescription: '对邮箱地址进行DES加密处理',
      ruleLogic: 'def encrypt_email(email, key):\n    cipher = DES.new(key, DES.MODE_ECB)\n    return cipher.encrypt(pad(email, DES.block_size))'
    },
    {
      ruleName: '财务数据加密规则',
      ruleDescription: '对财务相关数据进行混合加密，使用AES+RSA组合',
      ruleLogic: 'def encrypt_financial_data(data, aes_key, rsa_public_key):\n    # 先用AES加密数据\n    aes_encrypted = AES.new(aes_key, AES.MODE_CBC).encrypt(pad(data, AES.block_size))\n    # 再用RSA加密AES密钥\n    encrypted_key = rsa.encrypt(aes_key, rsa_public_key)\n    return aes_encrypted, encrypted_key'
    },
    {
      ruleName: '地址信息加密规则',
      ruleDescription: '对用户地址信息进行3DES加密保护',
      ruleLogic: 'def encrypt_address(address, key):\n    cipher = DES3.new(key, DES3.MODE_CBC)\n    return cipher.encrypt(pad(address, DES3.block_size))'
    },
    {
      ruleName: '业务数据MD5规则',
      ruleDescription: '对业务数据进行MD5哈希处理，用于数据完整性校验',
      ruleLogic: 'def encrypt_business_data(data):\n    return hashlib.md5(data.encode()).hexdigest()'
    },
    {
      ruleName: 'API接口数据加密',
      ruleDescription: '对API接口传输的敏感数据进行AES加密',
      ruleLogic: 'def encrypt_api_data(data, secret_key):\n    cipher = AES.new(secret_key, AES.MODE_GCM)\n    ciphertext, tag = cipher.encrypt_and_digest(data.encode())\n    return ciphertext, tag, cipher.nonce'
    },
    {
      ruleName: '多字段组合加密规则',
      ruleDescription: '对多个敏感字段进行组合加密处理',
      ruleLogic: 'def encrypt_multiple_fields(fields_data, key):\n    combined_data = "|".join(fields_data.values())\n    cipher = AES.new(key, AES.MODE_CBC)\n    return cipher.encrypt(pad(combined_data, AES.block_size))'
    }
  ]

  return mockRules.map((rule, index) => ({
    id: `rule_${Date.now()}_${index}`,
    index: index + 1,
    ruleName: rule.ruleName,
    ruleDescription: rule.ruleDescription,
    ruleLogic: rule.ruleLogic,
    createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString('zh-CN'),
    updateTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toLocaleString('zh-CN')
  }))
}

// 初始化数据
const initData = () => {
  const savedData = localStorage.getItem('dataEncryptionRules')
  if (savedData) {
    tableData.value = JSON.parse(savedData)
  } else {
    tableData.value = generateMockData()
    saveToLocalStorage()
  }
  updatePagination()
}

// 保存到localStorage
const saveToLocalStorage = () => {
  localStorage.setItem('dataEncryptionRules', JSON.stringify(tableData.value))
}

// 更新分页信息
const updatePagination = () => {
  pagination.total = tableData.value.length
}

// 返回上一页
const handleGoBack = () => {
  router.push('/reportIntegrationSpotCheck')
}

// Block高度变化处理
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 120
}

// 搜索功能
const onSearch = async () => {
  try {
    searchLoading.value = true
    await new Promise(resolve => setTimeout(resolve, 500))
    
    let filteredData = JSON.parse(localStorage.getItem('dataEncryptionRules') || '[]')
    
    if (searchForm.value.ruleName) {
      filteredData = filteredData.filter((item: EncryptionRule) =>
        item.ruleName.includes(searchForm.value.ruleName)
      )
    }
    
    tableData.value = filteredData
    pagination.page = 1
    updatePagination()
    
    ElMessage.success(`搜索完成，共找到 ${filteredData.length} 条记录`)
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    searchLoading.value = false
  }
}

// 重置搜索
const onReset = () => {
  searchForm.value = { ruleName: '' }
  initData()
}

// 新增规则
const onClickAdd = () => {
  dialogTitle.value = '创建数据加密规则'
  dialogForm.value = {}
  showDialogForm.value = true
}

// 表格操作按钮点击
const onTableClickButton = ({ row, btn }: any) => {
  switch (btn.code) {
    case 'view':
      viewRule(row)
      break
    case 'edit':
      editRule(row)
      break
    case 'delete':
      deleteRule(row)
      break
  }
}

// 查看规则
const viewRule = (row: EncryptionRule) => {
  viewDialogData.value = { ...row }
  showViewDialog.value = true
}

// 编辑规则
const editRule = (row: EncryptionRule) => {
  dialogTitle.value = '编辑数据加密规则'
  dialogForm.value = { ...row }
  showDialogForm.value = true
}

// 删除规则
const deleteRule = (row: EncryptionRule) => {
  const index = tableData.value.findIndex(item => item.id === row.id)
  if (index > -1) {
    tableData.value.splice(index, 1)
    saveToLocalStorage()
    updatePagination()
    ElMessage.success('删除成功')
  }
}

// 表单提交
const onDialogConfirm = () => {
  dialogFormRef.value.validate((valid: boolean) => {
    if (valid) {
      dialogLoading.value = true
      
      setTimeout(() => {
        const currentTime = new Date().toLocaleString('zh-CN')
        
        if (dialogForm.value.id) {
          // 编辑模式
          const index = tableData.value.findIndex(item => item.id === dialogForm.value.id)
          if (index > -1) {
            tableData.value[index] = {
              ...tableData.value[index],
              ...dialogForm.value,
              updateTime: currentTime
            } as EncryptionRule
          }
          ElMessage.success('编辑成功')
        } else {
          // 新增模式
          const newRule: EncryptionRule = {
            id: `rule_${Date.now()}`,
            index: tableData.value.length + 1,
            ruleName: dialogForm.value.ruleName!,
            ruleDescription: dialogForm.value.ruleDescription || '',
            ruleLogic: dialogForm.value.ruleLogic!,
            createTime: currentTime,
            updateTime: currentTime
          }
          tableData.value.push(newRule)
          ElMessage.success('创建成功')
        }
        
        saveToLocalStorage()
        updatePagination()
        showDialogForm.value = false
        dialogLoading.value = false
      }, 1000)
    }
  })
}

// 表格选择变化
const handleSelectionChange = (selection: EncryptionRule[]) => {
  selectedRows.value = selection
}

// 导出功能
const onExport = async () => {
  try {
    loading.value = true

    // 如果有选中数据，导出选中的；否则导出全部数据
    const dataToExport = selectedRows.value.length > 0 ? selectedRows.value : tableData.value

    if (dataToExport.length === 0) {
      ElMessage.warning('没有数据可以导出')
      return
    }

    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    workbook.creator = 'Data Encryption System'
    workbook.lastModifiedBy = 'System'
    workbook.created = new Date()
    workbook.modified = new Date()

    // 创建工作表
    const worksheet = workbook.addWorksheet('加密规则表')

    // 设置列定义
    worksheet.columns = [
      { header: '序号', key: 'index', width: 10 },
      { header: '加密规则名称', key: 'ruleName', width: 25 },
      { header: '加密规则说明', key: 'ruleDescription', width: 35 },
      { header: '加密规则逻辑', key: 'ruleLogic', width: 50 },
      { header: '创建时间', key: 'createTime', width: 20 },
      { header: '更新时间', key: 'updateTime', width: 20 }
    ]

    // 设置表头样式
    const headerRow = worksheet.getRow(1)
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    }
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' }
    headerRow.height = 25

    // 添加数据
    dataToExport.forEach((row, index) => {
      worksheet.addRow({
        index: index + 1,
        ruleName: row.ruleName,
        ruleDescription: row.ruleDescription,
        ruleLogic: row.ruleLogic,
        createTime: row.createTime,
        updateTime: row.updateTime
      })
    })

    // 设置数据行样式
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) { // 跳过表头
        row.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true }
        row.height = 30
      }

      // 设置边框
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 生成文件名：时间戳-加密规则表
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const fileName = `${timestamp}-加密规则表.xlsx`

    // 下载文件
    saveAs(blob, fileName)

    const exportType = selectedRows.value.length > 0 ? '选中' : '全部'
    ElMessage.success(`成功导出 ${exportType} ${dataToExport.length} 条数据`)
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    loading.value = false
  }
}

// 批量删除
const onBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的数据')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确认删除选中的 ${selectedRows.value.length} 条记录吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 800))
    
    const selectedIds = selectedRows.value.map(row => row.id)
    tableData.value = tableData.value.filter(item => !selectedIds.includes(item.id))
    
    saveToLocalStorage()
    updatePagination()
    selectedRows.value = []
    
    ElMessage.success('批量删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 分页变化
const handlePageChange = (page: number) => {
  pagination.page = page
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
}

// 生命周期
onMounted(() => {
  console.log('数据加密页面已挂载')
  initData()
  console.log('初始化数据完成，tableData:', tableData.value)
  console.log('分页数据:', paginatedData.value)
})
</script>

<route>
{
  meta: {
    title: '数据加密',
    ignoreLabel: false
  }
}
</route>

<template>
  <div class="data-encryption">
    <Block
      title="数据加密"
      :enable-expand-content="true"
      :enableBackButton="false"
      :enable-fixed-height="true"
      :enable-close-button="false"
      @content-expand="() => {}"
      @height-changed="onBlockHeightChanged"
    >
      <template #topRight>
        <el-button 
          size="small" 
          type="default" 
          @click="handleGoBack"
          style="margin-right: 8px"
        >
          <el-icon style="margin-right: 4px">
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>
        
        <el-button size="small" type="primary" @click="onClickAdd">
          新增规则
        </el-button>
        
        <el-button
          size="small"
          type="success"
          @click="onExport"
        >
          导出{{ selectedRows.length > 0 ? `(${selectedRows.length}条)` : '(全部)' }}
        </el-button>
        
        <el-button 
          size="small" 
          type="danger" 
          @click="onBatchDelete" 
          :disabled="selectedRows.length === 0"
        >
          批量删除
        </el-button>
      </template>

      <template #expand>
        <!-- 搜索区域 -->
        <div class="search" v-loading="searchLoading" element-loading-background="rgba(255, 255, 255, 0.8)">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="2"
            :label-width="120"
            :enable-reset="false"
            confirm-text="查询"
            reset-text="重置"
            button-vertical="flowing"
            @submit="onSearch"
            @reset="onReset"
          />
        </div>
      </template>

      <!-- 表格列表 -->
      <TableV2
        ref="tableRef"
        :columns="columns"
        :defaultTableData="paginatedData"
        :enable-toolbar="false"
        :enable-own-button="false"
        :enable-selection="true"
        :enable-index="false"
        :height="tableHeight"
        :buttons="buttons"
        :loading="loading"
        @click-button="onTableClickButton"
        @selection-change="handleSelectionChange"
      >
        <!-- 序号列 -->
        <template #index="{ row }">
          {{ (pagination.page - 1) * pagination.size + row.index }}
        </template>

        <!-- 加密规则逻辑列 -->
        <template #ruleLogic="{ row }">
          <div class="rule-logic-cell">
            <el-tooltip v-if="row.ruleLogic && row.ruleLogic.length > 50" :content="row.ruleLogic" placement="top" :show-after="500">
              <span class="rule-logic-text">{{ row.ruleLogic.substring(0, 50) }}...</span>
            </el-tooltip>
            <span v-else class="rule-logic-text">{{ row.ruleLogic || '' }}</span>
          </div>
        </template>
      </TableV2>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </Block>

    <!-- 创建/编辑对话框 -->
    <Dialog
      v-model="showDialogForm"
      :title="dialogTitle"
      :destroy-on-close="true"
      :loading="dialogLoading"
      loading-text="保存中"
      width="600px"
      @closed="dialogForm = {}"
      @click-confirm="onDialogConfirm"
    >
      <Form
        ref="dialogFormRef"
        v-model="dialogForm"
        :props="dialogFormProps"
        :rules="dialogFormRules"
        :enable-button="false"
        :column-count="1"
        :label-width="120"
      />
    </Dialog>

    <!-- 查看对话框 -->
    <Dialog
      v-model="showViewDialog"
      title="查看加密规则"
      :destroy-on-close="true"
      :enable-confirm="false"
      width="600px"
      @closed="viewDialogData = null"
    >
      <div v-if="viewDialogData" class="view-form">
        <el-form :model="viewDialogData" label-width="120px">
          <el-form-item label="加密规则名称">
            <el-input v-model="viewDialogData.ruleName" disabled />
          </el-form-item>
          <el-form-item label="加密规则说明">
            <el-input
              v-model="viewDialogData.ruleDescription"
              type="textarea"
              :rows="3"
              disabled
              placeholder="暂无说明"
            />
          </el-form-item>
          <el-form-item label="加密规则逻辑">
            <el-input
              v-model="viewDialogData.ruleLogic"
              type="textarea"
              :rows="5"
              disabled
            />
          </el-form-item>
          <el-form-item label="创建时间">
            <el-input v-model="viewDialogData.createTime" disabled />
          </el-form-item>
          <el-form-item label="更新时间">
            <el-input v-model="viewDialogData.updateTime" disabled />
          </el-form-item>
        </el-form>
      </div>
    </Dialog>
  </div>
</template>

<style lang="scss" scoped>
.data-encryption {
  height: 100%;

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    padding: 16px 0;
  }

  .rule-logic-cell {
    .rule-logic-text {
      font-family: 'Courier New', monospace;
      font-size: 12px;
      color: #606266;
      line-height: 1.4;
      white-space: pre-wrap;
    }
  }

  .view-form {
    .el-textarea__inner {
      min-height: 80px;
      font-family: 'Courier New', monospace;
    }
  }
}

:deep(.el-table) {
  .el-table__body-wrapper {
    overflow-x: auto;
  }
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-textarea__inner) {
  min-height: 80px;
  font-family: 'Courier New', monospace;
}
</style>
