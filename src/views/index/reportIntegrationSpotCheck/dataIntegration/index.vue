<script setup lang="ts" name="dataIntegration">
import ExcelJS from 'exceljs'
import { useUserStore } from '@/stores/useUserStore'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Plus, View, Edit, Delete, Warning, Check, Search, Setting } from '@element-plus/icons-vue'
import { watch } from 'vue'


// 路由实例
const router = useRouter()

// 数据映射关系接口定义
interface FieldMapping {
  id: string
  sourceField: string
  sourceType: string
  targetField: string
  targetType: string
}

// 数据集成任务数据类型定义
interface DataIntegrationTask {
  id: string
  index: number
  taskName: string
  createTime: string
  progress: number
  status: string
  hasException: boolean
  logGenerated: boolean
  reportGenerated: boolean
  // 数据库连接配置
  serverAddress: string
  port: number
  username: string
  password: string
  tableName: string
  // 数据集成权限配置
  permissions: string[]
  // 数据映射表配置
  fieldMappings: FieldMapping[]
}

// 数据集成规则数据类型定义
interface IntegrationRule {
  id: string
  index: number
  ruleName: string
  ruleType: string
  sourceSystem: string
  targetSystem: string
  ruleContent: string
  isEnabled: boolean
  createTime: string
  updateTime: string
  creator: string
}

// 搜索表单
const searchFormProp = ref([
  { label: '集成任务名称', prop: 'taskName', type: 'text' },
  { 
    label: '状态', 
    prop: 'status', 
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '进行中', value: '进行中' },
      { label: '已停止', value: '已停止' },
      { label: '已完成', value: '已完成' }
    ]
  }
])
const searchForm = ref({ taskName: '', status: '' })

// 加载状态
const loading = ref(false)
const searchLoading = ref(false)

// 表格ref
const tableRef = ref()
const tableHeight = ref(0)

// 表头配置
const columns = [
  { prop: 'index', label: '序号', width: 80 },
  { prop: 'taskName', label: '集成任务名称', minWidth: 200 },
  { prop: 'createTime', label: '创建时间', width: 180 },
  { prop: 'progress', label: '进程', minWidth: 150 },
  { prop: 'status', label: '状态', minWidth: 120 },
  { prop: 'logAction', label: '日志', width: 120 },
  { prop: 'reportAction', label: '数据质量报告', width: 200 },
  { prop: 'actions', label: '操作', width: 200, fixed: 'right' }
]

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 列表请求参数
const reqParams = reactive({
  taskName: '',
  status: '',
  skipCount: 0,
  maxResultCount: 10
})

// 表格数据
const tableData = ref<DataIntegrationTask[]>([])
const selectedRows = ref<DataIntegrationTask[]>([])

// 日志弹窗相关
const showLogDialog = ref(false)
const currentTaskLog = ref<any[]>([])
const currentTaskName = ref('')
const currentTaskId = ref('')
const logSearchKeyword = ref('')
const logSearchForm = ref({
  keyword: ''
})

// 日志搜索表单配置
const logSearchFormProp = ref([
  {
    label: '搜索关键词',
    prop: 'keyword',
    type: 'text',
    placeholder: '搜索操作人员、操作类型或详情'
  }
])
const logPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})
const allTaskLogs = ref<any[]>([])

// 日志数据接口定义
interface OperationLog {
  index: number
  operationType: string
  operationTime: string
  operator: string
  operationResult: string
  operationDetail: string
}

// 日志表格列配置
const logColumns = [
  { prop: 'index', label: '序号', width: 80 },
  { prop: 'operationType', label: '操作类型', width: 120 },
  { prop: 'operationTime', label: '操作时间', width: 180 },
  { prop: 'operator', label: '操作人员', width: 120 },
  { prop: 'operationResult', label: '操作结果', width: 120 },
  { prop: 'operationDetail', label: '详细描述', minWidth: 200 }
]

// 增删改查相关状态
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const showDetailDialog = ref(false)
const currentEditTask = ref<DataIntegrationTask | null>(null)
const currentDetailTask = ref<DataIntegrationTask | null>(null)

// 规则设置相关状态
const showRuleSettingsDialog = ref(false)
const showRuleFormDialog = ref(false)
const ruleSearchForm = ref({ ruleName: '' })
const ruleLoading = ref(false)
const selectedRules = ref<IntegrationRule[]>([])
const currentEditRule = ref<IntegrationRule | null>(null)
const ruleTableHeight = ref(400)
const ruleSortField = ref('createTime')
const ruleSortOrder = ref('desc')

// 表单数据
const createForm = ref({
  taskName: '',
  status: '进行中',
  progress: 0,
  hasException: false,
  // 数据库连接配置
  serverAddress: '',
  port: 3306,
  username: '',
  password: '',
  tableName: '',
  // 权限配置
  permissions: [] as string[],
  // 字段映射配置
  fieldMappings: [] as FieldMapping[]
})

const editForm = ref({
  taskName: '',
  status: '',
  progress: 0,
  hasException: false,
  // 数据库连接配置
  serverAddress: '',
  port: 3306,
  username: '',
  password: '',
  tableName: '',
  // 权限配置
  permissions: [] as string[],
  // 字段映射配置
  fieldMappings: [] as FieldMapping[]
})

// 表单验证规则
const createFormRules = {
  taskName: [
    { required: true, message: '请输入集成任务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '任务名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  serverAddress: [
    { required: true, message: '请输入服务器地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  tableName: [
    { required: true, message: '请输入表名', trigger: 'blur' }
  ],
  permissions: [
    { required: true, message: '请至少选择一个权限', trigger: 'change' }
  ]
}

const editFormRules = {
  taskName: [
    { required: true, message: '请输入集成任务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '任务名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  serverAddress: [
    { required: true, message: '请输入服务器地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  tableName: [
    { required: true, message: '请输入表名', trigger: 'blur' }
  ],
  permissions: [
    { required: true, message: '请至少选择一个权限', trigger: 'change' }
  ]
}

// 获取当前用户信息
const userStore = useUserStore()
const getCurrentOperatorName = (): string => {
  const userInfo = userStore.getUserInfo
  if (userInfo) {
    return userInfo.displayName || userInfo.name || userInfo.account || '未知用户'
  }
  return '未知用户'
}

// 字段映射管理函数
const addFieldMapping = (formType: 'create' | 'edit') => {
  const sourceFields = ['用户ID', '用户名', '创建时间', '更新时间', '状态', '部门ID', '角色ID', '邮箱', '手机号', '地址']
  const targetFields = ['user_id', 'username', 'create_time', 'update_time', 'status', 'dept_id', 'role_id', 'email', 'phone', 'address']
  const dataTypes = ['VARCHAR', 'INT', 'DATETIME', 'TEXT', 'BIGINT']

  const newMapping: FieldMapping = {
    id: `mapping_${Date.now()}`,
    sourceField: sourceFields[0],
    sourceType: dataTypes[0],
    targetField: targetFields[0],
    targetType: dataTypes[0]
  }

  if (formType === 'create') {
    createForm.value.fieldMappings.push(newMapping)
  } else {
    editForm.value.fieldMappings.push(newMapping)
  }
}

const removeFieldMapping = (formType: 'create' | 'edit', index: number) => {
  if (formType === 'create') {
    createForm.value.fieldMappings.splice(index, 1)
  } else {
    editForm.value.fieldMappings.splice(index, 1)
  }
}

// 获取可选字段选项
const getFieldOptions = () => {
  return [
    { label: '用户ID', value: '用户ID' },
    { label: '用户名', value: '用户名' },
    { label: '创建时间', value: '创建时间' },
    { label: '更新时间', value: '更新时间' },
    { label: '状态', value: '状态' },
    { label: '部门ID', value: '部门ID' },
    { label: '角色ID', value: '角色ID' },
    { label: '邮箱', value: '邮箱' },
    { label: '手机号', value: '手机号' },
    { label: '地址', value: '地址' }
  ]
}

const getTargetFieldOptions = () => {
  return [
    { label: 'user_id', value: 'user_id' },
    { label: 'username', value: 'username' },
    { label: 'create_time', value: 'create_time' },
    { label: 'update_time', value: 'update_time' },
    { label: 'status', value: 'status' },
    { label: 'dept_id', value: 'dept_id' },
    { label: 'role_id', value: 'role_id' },
    { label: 'email', value: 'email' },
    { label: 'phone', value: 'phone' },
    { label: 'address', value: 'address' }
  ]
}

const getDataTypeOptions = () => {
  return [
    { label: 'VARCHAR', value: 'VARCHAR' },
    { label: 'INT', value: 'INT' },
    { label: 'DATETIME', value: 'DATETIME' },
    { label: 'TEXT', value: 'TEXT' },
    { label: 'BIGINT', value: 'BIGINT' }
  ]
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 生成模拟字段映射数据
const generateMockFieldMappings = (): FieldMapping[] => {
  const sourceFields = ['用户ID', '用户名', '创建时间', '更新时间', '状态', '部门ID', '角色ID', '邮箱', '手机号', '地址']
  const targetFields = ['user_id', 'username', 'create_time', 'update_time', 'status', 'dept_id', 'role_id', 'email', 'phone', 'address']
  const dataTypes = ['VARCHAR', 'INT', 'DATETIME', 'TEXT', 'BIGINT']

  const mappings: FieldMapping[] = []
  const fieldCount = Math.floor(Math.random() * 5) + 3 // 3-7个字段映射

  for (let i = 0; i < fieldCount; i++) {
    mappings.push({
      id: `mapping_${Date.now()}_${i}`,
      sourceField: sourceFields[i % sourceFields.length],
      sourceType: dataTypes[Math.floor(Math.random() * dataTypes.length)],
      targetField: targetFields[i % targetFields.length],
      targetType: dataTypes[Math.floor(Math.random() * dataTypes.length)]
    })
  }

  return mappings
}

// 生成模拟数据
const generateMockData = () => {
  const statuses = ['进行中', '已停止', '已完成']
  const servers = ['*************', '*********', '***********', 'localhost']
  const usernames = ['admin', 'root', 'dbuser', 'integration_user']
  const tableNames = ['user_info', 'dept_data', 'role_config', 'system_log', 'business_data']

  const data = []
  for (let i = 1; i <= 50; i++) {
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    const progress = status === '已完成' ? 100 :
                    status === '进行中' ? Math.floor(Math.random() * 90) + 10 :
                    Math.floor(Math.random() * 60) + 10

    const hasException = Math.random() < 0.15 && status === '已停止' // 15%概率有异常，且只有已停止状态才有异常

    // 随机生成权限配置
    const allPermissions = ['读取', '写入', '删除']
    const permissions: string[] = []
    const permissionCount = Math.floor(Math.random() * 3) + 1 // 至少1个权限
    for (let j = 0; j < permissionCount; j++) {
      const permission = allPermissions[Math.floor(Math.random() * allPermissions.length)]
      if (!permissions.includes(permission)) {
        permissions.push(permission)
      }
    }

    data.push({
      id: `integration_task_${i}`,
      index: i,
      taskName: `数据集成任务${i}`,
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString('zh-CN'),
      progress: progress,
      status: status,
      hasException: hasException,
      logGenerated: false,
      reportGenerated: false,
      // 数据库连接配置
      serverAddress: servers[Math.floor(Math.random() * servers.length)],
      port: Math.random() > 0.5 ? 3306 : 5432,
      username: usernames[Math.floor(Math.random() * usernames.length)],
      password: '******', // 模拟密码显示
      tableName: tableNames[Math.floor(Math.random() * tableNames.length)],
      // 权限配置
      permissions: permissions,
      // 字段映射配置
      fieldMappings: generateMockFieldMappings()
    })
  }
  return data
}

// 初始化数据
const initData = () => {
  try {
    const savedData = localStorage.getItem('dataIntegrationTaskData')
    if (savedData) {
      const parsedData = JSON.parse(savedData)
      // 验证数据格式
      if (Array.isArray(parsedData)) {
        tableData.value = parsedData.filter(item =>
          item &&
          typeof item.id === 'string' &&
          typeof item.taskName === 'string' &&
          typeof item.createTime === 'string'
        )
      } else {
        throw new Error('数据格式不正确')
      }
    } else {
      tableData.value = generateMockData()
      localStorage.setItem('dataIntegrationTaskData', JSON.stringify(tableData.value))
    }
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.warning('数据加载异常，已重新生成模拟数据')
    tableData.value = generateMockData()
    localStorage.setItem('dataIntegrationTaskData', JSON.stringify(tableData.value))
  }

  // 重置搜索参数
  reqParams.taskName = ''
  reqParams.status = ''

  // 更新分页信息
  updatePagination()
}

// 获取过滤后的数据
const getFilteredData = () => {
  let filtered = [...tableData.value]

  if (reqParams.taskName) {
    filtered = filtered.filter(item =>
      item.taskName.includes(reqParams.taskName)
    )
  }

  if (reqParams.status) {
    filtered = filtered.filter(item => item.status === reqParams.status)
  }

  return filtered
}

// 获取过滤后的数据 - 计算属性
const filteredData = computed(() => {
  return getFilteredData()
})

// 计算分页数据
const paginatedData = computed(() => {
  const filtered = filteredData.value
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return filtered.slice(start, end)
})

// 更新分页信息
const updatePagination = () => {
  const filtered = filteredData.value
  pagination.total = filtered.length
  // 如果当前页超出范围，回到第一页
  if (pagination.page > Math.ceil(filtered.length / pagination.size) && filtered.length > 0) {
    pagination.page = 1
  }
}

// 监听过滤数据变化，更新分页
watch(filteredData, () => {
  updatePagination()
}, { immediate: true })



// 查询
const onSearch = async () => {
  try {
    searchLoading.value = true

    // 模拟搜索延迟
    await new Promise(resolve => setTimeout(resolve, 800))

    // 更新搜索参数
    reqParams.taskName = searchForm.value.taskName
    reqParams.status = searchForm.value.status

    // 重置分页到第一页并更新分页信息
    pagination.page = 1
    updatePagination()

    ElMessage.success('搜索完成')
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    searchLoading.value = false
  }
}

// 重置搜索
const onReset = () => {
  searchForm.value = {
    taskName: '',
    status: ''
  }
  reqParams.taskName = ''
  reqParams.status = ''
  pagination.page = 1
  updatePagination()
  ElMessage.success('重置完成')
}

// 分页变化
const handlePageChange = (page: number) => {
  pagination.page = page
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
}

// 块高度变化事件
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 75
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 通用日志记录函数
const recordOperation = (taskId: string, operationType: string, operationDetail: string, operationResult: string = '成功'): OperationLog | null => {
  try {
    // 参数验证
    if (!taskId || !operationType || !operationDetail) {
      console.error('日志记录参数不完整')
      return null
    }

    // 获取现有日志
    const taskLogsStr = localStorage.getItem('dataIntegrationTaskLogs') || '{}'
    const taskLogs = JSON.parse(taskLogsStr)
    const logs = taskLogs[taskId] || []

    // 随机操作人员
    const operators = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '陈九', '周十', '吴小明', '刘小红']
    const operator = operators[Math.floor(Math.random() * operators.length)]

    // 创建新日志
    const newLog: OperationLog = {
      index: logs.length + 1,
      operationType,
      operationTime: new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      operator,
      operationResult,
      operationDetail
    }

    // 添加到日志列表
    logs.unshift(newLog)

    // 更新索引
    logs.forEach((log: OperationLog, index: number) => {
      log.index = index + 1
    })

    // 保存日志
    taskLogs[taskId] = logs
    localStorage.setItem('dataIntegrationTaskLogs', JSON.stringify(taskLogs))

    // 更新任务状态
    const index = tableData.value.findIndex(item => item.id === taskId)
    if (index !== -1) {
      tableData.value[index].logGenerated = true
      localStorage.setItem('dataIntegrationTaskData', JSON.stringify(tableData.value))
    }

    return newLog
  } catch (error) {
    console.error('记录操作日志失败:', error)
    ElMessage.error('记录操作日志失败')
    return null
  }
}

// 生成日志
const generateLog = (row: DataIntegrationTask) => {
  const index = tableData.value.findIndex(item => item.id === row.id)
  if (index !== -1) {
    // 生成模拟日志数据
    generateTaskLogs(row.id)

    // 更新任务状态
    tableData.value[index].logGenerated = true
    localStorage.setItem('dataIntegrationTaskData', JSON.stringify(tableData.value))

    ElMessage.success('日志生成成功')
  }
}

// 查看日志
const viewLog = (row: DataIntegrationTask) => {
  currentTaskName.value = row.taskName
  currentTaskId.value = row.id
  logSearchKeyword.value = ''
  logSearchForm.value.keyword = ''
  logPagination.page = 1
  logPagination.size = 10

  // 获取日志数据
  allTaskLogs.value = getTaskLogs(row.id)
  currentTaskLog.value = getCurrentPageLogs()

  showLogDialog.value = true
}

// 生成任务日志数据
const generateTaskLogs = (taskId: string) => {
  const operationTypes = ['数据新增', '数据导出', '数据修改', '数据查看', '数据删除', '数据抽取', '数据清洗', '数据转换', '数据加载', '质量检查']
  const operators = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '陈九', '周十', '吴小明', '刘小红']
  const operationResults = ['成功', '失败', '部分成功']

  const details = {
    '数据新增': '向系统中新增数据记录',
    '数据导出': '从系统中导出数据到外部文件',
    '数据修改': '修改现有数据记录的内容',
    '数据查看': '查看和浏览数据记录详情',
    '数据删除': '从系统中删除指定数据记录',
    '数据抽取': '从源系统抽取数据进行处理',
    '数据清洗': '清洗数据质量问题，修复异常记录',
    '数据转换': '按照映射规则转换数据格式',
    '数据加载': '将处理后的数据加载到目标系统',
    '质量检查': '执行数据质量检查规则，验证数据完整性'
  }

  const logs: OperationLog[] = []
  const logCount = Math.floor(Math.random() * 8) + 5 // 5-12条日志

  for (let i = 0; i < logCount; i++) {
    const operationType = operationTypes[Math.floor(Math.random() * operationTypes.length)]
    const baseTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
    const operationTime = new Date(baseTime.getTime() - i * 30 * 60 * 1000)
    const operationResult = operationResults[Math.floor(Math.random() * operationResults.length)]

    logs.push({
      index: i + 1,
      operationType,
      operationTime: operationTime.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      operator: operators[Math.floor(Math.random() * operators.length)],
      operationResult,
      operationDetail: details[operationType as keyof typeof details]
    })
  }

  // 保存日志数据
  const taskLogs = JSON.parse(localStorage.getItem('dataIntegrationTaskLogs') || '{}')
  taskLogs[taskId] = logs.sort((a, b) => new Date(b.operationTime).getTime() - new Date(a.operationTime).getTime())
  localStorage.setItem('dataIntegrationTaskLogs', JSON.stringify(taskLogs))
}

// 获取任务日志
const getTaskLogs = (taskId: string): OperationLog[] => {
  try {
    const taskLogsStr = localStorage.getItem('dataIntegrationTaskLogs')
    if (!taskLogsStr) return []

    const taskLogs = JSON.parse(taskLogsStr)
    const logs = taskLogs[taskId] || []

    // 验证日志数据格式
    return logs.filter((log: any) => {
      return log &&
             typeof log.index === 'number' &&
             typeof log.operationType === 'string' &&
             typeof log.operationTime === 'string' &&
             typeof log.operator === 'string' &&
             typeof log.operationResult === 'string' &&
             typeof log.operationDetail === 'string'
    })
  } catch (error) {
    console.error('读取日志数据失败:', error)
    return []
  }
}

// 获取过滤后的日志数据
const getFilteredLogs = () => {
  const keyword = logSearchForm.value.keyword || logSearchKeyword.value
  if (!keyword) {
    return allTaskLogs.value
  }

  return allTaskLogs.value.filter(log =>
    log.operator.includes(keyword) ||
    log.operationType.includes(keyword) ||
    log.operationDetail.includes(keyword)
  )
}

// 获取当前页日志数据
const getCurrentPageLogs = () => {
  const filtered = getFilteredLogs()
  const start = (logPagination.page - 1) * logPagination.size
  const end = start + logPagination.size
  logPagination.total = filtered.length
  return filtered.slice(start, end)
}

// 获取操作类型颜色
const getOperationTypeColor = (operationType: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const colorMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    '数据新增': 'success',
    '数据导出': 'primary',
    '数据修改': 'warning',
    '数据查看': 'info',
    '数据删除': 'danger',
    '数据抽取': 'primary',
    '数据清洗': 'warning',
    '数据转换': 'info',
    '数据加载': 'success',
    '质量检查': 'warning'
  }
  return colorMap[operationType] || 'info'
}

// 日志搜索
const onLogSearch = () => {
  logPagination.page = 1
  currentTaskLog.value = getCurrentPageLogs()
}

// 日志分页变化
const onLogPageChange = (page: number) => {
  logPagination.page = page
  currentTaskLog.value = getCurrentPageLogs()
}

const onLogSizeChange = (size: number) => {
  logPagination.size = size
  logPagination.page = 1
  currentTaskLog.value = getCurrentPageLogs()
}

// 重置日志搜索
const onLogReset = () => {
  logSearchForm.value = { keyword: '' }
  currentTaskLog.value = [...allTaskLogs.value]
  logPagination.page = 1
  logPagination.total = allTaskLogs.value.length
}

// 生成报告数据
const generateReportData = (row: DataIntegrationTask) => {
  const checkItems = [
    '数据完整性检查',
    '数据一致性检查',
    '数据准确性检查',
    '数据时效性检查',
    '数据格式规范检查',
    '业务规则验证',
    '重复数据检查',
    '空值检查'
  ]

  const results = ['通过', '不通过', '部分通过']
  const riskLevels = ['低', '中', '高']
  const suggestions = [
    '建议完善数据录入流程',
    '建议增加数据校验规则',
    '建议定期清理重复数据',
    '建议建立数据质量监控机制',
    '建议加强业务培训',
    '建议优化数据采集流程',
    '建议建立数据标准化规范',
    '建议实施数据治理策略'
  ]

  return checkItems.map((item, index) => {
    const result = results[Math.floor(Math.random() * results.length)]
    const issueCount = result === '通过' ? 0 : Math.floor(Math.random() * 50) + 1
    const passRate = result === '通过' ? '100%' :
                    result === '不通过' ? `${Math.floor(Math.random() * 30) + 10}%` :
                    `${Math.floor(Math.random() * 40) + 50}%`
    const riskLevel = result === '通过' ? '低' :
                     result === '不通过' ? '高' : '中'

    return {
      checkItem: item,
      result,
      issueCount,
      passRate,
      riskLevel,
      suggestion: suggestions[index] || '建议联系技术支持'
    }
  })
}

// 生成数据质量报告
const generateReport = (row: DataIntegrationTask) => {
  const index = tableData.value.findIndex(item => item.id === row.id)
  if (index !== -1) {
    // 模拟报告生成延迟
    loading.value = true
    setTimeout(() => {
      tableData.value[index].reportGenerated = true
      localStorage.setItem('dataIntegrationTaskData', JSON.stringify(tableData.value))

      ElMessage.success('数据质量报告生成成功')
      loading.value = false
    }, 1500)
  }
}

// 导出报告为Excel
const exportReport = async (row: DataIntegrationTask) => {
  try {
    loading.value = true
    ElMessage.info(`正在导出"${row.taskName}"的数据质量报告...`)

    // 模拟报告数据生成
    const reportData = generateReportData(row)

    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    workbook.creator = 'Data Integration System'
    workbook.lastModifiedBy = 'System'
    workbook.created = new Date()
    workbook.modified = new Date()

    // 创建工作表
    const worksheet = workbook.addWorksheet('数据质量报告')

    // 设置报告标题
    worksheet.mergeCells('A1:F1')
    const titleCell = worksheet.getCell('A1')
    titleCell.value = `数据质量报告 - ${row.taskName}`
    titleCell.font = { bold: true, size: 16, color: { argb: '000000' } }
    titleCell.alignment = { horizontal: 'center', vertical: 'middle' }
    titleCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'E6F3FF' }
    }

    // 设置基本信息
    worksheet.getCell('A3').value = '任务ID:'
    worksheet.getCell('B3').value = row.id
    worksheet.getCell('A4').value = '任务名称:'
    worksheet.getCell('B4').value = row.taskName
    worksheet.getCell('A5').value = '创建时间:'
    worksheet.getCell('B5').value = row.createTime
    worksheet.getCell('A6').value = '当前状态:'
    worksheet.getCell('B6').value = row.status
    worksheet.getCell('A7').value = '执行进度:'
    worksheet.getCell('B7').value = `${row.progress}%`

    // 设置质量检查结果表头
    worksheet.getCell('A9').value = '质量检查结果'
    worksheet.mergeCells('A9:F9')
    const resultTitleCell = worksheet.getCell('A9')
    resultTitleCell.font = { bold: true, size: 14 }
    resultTitleCell.alignment = { horizontal: 'center' }
    resultTitleCell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'F0F0F0' }
    }

    // 设置表头
    const headers = ['检查项目', '检查结果', '问题数量', '通过率', '风险等级', '建议措施']
    headers.forEach((header, index) => {
      const cell = worksheet.getCell(10, index + 1)
      cell.value = header
      cell.font = { bold: true, color: { argb: 'FFFFFF' } }
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '4472C4' }
      }
      cell.alignment = { horizontal: 'center', vertical: 'middle' }
    })

    // 添加数据
    reportData.forEach((item, index) => {
      const rowIndex = 11 + index
      worksheet.getCell(rowIndex, 1).value = item.checkItem
      worksheet.getCell(rowIndex, 2).value = item.result
      worksheet.getCell(rowIndex, 3).value = item.issueCount
      worksheet.getCell(rowIndex, 4).value = item.passRate
      worksheet.getCell(rowIndex, 5).value = item.riskLevel
      worksheet.getCell(rowIndex, 6).value = item.suggestion
    })

    // 设置列宽
    worksheet.columns = [
      { width: 20 }, { width: 15 }, { width: 12 }, { width: 12 }, { width: 12 }, { width: 30 }
    ]

    // 设置边框
    const totalRows = 11 + reportData.length
    for (let row = 10; row <= totalRows; row++) {
      for (let col = 1; col <= 6; col++) {
        const cell = worksheet.getCell(row, col)
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      }
    }

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 生成文件名
    const now = new Date()
    const timestamp = now.toISOString().slice(0, 19).replace(/:/g, '-').replace('T', '-')
    const filename = `${timestamp}-${row.taskName}-数据质量报告.xlsx`

    // 下载文件
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success(`报告导出成功: ${filename}`)

    // 记录操作日志
    recordOperation(
      row.id,
      '数据导出',
      `导出数据质量报告"${row.taskName}"到Excel文件`,
      '成功'
    )
  } catch (error) {
    console.error('导出报告失败:', error)
    ElMessage.error('导出报告失败，请重试')

    // 记录失败日志
    recordOperation(
      row.id,
      '数据导出',
      `导出数据质量报告"${row.taskName}"到Excel文件`,
      '失败'
    )
  } finally {
    loading.value = false
  }
}

// 打印报告
const printReport = async (row: DataIntegrationTask) => {
  try {
    loading.value = true
    ElMessage.info(`正在准备打印"${row.taskName}"的数据质量报告...`)

    // 生成报告数据
    const reportData = generateReportData(row)

    // 创建新窗口用于打印
    const printWindow = window.open('', '_blank')
    if (!printWindow) {
      throw new Error('无法打开打印窗口，请检查浏览器弹窗设置')
    }

    // 生成表格行
    const generateTableRows = (data: any[]) => {
      return data.map((item, index) => `
        <tr>
          <td>${index + 1}</td>
          <td>${item.checkItem}</td>
          <td>${item.result}</td>
          <td>${item.issueCount}</td>
          <td>${item.passRate}</td>
          <td>${item.riskLevel}</td>
          <td>${item.suggestion}</td>
        </tr>
      `).join('')
    }

    // 创建打印文档内容
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>数据质量报告 - ${row.taskName}</title>
        <style>
          body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            color: #333;
          }
          .report-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #409EFF;
            padding-bottom: 20px;
          }
          .report-title {
            font-size: 24px;
            font-weight: bold;
            color: #409EFF;
            margin-bottom: 10px;
          }
          .report-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
          }
          .info-item {
            display: flex;
            align-items: center;
          }
          .info-label {
            font-weight: bold;
            margin-right: 10px;
            color: #606266;
            min-width: 80px;
          }
          .info-value {
            color: #303133;
          }
          .report-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
          .report-table th,
          .report-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
          }
          .report-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
            text-align: center;
          }
          .report-table tr:nth-child(even) {
            background-color: #f9f9f9;
          }
          .report-table tr:hover {
            background-color: #e6f7ff;
          }
          .report-footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 14px;
          }
          @media print {
            body { margin: 0; }
            .report-header { page-break-after: avoid; }
            .report-table { page-break-inside: avoid; }
          }
        </style>
      </head>
      <body>
        <div class="report-header">
          <div class="report-title">数据质量报告</div>
          <div style="color: #666; font-size: 16px;">${row.taskName}</div>
        </div>

        <div class="report-info">
          <div class="info-item">
            <span class="info-label">任务ID:</span>
            <span class="info-value">${row.id}</span>
          </div>
          <div class="info-item">
            <span class="info-label">任务名称:</span>
            <span class="info-value">${row.taskName}</span>
          </div>
          <div class="info-item">
            <span class="info-label">创建时间:</span>
            <span class="info-value">${row.createTime}</span>
          </div>
          <div class="info-item">
            <span class="info-label">当前状态:</span>
            <span class="info-value">${row.status}</span>
          </div>
          <div class="info-item">
            <span class="info-label">执行进度:</span>
            <span class="info-value">${row.progress}%</span>
          </div>
          <div class="info-item">
            <span class="info-label">报告生成时间:</span>
            <span class="info-value">${new Date().toLocaleString('zh-CN')}</span>
          </div>
        </div>

        <table class="report-table">
          <thead>
            <tr>
              <th>序号</th>
              <th>检查项目</th>
              <th>检查结果</th>
              <th>问题数量</th>
              <th>通过率</th>
              <th>风险等级</th>
              <th>建议措施</th>
            </tr>
          </thead>
          <tbody>
            ${generateTableRows(reportData)}
          </tbody>
        </table>

        <div class="report-footer">
          <p>本报告由重庆市数据集成系统自动生成</p>
          <p>打印时间：${new Date().toLocaleString('zh-CN')}</p>
        </div>
      </body>
      </html>
    `

    // 写入文档内容
    printWindow.document.open()
    printWindow.document.write(printContent)
    printWindow.document.close()

    // 等待内容加载完成后打印
    printWindow.onload = () => {
      printWindow.print()
      printWindow.close()
    }

    ElMessage.success(`已打开"${row.taskName}"数据质量报告打印预览窗口`)

    // 记录操作日志
    recordOperation(
      row.id,
      '数据打印',
      `打印数据质量报告"${row.taskName}"`,
      '成功'
    )
  } catch (error) {
    console.error('打印报告失败:', error)
    ElMessage.error('打印报告失败，请重试')

    // 记录失败日志
    recordOperation(
      row.id,
      '数据打印',
      `打印数据质量报告"${row.taskName}"`,
      '失败'
    )
  } finally {
    loading.value = false
  }
}

// 分享报告
const shareReport = async (row: DataIntegrationTask) => {
  try {
    loading.value = true
    ElMessage.info(`正在生成"${row.taskName}"的分享链接...`)

    // 模拟生成分享链接的过程
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 构建分享参数
    const shareParams = new URLSearchParams()
    shareParams.set('taskId', row.id)
    shareParams.set('taskName', row.taskName)
    shareParams.set('share', Date.now().toString())
    shareParams.set('type', 'report')

    // 生成分享链接
    const shareUrl = `${window.location.origin}/reportIntegrationSpotCheck/dataIntegration?${shareParams.toString()}`

    // 准备分享内容
    const shareTitle = `数据质量报告 - ${row.taskName}`
    const shareText = `数据集成任务"${row.taskName}"的质量报告已生成，当前进度：${row.progress}%，状态：${row.status}`

    // 尝试使用Web Share API（如果支持）
    if (navigator.share) {
      await navigator.share({
        title: shareTitle,
        text: shareText,
        url: shareUrl
      })
      ElMessage.success('分享成功')
    } else {
      // 备选方案：复制到剪贴板
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(shareUrl)
        ElMessage.success('分享链接已复制到剪贴板')

        // 显示分享信息
        ElMessageBox.alert(
          `${shareText}\n\n链接已复制到剪贴板，您可以粘贴分享给其他人。`,
          shareTitle,
          {
            confirmButtonText: '确定',
            type: 'success'
          }
        )
      } else {
        // 最后的备选方案：显示链接
        ElMessageBox.alert(
          `${shareText}\n\n请复制以下链接分享：\n${shareUrl}`,
          shareTitle,
          {
            confirmButtonText: '确定',
            type: 'info'
          }
        )
      }
    }

    // 记录操作日志
    recordOperation(
      row.id,
      '数据分享',
      `分享数据质量报告"${row.taskName}"`,
      '成功'
    )
  } catch (error: any) {
    console.error('分享报告失败:', error)
    if (error?.name === 'AbortError') {
      // 用户取消分享
      return
    }
    ElMessage.error('分享报告失败，请重试')

    // 记录失败日志
    recordOperation(
      row.id,
      '数据分享',
      `分享数据质量报告"${row.taskName}"`,
      '失败'
    )
  } finally {
    loading.value = false
  }
}

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '进行中':
      return 'primary'
    case '已停止':
      return 'warning'
    case '已完成':
      return 'success'
    default:
      return 'info'
  }
}


// 批量导出
const onExport = async () => {
  const exportData = selectedRows.value.length > 0 ? selectedRows.value : tableData.value

  if (exportData.length === 0) {
    ElMessage.warning('没有可导出的数据')
    return
  }

  try {
    loading.value = true

    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    workbook.creator = 'Data Integration System'
    workbook.lastModifiedBy = 'System'
    workbook.created = new Date()
    workbook.modified = new Date()

    // 创建工作表
    const worksheet = workbook.addWorksheet('数据集成任务')

    // 设置列定义
    worksheet.columns = [
      { header: '序号', key: 'index', width: 10 },
      { header: '集成任务名称', key: 'taskName', width: 25 },
      { header: '创建时间', key: 'createTime', width: 20 },
      { header: '进程', key: 'progress', width: 12 },
      { header: '状态', key: 'status', width: 12 }
    ]

    // 设置表头样式
    const headerRow = worksheet.getRow(1)
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    }
    headerRow.alignment = { vertical: 'middle', horizontal: 'center' }

    // 添加数据
    exportData.forEach((row, index) => {
      worksheet.addRow({
        index: index + 1,
        taskName: row.taskName,
        createTime: row.createTime,
        progress: `${row.progress}%`,
        status: row.status
      })
    })

    // 设置数据行样式
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) { // 跳过表头
        row.alignment = { vertical: 'middle', horizontal: 'center' }
      }

      // 设置边框
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 生成文件名
    const now = new Date()
    const timestamp = now.toISOString().slice(0, 19).replace(/:/g, '-').replace('T', '-')
    const filename = `${timestamp}-数据集成任务表.xlsx`

    // 下载文件
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success(`导出成功: ${filename}`)

    // 记录操作日志（为每个导出的任务记录日志）
    exportData.forEach(task => {
      recordOperation(
        task.id,
        '数据导出',
        `导出集成任务"${task.taskName}"到Excel文件`,
        '成功'
      )
    })
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')

    // 记录失败日志
    exportData.forEach(task => {
      recordOperation(
        task.id,
        '数据导出',
        `导出集成任务"${task.taskName}"到Excel文件`,
        '失败'
      )
    })
  } finally {
    loading.value = false
  }
}

// 批量删除
const onBatchDelete = () => {
  const deleteData = selectedRows.value.length > 0 ? selectedRows.value : tableData.value

  if (deleteData.length === 0) {
    ElMessage.warning('没有可删除的数据')
    return
  }

  const message = selectedRows.value.length > 0
    ? `确认删除选中的 ${selectedRows.value.length} 条记录吗？`
    : '确认删除全部记录吗？'

  ElMessageBox.confirm(
    message,
    '批量删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    if (selectedRows.value.length > 0) {
      const selectedIds = selectedRows.value.map(row => row.id)
      tableData.value = tableData.value.filter(item => !selectedIds.includes(item.id))
    } else {
      tableData.value = []
    }

    // 更新索引
    tableData.value.forEach((item, idx) => {
      item.index = idx + 1
    })

    localStorage.setItem('dataIntegrationTaskData', JSON.stringify(tableData.value))
    selectedRows.value = []

    // 更新分页信息
    updatePagination()

    ElMessage.success('批量删除成功')
  })
}

// 创建任务
const openCreateDialog = () => {
  createForm.value = {
    taskName: '',
    status: '进行中',
    progress: 0,
    hasException: false,
    // 数据库连接配置
    serverAddress: '',
    port: 3306,
    username: '',
    password: '',
    tableName: '',
    // 权限配置
    permissions: [],
    // 字段映射配置
    fieldMappings: []
  }
  showCreateDialog.value = true
}

const handleCreate = () => {
  // 表单验证
  if (!createForm.value.taskName.trim()) {
    ElMessage.error('请输入集成任务名称')
    return
  }

  if (createForm.value.taskName.length < 2 || createForm.value.taskName.length > 50) {
    ElMessage.error('任务名称长度在 2 到 50 个字符')
    return
  }

  // 数据库连接配置验证
  if (!createForm.value.serverAddress.trim()) {
    ElMessage.error('请输入服务器地址')
    return
  }

  if (!createForm.value.port || createForm.value.port < 1 || createForm.value.port > 65535) {
    ElMessage.error('请输入有效的端口号(1-65535)')
    return
  }

  if (!createForm.value.username.trim()) {
    ElMessage.error('请输入用户名')
    return
  }

  if (!createForm.value.password.trim()) {
    ElMessage.error('请输入密码')
    return
  }

  if (!createForm.value.tableName.trim()) {
    ElMessage.error('请输入表名')
    return
  }

  // 权限配置验证
  if (createForm.value.permissions.length === 0) {
    ElMessage.error('请至少选择一个权限')
    return
  }

  // 生成新任务
  const newTask: DataIntegrationTask = {
    id: `integration_task_${Date.now()}`,
    index: tableData.value.length + 1,
    taskName: createForm.value.taskName,
    createTime: new Date().toLocaleString('zh-CN'),
    progress: createForm.value.progress,
    status: createForm.value.status,
    hasException: createForm.value.hasException,
    logGenerated: false,
    reportGenerated: false,
    // 数据库连接配置
    serverAddress: createForm.value.serverAddress,
    port: createForm.value.port,
    username: createForm.value.username,
    password: createForm.value.password,
    tableName: createForm.value.tableName,
    // 权限配置
    permissions: [...createForm.value.permissions],
    // 字段映射配置
    fieldMappings: [...createForm.value.fieldMappings]
  }

  // 添加到数据列表
  tableData.value.unshift(newTask)

  // 更新索引
  tableData.value.forEach((item, index) => {
    item.index = index + 1
  })

  // 保存到localStorage
  localStorage.setItem('dataIntegrationTaskData', JSON.stringify(tableData.value))

  // 更新分页信息
  updatePagination()

  // 记录操作日志
  recordOperation(
    newTask.id,
    '数据新增',
    `创建新的集成任务"${newTask.taskName}"`,
    '成功'
  )

  showCreateDialog.value = false
  ElMessage.success('创建任务成功')
}

// 编辑任务
const openEditDialog = (row: DataIntegrationTask) => {
  currentEditTask.value = row
  editForm.value = {
    taskName: row.taskName,
    status: row.status,
    progress: row.progress,
    hasException: row.hasException,
    // 数据库连接配置
    serverAddress: row.serverAddress || '',
    port: row.port || 3306,
    username: row.username || '',
    password: row.password || '',
    tableName: row.tableName || '',
    // 权限配置
    permissions: [...(row.permissions || [])],
    // 字段映射配置
    fieldMappings: [...(row.fieldMappings || [])]
  }
  showEditDialog.value = true
}

const handleEdit = () => {
  if (!currentEditTask.value) return

  // 表单验证
  if (!editForm.value.taskName.trim()) {
    ElMessage.error('请输入集成任务名称')
    return
  }

  if (editForm.value.taskName.length < 2 || editForm.value.taskName.length > 50) {
    ElMessage.error('任务名称长度在 2 到 50 个字符')
    return
  }

  // 数据库连接配置验证
  if (!editForm.value.serverAddress.trim()) {
    ElMessage.error('请输入服务器地址')
    return
  }

  if (!editForm.value.port || editForm.value.port < 1 || editForm.value.port > 65535) {
    ElMessage.error('请输入有效的端口号(1-65535)')
    return
  }

  if (!editForm.value.username.trim()) {
    ElMessage.error('请输入用户名')
    return
  }

  if (!editForm.value.password.trim()) {
    ElMessage.error('请输入密码')
    return
  }

  if (!editForm.value.tableName.trim()) {
    ElMessage.error('请输入表名')
    return
  }

  // 权限配置验证
  if (editForm.value.permissions.length === 0) {
    ElMessage.error('请至少选择一个权限')
    return
  }

  // 更新任务数据
  const index = tableData.value.findIndex(item => item.id === currentEditTask.value!.id)
  if (index !== -1) {
    tableData.value[index] = {
      ...tableData.value[index],
      taskName: editForm.value.taskName,
      status: editForm.value.status,
      progress: editForm.value.progress,
      hasException: editForm.value.hasException,
      // 数据库连接配置
      serverAddress: editForm.value.serverAddress,
      port: editForm.value.port,
      username: editForm.value.username,
      password: editForm.value.password,
      tableName: editForm.value.tableName,
      // 权限配置
      permissions: [...editForm.value.permissions],
      // 字段映射配置
      fieldMappings: [...editForm.value.fieldMappings]
    }

    // 保存到localStorage
    localStorage.setItem('dataIntegrationTaskData', JSON.stringify(tableData.value))

    // 记录操作日志
    recordOperation(
      currentEditTask.value.id,
      '数据修改',
      `修改集成任务"${editForm.value.taskName}"的配置信息`,
      '成功'
    )

    showEditDialog.value = false
    ElMessage.success('编辑任务成功')
  }
}

// 查看详情
const openDetailDialog = (row: DataIntegrationTask) => {
  currentDetailTask.value = row
  showDetailDialog.value = true

  // 记录查看操作日志
  recordOperation(
    row.id,
    '数据查看',
    `查看集成任务"${row.taskName}"的详细信息`,
    '成功'
  )
}

// 删除单个任务
const deleteTask = (row: DataIntegrationTask) => {
  ElMessageBox.confirm(
    `确认删除任务"${row.taskName}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      tableData.value.splice(index, 1)

      // 更新索引
      tableData.value.forEach((item, idx) => {
        item.index = idx + 1
      })

      // 保存到localStorage
      localStorage.setItem('dataIntegrationTaskData', JSON.stringify(tableData.value))

      // 更新分页信息
      updatePagination()

      // 记录操作日志
      recordOperation(
        row.id,
        '数据删除',
        `删除集成任务"${row.taskName}"`,
        '成功'
      )

      ElMessage.success('删除任务成功')
    }
  })
}

// 规则设置相关数据和方法
const ruleTableData = ref<IntegrationRule[]>([])
const rulePagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 规则表格列配置
const ruleColumns = [
  { prop: 'index', label: '序号', width: 80 },
  { prop: 'ruleName', label: '规则名称', minWidth: 150 },
  { prop: 'ruleType', label: '规则类型', width: 120 },
  { prop: 'sourceSystem', label: '源系统', width: 120 },
  { prop: 'targetSystem', label: '目标系统', width: 120 },
  { prop: 'isEnabled', label: '状态', width: 100 },
  { prop: 'createTime', label: '创建时间', width: 180, sortable: true },
  { prop: 'actions', label: '操作', width: 200, fixed: 'right' }
]

// 规则搜索表单配置
const ruleSearchFormProp = ref([
  { label: '规则名称', prop: 'ruleName', type: 'text', placeholder: '请输入规则名称' }
])

// 规则表单数据
const ruleForm = ref({
  ruleName: '',
  ruleType: '',
  sourceSystem: '',
  targetSystem: '',
  ruleContent: '',
  isEnabled: true
})

// 规则表单验证规则
const ruleFormRules = {
  ruleName: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 2, max: 50, message: '规则名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  ruleType: [
    { required: true, message: '请选择规则类型', trigger: 'change' }
  ],
  sourceSystem: [
    { required: true, message: '请输入源系统', trigger: 'blur' }
  ],
  targetSystem: [
    { required: true, message: '请输入目标系统', trigger: 'blur' }
  ],
  ruleContent: [
    { required: true, message: '请输入规则内容', trigger: 'blur' }
  ]
}

// 生成模拟规则数据
const generateMockRuleData = () => {
  const ruleTypes = ['数据映射', '数据转换', '数据验证', '数据清洗', '数据同步']
  const systems = ['财务系统', '人事系统', '业务系统', '统计系统', '档案系统']

  const data = []
  for (let i = 1; i <= 20; i++) {
    data.push({
      id: `rule_${i}`,
      index: i,
      ruleName: `数据集成规则${i}`,
      ruleType: ruleTypes[Math.floor(Math.random() * ruleTypes.length)],
      sourceSystem: systems[Math.floor(Math.random() * systems.length)],
      targetSystem: systems[Math.floor(Math.random() * systems.length)],
      ruleContent: `规则${i}的具体内容描述，包含数据处理逻辑和转换规则`,
      isEnabled: Math.random() > 0.3,
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleString('zh-CN'),
      updateTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toLocaleString('zh-CN'),
      creator: ['张三', '李四', '王五', '赵六'][Math.floor(Math.random() * 4)]
    })
  }
  return data
}

// 初始化规则数据
const initRuleData = () => {
  try {
    const savedData = localStorage.getItem('dataIntegrationRuleData')
    if (savedData) {
      const parsedData = JSON.parse(savedData)
      if (Array.isArray(parsedData)) {
        ruleTableData.value = parsedData.filter(item =>
          item &&
          typeof item.id === 'string' &&
          typeof item.ruleName === 'string'
        )
      } else {
        throw new Error('规则数据格式不正确')
      }
    } else {
      ruleTableData.value = generateMockRuleData()
      localStorage.setItem('dataIntegrationRuleData', JSON.stringify(ruleTableData.value))
    }
  } catch (error) {
    console.error('初始化规则数据失败:', error)
    ElMessage.warning('规则数据加载异常，已重新生成模拟数据')
    ruleTableData.value = generateMockRuleData()
    localStorage.setItem('dataIntegrationRuleData', JSON.stringify(ruleTableData.value))
  }
  updateRulePagination()
}

// 更新规则分页信息
const updateRulePagination = () => {
  const filtered = getFilteredRuleData()
  rulePagination.total = filtered.length
  if (rulePagination.page > Math.ceil(filtered.length / rulePagination.size) && filtered.length > 0) {
    rulePagination.page = 1
  }
}

// 获取过滤后的规则数据
const getFilteredRuleData = () => {
  let filtered = [...ruleTableData.value]

  // 搜索过滤
  if (ruleSearchForm.value.ruleName) {
    filtered = filtered.filter(item =>
      item.ruleName.includes(ruleSearchForm.value.ruleName) ||
      item.ruleType.includes(ruleSearchForm.value.ruleName) ||
      item.sourceSystem.includes(ruleSearchForm.value.ruleName) ||
      item.targetSystem.includes(ruleSearchForm.value.ruleName)
    )
  }

  // 排序
  filtered.sort((a, b) => {
    const aValue = a[ruleSortField.value as keyof IntegrationRule]
    const bValue = b[ruleSortField.value as keyof IntegrationRule]

    if (ruleSortField.value === 'createTime') {
      const aTime = new Date(aValue as string).getTime()
      const bTime = new Date(bValue as string).getTime()
      return ruleSortOrder.value === 'desc' ? bTime - aTime : aTime - bTime
    }

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return ruleSortOrder.value === 'desc'
        ? bValue.localeCompare(aValue)
        : aValue.localeCompare(bValue)
    }

    return 0
  })

  return filtered
}

// 计算规则分页数据
const paginatedRuleData = computed(() => {
  const filtered = getFilteredRuleData()
  const start = (rulePagination.page - 1) * rulePagination.size
  const end = start + rulePagination.size
  return filtered.slice(start, end)
})

// 打开规则设置弹窗
const openRuleSettingsDialog = () => {
  initRuleData()
  showRuleSettingsDialog.value = true
}

// 规则搜索
const onRuleSearch = async () => {
  try {
    ruleLoading.value = true

    // 模拟搜索延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    rulePagination.page = 1
    updateRulePagination()

    ElMessage.success('搜索完成')
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    ruleLoading.value = false
  }
}

// 规则排序处理
const handleRuleSortChange = ({ prop, order }: { prop: string, order: string | null }) => {
  if (order) {
    ruleSortField.value = prop
    ruleSortOrder.value = order === 'ascending' ? 'asc' : 'desc'
  } else {
    ruleSortField.value = 'createTime'
    ruleSortOrder.value = 'desc'
  }
  updateRulePagination()
}

// 规则选择变化
const handleRuleSelectionChange = (selection: IntegrationRule[]) => {
  selectedRules.value = selection
}

// 规则状态变化
const handleRuleStatusChange = (row: IntegrationRule) => {
  const index = ruleTableData.value.findIndex(item => item.id === row.id)
  if (index !== -1) {
    ruleTableData.value[index].isEnabled = row.isEnabled
    ruleTableData.value[index].updateTime = new Date().toLocaleString('zh-CN')
    localStorage.setItem('dataIntegrationRuleData', JSON.stringify(ruleTableData.value))
    ElMessage.success(`规则"${row.ruleName}"状态已${row.isEnabled ? '启用' : '禁用'}`)
  }
}

// 规则分页变化
const handleRulePageChange = (page: number) => {
  rulePagination.page = page
}

const handleRuleSizeChange = (size: number) => {
  rulePagination.size = size
  rulePagination.page = 1
}

// 打开新增规则弹窗
const openRuleCreateDialog = () => {
  currentEditRule.value = null
  ruleForm.value = {
    ruleName: '',
    ruleType: '',
    sourceSystem: '',
    targetSystem: '',
    ruleContent: '',
    isEnabled: true
  }
  showRuleFormDialog.value = true
}

// 打开编辑规则弹窗
const openRuleEditDialog = (row: IntegrationRule) => {
  currentEditRule.value = row
  ruleForm.value = {
    ruleName: row.ruleName,
    ruleType: row.ruleType,
    sourceSystem: row.sourceSystem,
    targetSystem: row.targetSystem,
    ruleContent: row.ruleContent,
    isEnabled: row.isEnabled
  }
  showRuleFormDialog.value = true
}

// 删除单个规则
const deleteRule = (row: IntegrationRule) => {
  ElMessageBox.confirm(
    `确认删除规则"${row.ruleName}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = ruleTableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      ruleTableData.value.splice(index, 1)

      // 更新索引
      ruleTableData.value.forEach((item, idx) => {
        item.index = idx + 1
      })

      localStorage.setItem('dataIntegrationRuleData', JSON.stringify(ruleTableData.value))
      updateRulePagination()
      ElMessage.success('删除规则成功')
    }
  })
}

// 批量删除规则
const onBatchDeleteRules = () => {
  if (selectedRules.value.length === 0) {
    ElMessage.warning('请选择要删除的规则')
    return
  }

  ElMessageBox.confirm(
    `确认删除选中的 ${selectedRules.value.length} 条规则吗？`,
    '批量删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const selectedIds = selectedRules.value.map(row => row.id)
    ruleTableData.value = ruleTableData.value.filter(item => !selectedIds.includes(item.id))

    // 更新索引
    ruleTableData.value.forEach((item, idx) => {
      item.index = idx + 1
    })

    localStorage.setItem('dataIntegrationRuleData', JSON.stringify(ruleTableData.value))
    selectedRules.value = []
    updateRulePagination()
    ElMessage.success('批量删除成功')
  })
}

// 规则表单提交
const handleRuleSubmit = async () => {
  // 表单验证
  if (!ruleForm.value.ruleName.trim()) {
    ElMessage.error('请输入规则名称')
    return
  }

  if (!ruleForm.value.ruleType) {
    ElMessage.error('请选择规则类型')
    return
  }

  if (!ruleForm.value.sourceSystem) {
    ElMessage.error('请选择源系统')
    return
  }

  if (!ruleForm.value.targetSystem) {
    ElMessage.error('请选择目标系统')
    return
  }

  if (!ruleForm.value.ruleContent.trim()) {
    ElMessage.error('请输入规则内容')
    return
  }

  try {
    ruleLoading.value = true

    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 800))

    if (currentEditRule.value) {
      // 编辑规则
      const index = ruleTableData.value.findIndex(item => item.id === currentEditRule.value!.id)
      if (index !== -1) {
        ruleTableData.value[index] = {
          ...ruleTableData.value[index],
          ruleName: ruleForm.value.ruleName,
          ruleType: ruleForm.value.ruleType,
          sourceSystem: ruleForm.value.sourceSystem,
          targetSystem: ruleForm.value.targetSystem,
          ruleContent: ruleForm.value.ruleContent,
          isEnabled: ruleForm.value.isEnabled,
          updateTime: new Date().toLocaleString('zh-CN')
        }
        ElMessage.success('编辑规则成功')
      }
    } else {
      // 新增规则
      const newRule: IntegrationRule = {
        id: `rule_${Date.now()}`,
        index: ruleTableData.value.length + 1,
        ruleName: ruleForm.value.ruleName,
        ruleType: ruleForm.value.ruleType,
        sourceSystem: ruleForm.value.sourceSystem,
        targetSystem: ruleForm.value.targetSystem,
        ruleContent: ruleForm.value.ruleContent,
        isEnabled: ruleForm.value.isEnabled,
        createTime: new Date().toLocaleString('zh-CN'),
        updateTime: new Date().toLocaleString('zh-CN'),
        creator: getCurrentOperatorName()
      }

      ruleTableData.value.unshift(newRule)

      // 更新索引
      ruleTableData.value.forEach((item, idx) => {
        item.index = idx + 1
      })

      ElMessage.success('新增规则成功')
    }

    // 保存到localStorage
    localStorage.setItem('dataIntegrationRuleData', JSON.stringify(ruleTableData.value))

    // 更新分页信息
    updateRulePagination()

    // 关闭弹窗
    showRuleFormDialog.value = false
  } catch (error) {
    console.error('保存规则失败:', error)
    ElMessage.error('保存规则失败，请重试')
  } finally {
    ruleLoading.value = false
  }
}

// 初始化
onMounted(() => {
  initData()
})
</script>

<template>
  <div class="data-integration">
    <Block
      title="数据集成"
      :enable-expand-content="true"
      :enableBackButton="false"
      :enable-fixed-height="true"
      :enable-close-button="false"
      @height-changed="onBlockHeightChanged"
      @content-expand="() => {}"
    >

      <template #topRight>
        <el-button size="small" type="primary" @click="openCreateDialog">
          <el-icon><Plus /></el-icon>
          创建任务
        </el-button>
        <el-button size="small" type="info" @click="openRuleSettingsDialog">
          <el-icon><Setting /></el-icon>
          规则设置
        </el-button>
        <el-button size="small" type="success" @click="onExport" :disabled="selectedRows.length === 0">批量导出</el-button>
        <el-button size="small" type="danger" @click="onBatchDelete" :disabled="selectedRows.length === 0">批量删除</el-button>
        <el-button size="small" @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </template>

      <template #expand>
        <!-- 搜索区域 -->
        <div class="search" v-loading="searchLoading" element-loading-background="rgba(255, 255, 255, 0.8)">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :label-width="120"
            :enable-reset="false"
            :column-count="3"
            confirm-text="查询"
            button-vertical="flowing"
            @submit="onSearch"
          />
        </div>
      </template>

      <!-- 表格列表 -->
      <TableV2
        ref="tableRef"
        :columns="columns"
        :defaultTableData="paginatedData"
        :enable-toolbar="false"
        :enable-own-button="false"
        :enable-selection="true"
        :enable-index="false"
        :height="tableHeight"
        :loading="loading"
        @selection-change="handleSelectionChange"
      >
        <!-- 序号列 -->
        <template #index="{ index }">
          {{ (pagination.page - 1) * pagination.size + index + 1 }}
        </template>

        <!-- 任务名称列 -->
        <template #taskName="{ row }">
          <div class="task-name-cell">
            <span>{{ row.taskName }}</span>
            <el-icon v-if="row.hasException" class="exception-icon" color="#F56C6C" title="有异常数据">
              <Warning />
            </el-icon>
          </div>
        </template>

        <!-- 进程列 -->
        <template #progress="{ row }">
          <div class="progress-cell">
            <el-progress
              :percentage="row.progress"
              :status="row.progress === 100 ? 'success' : undefined"
              :stroke-width="8"
            />
            <span class="progress-text">{{ row.progress }}%</span>
          </div>
        </template>

        <!-- 状态列 -->
        <template #status="{ row }">
          <div class="status-cell">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
              :class="{ 'status-exception': row.hasException }"
            >
              {{ row.status }}
            </el-tag>
            <span v-if="row.hasException" class="exception-text">有异常数据</span>
          </div>
        </template>

        <!-- 日志操作列 -->
        <template #logAction="{ row }">
          <el-button
            v-if="!row.logGenerated"
            type="primary"
            size="small"
            @click="generateLog(row)"
          >
            生成
          </el-button>
          <el-button
            v-else
            type="info"
            size="small"
            @click="viewLog(row)"
          >
            查看
          </el-button>
        </template>

        <!-- 数据质量报告操作列 -->
        <template #reportAction="{ row }">
          <div v-if="!row.reportGenerated">
            <el-button
              type="primary"
              size="small"
              @click="generateReport(row)"
            >
              生成
            </el-button>
          </div>
          <div v-else class="report-actions">
            <el-button type="success" size="small" @click="exportReport(row)">导出</el-button>
            <el-button type="info" size="small" @click="printReport(row)">打印</el-button>
            <el-button type="warning" size="small" @click="shareReport(row)">分享</el-button>
          </div>
        </template>

        <!-- 操作列 -->
        <template #actions="{ row }">
          <div class="action-buttons">
            <el-button type="primary" size="small" @click="openDetailDialog(row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button type="warning" size="small" @click="openEditDialog(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteTask(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </template>
      </TableV2>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </Block>

    <!-- 日志查看弹窗 -->
    <Dialog
      v-model="showLogDialog"
      :title="`查看日志 - ${currentTaskName}`"
      width="1400px"
      :destroy-on-close="true"
      :enable-confirm="false"
      cancel-text="关闭"
    >
      <div class="records-content">
        <!-- 搜索区域 -->
        <div class="records-search" v-loading="false" element-loading-background="rgba(255, 255, 255, 0.8)">
          <Form
            :props="logSearchFormProp"
            v-model="logSearchForm"
            :column-count="4"
            :label-width="100"
            :enable-reset="false"
            confirm-text="查询"
            reset-text="重置"
            button-vertical="flowing"
            @submit="onLogSearch"
            @reset="onLogReset"
          />
        </div>

        <!-- 表格列表 -->
        <TableV2
          :columns="logColumns"
          :defaultTableData="currentTaskLog"
          :enable-toolbar="false"
          :enable-own-button="false"
          :enable-selection="false"
          :enable-index="false"
          :height="400"
          :loading="false"
        >
          <!-- 序号列 -->
          <template #index="{ index }">
            {{ (logPagination.page - 1) * logPagination.size + index + 1 }}
          </template>

          <!-- 操作类型列 -->
          <template #operationType="{ row }">
            <el-tag
              :type="getOperationTypeColor(row.operationType)"
              size="small"
            >
              {{ row.operationType }}
            </el-tag>
          </template>

          <!-- 操作结果列 -->
          <template #operationResult="{ row }">
            <el-tag
              :type="row.operationResult === '成功' ? 'success' : row.operationResult === '失败' ? 'danger' : 'warning'"
              size="small"
            >
              {{ row.operationResult }}
            </el-tag>
          </template>


        </TableV2>

        <!-- 分页 -->
        <div class="records-pagination">
          <el-pagination
            v-model:current-page="logPagination.page"
            v-model:page-size="logPagination.size"
            :page-sizes="[10, 20, 50]"
            :total="logPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="onLogSizeChange"
            @current-change="onLogPageChange"
          />
        </div>
      </div>
    </Dialog>

    <!-- 创建任务弹窗 -->
    <Dialog
      v-model="showCreateDialog"
      title="创建数据集成任务"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="createForm"
        :rules="createFormRules"
        label-width="120px"
        ref="createFormRef"
      >
        <el-form-item label="任务名称" prop="taskName">
          <el-input
            v-model="createForm.taskName"
            placeholder="请输入集成任务名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <!-- 数据库连接配置 -->
        <el-divider content-position="left">数据库连接配置</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务器地址" prop="serverAddress">
              <el-input
                v-model="createForm.serverAddress"
                placeholder="请输入服务器地址"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="端口" prop="port">
              <el-input-number
                v-model="createForm.port"
                :min="1"
                :max="65535"
                placeholder="请输入端口号"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="createForm.username"
                placeholder="请输入用户名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="createForm.password"
                type="password"
                placeholder="请输入密码"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="表名" prop="tableName">
          <el-input
            v-model="createForm.tableName"
            placeholder="请输入表名"
          />
        </el-form-item>

        <!-- 数据集成权限配置 -->
        <el-divider content-position="left">数据集成权限配置</el-divider>

        <el-form-item label="权限配置" prop="permissions">
          <el-checkbox-group v-model="createForm.permissions">
            <el-checkbox label="读取">读取</el-checkbox>
            <el-checkbox label="写入">写入</el-checkbox>
            <el-checkbox label="删除">删除</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 数据映射表配置 -->
        <el-divider content-position="left">数据映射表配置</el-divider>

        <el-form-item label="字段映射">
          <div class="field-mapping-container">
            <div class="mapping-header">
              <div class="mapping-section">
                <span class="section-title">数据源表</span>
                <div class="field-headers">
                  <span>字段名</span>
                  <span>类型</span>
                </div>
              </div>
              <div class="mapping-arrow">→</div>
              <div class="mapping-section">
                <span class="section-title">数据映射表</span>
                <div class="field-headers">
                  <span>字段名</span>
                  <span>类型</span>
                </div>
              </div>
              <div class="mapping-actions">操作</div>
            </div>

            <div
              v-for="(mapping, index) in createForm.fieldMappings"
              :key="mapping.id"
              class="mapping-row"
            >
              <div class="mapping-section">
                <el-select v-model="mapping.sourceField" placeholder="选择字段">
                  <el-option
                    v-for="option in getFieldOptions()"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
                <el-select v-model="mapping.sourceType" placeholder="选择类型">
                  <el-option
                    v-for="option in getDataTypeOptions()"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </div>
              <div class="mapping-arrow">→</div>
              <div class="mapping-section">
                <el-select v-model="mapping.targetField" placeholder="选择字段">
                  <el-option
                    v-for="option in getTargetFieldOptions()"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
                <el-select v-model="mapping.targetType" placeholder="选择类型">
                  <el-option
                    v-for="option in getDataTypeOptions()"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </div>
              <div class="mapping-actions">
                <el-button
                  type="danger"
                  size="small"
                  @click="removeFieldMapping('create', index)"
                >
                  删除
                </el-button>
              </div>
            </div>

            <div class="add-mapping">
              <el-button
                type="primary"
                size="small"
                @click="addFieldMapping('create')"
              >
                添加映射
              </el-button>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="任务状态">
          <el-select v-model="createForm.status" placeholder="请选择状态">
            <el-option label="进行中" value="进行中" />
            <el-option label="已停止" value="已停止" />
            <el-option label="已完成" value="已完成" />
          </el-select>
        </el-form-item>

        <el-form-item label="进度">
          <el-slider
            v-model="createForm.progress"
            :min="0"
            :max="100"
            show-input
            :format-tooltip="(val: number) => `${val}%`"
          />
        </el-form-item>

        <el-form-item label="异常标记">
          <el-switch
            v-model="createForm.hasException"
            active-text="有异常"
            inactive-text="无异常"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button type="primary" @click="handleCreate">确定</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 编辑任务弹窗 -->
    <Dialog
      v-model="showEditDialog"
      title="编辑数据集成任务"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="editForm"
        :rules="editFormRules"
        label-width="120px"
        ref="editFormRef"
      >
        <el-form-item label="任务名称" prop="taskName">
          <el-input
            v-model="editForm.taskName"
            placeholder="请输入集成任务名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <!-- 数据库连接配置 -->
        <el-divider content-position="left">数据库连接配置</el-divider>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务器地址" prop="serverAddress">
              <el-input
                v-model="editForm.serverAddress"
                placeholder="请输入服务器地址"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="端口" prop="port">
              <el-input-number
                v-model="editForm.port"
                :min="1"
                :max="65535"
                placeholder="请输入端口号"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="editForm.username"
                placeholder="请输入用户名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="editForm.password"
                type="password"
                placeholder="请输入密码"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="表名" prop="tableName">
          <el-input
            v-model="editForm.tableName"
            placeholder="请输入表名"
          />
        </el-form-item>

        <!-- 数据集成权限配置 -->
        <el-divider content-position="left">数据集成权限配置</el-divider>

        <el-form-item label="权限配置" prop="permissions">
          <el-checkbox-group v-model="editForm.permissions">
            <el-checkbox label="读取">读取</el-checkbox>
            <el-checkbox label="写入">写入</el-checkbox>
            <el-checkbox label="删除">删除</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 数据映射表配置 -->
        <el-divider content-position="left">数据映射表配置</el-divider>

        <el-form-item label="字段映射">
          <div class="field-mapping-container">
            <div class="mapping-header">
              <div class="mapping-section">
                <span class="section-title">数据源表</span>
                <div class="field-headers">
                  <span>字段名</span>
                  <span>类型</span>
                </div>
              </div>
              <div class="mapping-arrow">→</div>
              <div class="mapping-section">
                <span class="section-title">数据映射表</span>
                <div class="field-headers">
                  <span>字段名</span>
                  <span>类型</span>
                </div>
              </div>
              <div class="mapping-actions">操作</div>
            </div>

            <div
              v-for="(mapping, index) in editForm.fieldMappings"
              :key="mapping.id"
              class="mapping-row"
            >
              <div class="mapping-section">
                <el-select v-model="mapping.sourceField" placeholder="选择字段">
                  <el-option
                    v-for="option in getFieldOptions()"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
                <el-select v-model="mapping.sourceType" placeholder="选择类型">
                  <el-option
                    v-for="option in getDataTypeOptions()"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </div>
              <div class="mapping-arrow">→</div>
              <div class="mapping-section">
                <el-select v-model="mapping.targetField" placeholder="选择字段">
                  <el-option
                    v-for="option in getTargetFieldOptions()"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
                <el-select v-model="mapping.targetType" placeholder="选择类型">
                  <el-option
                    v-for="option in getDataTypeOptions()"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
              </div>
              <div class="mapping-actions">
                <el-button
                  type="danger"
                  size="small"
                  @click="removeFieldMapping('edit', index)"
                >
                  删除
                </el-button>
              </div>
            </div>

            <div class="add-mapping">
              <el-button
                type="primary"
                size="small"
                @click="addFieldMapping('edit')"
              >
                添加映射
              </el-button>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="任务状态">
          <el-select v-model="editForm.status" placeholder="请选择状态">
            <el-option label="进行中" value="进行中" />
            <el-option label="已停止" value="已停止" />
            <el-option label="已完成" value="已完成" />
          </el-select>
        </el-form-item>

        <el-form-item label="进度">
          <el-slider
            v-model="editForm.progress"
            :min="0"
            :max="100"
            show-input
            :format-tooltip="(val: number) => `${val}%`"
          />
        </el-form-item>

        <el-form-item label="异常标记">
          <el-switch
            v-model="editForm.hasException"
            active-text="有异常"
            inactive-text="无异常"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="handleEdit">确定</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 查看详情弹窗 -->
    <Dialog
      v-model="showDetailDialog"
      title="任务详情"
      width="700px"
      :close-on-click-modal="false"
    >
      <div v-if="currentDetailTask" class="task-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">
            {{ currentDetailTask.id }}
          </el-descriptions-item>
          <el-descriptions-item label="任务名称">
            {{ currentDetailTask.taskName }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ currentDetailTask.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <el-tag :type="getStatusType(currentDetailTask.status)">
              {{ currentDetailTask.status }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="执行进度">
            <div class="progress-detail">
              <el-progress
                :percentage="currentDetailTask.progress"
                :stroke-width="8"
                :color="currentDetailTask.hasException ? '#F56C6C' : '#409EFF'"
              />
              <span class="progress-text">{{ currentDetailTask.progress }}%</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="异常状态">
            <el-tag v-if="currentDetailTask.hasException" type="danger">
              <el-icon><Warning /></el-icon>
              有异常数据
            </el-tag>
            <el-tag v-else type="success">
              <el-icon><Check /></el-icon>
              正常
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="日志状态">
            <el-tag :type="currentDetailTask.logGenerated ? 'success' : 'info'">
              {{ currentDetailTask.logGenerated ? '已生成' : '未生成' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="报告状态">
            <el-tag :type="currentDetailTask.reportGenerated ? 'success' : 'info'">
              {{ currentDetailTask.reportGenerated ? '已生成' : '未生成' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
        </div>
      </template>
    </Dialog>

    <!-- 规则设置弹窗 -->
    <Dialog
      v-model="showRuleSettingsDialog"
      title="数据集成规则设置"
      width="1200px"
      :destroy-on-close="true"
      :enable-confirm="false"
      cancel-text="关闭"
    >
      <div class="records-content">
        <!-- 操作按钮区域 -->
        <div class="records-actions">
          <el-button size="small" type="primary" @click="openRuleCreateDialog">
            <el-icon><Plus /></el-icon>
            新增规则
          </el-button>
          <el-button
            size="small"
            type="danger"
            @click="onBatchDeleteRules"
            :disabled="selectedRules.length === 0"
          >
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </div>

        <!-- 搜索区域 -->
        <div class="records-search" v-loading="ruleLoading" element-loading-background="rgba(255, 255, 255, 0.8)">
          <Form
            :props="ruleSearchFormProp"
            v-model="ruleSearchForm"
            :column-count="4"
            :label-width="100"
            :enable-reset="false"
            confirm-text="查询"
            reset-text="重置"
            button-vertical="flowing"
            @submit="onRuleSearch"
          />
        </div>

        <!-- 规则表格 -->
        <div v-if="paginatedRuleData.length === 0 && !ruleLoading" class="empty-state">
          <el-empty description="暂无规则数据">
            <el-button type="primary" @click="openRuleCreateDialog">新增规则</el-button>
          </el-empty>
        </div>

        <TableV2
          v-else
          :columns="ruleColumns"
          :defaultTableData="paginatedRuleData"
          :enable-toolbar="false"
          :enable-own-button="false"
          :enable-selection="true"
          :enable-index="false"
          :height="ruleTableHeight"
          :loading="ruleLoading"
          @selection-change="handleRuleSelectionChange"
          @sort-change="handleRuleSortChange"
        >
          <!-- 序号列 -->
          <template #index="{ index }">
            {{ (rulePagination.page - 1) * rulePagination.size + index + 1 }}
          </template>

          <!-- 状态列 -->
          <template #isEnabled="{ row }">
            <el-switch
              v-model="row.isEnabled"
              @change="handleRuleStatusChange(row)"
              active-text="启用"
              inactive-text="禁用"
            />
          </template>

          <!-- 操作列 -->
          <template #actions="{ row }">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="openRuleEditDialog(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="danger" size="small" @click="deleteRule(row)">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </TableV2>

        <!-- 分页 -->
        <div class="records-pagination">
          <el-pagination
            v-model:current-page="rulePagination.page"
            v-model:page-size="rulePagination.size"
            :page-sizes="[10, 20, 50]"
            :total="rulePagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleRuleSizeChange"
            @current-change="handleRulePageChange"
          />
        </div>
      </div>
    </Dialog>

    <!-- 新增/编辑规则弹窗 -->
    <Dialog
      v-model="showRuleFormDialog"
      :title="currentEditRule ? '编辑规则' : '新增规则'"
      width="600px"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @click-confirm="handleRuleSubmit"
    >
      <el-form
        :model="ruleForm"
        :rules="ruleFormRules"
        label-width="120px"
        ref="ruleFormRef"
      >
        <el-form-item label="规则名称" prop="ruleName">
          <el-input
            v-model="ruleForm.ruleName"
            placeholder="请输入规则名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="规则类型" prop="ruleType">
          <el-select v-model="ruleForm.ruleType" placeholder="请选择规则类型" style="width: 100%">
            <el-option label="数据映射" value="数据映射" />
            <el-option label="数据转换" value="数据转换" />
            <el-option label="数据验证" value="数据验证" />
            <el-option label="数据清洗" value="数据清洗" />
            <el-option label="数据同步" value="数据同步" />
          </el-select>
        </el-form-item>

        <el-form-item label="源系统" prop="sourceSystem">
          <el-select v-model="ruleForm.sourceSystem" placeholder="请选择源系统" style="width: 100%">
            <el-option label="财务系统" value="财务系统" />
            <el-option label="人事系统" value="人事系统" />
            <el-option label="业务系统" value="业务系统" />
            <el-option label="统计系统" value="统计系统" />
            <el-option label="档案系统" value="档案系统" />
          </el-select>
        </el-form-item>

        <el-form-item label="目标系统" prop="targetSystem">
          <el-select v-model="ruleForm.targetSystem" placeholder="请选择目标系统" style="width: 100%">
            <el-option label="财务系统" value="财务系统" />
            <el-option label="人事系统" value="人事系统" />
            <el-option label="业务系统" value="业务系统" />
            <el-option label="统计系统" value="统计系统" />
            <el-option label="档案系统" value="档案系统" />
          </el-select>
        </el-form-item>

        <el-form-item label="规则内容" prop="ruleContent">
          <el-input
            v-model="ruleForm.ruleContent"
            type="textarea"
            :rows="4"
            placeholder="请输入规则的具体内容描述，包含数据处理逻辑和转换规则"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="启用状态">
          <el-switch
            v-model="ruleForm.isEnabled"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
    </Dialog>
  </div>
</template>

<route>
{
  meta: {
    title: '数据集成'
  }
}
</route>

<style lang="scss" scoped>
.data-integration {
  .task-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .exception-icon {
      font-size: 16px;
      cursor: help;
      animation: pulse 2s infinite;
    }
  }

  .progress-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;

    .el-progress {
      flex: 1;
      min-width: 80px;
    }

    .progress-text {
      font-size: 12px;
      color: #666;
      min-width: 35px;
      white-space: nowrap;
    }

    :deep(.el-progress-bar__outer) {
      border-radius: 10px;
      background-color: #f0f2f5;
      box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    :deep(.el-progress-bar__inner) {
      border-radius: 10px;
      transition: width 0.6s ease;
    }


  }

  .status-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .exception-text {
      font-size: 12px;
      color: #F56C6C;
      font-weight: 500;
    }

    .status-exception {
      border-color: #F56C6C;
      background-color: #fef0f0;
      color: #F56C6C;
    }
  }

  .report-actions {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;

    .el-button {
      margin: 0;
    }
  }

  .action-buttons {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;

    .el-button {
      margin: 0;
      padding: 4px 8px;
      font-size: 12px;
    }
  }

  // 任务详情样式
  .task-detail {
    .progress-detail {
      display: flex;
      align-items: center;
      gap: 12px;

      .progress-text {
        font-size: 14px;
        font-weight: 500;
        color: #606266;
        min-width: 40px;
      }
    }

    :deep(.el-descriptions) {
      .el-descriptions__label {
        font-weight: 600;
        color: #303133;
      }

      .el-descriptions__content {
        color: #606266;
      }
    }
  }

  // 弹窗样式优化
  .dialog-footer {
    text-align: right;

    .el-button {
      margin-left: 12px;
    }
  }

  // 字段映射样式
  .field-mapping-container {
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    padding: 16px;
    background-color: #fafafa;

    .mapping-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 2px solid #e4e7ed;
      font-weight: 600;
      color: #303133;

      .mapping-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;

        .section-title {
          font-size: 14px;
          color: #409eff;
          text-align: center;
        }

        .field-headers {
          display: flex;
          gap: 12px;

          span {
            flex: 1;
            text-align: center;
            font-size: 12px;
            color: #606266;
          }
        }
      }

      .mapping-arrow {
        width: 40px;
        text-align: center;
        font-size: 16px;
        color: #409eff;
        font-weight: bold;
      }

      .mapping-actions {
        width: 80px;
        text-align: center;
        font-size: 12px;
        color: #606266;
      }
    }

    .mapping-row {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      padding: 12px;
      background-color: white;
      border-radius: 4px;
      border: 1px solid #e4e7ed;

      .mapping-section {
        flex: 1;
        display: flex;
        gap: 12px;

        .el-select {
          flex: 1;
        }
      }

      .mapping-arrow {
        width: 40px;
        text-align: center;
        font-size: 16px;
        color: #409eff;
        font-weight: bold;
      }

      .mapping-actions {
        width: 80px;
        text-align: center;
      }
    }

    .add-mapping {
      text-align: center;
      margin-top: 16px;
      padding-top: 12px;
      border-top: 1px dashed #e4e7ed;
    }
  }

  // 表格样式优化
  :deep(.el-table) {
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #f8f9fa;
          color: #303133;
          font-weight: 600;
          border-bottom: 2px solid #e4e7ed;
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover {
            background-color: #f5f7fa;
          }

          td {
            border-bottom: 1px solid #ebeef5;
            padding: 12px 0;
          }
        }
      }
    }
  }

  // 按钮样式优化
  .el-button {
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &.is-disabled {
      transform: none;
      box-shadow: none;
    }
  }

  // 搜索区域样式
  .search {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
  }

  // 分页容器样式
  .pagination-container {
    display: flex;
    justify-content: flex-end;
    padding: 16px 0;
  }
}

// 日志弹窗样式 - 统一样式
.records-content {
  .records-actions {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #EBEEF5;
    gap: 8px;
  }

  .records-search {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 16px;
  }

  .records-pagination {
    display: flex;
    justify-content: flex-end;
    padding: 16px 0 0 0;
    border-top: 1px solid #EBEEF5;
    margin-top: 16px;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  // 空状态样式
  .empty-state {
    padding: 40px 20px;
    text-align: center;

    .el-empty {
      padding: 20px;
    }
  }
}

// 动画效果
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}


// 响应式设计
@media (max-width: 768px) {
  .data-integration {
    .report-actions {
      flex-direction: column;
      gap: 2px;

      .el-button {
        width: 100%;
        margin: 0;
      }
    }

    :deep(.el-table) {
      font-size: 12px;

      .el-table__header-wrapper,
      .el-table__body-wrapper {
        .el-table__header th,
        .el-table__body td {
          padding: 8px 4px;
        }
      }
    }
  }

  .records-content {
    .records-search {
      .el-input {
        width: 100% !important;
      }
    }
  }
}

@media (max-width: 480px) {
  .data-integration {
    .search {
      padding: 12px;
    }

    :deep(.el-button) {
      font-size: 12px;
      padding: 6px 12px;
    }
  }


}
</style>
