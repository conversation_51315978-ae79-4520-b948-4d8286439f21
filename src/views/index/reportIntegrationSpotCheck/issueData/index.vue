<script setup lang="ts" name="issueData">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Search, Refresh, Download, Printer, Share } from '@element-plus/icons-vue'
import * as ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'

// 路由实例
const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const tableHeight = ref(600)
const exportLoading = ref(false)
const printLoading = ref(false)
const shareLoading = ref(false)

// 问题数据接口定义
interface IssueData {
  id: string,
  index: number,
  problemId: string
  category: string
  createTime: string
  mark: string
  status: '处理中' | '已完成' | '未处理'
  description?: string
  operator?: string
  updateTime?: string
  isImportant: boolean // 是否为重点问题
  sourceReport?: string // 来源报表
  originalValue?: string // 原数据值
  errorType?: string // 错误类型
  operationHistory?: string[] // 操作历史
  suggestedAction?: string // 建议措施
}

// 表格相关
const tableRef = ref()
const tableData = ref<IssueData[]>([])
const selectedRows = ref<IssueData[]>([])

// 表头配置
const columns = [
  { prop: 'index', label: '序号', width: 80, sortable: true },
  { prop: 'problemId', label: '问题数据ID', minWidth: 120, sortable: true },
  { prop: 'category', label: '类别', minWidth: 120 },
  { prop: 'createTime', label: '创建时间', minWidth: 160, sortable: true },
  { prop: 'mark', label: '标记', minWidth: 80, align: 'center', sortable: true },
  { prop: 'status', label: '状态', minWidth: 100 },
  { prop: 'operator', label: '操作员', minWidth: 100 },
  { prop: 'updateTime', label: '更新时间', minWidth: 160 },
  { prop: 'action', label: '操作', width: 120, fixed: 'right' }
]

// 详情弹窗相关
const showDetailDialog = ref(false)
const currentDetailData = ref<IssueData | null>(null)

// 搜索表单配置
const searchFormProp = ref([
  { label: '问题ID', prop: 'problemId', type: 'text', placeholder: '请输入问题ID' },
  {
    label: '类别',
    prop: 'category',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '数据完整性', value: '数据完整性' },
      { label: '数据一致性', value: '数据一致性' },
      { label: '数据准确性', value: '数据准确性' },
      { label: '数据时效性', value: '数据时效性' },
      { label: '格式规范性', value: '格式规范性' },
      { label: '业务逻辑', value: '业务逻辑' }
    ]
  },
  {
    label: '状态',
    prop: 'status',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '未处理', value: '未处理' },
      { label: '处理中', value: '处理中' },
      { label: '已完成', value: '已完成' }
    ]
  },
  { label: '开始时间', prop: 'startTime', type: 'date' },
  { label: '结束时间', prop: 'endTime', type: 'date' }
])

// 搜索表单数据
const searchForm = ref({
  problemId: '',
  category: '',
  status: '',
  startTime: '',
  endTime: ''
})

// 分页配置
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 排序状态 - 默认按序号升序排列
const sortConfig = ref({
  prop: 'index',
  order: 'ascending' // 'ascending' | 'descending' | ''
})



// 标记选项
const markOptions = [
  { label: '高优先级', value: '高优先级' },
  { label: '中优先级', value: '中优先级' },
  { label: '低优先级', value: '低优先级' },
  { label: '紧急', value: '紧急' },
  { label: '一般', value: '一般' }
]

// 错误类型选项
const errorTypeOptions = [
  '数据输入错误',
  '格式不正确',
  '缺失值',
  '超出合理范围',
  '逻辑矛盾',
  '编码错误',
  '系统故障',
  '数据源变更',
  '异常值',
  '单位错误'
]

// 来源报表选项
const sourceReportOptions = [
  '财务月报',
  '人员统计表',
  '项目进度报告',
  '设备清单',
  '预算执行表',
  '绩效考核表',
  '库存盘点表',
  '客户信息表'
]

// 获取状态标签类型
const getStatusType = (status: string) => {
  switch (status) {
    case '已完成':
      return 'success'
    case '处理中':
      return 'warning'
    case '未处理':
      return 'danger'
    default:
      return 'info'
  }
}



// 生成更真实的原始数据值
const generateOriginalValue = (errorType: string): string => {
  switch (errorType) {
    case '数据输入错误':
      return Math.random() > 0.5 ? `${Math.floor(Math.random() * 900) + 100}0` : `${Math.random() > 0.5 ? 'O' : 'l'}${Math.floor(Math.random() * 900) + 100}`
    case '格式不正确':
      return Math.random() > 0.5 ? `${new Date().getFullYear()}-${Math.floor(Math.random() * 12) + 1}-${Math.floor(Math.random() * 31) + 1}` : `${Math.floor(Math.random() * 900) + 100}/${Math.floor(Math.random() * 900) + 100}`
    case '缺失值':
      return ''
    case '超出合理范围':
      return `${Math.floor(Math.random() * 9000) + 10000}`
    case '逻辑矛盾':
      return Math.random() > 0.5 ? `开始日期：2024-01-01，结束日期：2023-12-31` : `数量：-${Math.floor(Math.random() * 100) + 1}`
    case '编码错误':
      return `ERR_CODE_${Math.floor(Math.random() * 900) + 100}`
    case '系统故障':
      return `SYS_ERROR_${Math.floor(Math.random() * 900) + 100}`
    case '数据源变更':
      return `旧数据源：DB_${Math.floor(Math.random() * 10) + 1}，新数据源：DB_${Math.floor(Math.random() * 10) + 1}`
    case '异常值':
      return Math.random() > 0.5 ? `${Math.floor(Math.random() * 90000) + 10000}` : `${Math.random() > 0.5 ? '-' : ''}0.${Math.floor(Math.random() * 9) + 1}`
    case '单位错误':
      return `${Math.floor(Math.random() * 900) + 100}${Math.random() > 0.5 ? 'kg' : 'g'}`
    default:
      return `原始值${Math.floor(Math.random() * 1000)}`
  }
}

// 生成模拟数据
const generateMockData = (): IssueData[] => {
  const data: IssueData[] = []
  const categories = ['数据完整性', '数据一致性', '数据准确性', '数据时效性', '格式规范性', '业务逻辑']
  const statuses: Array<'处理中' | '已完成' | '未处理'> = ['处理中', '已完成', '未处理']
  const marks = markOptions.map(item => item.value)

  for (let i = 1; i <= 50; i++) {
    const createTime = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
    const isImportant = Math.random() < 0.3 // 30%为重点问题
    const errorType = errorTypeOptions[Math.floor(Math.random() * errorTypeOptions.length)]
    const sourceReport = sourceReportOptions[Math.floor(Math.random() * sourceReportOptions.length)]

    // 生成操作历史
    const operationHistory = []
    const historyCount = Math.floor(Math.random() * 3) + 1
    for (let j = 0; j < historyCount; j++) {
      const historyTime = new Date(createTime.getTime() + j * 24 * 60 * 60 * 1000)
      operationHistory.push(`${historyTime.toISOString().split('T')[0]} 操作员${Math.floor(Math.random() * 5) + 1} 执行了${j === 0 ? '创建' : j === 1 ? '分析' : '处理'}操作`)
    }

    data.push({
      id: `issue_${i.toString().padStart(3, '0')}`,
      index: i,
      problemId: `PID${(Math.floor(Math.random() * 9000) + 1000).toString()}`,
      category: categories[Math.floor(Math.random() * categories.length)],
      createTime: createTime.toISOString().split('T')[0] + ' ' + createTime.toTimeString().split(' ')[0],
      mark: marks[Math.floor(Math.random() * marks.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      description: `问题描述 ${i}：数据质量检查发现的问题`,
      operator: Math.random() > 0.5 ? `操作员${Math.floor(Math.random() * 10) + 1}` : '',
      updateTime: Math.random() > 0.3 ? new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0] : '',
      isImportant,
      sourceReport,
      originalValue: generateOriginalValue(errorType),
      errorType,
      operationHistory,
      suggestedAction: `建议${errorType === '数据输入错误' ? '重新录入数据' : errorType === '格式不正确' ? '调整数据格式' : errorType === '缺失值' ? '补充缺失数据' : '联系技术支持处理'}`
    })
  }

  return data.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())
}

// 过滤数据
const filteredData = computed(() => {
  let data = [...tableData.value]

  // 按问题ID筛选
  if (searchForm.value.problemId) {
    data = data.filter(item =>
      item.problemId.toLowerCase().includes(searchForm.value.problemId.toLowerCase())
    )
  }

  // 按类别筛选
  if (searchForm.value.category) {
    data = data.filter(item => item.category === searchForm.value.category)
  }

  // 按状态筛选
  if (searchForm.value.status) {
    data = data.filter(item => item.status === searchForm.value.status)
  }

  // 按时间范围筛选
  if (searchForm.value.startTime && searchForm.value.endTime) {
    const startDate = new Date(searchForm.value.startTime)
    const endDate = new Date(searchForm.value.endTime)
    data = data.filter(item => {
      const itemDate = new Date(item.createTime)
      return itemDate >= startDate && itemDate <= endDate
    })
  }

  return data
})

// 获取排序后的数据
const getSortedData = (data: IssueData[]) => {
  if (!sortConfig.value.prop || !sortConfig.value.order) {
    return data
  }

  const { prop, order } = sortConfig.value

  return [...data].sort((a, b) => {
    let aValue: any = a[prop as keyof IssueData]
    let bValue: any = b[prop as keyof IssueData]

    // 处理不同类型的排序
    if (prop === 'index') {
      // 序号按数值排序
      aValue = Number(aValue)
      bValue = Number(bValue)
    } else if (prop === 'createTime') {
      aValue = new Date(aValue).getTime()
      bValue = new Date(bValue).getTime()
    } else if (prop === 'problemId') {
      // 提取数字部分进行排序
      const aNum = parseInt(aValue.replace(/\D/g, '')) || 0
      const bNum = parseInt(bValue.replace(/\D/g, '')) || 0
      aValue = aNum
      bValue = bNum
    } else if (prop === 'mark') {
      // 标记按优先级排序：紧急 > 高优先级 > 中优先级 > 低优先级 > 一般
      const markOrder = { '紧急': 5, '高优先级': 4, '中优先级': 3, '低优先级': 2, '一般': 1 }
      aValue = markOrder[aValue as keyof typeof markOrder] || 0
      bValue = markOrder[bValue as keyof typeof markOrder] || 0
    }

    if (order === 'ascending') {
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
    } else {
      return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
    }
  })
}

// 获取当前页数据
const getCurrentPageData = () => {
  let filtered = filteredData.value

  // 应用排序
  if (sortConfig.value.prop && sortConfig.value.order) {
    filtered = getSortedData(filtered)
  }

  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  pagination.total = filtered.length
  return filtered.slice(start, end)
}

// 更新分页总数
const updatePagination = () => {
  pagination.total = filteredData.value.length
}

// 搜索数据
const onSearch = async () => {
  try {
    searchLoading.value = true

    // 模拟搜索延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    pagination.page = 1
    updatePagination()
    ElMessage.success('查询完成')
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    searchLoading.value = false
  }
}

// 处理排序变化
const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  sortConfig.value.prop = prop
  sortConfig.value.order = order

  // 重新加载数据
  pagination.page = 1
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type === 'page') {
    pagination.page = val
  } else {
    pagination.size = val
    pagination.page = 1
  }
}

// 表格选择变化
const handleSelectionChange = (selection: IssueData[]) => {
  selectedRows.value = selection
}

// 查看详情
const handleViewDetail = (row: IssueData) => {
  currentDetailData.value = row
  showDetailDialog.value = true
}

// 关闭详情弹窗
const handleCloseDetail = () => {
  showDetailDialog.value = false
  currentDetailData.value = null
}

// 返回上一页
const handleGoBack = () => {
  router.push('/reportIntegrationSpotCheck/resultDisplay')
}

// 导出Excel功能
const handleExportExcel = async () => {
  try {
    exportLoading.value = true

    // 确定导出数据：如果有选中行则导出选中数据，否则导出全部数据
    const exportData = selectedRows.value.length > 0 ? selectedRows.value : getCurrentPageData()
    const exportType = selectedRows.value.length > 0 ? '选中' : '全部'

    if (exportData.length === 0) {
      ElMessage.warning('没有可导出的数据')
      return
    }

    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    workbook.creator = 'Issue Data System'
    workbook.lastModifiedBy = 'System'
    workbook.created = new Date()
    workbook.modified = new Date()

    // 创建工作表
    const worksheet = workbook.addWorksheet('问题数据')

    // 设置列定义
    worksheet.columns = [
      { header: '序号', key: 'index', width: 10 },
      { header: '问题数据ID', key: 'problemId', width: 15 },
      { header: '类别', key: 'category', width: 15 },
      { header: '创建时间', key: 'createTime', width: 20 },
      { header: '标记', key: 'mark', width: 12 },
      { header: '状态', key: 'status', width: 12 },
      { header: '操作员', key: 'operator', width: 12 },
      { header: '更新时间', key: 'updateTime', width: 20 },
      { header: '来源报表', key: 'sourceReport', width: 15 },
      { header: '错误类型', key: 'errorType', width: 15 },
      { header: '原数据值', key: 'originalValue', width: 20 },
      { header: '建议措施', key: 'suggestedAction', width: 25 }
    ]

    // 设置表头样式
    const headerRow = worksheet.getRow(1)
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    }
    headerRow.alignment = { vertical: 'middle', horizontal: 'center' }

    // 添加数据
    exportData.forEach((row, index) => {
      worksheet.addRow({
        index: index + 1,
        problemId: row.problemId,
        category: row.category,
        createTime: row.createTime,
        mark: row.isImportant ? '★ 重点问题' : '☆ 普通问题',
        status: row.status,
        operator: row.operator || '',
        updateTime: row.updateTime || '',
        sourceReport: row.sourceReport || '',
        errorType: row.errorType || '',
        originalValue: row.originalValue || '',
        suggestedAction: row.suggestedAction || ''
      })
    })

    // 设置数据行样式
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber > 1) { // 跳过表头
        row.alignment = { vertical: 'middle', horizontal: 'left' }
      }

      // 设置边框
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        }
      })
    })

    // 自动调整列宽
    worksheet.columns.forEach((column) => {
      if (column.width && column.width < 10) {
        column.width = 10
      }
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    // 生成文件名：{地区名}-{开始年月日}-{结束年月日}-问题数据一览.xlsx
    const now = new Date()
    const startDate = searchForm.value.startTime ?
      new Date(searchForm.value.startTime).toISOString().slice(0, 10).replace(/-/g, '') :
      new Date(now.getFullYear(), now.getMonth(), 1).toISOString().slice(0, 10).replace(/-/g, '')
    const endDate = searchForm.value.endTime ?
      new Date(searchForm.value.endTime).toISOString().slice(0, 10).replace(/-/g, '') :
      now.toISOString().slice(0, 10).replace(/-/g, '')

    const fileName = `重庆市-${startDate}-${endDate}-问题数据一览.xlsx`

    // 下载文件
    saveAs(blob, fileName)

    ElMessage.success(`成功导出${exportType} ${exportData.length} 条数据`)
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    exportLoading.value = false
  }
}

// 打印功能
const handlePrint = async () => {
  try {
    printLoading.value = true

    // 确定打印数据：如果有选中行则打印选中数据，否则打印全部数据
    const printData = selectedRows.value.length > 0 ? selectedRows.value : getCurrentPageData()
    const printType = selectedRows.value.length > 0 ? '选中' : '全部'

    if (printData.length === 0) {
      ElMessage.warning('没有可打印的数据')
      return
    }

    // 创建新窗口用于打印
    const printWindow = window.open('', '_blank')
    if (!printWindow) {
      throw new Error('无法打开打印窗口，请检查浏览器弹窗设置')
    }

    // 生成表格HTML
    const generateTableRows = (data: IssueData[]) => {
      return data.map((row, index) => `
        <tr>
          <td>${index + 1}</td>
          <td>${row.problemId}</td>
          <td>${row.category}</td>
          <td>${row.createTime}</td>
          <td>${row.isImportant ? '★ 重点问题' : '☆ 普通问题'}</td>
          <td>${row.status}</td>
          <td>${row.operator || ''}</td>
          <td>${row.updateTime || ''}</td>
          <td>${row.sourceReport || ''}</td>
          <td>${row.errorType || ''}</td>
          <td>${row.originalValue || ''}</td>
          <td>${row.suggestedAction || ''}</td>
        </tr>
      `).join('')
    }

    // 创建打印文档内容
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>重庆市问题数据一览表</title>
        <style>
          body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            font-size: 12px;
            line-height: 1.4;
          }
          .report-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
          }
          .report-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
          }
          .report-info {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
          }
          .report-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          .report-table th, .report-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            vertical-align: middle;
          }
          .report-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
          }
          .report-table tr:nth-child(even) {
            background-color: #f9f9f9;
          }
          .report-footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            text-align: center;
            font-size: 12px;
            color: #999;
          }
          .summary-info {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #409EFF;
          }
          .summary-item {
            display: inline-block;
            margin-right: 30px;
            font-weight: bold;
          }
          @media print {
            body { margin: 0; }
            .report-header { page-break-after: avoid; }
            .report-table { page-break-inside: avoid; }
            .summary-info { page-break-after: avoid; }
          }
        </style>
      </head>
      <body>
        <div class="report-header">
          <div class="report-title">重庆市问题数据一览表</div>
          <div class="report-info">生成时间：${new Date().toLocaleString('zh-CN')}</div>
          <div class="report-info">数据范围：${printType}数据（共 ${printData.length} 条）</div>
        </div>

        <div class="summary-info">
          <div class="summary-item">总计：${printData.length} 条</div>
          <div class="summary-item">重点问题：${printData.filter(item => item.isImportant).length} 条</div>
          <div class="summary-item">已完成：${printData.filter(item => item.status === '已完成').length} 条</div>
          <div class="summary-item">处理中：${printData.filter(item => item.status === '处理中').length} 条</div>
          <div class="summary-item">未处理：${printData.filter(item => item.status === '未处理').length} 条</div>
        </div>

        <table class="report-table">
          <thead>
            <tr>
              <th>序号</th>
              <th>问题数据ID</th>
              <th>类别</th>
              <th>创建时间</th>
              <th>标记</th>
              <th>状态</th>
              <th>操作员</th>
              <th>更新时间</th>
              <th>来源报表</th>
              <th>错误类型</th>
              <th>原数据值</th>
              <th>建议措施</th>
            </tr>
          </thead>
          <tbody>
            ${generateTableRows(printData)}
          </tbody>
        </table>

        <div class="report-footer">
          <p>本报告由重庆市报表整合度抽查系统自动生成</p>
          <p>打印时间：${new Date().toLocaleString('zh-CN')}</p>
        </div>
      </body>
      </html>
    `

    // 写入文档内容
    printWindow.document.open()
    printWindow.document.write(printContent)
    printWindow.document.close()

    // 等待内容加载完成后打印
    printWindow.onload = () => {
      printWindow.print()
      printWindow.close()
    }

    ElMessage.success(`已打开${printType}数据打印预览窗口`)
  } catch (error) {
    console.error('打印失败:', error)
    ElMessage.error('打印失败，请重试')
  } finally {
    printLoading.value = false
  }
}

// 分享功能
const handleShare = async () => {
  try {
    shareLoading.value = true
    ElMessage.info('正在生成分享链接...')

    // 模拟生成分享链接的过程
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 构建分享参数
    const shareParams = new URLSearchParams()

    // 添加当前筛选条件
    if (searchForm.value.problemId) {
      shareParams.set('problemId', searchForm.value.problemId)
    }
    if (searchForm.value.category) {
      shareParams.set('category', searchForm.value.category)
    }
    if (searchForm.value.status) {
      shareParams.set('status', searchForm.value.status)
    }
    if (searchForm.value.startTime) {
      shareParams.set('startTime', searchForm.value.startTime)
    }
    if (searchForm.value.endTime) {
      shareParams.set('endTime', searchForm.value.endTime)
    }

    // 添加分享标识和时间戳
    shareParams.set('share', Date.now().toString())
    shareParams.set('page', pagination.page.toString())
    shareParams.set('size', pagination.size.toString())

    // 如果有选中数据，添加选中数据的ID
    if (selectedRows.value.length > 0) {
      const selectedIds = selectedRows.value.map(row => row.id).join(',')
      shareParams.set('selected', selectedIds)
    }

    // 生成分享链接
    const shareUrl = `${window.location.origin}/reportIntegrationSpotCheck/issueData?${shareParams.toString()}`

    // 准备分享内容
    const shareTitle = '重庆市问题数据一览'
    const shareText = `问题数据统计：共 ${filteredData.value.length} 条数据，${selectedRows.value.length > 0 ? `已选中 ${selectedRows.value.length} 条` : '查看全部数据'}`

    // 尝试使用Web Share API（如果支持）
    if (navigator.share) {
      await navigator.share({
        title: shareTitle,
        text: shareText,
        url: shareUrl
      })
      ElMessage.success('分享成功')
    } else {
      // 备选方案：复制到剪贴板
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(shareUrl)
        ElMessage.success('分享链接已复制到剪贴板')

        // 显示分享信息
        ElMessageBox.alert(
          `${shareText}\n\n链接已复制到剪贴板，您可以粘贴分享给其他人。`,
          shareTitle,
          {
            confirmButtonText: '确定',
            type: 'success'
          }
        )
      } else {
        // 最后的备选方案：显示链接
        ElMessageBox.alert(
          `${shareText}\n\n请复制以下链接分享：\n${shareUrl}`,
          shareTitle,
          {
            confirmButtonText: '确定',
            type: 'info'
          }
        )
      }
    }
  } catch (error: any) {
    console.error('分享失败:', error)
    if (error?.name === 'AbortError') {
      // 用户取消分享
      return
    }
    ElMessage.error('分享失败，请重试')
  } finally {
    shareLoading.value = false
  }
}

// Block高度变化处理
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 120 // 减去搜索区域和分页的高度
}

// 初始化数据
const initData = async () => {
  try {
    loading.value = true

    // 检查localStorage中的数据是否包含新字段
    const savedData = localStorage.getItem('reportIntegrationIssueData')
    let needRegenerate = false

    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData)
        // 检查第一条数据是否包含新字段
        if (parsedData.length > 0 && (!parsedData[0].hasOwnProperty('isImportant') || !parsedData[0].hasOwnProperty('sourceReport') || !parsedData[0].hasOwnProperty('errorType'))) {
          needRegenerate = true
        } else {
          tableData.value = parsedData
        }
      } catch (error) {
        console.warn('解析问题数据失败，重新生成:', error)
        needRegenerate = true
      }
    } else {
      needRegenerate = true
    }

    if (needRegenerate) {
      console.log('重新生成问题数据...')
      tableData.value = generateMockData()
      localStorage.setItem('reportIntegrationIssueData', JSON.stringify(tableData.value))
    }

    updatePagination()
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('数据加载失败，请刷新页面重试')
    // 使用默认数据
    tableData.value = generateMockData()
    updatePagination()
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  initData()
})
</script>

<route>
{
  meta: {
    title: '问题数据',
    ignoreLabel: false
  }
}
</route>

<template>
  <div class="issue-data">
    <Block
      title="问题数据列表"
      :enable-expand-content="true"
      :enableBackButton="false"
      :enable-fixed-height="true"
      :enable-close-button="false"
      @content-expand="() => {}"
      @height-changed="onBlockHeightChanged"
    >
      <template #topRight>
        <el-button
          size="small"
          type="primary"
          :loading="exportLoading"
          @click="handleExportExcel"
          style="margin-right: 8px"
        >
          <el-icon style="margin-right: 4px">
            <Download />
          </el-icon>
          导出
        </el-button>
        <el-button
          size="small"
          type="success"
          :loading="printLoading"
          @click="handlePrint"
          style="margin-right: 8px"
        >
          <el-icon style="margin-right: 4px">
            <Printer />
          </el-icon>
          打印
        </el-button>
        <el-button
          size="small"
          type="warning"
          :loading="shareLoading"
          @click="handleShare"
          style="margin-right: 8px"
        >
          <el-icon style="margin-right: 4px">
            <Share />
          </el-icon>
          分享
        </el-button>
        <el-button
          size="small"
          type="default"
          @click="handleGoBack"
          style="margin-right: 8px"
        >
          <el-icon style="margin-right: 4px">
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>
      </template>

      <template #expand>
        <!-- 搜索区域 -->
        <div class="search" v-loading="searchLoading" element-loading-background="rgba(255, 255, 255, 0.8)">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="6"
            :label-width="74"
            :enable-reset="false"
            confirm-text="查询"
            button-vertical="flowing"
            @submit="onSearch"
          />
        </div>
      </template>

      <!-- 数据表格 -->
      <TableV2
        ref="tableRef"
        :columns="columns"
        :defaultTableData="getCurrentPageData()"
        :enable-toolbar="false"
        :enable-own-button="false"
        :enable-selection="true"
        :enable-index="false"
        :height="tableHeight"
        :loading="loading"
        :default-sort="{ prop: 'index', order: 'ascending' }"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <!-- 序号列 -->
        <template #index="{ row }">
          {{ (pagination.page - 1) * pagination.size + row.index }}
        </template>

        <!-- 标记列 -->
        <template #mark="{ row }">
          <span
            :style="{ color: '#FFD700', fontSize: '18px' }"
            :title="row.isImportant ? '重点问题' : '普通问题'"
          >
            {{ row.isImportant ? '★' : '☆' }}
          </span>
        </template>

        <!-- 状态列 -->
        <template #status="{ row }">
          <el-tag :type="getStatusType(row.status)" size="small">
            {{ row.status }}
          </el-tag>
        </template>

        <!-- 操作列 -->
        <template #action="{ row }">
          <div class="action-buttons">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
            >
              查看详情
            </el-button>
          </div>
        </template>
      </TableV2>

      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.size"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      />
    </Block>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="showDetailDialog"
      title="问题数据详情"
      width="700px"
      :before-close="handleCloseDetail"
    >
      <div v-if="currentDetailData" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="问题数据ID">
            {{ currentDetailData.problemId }}
          </el-descriptions-item>
          <el-descriptions-item label="来源报表">
            {{ currentDetailData.sourceReport }}
          </el-descriptions-item>
          <el-descriptions-item label="问题类别">
            {{ currentDetailData.category }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ currentDetailData.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="原数据值">
            {{ currentDetailData.originalValue }}
          </el-descriptions-item>
          <el-descriptions-item label="错误类型">
            <el-tag type="danger" size="small">{{ currentDetailData.errorType }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="处理状态">
            <el-tag :type="getStatusType(currentDetailData.status)" size="small">
              {{ currentDetailData.status }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="重要程度">
            <span :style="{ color: '#FFD700', fontSize: '16px' }">
              {{ currentDetailData.isImportant ? '★ 重点问题' : '☆ 普通问题' }}
            </span>
          </el-descriptions-item>
        </el-descriptions>

        <div class="detail-section">
          <h4>操作历史</h4>
          <div class="operation-history">
            <el-timeline>
              <el-timeline-item
                v-for="(history, index) in currentDetailData.operationHistory"
                :key="index"
                :timestamp="history.split(' ')[0]"
                placement="top"
              >
                {{ history.substring(11) }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>

        <div class="detail-section">
          <h4>建议措施</h4>
          <div class="suggested-action">
            <el-alert
              :title="currentDetailData.suggestedAction"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="handleCloseDetail">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.issue-data {
  height: 100%;
  overflow: hidden;

  .search {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    :deep(.form-container) {
      .el-form-item {
        margin-bottom: 8px;
        margin-right: 16px;
      }

      .el-form-item__label {
        font-weight: 500;
        color: #606266;
      }
    }
  }

  // 表格操作按钮样式
  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;

    .el-button {
      margin: 0;
    }
  }

  // 详情弹窗样式
  .detail-content {
    .detail-section {
      margin-top: 20px;

      h4 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }

      .operation-history {
        max-height: 200px;
        overflow-y: auto;

        :deep(.el-timeline) {
          padding-left: 0;
        }

        :deep(.el-timeline-item__timestamp) {
          color: #909399;
          font-size: 12px;
        }

        :deep(.el-timeline-item__content) {
          color: #606266;
          font-size: 13px;
        }
      }

      .suggested-action {
        :deep(.el-alert) {
          .el-alert__title {
            font-size: 13px;
            line-height: 1.5;
          }
        }
      }
    }

    :deep(.el-descriptions) {
      .el-descriptions__label {
        font-weight: 600;
        color: #606266;
        width: 120px;
      }

      .el-descriptions__content {
        color: #303133;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .search {
      :deep(.form-container) {
        .el-form-item {
          margin-right: 8px;
          margin-bottom: 12px;
        }

        .el-input,
        .el-select {
          width: 140px !important;
        }

        .el-date-editor {
          width: 130px !important;
        }
      }
    }

    .action-buttons {
      .el-button {
        font-size: 12px;
        padding: 4px 8px;
      }
    }

    // 详情弹窗响应式
    .detail-content {
      :deep(.el-descriptions) {
        .el-descriptions__label {
          width: 100px;
          font-size: 12px;
        }

        .el-descriptions__content {
          font-size: 12px;
        }
      }

      .detail-section {
        h4 {
          font-size: 13px;
        }

        .operation-history {
          max-height: 150px;

          :deep(.el-timeline-item__content) {
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>
