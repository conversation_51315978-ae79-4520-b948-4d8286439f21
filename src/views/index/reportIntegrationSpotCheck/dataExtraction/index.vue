查看<script setup lang="ts" name="dataExtraction">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Plus, Download, Delete } from '@element-plus/icons-vue'

// 路由实例
const router = useRouter()

// 数据抽取周期接口
interface ExtractionCycle {
  id: string
  index: number
  cycleName: string
  cycleTime: [string, string]
  extractionScope: string[]
  extractionFields: string[]
  outputFormat: string
  ruleSettings: string
  createTime: string
  progress: number
  status: '待执行' | '进行中' | '已暂停' | '已完结'
  extractionRecords: any[]
}

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const tableHeight = ref(0)
const tableRef = ref()
const selectedRows = ref<ExtractionCycle[]>([])

// 搜索表单
const searchFormProp = ref([
  { 
    label: '创建时间', 
    prop: 'createTimeRange', 
    type: 'daterange',
    placeholder: ['开始时间', '结束时间']
  }
])
const searchForm = ref({ createTimeRange: [] })

// 表头配置
const columns = [
  { prop: 'index', label: '序号', width: 80 },
  { prop: 'cycleName', label: '数据抽取周期名称', minWidth: 200 },
  { prop: 'createTime', label: '创建时间', minWidth: 150, sortable: 'custom' },
  { prop: 'progress', label: '进程', minWidth: 150 },
  { prop: 'status', label: '状态', minWidth: 100 },
  { prop: 'extractionRecords', label: '抽取记录', minWidth: 150 },
  { prop: 'action', label: '操作', width: 200, fixed: 'right' }
]

// 分页配置
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 表格数据
const tableData = ref<ExtractionCycle[]>([])

// 创建新周期对话框相关
const showCreateDialog = ref(false)
const dialogTitle = ref('创建新周期')
const isEditing = ref(false)
const editingCycleId = ref('')
const createForm = ref({
  cycleName: '',
  cycleTime: ['', ''] as [string, string],
  extractionScope: [] as string[],
  extractionFields: [] as string[],
  outputFormat: '.CSV',
  ruleSettings: ''
})
const createFormRef = ref()

// 查看抽取记录对话框相关
const showRecordsDialog = ref(false)
const currentRecords = ref<any[]>([])
const allRecords = ref<any[]>([]) // 保存所有记录用于查询
const recordsDialogTitle = ref('')
const recordsPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})
const recordsTableHeight = ref(400)
const recordsSearchLoading = ref(false)
const selectedRecords = ref<any[]>([])
const recordsTableRef = ref()

// 抽取记录搜索表单
const recordsSearchForm = ref({
  ruleKeyword: '',
  status: '',
  timeRange: ['', ''] as [string, string]
})

const recordsSearchFormProp = ref([
  { label: '规则关键词', prop: 'ruleKeyword', type: 'text' },
  {
    label: '状态',
    prop: 'status',
    type: 'select',
    options: [
      { label: '全部', value: '' },
      { label: '抽取成功', value: '抽取成功' },
      { label: '抽取失败', value: '抽取失败' }
    ]
  },
  {
    label: '抽取时间',
    prop: 'timeRange',
    type: 'daterange',
    placeholder: ['开始时间', '结束时间']
  }
])

// 抽取记录表头配置
const recordsColumns = [
  { prop: '序号', label: '序号', width: 80 },
  { prop: '数据内容', label: '数据内容', minWidth: 250 },
  { prop: '抽取时间', label: '抽取时间', minWidth: 160, sortable: 'custom' },
  { prop: '抽取规则', label: '抽取规则', minWidth: 180 },
  { prop: '状态', label: '状态', width: 100 },
  { prop: '抽取错误原因', label: '抽取错误原因', minWidth: 150 },
  { prop: '操作', label: '操作', width: 80, fixed: 'right' }
]

// 查看周期详情对话框相关
const showViewDialog = ref(false)
const viewForm = ref({
  cycleName: '',
  cycleTime: ['', ''] as [string, string],
  extractionScope: [] as string[],
  extractionFields: [] as string[],
  outputFormat: '.CSV',
  ruleSettings: '',
  createTime: '',
  status: '',
  progress: 0
})

// 抽取数据恢复对话框相关
const showRecoveryDialog = ref(false)
const recoveryForm = ref({
  backupInterval: '24个小时',
  recoveryTime: ''
})
const recoveryFormRef = ref()

// 备份时间间隔选项
const backupIntervalOptions = [
  { label: '1个小时', value: '1个小时' },
  { label: '2个小时', value: '2个小时' },
  { label: '12个小时', value: '12个小时' },
  { label: '24个小时', value: '24个小时' }
]

// 抽取数据恢复表单验证规则
const recoveryFormRules = {
  backupInterval: [
    { required: true, message: '请选择自动备份时间间隔', trigger: 'change' }
  ],
  recoveryTime: [
    { required: true, message: '请选择恢复时间点', trigger: 'change' }
  ]
}

// 重庆市区县数据
const chongqingDistricts = [
  '渝中区', '江北区', '南岸区', '九龙坡区', '沙坪坝区', '大渡口区',
  '渝北区', '巴南区', '北碚区', '綦江区', '大足区', '长寿区',
  '江津区', '合川区', '永川区', '南川区', '璧山区', '铜梁区',
  '潼南区', '荣昌区'
]

// 抽取字段选项
const extractionFieldOptions = [
  { label: '性别', value: '性别' },
  { label: '住址', value: '住址' },
  { label: '电话', value: '电话' },
  { label: '身份证号', value: '身份证号' }
]

// 表单验证规则
const createFormRules = {
  cycleName: [
    { required: true, message: '请输入周期名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  cycleTime: [
    { required: true, message: '请选择周期时间', trigger: 'change' }
  ],
  extractionScope: [
    { required: true, message: '请选择抽取范围', trigger: 'change' }
  ],
  extractionFields: [
    { required: true, message: '请选择抽取字段', trigger: 'change' }
  ]
}

// 计算分页数据
const paginatedData = computed(() => {
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  return tableData.value.slice(start, end)
})

// 生成模拟数据
const generateMockData = (): ExtractionCycle[] => {
  const mockCycles = []
  const statuses: Array<'待执行' | '进行中' | '已暂停' | '已完结'> = ['待执行', '进行中', '已暂停', '已完结']
  
  for (let i = 1; i <= 15; i++) {
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    const progress = status === '待执行' ? 0 : 
                    status === '已完结' ? 100 : 
                    Math.floor(Math.random() * 90) + 10
    
    mockCycles.push({
      id: `cycle_${i}`,
      index: i,
      cycleName: `数据抽取周期${i}`,
      cycleTime: ['2024-04-01 09:00:00', '2024-04-30 18:00:00'] as [string, string],
      extractionScope: chongqingDistricts.slice(0, Math.floor(Math.random() * 5) + 3),
      extractionFields: extractionFieldOptions.slice(0, Math.floor(Math.random() * 3) + 1).map(f => f.value),
      outputFormat: Math.random() > 0.5 ? '.CSV' : '.JSON',
      ruleSettings: `抽取规则设定${i}`,
      createTime: new Date(2024, 3, Math.floor(Math.random() * 30) + 1, 
                          Math.floor(Math.random() * 24), 
                          Math.floor(Math.random() * 60)).toLocaleString(),
      progress,
      status,
      extractionRecords: []
    })
  }
  
  return mockCycles
}

// 初始化数据
const initData = () => {
  const stored = localStorage.getItem('dataExtractionCycles')
  if (stored) {
    try {
      tableData.value = JSON.parse(stored)
    } catch (error) {
      console.error('加载数据失败:', error)
      tableData.value = generateMockData()
      saveToLocalStorage()
    }
  } else {
    tableData.value = generateMockData()
    saveToLocalStorage()
  }
  updatePagination()
}

// 初始化恢复配置
const initRecoveryConfig = () => {
  const stored = localStorage.getItem('dataExtractionRecoveryConfig')
  if (stored) {
    try {
      const config = JSON.parse(stored)
      recoveryForm.value = {
        backupInterval: config.backupInterval || '24个小时',
        recoveryTime: config.recoveryTime || ''
      }
    } catch (error) {
      console.error('加载恢复配置失败:', error)
    }
  }
}

// 保存恢复配置到localStorage
const saveRecoveryConfig = () => {
  localStorage.setItem('dataExtractionRecoveryConfig', JSON.stringify(recoveryForm.value))
}

// 保存到localStorage
const saveToLocalStorage = () => {
  localStorage.setItem('dataExtractionCycles', JSON.stringify(tableData.value))
}

// 更新分页信息
const updatePagination = () => {
  pagination.total = tableData.value.length
}

// 返回上一页
const handleGoBack = () => {
  router.push('/reportIntegrationSpotCheck')
}

// Block高度变化处理
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 120
}

// 搜索功能
const onSearch = async () => {
  try {
    searchLoading.value = true
    await new Promise(resolve => setTimeout(resolve, 500))

    let filteredData = JSON.parse(localStorage.getItem('dataExtractionCycles') || '[]')

    if (searchForm.value.createTimeRange && searchForm.value.createTimeRange.length === 2) {
      const [startTime, endTime] = searchForm.value.createTimeRange
      filteredData = filteredData.filter((item: ExtractionCycle) => {
        const createTime = new Date(item.createTime)
        return createTime >= new Date(startTime) && createTime <= new Date(endTime)
      })
    }

    tableData.value = filteredData
    pagination.page = 1
    updatePagination()

    ElMessage.success(`搜索完成，共找到 ${filteredData.length} 条记录`)
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    searchLoading.value = false
  }
}

// 重置搜索
const onReset = () => {
  searchForm.value = { createTimeRange: [] }
  initData()
}

// 批量删除
const onBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确认删除选中的 ${selectedRows.value.length} 条记录吗？`,
      '批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const selectedIds = selectedRows.value.map(row => row.id)
    tableData.value = tableData.value.filter(item => !selectedIds.includes(item.id))

    saveToLocalStorage()
    updatePagination()
    selectedRows.value = []

    ElMessage.success('删除成功')
  } catch (error) {
    console.log('取消删除')
  }
}

// 表格选择变化
const handleSelectionChange = (selection: ExtractionCycle[]) => {
  selectedRows.value = selection
}

// 表格排序变化
const handleSortChange = ({ prop, order }: any) => {
  if (prop === 'createTime') {
    if (order === null) {
      // 取消排序，恢复原始顺序
      initData()
    } else {
      // 按时间排序
      tableData.value.sort((a, b) => {
        const timeA = new Date(a.createTime).getTime()
        const timeB = new Date(b.createTime).getTime()
        return order === 'ascending' ? timeA - timeB : timeB - timeA
      })
    }
  }
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case '待执行': return 'info'
    case '进行中': return 'primary'
    case '已暂停': return 'warning'
    case '已完结': return 'success'
    default: return 'info'
  }
}

// 生成抽取记录
const generateRecords = (row: ExtractionCycle) => {
  if (row.status === '待执行') {
    ElMessage.warning('待执行状态无法生成记录')
    return
  }

  // 生成模拟抽取记录
  const records = []
  const recordCount = Math.floor(Math.random() * 50) + 10

  const errorReasons = [
    '数据源连接超时',
    '字段格式不匹配',
    '权限验证失败',
    '数据量超出限制',
    '网络连接异常'
  ]

  for (let i = 1; i <= recordCount; i++) {
    const isSuccess = Math.random() > 0.1 // 90%成功率
    const extractTime = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000) // 最近7天内的随机时间

    const record: any = {
      id: `record_${row.id}_${i}`,
      序号: i,
      数据内容: {},
      抽取时间: extractTime.toLocaleString(),
      抽取规则: row.ruleSettings,
      状态: isSuccess ? '抽取成功' : '抽取失败',
      抽取错误原因: isSuccess ? '' : errorReasons[Math.floor(Math.random() * errorReasons.length)]
    }

    // 根据选择的抽取字段生成对应数据
    if (isSuccess) {
      row.extractionFields.forEach(field => {
        switch (field) {
          case '性别':
            record.数据内容[field] = Math.random() > 0.5 ? '男' : '女'
            break
          case '住址':
            record.数据内容[field] = `${row.extractionScope[Math.floor(Math.random() * row.extractionScope.length)]}某街道${Math.floor(Math.random() * 100) + 1}号`
            break
          case '电话':
            record.数据内容[field] = `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`
            break
          case '身份证号':
            record.数据内容[field] = `500${Math.floor(Math.random() * 1000000000000000).toString().padStart(15, '0')}`
            break
        }
      })
    } else {
      // 失败记录不生成数据内容
      record.数据内容 = {}
    }

    records.push(record)
  }

  // 按时间排序（最新的在前）
  records.sort((a, b) => new Date(b.抽取时间).getTime() - new Date(a.抽取时间).getTime())

  // 更新记录
  const index = tableData.value.findIndex(item => item.id === row.id)
  if (index !== -1) {
    tableData.value[index].extractionRecords = records
    saveToLocalStorage()
  }

  ElMessage.success(`成功生成 ${records.length} 条抽取记录`)
}

// 查看抽取记录
const viewRecords = (row: ExtractionCycle) => {
  if (row.extractionRecords.length === 0) {
    ElMessage.warning('暂无抽取记录')
    return
  }

  allRecords.value = [...row.extractionRecords]
  currentRecords.value = [...row.extractionRecords]
  recordsDialogTitle.value = `查看抽取记录 - ${row.cycleName}`
  recordsPagination.page = 1
  recordsPagination.total = currentRecords.value.length

  // 重置搜索表单
  recordsSearchForm.value = {
    ruleKeyword: '',
    status: '',
    timeRange: ['', ''] as [string, string]
  }

  // 重置选择状态
  selectedRecords.value = []

  showRecordsDialog.value = true
}

// 计算抽取记录分页数据
const paginatedRecords = computed(() => {
  const start = (recordsPagination.page - 1) * recordsPagination.size
  const end = start + recordsPagination.size
  return currentRecords.value.slice(start, end)
})

// 删除抽取记录
const deleteRecord = async (record: any) => {
  try {
    await ElMessageBox.confirm(
      `确认删除序号为 ${record.序号} 的抽取记录吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 从当前记录中删除
    const recordIndex = currentRecords.value.findIndex(r => r.id === record.id)
    if (recordIndex !== -1) {
      currentRecords.value.splice(recordIndex, 1)
      recordsPagination.total = currentRecords.value.length

      // 如果当前页没有数据了，回到上一页
      if (paginatedRecords.value.length === 0 && recordsPagination.page > 1) {
        recordsPagination.page--
      }

      // 更新原始数据
      const cycleIndex = tableData.value.findIndex(item =>
        item.extractionRecords.some(r => r.id === record.id)
      )
      if (cycleIndex !== -1) {
        const originalRecordIndex = tableData.value[cycleIndex].extractionRecords.findIndex(r => r.id === record.id)
        if (originalRecordIndex !== -1) {
          tableData.value[cycleIndex].extractionRecords.splice(originalRecordIndex, 1)
          saveToLocalStorage()
        }
      }

      // 如果删除的记录在选择列表中，需要更新选择状态
      selectedRecords.value = selectedRecords.value.filter(r => r.id !== record.id)

      ElMessage.success('删除成功')
    }
  } catch (error) {
    console.log('取消删除')
  }
}

// 抽取记录排序
const handleRecordsSortChange = ({ prop, order }: any) => {
  if (prop === '抽取时间') {
    if (order === null) {
      // 取消排序，恢复原始顺序
      const cycleIndex = tableData.value.findIndex(item =>
        item.extractionRecords.length > 0 &&
        item.extractionRecords[0].id.includes(currentRecords.value[0]?.id?.split('_')[1])
      )
      if (cycleIndex !== -1) {
        currentRecords.value = [...tableData.value[cycleIndex].extractionRecords]
      }
    } else {
      // 按时间排序
      currentRecords.value.sort((a, b) => {
        const timeA = new Date(a.抽取时间).getTime()
        const timeB = new Date(b.抽取时间).getTime()
        return order === 'ascending' ? timeA - timeB : timeB - timeA
      })
    }
    recordsPagination.page = 1
  }
}

// 抽取记录分页变化
const handleRecordsPageChange = (page: number) => {
  recordsPagination.page = page
}

const handleRecordsSizeChange = (size: number) => {
  recordsPagination.size = size
  recordsPagination.page = 1
}

// 抽取记录搜索
const onRecordsSearch = async () => {
  try {
    recordsSearchLoading.value = true
    await new Promise(resolve => setTimeout(resolve, 500))

    let filteredData = [...allRecords.value]

    // 规则关键词搜索
    if (recordsSearchForm.value.ruleKeyword) {
      filteredData = filteredData.filter(record =>
        record.抽取规则.includes(recordsSearchForm.value.ruleKeyword)
      )
    }

    // 状态筛选
    if (recordsSearchForm.value.status) {
      filteredData = filteredData.filter(record =>
        record.状态 === recordsSearchForm.value.status
      )
    }

    // 时间范围筛选
    if (recordsSearchForm.value.timeRange && recordsSearchForm.value.timeRange[0] && recordsSearchForm.value.timeRange[1]) {
      const [startTime, endTime] = recordsSearchForm.value.timeRange
      filteredData = filteredData.filter(record => {
        const recordTime = new Date(record.抽取时间)
        return recordTime >= new Date(startTime) && recordTime <= new Date(endTime)
      })
    }

    currentRecords.value = filteredData
    recordsPagination.page = 1
    recordsPagination.total = filteredData.length

    ElMessage.success(`搜索完成，共找到 ${filteredData.length} 条记录`)
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    recordsSearchLoading.value = false
  }
}

// 重置抽取记录搜索
const onRecordsReset = () => {
  recordsSearchForm.value = {
    ruleKeyword: '',
    status: '',
    timeRange: ['', ''] as [string, string]
  }
  currentRecords.value = [...allRecords.value]
  recordsPagination.page = 1
  recordsPagination.total = allRecords.value.length
}

// 抽取记录选择变化
const handleRecordsSelectionChange = (selection: any[]) => {
  selectedRecords.value = selection
}

// 批量删除抽取记录
const onBatchDeleteRecords = async () => {
  if (selectedRecords.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确认删除选中的 ${selectedRecords.value.length} 条抽取记录吗？`,
      '批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const selectedIds = selectedRecords.value.map(record => record.id)

    // 从当前记录中删除
    currentRecords.value = currentRecords.value.filter(record => !selectedIds.includes(record.id))
    allRecords.value = allRecords.value.filter(record => !selectedIds.includes(record.id))
    recordsPagination.total = currentRecords.value.length

    // 如果当前页没有数据了，回到上一页
    if (paginatedRecords.value.length === 0 && recordsPagination.page > 1) {
      recordsPagination.page--
    }

    // 更新原始数据
    tableData.value.forEach(cycle => {
      if (cycle.extractionRecords.length > 0) {
        cycle.extractionRecords = cycle.extractionRecords.filter(record => !selectedIds.includes(record.id))
      }
    })
    saveToLocalStorage()

    selectedRecords.value = []
    // 清空表格选择
    if (recordsTableRef.value && recordsTableRef.value.clearSelection) {
      recordsTableRef.value.clearSelection()
    }
    ElMessage.success(`成功删除 ${selectedIds.length} 条记录`)
  } catch (error) {
    console.log('取消删除')
  }
}

// 处理抽取记录对话框关闭
const handleRecordsDialogClosed = () => {
  console.log('抽取记录对话框关闭，重置状态')
  // 重置选择状态
  selectedRecords.value = []
  // 清空表格选择
  if (recordsTableRef.value && recordsTableRef.value.clearSelection) {
    recordsTableRef.value.clearSelection()
    console.log('已调用表格clearSelection方法')
  }
  console.log('已重置选择状态，当前选择数量:', selectedRecords.value.length)
  // 重置搜索表单
  recordsSearchForm.value = {
    ruleKeyword: '',
    status: '',
    timeRange: ['', ''] as [string, string]
  }
  // 重置分页
  recordsPagination.page = 1
}

// 获取行级操作按钮
const getRowActions = (row: ExtractionCycle) => {
  const actions = []

  switch (row.status) {
    case '待执行':
      actions.push(
        { code: 'execute', label: '执行' },
        { code: 'view', label: '查看' },
        { code: 'edit', label: '编辑' },
        { code: 'delete', label: '删除' }
      )
      break
    case '进行中':
      actions.push(
        { code: 'pause', label: '暂停' },
        { code: 'terminate', label: '终止' },
        { code: 'view', label: '查看' }
      )
      break
    case '已暂停':
      actions.push(
        { code: 'execute', label: '执行' },
        { code: 'terminate', label: '终止' },
        { code: 'view', label: '查看' }
      )
      break
    case '已完结':
      actions.push(
        { code: 'view', label: '查看' },
        { code: 'edit', label: '编辑' },
        { code: 'delete', label: '删除' }
      )
      break
  }

  return actions
}

// 获取操作按钮类型
const getActionButtonType = (actionCode: string) => {
  switch (actionCode) {
    case 'execute': return 'primary'
    case 'pause': return 'warning'
    case 'terminate': return 'danger'
    case 'view': return 'info'
    case 'edit': return 'primary'
    case 'delete': return 'danger'
    default: return 'default'
  }
}

// 判断操作是否禁用
const isActionDisabled = (_row: ExtractionCycle, _action: any) => {
  // 所有操作都不禁用，因为已经在getRowActions中过滤了
  return false
}

// 处理行级操作
const handleRowAction = async (row: ExtractionCycle, action: any) => {
  const index = tableData.value.findIndex(item => item.id === row.id)
  if (index === -1) return

  switch (action.code) {
    case 'execute':
      tableData.value[index].status = '进行中'
      tableData.value[index].progress = Math.floor(Math.random() * 30) + 10
      ElMessage.success('任务已开始执行')
      break
    case 'pause':
      tableData.value[index].status = '已暂停'
      ElMessage.success('任务已暂停')
      break
    case 'terminate':
      tableData.value[index].status = '已完结'
      tableData.value[index].progress = 100
      ElMessage.success('任务已终止')
      break
    case 'view':
      viewCycleDetails(row)
      break
    case 'edit':
      editCycle(row)
      break
    case 'delete':
      await deleteCycle(row)
      break
  }

  saveToLocalStorage()
}

// 查看周期详情
const viewCycleDetails = (row: ExtractionCycle) => {
  viewForm.value = {
    cycleName: row.cycleName,
    cycleTime: row.cycleTime,
    extractionScope: [...row.extractionScope],
    extractionFields: [...row.extractionFields],
    outputFormat: row.outputFormat,
    ruleSettings: row.ruleSettings,
    createTime: row.createTime,
    status: row.status,
    progress: row.progress
  }
  showViewDialog.value = true
}

// 编辑周期
const editCycle = (row: ExtractionCycle) => {
  isEditing.value = true
  editingCycleId.value = row.id
  dialogTitle.value = '编辑数据抽取周期'
  createForm.value = {
    cycleName: row.cycleName,
    cycleTime: row.cycleTime,
    extractionScope: [...row.extractionScope],
    extractionFields: [...row.extractionFields],
    outputFormat: row.outputFormat,
    ruleSettings: row.ruleSettings
  }
  showCreateDialog.value = true
}

// 删除周期
const deleteCycle = async (row: ExtractionCycle) => {
  try {
    await ElMessageBox.confirm(
      `确认删除周期 "${row.cycleName}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      tableData.value.splice(index, 1)
      saveToLocalStorage()
      updatePagination()
      ElMessage.success('删除成功')
    }
  } catch (error) {
    console.log('取消删除')
  }
}

// 分页变化
const handlePageChange = (page: number) => {
  pagination.page = page
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
}

// 打开创建对话框
const openCreateDialog = () => {
  isEditing.value = false
  editingCycleId.value = ''
  dialogTitle.value = '创建新周期'
  showCreateDialog.value = true
}

// 打开抽取数据恢复对话框
const openRecoveryDialog = () => {
  initRecoveryConfig()
  showRecoveryDialog.value = true
}

// 重置创建表单
const resetCreateForm = () => {
  isEditing.value = false
  editingCycleId.value = ''
  dialogTitle.value = '创建新周期'
  createForm.value = {
    cycleName: '',
    cycleTime: ['', ''] as [string, string],
    extractionScope: [] as string[],
    extractionFields: [] as string[],
    outputFormat: '.CSV',
    ruleSettings: ''
  }
  createFormRef.value?.resetFields()
}

// 处理创建确认
const handleCreateConfirm = async () => {
  try {
    await createFormRef.value?.validate()

    if (isEditing.value) {
      // 编辑模式
      const index = tableData.value.findIndex(item => item.id === editingCycleId.value)
      if (index !== -1) {
        tableData.value[index] = {
          ...tableData.value[index],
          cycleName: createForm.value.cycleName,
          cycleTime: createForm.value.cycleTime,
          extractionScope: [...createForm.value.extractionScope],
          extractionFields: [...createForm.value.extractionFields],
          outputFormat: createForm.value.outputFormat,
          ruleSettings: createForm.value.ruleSettings
        }
        ElMessage.success('编辑周期成功')
      }
    } else {
      // 创建模式
      const newCycle: ExtractionCycle = {
        id: `cycle_${Date.now()}`,
        index: tableData.value.length + 1,
        cycleName: createForm.value.cycleName,
        cycleTime: createForm.value.cycleTime,
        extractionScope: [...createForm.value.extractionScope],
        extractionFields: [...createForm.value.extractionFields],
        outputFormat: createForm.value.outputFormat,
        ruleSettings: createForm.value.ruleSettings,
        createTime: new Date().toLocaleString(),
        progress: 0,
        status: '待执行',
        extractionRecords: []
      }

      tableData.value.unshift(newCycle)
      ElMessage.success('创建新周期成功')
    }

    saveToLocalStorage()
    updatePagination()
    showCreateDialog.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 处理抽取数据恢复确认
const handleRecoveryConfirm = async () => {
  try {
    await recoveryFormRef.value?.validate()

    // 保存配置
    saveRecoveryConfig()

    // 显示成功提示
    ElMessage.success(`数据恢复配置已保存！备份间隔：${recoveryForm.value.backupInterval}，恢复时间点：${recoveryForm.value.recoveryTime}`)

    showRecoveryDialog.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 重置恢复表单
const resetRecoveryForm = () => {
  recoveryForm.value = {
    backupInterval: '24个小时',
    recoveryTime: ''
  }
  recoveryFormRef.value?.resetFields()
}

// 监听抽取记录对话框关闭
watch(showRecordsDialog, (newVal, oldVal) => {
  if (oldVal === true && newVal === false) {
    console.log('检测到抽取记录对话框关闭')
    handleRecordsDialogClosed()
  }
})

// 生命周期
onMounted(() => {
  console.log('数据抽取页面已挂载')
  initData()
  initRecoveryConfig()
})
</script>

<route>
{
  meta: {
    title: '数据抽取',
    ignoreLabel: false
  }
}
</route>

<template>
  <div class="data-extraction">
    <Block
      title="数据抽取"
      :enable-expand-content="true"
      :enableBackButton="false"
      :enable-fixed-height="true"
      :enable-close-button="false"
      @content-expand="() => {}"
      @height-changed="onBlockHeightChanged"
    >
      <template #topRight>
        <el-button 
          size="small" 
          type="default" 
          @click="handleGoBack"
          style="margin-right: 8px"
        >
          <el-icon style="margin-right: 4px">
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>
        
        <el-button size="small" type="primary" @click="openCreateDialog">
          创建新周期
        </el-button>

        <el-button size="small" type="primary" @click="openRecoveryDialog">
          抽取数据恢复
        </el-button>

        <el-button
          size="small"
          type="danger"
          @click="onBatchDelete"
          :disabled="selectedRows.length === 0"
        >
          批量删除
        </el-button>
      </template>

      <template #expand>
        <!-- 搜索区域 -->
        <div class="search" v-loading="searchLoading" element-loading-background="rgba(255, 255, 255, 0.8)">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="2"
            :label-width="70"
            :enable-reset="false"
            confirm-text="查询"
            reset-text="重置"
            button-vertical="flowing"
            @submit="onSearch"
            @reset="onReset"
          />
        </div>
      </template>

      <!-- 表格列表 -->
      <TableV2
        ref="tableRef"
        :columns="columns"
        :defaultTableData="paginatedData"
        :enable-toolbar="false"
        :enable-own-button="false"
        :enable-selection="true"
        :enable-index="false"
        :height="tableHeight"
        :loading="loading"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <!-- 序号列 -->
        <template #index="{ row }">
          {{ (pagination.page - 1) * pagination.size + row.index }}
        </template>

        <!-- 进程列 -->
        <template #progress="{ row }">
          <div class="progress-cell">
            <el-progress 
              :percentage="row.progress" 
              :status="row.progress === 100 ? 'success' : undefined"
              :stroke-width="8"
            />
            <span class="progress-text">{{ row.progress }}%</span>
          </div>
        </template>

        <!-- 状态列 -->
        <template #status="{ row }">
          <el-tag 
            :type="getStatusTagType(row.status)"
            size="small"
          >
            {{ row.status }}
          </el-tag>
        </template>

        <!-- 抽取记录列 -->
        <template #extractionRecords="{ row }">
          <div class="records-cell">
            <el-button 
              size="small" 
              type="primary" 
              @click="generateRecords(row)"
              :disabled="row.status === '待执行'"
            >
              生成
            </el-button>
            <el-button 
              size="small" 
              type="info" 
              @click="viewRecords(row)"
              :disabled="row.extractionRecords.length === 0"
            >
              查看
            </el-button>
          </div>
        </template>

        <!-- 操作列 -->
        <template #action="{ row }">
          <div class="action-buttons">
            <el-button
              v-for="action in getRowActions(row)"
              :key="action.code"
              size="small"
              :type="getActionButtonType(action.code)"
              @click="handleRowAction(row, action)"
              :disabled="isActionDisabled(row, action)"
            >
              {{ action.label }}
            </el-button>
          </div>
        </template>
      </TableV2>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </Block>

    <!-- 创建新周期对话框 -->
    <Dialog
      v-model="showCreateDialog"
      :title="dialogTitle"
      :destroy-on-close="true"
      width="700px"
      @closed="resetCreateForm"
      @click-confirm="handleCreateConfirm"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createFormRules"
        label-width="120px"
      >
        <el-form-item label="周期名称" prop="cycleName" required>
          <el-input
            v-model="createForm.cycleName"
            placeholder="请输入周期名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="周期时间" prop="cycleTime" required>
          <el-date-picker
            v-model="createForm.cycleTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="抽取范围" prop="extractionScope" required>
          <el-select
            v-model="createForm.extractionScope"
            multiple
            placeholder="请选择抽取范围"
            style="width: 100%"
            collapse-tags
            collapse-tags-tooltip
          >
            <el-option
              v-for="district in chongqingDistricts"
              :key="district"
              :label="district"
              :value="district"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="抽取字段" prop="extractionFields" required>
          <el-checkbox-group v-model="createForm.extractionFields">
            <el-checkbox
              v-for="field in extractionFieldOptions"
              :key="field.value"
              :label="field.value"
            >
              {{ field.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="输出格式" prop="outputFormat">
          <el-radio-group v-model="createForm.outputFormat">
            <el-radio label=".CSV">.CSV</el-radio>
            <el-radio label=".JSON">.JSON</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="规则设定" prop="ruleSettings">
          <el-input
            v-model="createForm.ruleSettings"
            type="textarea"
            placeholder="请输入规则设定"
            :rows="4"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 查看抽取记录对话框 -->
    <Dialog
      v-model="showRecordsDialog"
      :title="recordsDialogTitle"
      :destroy-on-close="true"
      width="1400px"
      :enable-confirm="false"
      cancel-text="关闭"
    >

      <div class="records-content">
        <!-- 操作按钮区域 -->
        <div class="records-actions">
          <el-button
            size="small"
            type="danger"
            @click="onBatchDeleteRecords"
            :disabled="selectedRecords.length === 0"
          >
            <el-icon style="margin-right: 4px">
              <Delete />
            </el-icon>
            批量删除
          </el-button>
        </div>

        <!-- 搜索区域 -->
        <div class="records-search" v-loading="recordsSearchLoading" element-loading-background="rgba(255, 255, 255, 0.8)">
          <Form
            :props="recordsSearchFormProp"
            v-model="recordsSearchForm"
            :column-count="4"
            :label-width="100"
            :enable-reset="false"
            confirm-text="查询"
            reset-text="重置"
            button-vertical="flowing"
            @submit="onRecordsSearch"
            @reset="onRecordsReset"
          />
        </div>

        <!-- 表格列表 -->
        <TableV2
          ref="recordsTableRef"
          :columns="recordsColumns"
          :defaultTableData="paginatedRecords"
          :enable-toolbar="false"
          :enable-own-button="false"
          :enable-selection="true"
          :enable-index="false"
          :height="recordsTableHeight"
          :loading="recordsSearchLoading"
          @sort-change="handleRecordsSortChange"
          @selection-change="handleRecordsSelectionChange"
        >
          <!-- 序号列 -->
          <template #序号="{ row }">
            {{ (recordsPagination.page - 1) * recordsPagination.size + row.序号 }}
          </template>

          <!-- 数据内容列 -->
          <template #数据内容="{ row }">
            <div class="data-content">
              <div v-if="Object.keys(row.数据内容).length === 0" class="no-data">
                <span class="no-data-text">无数据</span>
              </div>
              <div v-else>
                <div v-for="(value, key) in row.数据内容" :key="key" class="data-item">
                  <span class="data-key">{{ key }}:</span>
                  <span class="data-value">{{ value }}</span>
                </div>
              </div>
            </div>
          </template>

          <!-- 状态列 -->
          <template #状态="{ row }">
            <el-tag
              :type="row.状态 === '抽取成功' ? 'success' : 'danger'"
              size="small"
            >
              {{ row.状态 }}
            </el-tag>
          </template>

          <!-- 抽取错误原因列 -->
          <template #抽取错误原因="{ row }">
            <span v-if="row.抽取错误原因" class="error-reason">{{ row.抽取错误原因 }}</span>
            <span v-else class="no-error">-</span>
          </template>

          <!-- 操作列 -->
          <template #操作="{ row }">
            <el-button
              size="small"
              type="danger"
              @click="deleteRecord(row)"
            >
              删除
            </el-button>
          </template>
        </TableV2>

        <!-- 分页 -->
        <div class="records-pagination">
          <el-pagination
            v-model:current-page="recordsPagination.page"
            v-model:page-size="recordsPagination.size"
            :page-sizes="[10, 20, 50]"
            :total="recordsPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleRecordsSizeChange"
            @current-change="handleRecordsPageChange"
          />
        </div>
      </div>
    </Dialog>

    <!-- 查看周期详情对话框 -->
    <Dialog
      v-model="showViewDialog"
      title="查看周期详情"
      width="700px"
      :enable-confirm="false"
      cancel-text="关闭"
    >
      <el-form
        :model="viewForm"
        label-width="120px"
        class="view-form"
      >
        <el-form-item label="周期名称">
          <el-input v-model="viewForm.cycleName" disabled />
        </el-form-item>

        <el-form-item label="周期时间">
          <el-date-picker
            v-model="viewForm.cycleTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
            disabled
          />
        </el-form-item>

        <el-form-item label="抽取范围">
          <el-select
            v-model="viewForm.extractionScope"
            multiple
            placeholder="抽取范围"
            style="width: 100%"
            collapse-tags
            collapse-tags-tooltip
            disabled
          >
            <el-option
              v-for="district in chongqingDistricts"
              :key="district"
              :label="district"
              :value="district"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="抽取字段">
          <el-checkbox-group v-model="viewForm.extractionFields" disabled>
            <el-checkbox
              v-for="field in extractionFieldOptions"
              :key="field.value"
              :label="field.value"
            >
              {{ field.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="输出格式">
          <el-radio-group v-model="viewForm.outputFormat" disabled>
            <el-radio label=".CSV">.CSV</el-radio>
            <el-radio label=".JSON">.JSON</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="规则设定">
          <el-input
            v-model="viewForm.ruleSettings"
            type="textarea"
            placeholder="规则设定"
            :rows="4"
            disabled
          />
        </el-form-item>

        <el-form-item label="创建时间">
          <el-input v-model="viewForm.createTime" disabled />
        </el-form-item>

        <el-form-item label="当前状态">
          <el-tag
            :type="getStatusTagType(viewForm.status)"
            size="small"
          >
            {{ viewForm.status }}
          </el-tag>
        </el-form-item>

        <el-form-item label="执行进度">
          <div class="progress-cell">
            <el-progress
              :percentage="viewForm.progress"
              :status="viewForm.progress === 100 ? 'success' : undefined"
              :stroke-width="8"
            />
            <span class="progress-text">{{ viewForm.progress }}%</span>
          </div>
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 抽取数据恢复对话框 -->
    <Dialog
      v-model="showRecoveryDialog"
      title="抽取数据恢复"
      width="650px"
      :destroy-on-close="true"
      @closed="resetRecoveryForm"
      @click-confirm="handleRecoveryConfirm"
    >
      <el-form
        ref="recoveryFormRef"
        :model="recoveryForm"
        :rules="recoveryFormRules"
        label-width="140px"
      >
        <el-form-item label="自动备份时间间隔" prop="backupInterval" required>
          <el-select
            v-model="recoveryForm.backupInterval"
            placeholder="请选择备份时间间隔"
            style="width: 100%"
          >
            <el-option
              v-for="option in backupIntervalOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="恢复时间点" prop="recoveryTime" required>
          <el-date-picker
            v-model="recoveryForm.recoveryTime"
            type="datetime"
            placeholder="请选择恢复时间点"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
    </Dialog>
  </div>
</template>

<style lang="scss" scoped>
.data-extraction {
  height: 100%;

  .search {
    background: #f8f9fa;
    border-radius: 4px;
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    padding: 16px 0;
  }

  .progress-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;

    .el-progress {
      flex: 1;
      min-width: 80px;
    }

    .progress-text {
      font-size: 12px;
      color: #666;
      min-width: 35px;
      white-space: nowrap;
    }
  }

  .records-cell {
    display: flex;
    gap: 8px;
  }

  .action-buttons {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;

    .el-button {
      margin: 0;
    }
  }
}

.records-content {
  .records-actions {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #EBEEF5;
  }

  .records-search {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 16px;
  }

  .data-content {
    .data-item {
      display: flex;
      margin-bottom: 4px;

      .data-key {
        font-weight: bold;
        color: #606266;
        margin-right: 8px;
        min-width: 60px;
      }

      .data-value {
        color: #303133;
      }
    }

    .no-data {
      .no-data-text {
        color: #909399;
        font-style: italic;
      }
    }
  }

  .error-reason {
    color: #F56C6C;
  }

  .no-error {
    color: #909399;
  }

  .records-pagination {
    display: flex;
    justify-content: flex-end;
    padding: 16px 0 0 0;
    border-top: 1px solid #EBEEF5;
    margin-top: 16px;
  }
}

.view-form {
  .el-input,
  .el-select,
  .el-date-editor,
  .el-textarea {
    :deep(.el-input__inner),
    :deep(.el-textarea__inner) {
      background-color: #f5f7fa;
      color: #606266;
    }
  }

  .el-checkbox {
    :deep(.el-checkbox__input) {
      cursor: not-allowed;
    }
  }

  .el-radio {
    :deep(.el-radio__input) {
      cursor: not-allowed;
    }
  }
}

:deep(.el-table) {
  .el-table__body-wrapper {
    overflow-x: auto;
  }
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-checkbox-group) {
  .el-checkbox {
    margin-right: 24px;
    margin-bottom: 8px;
  }
}
</style>
