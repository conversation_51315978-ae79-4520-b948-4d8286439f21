<script setup lang="ts" name="resultDisplay">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Star, Warning, Download, View, Printer, Share, Plus, Check } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 路由实例
const router = useRouter()

// 响应式数据
const loading = ref(false)
const currentScore = ref(75) // 当前整合度得分

// 重庆市区县数据
const chongqingDistricts = [
  '渝中区', '江北区', '南岸区', '九龙坡区', '沙坪坝区', '大渡口区',
  '渝北区', '巴南区', '北碚区', '綦江区', '大足区', '长寿区',
  '江津区', '合川区', '永川区', '南川区', '璧山区', '铜梁区',
  '潼南区', '荣昌区'
]

// 整合度排名数据
const rankingData = ref<any[]>([])

// 趋势图表实例
const trendChartRef = ref()
const trendChart = ref<echarts.ECharts>()

// 对比图表实例
const comparisonChartRef = ref()
const comparisonChart = ref<echarts.ECharts>()

// 评估指标表单引用
const evaluationFormRef = ref()

// 历史报告相关状态
const showHistoryReportsDialog = ref(false)
const showReportDetailDialog = ref(false)
const historyReportsLoading = ref(false)
const currentReportDetail = ref<any>(null)

// 对比选择器数据
const comparisonForm = ref({
  district1: '渝中区',
  district2: '江北区'
})

// 生成整合度报告对话框
const showReportDialog = ref(false)
const reportActions = [
  { label: '查看报告', icon: View, action: 'view' },
  { label: '打印报告', icon: Printer, action: 'print' },
  { label: '分享报告', icon: Share, action: 'share' },
  { label: '导出PDF', icon: Download, action: 'export' }
]

// 查看报告预览对话框
const showReportPreviewDialog = ref(false)
const reportPreviewLoading = ref(false)

// 配置评估指标相关状态
const showEvaluationConfigDialog = ref(false)
const showEvaluationFormDialog = ref(false)
const evaluationFormLoading = ref(false)
const currentEvaluationItem = ref<any>(null)
const isEditingEvaluation = ref(false)

// 评估指标数据
interface EvaluationIndicator {
  id: string
  index: number
  tagText: string
  tagColor: string
  indicatorName: string
  indicatorCategory: string
  indicatorDescription: string
  indicatorWeight: number
  createTime: string
}

const evaluationIndicators = ref<EvaluationIndicator[]>([])

// 整合度计算方法配置
const integrationCalculationMethod = ref('层次分析法（AHP）')
const calculationMethods = [
  { label: '层次分析法（AHP）', value: '层次分析法（AHP）' },
  { label: '加权平均法', value: '加权平均法' },
  { label: '模糊综合评价法', value: '模糊综合评价法' },
  { label: '熵权法', value: '熵权法' }
]

// 评估指标表格配置
const evaluationTableRef = ref()
const evaluationTableHeight = ref(400)
const evaluationColumns = [
  { prop: 'index', label: '序号', width: 80 },
  { prop: 'tagText', label: '标签', minWidth: 120 },
  { prop: 'indicatorName', label: '指标名称', minWidth: 150 },
  { prop: 'indicatorCategory', label: '指标类别', minWidth: 120 },
  { prop: 'indicatorDescription', label: '指标说明', minWidth: 200 },
  { prop: 'indicatorWeight', label: '指标权重', minWidth: 100 },
  { prop: 'action', label: '操作', minWidth: 120, fixed: 'right' }
]

// 评估指标分页
const evaluationPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 历史报告数据结构
interface HistoryReport {
  id: string
  index: number
  reportTitle: string
  reportType: string
  generateTime: string
  reportScore: number
  reportStatus: string
  reportSummary: string
  dataRange: string
  operator: string
}

const historyReports = ref<HistoryReport[]>([])

// 历史报告表格配置
const historyReportsTableRef = ref()
const historyReportsTableHeight = ref(400)
const historyReportsColumns = [
  { prop: 'index', label: '序号', width: 80 },
  { prop: 'reportTitle', label: '报告标题', minWidth: 200 },
  { prop: 'reportType', label: '报告类型', width: 120 },
  { prop: 'reportStatus', label: '状态', width: 100 },
  { prop: 'generateTime', label: '生成时间', width: 160 },
  { prop: 'operator', label: '操作人', width: 120 },
  { prop: 'action', label: '操作', width: 100, fixed: 'right' }
]

// 历史报告分页
const historyReportsPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 评估指标表单数据
const evaluationForm = ref({
  tagText: '',
  tagColor: '#409EFF',
  indicatorName: '',
  indicatorCategory: '',
  indicatorDescription: '',
  indicatorWeight: 20
})

// 评估指标类别选项
const indicatorCategoryOptions = [
  { label: '一致性', value: '一致性' },
  { label: '准确性', value: '准确性' },
  { label: '完整性', value: '完整性' },
  { label: '有效性', value: '有效性' }
]

// 评估指标表单验证规则
const evaluationFormRules = {
  tagText: [
    { required: true, message: '请输入标签文本', trigger: 'blur' }
  ],
  indicatorName: [
    { required: true, message: '请输入指标名称', trigger: 'blur' }
  ],
  indicatorCategory: [
    { required: true, message: '请选择指标类别', trigger: 'change' }
  ],
  indicatorWeight: [
    { required: true, message: '请输入指标权重', trigger: 'blur' },
    {
      validator: (rule: any, value: number, callback: any) => {
        if (value < 0 || value > 100) {
          callback(new Error('权重范围为0-100'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 返回上一页
const handleGoBack = () => {
  router.push('/reportIntegrationSpotCheck')
}

// 导航到问题数据页面
const navigateToIssueData = () => {
  router.push('/reportIntegrationSpotCheck/issueData')
}

// 获取得分颜色
const getScoreColor = (score: number) => {
  if (score >= 80) return '#67C23A' // 绿色
  if (score >= 60) return '#E6A23C' // 黄色
  return '#F56C6C' // 红色
}

// 获取得分等级
const getScoreLevel = (score: number) => {
  if (score >= 80) return '优秀'
  if (score >= 60) return '良好'
  return '待改进'
}

// 生成排名数据
const generateRankingData = () => {
  const data = chongqingDistricts.map((district, index) => {
    const score = Math.floor(Math.random() * 45) + 50 // 50-95分
    return {
      id: `district_${index}`,
      rank: index + 1,
      district,
      score,
      isFirst: false,
      isLast: false
    }
  })
  
  // 按分数降序排列
  data.sort((a, b) => b.score - a.score)
  
  // 重新设置排名
  data.forEach((item, index) => {
    item.rank = index + 1
    item.isFirst = index === 0
    // item.isLast = index >= data.length - 2 // 最后两名
    item.isLast = item.score < 60
  })
  
  return data
}

// 生成趋势数据
const generateTrendData = () => {
  const months = []
  const scores = []
  const now = new Date()

  for (let i = 11; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
    months.push(`${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`)
    scores.push(Math.floor(Math.random() * 45) + 50) // 50-95分
  }

  return { months, scores }
}

// 获取分数等级文字
const getScoreLevelText = (score: number): string => {
  if (score < 40) return '严重偏低'
  if (score < 50) return '明显偏低'
  if (score < 60) return '整合度过低'
  return '正常'
}

// 生成预警标识数据
const generateWarningMarks = (scores: number[]): any[] => {
  const warningMarks: any[] = []
  scores.forEach((score, index) => {
    if (score < 60) {
      warningMarks.push({
        coord: [index, score],
        symbol: 'none',
        label: {
          show: true,
          position: 'top',
          distance: 15,
          formatter: () => {
            return `{scoreValue|${score}分}\n{levelText|${getScoreLevelText(score)}}\n{warning|⚠️}`
          },
          rich: {
            scoreValue: {
              fontSize: 12,
              color: '#F56C6C',
              fontWeight: 'bold',
              align: 'center',
              lineHeight: 16
            },
            levelText: {
              fontSize: 10,
              color: '#F56C6C',
              align: 'center',
              lineHeight: 14
            },
            warning: {
              fontSize: 14,
              color: '#F56C6C',
              align: 'center',
              lineHeight: 18
            }
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#F56C6C',
          borderWidth: 1,
          borderRadius: 4,
          padding: [4, 6],
          shadowColor: 'rgba(245, 108, 108, 0.3)',
          shadowBlur: 4,
          shadowOffsetY: 2
        }
      })
    }
  })
  return warningMarks
}

// 生成低分标记数据（小于60分的点）
const generateLowScoreMarks = (scores: number[], months: string[]): any[] => {
  if (scores.length === 0) return []

  // 找到所有小于60分的数据点
  const lowScoreData = scores
    .map((score, index) => ({ score, index }))
    .filter(item => item.score < 60)

  // 为每个低分数点创建标记
  const lowScoreMarks = lowScoreData.map(({ score, index }) => ({
    coord: [index, score],
    symbol: 'triangle', // 使用三角形标记
    symbolSize: 12,
    itemStyle: {
      color: '#FF4757', // 使用更鲜艳的红色
      borderColor: '#FFFFFF',
      borderWidth: 2,
      shadowColor: 'rgba(255, 71, 87, 0.5)',
      shadowBlur: 8,
      shadowOffsetY: 2
    },
    label: {
      show: true,
      position: 'bottom',
      distance: 15,
      formatter: () => {
        return `{lowIcon|📍}\n{lowLabel|低分预警}\n{lowValue|${score}分}\n{lowTime|${months[index]}}`
      },
      rich: {
        lowIcon: {
          fontSize: 16,
          align: 'center',
          lineHeight: 20
        },
        lowLabel: {
          fontSize: 11,
          color: '#FF4757',
          fontWeight: 'bold',
          align: 'center',
          lineHeight: 16
        },
        lowValue: {
          fontSize: 13,
          color: '#FF4757',
          fontWeight: 'bold',
          align: 'center',
          lineHeight: 18
        },
        lowTime: {
          fontSize: 10,
          color: '#666666',
          align: 'center',
          lineHeight: 14
        }
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#FF4757',
      borderWidth: 1,
      borderRadius: 6,
      padding: [6, 8],
      shadowColor: 'rgba(255, 71, 87, 0.3)',
      shadowBlur: 6,
      shadowOffsetY: 3
    }
  }))

  return lowScoreMarks
}

// 生成对比数据
const generateComparisonData = (_district1: string, _district2: string) => {
  const { months } = generateTrendData()
  const scores1 = months.map(() => Math.floor(Math.random() * 45) + 50)
  const scores2 = months.map(() => Math.floor(Math.random() * 45) + 50)

  return {
    months,
    district1Data: scores1,
    district2Data: scores2
  }
}

// 初始化趋势图表
const initTrendChart = () => {
  if (!trendChartRef.value) return

  trendChart.value = echarts.init(trendChartRef.value)
  const { months, scores } = generateTrendData()

  // 合并预警标记和低分标记
  const warningMarks = generateWarningMarks(scores)
  const lowScoreMarks = generateLowScoreMarks(scores, months)
  const allMarks = [...warningMarks, ...lowScoreMarks]

  const option = {
    title: {
      text: '整合度趋势曲线图',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const data = params[0]
        const score = data.value
        const isLowScore = score < 60
        const warning = isLowScore ? '<br/><span style="color: #F56C6C; font-weight: bold;">⚠️ 低分预警</span>' : ''
        const lowScoreInfo = isLowScore ? '<br/><span style="color: #FF4757; font-weight: bold;">📍 低分标记点</span>' : ''
        return `${data.axisValue}<br/>整合度得分: ${score}分${warning}${lowScoreInfo}`
      }
    },
    xAxis: {
      type: 'category',
      data: months,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}分'
      }
    },
    series: [{
      name: '整合度得分',
      type: 'line',
      data: scores,
      smooth: true,
      lineStyle: {
        width: 3,
        color: '#409EFF'
      },
      itemStyle: {
        color: '#409EFF'
      },
      markPoint: {
        data: allMarks
      }
    }]
  }

  trendChart.value.setOption(option)
}

// 初始化对比图表
const initComparisonChart = () => {
  if (!comparisonChartRef.value) return
  
  comparisonChart.value = echarts.init(comparisonChartRef.value)
  updateComparisonChart()
}

// 更新对比图表
const updateComparisonChart = () => {
  if (!comparisonChart.value) return
  
  const { months, district1Data, district2Data } = generateComparisonData(
    comparisonForm.value.district1,
    comparisonForm.value.district2
  )
  
  const option = {
    title: {
      text: '整合度对比折线图',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: [comparisonForm.value.district1, comparisonForm.value.district2],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: months,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLabel: {
        formatter: '{value}分'
      }
    },
    series: [
      {
        name: comparisonForm.value.district1,
        type: 'line',
        data: district1Data,
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#409EFF'
        }
      },
      {
        name: comparisonForm.value.district2,
        type: 'line',
        data: district2Data,
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#67C23A'
        }
      }
    ]
  }
  
  comparisonChart.value.setOption(option)
}

// 对比地区变化处理
const handleComparisonChange = () => {
  updateComparisonChart()
}

// 生成整合度报告
const generateReport = () => {
  showReportDialog.value = true
}

// 处理报告操作
const handleReportAction = async (action: string) => {
  switch (action) {
    case 'view':
      showReportDialog.value = false
      await openReportPreview()
      break
    case 'print':
      showReportDialog.value = false
      await printReport()
      break
    case 'share':
      showReportDialog.value = false
      await shareReport()
      break
    case 'export':
      showReportDialog.value = false
      await exportToPDF()
      break
  }
}

// 打开报告预览
const openReportPreview = async () => {
  try {
    reportPreviewLoading.value = true
    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    showReportPreviewDialog.value = true
  } catch (error) {
    console.error('打开报告预览失败:', error)
    ElMessage.error('打开报告预览失败，请重试')
  } finally {
    reportPreviewLoading.value = false
  }
}

// 导出PDF功能
const exportToPDF = async () => {
  try {
    loading.value = true

    // 显示提示信息
    ElMessageBox.alert(
      '当前系统使用浏览器打印功能来生成PDF报告。如需完整的PDF导出功能，请联系系统管理员安装相关组件。',
      'PDF导出说明',
      {
        confirmButtonText: '继续打印',
        type: 'info'
      }
    ).then(async () => {
      await printReport()
    }).catch(() => {
      // 用户取消
    })

  } catch (error) {
    console.error('导出PDF失败:', error)
    ElMessage.error('导出PDF失败，请重试')
  } finally {
    loading.value = false
  }
}

// 浏览器打印功能（备选方案）
const printReport = async () => {
  try {
    // 确保报告预览已打开
    if (!showReportPreviewDialog.value) {
      await openReportPreview()
      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    const reportElement = document.getElementById('report-content')
    if (!reportElement) {
      throw new Error('找不到报告内容元素')
    }

    // 创建新窗口用于打印
    const printWindow = window.open('', '_blank')
    if (!printWindow) {
      throw new Error('无法打开打印窗口，请检查浏览器弹窗设置')
    }

    // 创建打印文档内容
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>重庆市报表整合度分析报告</title>
        <style>
          body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            color: #333;
          }
          .report-header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #409EFF; }
          .report-title { font-size: 24px; font-weight: bold; margin: 0 0 15px 0; }
          .report-meta { font-size: 14px; color: #666; }
          .report-meta p { margin: 5px 0; }
          .section-title { font-size: 18px; font-weight: bold; margin: 20px 0 15px 0; padding-left: 10px; border-left: 4px solid #409EFF; }
          .report-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
          .report-table th, .report-table td { padding: 8px; text-align: center; border: 1px solid #ddd; }
          .report-table th { background-color: #f5f5f5; font-weight: bold; }
          .score-value { font-size: 36px; font-weight: bold; display: block; margin-bottom: 10px; }
          .current-score-display { text-align: center; background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0; }
          ul { padding-left: 20px; }
          li { margin-bottom: 8px; }
          .report-footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; font-size: 12px; color: #999; }
          @media print {
            body { margin: 0; }
            .report-header { page-break-after: avoid; }
            .section-title { page-break-after: avoid; }
            .report-table { page-break-inside: avoid; }
          }
        </style>
      </head>
      <body>
        ${reportElement.innerHTML}
      </body>
      </html>
    `

    // 写入文档内容（虽然document.write已弃用，但这是浏览器打印功能的标准做法）
    printWindow.document.open()
    printWindow.document.write(printContent)
    printWindow.document.close()

    // 等待内容加载完成后打印
    printWindow.onload = () => {
      printWindow.print()
      printWindow.close()
    }

    ElMessage.success('已打开打印预览窗口')
  } catch (error) {
    console.error('打印失败:', error)
    ElMessage.error('打印失败，请重试')
  }
}

// 分享报告功能
const shareReport = async () => {
  try {
    loading.value = true
    ElMessage.info('正在生成分享链接...')

    // 模拟生成分享链接的过程
    await new Promise(resolve => setTimeout(resolve, 1500))

    const shareUrl = `${window.location.origin}/reportIntegrationSpotCheck/resultDisplay?share=${Date.now()}`

    // 尝试使用Web Share API（如果支持）
    if (navigator.share) {
      await navigator.share({
        title: '重庆市报表整合度分析报告',
        text: `当前整合度得分：${currentScore.value}分，查看详细分析报告`,
        url: shareUrl
      })
      ElMessage.success('分享成功')
    } else {
      // 备选方案：复制到剪贴板
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(shareUrl)
        ElMessage.success('分享链接已复制到剪贴板')
      } else {
        // 最后的备选方案：显示链接
        ElMessageBox.alert(shareUrl, '分享链接', {
          confirmButtonText: '确定',
          type: 'info'
        })
      }
    }
  } catch (error: any) {
    console.error('分享失败:', error)
    if (error?.name === 'AbortError') {
      // 用户取消分享
      return
    }
    ElMessage.error('分享失败，请重试')
  } finally {
    loading.value = false
  }
}

// 趋势分析报告PDF生成
const exportTrendAnalysisToPDF = async () => {
  try {
    loading.value = true

    // 显示提示信息
    ElMessageBox.alert(
      '当前系统使用浏览器打印功能来生成PDF报告。如需完整的PDF导出功能，请联系系统管理员安装相关组件。',
      'PDF导出说明',
      {
        confirmButtonText: '继续打印',
        type: 'info'
      }
    ).then(async () => {
      await printTrendAnalysisReport()
    }).catch(() => {
      // 用户取消
    })

  } catch (error) {
    console.error('导出趋势分析PDF失败:', error)
    ElMessage.error('导出PDF失败，请重试')
  } finally {
    loading.value = false
  }
}

// 对比分析报告PDF生成
const exportComparisonAnalysisToPDF = async () => {
  try {
    loading.value = true

    // 显示提示信息
    ElMessageBox.alert(
      '当前系统使用浏览器打印功能来生成PDF报告。如需完整的PDF导出功能，请联系系统管理员安装相关组件。',
      'PDF导出说明',
      {
        confirmButtonText: '继续打印',
        type: 'info'
      }
    ).then(async () => {
      await printComparisonAnalysisReport()
    }).catch(() => {
      // 用户取消
    })

  } catch (error) {
    console.error('导出对比分析PDF失败:', error)
    ElMessage.error('导出PDF失败，请重试')
  } finally {
    loading.value = false
  }
}

// 打印趋势分析报告
const printTrendAnalysisReport = async () => {
  try {
    ElMessage.info('正在生成趋势分析报告...')

    // 获取趋势数据
    const { months, scores } = generateTrendData()
    const averageScore = Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length)
    const maxScore = Math.max(...scores)
    const minScore = Math.min(...scores)
    const warningCount = scores.filter(score => score < 60).length

    // 创建新窗口用于打印
    const printWindow = window.open('', '_blank')
    if (!printWindow) {
      throw new Error('无法打开打印窗口，请检查浏览器弹窗设置')
    }

    // 创建打印文档内容
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>重庆市报表整合度趋势分析报告</title>
        <style>
          body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            color: #333;
          }
          .report-header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #409EFF; }
          .report-title { font-size: 24px; font-weight: bold; margin: 0 0 15px 0; }
          .report-meta { font-size: 14px; color: #666; }
          .report-meta p { margin: 5px 0; }
          .section-title { font-size: 18px; font-weight: bold; margin: 20px 0 15px 0; padding-left: 10px; border-left: 4px solid #409EFF; }
          .report-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
          .report-table th, .report-table td { padding: 8px; text-align: center; border: 1px solid #ddd; }
          .report-table th { background-color: #f5f5f5; font-weight: bold; }
          .score-value { font-size: 36px; font-weight: bold; display: block; margin-bottom: 10px; color: ${getScoreColor(currentScore.value)}; }
          .current-score-display { text-align: center; background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0; }
          .trend-data-table { width: 100%; border-collapse: collapse; margin: 15px 0; font-size: 12px; }
          .trend-data-table th, .trend-data-table td { padding: 6px; text-align: center; border: 1px solid #ddd; }
          .trend-data-table th { background-color: #f5f5f5; }
          ul { padding-left: 20px; }
          li { margin-bottom: 8px; }
          .report-footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; font-size: 12px; color: #999; }
          .warning-text { color: #F56C6C; font-weight: bold; }
          .excellent-text { color: #67C23A; font-weight: bold; }
          .good-text { color: #E6A23C; font-weight: bold; }
          @media print {
            body { margin: 0; }
            .report-header { page-break-after: avoid; }
            .section-title { page-break-after: avoid; }
            .report-table { page-break-inside: avoid; }
          }
        </style>
      </head>
      <body>
        <div class="report-header">
          <h1 class="report-title">重庆市报表整合度趋势分析报告</h1>
          <div class="report-meta">
            <p><strong>生成时间：</strong>${new Date().toLocaleString('zh-CN')}</p>
            <p><strong>报告类型：</strong>趋势分析</p>
            <p><strong>分析范围：</strong>最近12个月</p>
          </div>
        </div>

        <div class="section-title">一、整合度得分概览</div>
        <div class="current-score-display">
          <span class="score-value">${currentScore.value}分</span>
          <span>当前重庆市整体报表整合度得分为${currentScore.value}分，评级为<span class="${currentScore.value >= 80 ? 'excellent-text' : currentScore.value >= 60 ? 'good-text' : 'warning-text'}">${getScoreLevel(currentScore.value)}</span></span>
        </div>

        <div class="section-title">二、趋势数据统计</div>
        <table class="report-table">
          <tr>
            <th>统计项目</th>
            <th>数值</th>
            <th>说明</th>
          </tr>
          <tr>
            <td>当前得分</td>
            <td class="${currentScore.value >= 80 ? 'excellent-text' : currentScore.value >= 60 ? 'good-text' : 'warning-text'}">${currentScore.value}分</td>
            <td>${getScoreLevel(currentScore.value)}</td>
          </tr>
          <tr>
            <td>平均得分</td>
            <td>${averageScore}分</td>
            <td>最近12个月平均水平</td>
          </tr>
          <tr>
            <td>最高得分</td>
            <td class="excellent-text">${maxScore}分</td>
            <td>历史最佳表现</td>
          </tr>
          <tr>
            <td>最低得分</td>
            <td class="${minScore < 60 ? 'warning-text' : 'good-text'}">${minScore}分</td>
            <td>需要关注的低点</td>
          </tr>
          <tr>
            <td>预警次数</td>
            <td class="${warningCount > 0 ? 'warning-text' : 'excellent-text'}">${warningCount}次</td>
            <td>得分低于60分的月份数</td>
          </tr>
        </table>

        <div class="section-title">三、月度趋势数据</div>
        <table class="trend-data-table">
          <thead>
            <tr>
              <th>月份</th>
              ${months.map(month => `<th>${month}</th>`).join('')}
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><strong>得分</strong></td>
              ${scores.map(score => `<td class="${score >= 80 ? 'excellent-text' : score >= 60 ? 'good-text' : 'warning-text'}">${score}分</td>`).join('')}
            </tr>
          </tbody>
        </table>

        <div class="section-title">四、趋势分析结论</div>
        <ul>
          <li>整体趋势：重庆市报表整合度在最近12个月内保持相对稳定，平均得分为${averageScore}分</li>
          <li>波动情况：最高得分${maxScore}分，最低得分${minScore}分，波动范围${maxScore - minScore}分</li>
          <li>预警情况：共出现${warningCount}次低分预警（得分低于60分），需要重点关注</li>
          <li>发展态势：${averageScore >= 80 ? '整体表现优秀，建议继续保持' : averageScore >= 60 ? '整体表现良好，有进一步提升空间' : '整体表现待改进，需要加强技术支持'}</li>
        </ul>

        <div class="section-title">五、改进建议</div>
        <ul>
          <li>继续保持当前整合度水平，巩固已有成果</li>
          <li>重点关注低分预警月份的改进措施，分析原因并制定针对性方案</li>
          <li>建立定期监测和评估机制，及时发现问题并处理</li>
          <li>加强技术支持和培训，提升整体技术水平</li>
          <li>促进经验交流，推广优秀实践案例</li>
        </ul>

        <div class="report-footer">
          <p>本报告由重庆市报表整合度抽查系统自动生成，数据截止时间：${new Date().toLocaleDateString('zh-CN')}</p>
        </div>
      </body>
      </html>
    `

    // 写入文档内容
    printWindow.document.open()
    printWindow.document.write(printContent)
    printWindow.document.close()

    // 等待内容加载完成后打印
    printWindow.onload = () => {
      printWindow.print()
      printWindow.close()
    }

    ElMessage.success('已打开趋势分析报告打印预览窗口')
  } catch (error) {
    console.error('打印趋势分析报告失败:', error)
    ElMessage.error('打印失败，请重试')
  }
}

// 打印对比分析报告
const printComparisonAnalysisReport = async () => {
  try {
    ElMessage.info('正在生成对比分析报告...')

    // 获取对比数据
    const { months, district1Data, district2Data } = generateComparisonData(
      comparisonForm.value.district1,
      comparisonForm.value.district2
    )

    const district1Average = Math.round(district1Data.reduce((sum, score) => sum + score, 0) / district1Data.length)
    const district2Average = Math.round(district2Data.reduce((sum, score) => sum + score, 0) / district2Data.length)
    const district1Max = Math.max(...district1Data)
    const district1Min = Math.min(...district1Data)
    const district2Max = Math.max(...district2Data)
    const district2Min = Math.min(...district2Data)
    const betterPerformer = district1Average > district2Average ? comparisonForm.value.district1 : comparisonForm.value.district2
    const performanceDifference = Math.abs(district1Average - district2Average)

    // 创建新窗口用于打印
    const printWindow = window.open('', '_blank')
    if (!printWindow) {
      throw new Error('无法打开打印窗口，请检查浏览器弹窗设置')
    }

    // 创建打印文档内容
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>重庆市报表整合度对比分析报告</title>
        <style>
          body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            color: #333;
          }
          .report-header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #409EFF; }
          .report-title { font-size: 24px; font-weight: bold; margin: 0 0 15px 0; }
          .report-meta { font-size: 14px; color: #666; }
          .report-meta p { margin: 5px 0; }
          .section-title { font-size: 18px; font-weight: bold; margin: 20px 0 15px 0; padding-left: 10px; border-left: 4px solid #409EFF; }
          .report-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
          .report-table th, .report-table td { padding: 8px; text-align: center; border: 1px solid #ddd; }
          .report-table th { background-color: #f5f5f5; font-weight: bold; }
          .comparison-data-table { width: 100%; border-collapse: collapse; margin: 15px 0; font-size: 12px; }
          .comparison-data-table th, .comparison-data-table td { padding: 6px; text-align: center; border: 1px solid #ddd; }
          .comparison-data-table th { background-color: #f5f5f5; }
          .district1-header { background-color: #E3F2FD !important; color: #1976D2; }
          .district2-header { background-color: #E8F5E8 !important; color: #388E3C; }
          ul { padding-left: 20px; }
          li { margin-bottom: 8px; }
          .report-footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; font-size: 12px; color: #999; }
          .warning-text { color: #F56C6C; font-weight: bold; }
          .excellent-text { color: #67C23A; font-weight: bold; }
          .good-text { color: #E6A23C; font-weight: bold; }
          .highlight-box { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #409EFF; }
          @media print {
            body { margin: 0; }
            .report-header { page-break-after: avoid; }
            .section-title { page-break-after: avoid; }
            .report-table { page-break-inside: avoid; }
          }
        </style>
      </head>
      <body>
        <div class="report-header">
          <h1 class="report-title">重庆市报表整合度对比分析报告</h1>
          <div class="report-meta">
            <p><strong>生成时间：</strong>${new Date().toLocaleString('zh-CN')}</p>
            <p><strong>报告类型：</strong>对比分析</p>
            <p><strong>对比地区：</strong>${comparisonForm.value.district1} VS ${comparisonForm.value.district2}</p>
            <p><strong>分析范围：</strong>最近12个月</p>
          </div>
        </div>

        <div class="section-title">一、对比概览</div>
        <div class="highlight-box">
          <p><strong>对比结果：</strong>${betterPerformer}在整合度方面表现更优，平均得分差距为${performanceDifference}分</p>
          <p><strong>整体评价：</strong>两地区在报表整合度方面形成良性竞争关系，均有进一步提升空间</p>
        </div>

        <div class="section-title">二、统计数据对比</div>
        <table class="report-table">
          <thead>
            <tr>
              <th>统计项目</th>
              <th class="district1-header">${comparisonForm.value.district1}</th>
              <th class="district2-header">${comparisonForm.value.district2}</th>
              <th>差值</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><strong>平均得分</strong></td>
              <td class="${district1Average >= 80 ? 'excellent-text' : district1Average >= 60 ? 'good-text' : 'warning-text'}">${district1Average}分</td>
              <td class="${district2Average >= 80 ? 'excellent-text' : district2Average >= 60 ? 'good-text' : 'warning-text'}">${district2Average}分</td>
              <td>${Math.abs(district1Average - district2Average)}分</td>
            </tr>
            <tr>
              <td><strong>最高得分</strong></td>
              <td class="excellent-text">${district1Max}分</td>
              <td class="excellent-text">${district2Max}分</td>
              <td>${Math.abs(district1Max - district2Max)}分</td>
            </tr>
            <tr>
              <td><strong>最低得分</strong></td>
              <td class="${district1Min < 60 ? 'warning-text' : 'good-text'}">${district1Min}分</td>
              <td class="${district2Min < 60 ? 'warning-text' : 'good-text'}">${district2Min}分</td>
              <td>${Math.abs(district1Min - district2Min)}分</td>
            </tr>
            <tr>
              <td><strong>得分稳定性</strong></td>
              <td>${district1Max - district1Min}分波动</td>
              <td>${district2Max - district2Min}分波动</td>
              <td>${Math.abs((district1Max - district1Min) - (district2Max - district2Min))}分差异</td>
            </tr>
          </tbody>
        </table>

        <div class="section-title">三、月度对比数据</div>
        <table class="comparison-data-table">
          <thead>
            <tr>
              <th>月份</th>
              ${months.map(month => `<th>${month}</th>`).join('')}
            </tr>
          </thead>
          <tbody>
            <tr class="district1-header">
              <td><strong>${comparisonForm.value.district1}</strong></td>
              ${district1Data.map(score => `<td class="${score >= 80 ? 'excellent-text' : score >= 60 ? 'good-text' : 'warning-text'}">${score}分</td>`).join('')}
            </tr>
            <tr class="district2-header">
              <td><strong>${comparisonForm.value.district2}</strong></td>
              ${district2Data.map(score => `<td class="${score >= 80 ? 'excellent-text' : score >= 60 ? 'good-text' : 'warning-text'}">${score}分</td>`).join('')}
            </tr>
            <tr>
              <td><strong>差值</strong></td>
              ${district1Data.map((score, index) => `<td>${Math.abs(score - district2Data[index])}分</td>`).join('')}
            </tr>
          </tbody>
        </table>

        <div class="section-title">四、对比分析结论</div>
        <ul>
          <li><strong>整体表现：</strong>${betterPerformer}整体表现更优，平均得分${betterPerformer === comparisonForm.value.district1 ? district1Average : district2Average}分，领先${performanceDifference}分</li>
          <li><strong>稳定性对比：</strong>${comparisonForm.value.district1}得分波动${district1Max - district1Min}分，${comparisonForm.value.district2}得分波动${district2Max - district2Min}分</li>
          <li><strong>发展潜力：</strong>两地区均有提升空间，建议加强交流合作</li>
          <li><strong>竞争态势：</strong>形成良性竞争关系，有利于共同提升整合度水平</li>
        </ul>

        <div class="section-title">五、改进建议</div>
        <ul>
          <li>加强两地区间的经验交流与合作，互相学习优秀实践</li>
          <li>推广表现优秀地区的成功经验，形成示范效应</li>
          <li>建立定期对比评估机制，持续跟踪改进效果</li>
          <li>促进共同提升整合度水平，实现区域协调发展</li>
          <li>针对薄弱环节制定专项改进计划，缩小差距</li>
        </ul>

        <div class="report-footer">
          <p>本报告由重庆市报表整合度抽查系统自动生成，数据截止时间：${new Date().toLocaleDateString('zh-CN')}</p>
        </div>
      </body>
      </html>
    `

    // 写入文档内容
    printWindow.document.open()
    printWindow.document.write(printContent)
    printWindow.document.close()

    // 等待内容加载完成后打印
    printWindow.onload = () => {
      printWindow.print()
      printWindow.close()
    }

    ElMessage.success('已打开对比分析报告打印预览窗口')
  } catch (error) {
    console.error('打印对比分析报告失败:', error)
    ElMessage.error('打印失败，请重试')
  }
}

// 窗口大小变化时的图表自适应
const handleChartResize = () => {
  if (trendChart.value) {
    trendChart.value.resize()
  }
  if (comparisonChart.value) {
    comparisonChart.value.resize()
  }
}

// 防抖处理的窗口大小变化
let resizeTimer: NodeJS.Timeout | null = null
const debouncedHandleResize = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  resizeTimer = setTimeout(() => {
    handleChartResize()
  }, 300)
}

// 配置评估指标相关方法
const openEvaluationConfig = () => {
  loadEvaluationIndicators()
  loadCalculationMethod()
  showEvaluationConfigDialog.value = true
}

const loadEvaluationIndicators = () => {
  try {
    const savedData = localStorage.getItem('evaluationIndicators')
    if (savedData) {
      evaluationIndicators.value = JSON.parse(savedData)
    } else {
      // 初始化默认数据
      evaluationIndicators.value = [
        {
          id: 'eval_001',
          index: 1,
          tagText: '字段匹配',
          tagColor: '#409EFF',
          indicatorName: '字段匹配度',
          indicatorCategory: '一致性',
          indicatorDescription: '检查两个或多个报表中相同字段的匹配一致性',
          indicatorWeight: 20,
          createTime: new Date().toLocaleString('zh-CN')
        },
        {
          id: 'eval_002',
          index: 2,
          tagText: '数据准确',
          tagColor: '#67C23A',
          indicatorName: '数据准确性',
          indicatorCategory: '准确性',
          indicatorDescription: '验证报表数据的准确性和可靠性',
          indicatorWeight: 25,
          createTime: new Date().toLocaleString('zh-CN')
        },
        {
          id: 'eval_003',
          index: 3,
          tagText: '完整性',
          tagColor: '#E6A23C',
          indicatorName: '数据完整性',
          indicatorCategory: '完整性',
          indicatorDescription: '检查报表数据的完整性，确保无缺失',
          indicatorWeight: 20,
          createTime: new Date().toLocaleString('zh-CN')
        },
        {
          id: 'eval_004',
          index: 4,
          tagText: '有效性',
          tagColor: '#F56C6C',
          indicatorName: '数据有效性',
          indicatorCategory: '有效性',
          indicatorDescription: '验证报表数据的有效性和合规性',
          indicatorWeight: 15,
          createTime: new Date().toLocaleString('zh-CN')
        },
        {
          id: 'eval_005',
          index: 5,
          tagText: '时效性',
          tagColor: '#909399',
          indicatorName: '数据时效性',
          indicatorCategory: '一致性',
          indicatorDescription: '检查报表数据的时效性和更新频率',
          indicatorWeight: 20,
          createTime: new Date().toLocaleString('zh-CN')
        }
      ]
      saveEvaluationIndicators()
    }
  } catch (error) {
    console.error('加载评估指标数据失败:', error)
    ElMessage.error('加载评估指标数据失败')
    evaluationIndicators.value = []
  }
}

const saveEvaluationIndicators = () => {
  try {
    localStorage.setItem('evaluationIndicators', JSON.stringify(evaluationIndicators.value))
  } catch (error) {
    console.error('保存评估指标数据失败:', error)
    ElMessage.error('保存评估指标数据失败')
  }
}

const openAddEvaluationForm = () => {
  isEditingEvaluation.value = false
  currentEvaluationItem.value = null
  evaluationForm.value = {
    tagText: '',
    tagColor: '#409EFF',
    indicatorName: '',
    indicatorCategory: '',
    indicatorDescription: '',
    indicatorWeight: 20
  }
  showEvaluationFormDialog.value = true
}

const openEditEvaluationForm = (item: EvaluationIndicator) => {
  isEditingEvaluation.value = true
  currentEvaluationItem.value = item
  evaluationForm.value = {
    tagText: item.tagText,
    tagColor: item.tagColor,
    indicatorName: item.indicatorName,
    indicatorCategory: item.indicatorCategory,
    indicatorDescription: item.indicatorDescription,
    indicatorWeight: item.indicatorWeight
  }
  showEvaluationFormDialog.value = true
}

const handleEvaluationFormSubmit = async () => {
  try {
    // 表单验证
    if (!evaluationFormRef.value) return

    const valid = await evaluationFormRef.value.validate().catch(() => false)
    if (!valid) {
      ElMessage.warning('请完善表单信息')
      return
    }

    evaluationFormLoading.value = true

    // 模拟提交延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    if (isEditingEvaluation.value && currentEvaluationItem.value) {
      // 编辑模式
      const index = evaluationIndicators.value.findIndex(item => item.id === currentEvaluationItem.value.id)
      if (index !== -1) {
        evaluationIndicators.value[index] = {
          ...currentEvaluationItem.value,
          ...evaluationForm.value,
          createTime: currentEvaluationItem.value.createTime // 保持原创建时间
        }
      }
      ElMessage.success('评估指标更新成功')
    } else {
      // 新增模式
      const newItem: EvaluationIndicator = {
        id: `eval_${Date.now()}`,
        index: evaluationIndicators.value.length + 1,
        ...evaluationForm.value,
        createTime: new Date().toLocaleString('zh-CN')
      }
      evaluationIndicators.value.push(newItem)
      ElMessage.success('评估指标添加成功')
    }

    saveEvaluationIndicators()
    showEvaluationFormDialog.value = false
  } catch (error) {
    console.error('保存评估指标失败:', error)
    ElMessage.error('保存评估指标失败，请重试')
  } finally {
    evaluationFormLoading.value = false
  }
}

const handleDeleteEvaluation = async (item: EvaluationIndicator) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除评估指标"${item.indicatorName}"吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const index = evaluationIndicators.value.findIndex(i => i.id === item.id)
    if (index !== -1) {
      evaluationIndicators.value.splice(index, 1)
      saveEvaluationIndicators()
      ElMessage.success('评估指标删除成功')
    }
  } catch (error) {
    // 用户取消删除
  }
}

// 整合度计算方法相关方法
const loadCalculationMethod = () => {
  try {
    const savedMethod = localStorage.getItem('integrationCalculationMethod')
    if (savedMethod) {
      integrationCalculationMethod.value = savedMethod
    }
  } catch (error) {
    console.error('加载计算方法失败:', error)
  }
}

const onCalculationMethodChange = (value: string) => {
  console.log('计算方法变更:', value)
}

const saveCalculationMethod = () => {
  try {
    localStorage.setItem('integrationCalculationMethod', integrationCalculationMethod.value)
    ElMessage.success(`计算方法已保存：${integrationCalculationMethod.value}`)
  } catch (error) {
    console.error('保存计算方法失败:', error)
    ElMessage.error('保存计算方法失败')
  }
}

// 表格排序处理
const handleEvaluationTableSort = ({ column, prop, order }: any) => {
  if (!order) return

  evaluationIndicators.value.sort((a, b) => {
    let aValue = a[prop as keyof EvaluationIndicator]
    let bValue = b[prop as keyof EvaluationIndicator]

    // 处理不同数据类型的排序
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase()
      bValue = bValue.toLowerCase()
    }

    if (order === 'ascending') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })
}

// 获取当前页评估指标数据
const getCurrentEvaluationPageData = () => {
  const filtered = evaluationIndicators.value
  evaluationPagination.total = filtered.length

  const start = (evaluationPagination.page - 1) * evaluationPagination.size
  const end = start + evaluationPagination.size

  return filtered.slice(start, end).map((item: EvaluationIndicator, index: number) => ({
    ...item,
    index: start + index + 1
  }))
}

// 评估指标分页处理
const onEvaluationPaginationChange = (value: number, type: 'page' | 'size') => {
  if (type === 'page') {
    evaluationPagination.page = value
  } else {
    evaluationPagination.size = value
    evaluationPagination.page = 1
  }
}

// 获取操作按钮
const getEvaluationButtons = (row: EvaluationIndicator) => {
  return [
    {
      code: 'edit',
      label: '编辑',
      type: 'primary' as const
    },
    {
      code: 'delete',
      label: '删除',
      type: 'danger' as const
    }
  ]
}

// 处理表格按钮点击
const onEvaluationTableClickButton = ({ row, btn }: { row: EvaluationIndicator, btn: any }) => {
  switch (btn.code) {
    case 'edit':
      openEditEvaluationForm(row)
      break
    case 'delete':
      handleDeleteEvaluation(row)
      break
  }
}



// 初始化数据
const initData = async () => {
  try {
    loading.value = true

    // 模拟数据加载延迟
    await new Promise(resolve => setTimeout(resolve, 300))

    // 从localStorage获取或生成数据
    const savedScore = localStorage.getItem('reportIntegrationCurrentScore')
    if (savedScore) {
      currentScore.value = parseInt(savedScore)
    } else {
      currentScore.value = Math.floor(Math.random() * 45) + 50
      localStorage.setItem('reportIntegrationCurrentScore', currentScore.value.toString())
    }

    const savedRanking = localStorage.getItem('reportIntegrationRankingData')
    if (savedRanking) {
      try {
        rankingData.value = JSON.parse(savedRanking)
      } catch (error) {
        console.warn('解析排名数据失败，重新生成:', error)
        rankingData.value = generateRankingData()
        localStorage.setItem('reportIntegrationRankingData', JSON.stringify(rankingData.value))
      }
    } else {
      rankingData.value = generateRankingData()
      localStorage.setItem('reportIntegrationRankingData', JSON.stringify(rankingData.value))
    }
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('数据加载失败，请刷新页面重试')
    // 使用默认数据
    currentScore.value = 75
    rankingData.value = generateRankingData()
  } finally {
    loading.value = false
  }
}



// 生命周期
onMounted(async () => {
  await initData()

  nextTick(() => {
    try {
      initTrendChart()
      initComparisonChart()

      // 监听窗口大小变化，使用防抖处理
      window.addEventListener('resize', debouncedHandleResize)
    } catch (error) {
      console.error('图表初始化失败:', error)
      ElMessage.error('图表加载失败，请刷新页面重试')
    }
  })
})

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', debouncedHandleResize)
  if (resizeTimer) {
    clearTimeout(resizeTimer)
  }
  if (trendChart.value) {
    trendChart.value.dispose()
  }
  if (comparisonChart.value) {
    comparisonChart.value.dispose()
  }
})

// 历史报告相关方法
const openHistoryReports = () => {
  loadHistoryReports()
  showHistoryReportsDialog.value = true
}

const loadHistoryReports = () => {
  try {
    const savedData = localStorage.getItem('historyReports')
    if (savedData) {
      historyReports.value = JSON.parse(savedData)
    } else {
      // 初始化默认历史报告数据
      historyReports.value = [
        {
          id: 'report_001',
          index: 1,
          reportTitle: '重庆市2024年第一季度报表整合度分析报告',
          reportType: '季度报告',
          generateTime: '2024-03-31 15:30:25',
          reportScore: 78,
          reportStatus: '已完成',
          reportSummary: '本季度整合度水平良好，各区县发展相对均衡',
          dataRange: '2024年1月-3月',
          operator: '张三'
        },
        {
          id: 'report_002',
          index: 2,
          reportTitle: '重庆市2024年2月报表整合度专项检查报告',
          reportType: '专项报告',
          generateTime: '2024-02-29 10:15:42',
          reportScore: 82,
          reportStatus: '已完成',
          reportSummary: '专项检查发现整合度水平有所提升，重点关注区域表现优秀',
          dataRange: '2024年2月',
          operator: '李四'
        },
        {
          id: 'report_003',
          index: 3,
          reportTitle: '重庆市2024年1月报表整合度月度报告',
          reportType: '月度报告',
          generateTime: '2024-01-31 16:45:18',
          reportScore: 75,
          reportStatus: '已完成',
          reportSummary: '月度整合度水平稳定，部分区县需要加强技术支持',
          dataRange: '2024年1月',
          operator: '王五'
        },
        {
          id: 'report_004',
          index: 4,
          reportTitle: '重庆市2023年年度报表整合度总结报告',
          reportType: '年度报告',
          generateTime: '2023-12-31 14:20:33',
          reportScore: 80,
          reportStatus: '已完成',
          reportSummary: '年度整合度水平达到预期目标，整体发展态势良好',
          dataRange: '2023年全年',
          operator: '赵六'
        },
        {
          id: 'report_005',
          index: 5,
          reportTitle: '重庆市2023年第四季度报表整合度分析报告',
          reportType: '季度报告',
          generateTime: '2023-12-30 11:30:15',
          reportScore: 77,
          reportStatus: '已完成',
          reportSummary: '第四季度整合度水平保持稳定，为年度目标达成奠定基础',
          dataRange: '2023年10月-12月',
          operator: '孙七'
        }
      ]
      saveHistoryReports()
    }
  } catch (error) {
    console.error('加载历史报告数据失败:', error)
    ElMessage.error('加载历史报告数据失败')
    historyReports.value = []
  }
}

const saveHistoryReports = () => {
  try {
    localStorage.setItem('historyReports', JSON.stringify(historyReports.value))
  } catch (error) {
    console.error('保存历史报告数据失败:', error)
    ElMessage.error('保存历史报告数据失败')
  }
}

// 获取当前页历史报告数据
const getCurrentHistoryReportsPageData = () => {
  const filtered = historyReports.value
  historyReportsPagination.total = filtered.length

  const start = (historyReportsPagination.page - 1) * historyReportsPagination.size
  const end = start + historyReportsPagination.size

  return filtered.slice(start, end).map((item: HistoryReport, index: number) => ({
    ...item,
    index: start + index + 1
  }))
}

// 历史报告分页处理
const onHistoryReportsPaginationChange = (value: number, type: 'page' | 'size') => {
  if (type === 'page') {
    historyReportsPagination.page = value
  } else {
    historyReportsPagination.size = value
    historyReportsPagination.page = 1
  }
}

// 查看单条报告详情
const viewReportDetail = (report: HistoryReport) => {
  currentReportDetail.value = report
  showReportDetailDialog.value = true
}

// 获取报告状态类型
const getReportStatusType = (status: string) => {
  switch (status) {
    case '已完成':
      return 'success'
    case '生成中':
      return 'warning'
    case '失败':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取历史报告操作按钮
const getHistoryReportButtons = (row: HistoryReport) => {
  return [
    {
      code: 'view',
      label: '查看',
      type: 'primary' as const
    }
  ]
}

// 处理历史报告表格按钮点击
const onHistoryReportTableClickButton = ({ row, btn }: { row: HistoryReport, btn: any }) => {
  switch (btn.code) {
    case 'view':
      viewReportDetail(row)
      break
  }
}


</script>

<route>
{
  meta: {
    title: '结果展示',
    ignoreLabel: false
  }
}
</route>

<template>
  <div class="result-display">
    <Block
      title="报表整合度分析结果"
      :enable-expand-content="true"
      :enableBackButton="false"
      :enable-fixed-height="true"
      :enable-close-button="false"
      @content-expand="() => {}"
    >
      <template #topRight>
        <el-button 
          size="small" 
          type="default" 
          @click="handleGoBack"
          style="margin-right: 8px"
        >
          <el-icon style="margin-right: 4px">
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>
        
        <el-button size="small" type="primary" @click="generateReport">
          生成整合度报告
        </el-button>

        <el-button size="small" type="primary" @click="navigateToIssueData" style="margin-left: 8px">
          查看问题数据
        </el-button>

        <el-button size="small" type="primary" @click="openEvaluationConfig" style="margin-left: 8px">
          配置评估指标
        </el-button>

        <el-button size="small" type="primary" @click="openHistoryReports" style="margin-left: 8px">
          查看历史报告
        </el-button>
      </template>

      <!-- 主要内容区域 -->
      <div class="result-content">
        <!-- 第一行：当前得分和排名表格 -->
        <div class="top-row">
          <!-- 当前整合度得分 -->
          <div class="score-card">
            <el-card class="score-display">
              <div class="score-content">
                <div class="score-number" :style="{ color: getScoreColor(currentScore) }">
                  {{ currentScore }}
                </div>
                <div class="score-label">整合度得分</div>
                <div class="score-level" :style="{ color: getScoreColor(currentScore) }">
                  {{ getScoreLevel(currentScore) }}
                </div>
              </div>
            </el-card>
          </div>

          <!-- 整合度排名表格 -->
          <div class="ranking-table">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>重庆市各区县整合度排名</span>
                </div>
              </template>
              
              <el-table
                :data="rankingData"
                style="width: 100%"
                height="320"
                :show-header="true"
              >
                <el-table-column prop="rank" label="排名" min-width="120" align="center">
                  <template #default="{ row }">
                    <div class="rank-cell">
                      <span>{{ row.rank }}</span>
                      <el-icon v-if="row.isFirst" class="first-icon">
                        <Star />
                      </el-icon>
                      <el-icon v-if="row.isLast" class="last-icon">
                        <Warning />
                      </el-icon>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="district" label="区县" min-width="120" />
                <el-table-column prop="score" label="得分" min-width="120" align="center">
                  <template #default="{ row }">
                    <span :style="{ color: getScoreColor(row.score), fontWeight: 'bold' }">
                      {{ row.score }}分
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="等级" width="100" align="center">
                  <template #default="{ row }">
                    <el-tag 
                      :type="row.score >= 80 ? 'success' : row.score >= 60 ? 'warning' : 'danger'"
                      size="small"
                    >
                      {{ getScoreLevel(row.score) }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </div>

        <!-- 第二行：趋势图和对比图 -->
        <div class="bottom-row">
          <!-- 整合度趋势曲线图 -->
          <div class="trend-chart">
            <el-card>
              <template #header>
                <div class="chart-header">
                  <span>整合度趋势曲线图</span>
                  <el-button
                    size="small"
                    type="primary"
                    :icon="Download"
                    @click="exportTrendAnalysisToPDF"
                    :loading="loading"
                  >
                    生成报表整合度趋势分析报告
                  </el-button>
                </div>
              </template>
              <div ref="trendChartRef" style="width: 100%; height: 350px;"></div>
            </el-card>
          </div>

          <!-- 整合度对比折线图 -->
          <div class="comparison-chart">
            <el-card>
              <template #header>
                <div class="comparison-header">
                  <div class="comparison-title-section">
                    <span>整合度对比分析</span>
                    <div class="comparison-selectors">
                      <el-select
                        v-model="comparisonForm.district1"
                        placeholder="选择地区1"
                        size="small"
                        style="width: 100px; margin-right: 8px"
                        @change="handleComparisonChange"
                      >
                        <el-option
                          v-for="district in chongqingDistricts"
                          :key="district"
                          :label="district"
                          :value="district"
                        />
                      </el-select>
                      <span style="margin: 0 8px;">VS</span>
                      <el-select
                        v-model="comparisonForm.district2"
                        placeholder="选择地区2"
                        size="small"
                        style="width: 100px"
                        @change="handleComparisonChange"
                      >
                        <el-option
                          v-for="district in chongqingDistricts"
                          :key="district"
                          :label="district"
                          :value="district"
                        />
                      </el-select>
                    </div>
                  </div>
                  <el-button
                    size="small"
                    type="primary"
                    :icon="Download"
                    @click="exportComparisonAnalysisToPDF"
                    :loading="loading"
                  >
                    生成报表整合度对比分析报告
                  </el-button>
                </div>
              </template>
              
              <div ref="comparisonChartRef" style="width: 100%; height: 300px;"></div>
            </el-card>
          </div>
        </div>
      </div>
    </Block>

    <!-- 生成整合度报告对话框 -->
    <Dialog
      v-model="showReportDialog"
      title="整合度报告操作"
      width="500px"
      :enable-confirm="false"
      cancel-text="关闭"
    >
      <div class="report-actions">
        <div class="action-grid">
          <div
            v-for="action in reportActions"
            :key="action.action"
            class="action-item"
            @click="handleReportAction(action.action)"
          >
            <el-icon class="action-icon">
              <component :is="action.icon" />
            </el-icon>
            <span class="action-label">{{ action.label }}</span>
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 报告预览对话框 -->
    <Dialog
      v-model="showReportPreviewDialog"
      title="报表整合度分析报告"
      width="1200px"
      :enable-confirm="false"
      cancel-text="关闭"
      :loading="reportPreviewLoading"
    >
      <div class="report-preview" id="report-content">
        <!-- 报告标题和基本信息 -->
        <div class="report-header">
          <h1 class="report-title">重庆市报表整合度分析报告</h1>
          <div class="report-meta">
            <p><strong>生成时间：</strong>{{ new Date().toLocaleString('zh-CN') }}</p>
            <p><strong>报告范围：</strong>重庆市各区县</p>
            <p><strong>分析周期：</strong>最近12个月</p>
          </div>
        </div>

        <!-- 整合度得分概览 -->
        <div class="report-section">
          <h2 class="section-title">一、整合度得分概览</h2>
          <div class="score-overview">
            <div class="score-summary">
              <div class="current-score-display">
                <span class="score-value" :style="{ color: getScoreColor(currentScore) }">
                  {{ currentScore }}分
                </span>
                <span class="score-description">
                  当前重庆市整体报表整合度得分为{{ currentScore }}分，
                  评级为<span :style="{ color: getScoreColor(currentScore) }">{{ getScoreLevel(currentScore) }}</span>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 区县排名详情 -->
        <div class="report-section">
          <h2 class="section-title">二、各区县整合度排名</h2>
          <div class="ranking-summary">
            <p>本次分析涵盖重庆市{{ rankingData.length }}个区县，按整合度得分降序排列如下：</p>
            <div class="ranking-table-container">
              <table class="report-table">
                <thead>
                  <tr>
                    <th>排名</th>
                    <th>区县名称</th>
                    <th>整合度得分</th>
                    <th>评级</th>
                    <th>备注</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="item in rankingData" :key="item.id">
                    <td>{{ item.rank }}</td>
                    <td>{{ item.district }}</td>
                    <td :style="{ color: getScoreColor(item.score), fontWeight: 'bold' }">
                      {{ item.score }}分
                    </td>
                    <td>
                      <span
                        class="level-tag"
                        :class="{
                          'level-excellent': item.score >= 80,
                          'level-good': item.score >= 60 && item.score < 80,
                          'level-poor': item.score < 60
                        }"
                      >
                        {{ getScoreLevel(item.score) }}
                      </span>
                    </td>
                    <td>
                      <span v-if="item.isFirst" class="remark excellent">🏆 优秀示范</span>
                      <span v-else-if="item.isLast" class="remark warning">⚠️ 待改进</span>
                      <span v-else>-</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 趋势分析摘要 -->
        <div class="report-section">
          <h2 class="section-title">三、整合度趋势分析</h2>
          <div class="trend-summary">
            <p>基于最近12个月的数据分析，重庆市报表整合度呈现以下趋势特点：</p>
            <ul class="trend-points">
              <li>整体趋势：整合度水平保持相对稳定，波动范围在合理区间内</li>
              <li>季节性特征：各季度间存在一定差异，需持续关注低分预警情况</li>
              <li>改进建议：建议加强对得分较低区县的技术指导和支持</li>
            </ul>
          </div>
        </div>

        <!-- 对比分析结果 -->
        <div class="report-section">
          <h2 class="section-title">四、区县对比分析</h2>
          <div class="comparison-summary">
            <p>以{{ comparisonForm.district1 }}和{{ comparisonForm.district2 }}为例进行对比分析：</p>
            <div class="comparison-details">
              <div class="comparison-item">
                <strong>{{ comparisonForm.district1 }}：</strong>
                <span>在对比期间表现稳定，整合度水平处于{{ getScoreLevel(Math.floor(Math.random() * 45) + 50) }}水平</span>
              </div>
              <div class="comparison-item">
                <strong>{{ comparisonForm.district2 }}：</strong>
                <span>整合度发展态势良好，与{{ comparisonForm.district1 }}形成良性竞争关系</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 报告结论 -->
        <div class="report-section">
          <h2 class="section-title">五、总结与建议</h2>
          <div class="conclusion">
            <p><strong>总体评价：</strong>重庆市报表整合度整体水平{{ getScoreLevel(currentScore) }}，各区县发展相对均衡。</p>
            <p><strong>主要建议：</strong></p>
            <ul class="suggestions">
              <li>继续加强优秀区县的示范引领作用</li>
              <li>重点关注和帮扶整合度较低的区县</li>
              <li>建立定期监测和评估机制</li>
              <li>促进区县间经验交流与合作</li>
            </ul>
          </div>
        </div>

        <!-- 报告尾注 -->
        <div class="report-footer">
          <p class="footer-text">
            本报告由重庆市报表整合度抽查系统自动生成，数据截止时间：{{ new Date().toLocaleDateString('zh-CN') }}
          </p>
        </div>
      </div>
    </Dialog>

    <!-- 配置评估指标主Dialog -->
    <Dialog
      v-model="showEvaluationConfigDialog"
      title="配置评估指标"
      width="1000px"
      :enable-confirm="false"
      cancel-text="关闭"
    >
      <div class="evaluation-config-content">
        <!-- 计算方法配置区域 -->
        <div class="calculation-method-section" style="margin-bottom: 20px; padding: 16px; background: #f8f9fa; border-radius: 6px; border: 1px solid #e9ecef">
          <div style="display: flex; align-items: center; justify-content: space-between">
            <div style="display: flex; align-items: center; gap: 12px">
              <span style="font-weight: 600; color: #303133; font-size: 14px">整合度计算方法：</span>
              <el-select
                v-model="integrationCalculationMethod"
                placeholder="请选择计算方法"
                style="width: 200px"
                @change="onCalculationMethodChange"
              >
                <el-option
                  v-for="method in calculationMethods"
                  :key="method.value"
                  :label="method.label"
                  :value="method.value"
                />
              </el-select>
            </div>
            <el-button type="success" size="small" @click="saveCalculationMethod">
              <el-icon style="margin-right: 4px">
                <Check />
              </el-icon>
              保存方法
            </el-button>
          </div>
          <div style="margin-top: 8px; font-size: 12px; color: #909399">
            当前选择的计算方法将用于整合度得分的计算，不同方法会影响最终的评估结果
          </div>
        </div>

        <!-- 操作区域 -->
        <div class="evaluation-header">
          <div class="action-section">
            <el-button type="primary" @click="openAddEvaluationForm">
              <el-icon style="margin-right: 4px">
                <Plus />
              </el-icon>
              新增指标
            </el-button>
          </div>
        </div>

        <!-- 评估指标表格 -->
        <TableV2
          ref="evaluationTableRef"
          :columns="evaluationColumns"
          :defaultTableData="getCurrentEvaluationPageData()"
          :enable-toolbar="false"
          :enable-own-button="false"
          :enable-selection="false"
          :enable-index="false"
          :height="evaluationTableHeight"
          :loading="false"
          @sort-change="handleEvaluationTableSort"
        >
          <!-- 序号列 -->
          <template #index="{ row }">
            {{ (evaluationPagination.page - 1) * evaluationPagination.size + row.index }}
          </template>

          <!-- 标签列 -->
          <template #tagText="{ row }">
            <el-tag
              :color="row.tagColor"
              effect="dark"
              style="color: white; border: none"
            >
              {{ row.tagText }}
            </el-tag>
          </template>

          <!-- 指标类别列 -->
          <template #indicatorCategory="{ row }">
            <el-tag
              :type="row.indicatorCategory === '一致性' ? 'primary' :
                    row.indicatorCategory === '准确性' ? 'success' :
                    row.indicatorCategory === '完整性' ? 'warning' : 'danger'"
              size="small"
            >
              {{ row.indicatorCategory }}
            </el-tag>
          </template>

          <!-- 指标权重列 -->
          <template #indicatorWeight="{ row }">
            <span style="font-weight: bold; color: #409EFF">{{ row.indicatorWeight }}%</span>
          </template>

          <!-- 操作列 -->
          <template #action="{ row }">
            <div class="action-buttons">
              <el-button
                v-for="btn in getEvaluationButtons(row)"
                :key="btn.code"
                :type="btn.type"
                size="small"
                @click="onEvaluationTableClickButton({ row, btn })"
              >
                {{ btn.label }}
              </el-button>
            </div>
          </template>
        </TableV2>

        <!-- 分页 -->
        <Pagination
          :total="evaluationPagination.total"
          :current-page="evaluationPagination.page"
          :page-size="evaluationPagination.size"
          @current-change="onEvaluationPaginationChange($event, 'page')"
          @size-change="onEvaluationPaginationChange($event, 'size')"
        />

        <!-- 统计信息 -->
        <div class="evaluation-summary" style="margin-top: 16px; padding: 12px; background: #f5f7fa; border-radius: 4px">
          <span style="color: #606266">
            共 {{ evaluationIndicators.length }} 个评估指标，
            总权重：{{ evaluationIndicators.reduce((sum, item) => sum + item.indicatorWeight, 0) }}%
          </span>
        </div>
      </div>
    </Dialog>

    <!-- 评估指标表单Dialog -->
    <Dialog
      v-model="showEvaluationFormDialog"
      :title="isEditingEvaluation ? '编辑评估指标' : '新增评估指标'"
      width="600px"
      :loading="evaluationFormLoading"
      @clickConfirm="handleEvaluationFormSubmit"
    >
      <el-form
        ref="evaluationFormRef"
        :model="evaluationForm"
        :rules="evaluationFormRules"
        label-width="120px"
        label-position="right"
      >
        <el-form-item label="标签文本" prop="tagText">
          <div style="display: flex; align-items: center; gap: 12px">
            <el-input
              v-model="evaluationForm.tagText"
              placeholder="请输入标签文本"
              style="flex: 1"
              clearable
            />
            <el-color-picker
              v-model="evaluationForm.tagColor"
              show-alpha
              :predefine="['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']"
            />
          </div>
          <div style="margin-top: 8px">
            <span style="color: #909399; font-size: 12px">预览：</span>
            <el-tag
              :color="evaluationForm.tagColor"
              effect="dark"
              style="color: white; border: none; margin-left: 8px"
              size="small"
            >
              {{ evaluationForm.tagText || '标签预览' }}
            </el-tag>
          </div>
        </el-form-item>

        <el-form-item label="指标名称" prop="indicatorName">
          <el-input
            v-model="evaluationForm.indicatorName"
            placeholder="请输入指标名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="指标类别" prop="indicatorCategory">
          <el-select
            v-model="evaluationForm.indicatorCategory"
            placeholder="请选择指标类别"
            style="width: 100%"
          >
            <el-option
              v-for="option in indicatorCategoryOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="指标说明" prop="indicatorDescription">
          <el-input
            v-model="evaluationForm.indicatorDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入指标说明"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="指标权重" prop="indicatorWeight">
          <el-input-number
            v-model="evaluationForm.indicatorWeight"
            :min="0"
            :max="100"
            :step="1"
            controls-position="right"
            style="width: 150px"
          />
          <span style="margin-left: 8px; color: #606266">%（范围：0-100）</span>
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 查看历史报告主Dialog -->
    <Dialog
      v-model="showHistoryReportsDialog"
      title="查看历史报告"
      width="1200px"
      :enable-confirm="false"
      cancel-text="关闭"
    >
      <div class="history-reports-content">
        <!-- 历史报告表格 -->
        <TableV2
          ref="historyReportsTableRef"
          :columns="historyReportsColumns"
          :defaultTableData="getCurrentHistoryReportsPageData()"
          :enable-toolbar="false"
          :enable-own-button="false"
          :enable-selection="false"
          :enable-index="false"
          :height="historyReportsTableHeight"
          :loading="historyReportsLoading"
        >
          <!-- 序号列 -->
          <template #index="{ row }">
            {{ (historyReportsPagination.page - 1) * historyReportsPagination.size + row.index }}
          </template>

          <!-- 报告类型列 -->
          <template #reportType="{ row }">
            <el-tag
              :type="row.reportType === '年度报告' ? 'danger' :
                    row.reportType === '季度报告' ? 'warning' :
                    row.reportType === '月度报告' ? 'primary' : 'info'"
              size="small"
            >
              {{ row.reportType }}
            </el-tag>
          </template>

          <!-- 整合度得分列 -->
          <template #reportScore="{ row }">
            <span
              :style="{
                color: row.reportScore >= 80 ? '#67C23A' :
                       row.reportScore >= 60 ? '#E6A23C' : '#F56C6C',
                fontWeight: 'bold'
              }"
            >
              {{ row.reportScore }}分
            </span>
          </template>

          <!-- 状态列 -->
          <template #reportStatus="{ row }">
            <el-tag
              :type="getReportStatusType(row.reportStatus)"
              size="small"
            >
              {{ row.reportStatus }}
            </el-tag>
          </template>

          <!-- 操作列 -->
          <template #action="{ row }">
            <div class="action-buttons">
              <el-button
                v-for="btn in getHistoryReportButtons(row)"
                :key="btn.code"
                :type="btn.type"
                size="small"
                @click="onHistoryReportTableClickButton({ row, btn })"
              >
                {{ btn.label }}
              </el-button>
            </div>
          </template>
        </TableV2>

        <!-- 分页 -->
        <Pagination
          :total="historyReportsPagination.total"
          :current-page="historyReportsPagination.page"
          :page-size="historyReportsPagination.size"
          @current-change="onHistoryReportsPaginationChange($event, 'page')"
          @size-change="onHistoryReportsPaginationChange($event, 'size')"
        />
      </div>
    </Dialog>

    <!-- 报告详情Dialog -->
    <Dialog
      v-model="showReportDetailDialog"
      :title="currentReportDetail?.reportTitle || '报告详情'"
      width="800px"
      :enable-confirm="false"
      cancel-text="关闭"
    >
      <div class="report-detail-content" v-if="currentReportDetail">
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">报告标题：</span>
              <span class="value">{{ currentReportDetail.reportTitle }}</span>
            </div>
            <div class="detail-item">
              <span class="label">报告类型：</span>
              <el-tag
                :type="currentReportDetail.reportType === '年度报告' ? 'danger' :
                      currentReportDetail.reportType === '季度报告' ? 'warning' :
                      currentReportDetail.reportType === '月度报告' ? 'primary' : 'info'"
                size="small"
              >
                {{ currentReportDetail.reportType }}
              </el-tag>
            </div>

            <div class="detail-item">
              <span class="label">报告状态：</span>
              <el-tag
                :type="getReportStatusType(currentReportDetail.reportStatus)"
                size="small"
              >
                {{ currentReportDetail.reportStatus }}
              </el-tag>
            </div>
            <div class="detail-item">
              <span class="label">数据范围：</span>
              <span class="value">{{ currentReportDetail.dataRange }}</span>
            </div>
            <div class="detail-item">
              <span class="label">生成时间：</span>
              <span class="value">{{ currentReportDetail.generateTime }}</span>
            </div>
            <div class="detail-item">
              <span class="label">操作人：</span>
              <span class="value">{{ currentReportDetail.operator }}</span>
            </div>
          </div>
        </div>

        <div class="detail-section">
          <h3 class="section-title">报告摘要</h3>
          <div class="summary-content">
            <p>{{ currentReportDetail.reportSummary }}</p>
          </div>
        </div>


      </div>
    </Dialog>
  </div>
</template>

<style lang="scss" scoped>
.result-display {
  height: 100%;
  overflow: hidden;

  .result-content {
    padding: 16px;
    min-height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    gap: 20px;

    .top-row {
      display: flex;
      gap: 16px;
      min-height: 380px;
      margin-bottom: 8px;

      .score-card {
        flex: 0 0 300px;

        .score-display {
          height: 100%;

          :deep(.el-card__body) {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
          }

          .score-content {
            text-align: center;

            .score-number {
              font-size: 72px;
              font-weight: bold;
              line-height: 1;
              margin-bottom: 8px;
            }

            .score-label {
              font-size: 16px;
              color: #606266;
              margin-bottom: 8px;
            }

            .score-level {
              font-size: 18px;
              font-weight: bold;
            }
          }
        }
      }

      .ranking-table {
        flex: 1;
        min-width: 0; // 防止flex子项溢出

        .card-header {
          font-weight: bold;
          font-size: 16px;
        }

        .rank-cell {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 4px;

          .first-icon {
            color: #F7BA2A;
            font-size: 16px;
          }

          .last-icon {
            color: #F56C6C;
            font-size: 16px;
          }
        }
      }
    }

    .bottom-row {
      display: flex;
      gap: 16px;
      min-height: 450px;
      flex-shrink: 0;

      .trend-chart {
        flex: 1;
        min-width: 0; // 防止flex子项溢出

        :deep(.el-card) {
          height: 100%;

          .el-card__body {
            height: calc(100% - 60px); // 减去header高度
            padding: 16px;
          }
        }
      }

      .comparison-chart {
        flex: 1;
        min-width: 0; // 防止flex子项溢出

        :deep(.el-card) {
          height: 100%;

          .el-card__body {
            height: calc(100% - 60px); // 减去header高度
            padding: 16px;
          }
        }

        .comparison-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-weight: bold;
          font-size: 16px;
          flex-wrap: wrap;
          gap: 8px;

          .comparison-title-section {
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
          }

          .comparison-selectors {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: normal;
            flex-wrap: wrap;
            gap: 4px;
          }
        }
      }
    }
  }

  // 图表头部样式
  .chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
    font-size: 16px;
    min-height: 40px;
    flex-wrap: wrap;
    gap: 8px;

    .el-button {
      white-space: nowrap;
      flex-shrink: 0;
    }
  }

  // 确保卡片有足够的最小高度
  :deep(.el-card) {
    .el-card__header {
      padding: 16px 20px;
      border-bottom: 1px solid #EBEEF5;
      background-color: #FAFAFA;
    }
  }
}

.report-actions {
  .action-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    padding: 16px 0;

    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px;
      border: 1px solid #DCDFE6;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #409EFF;
        background-color: #F0F9FF;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
      }

      .action-icon {
        font-size: 32px;
        color: #409EFF;
        margin-bottom: 8px;
      }

      .action-label {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
      }
    }
  }
}

// 报告预览样式
.report-preview {
  padding: 20px;
  background: #fff;
  color: #333;
  line-height: 1.6;
  font-family: 'Microsoft YaHei', Arial, sans-serif;

  .report-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #409EFF;

    .report-title {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
      margin: 0 0 15px 0;
    }

    .report-meta {
      font-size: 14px;
      color: #606266;

      p {
        margin: 5px 0;
      }
    }
  }

  .report-section {
    margin-bottom: 25px;

    .section-title {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
      margin: 0 0 15px 0;
      padding-left: 10px;
      border-left: 4px solid #409EFF;
    }
  }

  .score-overview {
    .score-summary {
      background: #F8F9FA;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #E9ECEF;

      .current-score-display {
        text-align: center;

        .score-value {
          font-size: 48px;
          font-weight: bold;
          display: block;
          margin-bottom: 10px;
        }

        .score-description {
          font-size: 16px;
          color: #606266;
        }
      }
    }
  }

  .ranking-summary {
    p {
      margin-bottom: 15px;
      color: #606266;
    }

    .ranking-table-container {
      overflow-x: auto;
    }
  }

  .report-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    font-size: 14px;

    th, td {
      padding: 12px 8px;
      text-align: center;
      border: 1px solid #DCDFE6;
    }

    th {
      background-color: #F5F7FA;
      font-weight: bold;
      color: #303133;
    }

    tbody tr:nth-child(even) {
      background-color: #FAFAFA;
    }

    .level-tag {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;

      &.level-excellent {
        background-color: #F0F9FF;
        color: #67C23A;
        border: 1px solid #67C23A;
      }

      &.level-good {
        background-color: #FDF6EC;
        color: #E6A23C;
        border: 1px solid #E6A23C;
      }

      &.level-poor {
        background-color: #FEF0F0;
        color: #F56C6C;
        border: 1px solid #F56C6C;
      }
    }

    .remark {
      font-size: 12px;
      padding: 2px 6px;
      border-radius: 4px;

      &.excellent {
        background-color: #F0F9FF;
        color: #409EFF;
      }

      &.warning {
        background-color: #FDF6EC;
        color: #E6A23C;
      }
    }
  }

  .trend-summary, .comparison-summary, .conclusion {
    p {
      margin-bottom: 10px;
      color: #606266;
    }

    ul {
      margin: 10px 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        color: #606266;
      }
    }
  }

  .comparison-details {
    background: #F8F9FA;
    padding: 15px;
    border-radius: 6px;
    margin: 15px 0;

    .comparison-item {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .suggestions {
    background: #F0F9FF;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #409EFF;
  }

  .report-footer {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #DCDFE6;
    text-align: center;

    .footer-text {
      font-size: 12px;
      color: #909399;
      margin: 0;
    }
  }
}

// 响应式设计
@media (max-width: 1400px) {
  .result-content {
    .top-row {
      min-height: 420px; // 增加高度以适应内容
    }

    .bottom-row {
      min-height: 500px; // 增加图表区域高度

      .comparison-chart .comparison-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .comparison-title-section {
          width: 100%;
          justify-content: space-between;
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .result-content {
    gap: 24px; // 增加间距

    .top-row {
      flex-direction: column;
      min-height: auto;
      gap: 20px;

      .score-card {
        flex: none;
        width: 100%;

        .score-display {
          height: 200px;
        }
      }

      .ranking-table {
        flex: none;
        width: 100%;
      }
    }

    .bottom-row {
      flex-direction: column;
      min-height: auto;
      gap: 20px;

      .trend-chart,
      .comparison-chart {
        flex: none;
        width: 100%;
        min-height: 400px;

        :deep(.el-card) {
          height: 400px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .result-content {
    padding: 12px;
    gap: 16px;

    .top-row {
      gap: 16px;

      .score-card .score-display .score-content .score-number {
        font-size: 48px;
      }
    }

    .bottom-row {
      gap: 16px;

      .trend-chart,
      .comparison-chart {
        min-height: 350px;

        :deep(.el-card) {
          height: 350px;
        }
      }

      .comparison-chart .comparison-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;

        .comparison-title-section {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }

        .comparison-selectors {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;

          span {
            display: none; // 隐藏VS文字
          }
        }
      }
    }
  }

  // 图表头部按钮在移动端的优化
  .chart-header {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 8px;

    .el-button {
      font-size: 12px;
      padding: 6px 12px;
    }
  }

  .report-actions .action-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

// Element Plus 样式覆盖
:deep(.el-card) {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid #EBEEF5;
  }

  .el-card__body {
    padding: 20px;
  }
}

:deep(.el-table) {
  .el-table__header-wrapper {
    .el-table__header {
      th {
        background-color: #F5F7FA;
        color: #303133;
        font-weight: bold;
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__row {
      &:hover {
        background-color: #F5F7FA;
      }
    }
  }
}

:deep(.el-select) {
  .el-input__inner {
    border-radius: 4px;
  }
}

:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}

// 评估指标配置样式
.evaluation-config-content {
  .evaluation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    flex-wrap: wrap;
    gap: 12px;

    .search-section {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 8px;
    }

    .action-section {
      display: flex;
      align-items: center;
    }
  }

  .evaluation-summary {
    font-size: 14px;
    color: #606266;
    background: #f5f7fa;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
  }
}

// 响应式适配评估指标配置
@media (max-width: 768px) {
  .evaluation-config-content {
    .evaluation-header {
      flex-direction: column;
      align-items: stretch;

      .search-section {
        justify-content: center;

        .el-input,
        .el-select {
          width: 100% !important;
          max-width: 200px;
        }
      }

      .action-section {
        justify-content: center;
      }
    }

    :deep(.el-table) {
      font-size: 12px;

      .el-table__cell {
        padding: 8px 4px;
      }

      .el-button {
        padding: 4px 8px;
        font-size: 12px;
      }
    }
  }
}

// 历史报告相关样式
.history-reports-content {
  .action-buttons {
    display: flex;
    gap: 8px;
  }
}

// 报告详情样式
.report-detail-content {
  .detail-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #e4e7ed;
    }

    .detail-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      .detail-item {
        display: flex;
        align-items: center;

        .label {
          font-weight: 500;
          color: #606266;
          min-width: 100px;
          margin-right: 8px;
        }

        .value {
          color: #303133;
          flex: 1;

          &.score-value {
            font-weight: bold;
            font-size: 16px;
          }
        }
      }
    }

    .summary-content {
      background: #f5f7fa;
      padding: 16px;
      border-radius: 4px;
      border: 1px solid #e4e7ed;

      p {
        margin: 0;
        line-height: 1.6;
        color: #606266;
      }
    }

    .action-buttons {
      display: flex;
      gap: 12px;
    }
  }
}

// 响应式适配报告详情
@media (max-width: 768px) {
  .report-detail-content {
    .detail-section {
      .detail-grid {
        grid-template-columns: 1fr;
        gap: 12px;

        .detail-item {
          flex-direction: column;
          align-items: flex-start;

          .label {
            min-width: auto;
            margin-bottom: 4px;
          }
        }
      }

      .action-buttons {
        flex-direction: column;

        .el-button {
          width: 100%;
        }
      }
    }
  }
}
</style>
