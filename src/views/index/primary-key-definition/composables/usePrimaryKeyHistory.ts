import { ref, computed } from 'vue'
import type { HistoryRecord } from '@/define/primary-key.define'
import { STORAGE_KEYS } from '@/define/primary-key.define'
import { useUserStore } from '@/stores/useUserStore'
import util from '@/plugin/util'

export const usePrimaryKeyHistory = () => {
	// 用户状态管理
	const userStore = useUserStore()
	
	// 响应式数据
	const historyRecords = ref<HistoryRecord[]>([])

	// 生成测试数据（用于演示分页功能）
	const generateTestData = () => {
		const testData: HistoryRecord[] = []
		const dataTypes = ['数值型N', '字符型C', '日期型T', '逻辑型L']
		const operators = ['张三', '李四', '王五', '赵六', '钱七']

		for (let i = 1; i <= 25; i++) {
			const oldType = dataTypes[Math.floor(Math.random() * dataTypes.length)]
			let newType = dataTypes[Math.floor(Math.random() * dataTypes.length)]
			// 确保新旧类型不同
			while (newType === oldType) {
				newType = dataTypes[Math.floor(Math.random() * dataTypes.length)]
			}

			testData.push({
				id: util._guid(),
				primaryKeyId: `pk_${i}`,
				primaryKeyName: `主键${i}`,
				oldDataType: oldType,
				newDataType: newType,
				operatorName: operators[Math.floor(Math.random() * operators.length)],
				operationTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
				operationType: Math.random() > 0.7 ? 'batch_update' : 'update'
			})
		}

		return testData.sort((a, b) => new Date(b.operationTime).getTime() - new Date(a.operationTime).getTime())
	}

	// 从本地存储加载历史记录
	const loadHistoryFromStorage = () => {
		try {
			const storedHistory = localStorage.getItem(STORAGE_KEYS.HISTORY_RECORDS)
			if (storedHistory) {
				historyRecords.value = JSON.parse(storedHistory)
			} else {
				// 如果没有存储的数据，生成测试数据用于演示
				historyRecords.value = generateTestData()
				saveHistoryToStorage()
			}
		} catch (error) {
			console.error('加载历史记录失败:', error)
			historyRecords.value = []
		}
	}

	// 保存历史记录到本地存储
	const saveHistoryToStorage = () => {
		try {
			localStorage.setItem(STORAGE_KEYS.HISTORY_RECORDS, JSON.stringify(historyRecords.value))
		} catch (error) {
			console.error('保存历史记录失败:', error)
		}
	}

	// 获取当前操作人姓名
	const getCurrentOperatorName = (): string => {
		const userInfo = userStore.getUserInfo
		if (userInfo) {
			// 优先使用 displayName，其次使用 name，最后使用 account
			return userInfo.displayName || userInfo.name || userInfo.account || '未知用户'
		}
		return '未知用户'
	}

	// 添加历史记录
	const addHistoryRecord = (
		primaryKeyId: string,
		primaryKeyName: string,
		oldDataType: string,
		newDataType: string,
		operationType: 'update' | 'batch_update' = 'update'
	) => {
		// 如果数据类型没有变化，不记录历史
		if (oldDataType === newDataType) {
			return
		}

		const historyRecord: HistoryRecord = {
			id: util._guid(),
			primaryKeyId,
			primaryKeyName,
			oldDataType,
			newDataType,
			operatorName: getCurrentOperatorName(),
			operationTime: new Date().toISOString(),
			operationType
		}

		historyRecords.value.unshift(historyRecord) // 新记录插入到开头
		saveHistoryToStorage()
	}

	// 批量添加历史记录
	const batchAddHistoryRecords = (records: Array<{
		primaryKeyId: string
		primaryKeyName: string
		oldDataType: string
		newDataType: string
	}>) => {
		const operatorName = getCurrentOperatorName()
		const operationTime = new Date().toISOString()

		records.forEach(record => {
			// 如果数据类型没有变化，不记录历史
			if (record.oldDataType === record.newDataType) {
				return
			}

			const historyRecord: HistoryRecord = {
				id: util._guid(),
				primaryKeyId: record.primaryKeyId,
				primaryKeyName: record.primaryKeyName,
				oldDataType: record.oldDataType,
				newDataType: record.newDataType,
				operatorName,
				operationTime,
				operationType: 'batch_update'
			}

			historyRecords.value.unshift(historyRecord)
		})

		saveHistoryToStorage()
	}

	// 获取所有历史记录（按时间倒序）
	const getAllHistoryRecords = computed(() => {
		return historyRecords.value.sort((a, b) => 
			new Date(b.operationTime).getTime() - new Date(a.operationTime).getTime()
		)
	})

	// 获取指定主键的历史记录
	const getHistoryByPrimaryKeyId = (primaryKeyId: string) => {
		return historyRecords.value
			.filter(record => record.primaryKeyId === primaryKeyId)
			.sort((a, b) => 
				new Date(b.operationTime).getTime() - new Date(a.operationTime).getTime()
			)
	}

	// 格式化操作时间
	const formatOperationTime = (isoString: string): string => {
		try {
			const date = new Date(isoString)
			return date.toLocaleString('zh-CN', {
				year: 'numeric',
				month: '2-digit',
				day: '2-digit',
				hour: '2-digit',
				minute: '2-digit',
				second: '2-digit'
			})
		} catch (error) {
			return isoString
		}
	}

	// 获取数据类型显示名称
	const getDataTypeLabel = (dataType: string): string => {
		const typeMap: Record<string, string> = {
			'string_c': '字符型C',
			'number_n': '数值型N',
			'boolean_b': '逻辑性B',
			'date_t': '日期型T',
			'xxxx': 'XXXX'
		}
		return typeMap[dataType] || dataType
	}

	// 清空所有历史记录
	const clearAllHistory = () => {
		historyRecords.value = []
		saveHistoryToStorage()
	}

	// 初始化
	loadHistoryFromStorage()

	return {
		// 数据
		historyRecords: getAllHistoryRecords,
		
		// 方法
		addHistoryRecord,
		batchAddHistoryRecords,
		getHistoryByPrimaryKeyId,
		formatOperationTime,
		getDataTypeLabel,
		clearAllHistory,
		loadHistoryFromStorage
	}
}
