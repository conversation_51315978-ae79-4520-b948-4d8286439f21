import { ref, computed } from 'vue'
import type {
	PrimaryKeyData,
	SortConfig,
	BatchUpdateData
} from '@/define/primary-key.define'
import {
	STORAGE_KEYS,
	getDefaultPrimaryKeyData,
	getDefaultSortConfig
} from '@/define/primary-key.define'
import { usePrimaryKeyHistory } from './usePrimaryKeyHistory'
import util from '@/plugin/util'

export const usePrimaryKeyStorage = () => {
	// 使用历史记录 composable
	const { addHistoryRecord, batchAddHistoryRecords } = usePrimaryKeyHistory()

	// 响应式数据
	const primaryKeys = ref<PrimaryKeyData[]>([])
	const sortConfig = ref<SortConfig>(getDefaultSortConfig())
	const selectedKeys = ref<string[]>([])

	// 从本地存储加载数据
	const loadFromStorage = () => {
		try {
			// 加载主键数据
			const storedKeys = localStorage.getItem(STORAGE_KEYS.PRIMARY_KEYS)
			if (storedKeys) {
				primaryKeys.value = JSON.parse(storedKeys)
			} else {
				// 如果没有存储数据，使用默认数据
				primaryKeys.value = getDefaultPrimaryKeyData()
				saveToStorage()
			}

			// 加载排序配置
			const storedSortConfig = localStorage.getItem(STORAGE_KEYS.SORT_CONFIG)
			if (storedSortConfig) {
				sortConfig.value = JSON.parse(storedSortConfig)
			}
		} catch (error) {
			console.error('加载本地存储数据失败:', error)
			// 如果加载失败，使用默认数据
			primaryKeys.value = getDefaultPrimaryKeyData()
			sortConfig.value = getDefaultSortConfig()
		}
	}

	// 保存数据到本地存储
	const saveToStorage = () => {
		try {
			localStorage.setItem(STORAGE_KEYS.PRIMARY_KEYS, JSON.stringify(primaryKeys.value))
			localStorage.setItem(STORAGE_KEYS.SORT_CONFIG, JSON.stringify(sortConfig.value))
		} catch (error) {
			console.error('保存数据到本地存储失败:', error)
		}
	}

	// 添加主键
	const addPrimaryKey = (keyData: Omit<PrimaryKeyData, 'id' | 'sequence' | 'createTime' | 'updateTime'>) => {
		const maxSequence = Math.max(...primaryKeys.value.map(k => k.sequence), 0)
		const newKey: PrimaryKeyData = {
			...keyData,
			id: util._guid(),
			sequence: maxSequence + 1,
			createTime: new Date().toISOString(),
			updateTime: new Date().toISOString()
		}
		primaryKeys.value.push(newKey)
		saveToStorage()
		return newKey
	}

	// 更新主键
	const updatePrimaryKey = (id: string, updates: Partial<PrimaryKeyData>) => {
		const index = primaryKeys.value.findIndex(k => k.id === id)
		if (index !== -1) {
			const oldData = primaryKeys.value[index]

			// 记录历史（仅当数据类型发生变化时）
			if (updates.dataType && updates.dataType !== oldData.dataType) {
				addHistoryRecord(
					id,
					oldData.name,
					oldData.dataType,
					updates.dataType,
					'update'
				)
			}

			primaryKeys.value[index] = {
				...primaryKeys.value[index],
				...updates,
				updateTime: new Date().toISOString()
			}
			saveToStorage()
			return primaryKeys.value[index]
		}
		return null
	}

	// 删除主键
	const deletePrimaryKey = (id: string) => {
		const index = primaryKeys.value.findIndex(k => k.id === id)
		if (index !== -1) {
			primaryKeys.value.splice(index, 1)
			// 重新排序序号
			primaryKeys.value.forEach((key, idx) => {
				key.sequence = idx + 1
			})
			saveToStorage()
			return true
		}
		return false
	}

	// 批量更新主键
	const batchUpdatePrimaryKeys = (ids: string[], updates: BatchUpdateData) => {
		let updatedCount = 0
		const historyRecords: Array<{
			primaryKeyId: string
			primaryKeyName: string
			oldDataType: string
			newDataType: string
		}> = []

		ids.forEach(id => {
			const index = primaryKeys.value.findIndex(k => k.id === id)
			if (index !== -1) {
				const oldData = primaryKeys.value[index]

				// 收集历史记录数据（仅当数据类型发生变化时）
				if (updates.dataType && updates.dataType !== oldData.dataType) {
					historyRecords.push({
						primaryKeyId: id,
						primaryKeyName: oldData.name,
						oldDataType: oldData.dataType,
						newDataType: updates.dataType
					})
				}

				primaryKeys.value[index] = {
					...primaryKeys.value[index],
					...updates,
					updateTime: new Date().toISOString()
				}
				updatedCount++
			}
		})

		if (updatedCount > 0) {
			saveToStorage()
			// 批量添加历史记录
			if (historyRecords.length > 0) {
				batchAddHistoryRecords(historyRecords)
			}
		}
		return updatedCount
	}

	// 更新排序配置
	const updateSortConfig = (config: SortConfig) => {
		sortConfig.value = config
		saveToStorage()
	}

	// 根据排序配置获取排序后的主键列表
	const sortedPrimaryKeys = computed(() => {
		if (!sortConfig.value.field || !sortConfig.value.order) {
			return primaryKeys.value
		}

		const sorted = [...primaryKeys.value].sort((a, b) => {
			const field = sortConfig.value.field as keyof PrimaryKeyData
			const aValue = a[field]
			const bValue = b[field]

			if (aValue === bValue) return 0

			let comparison = 0
			if (typeof aValue === 'string' && typeof bValue === 'string') {
				comparison = aValue.localeCompare(bValue)
			} else if (typeof aValue === 'number' && typeof bValue === 'number') {
				comparison = aValue - bValue
			} else {
				comparison = String(aValue).localeCompare(String(bValue))
			}

			return sortConfig.value.order === 'asc' ? comparison : -comparison
		})

		return sorted
	})

	// 根据名称搜索主键
	const searchPrimaryKeys = (searchTerm: string) => {
		if (!searchTerm.trim()) {
			return sortedPrimaryKeys.value
		}
		return sortedPrimaryKeys.value.filter(key => 
			key.name.toLowerCase().includes(searchTerm.toLowerCase())
		)
	}

	// 清空所有数据
	const clearAllData = () => {
		primaryKeys.value = []
		saveToStorage()
	}

	// 批量添加数据
	const batchAddPrimaryKeys = (keys: PrimaryKeyData[]) => {
		keys.forEach(key => {
			primaryKeys.value.push(key)
		})
		saveToStorage()
	}

	// 初始化
	loadFromStorage()

	return {
		// 数据
		primaryKeys: sortedPrimaryKeys,
		rawPrimaryKeys: primaryKeys, // 原始数据，用于直接操作
		sortConfig,
		selectedKeys,

		// 方法
		addPrimaryKey,
		updatePrimaryKey,
		deletePrimaryKey,
		batchUpdatePrimaryKeys,
		updateSortConfig,
		searchPrimaryKeys,
		loadFromStorage,
		saveToStorage,
		clearAllData,
		batchAddPrimaryKeys
	}
}
