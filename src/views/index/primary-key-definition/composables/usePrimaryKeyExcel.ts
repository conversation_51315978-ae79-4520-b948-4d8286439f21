import { ref } from 'vue'
import ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'
import { ElMessage } from 'element-plus'
import type { PrimaryKeyData } from '@/define/primary-key.define'
import { PrimaryKeyDataTypes } from '@/define/primary-key.define'
import util from '@/plugin/util'

export const usePrimaryKeyExcel = () => {
	const isImporting = ref(false)
	const isExporting = ref(false)

	// Excel 列定义 - 根据用户要求修改
	const excelColumns = [
		{ header: '主键名称', key: 'name', width: 20 },
		{ header: '数据类型', key: 'dataType', width: 15 },
		{ header: '最小长度', key: 'minLength', width: 12 },
		{ header: '最大长度', key: 'maxLength', width: 12 },
		{ header: '关联业务表', key: 'relatedTable', width: 20 },
		{ header: '是否默认值', key: 'isDefaultValue', width: 12 }
	]

	// 导出 Excel
	const exportToExcel = async (data: PrimaryKeyData[], filename: string = '主键定义数据') => {
		try {
			isExporting.value = true

			const workbook = new ExcelJS.Workbook()
			workbook.creator = 'Primary Key Definition System'
			workbook.lastModifiedBy = 'System'
			workbook.created = new Date()
			workbook.modified = new Date()

			// 创建工作表
			const worksheet = workbook.addWorksheet('主键定义')

			// 设置列
			worksheet.columns = excelColumns

			// 设置表头样式
			const headerRow = worksheet.getRow(1)
			headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
			headerRow.fill = {
				type: 'pattern',
				pattern: 'solid',
				fgColor: { argb: '4472C4' }
			}
			headerRow.alignment = { horizontal: 'center', vertical: 'middle' }
			headerRow.height = 25

			// 添加数据
			data.forEach((item, index) => {
				const row = worksheet.addRow({
					name: item.name,
					dataType: getDataTypeLabel(item.dataType),
					minLength: item.minLength || '',
					maxLength: item.maxLength || '',
					relatedTable: item.relatedTable || '',
					isDefaultValue: item.isDefaultValue ? '是' : '否'
				})

				// 设置数据行样式
				row.alignment = { horizontal: 'left', vertical: 'middle' }
				row.height = 20

				// 交替行颜色
				if (index % 2 === 1) {
					row.fill = {
						type: 'pattern',
						pattern: 'solid',
						fgColor: { argb: 'F2F2F2' }
					}
				}
			})

			// 设置边框
			worksheet.eachRow((row, rowNumber) => {
				row.eachCell((cell) => {
					cell.border = {
						top: { style: 'thin' },
						left: { style: 'thin' },
						bottom: { style: 'thin' },
						right: { style: 'thin' }
					}
				})
			})

			// 自动调整列宽
			worksheet.columns.forEach((column) => {
				if (column.width && column.width < 10) {
					column.width = 10
				}
			})

			// 生成文件
			const buffer = await workbook.xlsx.writeBuffer()
			const blob = new Blob([buffer], {
				type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
			})

			// 下载文件
			const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
			saveAs(blob, `${filename}_${timestamp}.xlsx`)

			ElMessage.success('导出成功')
		} catch (error) {
			console.error('导出失败:', error)
			ElMessage.error('导出失败，请重试')
		} finally {
			isExporting.value = false
		}
	}

	// 导入 Excel
	const importFromExcel = async (file: File): Promise<PrimaryKeyData[]> => {
		return new Promise(async (resolve, reject) => {
			try {
				isImporting.value = true

				const workbook = new ExcelJS.Workbook()
				const arrayBuffer = await file.arrayBuffer()
				await workbook.xlsx.load(arrayBuffer)

				const worksheet = workbook.getWorksheet(1)
				if (!worksheet) {
					throw new Error('Excel 文件中没有找到工作表')
				}

				const importedData: PrimaryKeyData[] = []
				const errors: string[] = []

				// 从第二行开始读取数据（第一行是表头）
				worksheet.eachRow((row, rowNumber) => {
					if (rowNumber === 1) return // 跳过表头

					try {
						// 根据新的列结构读取数据
						const name = String(row.getCell(1).value || '').trim()
						const dataTypeLabel = String(row.getCell(2).value || '').trim()
						const minLength = row.getCell(3).value ? Number(row.getCell(3).value) : undefined
						const maxLength = row.getCell(4).value ? Number(row.getCell(4).value) : undefined
						const relatedTable = String(row.getCell(5).value || '').trim()
						const isDefaultValueText = String(row.getCell(6).value || '').trim()

						// 验证必填项
						if (!name) {
							errors.push(`第${rowNumber}行：主键名称不能为空`)
							return
						}
						if (!dataTypeLabel) {
							errors.push(`第${rowNumber}行：数据类型不能为空`)
							return
						}
						if (minLength === undefined || minLength === null) {
							errors.push(`第${rowNumber}行：最小长度不能为空`)
							return
						}
						if (maxLength === undefined || maxLength === null) {
							errors.push(`第${rowNumber}行：最大长度不能为空`)
							return
						}

						// 转换数据类型
						const dataType = getDataTypeValue(dataTypeLabel)
						if (!dataType) {
							errors.push(`第${rowNumber}行：数据类型"${dataTypeLabel}"无效`)
							return
						}

						// 转换布尔值
						const isDefaultValue = ['是', 'true', '1', 'yes'].includes(isDefaultValueText.toLowerCase())

						// 验证长度
						if (minLength !== undefined && maxLength !== undefined && maxLength < minLength) {
							errors.push(`第${rowNumber}行：最大长度不能小于最小长度`)
							return
						}

						const primaryKey: PrimaryKeyData = {
							id: util._guid(),
							sequence: importedData.length + 1,
							name,
							dataType,
							minLength,
							maxLength,
							description: undefined, // 不再从Excel导入说明
							isDefaultValue,
							updateNotification: false, // 默认值
							encryptionLevel: undefined, // 不再从Excel导入
							encryptionContent: undefined, // 不再从Excel导入
							relatedTable: relatedTable || undefined,
							createTime: new Date().toISOString(),
							updateTime: new Date().toISOString()
						}

						importedData.push(primaryKey)
					} catch (error) {
						errors.push(`第${rowNumber}行：数据格式错误`)
					}
				})

				if (errors.length > 0) {
					const errorMessage = errors.slice(0, 5).join('\n') + (errors.length > 5 ? '\n...' : '')
					throw new Error(`导入失败，发现以下错误：\n${errorMessage}`)
				}

				if (importedData.length === 0) {
					throw new Error('Excel 文件中没有有效的数据')
				}

				ElMessage.success(`成功导入 ${importedData.length} 条数据`)
				resolve(importedData)
			} catch (error) {
				console.error('导入失败:', error)
				const message = error instanceof Error ? error.message : '导入失败，请检查文件格式'
				ElMessage.error(message)
				reject(error)
			} finally {
				isImporting.value = false
			}
		})
	}

	// 生成导入模板
	const generateTemplate = async () => {
		try {
			const workbook = new ExcelJS.Workbook()
			workbook.creator = 'Primary Key Definition System'

			const worksheet = workbook.addWorksheet('主键定义模板')
			worksheet.columns = excelColumns

			// 设置表头样式
			const headerRow = worksheet.getRow(1)
			headerRow.font = { bold: true, color: { argb: 'FFFFFF' } }
			headerRow.fill = {
				type: 'pattern',
				pattern: 'solid',
				fgColor: { argb: '4472C4' }
			}
			headerRow.alignment = { horizontal: 'center', vertical: 'middle' }
			headerRow.height = 25

			// 添加示例数据
			const exampleData = [
				{
					name: '示例主键1',
					dataType: '字符型C',
					minLength: 1,
					maxLength: 50,
					relatedTable: '用户表',
					isDefaultValue: '是'
				},
				{
					name: '示例主键2',
					dataType: '数值型N',
					minLength: 1,
					maxLength: 10,
					relatedTable: '订单表',
					isDefaultValue: '否'
				}
			]

			exampleData.forEach((item) => {
				const row = worksheet.addRow(item)
				row.alignment = { horizontal: 'left', vertical: 'middle' }
				row.height = 20
			})

			// 设置边框
			worksheet.eachRow((row) => {
				row.eachCell((cell) => {
					cell.border = {
						top: { style: 'thin' },
						left: { style: 'thin' },
						bottom: { style: 'thin' },
						right: { style: 'thin' }
					}
				})
			})

			// 删除说明内容，避免上传时出现问题

			// 生成文件
			const buffer = await workbook.xlsx.writeBuffer()
			const blob = new Blob([buffer], {
				type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
			})

			saveAs(blob, '主键定义导入模板.xlsx')
			ElMessage.success('模板下载成功')
		} catch (error) {
			console.error('生成模板失败:', error)
			ElMessage.error('生成模板失败，请重试')
		}
	}

	// 辅助函数：获取数据类型标签
	const getDataTypeLabel = (value: string) => {
		const type = PrimaryKeyDataTypes.find(t => t.value === value)
		return type ? type.label : value
	}

	// 辅助函数：获取数据类型值
	const getDataTypeValue = (label: string) => {
		const type = PrimaryKeyDataTypes.find(t => t.label === label)
		return type ? type.value : null
	}

	return {
		isImporting,
		isExporting,
		exportToExcel,
		importFromExcel,
		generateTemplate
	}
}
