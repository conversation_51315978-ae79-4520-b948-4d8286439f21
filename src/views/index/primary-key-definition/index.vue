<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled, InfoFilled, QuestionFilled, ArrowDown, Lock } from '@element-plus/icons-vue'

import { usePrimaryKeyStorage } from './composables/usePrimaryKeyStorage'
import { usePrimaryKeyExcel } from './composables/usePrimaryKeyExcel'

// 路由实例
const router = useRouter()
import HistoryDialog from './components/HistoryDialog.vue'
import type { PrimaryKeyData, BatchUpdateData, CleanupRuleData, HistoryColumnConfig, StorageLocationConfig, BackupStrategyConfig, CompositeKeyConfig, IndexConfig, LockConfig, PrimaryKeyStatsData, PrimaryKeyAutoGenConfig, AutoIncrementConfig, UUIDConfig, PrimaryKeyAuthStrategyConfig, PrimaryKeyUniquenessConfig, ForeignKeyRelationConfig, PrimaryKeyChangePermissionConfig, DynamicExtensionConfig } from '@/define/primary-key.define'
import { PrimaryKeyDataTypes, EncryptionLevels, CleanupStrategies, StorageLocationOptions, BackupFrequencyOptions, RetentionTimeOptions, IndexFieldOptions, getDefaultHistoryColumnConfig, getDefaultBackupStrategyConfig, getDefaultCompositeKeyConfig, getDefaultIndexConfig, getDefaultLockConfig, PrimaryKeyStrategyOptions, UUIDVersionOptions, getDefaultPrimaryKeyAutoGenConfig, STORAGE_KEYS, PrimaryKeyStrategy, getDefaultPrimaryKeyAuthStrategyConfig, getDefaultPrimaryKeyUniquenessConfig, getDefaultForeignKeyRelationConfig, getDefaultPrimaryKeyChangePermissionConfig, getDefaultDynamicExtensionConfig } from '@/define/primary-key.define'

// 主键缓存配置和关联数据同步配置的localStorage键
const CACHE_CONFIG_KEY = 'PRIMARY_KEYS_CACHE_CONFIG'
const DATA_SYNC_CONFIG_KEY = 'PRIMARY_KEYS_DATA_SYNC_CONFIG'
const VISUAL_IDENTIFIER_CONFIG_KEY = 'PRIMARY_KEYS_VISUAL_IDENTIFIER_CONFIG'
const ENCRYPTION_CONFIG_KEY = 'PRIMARY_KEYS_ENCRYPTION_CONFIG'

// 使用本地存储 composable
const {
	primaryKeys,
	rawPrimaryKeys,
	sortConfig,
	selectedKeys,
	addPrimaryKey,
	updatePrimaryKey,
	deletePrimaryKey,
	batchUpdatePrimaryKeys,
	updateSortConfig,
	clearAllData,
	batchAddPrimaryKeys
} = usePrimaryKeyStorage()

// 使用 Excel 导入导出 composable
const {
	isImporting,
	isExporting,
	exportToExcel,
	importFromExcel,
	generateTemplate
} = usePrimaryKeyExcel()

// 响应式数据
const searchKeyword = ref('')
const showSearchArea = ref(true)
const showAddDialog = ref(false)

// 批量修改相关
const selectedRows = ref<PrimaryKeyData[]>([])
const showBatchEditDialog = ref(false)

// 搜索表单相关
const searchForm = ref({
	keyword: '',
	dataType: '',
	minLength: '',
	maxLength: ''
})

const searchFormProp = ref([
	{
		prop: 'keyword',
		label: '主键名称',
		type: 'text',
		placeholder: '请输入主键名称'
	},
	{
		prop: 'dataType',
		label: '数据类型',
		type: 'select',
		options: [
			{ label: '全部', value: '' },
			...PrimaryKeyDataTypes
		]
	},
	{
		prop: 'minLength',
		label: '最小长度',
		type: 'text',
		placeholder: '请输入最小长度'
	},
	{
		prop: 'maxLength',
		label: '最大长度',
		type: 'text',
		placeholder: '请输入最大长度'
	}
])
const showBatchUpdateDialog = ref(false)
const showCleanupRuleDialog = ref(false)
const showStorageLocationDialog = ref(false)
const showImportDialog = ref(false)
const showDetailDialog = ref(false)
const showHistoryDialog = ref(false)
const historyPrimaryKeyId = ref<string>('')
const historyDialogTitle = ref('历史记录')
const showPrimaryKeyValidationDialog = ref(false)
const primaryKeyFunctionEnabled = ref(true)
const primaryKeyValidationFunction = ref('UUID')
const loading = ref(false)
const isEditMode = ref(false)
const isDetailMode = ref(false)

// 主键变更记录配置相关
const showHistoryColumnConfigDialog = ref(false)
const historyColumnConfig = ref<HistoryColumnConfig>(getDefaultHistoryColumnConfig())

// 主键备份策略配置相关
const showBackupStrategyDialog = ref(false)
const backupStrategyConfig = ref<BackupStrategyConfig>(getDefaultBackupStrategyConfig())

// 复合主键配置相关
const showCompositeKeyConfigDialog = ref(false)
const currentCompositeKeyConfig = ref<CompositeKeyConfig>(getDefaultCompositeKeyConfig())

// 主键索引配置相关
const showIndexConfigDialog = ref(false)
const currentIndexConfig = ref<IndexConfig>(getDefaultIndexConfig())

// 主键锁定配置相关
const showLockConfigDialog = ref(false)
const currentLockConfig = ref<LockConfig>(getDefaultLockConfig())
const lockTimeForm = ref({
	hours: 0,
	minutes: 0,
	seconds: 0
})

// 主键统计相关
const showPrimaryKeyStatsDialog = ref(false)
const primaryKeyStatsData = ref<PrimaryKeyStatsData[]>([])

// 生成主键统计数据
const generatePrimaryKeyStats = (): PrimaryKeyStatsData[] => {
	return primaryKeys.value.map(key => ({
		id: key.id,
		name: key.name,
		usageCount: Math.floor(Math.random() * 51) // 生成0-50之间的随机数
	}))
}

// 主键自动生成配置相关
const showAutoGenConfigDialog = ref(false)
const autoGenConfig = ref<PrimaryKeyAutoGenConfig>(getDefaultPrimaryKeyAutoGenConfig())
const originalAutoGenConfig = ref<PrimaryKeyAutoGenConfig>(getDefaultPrimaryKeyAutoGenConfig())

// 主键认证策略相关
const showAuthStrategyDialog = ref(false)
const authStrategyConfig = ref<PrimaryKeyAuthStrategyConfig>(getDefaultPrimaryKeyAuthStrategyConfig())

// 主键唯一性配置相关
const showUniquenessConfigDialog = ref(false)
const uniquenessConfig = ref<PrimaryKeyUniquenessConfig>(getDefaultPrimaryKeyUniquenessConfig())
const originalAuthStrategyConfig = ref<PrimaryKeyAuthStrategyConfig>(getDefaultPrimaryKeyAuthStrategyConfig())

// 主键与外键关联配置相关
const showForeignKeyRelationDialog = ref(false)
const currentForeignKeyRelationConfig = ref<ForeignKeyRelationConfig>(getDefaultForeignKeyRelationConfig())

// 主键更改权限配置相关
const showChangePermissionDialog = ref(false)
const changePermissionConfig = ref<PrimaryKeyChangePermissionConfig>(getDefaultPrimaryKeyChangePermissionConfig())
const originalChangePermissionConfig = ref<PrimaryKeyChangePermissionConfig>(getDefaultPrimaryKeyChangePermissionConfig())

// 主键动态扩展配置相关
const showDynamicExtensionDialog = ref(false)
const currentDynamicExtensionConfig = ref<DynamicExtensionConfig>(getDefaultDynamicExtensionConfig())

// 主键可视化标识配置相关
const showVisualIdentifierDialog = ref(false)
const currentVisualIdentifierConfig = ref({
	enabled: false,
	color: '#409EFF',
	fontSize: 14
})

// 存储所有主键的可视化配置，用于响应式更新
const visualIdentifierConfigs = ref<Record<string, any>>({})

// 主键加密设置相关
const showEncryptionConfigDialog = ref(false)
const encryptionConfigForm = ref({
	encryptionType: '',
	encryptionKey: '',
	description: ''
})
const currentEncryptionRow = ref<PrimaryKeyData | null>(null)
const encryptionConfigs = ref<Record<string, any>>({})

// 加密设置表单验证规则
const encryptionFormRules = {
	encryptionType: [
		{ required: true, message: '请选择加密类型', trigger: 'change' }
	],
	encryptionKey: [
		{ required: true, message: '请输入加密密钥', trigger: 'blur' },
		{ min: 1, max: 100, message: '密钥长度应在1-100个字符之间', trigger: 'blur' }
	]
}

// 主键动态调整相关
const showDynamicAdjustDialog = ref(false)
const dynamicAdjustForm = ref({
	newName: ''
})

// 主键缓存配置相关
const showCacheConfigDialog = ref(false)
const currentCacheConfig = ref({
	enabled: false,
	expireTime: 300 // 默认5分钟（300秒）
})

// 主键关联数据同步配置相关
const showDataSyncConfigDialog = ref(false)
const currentDataSyncConfig = ref({
	syncField: ''
})

// 主表字段选项数据
const masterTableFieldOptions = [
	{ label: '用户编号', value: 'user_id' },
	{ label: '用户名称', value: 'user_name' },
	{ label: '用户邮箱', value: 'user_email' },
	{ label: '部门编号', value: 'dept_id' },
	{ label: '部门名称', value: 'dept_name' },
	{ label: '角色编号', value: 'role_id' },
	{ label: '角色名称', value: 'role_name' },
	{ label: '身份证号', value: 'id_card' },
	{ label: '手机号', value: 'phone' },
	{ label: '性别', value: 'gender' },
	{ label: '年龄', value: 'age' },
	{ label: '创建时间', value: 'create_time' },
	{ label: '更新时间', value: 'update_time' }
]

// 关联数据同步字段选项
const dataSyncFieldOptions = [
	{ label: '用户信息', value: 'user_info' },
	{ label: '部门信息', value: 'dept_info' },
	{ label: '角色权限', value: 'role_permission' },
	{ label: '系统配置', value: 'system_config' },
	{ label: '业务数据', value: 'business_data' },
	{ label: '日志记录', value: 'log_record' },
	{ label: '文件管理', value: 'file_management' },
	{ label: '消息通知', value: 'message_notification' }
]

// 从表字段选项数据
const slaveTableFieldOptions = [
	{ label: '图表编号', value: 'chart_id' },
	{ label: '图表名称', value: 'chart_name' },
	{ label: '图表类型', value: 'chart_type' },
	{ label: '部门编号', value: 'dept_id' },
	{ label: '角色编号', value: 'role_id' },
	{ label: '分类编号', value: 'category_id' },
	{ label: '父级编号', value: 'parent_id' },
	{ label: '外键编号', value: 'foreign_key_id' },
	{ label: '引用编号', value: 'reference_id' },
	{ label: '关联标识', value: 'relation_id' },
	{ label: '绑定编号', value: 'bind_id' },
	{ label: '链接编号', value: 'link_id' }
]

// 授权人员选项数据
const authorizedPersonOptions = [
	{ label: '杨绍东', value: '杨绍东' },
	{ label: '张建波', value: '张建波' },
	{ label: '穆斯全', value: '穆斯全' },
	{ label: '郭威', value: '郭威' },
	{ label: '蒋干', value: '蒋干' },
	{ label: '陈振武', value: '陈振武' },
	{ label: '叶黄霞', value: '叶黄霞' },
	{ label: '李达康', value: '李达康' },
	{ label: '王大路', value: '王大路' },
	{ label: '赵德汉', value: '赵德汉' },
	{ label: '高育良', value: '高育良' },
	{ label: '祁同伟', value: '祁同伟' },
	{ label: '沙瑞金', value: '沙瑞金' },
	{ label: '易学习', value: '易学习' },
	{ label: '陈岩石', value: '陈岩石' }
]

// 动态扩展字段选项数据
const extensionFieldOptions = [
	{ label: '扩展字段1', value: 'extension_field_1' },
	{ label: '扩展字段2', value: 'extension_field_2' },
	{ label: '扩展字段3', value: 'extension_field_3' },
	{ label: '自定义字段', value: 'custom_field' },
	{ label: '备注字段', value: 'remark_field' },
	{ label: '状态字段', value: 'status_field' },
	{ label: '标签字段', value: 'tag_field' },
	{ label: '分类字段', value: 'category_field' },
	{ label: '优先级字段', value: 'priority_field' },
	{ label: '配置字段', value: 'config_field' }
]

// 扩展类型选项数据
const extensionTypeOptions = [
	{ label: '文本类型', value: 'text' },
	{ label: '数字类型', value: 'number' },
	{ label: '日期类型', value: 'date' },
	{ label: '时间类型', value: 'datetime' },
	{ label: '布尔类型', value: 'boolean' },
	{ label: '枚举类型', value: 'enum' },
	{ label: 'JSON类型', value: 'json' },
	{ label: '文件类型', value: 'file' }
]

// UUID格式选项的计算属性
const uuidFormatOptions = computed({
	get: () => {
		const options: string[] = []
		if (autoGenConfig.value.uuidConfig?.uppercase) options.push('uppercase')
		if (autoGenConfig.value.uuidConfig?.withHyphen) options.push('withHyphen')
		return options
	},
	set: (value: string[]) => {
		if (autoGenConfig.value.uuidConfig) {
			autoGenConfig.value.uuidConfig.uppercase = value.includes('uppercase')
			autoGenConfig.value.uuidConfig.withHyphen = value.includes('withHyphen')
		}
	}
})

// 加载主键自动生成配置
const loadAutoGenConfig = () => {
	const stored = localStorage.getItem(STORAGE_KEYS.AUTO_GEN_CONFIG)
	if (stored) {
		try {
			const parsedConfig = JSON.parse(stored)
			autoGenConfig.value = { ...getDefaultPrimaryKeyAutoGenConfig(), ...parsedConfig }
		} catch (error) {
			console.error('加载主键自动生成配置失败:', error)
			autoGenConfig.value = getDefaultPrimaryKeyAutoGenConfig()
		}
	}
}

// 保存主键自动生成配置
const saveAutoGenConfig = () => {
	try {
		localStorage.setItem(STORAGE_KEYS.AUTO_GEN_CONFIG, JSON.stringify(autoGenConfig.value))
		return true
	} catch (error) {
		console.error('保存主键自动生成配置失败:', error)
		return false
	}
}

// 加载主键认证策略配置
const loadAuthStrategyConfig = () => {
	const stored = localStorage.getItem(STORAGE_KEYS.AUTH_STRATEGY_CONFIG)
	if (stored) {
		try {
			const parsedConfig = JSON.parse(stored)
			authStrategyConfig.value = { ...getDefaultPrimaryKeyAuthStrategyConfig(), ...parsedConfig }
		} catch (error) {
			console.error('加载主键认证策略配置失败:', error)
			authStrategyConfig.value = getDefaultPrimaryKeyAuthStrategyConfig()
		}
	}
}

// 保存主键认证策略配置
const saveAuthStrategyConfig = () => {
	try {
		localStorage.setItem(STORAGE_KEYS.AUTH_STRATEGY_CONFIG, JSON.stringify(authStrategyConfig.value))
		return true
	} catch (error) {
		console.error('保存主键认证策略配置失败:', error)
		return false
	}
}

// 加载主键唯一性配置
const loadUniquenessConfig = () => {
	const stored = localStorage.getItem(STORAGE_KEYS.UNIQUENESS_CONFIG)
	if (stored) {
		try {
			const parsedConfig = JSON.parse(stored)
			uniquenessConfig.value = { ...getDefaultPrimaryKeyUniquenessConfig(), ...parsedConfig }
		} catch (error) {
			console.error('解析主键唯一性配置失败:', error)
			uniquenessConfig.value = getDefaultPrimaryKeyUniquenessConfig()
		}
	}
}

// 保存主键唯一性配置
const saveUniquenessConfig = () => {
	try {
		localStorage.setItem(STORAGE_KEYS.UNIQUENESS_CONFIG, JSON.stringify(uniquenessConfig.value))
		return true
	} catch (error) {
		console.error('保存主键唯一性配置失败:', error)
		return false
	}
}

// 加载主键更改权限配置
const loadChangePermissionConfig = () => {
	const stored = localStorage.getItem(STORAGE_KEYS.CHANGE_PERMISSION_CONFIG)
	if (stored) {
		try {
			const parsedConfig = JSON.parse(stored)
			changePermissionConfig.value = { ...getDefaultPrimaryKeyChangePermissionConfig(), ...parsedConfig }
		} catch (error) {
			console.error('加载主键更改权限配置失败:', error)
			changePermissionConfig.value = getDefaultPrimaryKeyChangePermissionConfig()
		}
	}
}

// 保存主键更改权限配置
const saveChangePermissionConfig = () => {
	try {
		localStorage.setItem(STORAGE_KEYS.CHANGE_PERMISSION_CONFIG, JSON.stringify(changePermissionConfig.value))
		return true
	} catch (error) {
		console.error('保存主键更改权限配置失败:', error)
		return false
	}
}

// 加载主键缓存配置
const loadCacheConfig = (primaryKeyId: string) => {
	const stored = localStorage.getItem(`${CACHE_CONFIG_KEY}_${primaryKeyId}`)
	if (stored) {
		try {
			const parsedConfig = JSON.parse(stored)
			currentCacheConfig.value = { ...currentCacheConfig.value, ...parsedConfig }
		} catch (error) {
			console.error('加载主键缓存配置失败:', error)
			currentCacheConfig.value = {
				enabled: false,
				expireTime: 300
			}
		}
	} else {
		currentCacheConfig.value = {
			enabled: false,
			expireTime: 300
		}
	}
}

// 保存主键缓存配置
const saveCacheConfig = (primaryKeyId: string) => {
	try {
		localStorage.setItem(`${CACHE_CONFIG_KEY}_${primaryKeyId}`, JSON.stringify(currentCacheConfig.value))
		return true
	} catch (error) {
		console.error('保存主键缓存配置失败:', error)
		return false
	}
}

// 加载主键关联数据同步配置
const loadDataSyncConfig = (primaryKeyId: string) => {
	const stored = localStorage.getItem(`${DATA_SYNC_CONFIG_KEY}_${primaryKeyId}`)
	if (stored) {
		try {
			const parsedConfig = JSON.parse(stored)
			currentDataSyncConfig.value = { ...currentDataSyncConfig.value, ...parsedConfig }
		} catch (error) {
			console.error('加载主键关联数据同步配置失败:', error)
			currentDataSyncConfig.value = {
				syncField: ''
			}
		}
	} else {
		currentDataSyncConfig.value = {
			syncField: ''
		}
	}
}

// 保存主键关联数据同步配置
const saveDataSyncConfig = (primaryKeyId: string) => {
	try {
		localStorage.setItem(`${DATA_SYNC_CONFIG_KEY}_${primaryKeyId}`, JSON.stringify(currentDataSyncConfig.value))
		return true
	} catch (error) {
		console.error('保存主键关联数据同步配置失败:', error)
		return false
	}
}

// 主键名称认证策略验证
const validatePrimaryKeyName = (name: string): { isValid: boolean; errorMessage?: string } => {
	// 如果认证策略未启用，直接通过验证
	if (!authStrategyConfig.value.enabled) {
		return { isValid: true }
	}

	// 跳过空格验证 - 使用原始名称检查
	if (!authStrategyConfig.value.skipWhitespace && /\s/.test(name)) {
		return {
			isValid: false,
			errorMessage: '主键名称不能包含空格字符'
		}
	}

	// 跳过特殊字符验证 - 使用原始名称检查
	if (!authStrategyConfig.value.skipSpecialChars && /[!@#$%^&*(),.?":{}|<>]/.test(name)) {
		return {
			isValid: false,
			errorMessage: '主键名称不能包含特殊字符（如：!@#$%^&*(),.?":{}|<>）'
		}
	}

	return { isValid: true }
}

// 排序相关
const sortField = ref<string>('')
const sortOrder = ref<'asc' | 'desc' | null>(null)

// 检查主键是否被锁定
const isKeyLocked = (row: PrimaryKeyData): boolean => {
	if (!row.lockConfig || !row.lockConfig.isLocked) {
		return false
	}

	const now = new Date()
	const lockEndTime = new Date(row.lockConfig.lockEndTime)

	return lockEndTime > now
}

// 自动解锁机制
let unlockTimer: NodeJS.Timeout | null = null

// 检查并自动解锁过期的主键
const checkAndUnlockExpiredKeys = () => {
	const now = new Date()
	let hasUnlocked = false

	primaryKeys.value.forEach(key => {
		if (key.lockConfig && key.lockConfig.isLocked) {
			const lockEndTime = new Date(key.lockConfig.lockEndTime)
			if (lockEndTime <= now) {
				// 自动解锁
				const updatedData = {
					...key,
					lockConfig: {
						...key.lockConfig,
						isLocked: false
					},
					updateTime: new Date().toISOString()
				}
				updatePrimaryKey(key.id, updatedData)
				hasUnlocked = true
			}
		}
	})

	if (hasUnlocked) {
		console.log('自动解锁了过期的主键')
	}
}

// 启动自动解锁定时器
const startUnlockTimer = () => {
	// 每30秒检查一次
	unlockTimer = setInterval(checkAndUnlockExpiredKeys, 30000)
}

// 停止自动解锁定时器
const stopUnlockTimer = () => {
	if (unlockTimer) {
		clearInterval(unlockTimer)
		unlockTimer = null
	}
}

// 加载历史记录列配置
const loadHistoryColumnConfig = () => {
	const stored = localStorage.getItem('PRIMARY_KEYS_HISTORY_COLUMN_CONFIG')
	if (stored) {
		try {
			historyColumnConfig.value = { ...getDefaultHistoryColumnConfig(), ...JSON.parse(stored) }
		} catch (error) {
			console.error('加载历史记录列配置失败:', error)
			historyColumnConfig.value = getDefaultHistoryColumnConfig()
		}
	}
}

// 保存历史记录列配置
const saveHistoryColumnConfig = () => {
	try {
		localStorage.setItem('PRIMARY_KEYS_HISTORY_COLUMN_CONFIG', JSON.stringify(historyColumnConfig.value))
		return true
	} catch (error) {
		console.error('保存历史记录列配置失败:', error)
		return false
	}
}

// 加载备份策略配置
const loadBackupStrategyConfig = () => {
	const stored = localStorage.getItem('PRIMARY_KEYS_BACKUP_STRATEGY_CONFIG')
	if (stored) {
		try {
			backupStrategyConfig.value = { ...getDefaultBackupStrategyConfig(), ...JSON.parse(stored) }
		} catch (error) {
			console.error('加载备份策略配置失败:', error)
			backupStrategyConfig.value = getDefaultBackupStrategyConfig()
		}
	}
}

// 保存备份策略配置
const saveBackupStrategyConfig = () => {
	try {
		localStorage.setItem('PRIMARY_KEYS_BACKUP_STRATEGY_CONFIG', JSON.stringify(backupStrategyConfig.value))
		return true
	} catch (error) {
		console.error('保存备份策略配置失败:', error)
		return false
	}
}

// 表格相关
const tableRef = ref()
const tableHeight = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 分页
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})



// 表单数据
const addForm = ref<Partial<PrimaryKeyData>>({
	name: '',
	dataType: '',
	minLength: 1,
	maxLength: 10,
	description: '',
	version: '',
	showOnMobile: false,
	isDefaultValue: true,
	updateNotification: false,
	encryptionLevel: '',
	encryptionContent: ''
})

const batchUpdateForm = ref<BatchUpdateData>({
	dataType: '',
	minLength: 0,
	maxLength: 0
})

// 新的批量编辑表单数据
const batchEditForm = ref<BatchUpdateData>({
	dataType: '',
	minLength: 1,
	maxLength: 1
})

const cleanupRuleForm = ref<CleanupRuleData>({
	cleanupTime: 30,
	cleanupStrategy: 'archive'
})

const storageLocationForm = ref<StorageLocationConfig>({
	storageLocation: ''
})

// 当前操作的行数据
const currentRow = ref<PrimaryKeyData | null>(null)

// 计算属性
const filteredPrimaryKeys = computed(() => {
	let filtered = primaryKeys.value

	// 根据主键名称筛选
	if (searchForm.value.keyword) {
		filtered = filtered.filter(item =>
			item.name.toLowerCase().includes(searchForm.value.keyword.toLowerCase())
		)
	}

	// 根据数据类型筛选
	if (searchForm.value.dataType) {
		filtered = filtered.filter(item => item.dataType === searchForm.value.dataType)
	}

	// 根据最小长度筛选
	if (searchForm.value.minLength !== '') {
		const minLength = parseInt(searchForm.value.minLength)
		if (!isNaN(minLength)) {
			filtered = filtered.filter(item =>
				item.minLength !== undefined && item.minLength >= minLength
			)
		}
	}

	// 根据最大长度筛选
	if (searchForm.value.maxLength !== '') {
		const maxLength = parseInt(searchForm.value.maxLength)
		if (!isNaN(maxLength)) {
			filtered = filtered.filter(item =>
				item.maxLength !== undefined && item.maxLength <= maxLength
			)
		}
	}

	// 兼容旧的searchKeyword搜索
	if (searchKeyword.value) {
		filtered = filtered.filter(item =>
			item.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
		)
	}

	return filtered
})

const totalCount = computed(() => filteredPrimaryKeys.value.length)

const paginatedData = computed(() => {
	const start = (currentPage.value - 1) * pageSize.value
	const end = start + pageSize.value
	return filteredPrimaryKeys.value.slice(start, end)
})

// 表格列定义 - TableV2 格式
const columns = [
	{ prop: 'name', label: '主键名称', width: 150, sortable: true, slot: 'name' },
	{ prop: 'dataType', label: '数据类型', width: 120, slot: 'dataType' },
	{ prop: 'minLength', label: '最小长度', width: 100, sortable: true },
	{ prop: 'maxLength', label: '最大长度', width: 100 },
	{ prop: 'description', label: '说明', width: 150 },
	{ prop: 'version', label: '主键版本', width: 100 },
	{ prop: 'showOnMobile', label: '是否展示移动端', width: 120, slot: 'showOnMobile' },
	{ prop: 'isDefaultValue', label: '主键默认值', width: 100, slot: 'isDefaultValue' }
]

// 操作按钮定义 - TableV2 格式
const buttons = [
	{ label: '详情', code: 'detail' },
	{ label: '修改', type: 'primary', code: 'edit' },
	{ label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除吗?' },
	{ label: '历史记录', code: 'history', more: true },// more: true 表示在更多下拉按钮里面
	{ label: '主键动态调整', code: 'dynamicAdjust', more: true },
	{ label: '主键自动清理', code: 'cleanupRule', more: true },
	{ label: '主键存储位置', code: 'storageLocation', more: true },
	{ label: '复合主键配置', code: 'compositeKeyConfig', more: true },
	{ label: '主键索引配置', code: 'indexConfig', more: true },
	{ label: '主键锁定', code: 'lockConfig', more: true },
	{ label: '主键与外键关联', code: 'foreignKeyRelation', more: true },
	{ label: '主键动态扩展', code: 'dynamicExtension', more: true },
	{ label: '主键缓存', code: 'cacheConfig', more: true },
	{ label: '主键关联数据同步', code: 'dataSyncConfig', more: true },
	{ label: '主键可视化标识', code: 'visualIdentifier', more: true },
	{ label: '加密设置', code: 'encryptionConfig', more: true }
]

// Block组件相关函数
const expendSearch = (expanded: boolean) => {
	showSearchArea.value = expanded
}

// 块高度变化事件时修改表格高度
const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
	if (type == 'page') {
		pagination.page = val
		currentPage.value = val
	} else {
		pagination.size = val
		pageSize.value = val
		currentPage.value = 1
		pagination.page = 1
	}
}

const onSearch = () => {
	// 处理搜索表单提交
	searchKeyword.value = searchForm.value.keyword
	currentPage.value = 1
	ElMessage.success('搜索完成')
}

const handleAddPrimaryKey = () => {
	// 加载最新的认证策略配置
	loadAuthStrategyConfig()

	isEditMode.value = false
	showAddDialog.value = true
	addForm.value = {
		name: '',
		dataType: '',
		minLength: 1,
		maxLength: 10,
		description: '',
		version: '',
		showOnMobile: false,
		isDefaultValue: true,
		updateNotification: false,
		encryptionLevel: '',
		encryptionContent: '',
		cleanupRule: {
			cleanupTime: 30,
			cleanupStrategy: 'archive'
		},
		storageLocation: {
			storageLocation: ''
		}
	}
}

const handleBatchImport = () => {
	showImportDialog.value = true
}

// 导入相关处理函数
const uploadRef = ref()
const selectedFile = ref<File | null>(null)

const handleConfirmImport = async () => {
	if (!selectedFile.value) {
		ElMessage.error('请先选择要导入的文件')
		return
	}

	// 文件格式验证
	const fileName = selectedFile.value.name
	const fileExtension = fileName.substring(fileName.lastIndexOf('.')).toLowerCase()
	if (!['.xlsx', '.xls'].includes(fileExtension)) {
		ElMessage.error('文件格式不正确，请选择 .xlsx 或 .xls 格式的文件')
		return
	}

	// 文件大小验证（50MB限制）
	const maxSize = 50 * 1024 * 1024 // 50MB
	if (selectedFile.value.size > maxSize) {
		ElMessage.error('文件大小不能超过50MB')
		return
	}

	try {
		const importedData = await importFromExcel(selectedFile.value)

		// 数据完整性校验
		const validationErrors = validateImportData(importedData)
		if (validationErrors.length > 0) {
			ElMessage.error(`数据校验失败：${validationErrors.join('; ')}`)
			return
		}

		ElMessageBox.confirm(
			`成功解析 ${importedData.length} 条数据，是否要替换当前所有数据？`,
			'确认导入',
			{
				confirmButtonText: '替换所有数据',
				cancelButtonText: '追加到现有数据',
				distinguishCancelAndClose: true,
				type: 'warning'
			}
		).then(() => {
			replaceAllData(importedData)
			showImportDialog.value = false
			selectedFile.value = null
			uploadRef.value?.clearFiles()
		}).catch((action) => {
			if (action === 'cancel') {
				appendData(importedData)
				showImportDialog.value = false
				selectedFile.value = null
				uploadRef.value?.clearFiles()
			}
		})
	} catch (error) {
		// 错误已在 composable 中处理
	}
}

// 数据校验函数 - 已移至usePrimaryKeyExcel composable中
const validateImportData = (data: PrimaryKeyData[]): string[] => {
	// 这个函数现在主要用于额外的业务逻辑校验
	// 基础的必填字段校验已经在Excel导入时处理
	const errors: string[] = []

	data.forEach((item, index) => {
		const rowNumber = index + 1

		// 检查主键名称重复
		const duplicateNames = data.filter(d => d.name === item.name)
		if (duplicateNames.length > 1) {
			errors.push(`第${rowNumber}行：主键名称"${item.name}"重复`)
		}
	})

	return errors
}

const handleFileChange = (file: any) => {
	selectedFile.value = file.raw
}

const handleExceed = () => {
	ElMessage.warning('只能选择一个文件')
}

const handleBatchExport = async () => {
	// 如果有选中的行，导出选中的数据；否则导出全部数据
	const dataToExport = selectedRows.value.length > 0 ? selectedRows.value : primaryKeys.value

	if (dataToExport.length === 0) {
		ElMessage.warning('没有数据可以导出')
		return
	}

	const filename = selectedRows.value.length > 0
		? `主键定义数据_已选择${selectedRows.value.length}条`
		: '主键定义数据_全部'

	await exportToExcel(dataToExport, filename)

	// 导出成功后的提示
	const message = selectedRows.value.length > 0
		? `成功导出 ${selectedRows.value.length} 条选中数据`
		: `成功导出全部 ${primaryKeys.value.length} 条数据`
	ElMessage.success(message)
}

const handleDownloadTemplate = async () => {
	await generateTemplate()
}

const handleCleanupRule = (row: PrimaryKeyData) => {
	currentRow.value = row
	showCleanupRuleDialog.value = true
	// 使用当前行的清理规则，如果没有则使用默认值
	cleanupRuleForm.value = row.cleanupRule ? { ...row.cleanupRule } : {
		cleanupTime: 30,
		cleanupStrategy: 'archive'
	}
}

const handleStorageLocation = (row: PrimaryKeyData) => {
	currentRow.value = row
	showStorageLocationDialog.value = true
	// 使用当前行的存储位置配置，如果没有则使用默认值
	storageLocationForm.value = row.storageLocation ? { ...row.storageLocation } : {
		storageLocation: ''
	}
}



// 详情查看
const handleDetailPrimaryKey = (row: PrimaryKeyData) => {
	isDetailMode.value = true
	showDetailDialog.value = true
	addForm.value = { ...row }
}

// 编辑主键
const handleEditPrimaryKey = (row: PrimaryKeyData) => {
	// 加载最新的认证策略配置
	loadAuthStrategyConfig()

	isEditMode.value = true
	showAddDialog.value = true
	addForm.value = { ...row }
}

// 表格操作点击事件, row 当前行数据
const onTableClickButton = ({row, btn}: any) => {
	if (btn.code === 'detail') {
		handleDetailPrimaryKey(row)
	} else if (btn.code === 'edit') {
		handleEditPrimaryKey(row)
	} else if (btn.code === 'delete') {
		handleDeletePrimaryKey(row)
	} else if (btn.code === 'compositeKeyConfig') {
		handleCompositeKeyConfig(row)
	} else if (btn.code === 'indexConfig') {
		handleIndexConfig(row)
	} else if (btn.code === 'lockConfig') {
		handleLockConfig(row)
	} else if (btn.code === 'history') {
		handleViewHistory(row)
	} else if (btn.code === 'dynamicAdjust') {
		handleDynamicAdjust(row)
	} else if (btn.code === 'cleanupRule') {
		handleCleanupRule(row)
	} else if (btn.code === 'storageLocation') {
		handleStorageLocation(row)
	} else if (btn.code === 'foreignKeyRelation') {
		handleForeignKeyRelation(row)
	} else if (btn.code === 'dynamicExtension') {
		handleDynamicExtensionConfig(row)
	} else if (btn.code === 'cacheConfig') {
		handleCacheConfig(row)
	} else if (btn.code === 'dataSyncConfig') {
		handleDataSyncConfig(row)
	} else if (btn.code === 'visualIdentifier') {
		handleVisualIdentifierConfig(row)
	} else if (btn.code === 'encryptionConfig') {
		handleEncryptionConfig(row)
	}
}

// 处理页面级下拉菜单命令
const handleDropdownCommand = (command: string) => {
	if (command === 'viewAllHistory') {
		handleViewAllHistory()
	} else if (command === 'primaryKeyValidation') {
		handlePrimaryKeyValidation()
	} else if (command === 'historyColumnConfig') {
		handleHistoryColumnConfig()
	} else if (command === 'backupStrategy') {
		handleBackupStrategy()
	} else if (command === 'primaryKeyStats') {
		handlePrimaryKeyStats()
	} else if (command === 'autoGenConfig') {
		handleAutoGenConfig()
	} else if (command === 'authStrategy') {
		handleAuthStrategy()
	} else if (command === 'uniquenessConfig') {
		handleUniquenessConfig()
	} else if (command === 'changePermissionConfig') {
		handleChangePermissionConfig()
	} else if (command === 'performanceMonitor') {
		handlePerformanceMonitor()
	}
}

// 主键性能监控分析处理函数
const handlePerformanceMonitor = () => {
	router.push('/primary-key-performance-monitor')
}

// 主键统计处理函数
const handlePrimaryKeyStats = () => {
	primaryKeyStatsData.value = generatePrimaryKeyStats()
	showPrimaryKeyStatsDialog.value = true
}

// 主键自动生成配置处理函数
const handleAutoGenConfig = () => {
	loadAutoGenConfig()
	// 保存原始配置用于取消操作
	originalAutoGenConfig.value = JSON.parse(JSON.stringify(autoGenConfig.value))
	showAutoGenConfigDialog.value = true
}

// 确认主键自动生成配置
const handleConfirmAutoGenConfig = () => {
	// 表单验证
	if (!autoGenConfig.value.strategy) {
		ElMessage.error('请选择主键策略')
		return
	}

	if (autoGenConfig.value.strategy === PrimaryKeyStrategy.AUTO_INCREMENT) {
		if (!autoGenConfig.value.autoIncrementConfig?.startValue?.trim()) {
			ElMessage.error('请输入起始值')
			return
		}
		if (!autoGenConfig.value.autoIncrementConfig?.stepSize || autoGenConfig.value.autoIncrementConfig.stepSize <= 0) {
			ElMessage.error('请输入有效的步长（大于0）')
			return
		}
	} else if (autoGenConfig.value.strategy === PrimaryKeyStrategy.UUID) {
		if (!autoGenConfig.value.uuidConfig?.version) {
			ElMessage.error('请选择UUID版本')
			return
		}
		// 检查UUID版本格式是否至少选择了一个选项
		if (!autoGenConfig.value.uuidConfig?.uppercase && !autoGenConfig.value.uuidConfig?.withHyphen) {
			ElMessage.error('请至少选择一种UUID版本格式')
			return
		}
	}

	// 保存配置
	if (saveAutoGenConfig()) {
		ElMessage.success('主键自动生成配置保存成功')
		showAutoGenConfigDialog.value = false
	} else {
		ElMessage.error('保存失败，请重试')
	}
}

// 取消主键自动生成配置
const handleCancelAutoGenConfig = () => {
	// 恢复原始配置
	autoGenConfig.value = JSON.parse(JSON.stringify(originalAutoGenConfig.value))
	showAutoGenConfigDialog.value = false
}

// 主键认证策略处理函数
const handleAuthStrategy = () => {
	loadAuthStrategyConfig()
	// 保存原始配置用于取消操作
	originalAuthStrategyConfig.value = JSON.parse(JSON.stringify(authStrategyConfig.value))
	showAuthStrategyDialog.value = true
}

// 主键唯一性配置处理函数
const handleUniquenessConfig = () => {
	loadUniquenessConfig()
	showUniquenessConfigDialog.value = true
}

// 确认主键认证策略配置
const handleConfirmAuthStrategy = () => {
	// 保存配置
	if (saveAuthStrategyConfig()) {
		ElMessage.success('主键认证策略配置保存成功')
		showAuthStrategyDialog.value = false
	} else {
		ElMessage.error('保存失败，请重试')
	}
}

// 取消主键认证策略配置
const handleCancelAuthStrategy = () => {
	// 恢复原始配置
	authStrategyConfig.value = JSON.parse(JSON.stringify(originalAuthStrategyConfig.value))
	showAuthStrategyDialog.value = false
}

// 确认主键唯一性配置
const handleConfirmUniquenessConfig = () => {
	// 保存配置
	if (saveUniquenessConfig()) {
		ElMessage.success('主键唯一性配置保存成功')
		showUniquenessConfigDialog.value = false
	} else {
		ElMessage.error('保存失败，请重试')
	}
}

// 取消主键唯一性配置
const handleCancelUniquenessConfig = () => {
	// 重新加载配置，恢复原始状态
	loadUniquenessConfig()
	showUniquenessConfigDialog.value = false
}

// 主键更改权限配置处理函数
const handleChangePermissionConfig = () => {
	loadChangePermissionConfig()
	// 保存原始配置用于取消操作
	originalChangePermissionConfig.value = JSON.parse(JSON.stringify(changePermissionConfig.value))
	showChangePermissionDialog.value = true
}

// 确认主键更改权限配置
const handleConfirmChangePermissionConfig = () => {
	// 保存配置
	if (saveChangePermissionConfig()) {
		ElMessage.success('主键更改权限配置保存成功')
		showChangePermissionDialog.value = false
	} else {
		ElMessage.error('保存失败，请重试')
	}
}

// 取消主键更改权限配置
const handleCancelChangePermissionConfig = () => {
	// 恢复原始配置
	changePermissionConfig.value = JSON.parse(JSON.stringify(originalChangePermissionConfig.value))
	showChangePermissionDialog.value = false
}

// 主键变更记录配置
const handleHistoryColumnConfig = () => {
	showHistoryColumnConfigDialog.value = true
}

// 主键备份策略配置
const handleBackupStrategy = () => {
	loadBackupStrategyConfig()
	showBackupStrategyDialog.value = true
}

// 复合主键配置
const handleCompositeKeyConfig = (row: PrimaryKeyData) => {
	currentRow.value = row
	// 加载当前行的复合主键配置，如果没有则使用默认值
	currentCompositeKeyConfig.value = row.compositeKeyConfig ? { ...row.compositeKeyConfig } : getDefaultCompositeKeyConfig()
	showCompositeKeyConfigDialog.value = true
}

// 主键索引配置
const handleIndexConfig = (row: PrimaryKeyData) => {
	currentRow.value = row
	// 加载当前行的索引配置，如果没有则使用默认值
	currentIndexConfig.value = row.indexConfig ? { ...row.indexConfig } : getDefaultIndexConfig()
	showIndexConfigDialog.value = true
}

// 主键锁定配置
const handleLockConfig = (row: PrimaryKeyData) => {
	currentRow.value = row
	// 加载当前行的锁定配置，如果没有则使用默认值
	currentLockConfig.value = row.lockConfig ? { ...row.lockConfig } : getDefaultLockConfig()

	// 如果主键当前被锁定，计算并显示剩余时间
	if (row.lockConfig && row.lockConfig.isLocked && isKeyLocked(row)) {
		const now = new Date()
		const lockEndTime = new Date(row.lockConfig.lockEndTime)
		const remainingMs = lockEndTime.getTime() - now.getTime()

		if (remainingMs > 0) {
			const remainingSeconds = Math.ceil(remainingMs / 1000)
			const hours = Math.floor(remainingSeconds / 3600)
			const minutes = Math.floor((remainingSeconds % 3600) / 60)
			const seconds = remainingSeconds % 60

			lockTimeForm.value = {
				hours: hours,
				minutes: minutes,
				seconds: seconds
			}
		} else {
			// 如果时间已过期，重置为默认值
			lockTimeForm.value = {
				hours: 0,
				minutes: 0,
				seconds: 0
			}
		}
	} else {
		// 如果没有锁定或已解锁，重置时间表单
		lockTimeForm.value = {
			hours: 0,
			minutes: 0,
			seconds: 0
		}
	}

	showLockConfigDialog.value = true
}

// 主键与外键关联配置
const handleForeignKeyRelation = (row: PrimaryKeyData) => {
	currentRow.value = row
	// 加载当前行的外键关联配置，如果没有则使用默认值
	currentForeignKeyRelationConfig.value = row.foreignKeyRelationConfig ? { ...row.foreignKeyRelationConfig } : getDefaultForeignKeyRelationConfig()
	showForeignKeyRelationDialog.value = true
}

// 主键动态扩展配置
const handleDynamicExtensionConfig = (row: PrimaryKeyData) => {
	currentRow.value = row
	// 加载当前行的动态扩展配置，如果没有则使用默认值
	currentDynamicExtensionConfig.value = row.dynamicExtensionConfig ? { ...row.dynamicExtensionConfig } : getDefaultDynamicExtensionConfig()
	showDynamicExtensionDialog.value = true
}

// 确认历史记录列配置
const handleConfirmHistoryColumnConfig = () => {
	if (saveHistoryColumnConfig()) {
		ElMessage.success('历史记录列配置保存成功')
		showHistoryColumnConfigDialog.value = false
	} else {
		ElMessage.error('保存失败，请重试')
	}
}

// 全选/取消全选历史记录列
const handleSelectAllHistoryColumns = (selectAll: boolean) => {
	Object.keys(historyColumnConfig.value).forEach(key => {
		historyColumnConfig.value[key as keyof HistoryColumnConfig] = selectAll
	})
}

// 查看历史记录 - 全局
const handleViewAllHistory = () => {
	historyPrimaryKeyId.value = ''
	historyDialogTitle.value = '全部历史记录'
	showHistoryDialog.value = true
}

// 查看历史记录 - 单条
const handleViewHistory = (row: PrimaryKeyData) => {
	historyPrimaryKeyId.value = row.id!
	historyDialogTitle.value = `主键【${row.name}】的历史记录`
	showHistoryDialog.value = true
}

// 主键函数验证
const handlePrimaryKeyValidation = () => {
	showPrimaryKeyValidationDialog.value = true
}

// 确认主键函数验证
const handleConfirmPrimaryKeyValidation = () => {
	ElMessage.success('主键函数验证操作成功')
	showPrimaryKeyValidationDialog.value = false
}

// 确认备份策略配置
const handleConfirmBackupStrategy = () => {
	if (saveBackupStrategyConfig()) {
		ElMessage.success('备份策略配置保存成功')
		showBackupStrategyDialog.value = false
	} else {
		ElMessage.error('保存失败，请重试')
	}
}

// 确认复合主键配置
const handleConfirmCompositeKeyConfig = () => {
	try {
		if (!currentRow.value) {
			ElMessage.error('未找到当前操作的主键数据')
			return
		}

		// 更新当前行的复合主键配置
		const updatedData = {
			...currentRow.value,
			compositeKeyConfig: { ...currentCompositeKeyConfig.value },
			updateTime: new Date().toISOString()
		}

		updatePrimaryKey(currentRow.value.id, updatedData)
		ElMessage.success('复合主键配置保存成功')
		showCompositeKeyConfigDialog.value = false
		currentRow.value = null
	} catch (error) {
		console.error('保存复合主键配置失败:', error)
		ElMessage.error('保存失败，请重试')
	}
}

// 确认主键索引配置
const handleConfirmIndexConfig = () => {
	try {
		if (!currentRow.value) {
			ElMessage.error('未找到当前操作的主键数据')
			return
		}

		// 验证必填字段
		if (!currentIndexConfig.value.indexField || currentIndexConfig.value.indexField.trim() === '') {
			ElMessage.error('请选择主键索引规则配置')
			return
		}

		// 更新当前行的索引配置
		const updatedData = {
			...currentRow.value,
			indexConfig: { ...currentIndexConfig.value },
			updateTime: new Date().toISOString()
		}

		updatePrimaryKey(currentRow.value.id, updatedData)
		ElMessage.success('主键索引配置保存成功')
		showIndexConfigDialog.value = false
		currentRow.value = null
	} catch (error) {
		console.error('保存主键索引配置失败:', error)
		ElMessage.error('保存失败，请重试')
	}
}

// 立即解锁主键
const handleUnlockKey = () => {
	try {
		if (!currentRow.value) {
			ElMessage.error('未找到当前操作的主键数据')
			return
		}

		// 更新当前行的锁定配置为解锁状态
		const updatedData = {
			...currentRow.value,
			lockConfig: {
				isLocked: false,
				lockEndTime: currentRow.value.lockConfig?.lockEndTime || '',
				lockReason: currentRow.value.lockConfig?.lockReason || ''
			},
			updateTime: new Date().toISOString()
		}

		updatePrimaryKey(currentRow.value.id, updatedData)
		ElMessage.success('主键已成功解锁')
		showLockConfigDialog.value = false
		currentRow.value = null
	} catch (error) {
		console.error('解锁主键失败:', error)
		ElMessage.error('解锁失败，请重试')
	}
}

// 确认主键锁定配置
const handleConfirmLockConfig = () => {
	try {
		if (!currentRow.value) {
			ElMessage.error('未找到当前操作的主键数据')
			return
		}

		// 验证时间输入
		const { hours, minutes, seconds } = lockTimeForm.value
		if (hours === 0 && minutes === 0 && seconds === 0) {
			ElMessage.error('请设置锁定时间')
			return
		}

		// 验证时间范围
		if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59 || seconds < 0 || seconds > 59) {
			ElMessage.error('请输入有效的时间范围（时：0-23，分：0-59，秒：0-59）')
			return
		}

		// 计算锁定结束时间
		const now = new Date()
		const lockEndTime = new Date(now.getTime() + (hours * 3600 + minutes * 60 + seconds) * 1000)

		// 验证不能设置过去的时间
		if (lockEndTime <= now) {
			ElMessage.error('锁定时间不能为过去的时间')
			return
		}

		// 更新当前行的锁定配置
		const updatedData = {
			...currentRow.value,
			lockConfig: {
				isLocked: true,
				lockEndTime: lockEndTime.toISOString(),
				lockReason: currentLockConfig.value.lockReason || ''
			},
			updateTime: new Date().toISOString()
		}

		updatePrimaryKey(currentRow.value.id, updatedData)
		ElMessage.success('主键锁定配置保存成功')
		showLockConfigDialog.value = false
		currentRow.value = null
	} catch (error) {
		console.error('保存主键锁定配置失败:', error)
		ElMessage.error('保存失败，请重试')
	}
}

// 确认主键与外键关联配置
const handleConfirmForeignKeyRelation = () => {
	try {
		if (!currentRow.value) {
			ElMessage.error('未找到当前操作的主键数据')
			return
		}

		// 验证必填字段
		if (!currentForeignKeyRelationConfig.value.masterTable || currentForeignKeyRelationConfig.value.masterTable.trim() === '') {
			ElMessage.error('请选择主表字段')
			return
		}

		if (!currentForeignKeyRelationConfig.value.slaveTable || currentForeignKeyRelationConfig.value.slaveTable.trim() === '') {
			ElMessage.error('请选择从表字段')
			return
		}

		// 验证主表字段和从表字段不能相同
		if (currentForeignKeyRelationConfig.value.masterTable === currentForeignKeyRelationConfig.value.slaveTable) {
			ElMessage.error('主表字段和从表字段不能相同')
			return
		}

		// 更新当前行的外键关联配置
		const updatedData = {
			...currentRow.value,
			foreignKeyRelationConfig: { ...currentForeignKeyRelationConfig.value },
			updateTime: new Date().toISOString()
		}

		updatePrimaryKey(currentRow.value.id, updatedData)
		ElMessage.success('主键与外键关联配置保存成功')
		showForeignKeyRelationDialog.value = false
		currentRow.value = null
	} catch (error) {
		console.error('保存主键与外键关联配置失败:', error)
		ElMessage.error('保存失败，请重试')
	}
}

// 确认主键动态扩展配置
const handleConfirmDynamicExtensionConfig = () => {
	try {
		if (!currentRow.value) {
			ElMessage.error('未找到当前操作的主键数据')
			return
		}

		// 验证必填字段
		if (!currentDynamicExtensionConfig.value.extensionField || currentDynamicExtensionConfig.value.extensionField.trim() === '') {
			ElMessage.error('请选择动态扩展字段')
			return
		}

		if (!currentDynamicExtensionConfig.value.extensionType || currentDynamicExtensionConfig.value.extensionType.trim() === '') {
			ElMessage.error('请选择扩展类型')
			return
		}

		// 更新当前行的动态扩展配置
		const updatedData = {
			...currentRow.value,
			dynamicExtensionConfig: { ...currentDynamicExtensionConfig.value },
			updateTime: new Date().toISOString()
		}

		updatePrimaryKey(currentRow.value.id, updatedData)
		ElMessage.success('主键动态扩展配置保存成功')
		showDynamicExtensionDialog.value = false
		currentRow.value = null
	} catch (error) {
		console.error('保存主键动态扩展配置失败:', error)
		ElMessage.error('保存失败，请重试')
	}
}

// 主键缓存配置
const handleCacheConfig = (row: PrimaryKeyData) => {
	currentRow.value = row
	// 加载当前行的缓存配置
	loadCacheConfig(row.id!)
	showCacheConfigDialog.value = true
}

// 确认主键缓存配置
const handleConfirmCacheConfig = () => {
	try {
		if (!currentRow.value) {
			ElMessage.error('未找到当前操作的主键数据')
			return
		}

		// 验证缓存有效期
		if (currentCacheConfig.value.enabled && (!currentCacheConfig.value.expireTime || currentCacheConfig.value.expireTime <= 0)) {
			ElMessage.error('请输入有效的缓存有效期')
			return
		}

		// 保存配置到localStorage
		if (saveCacheConfig(currentRow.value.id!)) {
			ElMessage.success('主键缓存配置保存成功')
			showCacheConfigDialog.value = false
			currentRow.value = null
		} else {
			ElMessage.error('保存失败，请重试')
		}
	} catch (error) {
		console.error('保存主键缓存配置失败:', error)
		ElMessage.error('保存失败，请重试')
	}
}

// 主键关联数据同步配置
const handleDataSyncConfig = (row: PrimaryKeyData) => {
	currentRow.value = row
	// 加载当前行的关联数据同步配置
	loadDataSyncConfig(row.id!)
	showDataSyncConfigDialog.value = true
}

// 确认主键关联数据同步配置
const handleConfirmDataSyncConfig = () => {
	try {
		if (!currentRow.value) {
			ElMessage.error('未找到当前操作的主键数据')
			return
		}

		// 验证必填字段
		if (!currentDataSyncConfig.value.syncField) {
			ElMessage.error('请选择关联数据同步字段')
			return
		}

		// 保存配置到localStorage
		if (saveDataSyncConfig(currentRow.value.id!)) {
			ElMessage.success('主键关联数据同步配置保存成功')
			showDataSyncConfigDialog.value = false
			currentRow.value = null
		} else {
			ElMessage.error('保存失败，请重试')
		}
	} catch (error) {
		console.error('保存主键关联数据同步配置失败:', error)
		ElMessage.error('保存失败，请重试')
	}
}

// 主键可视化标识配置
const handleVisualIdentifierConfig = (row: PrimaryKeyData) => {
	currentRow.value = row
	// 加载当前行的可视化标识配置
	loadVisualIdentifierConfig(row.id!)
	showVisualIdentifierDialog.value = true
}

// 主键加密设置配置
const handleEncryptionConfig = (row: PrimaryKeyData) => {
	currentEncryptionRow.value = row
	// 加载该主键的加密配置
	loadEncryptionConfig(row.id!)
	showEncryptionConfigDialog.value = true
}

// 加载主键可视化标识配置
const loadVisualIdentifierConfig = (primaryKeyId: string) => {
	const stored = localStorage.getItem(`${VISUAL_IDENTIFIER_CONFIG_KEY}_${primaryKeyId}`)
	if (stored) {
		try {
			const parsedConfig = JSON.parse(stored)
			currentVisualIdentifierConfig.value = { ...currentVisualIdentifierConfig.value, ...parsedConfig }
		} catch (error) {
			console.error('加载主键可视化标识配置失败:', error)
			currentVisualIdentifierConfig.value = {
				enabled: false,
				color: '#409EFF',
				fontSize: 14
			}
		}
	} else {
		currentVisualIdentifierConfig.value = {
			enabled: false,
			color: '#409EFF',
			fontSize: 14
		}
	}
}

// 保存主键可视化标识配置
const saveVisualIdentifierConfig = (primaryKeyId: string) => {
	try {
		localStorage.setItem(`${VISUAL_IDENTIFIER_CONFIG_KEY}_${primaryKeyId}`, JSON.stringify(currentVisualIdentifierConfig.value))
		// 同时更新响应式数据
		visualIdentifierConfigs.value[primaryKeyId] = { ...currentVisualIdentifierConfig.value }
		return true
	} catch (error) {
		console.error('保存主键可视化标识配置失败:', error)
		return false
	}
}

// 确认主键可视化标识配置
const handleConfirmVisualIdentifierConfig = () => {
	try {
		if (!currentRow.value) {
			ElMessage.error('未找到当前操作的主键数据')
			return
		}

		// 验证字体大小
		if (currentVisualIdentifierConfig.value.enabled && (!currentVisualIdentifierConfig.value.fontSize || currentVisualIdentifierConfig.value.fontSize <= 0)) {
			ElMessage.error('请输入有效的字体大小')
			return
		}

		// 保存配置到localStorage
		if (saveVisualIdentifierConfig(currentRow.value.id!)) {
			ElMessage.success('主键可视化标识配置保存成功')
			showVisualIdentifierDialog.value = false
			currentRow.value = null
		} else {
			ElMessage.error('保存失败，请重试')
		}
	} catch (error) {
		console.error('保存主键可视化标识配置失败:', error)
		ElMessage.error('保存失败，请重试')
	}
}

// 取消主键可视化标识配置
const handleCancelVisualIdentifierConfig = () => {
	showVisualIdentifierDialog.value = false
	currentRow.value = null
}

// 获取主键可视化样式
const getVisualIdentifierStyle = (row: PrimaryKeyData) => {
	// 优先从响应式数据中获取
	const config = visualIdentifierConfigs.value[row.id!]
	if (config && config.enabled) {
		return {
			color: config.color || '#409EFF',
			fontSize: `${config.fontSize || 14}px`,
			fontWeight: 'bold'
		}
	}

	// 如果响应式数据中没有，从localStorage获取
	const stored = localStorage.getItem(`${VISUAL_IDENTIFIER_CONFIG_KEY}_${row.id}`)
	if (stored) {
		try {
			const storedConfig = JSON.parse(stored)
			if (storedConfig.enabled) {
				// 同时更新到响应式数据中
				visualIdentifierConfigs.value[row.id!] = storedConfig
				return {
					color: storedConfig.color || '#409EFF',
					fontSize: `${storedConfig.fontSize || 14}px`,
					fontWeight: 'bold'
				}
			}
		} catch (error) {
			console.error('解析主键可视化标识配置失败:', error)
		}
	}
	return {}
}

// 初始化所有主键的可视化配置
const initializeVisualIdentifierConfigs = () => {
	primaryKeys.value.forEach(key => {
		const stored = localStorage.getItem(`${VISUAL_IDENTIFIER_CONFIG_KEY}_${key.id}`)
		if (stored) {
			try {
				const config = JSON.parse(stored)
				visualIdentifierConfigs.value[key.id!] = config
			} catch (error) {
				console.error('加载主键可视化标识配置失败:', error)
			}
		}
	})
}

// 加载主键加密配置
const loadEncryptionConfig = (primaryKeyId: string) => {
	const stored = localStorage.getItem(`${ENCRYPTION_CONFIG_KEY}_${primaryKeyId}`)
	if (stored) {
		try {
			const config = JSON.parse(stored)
			encryptionConfigs.value[primaryKeyId] = config
			encryptionConfigForm.value = {
				encryptionType: config.encryptionType || '',
				encryptionKey: config.encryptionKey || '',
				description: config.description || ''
			}
		} catch (error) {
			console.error('加载加密配置失败:', error)
		}
	} else {
		// 设置默认值
		encryptionConfigForm.value = {
			encryptionType: '',
			encryptionKey: '',
			description: ''
		}
	}
}

// 保存主键加密配置
const saveEncryptionConfig = (primaryKeyId: string, config: any) => {
	try {
		localStorage.setItem(`${ENCRYPTION_CONFIG_KEY}_${primaryKeyId}`, JSON.stringify(config))
		encryptionConfigs.value[primaryKeyId] = config
		return true
	} catch (error) {
		console.error('保存加密配置失败:', error)
		return false
	}
}

// 获取主键加密配置
const getEncryptionConfig = (primaryKeyId: string) => {
	return encryptionConfigs.value[primaryKeyId] || {
		encryptionType: '',
		encryptionKey: '',
		description: ''
	}
}

// 确认加密配置
const handleConfirmEncryptionConfig = () => {
	if (!currentEncryptionRow.value) return

	// 表单验证
	if (!encryptionConfigForm.value.encryptionType) {
		ElMessage.error('请选择加密类型')
		return
	}

	if (!encryptionConfigForm.value.encryptionKey.trim()) {
		ElMessage.error('请输入加密密钥')
		return
	}

	// 保存配置到localStorage
	const config = {
		encryptionType: encryptionConfigForm.value.encryptionType,
		encryptionKey: encryptionConfigForm.value.encryptionKey,
		description: encryptionConfigForm.value.description
	}

	if (saveEncryptionConfig(currentEncryptionRow.value.id!, config)) {
		ElMessage.success('加密设置保存成功')
		showEncryptionConfigDialog.value = false
		currentEncryptionRow.value = null
	} else {
		ElMessage.error('保存失败，请重试')
	}
}

// 取消加密配置
const handleCancelEncryptionConfig = () => {
	showEncryptionConfigDialog.value = false
	currentEncryptionRow.value = null
	// 重置表单
	encryptionConfigForm.value = {
		encryptionType: '',
		encryptionKey: '',
		description: ''
	}
}

// 初始化所有主键的加密配置
const initializeEncryptionConfigs = () => {
	primaryKeys.value.forEach(key => {
		const stored = localStorage.getItem(`${ENCRYPTION_CONFIG_KEY}_${key.id}`)
		if (stored) {
			try {
				const config = JSON.parse(stored)
				encryptionConfigs.value[key.id!] = config
			} catch (error) {
				console.error('加载主键加密配置失败:', error)
			}
		}
	})
}

// 主键动态调整
const handleDynamicAdjust = (row: PrimaryKeyData) => {
	currentRow.value = row
	// 预填充当前主键名称
	dynamicAdjustForm.value.newName = row.name
	showDynamicAdjustDialog.value = true
}

// 确认主键动态调整
const handleConfirmDynamicAdjust = () => {
	try {
		if (!currentRow.value) {
			ElMessage.error('未找到当前操作的主键数据')
			return
		}

		// 验证新主键名称不能为空
		if (!dynamicAdjustForm.value.newName?.trim()) {
			ElMessage.error('请输入新主键名称')
			return
		}

		const newName = dynamicAdjustForm.value.newName.trim()

		// 主键认证策略验证
		const validationResult = validatePrimaryKeyName(newName)
		if (!validationResult.isValid) {
			ElMessage.error(validationResult.errorMessage!)
			return
		}

		// 检查主键名称是否与其他行重复（排除当前行）
		const existingKeys = primaryKeys.value.filter(key =>
			key.id !== currentRow.value!.id &&
			key.name?.trim().toLowerCase() === newName.toLowerCase()
		)

		if (existingKeys.length > 0) {
			ElMessage.error('主键名称已存在，请使用其他名称')
			return
		}

		// 更新主键名称
		const updatedData = {
			...currentRow.value,
			name: newName,
			updateTime: new Date().toISOString()
		}

		updatePrimaryKey(currentRow.value.id, updatedData)
		ElMessage.success('主键名称修改成功')
		showDynamicAdjustDialog.value = false
		currentRow.value = null

		// 重置表单
		dynamicAdjustForm.value.newName = ''
	} catch (error) {
		console.error('主键动态调整失败:', error)
		ElMessage.error('修改失败，请重试')
	}
}

// 取消主键动态调整
const handleCancelDynamicAdjust = () => {
	showDynamicAdjustDialog.value = false
	currentRow.value = null
	dynamicAdjustForm.value.newName = ''
}

// 处理移动端展示开关变化
const handleShowOnMobileChange = (row: PrimaryKeyData, value: boolean) => {
	try {
		const updatedData = {
			...row,
			showOnMobile: value,
			updateTime: new Date().toISOString()
		}

		updatePrimaryKey(row.id, updatedData)
		ElMessage.success(`已${value ? '开启' : '关闭'}移动端展示`)
	} catch (error) {
		console.error('更新移动端展示状态失败:', error)
		ElMessage.error('更新失败，请重试')
		// 恢复原状态
		row.showOnMobile = !value
	}
}

// 处理主键默认值开关变化
const handleIsDefaultValueChange = (row: PrimaryKeyData, value: boolean) => {
	try {
		const updatedData = {
			...row,
			isDefaultValue: value,
			updateTime: new Date().toISOString()
		}

		updatePrimaryKey(row.id, updatedData)
		ElMessage.success(`已${value ? '设置' : '取消'}主键默认值`)
	} catch (error) {
		console.error('更新主键默认值状态失败:', error)
		ElMessage.error('更新失败，请重试')
		// 恢复原状态
		row.isDefaultValue = !value
	}
}

const handleDeletePrimaryKey = (row: PrimaryKeyData) => {
	if (!row.id) {
		ElMessage.error('无法删除：主键ID不存在')
		return
	}

	ElMessageBox.confirm(
		`确定要删除主键"${row.name}"吗？`,
		'确认删除',
		{
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}
	).then(() => {
		if (deletePrimaryKey(row.id!)) {
			ElMessage.success('删除成功')
		} else {
			ElMessage.error('删除失败')
		}
	}).catch(() => {
		// 用户取消删除
	})
}

const handleSelectionChange = (selection: PrimaryKeyData[]) => {
	selectedKeys.value = selection.map(item => item.id)
	selectedRows.value = selection
}

// 新的批量修改处理函数
const handleBatchEdit = () => {
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请先选择要修改的主键')
		return
	}
	showBatchEditDialog.value = true
	batchEditForm.value = {
		dataType: '',
		minLength: 1,
		maxLength: 1
	}
}

// 确认批量修改
const handleConfirmBatchEdit = async () => {
	try {
		if (!batchEditForm.value.dataType) {
			ElMessage.error('请选择数据类型')
			return
		}

		if (batchEditForm.value.minLength === null || batchEditForm.value.minLength === undefined || Number.isNaN(batchEditForm.value.minLength)) {
			ElMessage.error('请输入最小长度')
			return
		}

		if (batchEditForm.value.maxLength === null || batchEditForm.value.maxLength === undefined || Number.isNaN(batchEditForm.value.maxLength)) {
			ElMessage.error('请输入最大长度')
			return
		}

		if (batchEditForm.value.minLength < 0) {
			ElMessage.error('最小长度不能小于0')
			return
		}

		if (batchEditForm.value.maxLength < 0) {
			ElMessage.error('最大长度不能小于0')
			return
		}

		if (batchEditForm.value.minLength > batchEditForm.value.maxLength) {
			ElMessage.error('最小长度不能大于最大长度')
			return
		}

		const updateData: BatchUpdateData = {
			dataType: batchEditForm.value.dataType,
			minLength: batchEditForm.value.minLength,
			maxLength: batchEditForm.value.maxLength
		}

		const selectedIds = selectedRows.value.map(item => item.id).filter(id => id !== undefined)
		const result = batchUpdatePrimaryKeys(selectedIds, updateData)
		if (result) {
			ElMessage.success(`成功批量修改 ${selectedIds.length} 条记录`)
			showBatchEditDialog.value = false
			selectedRows.value = []
			selectedKeys.value = []
		} else {
			ElMessage.error('批量修改失败')
		}
	} catch (error) {
		console.error('批量修改失败:', error)
		ElMessage.error('批量修改失败，请重试')
	}
}

const handleSortChange = ({ column, prop, order }: any) => {
	// 更新排序状态
	sortField.value = prop
	sortOrder.value = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : null

	// 保存排序配置
	updateSortConfig({
		field: prop || '',
		order: sortOrder.value
	})

	// 执行排序
	if (!sortOrder.value) {
		// 如果没有排序，恢复原始顺序
		primaryKeys.value = [...rawPrimaryKeys.value]
		return
	}

	primaryKeys.value.sort((a, b) => {
		let valueA: any = a[prop as keyof PrimaryKeyData]
		let valueB: any = b[prop as keyof PrimaryKeyData]

		// 处理不同数据类型的排序
		if (prop === 'name') {
			// 字符串排序（主键名称）
			valueA = (valueA || '').toString().toLowerCase()
			valueB = (valueB || '').toString().toLowerCase()
		} else if (prop === 'minLength') {
			// 数字排序（最小长度）
			valueA = Number(valueA) || 0
			valueB = Number(valueB) || 0
		}

		// 执行比较
		if (valueA < valueB) {
			return sortOrder.value === 'asc' ? -1 : 1
		}
		if (valueA > valueB) {
			return sortOrder.value === 'asc' ? 1 : -1
		}
		return 0
	})
}

// 辅助方法
const getDataTypeLabel = (dataType: string) => {
	const type = PrimaryKeyDataTypes.find(t => t.value === dataType)
	return type ? type.label : dataType
}

// Excel 导入相关方法
const replaceAllData = (importedData: PrimaryKeyData[]) => {
	clearAllData()
	batchAddPrimaryKeys(importedData)
	ElMessage.success(`已替换所有数据，共 ${importedData.length} 条`)
}

const appendData = (importedData: PrimaryKeyData[]) => {
	let addedCount = 0
	let skippedCount = 0
	
	importedData.forEach(item => {
		const exists = primaryKeys.value.some(existing => existing.name === item.name)
		if (!exists) {
			addPrimaryKey(item)
			addedCount++
		} else {
			skippedCount++
		}
	})
	
	if (skippedCount > 0) {
		ElMessage.warning(`已添加 ${addedCount} 条数据，跳过 ${skippedCount} 条重复数据`)
	} else {
		ElMessage.success(`已添加 ${addedCount} 条数据`)
	}
}

// 表单引用
const addFormRef = ref()

// 弹框确认处理
const handleConfirmAdd = async () => {
	try {
		// 基础字段验证
		if (!addForm.value.name?.trim()) {
			ElMessage.error('请输入主键名称')
			return
		}

		// 主键认证策略验证
		const validationResult = validatePrimaryKeyName(addForm.value.name)
		if (!validationResult.isValid) {
			ElMessage.error(validationResult.errorMessage!)
			return
		}

		if (!addForm.value.dataType) {
			ElMessage.error('请选择主键数据类型')
			return
		}

		// 最小长度和最大长度必填验证
		if (addForm.value.minLength === undefined || addForm.value.minLength === null) {
			ElMessage.error('请输入最小长度')
			return
		}

		if (addForm.value.maxLength === undefined || addForm.value.maxLength === null) {
			ElMessage.error('请输入最大长度')
			return
		}

		// 长度逻辑验证
		if (addForm.value.minLength < 0) {
			ElMessage.error('最小长度不能小于0')
			return
		}

		if (addForm.value.maxLength < 0) {
			ElMessage.error('最大长度不能小于0')
			return
		}

		if (addForm.value.minLength > addForm.value.maxLength) {
			ElMessage.error('最小长度不能大于最大长度')
			return
		}

		// 主键名称唯一性验证
		const existingKeys = primaryKeys.value.filter(key =>
			key.id !== addForm.value.id && // 编辑时排除自己
			key.name?.trim().toLowerCase() === addForm.value.name?.trim().toLowerCase()
		)

		if (existingKeys.length > 0) {
			ElMessage.error('主键名称已存在，请使用其他名称')
			return
		}

		// 数据类型重复检查（如果启用了数据类型检查）
		if (uniquenessConfig.value.dataTypeCheck) {
			const existingDataTypes = primaryKeys.value.filter(key =>
				key.id !== addForm.value.id && // 编辑时排除自己
				key.dataType === addForm.value.dataType
			)

			if (existingDataTypes.length > 0) {
				ElMessage.error('该数据类型已存在，请选择其他数据类型')
				return
			}
		}

		// 执行保存操作
		if (isEditMode.value && addForm.value.id) {
			// 编辑模式
			const result = updatePrimaryKey(addForm.value.id, addForm.value)
			if (result) {
				ElMessage.success('主键更新成功')
				showAddDialog.value = false
				isEditMode.value = false
			} else {
				ElMessage.error('主键更新失败')
			}
		} else {
			// 新增模式
			const result = addPrimaryKey(addForm.value as Omit<PrimaryKeyData, 'id' | 'sequence' | 'createTime' | 'updateTime'>)
			if (result) {
				ElMessage.success('主键添加成功')
				showAddDialog.value = false
				isEditMode.value = false
			} else {
				ElMessage.error('主键添加失败')
			}
		}
	} catch (error) {
		console.error('保存失败:', error)
		ElMessage.error('保存失败，请重试')
	}
}

// 取消新增/编辑
const handleCancelAdd = () => {
	showAddDialog.value = false
	isEditMode.value = false
	// 重置表单数据
	addForm.value = {
		name: '',
		dataType: '',
		minLength: 1,
		maxLength: 10,
		description: '',
		version: '',
		showOnMobile: false,
		isDefaultValue: true,
		updateNotification: true,
		encryptionLevel: '',
		encryptionContent: ''
	}
}

// 取消详情查看
const handleCancelDetail = () => {
	showDetailDialog.value = false
	isDetailMode.value = false
	// 重置表单数据
	addForm.value = {
		name: '',
		dataType: '',
		minLength: 1,
		maxLength: 10,
		description: '',
		version: '',
		showOnMobile: false,
		isDefaultValue: true,
		updateNotification: true,
		encryptionLevel: '',
		encryptionContent: ''
	}
}

// 批量修改确认处理
const handleConfirmBatchUpdate = async () => {
	try {
		if (!batchUpdateForm.value.dataType) {
			ElMessage.error('请选择数据类型')
			return
		}

		const updateData: BatchUpdateData = {
			dataType: batchUpdateForm.value.dataType,
			minLength: batchUpdateForm.value.minLength,
			maxLength: batchUpdateForm.value.maxLength
		}

		const result = batchUpdatePrimaryKeys(selectedKeys.value, updateData)
		if (result) {
			ElMessage.success(`成功批量修改 ${selectedKeys.value.length} 条记录`)
			showBatchUpdateDialog.value = false
			selectedKeys.value = []
		} else {
			ElMessage.error('批量修改失败')
		}
	} catch (error) {
		console.error('批量修改失败:', error)
		ElMessage.error('批量修改失败，请重试')
	}
}

// 主键清理规则确认处理
const handleConfirmCleanupRule = async () => {
	try {
		if (!cleanupRuleForm.value.cleanupTime || !cleanupRuleForm.value.cleanupStrategy) {
			ElMessage.error('请填写完整的清理规则')
			return
		}

		if (!currentRow.value) {
			ElMessage.error('未找到当前操作的主键数据')
			return
		}

		const ruleData: CleanupRuleData = {
			cleanupTime: cleanupRuleForm.value.cleanupTime,
			cleanupStrategy: cleanupRuleForm.value.cleanupStrategy
		}

		// 更新当前行的清理规则
		const updatedData = {
			...currentRow.value,
			cleanupRule: ruleData,
			updateTime: new Date().toISOString()
		}

		updatePrimaryKey(currentRow.value.id, updatedData)
		ElMessage.success('清理规则保存成功')
		showCleanupRuleDialog.value = false
		currentRow.value = null
	} catch (error) {
		console.error('保存清理规则失败:', error)
		ElMessage.error('保存失败，请重试')
	}
}

// 取消清理规则配置
const handleCancelCleanupRule = () => {
	showCleanupRuleDialog.value = false
	currentRow.value = null
}

// 确认存储位置配置
const handleConfirmStorageLocation = async () => {
	try {
		if (!storageLocationForm.value.storageLocation) {
			ElMessage.error('请选择存储位置')
			return
		}

		if (!currentRow.value) {
			ElMessage.error('未找到当前操作的主键数据')
			return
		}

		const locationData: StorageLocationConfig = {
			storageLocation: storageLocationForm.value.storageLocation
		}

		// 更新当前行的存储位置配置
		const updatedData = {
			...currentRow.value,
			storageLocation: locationData,
			updateTime: new Date().toISOString()
		}

		updatePrimaryKey(currentRow.value.id, updatedData)
		ElMessage.success('存储位置配置保存成功')
		showStorageLocationDialog.value = false
		currentRow.value = null
	} catch (error) {
		console.error('保存存储位置配置失败:', error)
		ElMessage.error('保存失败，请重试')
	}
}

// 取消存储位置配置
const handleCancelStorageLocation = () => {
	showStorageLocationDialog.value = false
	currentRow.value = null
}

// 应用已保存的排序配置
const applySavedSortConfig = () => {
	if (sortConfig.value.field && sortConfig.value.order) {
		sortField.value = sortConfig.value.field
		sortOrder.value = sortConfig.value.order

		// 应用排序
		handleSortChange({
			column: null,
			prop: sortConfig.value.field,
			order: sortConfig.value.order === 'asc' ? 'ascending' : 'descending'
		})
	}
}

// 生命周期
onMounted(() => {
	// 组件挂载时加载历史记录列配置
	loadHistoryColumnConfig()
	// 加载备份策略配置
	loadBackupStrategyConfig()
	// 加载主键自动生成配置
	loadAutoGenConfig()
	// 加载主键认证策略配置
	loadAuthStrategyConfig()
	// 加载主键唯一性配置
	loadUniquenessConfig()
	// 加载主键更改权限配置
	loadChangePermissionConfig()
	// 应用已保存的排序配置
	applySavedSortConfig()
	// 启动自动解锁定时器
	startUnlockTimer()
	// 初始化可视化标识配置
	initializeVisualIdentifierConfigs()
	// 初始化加密配置
	initializeEncryptionConfigs()
})

// 组件卸载时清理定时器
onUnmounted(() => {
	stopUnlockTimer()
})
</script>
<route>
	{
		meta: {
			title:'业务表主键定义',
			ignoreLabel:false
		}
	}
</route>
<template>
	<div class="primary-key-definition">
			<Block
				title="业务表主键定义"
				:enable-expand-content="true"
				:enableBackButton="false"
				:enable-fixed-height="true"
				:enable-close-button="false"
				@content-expand="expendSearch"
				@height-changed="onBlockHeightChanged"
			>

				<template #topRight>
					<el-button size="small" type="primary" @click="handleAddPrimaryKey">新增主键</el-button>

					<el-button
						size="small"
						type="success"
						@click="handleBatchImport"
						style="margin-right: 8px"
						:loading="isImporting"
					>
						批量导入
					</el-button>
					<el-button
						size="small"
						type="info"
						@click="handleBatchExport"
						style="margin-right: 8px"
						:loading="isExporting"
					>
						批量导出
					</el-button>
					<el-button
						size="small"
						type="warning"
						:disabled="selectedRows.length === 0"
						@click="handleBatchEdit"
						style="margin-right: 8px"
						data-test="batch-edit-button"
					>
						批量修改
					</el-button>


					<!-- 页面级更多操作下拉按钮 -->
					<el-dropdown
						style="margin-right: 8px"
						trigger="click"
						@command="handleDropdownCommand"
					>
						<el-button size="small" type="default">
							更多操作
							<el-icon class="el-icon--right"><arrow-down /></el-icon>
						</el-button>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item command="performanceMonitor">
									主键性能监控分析
								</el-dropdown-item>
								<el-dropdown-item command="authStrategy">
									主键认证策略
								</el-dropdown-item>
								<el-dropdown-item command="uniquenessConfig">
									主键唯一性配置
								</el-dropdown-item>
								<el-dropdown-item command="autoGenConfig">
									主键自动生成配置
								</el-dropdown-item>
								<el-dropdown-item command="primaryKeyStats">
									主键统计
								</el-dropdown-item>
								<el-dropdown-item command="backupStrategy">
									主键备份与恢复
								</el-dropdown-item>
								<el-dropdown-item command="viewAllHistory">
									历史记录
								</el-dropdown-item>
								<el-dropdown-item command="primaryKeyValidation">
									主键函数验证
								</el-dropdown-item>
								<el-dropdown-item command="historyColumnConfig">
									主键变更记录配置
								</el-dropdown-item>
								<el-dropdown-item command="changePermissionConfig">
									主键更改权限配置
								</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
				</template>
				<template #expand>
					<!-- 搜索 -->
					<div class="search">
						<Form
							:props="searchFormProp"
							v-model="searchForm"
							:column-count="5"
							:label-width="74"
							:enable-reset="false"
							confirm-text="查询"
							button-vertical="flowing"
							@submit="onSearch"
						></Form>
					</div>
				</template>

				<!-- 列表 -->
				<TableV2
					ref="tableRef"
					:defaultTableData="paginatedData"
					:columns="columns"
					:enable-toolbar="false"
					:enable-own-button="false"
					:enable-selection="true"
					:height="tableHeight"
					:buttons="buttons"
					@loading="loading = $event"
					@click-button="onTableClickButton"
					@sort-change="handleSortChange"
					@selection-change="handleSelectionChange"
				>
					<!-- 主键名称列自定义显示 -->
					<template #name="{ row }">
						<div class="name-cell">
							<el-icon v-if="isKeyLocked(row)" class="lock-icon">
								<Lock />
							</el-icon>
							<span :style="getVisualIdentifierStyle(row)">{{ row.name }}</span>
						</div>
					</template>

					<!-- 数据类型列自定义显示 -->
					<template #dataType="{ row }">
						{{ getDataTypeLabel(row.dataType) }}
					</template>

					<!-- 移动端展示开关列自定义显示 -->
					<template #showOnMobile="{ row }">
						<el-switch
							v-model="row.showOnMobile"
							@change="handleShowOnMobileChange(row, $event as boolean)"
						/>
					</template>

					<!-- 主键默认值列自定义显示 -->
					<template #isDefaultValue="{ row }">
						<el-switch
							v-model="row.isDefaultValue"
							@change="handleIsDefaultValueChange(row, $event as boolean)"
						/>
					</template>
				</TableV2>
				<!-- 分页 -->
				<Pagination
					:total="totalCount"
					:current-page="currentPage"
					:page-size="pageSize"
					@current-change="onPaginationChange($event, 'page')"
					@size-change="onPaginationChange($event, 'size')"
				></Pagination>
			</Block>

			<!-- 主键新增/编辑弹框 -->
		<DialogComp
			v-model:visible="showAddDialog"
			:title="isEditMode ? '编辑主键' : '主键新增'"
			width="600px"
			@click-confirm="handleConfirmAdd"
			@click-cancel="handleCancelAdd"
			@closed="handleCancelAdd"
		>
			<template #body>
				<el-form
					ref="addFormRef"
					:model="addForm"
					label-width="120px"
					class="primary-key-form"
				>
					<el-form-item label="主键名称" required>
						<el-input
							v-model="addForm.name"
							placeholder="请输入主键名称"
							clearable
						/>
					</el-form-item>

					<el-form-item label="主键数据类型" required>
						<el-select
							v-model="addForm.dataType"
							placeholder="请选择数据类型"
							style="width: 100%"
						>
							<el-option
								v-for="type in PrimaryKeyDataTypes"
								:key="type.value"
								:label="type.label"
								:value="type.value"
							/>
						</el-select>
					</el-form-item>

					<el-row :gutter="20">
						<el-col :span="12">
							<el-form-item label="最小长度" required>
								<el-input-number
									v-model="addForm.minLength"
									:min="0"
									:max="9999"
									style="width: 100%"
								/>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="最大长度" required>
								<el-input-number
									v-model="addForm.maxLength"
									:min="0"
									:max="9999"
									style="width: 100%"
								/>
							</el-form-item>
						</el-col>
					</el-row>

					<el-form-item label="主键说明">
						<el-input
							v-model="addForm.description"
							type="textarea"
							:rows="3"
							placeholder="请输入主键说明"
						/>
					</el-form-item>

					<el-form-item label="主键版本">
						<el-input
							v-model="addForm.version"
							placeholder="请输入主键版本"
							clearable
						/>
					</el-form-item>

					<el-form-item label="主键更新通知" required>
						<el-switch
							v-model="addForm.updateNotification"
							active-text="开启"
							inactive-text="关闭"
						/>
					</el-form-item>

					<el-form-item label="主键加密级别">
						<el-select
							v-model="addForm.encryptionLevel"
							placeholder="请选择加密级别"
							style="width: 100%"
						>
							<el-option
								v-for="level in EncryptionLevels"
								:key="level.value"
								:label="level.label"
								:value="level.value"
							/>
						</el-select>
					</el-form-item>

					<el-form-item label="主键加密内容">
						<el-input
							v-model="addForm.encryptionContent"
							placeholder="请输入加密内容"
							clearable
						/>
					</el-form-item>
				</el-form>
			</template>
		</DialogComp>

		<!-- 主键详情弹框 -->
		<DialogComp
			v-model:visible="showDetailDialog"
			title="主键详情"
			width="600px"
			:visibleConfirmButton="false"
			cancelText="关闭"
			@click-cancel="handleCancelDetail"
			@closed="handleCancelDetail"
		>
			<template #body>
				<el-form
					ref="detailFormRef"
					:model="addForm"
					label-width="120px"
					class="primary-key-form"
				>
					<el-form-item label="主键名称">
						<el-input
							v-model="addForm.name"
							placeholder="请输入主键名称"
							readonly
						/>
					</el-form-item>

					<el-form-item label="主键数据类型">
						<el-select
							v-model="addForm.dataType"
							placeholder="请选择数据类型"
							style="width: 100%"
							disabled
						>
							<el-option
								v-for="type in PrimaryKeyDataTypes"
								:key="type.value"
								:label="type.label"
								:value="type.value"
							/>
						</el-select>
					</el-form-item>

					<el-row :gutter="20">
						<el-col :span="12">
							<el-form-item label="最小长度" required>
								<el-input-number
									v-model="addForm.minLength"
									:min="0"
									:max="9999"
									style="width: 100%"
									readonly
								/>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="最大长度" required>
								<el-input-number
									v-model="addForm.maxLength"
									:min="0"
									:max="9999"
									style="width: 100%"
									readonly
								/>
							</el-form-item>
						</el-col>
					</el-row>

					<el-form-item label="主键说明">
						<el-input
							v-model="addForm.description"
							type="textarea"
							:rows="3"
							placeholder="请输入主键说明"
							readonly
						/>
					</el-form-item>

					<el-form-item label="主键更新通知">
						<el-switch
							v-model="addForm.updateNotification"
							active-text="开启"
							inactive-text="关闭"
							disabled
						/>
					</el-form-item>

					<el-form-item label="主键加密级别">
						<el-select
							v-model="addForm.encryptionLevel"
							placeholder="请选择加密级别"
							style="width: 100%"
							disabled
						>
							<el-option
								v-for="level in EncryptionLevels"
								:key="level.value"
								:label="level.label"
								:value="level.value"
							/>
						</el-select>
					</el-form-item>

					<el-form-item label="主键加密内容">
						<el-input
							v-model="addForm.encryptionContent"
							placeholder="请输入加密内容"
							readonly
						/>
					</el-form-item>
				</el-form>
			</template>
		</DialogComp>

		<!-- 批量修改弹框 -->
		<DialogComp
			v-model:visible="showBatchUpdateDialog"
			title="批量修改"
			width="500px"
			@click-confirm="handleConfirmBatchUpdate"
			@click-cancel="showBatchUpdateDialog = false"
		>
			<template #body>
				<el-form
					ref="batchUpdateFormRef"
					:model="batchUpdateForm"
					label-width="120px"
					class="batch-update-form"
				>
					<el-form-item label="主键数据类型" required>
						<el-select
							v-model="batchUpdateForm.dataType"
							placeholder="请选择数据类型"
							style="width: 100%"
						>
							<el-option
								v-for="type in PrimaryKeyDataTypes"
								:key="type.value"
								:label="type.label"
								:value="type.value"
							/>
						</el-select>
					</el-form-item>

					<el-form-item label="最小长度">
						<el-input-number
							v-model="batchUpdateForm.minLength"
							:min="0"
							:max="9999"
							style="width: 100%"
						/>
					</el-form-item>

					<el-form-item label="最大长度">
						<el-input-number
							v-model="batchUpdateForm.maxLength"
							:min="0"
							:max="9999"
							style="width: 100%"
						/>
					</el-form-item>
				</el-form>
			</template>
		</DialogComp>

		<!-- 新的批量编辑弹框 -->
		<DialogComp
			v-model:visible="showBatchEditDialog"
			title="批量修改"
			width="500px"
			@click-confirm="handleConfirmBatchEdit"
			@click-cancel="showBatchEditDialog = false"
		>
			<template #body>
				<div class="batch-edit-info" style="margin-bottom: 16px; padding: 12px; background-color: #f5f7fa; border-radius: 4px;">
					<p style="margin: 0; color: #606266; font-size: 14px;">
						已选择 <strong style="color: #409eff;">{{ selectedRows.length }}</strong> 条记录进行批量修改
					</p>
				</div>
				<el-form
					ref="batchEditFormRef"
					:model="batchEditForm"
					label-width="120px"
					class="batch-edit-form"
				>
					<el-form-item label="主键数据类型" required>
						<el-select
							v-model="batchEditForm.dataType"
							placeholder="请选择数据类型"
							style="width: 100%"
						>
							<el-option
								v-for="type in PrimaryKeyDataTypes"
								:key="type.value"
								:label="type.label"
								:value="type.value"
							/>
						</el-select>
					</el-form-item>

					<el-form-item label="最小长度" required>
						<el-input-number
							v-model="batchEditForm.minLength"
							:min="0"
							:max="9999"
							style="width: 100%"
							placeholder="请输入最小长度"
						/>
					</el-form-item>

					<el-form-item label="最大长度" required>
						<el-input-number
							v-model="batchEditForm.maxLength"
							:min="0"
							:max="9999"
							style="width: 100%"
							placeholder="请输入最大长度"
						/>
					</el-form-item>
				</el-form>
			</template>
		</DialogComp>

		<!-- 主键清理规则弹框 -->
		<DialogComp
			v-model:visible="showCleanupRuleDialog"
			title="主键清理规则"
			width="600px"
			@click-confirm="handleConfirmCleanupRule"
			@click-cancel="handleCancelCleanupRule"
			@closed="handleCancelCleanupRule"
		>
			<template #body>
				<el-form
					ref="cleanupRuleFormRef"
					:model="cleanupRuleForm"
					label-width="120px"
					class="cleanup-rule-form"
				>
					<el-form-item label="清理时间" required>
						<el-input-number
							v-model="cleanupRuleForm.cleanupTime"
							:min="1"
							:max="365"
							style="width: 200px"
						/>
						<span style="margin-left: 8px;">天</span>
					</el-form-item>

					<el-form-item label="清理策略" required>
						<el-select
							v-model="cleanupRuleForm.cleanupStrategy"
							placeholder="请选择清理策略"
							style="width: 100%"
						>
							<el-option
								v-for="strategy in CleanupStrategies"
								:key="strategy.value"
								:label="strategy.label"
								:value="strategy.value"
							/>
						</el-select>
					</el-form-item>
				</el-form>
			</template>
		</DialogComp>

		<!-- 主键存储位置弹框 -->
		<DialogComp
			v-model:visible="showStorageLocationDialog"
			title="主键存储位置"
			width="600px"
			@click-confirm="handleConfirmStorageLocation"
			@click-cancel="handleCancelStorageLocation"
			@closed="handleCancelStorageLocation"
		>
			<template #body>
				<el-form
					ref="storageLocationFormRef"
					:model="storageLocationForm"
					label-width="120px"
					class="storage-location-form"
				>
					<el-form-item label="主键存储位置" required>
						<el-select
							v-model="storageLocationForm.storageLocation"
							placeholder="请选择"
							style="width: 100%"
						>
							<el-option
								v-for="option in StorageLocationOptions"
								:key="option.value"
								:label="option.label"
								:value="option.value"
							/>
						</el-select>
					</el-form-item>
				</el-form>
			</template>
		</DialogComp>

		<!-- 批量导入弹框 -->
		<DialogComp
			v-model:visible="showImportDialog"
			title="批量导入"
			width="900px"
			@click-confirm="handleConfirmImport"
			@click-cancel="showImportDialog = false"
			:loading="isImporting"
		>
			<template #body>
				<div class="import-container">
					<!-- 顶部提示信息 -->
					<div class="import-header">
						<div class="import-tip">
							<el-icon class="tip-icon"><InfoFilled /></el-icon>
							<span>点击批量导入，跳转到此弹窗。</span>
						</div>
					</div>

					<!-- 导入须知 -->
					<div class="import-notice-section">
						<div class="notice-title">
							<span class="notice-text">导入须知</span>
							<el-tooltip
								effect="dark"
								placement="right"
								popper-class="import-notice-tooltip"
							>
								<template #content>
									<div class="tooltip-content">
										<p><strong>导入须知：</strong></p>
										<p>1.业务表设置了唯一一标识，会按数据一标识字段是否已存在，已存在则更新该行数据，不存在则新增一行数据；</p>
										<p>2.导入文件后缀名为xlsx或xlsx，文件大小不超过50M</p>
										<p>3.数据需放在合并的单元格中</p>
										<p>4.单元格内容为多选选项的，会按页面之前用中文分号；隔开</p>
										<p>5.导入模板中标红的列为必填项，必须才能导入成功</p>
									</div>
								</template>
								<el-icon class="notice-icon"><QuestionFilled /></el-icon>
							</el-tooltip>
						</div>
					</div>

					<!-- 操作流程 -->
					<div class="import-steps">
						<div class="steps-title">操作流程：</div>
						<div class="steps-container">
							<div class="step-item">
								<div class="step-number">1</div>
								<div class="step-title">下载模板</div>
							</div>
							<div class="step-arrow">→</div>
							<div class="step-item">
								<div class="step-number">2</div>
								<div class="step-title">填写表格</div>
							</div>
							<div class="step-arrow">→</div>
							<div class="step-item">
								<div class="step-number">3</div>
								<div class="step-title">上传表格</div>
							</div>
						</div>
					</div>

					<!-- 下载模板按钮 -->
					<div class="download-template-section">
						<el-button
							type="primary"
							@click="handleDownloadTemplate"
							:loading="isExporting"
							class="download-btn"
						>
							📥 导入模板下载
						</el-button>
					</div>

					<!-- 文件上传区域 -->
					<div class="upload-section">
						<el-upload
							ref="uploadRef"
							class="upload-dragger"
							drag
							:auto-upload="false"
							:show-file-list="true"
							:limit="1"
							accept=".xlsx,.xls"
							@change="handleFileChange"
							@exceed="handleExceed"
						>
							<div class="upload-content">
								<div class="upload-icon-container">
									<el-icon class="upload-icon"><UploadFilled /></el-icon>
								</div>
								<div class="upload-text">点击或将文件拖拽到这里上传</div>
								<div class="upload-hint">导入模式为业务表中已有数据更新，业务表中不包含数据新增，仅支持后缀名为xlsx文件</div>
							</div>
						</el-upload>
					</div>
				</div>
			</template>
		</DialogComp>

		<!-- 历史记录弹窗 -->
		<HistoryDialog
			v-model:visible="showHistoryDialog"
			:primary-key-id="historyPrimaryKeyId"
			:title="historyDialogTitle"
		/>

		<!-- 主键认证策略弹窗 -->
		<DialogComp
			v-model:visible="showAuthStrategyDialog"
			title="主键认证策略"
			width="600px"
			@click-confirm="handleConfirmAuthStrategy"
			@click-cancel="handleCancelAuthStrategy"
			@closed="handleCancelAuthStrategy"
		>
			<template #body>
				<div class="auth-strategy-content">
					<el-form
						ref="authStrategyFormRef"
						:model="authStrategyConfig"
						label-width="140px"
						class="auth-strategy-form"
					>
						<!-- 主键验证策略开关 -->
						<el-form-item label="主键验证策略">
							<el-switch
								v-model="authStrategyConfig.enabled"
								active-text=""
								inactive-text=""
							/>
						</el-form-item>

						<!-- 验证规则选择 -->
						<template v-if="authStrategyConfig.enabled">
							<el-form-item label="验证规则">
								<div class="validation-rules">
									<el-checkbox
										v-model="authStrategyConfig.skipWhitespace"
										label="跳过空格"
									>
										跳过空格
									</el-checkbox>
									<el-checkbox
										v-model="authStrategyConfig.skipSpecialChars"
										label="跳过字符"
									>
										跳过字符
									</el-checkbox>
								</div>
							</el-form-item>

							<!-- 验证规则说明 -->
							<el-form-item label="" class="validation-description-el-form-item">
								<div class="validation-description">
									<p><strong>验证规则说明：</strong></p>
									<ul>
										<li><strong>跳过空格：</strong>勾选该项后，主键能保存空格字符</li>
										<li><strong>跳过字符：</strong>勾选该项后，主键能保存特殊字符（如：!@#$%^&*(),.?":{}|&lt;&gt;）</li>
									</ul>
								</div>
							</el-form-item>
						</template>
					</el-form>
				</div>
			</template>
		</DialogComp>

		<!-- 主键唯一性配置弹窗 -->
		<DialogComp
			v-model:visible="showUniquenessConfigDialog"
			title="主键唯一性配置"
			width="600px"
			@click-confirm="handleConfirmUniquenessConfig"
			@click-cancel="handleCancelUniquenessConfig"
			@closed="handleCancelUniquenessConfig"
		>
			<template #body>
				<div class="uniqueness-config-content">
					<el-form
						ref="uniquenessConfigFormRef"
						:model="uniquenessConfig"
						label-width="140px"
						class="uniqueness-config-form"
					>
						<!-- 配置说明 -->
						<div class="config-tip">
							<el-icon class="tip-icon"><InfoFilled /></el-icon>
							<span>主键唯一性验证：例如勾选数据类型检查，则在新增主键时需要校验主键数据类型是否唯一</span>
						</div>

						<!-- 配置选项 -->
						<div class="config-options">
							<el-form-item label="空值检查">
								<el-checkbox
									v-model="uniquenessConfig.nullCheck"
									:disabled="true"
								>
									空值检查
								</el-checkbox>
							</el-form-item>

							<el-form-item label="唯一性检查">
								<el-checkbox
									v-model="uniquenessConfig.uniquenessCheck"
									:disabled="true"
								>
									唯一性检查
								</el-checkbox>
							</el-form-item>

							<el-form-item label="数据类型检查">
								<el-checkbox
									v-model="uniquenessConfig.dataTypeCheck"
								>
									数据类型检查
								</el-checkbox>
							</el-form-item>
						</div>
					</el-form>
				</div>
			</template>
		</DialogComp>

		<!-- 主键自动生成配置弹窗 -->
		<DialogComp
			v-model:visible="showAutoGenConfigDialog"
			title="主键自动生成配置"
			width="700px"
			@click-confirm="handleConfirmAutoGenConfig"
			@click-cancel="handleCancelAutoGenConfig"
			@closed="handleCancelAutoGenConfig"
		>
			<template #body>
				<div class="auto-gen-config-content">
					<el-form
						ref="autoGenConfigFormRef"
						:model="autoGenConfig"
						label-width="140px"
						class="auto-gen-config-form"
					>
						<!-- 主键策略选择 -->
						<el-form-item label="主键策略选择" required>
							<el-select
								v-model="autoGenConfig.strategy"
								placeholder="请选择主键策略"
								style="width: 100%"
							>
								<el-option
									v-for="option in PrimaryKeyStrategyOptions"
									:key="option.value"
									:label="option.label"
									:value="option.value"
								/>
							</el-select>
						</el-form-item>

						<!-- 自增序列配置 -->
						<template v-if="autoGenConfig.strategy === PrimaryKeyStrategy.AUTO_INCREMENT">
							<el-form-item label="起始值" required>
								<el-input
									v-model="autoGenConfig.autoIncrementConfig!.startValue"
									placeholder="请输入起始值"
									clearable
								/>
							</el-form-item>

							<el-form-item label="步长" required>
								<el-input-number
									v-model="autoGenConfig.autoIncrementConfig!.stepSize"
									:min="1"
									:max="1000"
									style="width: 100%"
									placeholder="请输入步长"
								/>
							</el-form-item>
						</template>

						<!-- UUID配置 -->
						<template v-if="autoGenConfig.strategy === PrimaryKeyStrategy.UUID">
							<el-form-item label="UUID版本" required>
								<el-radio-group v-model="autoGenConfig.uuidConfig!.version">
									<el-radio
										v-for="option in UUIDVersionOptions"
										:key="option.value"
										:label="option.value"
									>
										{{ option.label }}
									</el-radio>
								</el-radio-group>
							</el-form-item>

							<el-form-item label="UUID版本格式" required>
								<el-checkbox-group v-model="uuidFormatOptions">
									<el-checkbox label="uppercase">大写</el-checkbox>
									<el-checkbox label="withHyphen">连带字符</el-checkbox>
								</el-checkbox-group>
							</el-form-item>
						</template>
					</el-form>
				</div>
			</template>
		</DialogComp>

		<!-- 主键统计弹窗 -->
		<DialogComp
			v-model:visible="showPrimaryKeyStatsDialog"
			title="主键统计"
			width="800px"
			:visibleConfirmButton="false"
			cancelText="关闭"
			@click-cancel="showPrimaryKeyStatsDialog = false"
			@closed="showPrimaryKeyStatsDialog = false"
		>
			<template #body>
				<div class="primary-key-stats-content">
					<!-- 统计概览卡片 -->
					<div class="stats-overview">
						<div class="stats-card">
							<div class="stats-number">{{ primaryKeyStatsData.length }}</div>
							<div class="stats-label">总数主键数</div>
						</div>
						<div class="stats-card">
							<div class="stats-number">{{ primaryKeyStatsData.reduce((sum, item) => sum + item.usageCount, 0) }}</div>
							<div class="stats-label">累计主键数</div>
						</div>
						<div class="stats-card">
							<div class="stats-number">{{ primaryKeyStatsData.filter(item => item.usageCount > 0).length }}</div>
							<div class="stats-label">除名主键数</div>
						</div>
						<div class="stats-card">
							<div class="stats-number">{{ primaryKeyStatsData.filter(item => item.usageCount === 0).length }}</div>
							<div class="stats-label">空闲主键数</div>
						</div>
					</div>

					<!-- 统计表格 -->
					<div class="stats-table-container">
						<el-table
							:data="primaryKeyStatsData"
							style="width: 100%"
							stripe
							border
							height="400"
						>
							<el-table-column
								prop="name"
								label="主键名称"
								width="300"
								align="left"
							/>
							<el-table-column
								prop="usageCount"
								label="使用次数"
								align="center"
								sortable
							>
								<template #default="{ row }">
									<el-tag
										:type="row.usageCount === 0 ? 'info' : row.usageCount < 10 ? 'warning' : 'success'"
										size="small"
									>
										{{ row.usageCount }} 次
									</el-tag>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</div>
			</template>
		</DialogComp>

		<!-- 主键函数验证弹窗 -->
		<DialogComp
			v-model:visible="showPrimaryKeyValidationDialog"
			title="主键自定义函数验证"
			width="600px"
			@click-confirm="handleConfirmPrimaryKeyValidation"
			@click-cancel="showPrimaryKeyValidationDialog = false"
			@closed="showPrimaryKeyValidationDialog = false"
		>
			<template #body>
				<div class="primary-key-validation-content">
					<el-form
						label-width="140px"
						class="primary-key-validation-form"
					>
						<el-form-item label="主键函数">
							<el-switch
								v-model="primaryKeyFunctionEnabled"
								active-text=""
								inactive-text=""
							/>
						</el-form-item>

						<el-form-item label="主键验证函数" required>
							<el-select
								v-model="primaryKeyValidationFunction"
								placeholder="请选择主键验证函数"
								style="width: 100%"
							>
								<el-option label="时间戳" value="时间戳" />
								<el-option label="UUID" value="UUID" />
								<el-option label="GUID" value="GUID" />
							</el-select>
						</el-form-item>
					</el-form>
				</div>
			</template>
		</DialogComp>

		<!-- 主键备份策略配置弹窗 -->
		<DialogComp
			v-model:visible="showBackupStrategyDialog"
			title="主键备份策略"
			width="800px"
			@click-confirm="handleConfirmBackupStrategy"
			@click-cancel="showBackupStrategyDialog = false"
			@closed="showBackupStrategyDialog = false"
		>
			<template #body>
				<div class="backup-strategy-content">
					<!-- 主键备份规则 -->
					<div class="backup-section">
						<div class="section-title">主键备份规则</div>
						<el-form
							:model="backupStrategyConfig"
							label-width="140px"
							class="backup-strategy-form"
						>
							<el-form-item label="主键备份开始时间">
								<el-date-picker
									v-model="backupStrategyConfig.backupStartTime"
									type="date"
									placeholder="请选择日期"
									style="width: 100%"
								/>
							</el-form-item>

							<el-form-item label="全量备份频率：">
								<el-select
									v-model="backupStrategyConfig.fullBackupFrequency"
									placeholder="下拉选择"
									style="width: 100%"
								>
									<el-option
										v-for="option in BackupFrequencyOptions"
										:key="option.value"
										:label="option.label"
										:value="option.value"
									/>
								</el-select>
							</el-form-item>

							<el-form-item label="增量备份频率：">
								<el-select
									v-model="backupStrategyConfig.incrementalBackupFrequency"
									placeholder="下拉选择"
									style="width: 100%"
								>
									<el-option
										v-for="option in BackupFrequencyOptions"
										:key="option.value"
										:label="option.label"
										:value="option.value"
									/>
								</el-select>
							</el-form-item>

							<el-form-item label="">
								<el-select
									v-model="backupStrategyConfig.retentionTime"
									placeholder="下拉选择"
									style="width: 100%"
								>
									<el-option
										v-for="option in RetentionTimeOptions"
										:key="option.value"
										:label="option.label"
										:value="option.value"
									/>
								</el-select>
							</el-form-item>
						</el-form>
					</div>

					<!-- 主键恢复规则 -->
					<div class="restore-section">
						<div class="section-title">主键恢复规则</div>
						<el-form
							:model="backupStrategyConfig"
							label-width="180px"
							class="restore-strategy-form"
						>
							<el-form-item label="请选择恢复至指定时期：">
								<el-date-picker
									v-model="backupStrategyConfig.restoreTargetDate"
									type="date"
									placeholder="请选择日期"
									style="width: 100%"
								/>
							</el-form-item>

							<el-form-item label="请选择恢复执行时间：">
								<el-date-picker
									v-model="backupStrategyConfig.restoreExecutionTime"
									type="date"
									placeholder="请选择日期"
									style="width: 100%"
								/>
							</el-form-item>
						</el-form>
					</div>
				</div>
			</template>
		</DialogComp>

		<!-- 主键锁定配置弹窗 -->
		<DialogComp
			v-model:visible="showLockConfigDialog"
			:title="currentRow && currentRow.lockConfig && currentRow.lockConfig.isLocked && isKeyLocked(currentRow) ? '主键锁定 - 当前已锁定' : '主键锁定'"
			width="600px"
			@closed="showLockConfigDialog = false"
			:visible-close-button="false"
			:visible-confirm-button="false"
		>
			<template #body>
				<div class="lock-config-content">
					<div class="config-description">
						<span v-if="currentRow && currentRow.lockConfig && currentRow.lockConfig.isLocked && isKeyLocked(currentRow)">
							当前主键已被锁定，显示剩余锁定时间。您可以修改锁定时间或解锁。
						</span>
						<span v-else>
							根据设置的时间进行锁定主键，锁定时间到达后自动解锁
						</span>
					</div>

					<el-form
						:model="lockTimeForm"
						label-width="100px"
						class="lock-config-form"
					>
						<el-form-item label="锁定时间" required>
							<div class="time-input-group">
								<el-input-number
									v-model="lockTimeForm.hours"
									:min="0"
									:max="23"
									placeholder="请输入"
									class="time-input"
								/>
								<span class="time-label">时</span>

								<el-input-number
									v-model="lockTimeForm.minutes"
									:min="0"
									:max="59"
									placeholder="请输入"
									class="time-input"
								/>
								<span class="time-label">分</span>

								<el-input-number
									v-model="lockTimeForm.seconds"
									:min="0"
									:max="59"
									placeholder="请输入"
									class="time-input"
								/>
								<span class="time-label">秒</span>
							</div>
						</el-form-item>

						<el-form-item label="锁定原因">
							<el-input
								v-model="currentLockConfig.lockReason"
								type="textarea"
								:rows="3"
								placeholder="请输入锁定原因（可选）"
							/>
						</el-form-item>
					</el-form>
				</div>
			</template>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="showLockConfigDialog = false">取消</el-button>
					<el-button
						v-if="currentRow && currentRow.lockConfig && currentRow.lockConfig.isLocked && isKeyLocked(currentRow)"
						type="warning"
						@click="handleUnlockKey"
					>
						立即解锁
					</el-button>
					<el-button type="primary" @click="handleConfirmLockConfig">确定</el-button>
				</div>
			</template>
		</DialogComp>

		<!-- 主键索引配置弹窗 -->
		<DialogComp
			v-model:visible="showIndexConfigDialog"
			title="主键索引规则配置"
			width="500px"
			@click-confirm="handleConfirmIndexConfig"
			@click-cancel="showIndexConfigDialog = false"
			@closed="showIndexConfigDialog = false"
		>
			<template #body>
				<div class="index-config-content">
					<div class="config-section">
						<div class="section-subtitle">
							选择字段作为主键索引
						</div>

						<el-form
							:model="currentIndexConfig"
							label-width="150px"
							class="index-config-form"
						>
							<el-form-item label="主键索引规则配置：" required>
								<el-select
									v-model="currentIndexConfig.indexField"
									placeholder="请选择"
									style="width: 100%"
								>
									<el-option
										v-for="option in IndexFieldOptions"
										:key="option.value"
										:label="option.label"
										:value="option.value"
									/>
								</el-select>
							</el-form-item>
						</el-form>
					</div>
				</div>
			</template>
		</DialogComp>

		<!-- 复合主键配置弹窗 -->
		<DialogComp
			v-model:visible="showCompositeKeyConfigDialog"
			title="复合主键规则配置"
			width="500px"
			@click-confirm="handleConfirmCompositeKeyConfig"
			@click-cancel="showCompositeKeyConfigDialog = false"
			@closed="showCompositeKeyConfigDialog = false"
		>
			<template #body>
				<div class="composite-key-config-content">
					<div class="config-section">
						<div class="section-title">
							<span>复合主键规则配置：</span>
						</div>

						<div class="checkbox-container">
							<div class="composite-checkbox-group">
								<el-checkbox
									v-model="currentCompositeKeyConfig.integerType"
									label="整数类型"
									class="composite-checkbox"
								/>
								<el-checkbox
									v-model="currentCompositeKeyConfig.floatType"
									label="浮点数类型"
									class="composite-checkbox"
								/>
								<el-checkbox
									v-model="currentCompositeKeyConfig.stringType"
									label="字符串类型"
									class="composite-checkbox"
								/>
								<el-checkbox
									v-model="currentCompositeKeyConfig.dateTimeType"
									label="日期时间类型"
									class="composite-checkbox"
								/>
							</div>
						</div>
					</div>
				</div>
			</template>
		</DialogComp>

		<!-- 主键变更记录配置弹窗 -->
		<DialogComp
			v-model:visible="showHistoryColumnConfigDialog"
			title="主键变更记录配置"
			width="600px"
			@click-confirm="handleConfirmHistoryColumnConfig"
			@click-cancel="showHistoryColumnConfigDialog = false"
			@closed="showHistoryColumnConfigDialog = false"
		>
			<template #body>
				<div class="history-column-config-content">
					<div class="config-header">
						<div class="config-tip">
							<el-icon class="tip-icon"><InfoFilled /></el-icon>
							<span>勾选变更对于的字段后，查看时就显示对应的字段</span>
						</div>
					</div>

					<div class="config-section">
						<div class="section-title">
							<span>变更记录选择项：</span>
							<div class="select-all-controls">
								<el-button
									size="small"
									type="primary"
									link
									@click="handleSelectAllHistoryColumns(true)"
								>
									全选
								</el-button>
								<el-button
									size="small"
									type="primary"
									link
									@click="handleSelectAllHistoryColumns(false)"
								>
									取消全选
								</el-button>
							</div>
						</div>

						<div class="checkbox-container">
							<div class="column-checkbox-group">
								<el-checkbox
									v-model="historyColumnConfig.sequence"
									label="序号"
									class="column-checkbox"
								/>
								<el-checkbox
									v-model="historyColumnConfig.primaryKeyName"
									label="主键名称"
									class="column-checkbox"
								/>
								<el-checkbox
									v-model="historyColumnConfig.oldDataType"
									label="原数据类型"
									class="column-checkbox"
								/>
								<el-checkbox
									v-model="historyColumnConfig.newDataType"
									label="新数据类型"
									class="column-checkbox"
								/>
								<el-checkbox
									v-model="historyColumnConfig.operatorName"
									label="操作人"
									class="column-checkbox"
								/>
								<el-checkbox
									v-model="historyColumnConfig.operationTime"
									label="操作时间"
									class="column-checkbox"
								/>
								<el-checkbox
									v-model="historyColumnConfig.operationType"
									label="操作类型"
									class="column-checkbox"
								/>
							</div>
						</div>
					</div>
				</div>
			</template>
		</DialogComp>

		<!-- 主键与外键关联配置弹窗 -->
		<DialogComp
			v-model:visible="showForeignKeyRelationDialog"
			title="主键与外键关联规则"
			width="600px"
			@click-confirm="handleConfirmForeignKeyRelation"
			@click-cancel="showForeignKeyRelationDialog = false"
			@closed="showForeignKeyRelationDialog = false"
		>
			<template #body>
				<div class="foreign-key-relation-content">
					<el-form
						:model="currentForeignKeyRelationConfig"
						label-width="100px"
						class="foreign-key-relation-form"
					>
						<el-form-item label="主表" required>
							<el-select
								v-model="currentForeignKeyRelationConfig.masterTable"
								placeholder="请选择主表字段"
								style="width: 100%"
								clearable
							>
								<el-option
									v-for="option in masterTableFieldOptions"
									:key="option.value"
									:label="option.label"
									:value="option.value"
								/>
							</el-select>
						</el-form-item>

						<el-form-item label="从表" required>
							<el-select
								v-model="currentForeignKeyRelationConfig.slaveTable"
								placeholder="请选择从表字段"
								style="width: 100%"
								clearable
							>
								<el-option
									v-for="option in slaveTableFieldOptions"
									:key="option.value"
									:label="option.label"
									:value="option.value"
								/>
							</el-select>
						</el-form-item>

						<el-form-item label="级联操作">
							<div class="cascade-options">
								<el-checkbox
									v-model="currentForeignKeyRelationConfig.cascadeDelete"
									label="级联删除"
									class="cascade-checkbox"
								/>
								<el-checkbox
									v-model="currentForeignKeyRelationConfig.cascadeUpdate"
									label="级联更新"
									class="cascade-checkbox"
								/>
								<el-checkbox
									v-model="currentForeignKeyRelationConfig.setNull"
									label="设为空"
									class="cascade-checkbox"
								/>
							</div>
						</el-form-item>
					</el-form>
				</div>
			</template>
		</DialogComp>

		<!-- 主键更改权限配置弹窗 -->
		<DialogComp
			v-model:visible="showChangePermissionDialog"
			title="主键更改权限配置"
			width="600px"
			@click-confirm="handleConfirmChangePermissionConfig"
			@click-cancel="handleCancelChangePermissionConfig"
			@closed="handleCancelChangePermissionConfig"
		>
			<template #body>
				<div class="change-permission-content">
					<el-form
						:model="changePermissionConfig"
						label-width="120px"
						class="change-permission-form"
					>
						<el-form-item label="授权人员" required>
							<el-select
								v-model="changePermissionConfig.authorizedPersons"
								placeholder="请选择授权人员"
								style="width: 100%"
								multiple
								clearable
								collapse-tags
								collapse-tags-tooltip
							>
								<el-option
									v-for="option in authorizedPersonOptions"
									:key="option.value"
									:label="option.label"
									:value="option.value"
								/>
							</el-select>
							<div class="form-tip">
								选择有权限修改主键配置的人员，支持多选
							</div>
						</el-form-item>
					</el-form>
				</div>
			</template>
		</DialogComp>

		<!-- 主键动态扩展配置弹窗 -->
		<DialogComp
			v-model:visible="showDynamicExtensionDialog"
			title="主键动态扩展配置"
			width="700px"
			@click-confirm="handleConfirmDynamicExtensionConfig"
			@click-cancel="showDynamicExtensionDialog = false"
			@closed="showDynamicExtensionDialog = false"
		>
			<template #body>
				<div class="dynamic-extension-content">
					<el-form
						:model="currentDynamicExtensionConfig"
						label-width="120px"
						class="dynamic-extension-form"
					>
						<el-form-item label="动态扩展字段" required>
							<el-select
								v-model="currentDynamicExtensionConfig.extensionField"
								placeholder="请选择动态扩展字段"
								style="width: 100%"
								clearable
							>
								<el-option
									v-for="option in extensionFieldOptions"
									:key="option.value"
									:label="option.label"
									:value="option.value"
								/>
							</el-select>
						</el-form-item>

						<el-form-item label="扩展类型" required>
							<el-select
								v-model="currentDynamicExtensionConfig.extensionType"
								placeholder="请选择扩展类型"
								style="width: 100%"
								clearable
							>
								<el-option
									v-for="option in extensionTypeOptions"
									:key="option.value"
									:label="option.label"
									:value="option.value"
								/>
							</el-select>
						</el-form-item>

						<el-form-item label="是否必填">
							<el-switch
								v-model="currentDynamicExtensionConfig.isRequired"
								active-text="必填"
								inactive-text="非必填"
							/>
						</el-form-item>

						<el-form-item label="默认值">
							<el-input
								v-model="currentDynamicExtensionConfig.defaultValue"
								placeholder="请输入默认值"
								clearable
							/>
						</el-form-item>

						<el-form-item label="字段描述">
							<el-input
								v-model="currentDynamicExtensionConfig.fieldDescription"
								type="textarea"
								:rows="3"
								placeholder="请输入字段描述"
								maxlength="200"
								show-word-limit
							/>
						</el-form-item>
					</el-form>
				</div>
			</template>
		</DialogComp>

		<!-- 主键动态调整弹窗 -->
		<DialogComp
			v-model:visible="showDynamicAdjustDialog"
			title="主键动态调整"
			width="500px"
			@click-confirm="handleConfirmDynamicAdjust"
			@click-cancel="handleCancelDynamicAdjust"
			@closed="handleCancelDynamicAdjust"
		>
			<template #body>
				<div class="dynamic-adjust-content">
					<el-form
						:model="dynamicAdjustForm"
						label-width="120px"
						class="dynamic-adjust-form"
					>
						<el-form-item label="新主键名称" required>
							<el-input
								v-model="dynamicAdjustForm.newName"
								placeholder="请输入新的主键名称"
								clearable
								maxlength="50"
								show-word-limit
							/>
							<div class="form-tip">
								请输入新的主键名称，不能与其他主键名称重复
							</div>
						</el-form-item>
					</el-form>
				</div>
			</template>
		</DialogComp>

		<!-- 主键缓存配置弹窗 -->
		<DialogComp
			v-model:visible="showCacheConfigDialog"
			title="主键缓存机制"
			width="500px"
			@click-confirm="handleConfirmCacheConfig"
			@click-cancel="showCacheConfigDialog = false"
			@closed="showCacheConfigDialog = false"
		>
			<template #body>
				<div class="cache-config-content">
					<div class="config-description">
						<span>关闭后不启用缓存，到达设置的缓存有效期后，结束缓存</span>
					</div>
					<el-form
						:model="currentCacheConfig"
						label-width="100px"
						class="cache-config-form"
					>
						<el-form-item label="启用缓存">
							<el-switch
								v-model="currentCacheConfig.enabled"
								active-text="启用"
								inactive-text="关闭"
							/>
						</el-form-item>
						<el-form-item
							label="缓存有效期（秒）"
							v-if="currentCacheConfig.enabled"
							required
						>
							<el-input
								v-model.number="currentCacheConfig.expireTime"
								placeholder="请输入"
								type="number"
								:min="1"
								style="width: 100%"
							/>
						</el-form-item>
					</el-form>
				</div>
			</template>
		</DialogComp>

		<!-- 主键关联数据同步配置弹窗 -->
		<DialogComp
			v-model:visible="showDataSyncConfigDialog"
			title="主键关联数据同步"
			width="500px"
			@click-confirm="handleConfirmDataSyncConfig"
			@click-cancel="showDataSyncConfigDialog = false"
			@closed="showDataSyncConfigDialog = false"
		>
			<template #body>
				<div class="data-sync-config-content">
					<el-form
						:model="currentDataSyncConfig"
						label-width="120px"
						class="data-sync-config-form"
					>
						<el-form-item label="关联数据同步：" required>
							<el-select
								v-model="currentDataSyncConfig.syncField"
								placeholder="字段1"
								style="width: 100%"
							>
								<el-option
									v-for="option in dataSyncFieldOptions"
									:key="option.value"
									:label="option.label"
									:value="option.value"
								/>
							</el-select>
						</el-form-item>
					</el-form>
				</div>
			</template>
		</DialogComp>

		<!-- 主键可视化标识配置弹窗 -->
		<DialogComp
			v-model:visible="showVisualIdentifierDialog"
			title="主键可视化标识"
			width="500px"
			@click-confirm="handleConfirmVisualIdentifierConfig"
			@click-cancel="handleCancelVisualIdentifierConfig"
			@closed="handleCancelVisualIdentifierConfig"
		>
			<template #body>
				<div class="visual-identifier-config-content">
					<el-form
						:model="currentVisualIdentifierConfig"
						label-width="120px"
						class="visual-identifier-config-form"
					>
						<el-form-item label="启用主键标识">
							<el-switch
								v-model="currentVisualIdentifierConfig.enabled"
								active-text="启用"
								inactive-text="禁用"
							/>
						</el-form-item>

						<template v-if="currentVisualIdentifierConfig.enabled">
							<el-form-item label="标识颜色" required>
								<el-color-picker
									v-model="currentVisualIdentifierConfig.color"
									show-alpha
									:predefine="['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']"
								/>
							</el-form-item>

							<el-form-item label="字体大小" required>
								<el-input-number
									v-model="currentVisualIdentifierConfig.fontSize"
									:min="10"
									:max="24"
									:step="1"
									controls-position="right"
									style="width: 120px"
								/>
								<span style="margin-left: 8px; color: #606266;">px</span>
							</el-form-item>

							<el-form-item label="预览效果">
								<div class="preview-container">
									<span
										class="preview-text"
										:style="{
											color: currentVisualIdentifierConfig.color,
											fontSize: `${currentVisualIdentifierConfig.fontSize}px`,
											fontWeight: 'bold'
										}"
									>
										{{ currentRow?.name || '示例主键名称' }}
									</span>
								</div>
							</el-form-item>
						</template>
					</el-form>
				</div>
			</template>
		</DialogComp>

		<!-- 主键加密设置弹窗 -->
		<DialogComp
			v-model:visible="showEncryptionConfigDialog"
			title="加密设置"
			width="500px"
			@click-confirm="handleConfirmEncryptionConfig"
			@click-cancel="handleCancelEncryptionConfig"
			@closed="handleCancelEncryptionConfig"
		>
			<template #body>
				<div class="encryption-config-content">
					<el-form
						ref="encryptionFormRef"
						:model="encryptionConfigForm"
						:rules="encryptionFormRules"
						label-width="120px"
						class="encryption-config-form"
					>
						<el-form-item label="加密类型" prop="encryptionType" required>
							<el-select
								v-model="encryptionConfigForm.encryptionType"
								placeholder="请选择加密类型"
								style="width: 100%"
							>
								<el-option label="AES" value="AES" />
								<el-option label="RSA" value="RSA" />
								<el-option label="MD5" value="MD5" />
							</el-select>
						</el-form-item>

						<el-form-item label="加密密钥" prop="encryptionKey" required>
							<el-input
								v-model="encryptionConfigForm.encryptionKey"
								type="password"
								placeholder="请输入加密密钥"
								show-password
								clearable
								maxlength="100"
								show-word-limit
							/>
						</el-form-item>

						<el-form-item label="描述" prop="description">
							<el-input
								v-model="encryptionConfigForm.description"
								type="textarea"
								placeholder="请输入描述信息（可选）"
								:rows="3"
								maxlength="200"
								show-word-limit
							/>
						</el-form-item>
					</el-form>
				</div>
			</template>
		</DialogComp>
	</div>
</template>

<style scoped lang="scss">
.primary-key-definition {
    height: 100%;

    :deep(.el-table) {
        width: 100% !important;
    }

    :deep(.el-table__body),
    :deep(.el-table__header) {
        width: 100% !important;
    }
}

// 批量导入弹窗样式
.import-container {
    padding: 20px;

    .import-header {
        margin-bottom: 20px;

        .import-tip {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background-color: #f0f9ff;
            border: 1px solid #409EFF;
            border-radius: 4px;
            color: #409EFF;
            font-size: 14px;

            .tip-icon {
                margin-right: 8px;
                font-size: 16px;
            }
        }
    }

    .import-notice-section {
        margin-bottom: 24px;

        .notice-title {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            .notice-text {
                font-size: 16px;
                font-weight: 600;
                color: #303133;
                margin-right: 8px;
            }

            .notice-icon {
                color: #409EFF;
                cursor: pointer;
                font-size: 16px;
                transition: color 0.3s ease;

                &:hover {
                    color: #66b1ff;
                }
            }
        }
    }

    .import-steps {
        margin-bottom: 24px;

        .steps-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 16px;
        }

        .steps-container {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            background-color: #fafafa;
            border-radius: 8px;

            .step-item {
                display: flex;
                flex-direction: column;
                align-items: center;

                .step-number {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    background-color: #409EFF;
                    color: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: bold;
                    margin-bottom: 8px;
                }

                .step-title {
                    font-size: 14px;
                    color: #606266;
                    white-space: nowrap;
                }
            }

            .step-arrow {
                margin: 0 24px;
                color: #409EFF;
                font-size: 18px;
                font-weight: bold;
            }
        }
    }

    .download-template-section {
        margin-bottom: 24px;
        text-align: center;

        .download-btn {
            padding: 12px 24px;
            font-size: 14px;
        }
    }

    .upload-section {
        .upload-dragger {
            :deep(.el-upload-dragger) {
                border: 2px dashed #d9d9d9;
                border-radius: 8px;
                padding: 40px 20px;
                text-align: center;
                background-color: #fafafa;
                transition: all 0.3s ease;

                &:hover {
                    border-color: #409EFF;
                    background-color: #f0f9ff;
                }
            }

            .upload-content {
                .upload-icon-container {
                    margin-bottom: 16px;

                    .upload-icon {
                        font-size: 48px;
                        color: #409EFF;
                    }
                }

                .upload-text {
                    font-size: 16px;
                    color: #606266;
                    margin-bottom: 8px;
                    font-weight: 500;
                }

                .upload-hint {
                    font-size: 14px;
                    color: #909399;
                    line-height: 1.5;
                    max-width: 400px;
                    margin: 0 auto;
                }
            }
        }
    }
}

// 导入须知提示框样式
:deep(.import-notice-tooltip) {
    max-width: 400px;

    .tooltip-content {
        p {
            margin: 0 0 8px 0;
            line-height: 1.6;

            &:last-child {
                margin-bottom: 0;
            }

            strong {
                color: #409EFF;
            }
        }
    }
}

// 表格操作按钮样式
.table-operation-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;

    .el-button {
        margin: 0;
    }
}

// 主键认证策略弹窗样式
.auth-strategy-content {
    padding: 20px;

    .auth-strategy-form {
        .el-form-item {
            margin-bottom: 24px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        :deep(.el-form-item__label) {
            color: #303133;
            font-weight: 500;
        }

        :deep(.el-switch) {
            .el-switch__core {
                width: 40px;
                height: 20px;
                border-radius: 10px;
            }
        }

        .validation-rules {
            display: flex;
            flex-direction: column;
            gap: 12px;

            .el-checkbox {
                margin-right: 0;

                :deep(.el-checkbox__label) {
                    color: #606266;
                    font-size: 14px;
                }

                &.is-checked {
                    :deep(.el-checkbox__label) {
                        color: #409eff;
                        font-weight: 500;
                    }
                }
            }
        }

		.validation-description-el-form-item {
			margin-left: -156px;
		}

        .validation-description {
            background-color: #f5f7fa;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            padding: 16px;
            margin-top: 8px;
			margin-left: 20px;

            p {
                margin: 0 0 8px 0;
                color: #303133;
                font-weight: 500;
            }

            ul {
                margin: 0;
                padding-left: 20px;
                color: #606266;
                font-size: 14px;
                line-height: 1.6;

                li {
                    margin-bottom: 4px;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    strong {
                        color: #409eff;
                    }
                }
            }
        }
    }
}

// 主键自动生成配置弹窗样式
.auto-gen-config-content {
    padding: 20px;

    .auto-gen-config-form {
        .el-form-item {
            margin-bottom: 24px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        :deep(.el-form-item__label) {
            color: #303133;
            font-weight: 500;
        }

        :deep(.el-select) {
            .el-input__wrapper {
                border-radius: 4px;
            }
        }

        :deep(.el-input-number) {
            .el-input__wrapper {
                border-radius: 4px;
            }
        }

        :deep(.el-radio-group) {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;

            .el-radio {
                margin-right: 0;
                margin-bottom: 8px;

                .el-radio__label {
                    color: #606266;
                    font-size: 14px;
                }

                &.is-checked {
                    .el-radio__label {
                        color: #409eff;
                        font-weight: 500;
                    }
                }
            }
        }

        :deep(.el-checkbox-group) {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;

            .el-checkbox {
                margin-right: 0;
                margin-bottom: 8px;

                .el-checkbox__label {
                    color: #606266;
                    font-size: 14px;
                }

                &.is-checked {
                    .el-checkbox__label {
                        color: #409eff;
                        font-weight: 500;
                    }
                }
            }
        }
    }
}

// 主键统计弹窗样式
.primary-key-stats-content {
    padding: 20px;

    .stats-overview {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
        margin-bottom: 24px;

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;

            &:hover {
                transform: translateY(-2px);
            }

            &:nth-child(2) {
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            }

            &:nth-child(3) {
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            }

            &:nth-child(4) {
                background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            }

            .stats-number {
                font-size: 28px;
                font-weight: bold;
                margin-bottom: 8px;
                line-height: 1;
            }

            .stats-label {
                font-size: 14px;
                opacity: 0.9;
                font-weight: 500;
            }
        }
    }

    .stats-table-container {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        :deep(.el-table) {
            .el-table__header {
                background-color: #f8f9fa;

                th {
                    background-color: #f8f9fa !important;
                    color: #303133;
                    font-weight: 600;
                    border-bottom: 2px solid #e4e7ed;
                }
            }

            .el-table__body {
                tr:hover {
                    background-color: #f5f7fa;
                }

                td {
                    border-bottom: 1px solid #ebeef5;
                    padding: 12px 0;
                }
            }
        }

        :deep(.el-tag) {
            font-weight: 500;
            border-radius: 12px;
            padding: 4px 12px;
        }
    }
}

// 主键函数验证弹窗样式
.primary-key-validation-content {
    padding: 20px;

    .primary-key-validation-form {
        .el-form-item {
            margin-bottom: 24px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        :deep(.el-form-item__label) {
            color: #303133;
            font-weight: 500;
        }

        :deep(.el-switch) {
            .el-switch__core {
                width: 40px;
                height: 20px;
                border-radius: 10px;
            }

            .el-switch__action {
                width: 16px;
                height: 16px;
            }
        }

        :deep(.el-select) {
            .el-input__wrapper {
                border-radius: 4px;
            }
        }
    }
}

// 主键备份策略弹窗样式
.backup-strategy-content {
    padding: 20px;

    .backup-section, .restore-section {
        margin-bottom: 24px;

        &:last-child {
            margin-bottom: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e4e7ed;
        }

        .backup-strategy-form, .restore-strategy-form {
            .el-form-item {
                margin-bottom: 20px;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            :deep(.el-form-item__label) {
                color: #303133;
                font-weight: 500;
            }

            :deep(.el-select) {
                .el-input__wrapper {
                    border-radius: 4px;
                }
            }

            :deep(.el-date-editor) {
                width: 100%;
            }
        }
    }
}

// 主键锁定配置弹窗样式
.lock-config-content {
    padding: 20px;

    .config-description {
        font-size: 14px;
        color: #606266;
        margin-bottom: 24px;
        text-align: left;
        line-height: 1.5;
    }

    .lock-config-form {
        .el-form-item {
            margin-bottom: 24px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        :deep(.el-form-item__label) {
            color: #303133;
            font-weight: 500;
        }

        .time-input-group {
            display: flex;
            align-items: center;
            gap: 8px;

            .time-input {
                width: 120px;
            }

            .time-label {
                font-size: 14px;
                color: #606266;
                margin-right: 8px;
            }
        }
    }
}

// 主键名称列锁定图标样式
.name-cell {
    display: flex;
    align-items: center;
    gap: 6px;

    .lock-icon {
        color: #f56c6c;
        font-size: 14px;
    }
}

// 主键索引配置弹窗样式
.index-config-content {
    padding: 20px;

    .config-section {
        .section-subtitle {
            font-size: 14px;
            color: #606266;
            margin-bottom: 20px;
            text-align: left;
        }

        .index-config-form {
            .el-form-item {
                margin-bottom: 20px;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            :deep(.el-form-item__label) {
                color: #303133;
                font-weight: 500;
            }

            :deep(.el-select) {
                .el-input__wrapper {
                    border-radius: 4px;
                }
            }
        }
    }
}

// 复合主键配置弹窗样式
.composite-key-config-content {
    padding: 20px;

    .config-section {
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 16px;
        }

        .checkbox-container {
            .composite-checkbox-group {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
                padding: 16px;
                background-color: #fafafa;
                border-radius: 8px;
                border: 1px solid #e4e7ed;

                .composite-checkbox {
                    margin: 0;

                    :deep(.el-checkbox__label) {
                        color: #606266;
                        font-size: 14px;
                        font-weight: 500;
                    }

                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #409eff;
                        border-color: #409eff;
                    }

                    :deep(.el-checkbox__inner:hover) {
                        border-color: #409eff;
                    }
                }
            }
        }
    }
}

// 主键变更记录配置弹窗样式
.history-column-config-content {
    padding: 20px;

    .config-header {
        margin-bottom: 24px;

        .config-tip {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background-color: #f0f9ff;
            border: 1px solid #409EFF;
            border-radius: 4px;
            color: #409EFF;
            font-size: 14px;

            .tip-icon {
                margin-right: 8px;
                font-size: 16px;
            }
        }
    }

    .config-section {
        .section-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            font-size: 16px;
            font-weight: 600;
            color: #303133;

            .select-all-controls {
                display: flex;
                gap: 8px;
            }
        }

        .checkbox-container {
            .column-checkbox-group {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 16px;
                padding: 16px;
                background-color: #fafafa;
                border-radius: 8px;
                border: 1px solid #e4e7ed;

                .column-checkbox {
                    margin: 0;

                    :deep(.el-checkbox__label) {
                        color: #606266;
                        font-size: 14px;
                        font-weight: 500;
                    }

                    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                        background-color: #409eff;
                        border-color: #409eff;
                    }

                    :deep(.el-checkbox__inner:hover) {
                        border-color: #409eff;
                    }
                }
            }
        }
    }
}

// 主键唯一性配置弹窗样式
.uniqueness-config-content {
    padding: 20px;

    .config-tip {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        margin-bottom: 24px;
        background-color: #f0f9ff;
        border: 1px solid #409EFF;
        border-radius: 4px;
        color: #409EFF;
        font-size: 14px;

        .tip-icon {
            margin-right: 8px;
            font-size: 16px;
        }
    }

    .config-options {
        .el-form-item {
            margin-bottom: 20px;

            :deep(.el-form-item__label) {
                font-weight: 500;
                color: #303133;
            }

            :deep(.el-checkbox) {
                .el-checkbox__label {
                    font-size: 14px;
                    color: #606266;
                }

                &.is-disabled {
                    .el-checkbox__label {
                        color: #C0C4CC;
                    }

                    .el-checkbox__inner {
                        background-color: #F5F7FA;
                        border-color: #E4E7ED;
                        cursor: not-allowed;

                        &::after {
                            border-color: #C0C4CC;
                        }
                    }
                }

                &.is-checked.is-disabled {
                    .el-checkbox__inner {
                        background-color: #F0F2F5;
                        border-color: #D3D4D6;

                        &::after {
                            border-color: #8C939D;
                        }
                    }
                }
            }
        }
    }
}

// 主键与外键关联配置弹窗样式
.foreign-key-relation-content {
    padding: 20px;

    .foreign-key-relation-form {
        .el-form-item {
            margin-bottom: 20px;

            &:last-child {
                margin-bottom: 0;
            }

            .el-form-item__label {
                font-weight: 500;
                color: #303133;
            }

            .el-select {
                width: 100%;
            }

            .cascade-options {
                display: flex;
                flex-direction: column;
                gap: 12px;

                .cascade-checkbox {
                    margin: 0;

                    :deep(.el-checkbox__label) {
                        font-size: 14px;
                        color: #606266;
                    }
                }
            }
        }
    }
}

// 主键更改权限配置弹窗样式
.change-permission-content {
    padding: 20px;

    .change-permission-form {
        .el-form-item {
            margin-bottom: 20px;

            &:last-child {
                margin-bottom: 0;
            }

            .el-form-item__label {
                font-weight: 500;
                color: #303133;
            }

            .el-select {
                width: 100%;
            }

            .form-tip {
                margin-top: 8px;
                font-size: 12px;
                color: #909399;
                line-height: 1.4;
            }
        }
    }
}

// 主键动态调整弹窗样式
.dynamic-adjust-content {
    padding: 20px;

    .dynamic-adjust-form {
        .el-form-item {
            margin-bottom: 20px;

            &:last-child {
                margin-bottom: 0;
            }

            .el-form-item__label {
                font-weight: 500;
                color: #303133;
            }

            .form-tip {
                margin-top: 8px;
                font-size: 12px;
                color: #909399;
                line-height: 1.4;
            }
        }
    }
}

// 主键动态扩展配置弹窗样式
.dynamic-extension-content {
    padding: 20px;

    .dynamic-extension-form {
        .el-form-item {
            margin-bottom: 20px;

            &:last-child {
                margin-bottom: 0;
            }

            .el-form-item__label {
                font-weight: 500;
                color: #303133;
            }

            .el-select,
            .el-input {
                width: 100%;
            }

            .el-switch {
                .el-switch__label {
                    font-size: 14px;
                    color: #606266;
                }
            }
        }
    }
}

// 主键缓存配置弹窗样式
.cache-config-content {
    padding: 20px;

    .config-description {
        margin-bottom: 20px;
        padding: 12px;
        background-color: #f5f7fa;
        border-radius: 4px;
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
    }

    .cache-config-form {
        .el-form-item {
            margin-bottom: 20px;

            &:last-child {
                margin-bottom: 0;
            }

            .el-form-item__label {
                font-weight: 500;
                color: #303133;
            }

            .el-input {
                width: 100%;
            }
        }
    }
}

// 主键关联数据同步配置弹窗样式
.data-sync-config-content {
    padding: 20px;

    .data-sync-config-form {
        .el-form-item {
            margin-bottom: 20px;

            &:last-child {
                margin-bottom: 0;
            }

            .el-form-item__label {
                font-weight: 500;
                color: #303133;
            }

            .el-select {
                width: 100%;
            }
        }
    }
}

// 主键可视化标识配置弹窗样式
.visual-identifier-config-content {
    padding: 20px;

    .visual-identifier-config-form {
        .el-form-item {
            margin-bottom: 20px;

            &:last-child {
                margin-bottom: 0;
            }

            .el-form-item__label {
                font-weight: 500;
                color: #303133;
            }

            .el-switch {
                .el-switch__label {
                    font-size: 14px;
                    color: #606266;
                }
            }

            .preview-container {
                padding: 12px 16px;
                background-color: #f5f7fa;
                border: 1px solid #e4e7ed;
                border-radius: 4px;
                min-height: 40px;
                display: flex;
                align-items: center;

                .preview-text {
                    transition: all 0.3s ease;
                }
            }
        }
    }
}

// 主键加密设置弹窗样式
.encryption-config-content {
    padding: 20px;

    .encryption-config-form {
        .el-form-item {
            margin-bottom: 20px;

            &:last-child {
                margin-bottom: 0;
            }

            .el-form-item__label {
                font-weight: 500;
                color: #303133;
            }

            .el-select,
            .el-input {
                width: 100%;
            }

            .el-textarea {
                .el-textarea__inner {
                    resize: vertical;
                }
            }
        }
    }
}
</style>
