import { config } from '@vue/test-utils'
import { vi } from 'vitest'

// Mock global properties
config.global.mocks = {
  $t: (key: string) => key,
  $route: {
    path: '/primary-key-definition',
    name: 'PrimaryKeyDefinition'
  },
  $router: {
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn()
  }
}

// Mock global components
config.global.stubs = {
  'router-link': true,
  'router-view': true,
  'el-config-provider': true
}

// Setup global test environment
beforeEach(() => {
  vi.clearAllMocks()
})
