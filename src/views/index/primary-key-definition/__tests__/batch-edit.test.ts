import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElMessage } from 'element-plus'
import PrimaryKeyDefinition from '../index.vue'
import type { PrimaryKeyData } from '@/define/primary-key.define'

// Mock Element Plus Message
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn()
  },
  ElMessageBox: {
    confirm: vi.fn()
  }
}))

// Mock composables
vi.mock('../composables/usePrimaryKeyStorage', () => ({
  usePrimaryKeyStorage: () => ({
    primaryKeys: vi.fn().mockReturnValue([]),
    rawPrimaryKeys: vi.fn().mockReturnValue([]),
    cleanupRule: vi.fn().mockReturnValue({}),
    sortConfig: vi.fn().mockReturnValue({}),
    selectedKeys: vi.fn().mockReturnValue([]),
    addPrimaryKey: vi.fn(),
    updatePrimaryKey: vi.fn(),
    deletePrimaryKey: vi.fn(),
    batchUpdatePrimaryKeys: vi.fn().mockReturnValue(true),
    updateCleanupRule: vi.fn(),
    updateSortConfig: vi.fn(),
    searchPrimaryKeys: vi.fn(),
    clearAllData: vi.fn(),
    batchAddPrimaryKeys: vi.fn()
  })
}))

vi.mock('../composables/usePrimaryKeyExcel', () => ({
  usePrimaryKeyExcel: () => ({
    isImporting: vi.fn().mockReturnValue(false),
    isExporting: vi.fn().mockReturnValue(false),
    exportToExcel: vi.fn(),
    importFromExcel: vi.fn(),
    generateTemplate: vi.fn()
  })
}))

// Mock global components
const mockComponents = {
  Block: {
    template: '<div><slot name="topRight"></slot><slot></slot></div>'
  },
  Form: {
    template: '<div></div>'
  },
  TableV2: {
    template: '<div></div>',
    props: ['enableSelection', 'defaultTableData', 'columns', 'buttons'],
    emits: ['selection-change', 'click-button']
  },
  Pagination: {
    template: '<div></div>'
  },
  DialogComp: {
    template: '<div v-if="visible"><slot name="body"></slot></div>',
    props: ['visible', 'title', 'width'],
    emits: ['click-confirm', 'click-cancel']
  }
}

describe('PrimaryKeyDefinition - 批量修改功能', () => {
  let wrapper: any
  
  const mockPrimaryKeys: PrimaryKeyData[] = [
    {
      id: '1',
      sequence: 1,
      name: '主键1',
      dataType: '字符型',
      minLength: 1,
      maxLength: 10,
      description: '测试主键1',
      isActive: true,
      updateNotification: false,
      encryptionLevel: '',
      encryptionContent: '',
      createTime: new Date(),
      updateTime: new Date()
    },
    {
      id: '2',
      sequence: 2,
      name: '主键2',
      dataType: '数值型',
      minLength: 1,
      maxLength: 5,
      description: '测试主键2',
      isActive: true,
      updateNotification: false,
      encryptionLevel: '',
      encryptionContent: '',
      createTime: new Date(),
      updateTime: new Date()
    }
  ]

  beforeEach(() => {
    wrapper = mount(PrimaryKeyDefinition, {
      global: {
        components: mockComponents,
        stubs: {
          'el-button': true,
          'el-form': true,
          'el-form-item': true,
          'el-select': true,
          'el-option': true,
          'el-input-number': true
        }
      }
    })
  })

  describe('批量修改按钮', () => {
    it('应该在页面中显示批量修改按钮', () => {
      const batchEditButton = wrapper.find('[data-test="batch-edit-button"]')
      expect(batchEditButton.exists()).toBe(true)
    })

    it('未选择任何行时，批量修改按钮应该被禁用', async () => {
      const batchEditButton = wrapper.find('el-button[type="warning"]')
      expect(batchEditButton.attributes('disabled')).toBeDefined()
    })

    it('选择行后，批量修改按钮应该被启用', async () => {
      // 模拟选择行
      await wrapper.vm.handleSelectionChange(mockPrimaryKeys)
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.selectedRows.length).toBe(2)
      const batchEditButton = wrapper.find('el-button[type="warning"]')
      expect(batchEditButton.attributes('disabled')).toBeUndefined()
    })
  })

  describe('表格多选功能', () => {
    it('TableV2 组件应该启用多选功能', () => {
      const tableV2 = wrapper.findComponent({ name: 'TableV2' })
      expect(tableV2.props('enableSelection')).toBe(true)
    })

    it('应该正确处理选择变化事件', async () => {
      await wrapper.vm.handleSelectionChange(mockPrimaryKeys)
      
      expect(wrapper.vm.selectedRows).toEqual(mockPrimaryKeys)
      expect(wrapper.vm.selectedKeys).toEqual(['1', '2'])
    })
  })

  describe('批量修改弹窗', () => {
    beforeEach(async () => {
      // 先选择一些行
      await wrapper.vm.handleSelectionChange(mockPrimaryKeys)
    })

    it('点击批量修改按钮应该打开弹窗', async () => {
      await wrapper.vm.handleBatchEdit()
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.showBatchEditDialog).toBe(true)
    })

    it('弹窗应该显示选中的记录数量', async () => {
      await wrapper.vm.handleBatchEdit()
      await wrapper.vm.$nextTick()
      
      const dialogComp = wrapper.findComponent({ name: 'DialogComp' })
      expect(dialogComp.exists()).toBe(true)
      expect(wrapper.vm.selectedRows.length).toBe(2)
    })

    it('应该重置批量修改表单数据', async () => {
      await wrapper.vm.handleBatchEdit()
      
      expect(wrapper.vm.batchEditForm).toEqual({
        dataType: '',
        minLength: 0,
        maxLength: 0
      })
    })
  })

  describe('批量修改逻辑', () => {
    beforeEach(async () => {
      await wrapper.vm.handleSelectionChange(mockPrimaryKeys)
      await wrapper.vm.handleBatchEdit()
    })

    it('未选择数据类型时应该显示错误信息', async () => {
      wrapper.vm.batchEditForm.dataType = ''
      
      await wrapper.vm.handleConfirmBatchEdit()
      
      expect(ElMessage.error).toHaveBeenCalledWith('请选择数据类型')
    })

    it('成功批量修改后应该显示成功信息', async () => {
      wrapper.vm.batchEditForm = {
        dataType: '字符型',
        minLength: 1,
        maxLength: 20
      }
      
      await wrapper.vm.handleConfirmBatchEdit()
      
      expect(ElMessage.success).toHaveBeenCalledWith('成功批量修改 2 条记录')
      expect(wrapper.vm.showBatchEditDialog).toBe(false)
      expect(wrapper.vm.selectedRows).toEqual([])
      expect(wrapper.vm.selectedKeys).toEqual([])
    })

    it('批量修改失败时应该显示错误信息', async () => {
      // Mock batchUpdatePrimaryKeys 返回 false
      const mockBatchUpdate = vi.fn().mockReturnValue(false)
      wrapper.vm.batchUpdatePrimaryKeys = mockBatchUpdate
      
      wrapper.vm.batchEditForm = {
        dataType: '字符型',
        minLength: 1,
        maxLength: 20
      }
      
      await wrapper.vm.handleConfirmBatchEdit()
      
      expect(ElMessage.error).toHaveBeenCalledWith('批量修改失败')
    })
  })

  describe('用户交互场景', () => {
    it('完整的批量修改流程', async () => {
      // 1. 选择多行
      await wrapper.vm.handleSelectionChange(mockPrimaryKeys)
      expect(wrapper.vm.selectedRows.length).toBe(2)
      
      // 2. 点击批量修改按钮
      await wrapper.vm.handleBatchEdit()
      expect(wrapper.vm.showBatchEditDialog).toBe(true)
      
      // 3. 填写表单
      wrapper.vm.batchEditForm = {
        dataType: '字符型',
        minLength: 5,
        maxLength: 50
      }
      
      // 4. 确认修改
      await wrapper.vm.handleConfirmBatchEdit()
      
      // 5. 验证结果
      expect(ElMessage.success).toHaveBeenCalledWith('成功批量修改 2 条记录')
      expect(wrapper.vm.showBatchEditDialog).toBe(false)
      expect(wrapper.vm.selectedRows).toEqual([])
    })

    it('未选择行时点击批量修改应该显示警告', async () => {
      await wrapper.vm.handleBatchEdit()
      
      expect(ElMessage.warning).toHaveBeenCalledWith('请先选择要修改的主键')
    })
  })

  describe('错误处理', () => {
    it('应该处理批量修改过程中的异常', async () => {
      // Mock 抛出异常
      const mockBatchUpdate = vi.fn().mockImplementation(() => {
        throw new Error('网络错误')
      })
      wrapper.vm.batchUpdatePrimaryKeys = mockBatchUpdate
      
      await wrapper.vm.handleSelectionChange(mockPrimaryKeys)
      wrapper.vm.batchEditForm = {
        dataType: '字符型',
        minLength: 1,
        maxLength: 20
      }
      
      await wrapper.vm.handleConfirmBatchEdit()
      
      expect(ElMessage.error).toHaveBeenCalledWith('批量修改失败，请重试')
    })
  })
})
