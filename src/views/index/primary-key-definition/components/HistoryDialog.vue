<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { HistoryRecord, HistoryColumnConfig } from '@/define/primary-key.define'
import { usePrimaryKeyHistory } from '../composables/usePrimaryKeyHistory'
import { getDefaultHistoryColumnConfig, STORAGE_KEYS } from '@/define/primary-key.define'

interface Props {
	visible: boolean
	primaryKeyId?: string // 如果提供了主键ID，则只显示该主键的历史记录
	title?: string
}

const props = withDefaults(defineProps<Props>(), {
	title: '历史记录'
})

const emits = defineEmits<{
	'update:visible': [value: boolean]
	'closed': []
}>()

// 使用历史记录 composable
const {
	historyRecords: allHistoryRecords,
	getHistoryByPrimaryKeyId,
	formatOperationTime,
	getDataTypeLabel,
	loadHistoryFromStorage
} = usePrimaryKeyHistory()

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)

// 历史记录列配置
const historyColumnConfig = ref<HistoryColumnConfig>(getDefaultHistoryColumnConfig())

// 加载历史记录列配置
const loadHistoryColumnConfig = () => {
	const stored = localStorage.getItem(STORAGE_KEYS.HISTORY_COLUMN_CONFIG)
	if (stored) {
		try {
			historyColumnConfig.value = { ...getDefaultHistoryColumnConfig(), ...JSON.parse(stored) }
		} catch (error) {
			console.error('加载历史记录列配置失败:', error)
			historyColumnConfig.value = getDefaultHistoryColumnConfig()
		}
	}
}

// 计算显示的历史记录
const displayHistoryRecords = computed(() => {
	if (props.primaryKeyId) {
		// 显示指定主键的历史记录
		return getHistoryByPrimaryKeyId(props.primaryKeyId)
	} else {
		// 显示所有历史记录
		return allHistoryRecords.value
	}
})

// 分页后的数据
const paginatedHistoryRecords = computed(() => {
	const start = (currentPage.value - 1) * pageSize.value
	const end = start + pageSize.value
	return displayHistoryRecords.value.slice(start, end)
})

// 总记录数
const totalCount = computed(() => displayHistoryRecords.value.length)

// 动态表格列定义
const columns = computed(() => {
	const allColumns = [
		{ prop: 'sequence', label: '序号', minWidth: 80, configKey: 'sequence' },
		{ prop: 'primaryKeyName', label: '主键名称', minWidth: 150, configKey: 'primaryKeyName' },
		{ prop: 'oldDataType', label: '原数据类型', minWidth: 120, configKey: 'oldDataType' },
		{ prop: 'newDataType', label: '新数据类型', minWidth: 120, configKey: 'newDataType' },
		{ prop: 'operatorName', label: '操作人', minWidth: 120, configKey: 'operatorName' },
		{ prop: 'operationTime', label: '操作时间', minWidth: 180, configKey: 'operationTime' },
		{ prop: 'operationType', label: '操作类型', minWidth: 100, configKey: 'operationType' }
	]

	// 根据配置过滤显示的列
	const filteredColumns = allColumns.filter(column =>
		historyColumnConfig.value[column.configKey as keyof HistoryColumnConfig]
	)

	// 如果只有少数列显示，让最后一列自动填充剩余空间
	if (filteredColumns.length > 0) {
		const lastColumn = filteredColumns[filteredColumns.length - 1]
		// 为最后一列添加自适应宽度
		lastColumn.width = undefined // 移除固定宽度，让其自适应
	}

	return filteredColumns
})

// 处理弹窗关闭
const handleClose = () => {
	emits('update:visible', false)
	emits('closed')
}

// 分页事件处理
const handlePageChange = (page: number) => {
	currentPage.value = page
}

const handlePageSizeChange = (size: number) => {
	pageSize.value = size
	currentPage.value = 1
}

// 监听弹窗显示状态，重置分页并加载配置
watch(() => props.visible, (newVisible) => {
	if (newVisible) {
		currentPage.value = 1
		loadHistoryColumnConfig()
		// 重新加载历史记录数据，确保显示最新的修改记录
		loadHistoryFromStorage()
	}
})

// 获取操作类型显示文本
const getOperationTypeText = (type: string): string => {
	return type === 'batch_update' ? '批量修改' : '单条修改'
}
</script>

<template>
	<DialogComp
		:visible="visible"
		:title="title"
		width="1000px"
		:visibleConfirmButton="false"
		cancelText="关闭"
		@click-cancel="handleClose"
		@closed="handleClose"
	>
		<template #body>
			<div class="history-dialog-content">
				<!-- 统计信息 -->
				<div class="history-stats" v-if="totalCount > 0">
					<span class="stats-text">
						共找到 <strong>{{ totalCount }}</strong> 条历史记录
					</span>
				</div>

				<!-- 历史记录表格 -->
				<el-table
					:data="paginatedHistoryRecords"
					stripe
					border
					style="width: 100%"
					empty-text="暂无历史记录"
					max-height="350"
				>
					<!-- 动态渲染列 -->
					<template v-for="column in columns" :key="column.prop">
						<!-- 序号列 -->
						<el-table-column
							v-if="column.prop === 'sequence'"
							label="序号"
							type="index"
							:min-width="column.minWidth"
							:width="column.width"
							align="center"
							:index="(index) => (currentPage - 1) * pageSize + index + 1"
						/>

						<!-- 主键名称列 -->
						<el-table-column
							v-else-if="column.prop === 'primaryKeyName'"
							:prop="column.prop"
							:label="column.label"
							:min-width="column.minWidth"
							:width="column.width"
							show-overflow-tooltip
						/>

						<!-- 原数据类型列 -->
						<el-table-column
							v-else-if="column.prop === 'oldDataType'"
							:prop="column.prop"
							:label="column.label"
							:min-width="column.minWidth"
							:width="column.width"
							align="center"
						>
							<template #default="{ row }">
								<el-tag type="info" size="small">
									{{ getDataTypeLabel(row.oldDataType) }}
								</el-tag>
							</template>
						</el-table-column>

						<!-- 新数据类型列 -->
						<el-table-column
							v-else-if="column.prop === 'newDataType'"
							:prop="column.prop"
							:label="column.label"
							:min-width="column.minWidth"
							:width="column.width"
							align="center"
						>
							<template #default="{ row }">
								<el-tag type="success" size="small">
									{{ getDataTypeLabel(row.newDataType) }}
								</el-tag>
							</template>
						</el-table-column>

						<!-- 操作人列 -->
						<el-table-column
							v-else-if="column.prop === 'operatorName'"
							:prop="column.prop"
							:label="column.label"
							:min-width="column.minWidth"
							:width="column.width"
							align="center"
							show-overflow-tooltip
						/>

						<!-- 操作时间列 -->
						<el-table-column
							v-else-if="column.prop === 'operationTime'"
							:prop="column.prop"
							:label="column.label"
							:min-width="column.minWidth"
							:width="column.width"
							align="center"
						>
							<template #default="{ row }">
								{{ formatOperationTime(row.operationTime) }}
							</template>
						</el-table-column>

						<!-- 操作类型列 -->
						<el-table-column
							v-else-if="column.prop === 'operationType'"
							:prop="column.prop"
							:label="column.label"
							:min-width="column.minWidth"
							:width="column.width"
							align="center"
						>
							<template #default="{ row }">
								<el-tag
									:type="row.operationType === 'batch_update' ? 'warning' : 'primary'"
									size="small"
								>
									{{ getOperationTypeText(row.operationType) }}
								</el-tag>
							</template>
						</el-table-column>
					</template>
				</el-table>

				<!-- 分页 -->
				<div class="history-pagination" v-if="totalCount > 0">
					<el-pagination
						v-model:current-page="currentPage"
						v-model:page-size="pageSize"
						:page-sizes="[5, 10, 20, 50]"
						:total="totalCount"
						layout="total, sizes, prev, pager, next, jumper"
						background
						@current-change="handlePageChange"
						@size-change="handlePageSizeChange"
					/>
				</div>

				<!-- 空状态 -->
				<div class="empty-state" v-if="totalCount === 0">
					<el-empty description="暂无历史记录" />
				</div>
			</div>
		</template>
	</DialogComp>
</template>

<style scoped lang="scss">
.history-dialog-content {
	padding: 0;

	.history-stats {
		margin-bottom: 16px;
		padding: 12px 16px;
		background-color: #f5f7fa;
		border-radius: 4px;
		border-left: 4px solid #409eff;

		.stats-text {
			font-size: 14px;
			color: #606266;

			strong {
				color: #409eff;
				font-weight: 600;
			}
		}
	}

	.history-pagination {
		margin-top: 16px;
		display: flex;
		justify-content: center;
	}

	.empty-state {
		padding: 40px 0;
		text-align: center;
	}
}

// 表格样式优化
:deep(.el-table) {
	.el-table__header {
		th {
			background-color: #fafafa;
			color: #606266;
			font-weight: 600;
		}
	}

	.el-table__body {
		tr:hover {
			background-color: #f5f7fa;
		}
	}
}
</style>
