<!-- 访问权限管理弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="访问权限调整"
    :destroy-on-close="true"
    width="600px"
    :visible-confirm-button="false"
  >
    <div class="dialog-content" style="padding: 20px;">
      <el-form :model="accessPermissionForm" label-width="100px">
        <el-form-item label="对象类型：">
          <el-select v-model="accessPermissionForm.objectType" style="width: 100%">
            <el-option label="用户" value="用户" />
            <el-option label="角色" value="角色" />
          </el-select>
        </el-form-item>

        <el-form-item label="用户/角色：">
          <el-select v-model="accessPermissionForm.objectSelection" placeholder="请选择" style="width: 100%">
            <el-option
              v-for="item in getObjectSelectionData"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="权限范围：">
          <div style="display: flex; flex-wrap: wrap; gap: 20px;">
            <el-checkbox v-model="accessPermissionForm.readPermission">库级权限</el-checkbox>
            <el-checkbox v-model="accessPermissionForm.tablePermission">表级权限</el-checkbox>
            <el-checkbox v-model="accessPermissionForm.fieldPermission">字段权限</el-checkbox>
          </div>
        </el-form-item>

        <el-form-item label="权限设置：">
          <div style="display: flex; flex-wrap: wrap; gap: 20px;">
            <el-checkbox v-model="accessPermissionForm.viewPermission">查看</el-checkbox>
            <el-checkbox v-model="accessPermissionForm.editPermission">编辑</el-checkbox>
            <el-checkbox v-model="accessPermissionForm.addPermission">新增</el-checkbox>
            <el-checkbox v-model="accessPermissionForm.deletePermission">删除</el-checkbox>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="saveAccessPermission">确定</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps<{
  modelValue: boolean
  editData?: any
  dataSourceType?: string // 'datasource', 'dataset', 'accessBinding'
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 访问权限表单
const accessPermissionForm = ref({
  dataSet: '',
  objectType: '用户',
  objectSelection: '',
  readPermission: false,
  tablePermission: false,
  fieldPermission: false,
  viewPermission: false,
  editPermission: false,
  addPermission: false,
  deletePermission: false
})

// 模拟数据集列表
const dataSetList = ref([
  { id: 1, name: '用户行为数据集' },
  { id: 2, name: '销售业绩数据集' },
  { id: 3, name: '产品信息数据集' },
  { id: 4, name: '客户关系数据集' },
  { id: 5, name: '财务报表数据集' }
])

// 用户和角色数据
const userData = ref([
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' },
  { id: 4, name: '赵六' }
])

const roleData = ref([
  { id: 1, name: '系统管理员' },
  { id: 2, name: '普通用户' },
  { id: 3, name: '数据分析师' },
  { id: 4, name: '业务管理员' }
])

// 权限审计数据存储
const permissionAuditData = ref<any[]>([])

// 缓存键 - 根据数据源类型区分
const getStorageKey = () => {
  const type = props.dataSourceType || 'accessBinding'
  return `${type}_permissionAuditData`
}

const getRowConfigStorageKey = () => {
  const type = props.dataSourceType || 'accessBinding'
  return `${type}_dataPermissionRowConfigs`
}

// 根据对象类型获取对象选择数据
const getObjectSelectionData = computed(() => {
  return accessPermissionForm.value.objectType === '用户' ? userData.value : roleData.value
})

// 基于行ID查找已保存的配置
const findRowConfigByRowId = (rowId: number) => {
  try {
    const cached = localStorage.getItem(getRowConfigStorageKey())
    if (!cached) return null

    const configs = JSON.parse(cached)
    return configs.find((config: any) => config.rowId === rowId)
  } catch (error) {
    console.error('查找权限管理行配置失败:', error)
    return null
  }
}

// 保存行配置数据
const saveRowConfigToCache = (rowData: any) => {
  if (!rowData?.id) {
    console.error('缺少行ID，无法保存权限管理配置')
    return
  }

  try {
    const cached = localStorage.getItem(getRowConfigStorageKey())
    let configs = []

    if (cached) {
      configs = JSON.parse(cached)
    }

    const rowId = rowData.id
    const existingIndex = configs.findIndex((config: any) => config.rowId === rowId)

    const configData = {
      rowId: rowId,
      partitionName: rowData.partitionName,
      datasetName: rowData.datasetName,
      businessTable: rowData.businessTable,
      bindingStatus: rowData.bindingStatus,
      accessPermissionForm: accessPermissionForm.value,
      permissionAuditData: permissionAuditData.value,
      createTime: existingIndex >= 0 ? configs[existingIndex].createTime : new Date().toLocaleString(),
      updateTime: new Date().toLocaleString()
    }

    if (existingIndex >= 0) {
      // 更新现有配置
      configs[existingIndex] = configData
      console.log('更新权限管理配置:', configData)
    } else {
      // 新增配置
      configs.push(configData)
      console.log('新增权限管理配置:', configData)
    }

    localStorage.setItem(getRowConfigStorageKey(), JSON.stringify(configs))
  } catch (error) {
    console.error('保存权限管理行配置失败:', error)
  }
}

// 加载权限数据
const loadAccessPermissionData = () => {
  try {
    const savedData = localStorage.getItem(getStorageKey())
    if (savedData) {
      permissionAuditData.value = JSON.parse(savedData)
    }

    // 加载当前数据集的权限设置（暂时使用第一个数据集作为示例）
    const currentDataSetName = dataSetList.value[0]?.name
    if (currentDataSetName) {
      const existingPermission = permissionAuditData.value.find(
        item => item.dataSetName === currentDataSetName
      )

      if (existingPermission) {
        // 回显已保存的权限设置
        accessPermissionForm.value = {
          dataSet: existingPermission.dataSetName,
          objectType: existingPermission.objectType,
          objectSelection: existingPermission.objectName,
          readPermission: existingPermission.readPermission,
          tablePermission: existingPermission.tablePermission,
          fieldPermission: existingPermission.fieldPermission,
          viewPermission: existingPermission.viewPermission,
          editPermission: existingPermission.editPermission,
          addPermission: existingPermission.addPermission,
          deletePermission: existingPermission.deletePermission
        }
        return
      }
    }

    // 如果没有找到已保存的数据，使用默认值
    accessPermissionForm.value = {
      dataSet: '',
      objectType: '用户',
      objectSelection: '',
      readPermission: false,
      tablePermission: false,
      fieldPermission: false,
      viewPermission: false,
      editPermission: false,
      addPermission: false,
      deletePermission: false
    }
  } catch (error) {
    console.error('加载访问权限数据失败:', error)
    // 出错时使用默认值
    accessPermissionForm.value = {
      dataSet: '',
      objectType: '用户',
      objectSelection: '',
      readPermission: false,
      tablePermission: false,
      fieldPermission: false,
      viewPermission: false,
      editPermission: false,
      addPermission: false,
      deletePermission: false
    }
  }
}

// 保存访问权限设置
const saveAccessPermission = () => {
  // 验证必填字段
  if (!accessPermissionForm.value.dataSet) {
    ElMessage.warning('请选择数据集')
    return
  }

  if (!accessPermissionForm.value.objectSelection) {
    ElMessage.warning('请选择用户或角色')
    return
  }

  const currentDataSetName = accessPermissionForm.value.dataSet

  // 检查是否已存在该数据集的权限设置
  const existingIndex = permissionAuditData.value.findIndex(
    item => item.dataSetName === currentDataSetName && item.objectName === accessPermissionForm.value.objectSelection
  )

  const permissionData = {
    id: existingIndex >= 0 ? permissionAuditData.value[existingIndex].id : Date.now(),
    dataSetName: currentDataSetName,
    objectType: accessPermissionForm.value.objectType,
    objectName: accessPermissionForm.value.objectSelection,
    readPermission: accessPermissionForm.value.readPermission,
    tablePermission: accessPermissionForm.value.tablePermission,
    fieldPermission: accessPermissionForm.value.fieldPermission,
    viewPermission: accessPermissionForm.value.viewPermission,
    editPermission: accessPermissionForm.value.editPermission,
    addPermission: accessPermissionForm.value.addPermission,
    deletePermission: accessPermissionForm.value.deletePermission,
    createTime: existingIndex >= 0 ? permissionAuditData.value[existingIndex].createTime : new Date().toLocaleString('zh-CN'),
    updateTime: new Date().toLocaleString('zh-CN')
  }

  if (existingIndex >= 0) {
    // 更新已存在的权限设置
    permissionAuditData.value[existingIndex] = permissionData
  } else {
    // 添加新的权限设置
    permissionAuditData.value.push(permissionData)
  }

  // 保存到localStorage
  localStorage.setItem(getStorageKey(), JSON.stringify(permissionAuditData.value))

  // 同时保存行配置
  if (props.editData) {
    saveRowConfigToCache(props.editData)
  }

  ElMessage.success('访问权限调整保存成功')
  visible.value = false
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 处理编辑数据的函数
const handleEditData = (editData: any) => {
  if (!editData) {
    return
  }

  console.log('权限管理 - 处理编辑数据:', editData)

  // 首先尝试基于行ID查找已保存的配置
  if (editData.id) {
    const savedConfig = findRowConfigByRowId(editData.id)
    if (savedConfig) {
      console.log('找到已保存的权限管理配置:', savedConfig)
      // 使用已保存的配置数据回显到表单
      if (savedConfig.accessPermissionForm) {
        accessPermissionForm.value = {
          ...accessPermissionForm.value,
          ...savedConfig.accessPermissionForm
        }
      }
      if (savedConfig.permissionAuditData) {
        permissionAuditData.value = savedConfig.permissionAuditData
      }
      console.log('回显已保存的权限管理配置:', accessPermissionForm.value)
      return
    }
  }

  // 如果没有找到已保存的配置，使用默认配置
  console.log('未找到已保存配置，使用默认权限管理配置')

  // 根据表格行数据生成智能描述和配置
  const partitionName = editData.partitionName || ''
  const datasetName = editData.datasetName || ''
  const bindingStatus = editData.bindingStatus || ''

  if (partitionName && datasetName) {
    console.log(`权限管理 - 为分区"${partitionName}"的数据集"${datasetName}"配置访问权限`)

    // 设置数据集名称
    accessPermissionForm.value.dataSet = datasetName

    // 根据行数据设置特定的权限策略到表单字段
    if (bindingStatus === '启用') {
      // 为启用状态的数据源设置完整权限
      console.log('权限管理 - 设置启用状态的权限策略（完整权限）')
      accessPermissionForm.value = {
        dataSet: datasetName,
        objectType: '用户',
        objectSelection: '',
        readPermission: true,
        tablePermission: true,
        fieldPermission: false,
        viewPermission: true,
        editPermission: true,
        addPermission: true,
        deletePermission: false
      }
    } else {
      // 为禁用状态的数据源设置受限权限
      console.log('权限管理 - 设置禁用状态的权限策略（受限权限）')
      accessPermissionForm.value = {
        dataSet: datasetName,
        objectType: '用户',
        objectSelection: '',
        readPermission: true,
        tablePermission: false,
        fieldPermission: false,
        viewPermission: true,
        editPermission: false,
        addPermission: false,
        deletePermission: false
      }
    }

    console.log('设置权限管理表单数据:', accessPermissionForm.value)
  }
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  console.log('权限管理弹窗状态变化:', newVal, '编辑数据:', props.editData)
  if (newVal) {
    loadAccessPermissionData()

    // 使用nextTick确保DOM更新后再处理数据回显
    nextTick(() => {
      if (props.editData) {
        console.log('开始处理权限管理数据回显:', props.editData)
        handleEditData(props.editData)
      }
    })
  }
})

// 监听编辑数据变化
watch(() => props.editData, (newEditData) => {
  console.log('权限管理编辑数据变化:', newEditData)
  if (props.modelValue && newEditData) {
    handleEditData(newEditData)
  }
}, { deep: true })
</script>

<style lang="scss" scoped>
.dialog-content {
  .el-form-item {
    margin-bottom: 20px;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
