<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { Loading, DataAnalysis, Warning, CircleCheckFilled, ArrowLeft } from '@element-plus/icons-vue'

// 路由实例
const router = useRouter()

// 性能数据接口
interface PerformanceData {
  date: string
  responseTime: number // 平均响应时间(ms)
  viewCount: number    // 查看次数
}

// 关键指标接口
interface KeyMetrics {
  idGenerationRate: number // ID生成速率(/秒)
  conflictRate: number     // 主键冲突率(%)
}

// 响应式数据
const performanceData = ref<PerformanceData[]>([])
const keyMetrics = ref<KeyMetrics>({
  idGenerationRate: 0,
  conflictRate: 0
})

// Block组件相关
const showSearchArea = ref(true)
const tableHeight = ref(0)

// 生成最近7天的日期数组
const generateLast7Days = (): string[] => {
  const dates: string[] = []
  const today = new Date()
  
  for (let i = 6; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(today.getDate() - i)
    dates.push(formatDate(date))
  }
  
  return dates
}

// 格式化日期
const formatDate = (date: Date): string => {
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return `${month}-${day}`
}

// 生成模拟性能数据
const generatePerformanceData = (): PerformanceData[] => {
  const dates = generateLast7Days()
  
  return dates.map(date => ({
    date,
    responseTime: Math.floor(Math.random() * 91) + 10, // 10-100ms
    viewCount: Math.floor(Math.random() * 901) + 100   // 100-1000次
  }))
}

// 生成关键指标数据
const generateKeyMetrics = (): KeyMetrics => {
  return {
    idGenerationRate: Math.floor(Math.random() * 4001) + 1000, // 1000-5000/秒
    conflictRate: parseFloat((Math.random() * 0.09 + 0.01).toFixed(3)) // 0.01%-0.1%
  }
}

// ECharts配置选项
const chartOption = computed(() => {
  if (performanceData.value.length === 0) return null

  return {
    // 添加动画配置
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut',

    title: {
      text: '主键性能趋势',
      left: 'center',
      top: 10,
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#303133'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      formatter: (params: any) => {
        let result = `<div style="margin-bottom: 8px; font-weight: bold;">${params[0].axisValue}</div>`
        params.forEach((param: any) => {
          const unit = param.seriesName === '平均响应时间' ? 'ms' : '次'
          result += `<div style="margin: 4px 0;">${param.marker}<span style="margin-left: 8px;">${param.seriesName}: <strong>${param.value}${unit}</strong></span></div>`
        })
        return result
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e4e7ed',
      borderWidth: 1,
      textStyle: {
        color: '#303133',
        fontSize: 12
      },
      padding: [10, 15]
    },
    legend: {
      data: ['平均响应时间', '查看次数'],
      top: 40,
      left: 'center',
      itemGap: 30,
      textStyle: {
        fontSize: 12,
        color: '#606266'
      },
      icon: 'roundRect'
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '8%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: true, // 修复：为柱状图提供边界间距
      data: performanceData.value.map(item => item.date),
      axisLabel: {
        fontSize: 12,
        color: '#606266',
        margin: 10
      },
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '响应时间(ms)',
        position: 'left',
        axisLabel: {
          formatter: '{value} ms',
          fontSize: 12,
          color: '#606266'
        },
        nameTextStyle: {
          fontSize: 12,
          color: '#909399',
          padding: [0, 0, 0, 0]
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
            type: 'dashed'
          }
        }
      },
      {
        type: 'value',
        name: '查看次数',
        position: 'right',
        axisLabel: {
          formatter: '{value} 次',
          fontSize: 12,
          color: '#606266'
        },
        nameTextStyle: {
          fontSize: 12,
          color: '#909399',
          padding: [0, 0, 0, 0]
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '平均响应时间',
        type: 'bar',
        yAxisIndex: 0,
        data: performanceData.value.map(item => item.responseTime),
        itemStyle: {
          color: '#409EFF',
          borderRadius: [4, 4, 0, 0] // 添加圆角
        },
        barWidth: '30%', // 修复：减小柱宽，避免重叠
        barMaxWidth: 45, // 限制最大宽度
        barMinWidth: 20, // 限制最小宽度
        emphasis: {
          itemStyle: {
            color: '#337ecc'
          }
        },
        // 添加数据标签
        label: {
          show: false, // 默认不显示，悬停时显示
          position: 'top',
          formatter: '{c}ms',
          fontSize: 11,
          color: '#606266'
        }
      },
      {
        name: '查看次数',
        type: 'line',
        yAxisIndex: 1,
        data: performanceData.value.map(item => item.viewCount),
        itemStyle: {
          color: '#67C23A'
        },
        lineStyle: {
          color: '#67C23A',
          width: 3,
          shadowColor: 'rgba(103, 194, 58, 0.3)',
          shadowBlur: 4,
          shadowOffsetY: 2
        },
        symbol: 'circle',
        symbolSize: 8,
        smooth: true, // 添加平滑曲线
        emphasis: {
          itemStyle: {
            color: '#529b2e',
            borderColor: '#67C23A',
            borderWidth: 2
          },
          lineStyle: {
            width: 4
          }
        }
      }
    ]
  }
})

// 格式化ID生成速率
const formattedIdGenerationRate = computed(() => {
  return `${keyMetrics.value.idGenerationRate.toLocaleString()}/秒`
})

// 格式化冲突率
const formattedConflictRate = computed(() => {
  return `${keyMetrics.value.conflictRate}%`
})

// 健康状态计算
const healthStatusColor = computed(() => {
  const conflictRate = keyMetrics.value.conflictRate
  if (conflictRate <= 0.05) {
    return '#67C23A' // 绿色 - 良好
  } else if (conflictRate <= 0.08) {
    return '#E6A23C' // 橙色 - 警告
  } else {
    return '#F56C6C' // 红色 - 危险
  }
})

const healthStatusText = computed(() => {
  const conflictRate = keyMetrics.value.conflictRate
  if (conflictRate <= 0.05) {
    return '良好'
  } else if (conflictRate <= 0.08) {
    return '警告'
  } else {
    return '危险'
  }
})

// 返回到主键定义页面
const handleGoBack = () => {
  router.push('/primary-key-definition')
}

// Block组件相关函数
const expendSearch = (expanded: boolean) => {
  showSearchArea.value = expanded
}

// 块高度变化事件时修改表格高度
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 75
}

// 初始化数据
const initializeData = () => {
  performanceData.value = generatePerformanceData()
  keyMetrics.value = generateKeyMetrics()
}

// 响应式图表配置
const isMobile = ref(false)

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 响应式图表选项
const responsiveChartOption = computed(() => {
  const baseOption = chartOption.value
  if (!baseOption) return null

  if (isMobile.value) {
    // 移动端优化配置
    return {
      ...baseOption,
      title: {
        ...baseOption.title,
        textStyle: {
          ...baseOption.title.textStyle,
          fontSize: 14
        }
      },
      legend: {
        ...baseOption.legend,
        top: 35,
        itemGap: 20,
        textStyle: {
          fontSize: 11,
          color: '#606266'
        }
      },
      grid: {
        left: '12%',
        right: '12%',
        bottom: '10%',
        top: '25%',
        containLabel: true
      },
      xAxis: {
        ...baseOption.xAxis,
        axisLabel: {
          ...baseOption.xAxis.axisLabel,
          fontSize: 10,
          rotate: 0
        }
      },
      yAxis: baseOption.yAxis.map((axis: any) => ({
        ...axis,
        nameTextStyle: {
          ...axis.nameTextStyle,
          fontSize: 10
        },
        axisLabel: {
          ...axis.axisLabel,
          fontSize: 10
        }
      })),
      series: baseOption.series.map((serie: any) => {
        if (serie.type === 'bar') {
          return {
            ...serie,
            barWidth: '35%',
            barMaxWidth: 30
          }
        }
        return {
          ...serie,
          symbolSize: 6,
          lineStyle: {
            ...serie.lineStyle,
            width: 2
          }
        }
      })
    }
  }

  return baseOption
})

// 组件挂载时初始化数据
onMounted(() => {
  initializeData()
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<route>
{
  meta: {
    title: '主键性能监控分析',
    ignoreLabel: false
  }
}
</route>

<template>
  <div class="primary-key-performance-monitor">
    <Block
      title="主键性能监控分析"
      :enable-expand-content="false"
      :enableBackButton="false"
      :enable-fixed-height="true"
      :enable-close-button="false"
      @content-expand="expendSearch"
      @height-changed="onBlockHeightChanged"
    >
      <template #topRight>
        <el-button 
          size="small" 
          type="default" 
          @click="handleGoBack"
          style="margin-right: 8px"
        >
          <el-icon style="margin-right: 4px">
            <ArrowLeft />
          </el-icon>
          返回
        </el-button>
      </template>

      <!-- 监控分析内容 -->
      <div class="performance-monitor-content">
        <!-- 图表区域 -->
        <div class="chart-section">
          <div class="chart-container">
            <Charts
              v-if="responsiveChartOption"
              :option="responsiveChartOption"
              width="100%"
              :height="isMobile ? '350px' : '400px'"
            />
            <div v-else class="chart-loading">
              <el-icon class="is-loading">
                <Loading />
              </el-icon>
              <span>加载中...</span>
            </div>
          </div>
        </div>

        <!-- 关键指标卡片区域 -->
        <div class="metrics-section">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-card class="metrics-card" shadow="hover">
                <div class="metric-content">
                  <div class="metric-icon">
                    <el-icon size="24" color="#409EFF">
                      <DataAnalysis />
                    </el-icon>
                  </div>
                  <div class="metric-info">
                    <div class="metric-label">ID生成速率</div>
                    <div class="metric-value">{{ formattedIdGenerationRate }}</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-card class="metrics-card" shadow="hover">
                <div class="metric-content">
                  <div class="metric-icon">
                    <el-icon size="24" color="#F56C6C">
                      <Warning />
                    </el-icon>
                  </div>
                  <div class="metric-info">
                    <div class="metric-label">主键冲突率</div>
                    <div class="metric-value">{{ formattedConflictRate }}</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
              <el-card class="metrics-card" shadow="hover">
                <div class="metric-content">
                  <div class="metric-icon">
                    <el-icon size="20" :color="healthStatusColor">
                      <CircleCheckFilled />
                    </el-icon>
                  </div>
                  <div class="metric-info">
                    <div class="metric-label">健康状态</div>
                    <div class="metric-value" :style="{ color: healthStatusColor }">{{ healthStatusText }}</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </Block>
  </div>
</template>

<style scoped lang="scss">
.primary-key-performance-monitor {
  height: 100%;

  .performance-monitor-content {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: 600px;

    .chart-section {
      margin-bottom: 24px;

      .chart-container {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        border: 1px solid #f0f0f0;

        .chart-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 400px;
          color: #909399;
          font-size: 14px;

          .el-icon {
            font-size: 32px;
            margin-bottom: 12px;
          }
        }
      }
    }

    .metrics-section {
      .metrics-card {
        height: 120px;
        margin-bottom: 16px;

        :deep(.el-card__body) {
          height: 100%;
          padding: 20px;
        }

        .metric-content {
          display: flex;
          align-items: center;
          height: 100%;

          .metric-icon {
            margin-right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background-color: #f0f9ff;
          }

          .metric-info {
            flex: 1;

            .metric-label {
              font-size: 14px;
              color: #606266;
              margin-bottom: 8px;
            }

            .metric-value {
              font-size: 24px;
              font-weight: bold;
              color: #303133;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .primary-key-performance-monitor {
    .performance-monitor-content {
      padding: 16px;

      .chart-section .chart-container {
        padding: 16px;
      }

      .metrics-section .metrics-card {
        margin-bottom: 12px;

        .metric-content .metric-info .metric-value {
          font-size: 20px;
        }
      }
    }
  }
}
</style>
