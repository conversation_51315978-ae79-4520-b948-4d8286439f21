# 主键性能监控分析页面

## 功能概述

主键性能监控分析页面 (`src/views/index/primary-key-performance-monitor/index.vue`) 提供了主键系统的性能监控和分析功能，包括性能趋势图表和关键指标展示。该功能已从弹窗模式改为独立页面模式。

## 功能特性

### 1. 主键性能趋势图表
- **图表类型**: ECharts柱状图+折线图组合
- **数据展示**:
  - 柱状图：平均响应时间（毫秒）
  - 折线图：查看次数
- **时间维度**: 最近7天的数据
- **双Y轴设计**: 左侧显示响应时间，右侧显示查看次数

### 2. 关键指标卡片
- **ID生成速率**: 显示每秒生成的ID数量
- **主键冲突率**: 显示主键冲突的百分比
- **健康状态**: 基于冲突率自动判断系统健康状态
  - 良好 (≤0.05%): 绿色
  - 警告 (0.05%-0.08%): 橙色  
  - 危险 (>0.08%): 红色

### 3. 数据模拟
- **ID生成速率**: 1000-5000/秒的随机数据
- **主键冲突率**: 0.01%-0.1%的随机数据
- **平均响应时间**: 10-100毫秒的时间序列数据
- **查看次数**: 100-1000次的时间序列数据

## 使用方法

### 1. 访问功能
1. 进入"业务表主键定义"页面 (`/primary-key-definition`)
2. 点击页面右上角的"更多操作"下拉按钮
3. 选择"主键性能监控分析"选项
4. 系统将跳转到独立的监控分析页面 (`/primary-key-performance-monitor`)

### 2. 页面导航
- 在监控分析页面的右上角有"返回"按钮
- 点击"返回"按钮可以回到"业务表主键定义"页面
- 支持浏览器的前进/后退导航

### 3. 查看数据
- 图表会自动加载最近7天的性能数据
- 关键指标卡片显示当前系统状态
- 健康状态指示器提供系统整体健康评估

## 技术实现

### 技术栈
- **前端框架**: Vue 3 + TypeScript
- **UI组件库**: Element Plus
- **图表库**: ECharts (通过akvts框架的Charts组件)
- **路由**: Vue Router
- **样式**: SCSS

### 页面结构
```
primary-key-performance-monitor/index.vue
├── Block组件容器
│   ├── 页面标题和返回按钮
│   └── 监控内容区域
│       ├── 图表区域 (chart-section)
│       │   └── ECharts组合图表
│       └── 关键指标区域 (metrics-section)
│           ├── ID生成速率卡片
│           ├── 主键冲突率卡片
│           └── 健康状态卡片
```

### 响应式设计
- 支持不同屏幕尺寸的自适应布局
- 移动端优化的卡片布局
- 图表自动调整大小

## 路由配置

页面路由信息：
- **路径**: `/primary-key-performance-monitor`
- **页面标题**: 主键性能监控分析
- **父级路由**: 无（独立页面）

## 数据接口

### PerformanceData
```typescript
interface PerformanceData {
  date: string          // 日期 (MM-DD格式)
  responseTime: number  // 平均响应时间(ms)
  viewCount: number     // 查看次数
}
```

### KeyMetrics
```typescript
interface KeyMetrics {
  idGenerationRate: number // ID生成速率(/秒)
  conflictRate: number     // 主键冲突率(%)
}
```

## 样式特性

- 与现有页面保持视觉一致性
- 使用Element Plus的设计规范
- 使用Block组件作为页面容器
- 卡片阴影和悬停效果
- 响应式网格布局
- 适配移动端显示

## 页面导航

### 进入页面
- 从"业务表主键定义"页面的"更多操作"菜单进入
- 直接访问URL: `/primary-key-performance-monitor`

### 离开页面
- 点击页面右上角的"返回"按钮
- 使用浏览器的后退按钮
- 直接导航到其他页面

## 图表优化修复

### 修复的问题
1. **柱状图间距优化**：
   - 修改 `xAxis.boundaryGap` 从 `false` 改为 `true`，为柱状图提供边界间距
   - 调整 `barWidth` 从 `40%` 改为 `30%`，避免柱形重叠
   - 添加 `barMaxWidth: 45` 和 `barMinWidth: 20` 限制柱形尺寸

2. **图例布局优化**：
   - 将图例位置从右上角改为顶部居中 (`left: 'center'`)
   - 增加图例项间距 (`itemGap: 30`)
   - 优化图例样式和颜色

3. **图表区域布局**：
   - 优化 `grid` 配置，增加左右边距从 `3%/4%` 改为 `8%/8%`
   - 调整顶部边距从 `15%` 改为 `20%`，为图例留出更多空间
   - 添加坐标轴样式优化，包括分割线和标签样式

4. **响应式适配**：
   - 添加移动端检测和响应式配置
   - 移动端优化：更小的字体、调整间距、优化柱宽
   - 动态图表高度：桌面端400px，移动端350px

### 新增功能
1. **视觉增强**：
   - 添加柱状图圆角效果 (`borderRadius: [4, 4, 0, 0]`)
   - 添加折线图阴影效果和平滑曲线
   - 优化工具提示样式和格式

2. **交互优化**：
   - 添加悬停效果和强调样式
   - 添加图表动画效果 (`animation: true`)
   - 优化数据标签显示

3. **响应式设计**：
   - 自动检测屏幕尺寸并应用相应配置
   - 移动端专用的图表参数优化
   - 窗口大小变化时自动调整

## 扩展说明

该页面目前使用模拟数据，在实际生产环境中可以：
1. 替换数据生成函数为真实API调用
2. 添加数据刷新功能
3. 增加更多性能指标
4. 支持自定义时间范围选择
5. 添加数据导出功能
6. 添加实时数据更新
7. 支持历史数据查询
8. 添加数据钻取功能
9. 支持图表类型切换
10. 添加性能阈值告警
