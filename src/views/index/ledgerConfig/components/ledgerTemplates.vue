<script lang="ts" setup>
import {onActivated, onMounted, onUnmounted, ref, reactive, toRaw, watch} from 'vue'

import {getLedgerTemplateTableList} from '@/api/LedgerApi'

import {useViewStore} from '@/stores/useViewStore'

import ledgerTemplatePreview from '../../ledgerTemplate/components/ledgerTemplatePreview.vue'

interface Props {
	visible?: boolean
}

const props = withDefaults(defineProps<Props>(), {})

const isShowPreview = ref<any>(false)

const colData = ref([
	{title: '模板名称', field: 'displayName'},

	{title: '说明', field: 'remark', width: '850px'},
])

const buttons = ref([
	{
		code: 'view',

		title: '查看',

		icon: '<i i-majesticons-eye-line></i>',

		verify: `true`,
	},
])

const tableData = ref<any>([])

const pageation = ref<any>({
	currentPage: 1,

	pageSize: 10,

	total: 0,
})

// 表格数据

const initTable = () => {
	console.log(pageation)

	let datas = {
		Name: searchForm.value,

		Sorting: '',

		SkipCount: (pageation.value.currentPage - 1) * pageation.value.pageSize,

		MaxResultCount: pageation.value.pageSize,
	}

	getLedgerTemplateTableList(datas).then((res) => {
		tableData.value = res.data.items

		pageation.value.total = res.data.totalCount
	})
}

// 搜索功能

const searchForm = reactive({
	value: '',
})

const searchLedgerTemplates = () => {
	initTable()
}

// 表格功能

const templateName = ref('')

const onTableClickButton = ({btn, scope}: any) => {
	//操作栏

	console.log(btn, scope)

	isShowPreview.value = true

	LedgertemplateId.value = scope.id

	templateName.value = scope.displayName
}

const onPageationChange = (val: number, type: string) => {
	//分页

	if (type === 'size') {
		pageation.value.pageSize = val
	} else {
		pageation.value.currentPage = val
	}

	initTable()
}

const tableRef = ref<any>(null)

const handleSelectionChange = (val: any) => {
	//多选框

	console.log(val)

	LedgertemplateId.value = val[val.length - 1]?.id

	if (val.length > 1) {
		tableRef.value.selected(val[0].id, false)
	}
}

// 处理表格模板详情

const LedgertemplateId = ref<any>('')

const changeLedgerTemplateVisible = (val: any) => {
	isShowPreview.value = val
}

const viewStore = useViewStore()

console.log(viewStore)

const emits = defineEmits(['onLedgerTemplateConfirm'])

const onConfirm = () => {
	console.log(LedgertemplateId.value)

	emits('onLedgerTemplateConfirm', JSON.stringify(LedgertemplateId.value))
}

onMounted(() => {
	console.log('ledgerTemplates mounted')
})

onUnmounted(() => {})

onActivated(() => {
	console.log('ledgerTemplates activated')
})
</script>

<template>
	<Dialog v-bind="$attrs" @clickConfirm="onConfirm" @open="initTable">
		<div class="ledger-templates"></div>

		<BaseTableComp
			ref="tableRef"
			:auto-height="true"
			:offsetHeight="10"
			:checkbox="true"
			:data="tableData"
			:colData="colData"
			:buttons="buttons"
			:visibleSetting="false"
			:currentPage="pageation.currentPage"
			:pageSize="pageation.pageSize"
			:total="pageation.total"
			@clickButton="onTableClickButton"
			@size-change="onPageationChange($event, 'size')"
			@current-change="onPageationChange($event, 'current')"
			@selection-change="handleSelectionChange"
		>
			<template #header>
				<div class="header-column">
					<div class="header-layout" style="display: flex; justify-content: end">
						<div style="margin-right: 10px">
							<el-input
								class="search-input"
								v-model="searchForm.value"
								placeholder="请输入模板名称"
								size="small"
								clearable
								@keyup.enter="searchLedgerTemplates"
							></el-input>
						</div>

						<el-button
							style="height: 24px"
							type="primary"
							@click="searchLedgerTemplates"
							>搜索</el-button
						>
					</div>
				</div>
			</template>

			<template #remark="{rowData}">
				{{ rowData.remark == '' ? '-' : rowData.remark }}
			</template>
		</BaseTableComp>
	</Dialog>

	<!-- 传递一个模板id -->

	<ledgerTemplatePreview
		width="1000"
		:title="templateName"
		:LedgertemplateId="LedgertemplateId"
		:visible="isShowPreview"
		@changeLedgerTemplateVisible="changeLedgerTemplateVisible"
		@closed="isShowPreview = false"
		@clickCancel="isShowPreview = false"
	>
	</ledgerTemplatePreview>
</template>

<style scoped lang="scss">
.ledger-templates {
	.header-column {
	}
}
</style>
