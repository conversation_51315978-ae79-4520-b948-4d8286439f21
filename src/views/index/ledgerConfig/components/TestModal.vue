<script setup lang="ts">
import {ElMessage} from 'element-plus'
import {ref, watch, reactive} from 'vue'

interface Props {
	label?: string
	multiple?: boolean
	defaultData?: []
}

const props = withDefaults(defineProps<Props>(), {
	label: '-',
	multiple: false,
	defaultData: [] as any,
})

const emits = defineEmits(['clickConfirm'])

watch(
	() => props.defaultData,
	(newVal) => {
		if (newVal && newVal.length > 0) {
			items.value = newVal.map((m: any) => ({value: m}))
		} else {
			items.value = [{value: ''}, {value: ''}]
		}
	}
)

const items = ref<any[]>([{value: ''}, {value: ''}])

const onAddItem = () => {
	items.value.push({value: ''})
}
const onConfirm = () => {
	emits('clickConfirm', items.value.map((m: any) => m.value).filter(Boolean))
	items.value = [{value: ''}, {value: ''}]
}

const form = ref({
	rules: [
		{start: 1, end: 2}, // 默认一组规则
	],
	inputText: '',
	desensitizedText: '',
})
const formRef = ref()

const addRule = () => {
	const lastRule = form.value.rules[form.value.rules.length - 1]
	const newRule = {
		start: lastRule.end + 1,
		end: lastRule.end + 2,
	}
	form.value.rules.push(newRule)
}

const removeRule = (index: number) => {
	form.value.rules.splice(index, 1)
}

const validateRules = () => {
	const rules = form.value.rules

	// 遍历所有规则进行验证
	for (let i = 0; i < rules.length; i++) {
		const currentRule = rules[i]
		const nextRule = rules[i + 1]

		// 验证start是否小于等于end
		if (currentRule.start > currentRule.end) {
			ElMessage.warning(`规则${i + 1}的起始值不能大于结束值。`)
			return false
		}

		// 验证当前规则的end是否小于下一个规则的start（如果存在）
		if (nextRule && currentRule.end >= nextRule.start) {
			ElMessage.warning(`规则${i + 1}的结束值不能大于或等于规则${i + 2}的起始值。`)

			return false
		}
	}

	return true
}

const desensitizeText = () => {
	if (validateRules()) {
		const rules = form.value.rules
		let result = form.value.inputText

		rules.forEach((rule) => {
			if (rule.start > 0 && rule.end <= result.length) {
				const start = rule.start - 1
				const end = rule.end
				const replaceStr = '*'.repeat(end - start)
				result = result.slice(0, start) + replaceStr + result.slice(end)
			}
		})

		form.value.desensitizedText = result
	}
}
</script>
<template>
	<DialogComp v-bind="$attrs" @clickConfirm="onConfirm">
		<template #body>
			<div>
				<el-form :model="form" ref="formRef" label-width="120px">
					<p>配置脱敏位数：</p>
					<div class="orders">
						<el-row v-for="(rule, index) in form.rules" :key="index">
							<el-col :span="20">
								<el-row>
									<el-col :span="12">
										<el-form-item :prop="`rules.${index}.start`">
											<el-input-number
												placeholder="开始数值"
												v-model="rule.start"
											></el-input-number>
										</el-form-item>
									</el-col>
									<el-col :span="12">
										<el-form-item :prop="`rules.${index}.end`">
											<el-input-number placeholder="结束数值" v-model="rule.end"></el-input-number>
										</el-form-item>
									</el-col>
								</el-row>
							</el-col>
							<el-col :span="4" style="margin-top: 3px">
								<el-button
									:disabled="form.rules.length <= 1"
									type="danger"
									size="small"
									@click="removeRule(index)"
									style="margin-left: 15px"
								>
									<i class="icon i-ic-outline-remove"></i>
									删除
								</el-button>
							</el-col>
						</el-row>
						<el-button type="primary" class="full" @click="addRule">
							<i class="icon i-ic-baseline-add"></i>
							添加
						</el-button>
					</div>
					<div style="margin-top: 20px">
						<p>查看脱敏效果：</p>
						<el-row>
							<el-col :span="20">
								<el-form-item label="测试数据" label-width="82">
									<el-input
										v-model="form.inputText"
										style="width: 80%"
										type="textarea"
										rows="4"
									></el-input>
								</el-form-item>
							</el-col>
							<el-col :span="3">
								<el-button style="margin-left: 10px" type="primary" @click="desensitizeText"
									>脱敏</el-button
								>
							</el-col>
						</el-row>
						<el-form-item label-width="82" label="脱敏效果">
							<el-input
								v-model="form.desensitizedText"
								type="textarea"
								rows="4"
								readonly
							></el-input>
						</el-form-item>
					</div>
				</el-form>
			</div>
		</template>
	</DialogComp>
</template>
<style scoped lang="scss">
.items {
	display: flex;
	align-items: center;
	margin-bottom: 10px;

	p {
		width: 100px;
		// margin-right:10px
	}
}

.orders {
	border-radius: torem(5px);
	background-color: var(--z-bg-secondary);
	padding: torem(20px);
	width: 100%;

	:deep(.el-form-item__content) {
		margin: 0 !important;
	}

	.item {
		align-items: center;
		display: flex;
		margin-bottom: torem(15px);
	}

	.full {
		width: 100%;
	}
}

.content {
	display: flex;
}

.header {
	align-items: center;
	display: flex;
	border-bottom: var(--z-border);
	padding: 0 0 9px 0;
	margin-bottom: 10px;

	.label {
		font-weight: 500;
		text-align: left;
	}

	.value {
		margin-left: 10px;
		flex: 1;
	}
}

.item {
	// align-items: center;
	// display: flex;
	margin-bottom: 10px;

	button {
		margin-left: 10px;
	}
}
</style>
