<script setup lang="ts">
import {ref, watch} from 'vue'
import {useRoute} from 'vue-router'

interface Props {
	tableInfo?: any
	openCardModal?:any
	defaultDataCard?: any
	allData:any
}
enum ViewType {
	Add = 'add',
	Edit = 'edit',
	View = 'view',
}
const props = withDefaults(defineProps<Props>(), {
	tableInfo: [] as any,
	openCardModal:"",
	defaultDataCard: {},
	allData:{}
})

const emits = defineEmits(['clickConfirmIdCard'])
const selectIdCode = ref('')
const route = useRoute()
const viewTypes = ref<any>(route.query.type)

watch(
	()=>props.openCardModal,
	(newVal) =>{ 
		console.log(newVal)
		console.log(props.allData)

	},
	{
		immediate:true,
		deep:true
	}
)

watch(
	() => props.defaultDataCard,
	(newVal) => {
		console.log(props.defaultDataCard)
		if (newVal) {
			console.log(newVal)
			selectIdCode.value = newVal?.relevanceFieldId
		} else {
			selectIdCode.value = ''
		}
	}
)

const onConfirm = () => {
	if (selectIdCode.value) {
		let obj = {
			relevanceType: 1,
			relevanceFieldId: selectIdCode.value,
			level: 1,
		}
		console.log(selectIdCode.value)
		emits('clickConfirmIdCard', {...obj})
		selectIdCode.value = ''
	} else {
		emits('clickConfirmIdCard', null)
		selectIdCode.value = ''
	}
}
</script>
<template>
	<Dialog v-bind="$attrs" @clickConfirm="onConfirm">
		<div class="contentss">
			<div>
				<div style="margin-bottom: 20px; width: 400px; align-items: center">
					<p style="margin-bottom: 20px">请选择居民身份证号用来提取{{ props.allData?.type === 'sex'?'性别':props.allData?.type === 'age'?'年龄':'出生日期' }}</p>
					<el-select
						:disabled="viewTypes === ViewType.View"
						clearable
						v-model="selectIdCode"
						placeholder="请选择"
					>
						<el-option
							v-for="item of props.tableInfo"
							:key="item.label"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
				</div>
			</div>
		</div>
	</Dialog>
</template>
<style scoped lang="scss">
.contentss {
	display: flex;
	padding-left: 30px;
}

.header {
	align-items: center;
	display: flex;
	border-bottom: var(--z-border);
	padding: 0 0 9px 0;
	margin-bottom: 10px;

	.label {
		font-weight: 500;
		text-align: left;
	}

	.value {
		margin-left: 10px;
		flex: 1;
	}
}

.item {
	align-items: center;
	display: flex;
	margin-bottom: 10px;

	button {
		margin-left: 10px;
	}
}
</style>
