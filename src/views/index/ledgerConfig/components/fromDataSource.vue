<script lang="ts" setup>
import {onActivated, onMounted, onUnmounted, ref, toRaw, watch} from 'vue'
import {getTableDataSet, getTableDataSetById} from '@/api/LedgerApi'
import {useViewStore} from '@/stores/useViewStore'
import {useRoute, useRouter} from 'vue-router'

interface Props {
	defaultData: any[]
	visible?: boolean
	disabled?: boolean
	viewType?: string
}
const props = withDefaults(defineProps<Props>(), {
	defaultData: [] as any,
	disabled: false,
})

watch(
	() => props.defaultData,
	async (newVal) => {
		// 设置默认值
		const nd = newVal
			.map((m) => toRaw(m))
			.filter((f: any) => fixedFields.indexOf(f.name ?? f.dbTableField.name) <= -1)

		filterDefaultData.value = nd
		if (nd.length > 0) {
			currentDataset.value = nd[0].__id.split('/')[0]
			datasetChecked.value = nd.map((m: any) => m.__id) as any
			const promises: any = []
			datasetChecked.value
				.map((m: any) => m.split('/')[0])
				.filter((m: any, index: number, arr: any[]) => arr.indexOf(m) === index)
				.forEach((id: any) => {
					promises.push(onChangeTableDataSet(id, false))
				})
			await Promise.all(promises)
			// onChangeChecked()
		}
	}
)

watch(
	() => props.visible,
	(newVal) => {
		if (newVal && needClear.value) {
			setTimeout(() => clearAll(), 16.7)
		}
	}
)

const viewStore = useViewStore()
const emits = defineEmits(['clickConfirm', 'changeSource', 'completed'])
const filter = ref('')
const dataset = ref<any>([]) // 数据集下拉列表
const currentDataset = ref<any>(null) // 当前数据集
const route = useRoute()
const datasetGroup = ref<any>([]) // 当前数据集字段
const datasetGroupRaw = ref<any>({}) // 已加载数据集字段

const datasetChecked: any = ref([]) // 当前数据集字段选中
const checkedList = ref<any>([]) // 已选数据集字段

const fixedFields = [
	'Editor',
	'DataSource',
	'Informant',
	'UpdateTime',
	'Community',
	'Street',
	'District',
	'City',
]
const filterDefaultData = ref<any>([])
const filterCheckedValue = ref('')
const needClear = ref(false)

const clearAll = () => {
	filter.value = ''
	datasetGroup.value = []
	datasetChecked.value = []
	checkedList.value = []
	if (needClear.value) {
		currentDataset.value = null
		needClear.value = false
	}
}

const onConfirm = () => {
	emits(
		'clickConfirm',
		checkedList.value.map((m: any) => toRaw(m))
	)
}

const onRemove = (item: any) => {
	checkedList.value = checkedList.value.filter((f: any) => f !== item)
	datasetChecked.value = datasetChecked.value.filter(
		(f: any) => f !== `${item.tableDataSetId}/${item.dbTableFieldId}`
	)
}

const onChangeTableDataSet = (id: string, isClearAll: boolean = true) => {
	return new Promise((resolve, reject) => {
		filter.value = ''

		if (isClearAll) {
			clearAll()

			if (filterDefaultData.value.length > 0) {
				const defaultTableDataSetId = filterDefaultData.value[0]?.__id.split('/')[0]
				if (defaultTableDataSetId === id) {
					datasetChecked.value = filterDefaultData.value.map((m: any) => m.__id) as any
				}
			}
		}

		const nextChecked = (data: any) => {
			const filterFixedFields = data.filter(
				(f: any) => fixedFields.indexOf(f.dbTableField.name) > -1
			)
			filterFixedFields.forEach((f: any) => {
				datasetChecked.value.unshift(`${f.tableDataSetId}/${f.dbTableFieldId}`)
			})
			onChangeChecked()
			emits('changeSource')
		}

		if (datasetGroupRaw.value.hasOwnProperty(id)) {
			datasetGroup.value = datasetGroupRaw.value[id]
			nextChecked(datasetGroupRaw.value[id])
			return resolve('')
		}

		getTableDataSetById(id).then((res: any) => {
			const fields = res.data.tableDataSetMappingFields
			fields.forEach(
				(f: any) =>
					(f.__dataset = toRaw(dataset.value.find((ff: any) => ff.id === currentDataset.value)))
			)

			fields.sort((a: any, b: any) => {
				const aIndex = fixedFields.indexOf(a.dbTableField.name)
				const bIndex = fixedFields.indexOf(b.dbTableField.name)
				if (aIndex > -1 && bIndex > -1) {
					return aIndex - bIndex
				} else if (aIndex > -1) {
					return -1
				} else if (bIndex > -1) {
					return 1
				} else {
					return 0
				}
			})

			datasetGroup.value = datasetGroupRaw.value[id] = fields
			nextChecked(fields)
			resolve('')
		})
	})
}

const onChangeChecked = () => {
	checkedList.value = datasetChecked.value.map((m: any) => {
		const [tableDataSetId, dbTableFieldId] = m.split('/')
		return datasetGroupRaw.value[tableDataSetId]?.find(
			(f: any) => f.dbTableFieldId === dbTableFieldId
		)
	})
}

const onFilter = (val: string) => {
	datasetGroup.value = datasetGroupRaw.value[currentDataset.value].filter(
		(f: any) => f.displayName.indexOf(val) > -1
	)
}

onMounted(() => {
	console.log('fromDataSource mounted')

	getTableDataSet().then((res: any) => {
		dataset.value = res.data.items
		emits('completed', toRaw(dataset.value))
	})
})
onUnmounted(() => {})

onActivated(() => {
	console.log('fromDataSource activated')
	if (
		(route.query.type === 'edit' || route.query.type === 'view') &&
		!history.state.forward?.includes('/ledgerConfig/ledgerDisplay')
	) {
		getTableDataSet().then((res: any) => {
			dataset.value = res.data.items
			emits('completed', toRaw(dataset.value))
		})
	}
})

defineExpose({
	clear: () => (needClear.value = true),
})
</script>
<template>
	<Dialog v-bind="$attrs" @clickConfirm="onConfirm" :visible="visible">
		<div class="data-source">
			<div class="left">
				<div class="title">可选数据项</div>
				<div class="filter">
					<el-select
						v-model="currentDataset"
						@change="onChangeTableDataSet"
						mr-10px
						w-250px
						placeholder="请选择数据集"
						:disabled="props.disabled"
					>
						<el-option v-for="item of dataset" :label="item.name" :value="item.id" :key="item.id" />
					</el-select>
					<el-input
						v-model="filter"
						placeholder="请输入数据项名称搜索"
						@input="onFilter"
					></el-input>
				</div>
				<el-scrollbar height="400" class="checkbox-group">
					<el-checkbox-group
						v-model="datasetChecked"
						class="group"
						v-if="datasetGroup.length > 0"
						@change="onChangeChecked"
					>
						<el-checkbox
							:disabled="
								fixedFields.indexOf(item.dbTableField.name) > -1 ||
								(props.disabled &&
									datasetChecked.includes(`${item.tableDataSetId}/${item.dbTableFieldId}`))
							"
							:label="`${item.tableDataSetId}/${item.dbTableFieldId}`"
							:title="item.displayName"
							v-for="item of (datasetGroup as any)"
						>
							{{ item.displayName }}
						</el-checkbox>
					</el-checkbox-group>
					<el-skeleton v-else p10px>
						<template #template>
							<el-skeleton :rows="6" />
							<div style="text-align: center; color: #909399; padding: 23px; font-size: 13px">
								{{ filter ? '未查询到该字段' : '未选择数据集' }}
							</div>
						</template>
					</el-skeleton>
				</el-scrollbar>
			</div>
			<div class="right">
				<div class="title">
					<span>已选数据项</span>
					<el-input
						v-model="filterCheckedValue"
						type="text"
						size="small"
						placeholder="输入关键字过滤已选数据项"
						style="width: 200px"
					></el-input>
				</div>
				<ul class="checked-list" style="height: 38px">
					<li class="first">
						<span>数据项名称</span>
						<span>数据类型</span>
						<span>操作</span>
					</li>
				</ul>
				<el-scrollbar class="checked-list">
					<ul v-if="checkedList.length > 0">
						<li
							v-for="item of (checkedList as any)"
							v-show="item?.displayName.includes(filterCheckedValue)"
						>
							<span :title="item?.displayName">{{ item?.displayName }}</span>
							<span>{{ item?.dbTableField.businessType }}</span>
							<span>
								<el-button
									v-if="fixedFields.indexOf(item?.dbTableField.name) === -1"
									type="primary"
									size="small"
									:disabled="props.disabled"
									@click="onRemove(item)"
								>
									移除
								</el-button>
								<template v-else>-</template>
							</span>
						</li>
					</ul>
					<el-empty v-else description="暂无数据" style="height: 403px" />
				</el-scrollbar>
			</div>
		</div>
	</Dialog>
</template>
<style scoped lang="scss">
.data-source {
	display: flex;

	.left {
		flex: 1;
		margin-right: 20px;

		.filter {
			align-items: center;
			display: flex;
		}
	}

	.right {
		flex: none;
		width: 300px;

		.title {
			align-items: center;
			display: flex;
			white-space: nowrap;
			span {
				flex: 1;
			}
		}
	}

	.title {
		border-bottom: var(--z-border);
		font-weight: 500;
		height: 30px;
		margin-bottom: 10px;
	}

	.checkbox-group {
		border-radius: 5px;
		border: var(--z-border);
		height: 400px;
		margin-top: 10px;
		.group {
			display: flex;
			flex-wrap: wrap;
			padding: 0 10px;
		}

		label {
			width: calc(20% - 1px);
			&:nth-child(4n) {
				margin-right: 0;
			}

			:deep(.el-checkbox__label) {
				overflow: hidden;
				text-overflow: ellipsis;
				max-width: 100%;
			}
		}
	}

	.checked-list {
		border-bottom: var(--z-border);
		height: 404px;
		overflow: hidden;

		:deep(.el-scrollbar__view) {
			min-height: 100%;
		}

		li {
			align-items: center;
			border-bottom: var(--z-border);
			display: flex;
			font-size: 13px;
			height: 38px;

			&.first {
				background-color: #fafafa;
				font-weight: 500;
			}

			span {
				flex: 1;
				padding-left: 10px;
				max-width: 100%;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;

				&:not(:first-child) {
					text-align: center;
				}
				&:last-child {
					cursor: pointer;
				}
			}
		}
	}
}
</style>
