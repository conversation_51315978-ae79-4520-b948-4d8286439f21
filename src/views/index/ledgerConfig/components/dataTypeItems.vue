<script setup lang="ts">
import {ref, watch} from 'vue'
import {ElMessage} from 'element-plus'
import {useRoute} from 'vue-router'

interface Props {
	label?: string
	multiple?: boolean
	defaultData?: any
	tableInfo?: any
	defaultMacthData?: []
	openDataTypeItems?:boolean
}
enum ViewType {
	Add = 'add',
	Edit = 'edit',
	View = 'view',
}
const props = withDefaults(defineProps<Props>(), {
	label: '-',
	multiple: false,
	defaultData: [] as any,
	tableInfo: [] as any,
	defaultMacthData: [] as any,
	openDataTypeItems:false
})
const route = useRoute()
const viewTypes = ref<any>(route.query.type)

const emits = defineEmits(['clickConfirm'])
const displayName = ref('')
const radio = ref(null)
const itemsTree = ref<any>([])
const items = ref<any[]>([{value: ''}])
const isOpenView = ref(false)

const hasLongChildren = (array: any) => {
	return array?.some((element: any) => {
		// 检查元素是否有 children 属性且它是一个数组
		if (Array.isArray(element.children) && element.children.length > 0) {
			// 使用 some 方法检查 children 数组中是否有至少一个元素不是空字符串
			return element.children.some((child: any) => child.value !== '')
		}
		// 如果元素没有 children 属性或者 children 数组长度不大于1，返回 false
		return false
	})
}

watch(()=>props.openDataTypeItems,
	(newVal:any)=> { 
		console.log(isSave.value)
		AiValue.value = ''
		console.log(props.defaultData.options)
		if(!isSave.value) { 
			items.value =
			props.defaultData.options && props.defaultData.options.length > 0
					? props.defaultData.options.map((m: any) => ({value: m}))
					: (items.value = [{value: ''}, {value: ''}])
		}
	}
)

watch(
	() => props.defaultData,
	(newVal: any,oldVal:any) => {
		console.log(oldVal)
		// if (newVal && newVal.length > 0) {
		if (newVal) {
			// items.value =  newVal.map((m: any) => ({value: m}))
			items.value =
				newVal.options && newVal.options.length > 0
					? newVal.options.map((m: any) => ({value: m}))
					: (items.value = [{value: ''}, {value: ''}])
			itemsTree.value = newVal.fieldMultipleDto?.multipleArr ?? []
			displayName.value = newVal.fieldMultipleDto?.multipleArrId ?? ''
			let selectRow: any = props.tableInfo.filter(
				(item: any) => item.value === newVal.fieldMultipleDto?.multipleArrId
			)[0]
			names.value = (selectRow && selectRow?.raw?.displayName) || ''
			// activeName.value = 'first'

			console.log(items.value)
			console.log(newVal.options)
		} else {
			items.value = [{value: ''}, {value: ''}]
			// itemsTree.value = []
		}
	},
	{deep: true}
)

const activeName = ref('first')
const handleClick = (tab: any, event: any) => {
	console.log(tab, event)
}

const add = () => {
	itemsTree.value.push({value: '', children: [{value: ''}]})
}
const childrenAdd = (index: any) => {
	activeName.value = 'second'
	console.log(activeName.value)
	if (itemsTree.value[index].children) {
		itemsTree.value[index].children.push({value: ''})
	} else {
		itemsTree.value[index].children = []
		itemsTree.value[index].children.push({value: ''})
	}
}
const childrenADilt = (index: any, i: any) => {
	itemsTree.value[index].children.splice(i, 1)
}

const addChild = (item: any, index: any) => {
	if (itemsTree.value[index].children && itemsTree.value[index].children.length > 0) {
		return
	}
	itemsTree.value[index].children = []
	itemsTree.value[index].children.push({value: ''})
}

const names = ref('')
const selectChange = (val: any) => {
	let selectRow = props.tableInfo.filter((item: any) => item.value === val)[0]
	names.value = selectRow.raw.displayName
	console.log(selectRow.raw)
	console.log(selectRow.raw.options)

	itemsTree.value =
		selectRow.raw.options &&
		selectRow.raw.options.map((item: any) => {
			let obj: any = {}
			obj.value = item
			obj.children = [{value: ''}]
			return obj
		})
}

const onAddItem = () => {
	items.value.push({value: ''})
}

const foundDuplicate = ref(false) // 二级选标记是否找到重复项
const duplicateMessage = ref('') // 一级选存储重复项消息的变量

const checkDuplicates = (array: any) => {
	const indexMap: any = {}

	for (let i = 0; i < array.length; i++) {
		const element = array[i]
		if (indexMap[element] !== undefined) {
			// 如果元素已经存在，打印其索引并停止循环
			foundDuplicate.value = true
			duplicateMessage.value = `选项 '${element}' 在第 ${indexMap[element] + 1} 和 ${i + 1} 中重复`
			break // 跳出循环
		} else {
			foundDuplicate.value = false
		}

		// 否则，将其添加到索引映射中
		indexMap[element] = i
	}
}

let duplicateFound = ref(false)
let duplicateMessageTwo = ref('')

// const checkLevelTwo = (nodes: any) => {
// 	const ids = new Set()

// 	for (const node of nodes) {
// 		if (node.children) {
// 			for (const child of node.children) {
// 				if (ids.has(child.value)) {
// 					// 发现重复项
// 					duplicateFound.value = true
// 					duplicateMessageTwo.value = `字段'${child.value}'在 选项 '${node.value}' 的第二级中有重复`
// 					return // 立即返回，不需要继续检查
// 				} else {
// 					duplicateFound.value = false
// 				}
// 				ids.add(child.value)
// 			}
// 		}
// 	}
// }

const checkLevelTwo = (nodes) => {
	duplicateFound.value = false
	duplicateMessageTwo.value = ''
	for (const node of nodes) {
		if (node.children && Array.isArray(node.children)) {
			const ids = new Set() // 确保在每个node开始时都创建一个新的Set
			let currentNodeDuplicateFound = false // 用于标记当前node是否有重复
			let currentNodeDuplicateMessage = '' // 用于存储当前node的重复信息

			for (const child of node.children) {
				if (ids.has(child.value)) {
					// 发现重复项
					currentNodeDuplicateFound = true
					currentNodeDuplicateMessage = `字段'${child.value}'在 选项 '${node.value}' 的第二级中有重复`
					break // 可以选择立即跳出循环，因为已经找到了重复项
				} else {
					ids.add(child.value)
				}
			}

			// 如果当前node有重复项，则更新全局的duplicateFound和duplicateMessage
			if (currentNodeDuplicateFound) {
				duplicateFound.value = true
				duplicateMessageTwo.value = currentNodeDuplicateMessage

				// 如果只想找到第一个重复就停止检查，可以在这里返回
				return
			}
		}
	}
}
const errorMessage = ref('')

function checkChildrenValues(nodes, parentPath = []) {
	for (let i = 0; i < nodes.length; i++) {
		const currentPath = [...parentPath, i + 1] // 构建当前节点的路径
		if (!nodes[i].value || nodes[i].value.trim() === '') {
			errorMessage.value = `第${currentPath.join('-')}行的输入框未填写。`
			return false
		}
		if (nodes[i].children && !checkChildrenValues(nodes[i].children, currentPath)) {
			return false
		}
	}
	return true
}
const isSave = ref(false)
const onConfirm = () => {
	if (!checkChildrenValues(itemsTree.value)) {
		return ElMessage.error(errorMessage.value)
	}
	checkDuplicates(items.value.map((m: any) => m.value).filter(Boolean))
	if (foundDuplicate.value) {
		return ElMessage.error(duplicateMessage.value)
	}

	checkLevelTwo(hasLongChildren(itemsTree.value) ? itemsTree.value : [])
	if (duplicateFound.value) {
		return ElMessage.error(duplicateMessageTwo.value)
	}
	if (
		items.value.map((m: any) => m.value).filter(Boolean).length > 0 &&
		hasLongChildren(itemsTree.value)
	) {
		isOpenView.value = true
	} else {
		let obj = {
			options: items.value.map((m: any) => m.value).filter(Boolean),
			multipleArr: hasLongChildren(itemsTree.value) ? itemsTree.value : null,
			multipleArrId: hasLongChildren(itemsTree.value) ? displayName.value : '',
		}
		emits('clickConfirm', obj)
		items.value = [{value: ''}, {value: ''}]
		itemsTree.value = []
		displayName.value = ''
		names.value = ''
		activeName.value = 'first'
		radio.value = null
		isSave.value = true
	}

	// emits('clickConfirm',items.value.map((m: any) => m.value).filter(Boolean) )
}

const onConfirmUp = () => {
	if (radio.value == 1) {
		let obj = {
			options: items.value.map((m: any) => m.value).filter(Boolean),
			multipleArr: null,
			multipleArrId: '',
		}
		emits('clickConfirm', obj)
		isSave.value = true

	} else {
		let obj = {
			options: null,
			multipleArr: itemsTree.value,
			multipleArrId: displayName.value,
		}
		emits('clickConfirm', obj)
		isSave.value = true

	}
	isOpenView.value = false
	items.value = [{value: ''}, {value: ''}]
	itemsTree.value = []
	displayName.value = ''
	names.value = ''
	activeName.value = 'first'
	radio.value = null
}
const clickClose = () => {
		AiValue.value = ''
	isSave.value = false
}
const AiValue = ref('')
const onAiadd = ()=> { 
	// 正则表达式：匹配由逗号分隔的一个或多个非空字符串
	// const regex = /^([^，,\s]+[，,])+[^，,\s]+$/;
	const regex = /^(.*\S.*[，,])+.*\S.*$/;
	if(!AiValue.value) { 
		return	ElMessage.warning('请输入选项')
	}
	if(!regex.test(AiValue.value.trim())) { 
		return	ElMessage.warning('请输入用逗号隔开的选项')
	}
	let arr = AiValue.value
          .split(/[,，]/) // 使用中英文逗号分割
          .map((item:any) => item.trim()) // 去除首尾空格
          .filter((item:any) => item !== ""); // 移除空字符串
	if(arr.length > 0) { 
		if(items.value[0].value != ''){
			arr.forEach((it:any)=> { 
			items.value.push({value:it})
			})
		}else  {
			items.value = arr.map((item:any)=> { 
				 return {value:item}
			})
		}
	
		AiValue.value = ''
	}

}
</script>
<template>
	<Dialog v-bind="$attrs" @clickConfirm="onConfirm" :autoHeight="true" @clickClose="clickClose">
		<div class="header">
			<span class="label">业务表列表名: </span>
			<span class="value">{{ props.label }}</span>
			<span class="label">数据类型: </span>
			<span class="value">{{ props.multiple ? '多选' : '单选' }}</span>
			<!-- <el-button size="small" type="primary" @click="onAddItem">添加</el-button> -->
		</div>
		<!-- <div class="items">
			<div class="item" v-for="(item, index) of items">
				<el-input v-model="item.value" placeholder="请输入选项"></el-input>
				<el-button v-if="index > 0" type="danger" @click="items.splice(index, 1)"> 删除 </el-button>
			</div>
		</div> -->
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<el-tab-pane label="一级选项" name="first">
				<div class="items">
					<el-button
						:disabled="viewTypes === ViewType.View"
						size="default"
						type="primary"
						style="margin-bottom: 20px"
						@click="onAddItem"
						>添加</el-button
					>
					<div class="item" style="margin-bottom: 20px;">
						<el-input
							
							:disabled="viewTypes === ViewType.View"
							v-model="AiValue"
							placeholder="多个选项间输入逗号分割。例如：选项一，选项二，选项三"
						></el-input>
						<el-button
							:disabled="viewTypes === ViewType.View  "
							size="default"
							type="primary"
					
							@click="onAiadd"
							>自动识别</el-button
						>

					</div>

					<div class="item" v-for="(item, index) of items">
						<el-input
							:disabled="viewTypes === ViewType.View"
							v-model="item.value"
							placeholder="请输入选项"
						></el-input>
						<el-button
							:disabled="viewTypes === ViewType.View"
							v-if="index > 0"
							type="danger"
							@click="items.splice(index, 1)"
						>
							删除
						</el-button>
				
					</div>
				</div>
			</el-tab-pane>
			<el-tab-pane label="多级选项" name="second">
				<p class="selectForm" style="margin-bottom: 8px">
					<span> 选择字段</span>
					<el-select
						clearable
						style="width: 50%"
						v-model="displayName"
						@change="selectChange"
						:disabled="viewTypes === ViewType.View"
						placeholder="请选择计算方式"
					>
						<el-option
							v-for="item of props.tableInfo"
							:key="item.label"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
				</p>
				<div style="margin-bottom: 20px">配置选项</div>
				<div class="content" v-for="(item, index) of itemsTree">
					<div class="items" style="width: 300px; margin-right: 30px">
						<div class="item">
							<div class="selectForm">
								<p>
									当“<i style="font-style: normal" :title="names">{{ names }}</i
									>”为
								</p>
								<el-button
									size="default"
									:disabled="viewTypes === ViewType.View"
									:title="item.value"
									type="primary"
									@click="addChild(item, index)"
									>{{ item.value }}</el-button
								>
							</div>
						</div>
					</div>
					<div class="items">
						<div class="item" v-for="(it, i) of item.children">
							<p class="tips">当前字段可选</p>
							<div class="chunk">
								<el-input
									:disabled="viewTypes === ViewType.View"
									v-model="it.value"
									placeholder="请输入选项"
								></el-input>
								<el-button
									:disabled="viewTypes === ViewType.View"
									v-if="i !== 0"
									size="small"
									type="danger"
									@click="childrenADilt(index, i)"
								>
									删除
								</el-button>
								<el-button
									:disabled="viewTypes === ViewType.View"
									size="small"
									@click="childrenAdd(index)"
									>添加</el-button
								>
							</div>
						</div>
					</div>
				</div>
			</el-tab-pane>
		</el-tabs>
	</Dialog>
	<Dialog
		v-model="isOpenView"
		title="请选择一级选项还是多级选项"
		width="500"
		@clickConfirm="onConfirmUp"
		@close="isOpenView = false"
		style="overflow: visible"
	>
		<div>
			<div title="监测到你既配置了一级选项右配置了多级选项，请确认一种选项模式">选项模式:</div>
			<el-radio v-model="radio" value="1">使用一级选项</el-radio>
			<el-radio v-model="radio" value="2"> 使用多级选项 </el-radio>
		</div>
	</Dialog>
</template>
<style scoped lang="scss">
.content {
	display: flex;
}

.header {
	align-items: center;
	display: flex;
	border-bottom: var(--z-border);
	padding: 0 0 9px 0;
	margin-bottom: 10px;

	.label {
		font-weight: 500;
		text-align: left;
	}

	.value {
		margin-left: 10px;
		flex: 1;
	}
}

.item {
	align-items: center;
	display: flex;
	margin-bottom: 10px;

	button {
		margin-left: 10px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		width: 100px;

		span {
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;

			display: inline-block;
		}
	}

	:deep(.el-button > span) {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;

		display: inline-block;
	}

	.tips {
		width: 100px;
	}
}

.selectForm {
	display: flex;
	align-items: center;

	span {
		display: inline-block;
		width: 100px;
	}

	p {
		display: flex;
		align-items: center;
	}
}

.chunk {
	width: 250px;
	display: flex;
	align-items: center;
}

i {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	width: 100px;
	display: inline-block;
	text-align: center;
}
</style>
