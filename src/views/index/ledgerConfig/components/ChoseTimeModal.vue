<script setup lang="ts">
import {ref, watch} from 'vue'
import {useRoute} from 'vue-router'

interface Props {
	label?: string
	multiple?: boolean
	defaultDataTime?: []
}
enum ViewType {
	Add = 'add',
	Edit = 'edit',
	View = 'view',
}
const props = withDefaults(defineProps<Props>(), {
	label: '-',
	multiple: false,
	defaultDataTime: [] as any,
})

const emits = defineEmits(['clickConfirmTime'])
const selectTimes = ref('')
const route = useRoute()
const viewTypes = ref<any>(route.query.type)
const RunwayList = [
	{
		name: '年 月 日',
		value: 1,
		type: '2024年5月1日',
	},
	{
		name: '年 月',
		value: 2,
		type: '2024年5月',
	},
	{
		name: '日期时间',
		value: 0,
		type: '2024/5/1 09:00',
	},
	{
		name: '年份',
		value: 3,
		type: '2024年',
	},
	{
		name: '季度',
		value: 4,
		type: '2024年第一季度',
	},
]
watch(
	() => props.defaultDataTime,
	(newVal) => {
		console.log(props.defaultDataTime)
		if (newVal) {
			console.log(newVal)
			selectTimes.value = newVal.displayForm
		} else {
			selectTimes.value = ''
		}
	}
)

const onConfirm = () => {
	emits('clickConfirmTime', selectTimes.value)
	// selectTimes.value = ""
}
</script>
<template>
	<Dialog v-bind="$attrs" @clickConfirm="onConfirm">
		<div class="contentss">
			<div>
				<p style="margin-bottom: 20px; display: flex; width: 400px; align-items: center">
					<span style="display: inline-block; width: 150px"> 时间日期类型: </span>
					<el-select
						:disabled="viewTypes === ViewType.View"
						clearable
						v-model="selectTimes"
						placeholder="请选择时间日期类型"
					>
						<el-option
							v-for="item of RunwayList"
							:key="item.name"
							:label="item.name"
							:value="item.value"
						>
							<span style="float: left">{{ item.name }}</span>
							<span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
								{{ item.type }}
							</span>
						</el-option>
					</el-select>
				</p>
			</div>
		</div>
	</Dialog>
</template>
<style scoped lang="scss">
.contentss {
	display: flex;
	padding-left: 30px;
}

.header {
	align-items: center;
	display: flex;
	border-bottom: var(--z-border);
	padding: 0 0 9px 0;
	margin-bottom: 10px;

	.label {
		font-weight: 500;
		text-align: left;
	}

	.value {
		margin-left: 10px;
		flex: 1;
	}
}

.item {
	align-items: center;
	display: flex;
	margin-bottom: 10px;

	button {
		margin-left: 10px;
	}
}
</style>
