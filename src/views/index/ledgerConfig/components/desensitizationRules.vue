<script setup lang="ts">
import {ref, watch} from 'vue'
import {DesensitizationRules} from '@/define/ledger.define'
import {useRoute} from 'vue-router'

interface Props {
	label?: string
	multiple?: boolean
	defaultData?: any
}

enum ViewType {
	Add = 'add',
	Edit = 'edit',
	View = 'view',
}

const props = withDefaults(defineProps<Props>(), {
	label: '-',
	multiple: false,
})
const route = useRoute()
const viewTypes = ref<any>(route.query.type)
const emits = defineEmits(['clickConfirm'])
const checked = ref<string>('')

watch(
	() => props.defaultData,
	(newVal) => {
		if (newVal) {
			checked.value = DesensitizationRules.find((item) => item.value === newVal)?.label ?? ''
		} else {
			checked.value = ''
		}
	}
)

const onConfirm = () => {
	emits(
		'clickConfirm',
		DesensitizationRules.find((item) => item.label === checked.value)
	)
}
</script>
<template>
	<Dialog v-bind="$attrs" @clickConfirm="onConfirm">
		<div class="header">
			<span class="label">业务表列表名: </span>
			<span class="value">{{ props.label }}</span>
		</div>
		<el-radio-group :disabled="viewTypes === ViewType.View" v-model="checked" class="radio-group">
			<el-radio
				border
				:disabled="viewTypes === ViewType.View"
				:label="item.label"
				v-for="item of DesensitizationRules"
			>
				{{ item.label }}
			</el-radio>
		</el-radio-group>
	</Dialog>
</template>
<style scoped lang="scss">
.header {
	align-items: center;
	display: flex;
	border-bottom: var(--z-border);
	padding: 0 0 9px 0;
	margin-bottom: 10px;

	.label {
		font-weight: 500;
		text-align: left;
	}

	.value {
		margin-left: 10px;
		flex: 1;
	}
}

.radio-group {
	label {
		margin-bottom: 10px;
		min-width: 100px;

		&:nth-child(n) {
			margin-right: 14px;
		}
	}
}
</style>
