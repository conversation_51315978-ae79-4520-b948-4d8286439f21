<script setup lang="ts">
import util from '@/plugin/util'
import {useUserStore} from '@/stores/useUserStore'
import {ElNotification, FormInstance, TreeNode} from 'element-plus'
import {inject, onMounted, ref, watch, onActivated} from 'vue'
const axios = inject('#axios') as any
const props = defineProps({
	data: {type: Array},
	defaultCheckedData: {type: Array},
	placeholder: {type: String},
	type: {type: String, default: 'select'},
})
const hasCollection = ref(false)
const emits = defineEmits<{
	(eventName: 'change', e: any): void
}>()
// 控制下拉类型显示和隐藏
const openModalIsVisible = ref(false)
// 控制弹窗类型显示和隐藏
const openDialogVisible = ref(false)
const treeRef = ref<any>(null)
const selectKeys = ref<string[]>([])
const expandedKeys = ref<string[]>([])
const hasCheckList = ref<any>([])
const checkedDepartmentList = ref<any>([])

const filterText = ref('')

const regionLsit = ref<any[]>([])
const checkRegion = ref<any[]>([])
const departmentList = ref<any[]>([])
const departmentGroupList = ref<any[]>([])
const departmentTotal = ref()
const departmentPage = ref({
	MaxResultCount: 10,
	SkipCount: 0,
})
// 点击取消
const cancel = () => {
	selectKeys.value = []
	checkedDepartmentList.value = []
	hasCheckList.value = []
	openModalIsVisible.value = false
	//     if(checkedDepartmentList.value){
	//     departmentList.value.forEach((e:any) => {
	//         if(e.id!=checkedDepartmentList.value[0].id){
	//             e.checked=false
	//         }

	//     });
	// }
}
const close = () => {
	openDialogVisible.value = false
	if (checkedDepartmentList.value.length > 0 && hasCheckList.value.length == 0) {
		departmentList.value.forEach((e: any) => {
			e.checked = false
		})
	}
}
// 点击确定
const handleOk = () => {
	hasCheckList.value = JSON.parse(JSON.stringify(checkedDepartmentList.value))
	// 抛出事件
	openDialogVisible.value = false
	emits('change', hasCheckList.value)
}
function handleClick(event: any) {
	const isSelf = document.getElementById('content')?.contains(event.target)
	const isModal = document.getElementById('select-modal')?.contains(event.target)
	if (!isSelf && !isModal) {
		openModalIsVisible.value = false
	}
	if (isSelf) {
		openDialogVisible.value = true
	}
}
const deparmentItemId = ref()

async function getRegionList() {
	await axios
		?.request({
			method: 'get',
			url: '/api/platform/region/regions',
			headers: {
				Urlkey: 'iframeCode',
			},
		})
		.then((res: any) => {
			const promise: any[] = []
			const departmentList: any = []
			const {city, district, community, street} = useUserStore().getCurrentDepartment
			const role = community || street || district || city
			const node = res.data.find((f: any) => f.name === role)
			const nodeChild = res.data
				.filter((f: any) => f.parentId === node.id || f.id === node.id)
				.map((c) => ({...c, label: c.name, value: c.id}))
			regionLsit.value = util.arrayToTree(nodeChild)
			checkRegion.value = nodeChild.map((v: any) => v.id)
			console.log(regionLsit.value)
			console.log(nodeChild)

		})
}
async function currentChange(e: number) {
	departmentPage.value.MaxResultCount = 10
	departmentPage.value.SkipCount = departmentPage.value.MaxResultCount * (e - 1)
	getDepartmentList(regionIds.value)
}
async function sizeChange(e: number) {
	departmentPage.value.SkipCount = 0
	departmentPage.value.MaxResultCount = e
	getDepartmentList(regionIds.value)
}
function selectChange(checked: boolean, data: any) {
	console.log(checked, data)
	if (checked) {
		checkedDepartmentList.value = [{...data, name: data.name ? data.name : data.displayName}]
	} else {
		let currentIndex = undefined
		checkedDepartmentList.value.forEach((x: any, index: number) => {
			if (x.name === data.name) {
				currentIndex = index
			}
		})
		if (currentIndex !== undefined) checkedDepartmentList.value.splice(currentIndex, 1)
	}
	console.log(checkedDepartmentList.value, 8888)
	if (checkedDepartmentList.value.length > 0) {
		departmentList.value.forEach((e: any) => {
			if (e.name != checkedDepartmentList.value[0].name) {
				e.checked = false
			}
		})
	}
	console.log(departmentList.value, 77777777777)
}
async function getDepartmentList(regionIds: string[], filter?: string) {
	await axios
		?.request({
			method: 'post',
			url: `/api/platform/departmentInternal/department-extend-list`,
			data: {
				regionIds: regionIds,
				filter: filter ? filter : filterText.value,
				maxResultCount: departmentPage.value.MaxResultCount,
				skipCount: departmentPage.value.SkipCount,
			},

			headers: {
				Urlkey: 'iframeCode',
			},
		})
		.then((res: any) => {
			const {city, district, community, street} = useUserStore().getCurrentDepartment
			const role = community || street || district || city
			departmentList.value = res.data.items.map((e) => {
				return {
					...e,
					checked:
						checkedDepartmentList.value.find((v: any) => v.name === e.name) === undefined
							? false
							: true,
					displayName: e.fullName
						.split('/')
						.slice(
							e.fullName.split('/').findIndex((v) => v.replace('※', '') === role),
							e.fullName.split('/').length
						)
						.join('/'),
				}
			})
			console.log(departmentList.value)

			departmentTotal.value = res.data.totalCount
		})
}
async function getDepartmentGroupList() {
	await axios?.get('/api/filling/plan-task/departmentGroup').then((res) => {
		const {data} = res
		if (data[0]?.departments) {
			data[0].departments = data[0]?.departments?.map((v) => ({
				...v,
				name: v.region === null ? v.name : v.region?.name + '-' + v.name,
			}))
			departmentGroupList.value = util.arrayToTree(data[0].departments)
		} else {
			departmentGroupList.value = []
		}
	})
}
const getList = (data?: any) => {
	document.addEventListener('click', handleClick)
	getRegionList()
	if (props.defaultCheckedData) {
		checkedDepartmentList.value = props.defaultCheckedData || data.defaultCheckedData
		selectKeys.value = checkedDepartmentList.value.map((v: any) => v.name)
		hasCheckList.value = JSON.parse(JSON.stringify(checkedDepartmentList.value))
		expandedKeys.value = Array.from(new Set(hasCheckList.value.map((v: any) => v.parentId ?? null)))
	}
}
onMounted(async () => {
	getList()
})
const onQueryChanged = async () => {
	// treeRef.value!.filter(val)
	console.log(regionIds.value)
	console.log(filterText.value)

	await getDepartmentList(regionIds.value, filterText.value)
}

const prop = {
	value: 'id',
	label: 'label',
	children: 'children',
}
// watch(
// 	() => filterText.value,
// 	(val) => {
// 		treeRef.value!.filter(val)
// 	}
// )
watch(
	() => openModalIsVisible.value,
	(val) => {
		if (val) {
			if (props.type === 'select') {
				deparmentItemId.value = null
			}
			hasCollection.value = false
			if (hasCheckList.value.length !== 0) {
				// 获取默认选中的节点内容
				selectKeys.value = hasCheckList.value.map((v: any) => v.id)
				// 获取默认展开的树节点
				expandedKeys.value = Array.from(
					new Set(hasCheckList.value.map((v: any) => v.parentId ?? null))
				)
			} else {
				if (props.type === 'select') {
					checkedDepartmentList.value = []
					selectKeys.value = []
					expandedKeys.value = []
				}
			}
		}
	}
)
watch(
	() => openDialogVisible.value,
	(val) => {
		if (val) {
			deparmentItemId.value = null
			filterText.value = ''
			hasCollection.value = false
			if (hasCheckList.value.length !== 0) {
				// 获取默认选中的节点内容
				selectKeys.value = hasCheckList.value.map((v: any) => v.id)
				// 获取默认展开的树节点
				expandedKeys.value = Array.from(
					new Set(hasCheckList.value.map((v: any) => v.parentId ?? null))
				)
			} else {
				checkedDepartmentList.value = []
				selectKeys.value = []
				expandedKeys.value = []
			}
		}
	}
)
const regionIds = ref<any[]>([])

const currentRegionIndex = ref(1)

watch(
	() => checkRegion.value,
	async (val) => {
		if (val.length !== 0) {
			regionIds.value = []
			console.log(regionLsit.value)
			if (checkRegion.value.length === regionLsit.value.length) {
				// debugger
				regionIds.value = checkRegion.value.concat(regionLsit.value[0].id)
			} else {
				regionIds.value = checkRegion.value
			}
			await getDepartmentList(regionIds.value)
			await getDepartmentGroupList()
		} else {
			regionIds.value = util.treeToArray(regionLsit.value).map((v) => v.id)
		}
	}
)

defineExpose({
	cancel,
	getList,
})
</script>
<template>
	<div class="content" id="content" :class="{activeClass: openModalIsVisible}" style="width: #fff">
		<div class="placeholder flx" v-if="hasCheckList.length === 0">{{ placeholder }}</div>
		<div class="flx" v-if="hasCheckList.length !== 0">
			<el-tag v-for="(item, index) in hasCheckList" :key="item.id" class="mg-right-5">
				{{ item.displayName ? item.displayName : item.name }}
			</el-tag>
		</div>
	</div>

	<Dialog
		v-model="openDialogVisible"
		:key="openDialogVisible"
		:close-on-click-modal="false"
		id="openDialogVisible"
		title="部门选择"
		width="1000"
		class="select-modal"
		@close="close"
		@clickConfirm="handleOk"
	>
		<div class="top">
			<div class="left" style="height: 440px; width: 100%">
				<div class="title">部门列表</div>
				<div class="w-full df flx aic mg-top-5 pd-5" style="height: 40px">
					<div class="w-full pd-right-10">
						<el-tree-select
							class="w-full"
							v-model="checkRegion"
							:data="regionLsit"
							show-checkbox
							check-on-click-node
							multiple
							collapse-tags
							collapse-tags-tooltip
							:max-collapse-tags="1"
							:render-after-expand="false"
						></el-tree-select>
					</div>
					<div class="df w-full h-full aic"  v-action:enter="onQueryChanged">
						<el-input
							style="margin: 10px 0; flex: 1"
							v-model="filterText"
							@keyup.enter="onQueryChanged"
							placeholder="请输入关键字"
						/>
						<el-button class="mg-left-4" type="primary" @click="onQueryChanged">查询</el-button>
					</div>
				</div>
				<div class="w-full" style="height: 366px">
					<div class="w-full pd-10" style="height: 330px; overflow-y: auto">
						<div class="w-full">
							<el-checkbox
								class="w-full"
								size="small"
								v-for="item in departmentList"
								v-model="item.checked"
								:label="item.displayName"
								@change="(e:boolean)=> selectChange(e,item)"
							></el-checkbox>
						</div>
					</div>
					<div class="w-full df aic jce pd-right-10" style="height: 36px">
						<el-pagination
							class="df jce"
							small
							layout="total,prev,pager,next"
							:total="departmentTotal"
							@current-change="currentChange"
							@size-change="sizeChange"
						></el-pagination>
					</div>
				</div>
			</div>
		</div>
	</Dialog>
</template>
<style lang="scss" scoped>
.content {
	width: 100%;
	// height: 30px;
	border: 1px solid #d9d9d9;
	cursor: pointer;
	display: flex;
	align-items: center;
	font-size: 12px;
	padding: 0 10px;
	position: relative;

	.placeholder {
		color: #aaacb2;
	}

	&:hover {
		border-color: #437bc3;
	}
}

.select-modal {
	margin-top: 10px;
	width: 1000px;
	transition: height 0.1s ease;
	background-color: #fff;
	height: 0px;
	box-shadow: 0px 0px 20px 1px rgba(199, 210, 223, 0.5);
	position: absolute;
	top: 40px;
	z-index: 9999;

	&.unfold {
		height: 300px;
	}

	.top {
		width: 100%;
		height: calc(100% - 40px);
		// overflow-y: auto;
		display: flex;
		div {
			// flex: 1;
			.title {
				height: 40px;
				line-height: 40px;
				padding: 0 10px;
			}
			.select {
				height: 400px;
				overflow-y: auto;
				padding: 10px;
				width: 100%;
				:deep(.el-tree) {
					height: 100%;
				}
			}
			// &:first-child {
			// }
		}
		.left {
			border-right: 1px solid #f0f0f0;
		}
	}
}

.activeClass {
	border-color: #437bc3;
}

input {
	width: 100%;
	height: 100%;
}
.el-button {
	height: 30px !important;
}
</style>
