<script setup lang="ts" name="changeRecord">
import {ref,onActivated,onMounted} from 'vue'
interface Props {
	title: string
	width?: string
  visible?: boolean
}
import {useRoute, useRouter} from 'vue-router'
import {
	ledgerhangeRecord
} from '@/api/LedgerApi'

const route = useRoute()
const props = withDefaults(defineProps<Props>(), {
	title: '搜索',
	width: '30%',
  visible: false,
})

const ledgerld = ref('')
ledgerld.value = route.query.id as string
const drawerComp = ref()
const logList = ref([])
const getLedgerhangeRecord = () =>{
  ledgerhangeRecord({
    LedgerId:ledgerld.value
  }).then((res:any)=>{
    const {data} = res
    data.items.forEach((item:any) =>{
      item.label = item.creationTime
      item.text =`${item.departmentName} ${item.editUserName}`
    })
    logList.value = data.items
  })
}
onMounted(() => {
  getLedgerhangeRecord()
})
</script>
<template>
	<el-drawer
    :modal="false"
    v-bind="$attrs"
		class="drawer-comp"
		ref="drawerComp"
		:title="title"
		:size="width"
		:append-to-body="true"
	>
  <div>
    <Record :sort="'asc'" :data="logList">
      <template #label="{item}">
        <h3 class="color">{{ item.creationTime }}</h3>
      </template>
      <template #text="{item}">
        <p style="color: #000;">更改人：{{item.text}}</p>
        <p class="color" style="margin-top: 4px;" v-for="(row,index) in item.ledgerChangeRecordDetail" :key="index">
          {{ row.description }}
        </p>
      </template>
    </Record>
  </div>
	</el-drawer>
</template>
<style scoped lang="scss">
  .color{
    color: #1961e6;
  }
</style>