<script setup lang="ts">
import {ref, watch ,reactive} from 'vue'
import {ElMessage} from 'element-plus'
import {useRoute} from 'vue-router'

interface Props {
	label?: string
	numberType?:string
	defaultDataMath?: []
	defaultDataNumber?:[]
	tableInfo?: []
	tableData?: []
}
enum ViewType {
	Add = 'add',
	Edit = 'edit',
	View = 'view',
}
const props = withDefaults(defineProps<Props>(), {
	label: '-',
	numberType:'int',
	defaultDataMath: [] as any,
	defaultDataNumber: [] as any,
	tableInfo: [] as any,
	tableData: [] as any,
})

const selectedOperation = ref('')
const selectedLetters = ref('')
const route = useRoute()
const viewTypes = ref<any>(route.query.type)
const checked2 = ref(false)

const isOpenView = ref(false)
const radio = ref(null)
const decimalList = [{
		value:1,
		lable:'0.1'
	},{
		value:2,
		lable:'0.01'
	},{
		value:3,
		lable:'0.001'
	},{
		value:4,
		lable:'0.0001'
	},{
		value:5,
		lable:'0.00001'
	},{
		value:6,
		lable:'0.000001'
	},]

const typeList = [{
		value:0,
		lable:'等于'
	},{
		value:1,
		lable:'不等于'
	},{
		value:2,
		lable:'大于'
	},{
		value:3,
		lable:'大于等于'
	},{
		value:4,
		lable:'小于'
	},{
		value:5,
		lable:'小于等于'
	},{
		value:6,
		lable:'包含在'
	},{
		value:7,
		lable:'不包含在'
	}]
const RunwayList = [
	{
		value: 'sum',
		label: '求和',
	},
	{
		value: 'avg',
		label: '求平均',
	},
	{
		value: 'max',
		label: '最大值',
	},
	{
		value: 'min',
		label: '最小值',
	},
]

const emits = defineEmits(['clickConfirm'])
const formula = ref('')
const levelIndex = ref<any>(null)
const items = ref<any[]>()
const form = reactive<any>({
	precision: 3,
	"type": null,
     "rangeOne": null,
      "rangeTwo": null
})
const activeName = ref('first')


watch(
	() => props.defaultDataMath,
	(newVal: any, oldVal) => {
		if (newVal) {
				items.value = newVal
		
				if (newVal.rpnExpression !== '') {
					const regex =
						/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/
					const ary = newVal.rpnExpression.split(' ')
					levelIndex.value = newVal.level
					const arr = newVal.rpnExpression.split(' ').filter((item) => regex.test(item))
					console.log(arr)
					if (
						ary[newVal.rpnExpression.split(' ').length - 1] === 'max' ||
						ary[newVal.rpnExpression.split(' ').length - 1] === 'min'
					) {
						selectedOperation.value = ary[newVal.rpnExpression.split(' ').length - 1]
					} else if (ary[newVal.rpnExpression.split(' ').length - 2] === '+') {
						selectedOperation.value = 'sum'
					} else {
						selectedOperation.value = 'avg'
					}
					selectedLetters.value = arr
				} else {
					selectedOperation.value = ''
					selectedLetters.value = ''
				}
			

		} else {
			console.log(23232)
			selectedOperation.value = ''
			levelIndex.value = ''
			selectedLetters.value = ''
		}
		console.log(newVal)
	},
	{deep: true}
)
watch(
	() => props.defaultDataNumber,
	(newVal: any, oldVal) => {
		if (newVal) {
				if(newVal.calculateRule) { 
					activeName.value = 'second'
				}else { 
					activeName.value = 'first'
				}
				form.precision = newVal.precision || 3
				if(newVal.customValueLimit&&newVal.customValueLimit?.rangeOne !== null) { 
					form.type = newVal.customValueLimit?.type
					form.rangeOne = newVal.customValueLimit?.rangeOne
					form.rangeTwo = newVal.customValueLimit?.rangeTwo
					checked2.value = true
				}else  { 
					checked2.value = false
					form.rangeOne = null
					form.rangeTwo = null
					form.type = null
				}
			
		} 
		console.log(newVal)
	},
	{deep: true}
)
const generateFormula = () => {
	if (selectedLetters.value.length < 2) {
		return ''
	}
	const letterStr = selectedLetters.value
	let operationStr = ''
	let suffix = ''

	if (selectedOperation.value === 'sum') {
		operationStr = '+ '.repeat(letterStr.length - 1)
	} else if (selectedOperation.value === 'avg') {
		operationStr = '+ '.repeat(letterStr.length - 1)
		suffix = ` ${letterStr.length} /`
	} else {
		operationStr =
			selectedOperation.value + ' '.repeat(letterStr.length - 1) + selectedOperation.value
		operationStr = operationStr.trim()
	}

	formula.value = `${letterStr} ${operationStr}${suffix}`.split(',').join(' ')
}


// const onCancel = ( ) => {
// 	formula.value = ""
// 	selectedOperation.value = ""
// 	selectedLetters.value = ""
// 	levelIndex.value = ""

// }
const onclosed = () => {
	formula.value = ''
	selectedOperation.value = ''
	selectedLetters.value = ''
	levelIndex.value = ''
}

const onChange = () => {
	if (props.tableInfo.length < 2) {
		return ElMessage.warning('整数或小数类型必须大于1个')
	}
}
const handleClick = (tab: any, event: any) => {
	console.log(tab, event)
}
const onConfirm = () => {
	console.log(activeName.value)

	if((form.rangeOne&& selectedOperation.value !== ''&&selectedLetters.value !== '')) { 
		return isOpenView.value = true
	}

	if(activeName.value === 'first') { 
		if(checked2.value && (form.type === null|| form.type === ''|| form.type === undefined) ) { 
			console.log(form.type )
			console.log(form)
			return ElMessage.warning(`请选择输入范围1`)
		}
		if(checked2.value && (form.rangeOne === null || form.rangeOne === '')  ) { 
			return ElMessage.warning(`请选择输入范围2`)
		}
		if(checked2.value &&(form.type === 6 ||form.type === 7 )&&!form.rangeTwo  ) { 
			return ElMessage.warning(`请选择输入范围3`)
		}
		console.log(form)
		let obj:any =  {
			customValueLimit: {
				"type": form.type,
				"rangeOne": form.rangeOne,
				"rangeTwo": form.rangeTwo,
				"__types":props.numberType
			}
		}
		if(checked2.value) { 
			emits('clickConfirm', {...obj,...form,modelType:'number'})
		}else  {
			emits('clickConfirm', {customValueLimit:null,precision:form.precision,modelType:'number'})
		}
		form.rangeOne = null
		form.rangeTwo = null
		form.type = null
	
		checked2.value = false
	}else {
			const levelIndexs:any = props.tableData.filter(
			(item: any) => item.calculateRule && item.calculateRule.rpnExpression !== ''
			).length

			if (!selectedOperation.value) {
				return ElMessage.warning(`请选择计算方式`)
			}
			if (selectedLetters.value.length < 2) {
				return ElMessage.warning(`选择的计算内容必须大于等于2条`)
			}

			generateFormula()
			let obj = {
				type: 1, // 默认为1
				rpnExpression: formula.value, // rpn表达式
				level: levelIndex.value ? levelIndex.value : levelIndexs + 1,
				modelType:'calculation',
				precision:form.precision
			}
			emits('clickConfirm', {...obj})
			formula.value = ''
			selectedOperation.value = ''
			selectedLetters.value = ''
			levelIndex.value = ''
	}

}
const onConfirmUp = () => {
	if (radio.value == 1) {
		let obj:any =  {
			customValueLimit: {
				"type": form.type,
				"rangeOne": form.rangeOne,
				"rangeTwo": form.rangeTwo,
				"__types":props.numberType
			}
		}
		if(checked2.value) { 
			emits('clickConfirm', {...obj,...form,modelType:'number'})
		}else  {
			emits('clickConfirm', {customValueLimit:null,precision:form.precision,modelType:'number'})
		}
	} else {
		const levelIndexs:any = props.tableData.filter(
			(item: any) => item.calculateRule && item.calculateRule.rpnExpression !== ''
			).length

			if (!selectedOperation.value) {
				return ElMessage.warning(`请选择计算方式`)
			}
			if (selectedLetters.value.length < 2) {
				return ElMessage.warning(`选择的计算内容必须大于等于2条`)
			}

			generateFormula()
			let obj = {
				type: 1, // 默认为1
				rpnExpression: formula.value, // rpn表达式
				level: levelIndex.value ? levelIndex.value : levelIndexs + 1,
				modelType:'calculation',
				precision:form.precision
			}
			emits('clickConfirm', {...obj})

	}
	formula.value = ''
	selectedOperation.value = ''
	selectedLetters.value = ''
	levelIndex.value = ''
	form.rangeOne = null
	form.rangeTwo = null
	form.type = null
	form.precision = null
	checked2.value = false
	isOpenView.value = false
	activeName.value = 'first'
	radio.value = null
}
function truncateDecimal(num:any, maxDecimalPlaces = 3) {
	console.log(num)
	// 转换为字符串
	const strNum = String(num)

	// 找到小数点的位置
	const decimalIndex = strNum.indexOf('.')

	// 如果没有小数点，直接返回原始字符串
	if (decimalIndex === -1) {
		return parseFloat(strNum)
	}

	// 计算小数部分的长度
	const decimalLength = strNum.length - decimalIndex - 1

	// 如果小数部分长度已经超过最大小数位数，则截断
	if (decimalLength > maxDecimalPlaces) {
		return parseFloat(strNum.slice(0, decimalIndex + 1 + maxDecimalPlaces))
	}

	// 否则返回原始字符串
	return parseFloat(strNum)
}
const changePre = () => { 
	form.rangeOne = null
	form.rangeTwo = null

}
const inputOne = (val:any)=> { 
	const pro = form.precision
	if(pro) { 
		console.log(truncateDecimal(val,pro))
		form.rangeOne = truncateDecimal(val,pro)
	}else{ 
		form.rangeOne = truncateDecimal(val,3)
	}
	
}
const inputTwo = (val:any)=> { 
	const pro = form.precision
	if(pro) { 
		console.log(truncateDecimal(val,pro))

		form.rangeTwo = truncateDecimal(val,pro)
	}else{ 
		form.rangeTwo = truncateDecimal(val,3)
	}
	
}
</script>
<template>
	<Dialog v-bind="$attrs" @clickConfirm="onConfirm">
		<div class="header">
			<span class="label">业务表列表名: </span>
			<span class="value">{{ props.label }}</span>
			<span class="label">数据类型: </span>
			<span class="value">{{ props.numberType==='int' ? '整数' : '小数' }}</span>

		</div>
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<el-tab-pane label="普通数值" name="first">
				<el-form :model="form" label-width="auto" v-if="props.numberType === 'decimal'">
					<el-form-item label="数据精度"  prop="precision">
						<el-select
								clearable
								v-model="form.precision"
								placeholder="请选择精度"
								filterable
								@change="changePre"
							>
								<el-option
									v-for="item of decimalList"
									:key="item.value"
									:label="item.lable"
									:value="item.value"
								/>
							</el-select>
					</el-form-item>
				</el-form>
				<el-checkbox v-model="checked2">限制范围</el-checkbox>
				<el-form-item label="允许输入范围" v-if="checked2" required>
					<el-col :span="7" style="margin-right: 10px;">
						<el-form-item prop="type">
								<el-select
									clearable
									filterable
									v-model="form.type"
									placeholder="请选择"
								>
									<el-option
										v-for="item of typeList"
										:key="item.value"
										:label="item.lable"
										:value="item.value"
									/>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="7">
							<el-form-item prop="rangeOne">
									<el-input-number @change="inputOne" controls-position="right" v-model="form.rangeOne" />
								</el-form-item>
						</el-col>
						<el-col :span="2" class="text-center" v-if="form.type === 6 ||form.type === 7 ">
							<span class="text-gray-500">和</span>
						</el-col>
						<el-col :span="7" v-if="form.type === 6 ||form.type === 7 ">
							<el-form-item prop="rangeTwo">
									<el-input-number @change="inputTwo" controls-position="right" v-model="form.rangeTwo" />
								</el-form-item>
						</el-col>
					</el-form-item>
			</el-tab-pane>
			<el-tab-pane label="计算结果" name="second">
				<div class="content">
					<div>
						<p style="margin-bottom: 20px">
							<span> 计算方式： </span>
							<el-select
								:disabled="viewTypes === ViewType.View"
								clearable
								v-model="selectedOperation"
								placeholder="请选择计算方式"
							>
								<el-option
									v-for="item of RunwayList"
									:key="item.label"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</p>
						<p>
							<span> 计算内容： </span>
							<el-select
								clearable
								multiple
								@change="onChange"
								v-model="selectedLetters"
								:disabled="viewTypes === ViewType.View"
								placeholder="请选择计算内容"
							>
								<el-option
									v-for="item of props.tableInfo"
									:key="item.label"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</p>
						<!-- <p style="margin-top: 10px; display: flex; align-items: center">
							<el-icon mr-5px>
								<WarningFilled /> </el-icon
							>计算结果非整数时保留小数点后3位。
						</p> -->
					</div>
				</div>
			</el-tab-pane>
			
		</el-tabs>
	</Dialog>

	<Dialog
		v-model="isOpenView"
		title="请选择普通数值还是计算结果"
		width="500"
		@clickConfirm="onConfirmUp"
		@close="isOpenView = false"
		style="overflow: visible"
	>
		<div>
			<div title="监测到你既配置了普通数值又配置了计算结果，请确认一种选项模式">选项模式:</div>
			<el-radio v-model="radio" value="1">使用普通数值</el-radio>
			<el-radio v-model="radio" value="2"> 使用计算结果 </el-radio>
		</div>
	</Dialog>
</template>
<style scoped lang="scss">
.header {
	align-items: center;
	display: flex;
	border-bottom: var(--z-border);
	padding: 0 0 9px 0;
	margin-bottom: 10px;

	.label {
		font-weight: 500;
		text-align: left;
	}

	.value {
		margin-left: 10px;
		flex: 1;
	}
}

.item {
	align-items: center;
	display: flex;
	margin-bottom: 10px;

	button {
		margin-left: 10px;
	}
}

.content {
	display: flex;
	padding-left: 30px;
}
</style>
