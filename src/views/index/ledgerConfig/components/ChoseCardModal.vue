<script setup lang="ts">
import {ref, watch} from 'vue'
import {useRoute} from 'vue-router'
import {ElMessage,} from 'element-plus'

interface Props {
	label?: string
	multiple?: boolean
	defaultDataCard?: []
}
enum ViewType {
	Add = 'add',
	Edit = 'edit',
	View = 'view',
}
const props = withDefaults(defineProps<Props>(), {
	label: '-',
	multiple: false,
	defaultDataCard: [] as any,
})

const emits = defineEmits(['clickConfirmCard'])
const selectCard = ref('')
const route = useRoute()
const viewTypes = ref<any>(route.query.type)
const RunwayList = [
	{
		name: '居民身份证号',
		value: 'identification_number',
	},
	{
		name: '护照号',
		value: 'passport',
	},
	{
		name: '港澳居民来往内地通行证',
		value: 'hk_macao_permit',
	},
	{
		name: '台湾居民来往内地通行证',
		value: 'taiwan_permit',
	},

]
watch(
	() => props.defaultDataCard,
	(newVal) => {
		console.log(props.defaultDataCard)
		if (newVal) { 
			if(newVal.customValueOptions&& newVal.customValueOptions.length > 0) {
				selectCard.value = newVal?.customValueOptions.map(obj => obj.name) 
			}else { 
				selectCard.value = ['identification_number']
			}
		} else {
			selectCard.value = ''
		}
	}
)

const onConfirm = () => {
	if(selectCard.value.length <= 0) { 
		return ElMessage.warning('请选择证件类型')
	}
	emits('clickConfirmCard', selectCard.value.map(item => ({ name: item })))
	
	// selectCard.value = ""
}
</script>
<template>
	<Dialog v-bind="$attrs" @clickConfirm="onConfirm">
		<div class="contentss">
			<div>
				<p style="margin-bottom: 20px; display: flex; width: 400px; align-items: center">
					<span style="display: inline-block; width: 150px"> 证件类型: </span>
					<el-select
						:disabled="viewTypes === ViewType.View"
						clearable
						multiple
						v-model="selectCard"
						placeholder="请选择证件类型"
					>
						<el-option
							v-for="item of RunwayList"
							:key="item.name"
							:label="item.name"
							:value="item.value"
						>
						</el-option>
					</el-select>
				</p>
			</div>
		</div>
	</Dialog>
</template>
<style scoped lang="scss">
.contentss {
	display: flex;
	padding-left: 30px;
}

.header {
	align-items: center;
	display: flex;
	border-bottom: var(--z-border);
	padding: 0 0 9px 0;
	margin-bottom: 10px;

	.label {
		font-weight: 500;
		text-align: left;
	}

	.value {
		margin-left: 10px;
		flex: 1;
	}
}

.item {
	align-items: center;
	display: flex;
	margin-bottom: 10px;

	button {
		margin-left: 10px;
	}
}
</style>
