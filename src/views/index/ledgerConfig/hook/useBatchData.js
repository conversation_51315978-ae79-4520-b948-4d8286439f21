import {updateLedgerOnline, deleteOfflineLedger} from '@/api/LedgerApi'
export const useBatchData = () => {
	const onlineByIds = async (ids, status, startImmediately) => {
		const promise = []
		ids.forEach((id) => {
			promise.push(updateLedgerOnline(id, status, startImmediately))
		})
		await Promise.all(promise)
		ElMessage.success(`批量${status ? '上线' : '下线'}成功`)
	}

	const deleteByIds = async (ids) => {
		const promise = []
		ids.forEach((id) => {
			promise.push(deleteOfflineLedger({ledgerId: id}))
		})
		await Promise.all(promise)
		ElMessage.success('批量删除成功')
	}

	return {
		onlineByIds,
		deleteByIds,
	}
}
export default useBatchData
