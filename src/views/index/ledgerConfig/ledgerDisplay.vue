<script setup lang="ts" name="ledgerdisplay">
import {computed, onActivated, onMounted, ref, toRaw, onUnmounted, nextTick} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {ElMessage, ElMessageBox} from 'element-plus'
import {
	createLedger,
	updateLedgerById,
	getDetailByLedgerId,
	deleteRelations,
	checkTaskItemExists,
} from '@/api/LedgerApi'
import {deleteLabel} from '@/hooks/useLabels'

import util from '@/plugin/util'
import {useArrayToTree} from '@/hooks/useConvertHook'
import phoneImg from './assets/phone.png'

const route = useRoute()
const router = useRouter()
const datas = ref<any>([])
const currentfield = ref<any>()
const tableRef = ref<any>(null)
const tableHeader = ref<any>([])
const tableHeaderData = ref<any>([])
const treeData = ref<any>()

const allfield = ref<any>()
const isBlockGroup = ref<any>(false)
const isOpenView = ref<any>(false)
console.log(router.currentRoute.value)

const ClosePhoneModal = () => {
	isOpenView.value = false
}

// 生成表格表头
const createTableHedaer = () => {
	tableHeaderData.value = []
	tableHeader.value = []
	datas.value.forEach((item: any, index: any) => {
		if (!item.children) {
			tableHeaderData.value.push({title: item.label, field: item.field, align: 'center'})
		} else {
			// 循环判断方法
			tableHeaderData.value.push({
				title: item.label,
				field: item.field,
				align: 'center',
				children: [],
			})
			checkoutIsChildren(item.children, tableHeaderData.value[index].children)
		}
		console.log(tableHeader,'cesss')
	})
	//生成完成后循环去判断查找一下最后一级field是否是空
	setTimeout(() => {
		tableHeader.value = tableHeaderData.value
	}, 1)
}

const phoneData = ref([])

function processTree(tree, parentNames = '') {
	const result = []

	for (const node of tree) {
		const displayName = node.displayName || node.label
		// 构建当前节点的完整名称（包含所有父级名称）
		const currentNames = parentNames ? `${parentNames} - ${displayName}` : displayName
		// 检查当前节点是否有符合条件的clientSettings
		const filteredSettings = node.clientSettings?.filter((s) => s.isDisplay)

		if (filteredSettings?.length) {
			// 如果有符合条件的clientSettings，则处理该节点
			const newNode = {...node} // 创建新对象以避免直接修改原始数据
			delete newNode.children // 如果不需要子节点，则删除它们
			newNode.names = currentNames // 设置names属性

			// 保留筛选出的clientSettings（如果需要）
			newNode.clientSettings = filteredSettings.map((s) => ({...s, names: newNode.names}))

			result.push(newNode) // 将处理后的节点添加到结果数组中
		}

		// 如果节点有子节点，则递归处理它们，并传递累积的父级名称
		if (node.children) {
			const childResults = processTree(node.children, currentNames)
			result.push(...childResults) // 将子节点的结果添加到结果数组中
		}
	}

	return result
}

// 生成移动端预览
const createPhoneViews = () => {
	console.log(datas.value)
	const res = processTree(datas.value)
	console.log(res)
	phoneData.value = res
	maxWidth.value = window.innerWidth * 0.5
	isOpenView.value = true
}
const handleResize = () => {
	maxWidth.value = window.innerWidth * 0.5
}

const checkoutIsChildren = (itemData: any, headerData: any) => {
	itemData.forEach((item: any, index: any) => {
		if (!item.children) {
			headerData.push({title: item.label, field: item.field, align: 'center'})
		} else {
			headerData.push({title: item.label, field: item.field, align: 'center', children: []})
			checkoutIsChildren(item.children, headerData[index].children)
		}
	})
}

const selectTreeNode = ref<any>({})
const tree = ref<any>()
const treePhone = ref<any>()
// 选中节点时候触发
const treeNodeClick = (node: any, e: any) => {
	console.log(e)
	selectTreeNode.value = e.parent
}
// 添加同级节点
const addTreeGroup = () => {
	const newNode = {
		id: util._guid(),
		label: '新建节点',
		ischange: true,
		changetype: false,
		parentId: selectTreeNode.value.data.id ? selectTreeNode.value.data.id : null,
		isDelete: true,
	}
	console.log(selectTreeNode.value)

	console.log(selectTreeNode.value.id)

	if (selectTreeNode.value.id !== undefined || selectTreeNode.value.id !== null) {
		tree.value.append(newNode, selectTreeNode.value)
	} else {
		ElMessage('请选择节点')
	}
}
const deleteTreeNode = (node: any, data: any) => {
	console.log(node, data)
	if (data.isDelete) {
		if (!data.children || data.children.length == 0) {
			ElMessageBox({
				title: '是否删除',
				message: `是否删除该节点`,
			})
				.then(() => {
					const parent = node.parent
					const children: any[] = parent.data.children || parent.data
					const index = children.findIndex((d) => d.$treeNodeId === data.$treeNodeId)
					children.splice(index, 1)
					ElMessage.success('删除成功')
				})
				.catch((action) => {
					// console.log(action)
				})
		} else {
			ElMessage.warning('当前节点内存在内容请清空节点内容后删除')
		}
	}
}
const onNodeBlur = (node: any, data: any) => {
	data.changeType = false
}
const tableFieldGroups = ref<any>([])
const tableFields = ref<any>([])

let sort = 1

const onNodeDrop = (before: any, after: any, inner: any) => {
	console.log(before, after, inner, '测试数据')

	if (Array.isArray(after.parent.data)) {
		console.log('进入Array.isArray(after.parent.data，inner为', inner)

		if (inner === 'inner') {
			console.log('if 进入了inner')
			console.log(currentfield.value)
			if (!before.data.displayName) {
				console.log('before没有displayName')
				before.data.parentId = after.data.id
			}
		} else {
			before.data.parentId = null
			before.data.tableFieldGroupId = null

			currentfield.value?.tableInfo?.fields.some((f: any) => {
				if (f.id === before.data.id) {
					console.log(99999, f)

					f.tableFieldGroupId = null
					return true
				}
			})
		}
	} else {
		console.log('没有进入Array.isArray(after.parent.data')

		currentfield.value?.tableInfo?.tableFieldGroups.some((f: any) => {
			if (f.id === before.data.id) {
				f.parentId = after.parent.data.id
			}
		})
		before.data.parentId = after.parent.data.id
	}

	// localStorage.setItem("datas",JSON.stringify(datas.value))
}

const loopFields = (arr: any) => {
	let i = 0
	let next: any = []
	arr.forEach((item: any, index: any) => {
		if (item.children && item.children.length > 0) {
			next = [...next, ...item.children]
		}
		item.sort = sort
		sort++
		if (item.children && item.children.length > 0) {
			item.tableFields = toRaw(item.children).filter((f: any) => f.hasOwnProperty('field'))
			item.tableFields.forEach((f: any) => {
				f.parentId = item.id
				if (f.id) {
					f.oldId = f.id
				}
				if (f.clientSettings.length !== 0 && f.clientSettings[0].sort === null) {
					f.clientSettings = []
				}
			})
			tableFieldGroups.value.push({
				id: item.id,
				name: item.label,
				style: 'center',
				sort: item.sort,
				parentId: item.parentId,
				tableFields: item.tableFields,
			})
		} else if (item.field && !item.parentId) {
			console.log(currentfield.value)
			console.log(item)
			console.log(arr)

			currentfield.value.tableFields.find((f: any) => f.name == item.name).sort = item.sort
			if (currentfield.value.tableInfo) {
				currentfield.value.tableInfo.fields.find((f: any) => f.name == item.name).sort =
					item.sort
			}
		}
		if (!item.children && !item.field) {
			i++
		}
		if (i > 0) {
			isBlockGroup.value = true
			return
		} else {
			isBlockGroup.value = false
		}
		if (index === arr.length - 1 && next.length > 0) {
			loopFields(next)
		}
	})
}

function compareObjects(obj1: any, obj2: any) {
	// 获取两个对象的属性名数组
	const keys1 = Object.keys(obj1)
	const keys2 = Object.keys(obj2)

	// 检查属性数量是否一致
	if (keys1.length !== keys2.length) {
		return false
	}

	// 遍历所有属性进行比较
	for (const key of keys1) {
		// 确保obj2有相同的属性
		if (!obj2.hasOwnProperty(key)) {
			return false
		}
		// 使用Object.is进行严格值比较（处理NaN和±0的情况）
		if (!Object.is(obj1[key], obj2[key])) {
			return false
		}
	}

	return true
}

const getfields = ref<any>([])
const saveLoading = ref(false)
const isOpenSub = ref(false)

const ensureParentIdConsistency = (node: any, parentId?: string) => {
	if (!node) return

	// 如果当前节点有 children 且长度不为 0
	if (node.children && node.children.length > 0) {
		node.children.forEach((child: any) => {
			// 确保子节点的 parentId 与当前节点的 id 一致
			if (child.parentId !== node.id) {
				child.parentId = node.id
			}
			// 递归调用，处理子节点的 children
			ensureParentIdConsistency(child, node.id)
		})
	}
}

const submitComfirm = () => {
	// saveLoading.value = true
	tableFieldGroups.value = []
	tableFields.value = []
	sort = 1
	console.log(currentfield.value)

	if (route.query.type == 'add') {
		currentfield.value.tableFields
			.filter((f: any) => f?.isListField == false)
			.forEach((item: any) => {
				item.sort = 999
			})
		currentfield.value.tableFields.forEach((item: any) => {
			if (item.clientSettings.length !== 0 && item.clientSettings[0].sort === null) {
				item.clientSettings = []
			}
		})
	} else {
		allfield.value &&
			allfield.value
				.filter((f: any) => f.isListField == false)
				.forEach((item: any) => {
					item.sort = 999
					if (item.clientSettings.length !== 0 && item.clientSettings[0].sort === null) {
						item.clientSettings = []
					}
				})

		allfield.value &&
			allfield.value.forEach((item: any) => {
				if (item.clientSettings.length !== 0 && item.clientSettings[0].sort === null) {
					item.clientSettings = []
				}
			})
		currentfield.value.tableFields = allfield.value
	}
	// debugger
	// 确保 parentId 一致性
	datas.value.forEach((node: any) => {
		ensureParentIdConsistency(node)
	})
	loopFields(datas.value)
	console.log('currentfield.value', currentfield.value)

	const createLedgerJSON = JSON.parse(localStorage.getItem('CreateLedger') as any)
	console.log('createLedgerJSON', createLedgerJSON)
	currentfield.value = {
		...currentfield.value,
		...{
			name: createLedgerJSON.name,
			runway: createLedgerJSON.runway,
			ledgerTypeId: createLedgerJSON.ledgerTypeId,
			updateDescription: createLedgerJSON.updateDescription,
			tableFieldGroups: tableFieldGroups.value,
		},
	}
	console.log(currentfield.value)
	if (
		isBlockGroup.value ||
		datas.value.findIndex(
			(f: any) => !f.children || (f.children && f.children == 0 && !f.field)
		) != -1
	) {
		ElMessage.warning('请删除无用空节点后再保存!')
		saveLoading.value = false
	} else {
		getfields.value = []
		tableFieldGroups.value.forEach((item: any) => {
			getfields.value = [...getfields.value, ...item.tableFields]
		})
		getfields.value.forEach((item: any) => {
			for (let i = 0; i < currentfield.value.tableFields.length; i++) {
				if (currentfield.value.tableFields[i].name === item.name) {
					currentfield.value.tableFields[i].sort = item.sort
					break
				}
			}
		})
		console.log(currentfield.value.tableFields)
		currentfield.value.tableFields.forEach((item: any) => {
			if (item.type === 'string_0' || item.type === 'string_500') {
				item.type = item.type.split('_')[0]
			}
		})
		console.log(currentfield.value.tableFields)

		currentfield.value.tableFields.forEach((item) => {
			if (item.fieldMultipleDto && item.fieldMultipleDto?.multipleArrId) {
				console.log(item.fieldMultipleDto)
				item.fieldMultipleDto.multipleArr = item.fieldMultipleDto.multipleArr.map((it) => {
					console.log(it)
					let newObj = {
						value: it.value,
						children: it.children ? it.children.map((child) => child.value) : [], // 提取 children 中的 value 值
					}
					return newObj
				})
			}
		})
		console.log(currentfield.value.tableFields)

		currentfield.value.tableFields.forEach((item: any) => {
			if (!item.options) {
				item.options = []
			}
		})
		console.log(currentfield.value.tableFields)

		tableFieldGroups.value.forEach((item: any) => {
			item.tableFields.forEach((it2: any) => {
				if (it2.fieldMultipleDto) {
					it2.fieldMultipleDto.multipleArr = it2.fieldMultipleDto.multipleArr.map(
						(it3: any) => {
							console.log(it3)
							let newObj = {
								value: it3.value,
								children: it3.children
									? it3.children.map((child: any) => child.value)
									: [], // 提取 children 中的 value 值
							}
							return newObj
						}
					)
				}
			})
		})
		console.log(currentfield.value.tableFields)

		if (route.query.type == 'add') {
			console.log('tableFieldGroups.value', tableFieldGroups.value)
			console.log(' currentfield.value.tableFields,', currentfield.value.tableFields)
			createLedger({
				name: currentfield.value.name,
				runway: currentfield.value.runway,
				ledgerTypeId: currentfield.value.ledgerTypeId,
				updateDescription: currentfield.value.updateDescription,
				tableFieldGroups: tableFieldGroups.value,
				tableFields: currentfield.value.tableFields,
				reminderConfig: currentfield.value.reminderConfig,
				workflowSchemeCode: currentfield.value.workflowSchemeCode,
				departmentIds: CreateLedger.value.departmentIds,
				auditType: CreateLedger.value.auditType,
				isDepartmentalAuthorization: CreateLedger.value?.isDepartmentalAuthorization,
				exportSmallDataWorkflowSchemeCode:
					CreateLedger.value.exportSmallDataWorkflowSchemeCode,
				exportLargeDataWorkflowSchemeCode:
					CreateLedger.value.exportLargeDataWorkflowSchemeCode,
				businessLedgerCategory: CreateLedger.value.businessLedgerCategory,
			})
				.then((res: any) => {
					localStorage.removeItem('CreateLedger')
					localStorage.removeItem('datas')
					localStorage.removeItem('allfield')
					localStorage.removeItem('disPlayMobileList')
					ElMessage.success('业务表添加成功')
					saveLoading.value = false
					deleteLabel({path: localStorage.getItem('currentRoute')})
					router.push('/ledgerConfig')
				})
				.catch((err) => (saveLoading.value = false))
		} else if (route.query.type == 'edit') {
			const currentRunway = localStorage.getItem('currentRunway')
			// debugger
			// 如果当前业务表和当前选择的业务表不一致，则调用修改跑道接口
			if (currentRunway !== currentfield.value.runway) {
				const params = {
					ledgerId: route.query.ledgerId,
					newRunway: currentfield.value.runway,
					oldRunway: currentRunway,
				}
				deleteRelations(params)
			}
			console.log('tableFieldGroups.value', tableFieldGroups.value)
			console.log(' currentfield.value.tableFields,', currentfield.value.tableFields)
			// 弹框
			let oldreminderConfig = JSON.parse(
				localStorage.getItem('CreateLedger') as any
			).reminderConfig
			const queryData = ref({
				name: currentfield.value.name,
				runway: currentfield.value.runway,
				ledgerTypeId: currentfield.value.ledgerTypeId,
				updateDescription: currentfield.value.updateDescription,
				tableFieldGroups: tableFieldGroups.value,
				tableFields: currentfield.value.tableFields,
				reminderConfig: CreateLedger.value.reminderConfig,
				workflowSchemeCode: CreateLedger.value.workflowSchemeCode,

				departmentIds: CreateLedger.value.departmentIds,
				auditType: CreateLedger.value.auditType,
				isDepartmentalAuthorization: CreateLedger.value?.isDepartmentalAuthorization,
				exportSmallDataWorkflowSchemeCode:
					CreateLedger.value.exportSmallDataWorkflowSchemeCode,
				exportLargeDataWorkflowSchemeCode:
					CreateLedger.value.exportLargeDataWorkflowSchemeCode,
				businessLedgerCategory: CreateLedger.value.businessLedgerCategory,
			})
			// debugger
			if (compareObjects(oldreminderConfig, oldReminderConfig.value)) {
				updateLedgerById(route.query.ledgerId as any, queryData.value)
					.then(() => {
						ElMessage.success('业务表更新成功')
						localStorage.removeItem('CreateLedger')
						localStorage.removeItem('datas')
						localStorage.removeItem('allfield')
						localStorage.removeItem('disPlayMobileList')
						saveLoading.value = false
						deleteLabel({path: localStorage.getItem('currentRoute')})
						router.push('/ledgerConfig')
					})
					.catch((err) => (saveLoading.value = false))
			} else {
				if (oldreminderConfig.interval !== oldReminderConfig.value.interval) {
					checkTaskItemExists({
						bindObjectId: route.query.ledgerId,
						intervalType: oldreminderConfig.interval,
					}).then((res) => {
						const data = res.data
						if (!data && 0) {
							ElMessageBox.alert('周期已经更改，请确定', '消息确认', {
								confirmButtonText: '确定',
								callback: () => {
									updateLedgerById(route.query.ledgerId as any, queryData.value)
										.then(() => {
											ElMessage.success('业务表更新成功')
											localStorage.removeItem('CreateLedger')
											localStorage.removeItem('datas')
											localStorage.removeItem('allfield')
											localStorage.removeItem('disPlayMobileList')
											saveLoading.value = false
											deleteLabel({
												path: localStorage.getItem('currentRoute'),
											})
											router.push('/ledgerConfig')
										})
										.catch((err) => (saveLoading.value = false))
								},
							})
						} else {
							updateLedgerById(route.query.ledgerId as any, queryData.value)
								.then(() => {
									ElMessage.success('业务表更新成功')
									localStorage.removeItem('CreateLedger')
									localStorage.removeItem('datas')
									localStorage.removeItem('allfield')
									localStorage.removeItem('disPlayMobileList')
									saveLoading.value = false
									deleteLabel({path: localStorage.getItem('currentRoute')})
									router.push('/ledgerConfig')
								})
								.catch((err) => (saveLoading.value = false))
						}
					})
				} else {
					updateLedgerById(route.query.ledgerId as any, queryData.value)
						.then(() => {
							ElMessage.success('业务表更新成功')
							localStorage.removeItem('CreateLedger')
							localStorage.removeItem('datas')
							localStorage.removeItem('allfield')
							localStorage.removeItem('disPlayMobileList')
							saveLoading.value = false
							deleteLabel({path: localStorage.getItem('currentRoute')})
							router.push('/ledgerConfig')
						})
						.catch((err) => (saveLoading.value = false))
				}
			}

			console.log(oldreminderConfig)
			console.log(oldReminderConfig.value)
		}
	}
}
const sortTree = (tree: any) => {
	return tree
		.sort((a: any, b: any) => a.sort - b.sort)
		.filter((f: any) => f.isListField)
		.map((node: any) => ({...node, children: node.children ? sortTree(node.children) : []}))
}
// 拖动判断
const allowDrop = (draggingNode: any, dropNode: any, type: any) => {
	if (type == 'inner' && dropNode.data.displayName) {
		return false
	} else {
		return true
	}
}
const CreateLedger = ref<any>(null)
const oldReminderConfig = ref<any>()
onMounted(() => {
	CreateLedger.value = JSON.parse(localStorage.getItem('CreateLedger') as any)
	currentfield.value = JSON.parse(localStorage.getItem('CreateLedger') as any)
	console.log(CreateLedger.value)
	console.log(currentfield.value)

	if (route.query.type == 'add' && !JSON.parse(localStorage.getItem('datas') as any)) {
		if (CreateLedger.value.isSelectLedgerTemplate) {
			currentfield.value.tableFieldGroups = CreateLedger.value.tableFieldGroups
			CreateLedger.value.tableFields.forEach((item: any) => {
				if (!item.id) {
					item.id = util._guid()
				}
			})
			currentfield.value.tableFieldGroups = CreateLedger.value.tableFieldGroups

			allfield.value = currentfield.value.tableFields.sort(
				(a: any, b: any) => a.sort - b.sort
			)
			// localStorage.setItem("allfield",JSON.stringify(allfield.value))
			console.log(currentfield.value.tableFields)
			currentfield.value.tableFieldGroups =
				currentfield.value.tableFieldGroups &&
				currentfield.value.tableFieldGroups.sort((a: any, b: any) => a.sort - b.sort)
			currentfield.value.tableFieldGroups &&
				currentfield.value.tableFieldGroups.forEach((item: any) => {
					item.tableFields.sort((a: any, b: any) => a.sort - b.sort)
				})
			const groups: any = []
			currentfield.value.tableFieldGroups &&
				currentfield.value.tableFieldGroups.forEach((item: any, index: any) => {
					if (item.tableFields) {
						groups.push(
							Object.assign(item, {
								id: item.id,
								label: item.name,
								parentId: item.parentId,
								isDelete: true,
								ischange: true,
								changeType: false,
								isListField: true,
								tableFields: toRaw(item.tableFields),
							})
						)
						console.log(item.tableFields)

						item.tableFields.forEach((tf: any) => {
							console.log(tf.isListField)
							console.log(currentfield.value.tableFields)

							// 跟字段的isListField同步
							tf.isListField = currentfield.value.tableFields.filter(
								(f: any) => tf.id == f.id
							)[0]?.isListField
							// 不显示则不push进去
							if (tf.isListField) {
								groups.push({
									label: tf.displayName,
									field: tf.name,
									parentId: item.id,
									id: tf.id,
									...tf,
									isDelete: false,
									ischange: false,
									changeType: false,
								})
							}
						})
					} else {
						groups.push({
							label: item.name,
							id: item.id,
							parentId: item.parentId,
							isDelete: false,
							ischange: false,
							changeType: false,
						})
					}
				})

			const LedgerGroup = useArrayToTree(
				groups,
				'id',
				'parentId',
				'label',
				false,
				'children',
				true
			)

			// 大概是根据tableFieldGroupId 去push进去
			currentfield.value.tableFields.forEach((item: any, index: any) => {
				console.log(item, item.tableFieldGroupId, 'item.tableFieldGroupId')
				// 有没有父id 并且 需要显示
				if (item.tableFieldGroupId == null && item.isListField) {
					console.log(item.tableFieldGroupId)
					LedgerGroup.push({
						...item,
						label: item.displayName,
						field: item.name,
						isDelete: false,
						ischange: false,
						changeType: false,
					})
				} else if (item.tableFieldGroupId != null) {
					// 父id不为空  并且 需要显示
					if (!item.isListField) {
						LedgerGroup.push({
							...item,
							label: item.displayName,
							field: item.name,
							isDelete: false,
							ischange: false,
							changeType: false,
							sort: 999,
						})
					}
				}
			})
			// 获取datas.value数据
			console.log(LedgerGroup, 'onActivated获取生成好的datas.value')

			// datas.value = LedgerGroup
			datas.value = computed(() => {
				return sortTree([...LedgerGroup])
			})
			console.log(datas.value.value)
			console.log(currentfield.value)
			datas.value = datas.value.value
			// 去接group内容
			createTableHedaer()
		} else {
			datas.value = []
			treeData.value = []
			currentfield.value.tableFields
				.filter((f: any) => f.isListField)
				.forEach((item: any, index: any) => {
					item.id = util._guid()
					const field = {
						label: item.displayName,
						field: item.name,
						isDelete: false,
						ischange: false,
						changeType: false,
						children: [],
						...item,
					}
					datas.value.push(field)
				})
		}
		// localStorage.setItem("datas",JSON.stringify(datas.value))

		createTableHedaer()
	} else if (route.query.type == 'edit' && !JSON.parse(localStorage.getItem('datas') as any)) {
		datas.value = []
		treeData.value = []
		const ledgerId = route.query.ledgerId
		getDetailByLedgerId(ledgerId as any).then((res: any) => {
			console.log(res.data)
			const {data} = res
			// 接口
			currentfield.value = data
			oldReminderConfig.value = data?.reminderConfig

			CreateLedger.value.tableFields.forEach((item: any) => {
				if (!item.id) {
					item.id = util._guid()
				}
			})

			currentfield.value.tableInfo.fields = CreateLedger.value.tableFields
			// 匹配上一步更改的displayName
			currentfield.value.tableInfo.tableFieldGroups.forEach((item: any) => {
				if (item.tableFields) {
					item.tableFields.forEach((item2: any) => {
						item2.displayName = currentfield.value.tableInfo.fields.filter(
							(f: any) => f.id === item2.id
						)[0]?.displayName
					})
				}
			})
			allfield.value = currentfield.value.tableInfo.fields.sort(
				(a: any, b: any) => a.sort - b.sort
			)
			// localStorage.setItem("allfield",JSON.stringify(allfield.value))

			currentfield.value.tableInfo.tableFieldGroups =
				currentfield.value.tableInfo.tableFieldGroups.sort(
					(a: any, b: any) => a.sort - b.sort
				)
			currentfield.value.tableInfo.tableFieldGroups.forEach((item: any) => {
				item.tableFields.sort((a: any, b: any) => a.sort - b.sort)
			})
			const groups: any = []
			console.log(
				currentfield.value.tableInfo.fields,
				currentfield.value.tableInfo.tableFieldGroups,
				'最初数据'
			)
			currentfield.value.tableInfo.tableFieldGroups.forEach((item: any, index: any) => {
				if (item.tableFields) {
					groups.push(
						Object.assign(item, {
							id: item.id,
							label: item.name,
							parentId: item.parentId,
							isDelete: true,
							ischange: true,
							changeType: false,
							isListField: true,
							tableFields: toRaw(item.tableFields),
						})
					)
					item.tableFields.forEach((tf: any) => {
						// 跟字段的isListField同步
						tf.isListField = currentfield.value.tableInfo.fields.filter(
							(f: any) => tf.id == f.id
						)[0]?.isListField
						// 不显示则不push进去
						if (tf.isListField) {
							groups.push({
								label: tf.displayName,
								field: tf.name,
								parentId: item.id,
								id: tf.id,
								...tf,
								isDelete: false,
								ischange: false,
								changeType: false,
							})
						}
					})
				} else {
					groups.push({
						label: item.name,
						id: item.id,
						parentId: item.parentId,
						isDelete: false,
						ischange: false,
						changeType: false,
					})
				}
			})

			const LedgerGroup = useArrayToTree(
				groups,
				'id',
				'parentId',
				'label',
				false,
				'children',
				true
			)

			// 大概是根据tableFieldGroupId 去push进去
			currentfield.value.tableInfo.fields.forEach((item: any, index: any) => {
				console.log(item, item.tableFieldGroupId, 'item.tableFieldGroupId')
				// 有没有父id 并且 需要显示
				if (item.tableFieldGroupId == null && item.isListField) {
					console.log(item.tableFieldGroupId)
					LedgerGroup.push({
						...item,
						label: item.displayName,
						field: item.name,
						isDelete: false,
						ischange: false,
						changeType: false,
					})
				} else if (item.tableFieldGroupId != null) {
					// 父id不为空  并且 需要显示
					if (!item.isListField) {
						LedgerGroup.push({
							...item,
							label: item.displayName,
							field: item.name,
							isDelete: false,
							ischange: false,
							changeType: false,
							sort: 999,
						})
					}
				}
			})
			// 获取datas.value数据
			console.log(LedgerGroup, 'onActivated获取生成好的datas.value')
			// datas.value = LedgerGroup
			datas.value = computed(() => {
				return sortTree([...LedgerGroup])
			})
			console.log(datas.value.value)
			datas.value = datas.value.value
			// 去接group内容
			// localStorage.setItem("datas",JSON.stringify(datas.value))

			createTableHedaer()
		})
	} else {
		datas.value = JSON.parse(localStorage.getItem('datas') as any)
		allfield.value = JSON.parse(localStorage.getItem('allfield') as any)
	}
})

const getBack = () => {
	// localStorage.setItem("datas",JSON.stringify(datas.value))
	// localStorage.setItem("allfield",JSON.stringify(allfield.value))

	router.go(-1)
}

onUnmounted(() => {
	localStorage.removeItem('datas')
	window.removeEventListener('resize', handleResize)

	localStorage.removeItem('allfield')
})
const maxWidth = ref(window.innerWidth * 0.8) // 初始宽度为屏幕宽度的80%

const handleClose = () => {
	isOpenSub.value = false
}
const handleInopenModel = () => {
	isOpenSub.value = false
}
const handleInopenModel2 = () => {
	isOpenSub.value = false
}
</script>
<template>
	<div class="layout">
		<div class="left" ref="left" style="overflow-y: hidden">
			<div class="add">
				<div>业务表显示</div>
				<el-button type="primary" size="small" class="addbtns" @click="addTreeGroup">
					<!-- <i class="icon i-ic-baseline-search"></i> -->
					添加分组
				</el-button>
			</div>
			<el-scrollbar height="calc(100% - 50px)" max-height="80vh">
				<el-tree
					ref="tree"
					:data="datas"
					hlight-current="true"
					vh
					draggable
					default-expand-all
					node-key="id"
					@node-click="treeNodeClick"
					@node-drop="onNodeDrop"
					:allow-drop="allowDrop"
				>
					<template #default="{node, data}">
						<!-- 编辑状态 -->
						<div v-if="data.changeType && data.ischange">
							<el-input
								v-model="data.label"
								size="small"
								@blur="onNodeBlur(node, data)"
							></el-input>
						</div>
						<!--  ischange 是否可以改变 changetype 是否可以编辑 -->
						<div
							class="custom-tree-node"
							v-else-if="!data.changetype"
							style="width: 100%"
						>
							<div @dblclick="data.changeType = true" class="txt">
								{{ data.sort ? data.sort + ':  ' + data.label : data.label }}
							</div>
							<div class="pd-right-10">
								<Icons
									v-if="data.isDelete"
									name="Clear2"
									@click.stop="deleteTreeNode(node, data)"
								></Icons>
							</div>
						</div>
					</template>
				</el-tree>
			</el-scrollbar>
		</div>
		<div class="right">
			<BaseTableComp
				ref="tableRef"
				:offsetHeight="-55"
				:checkbox="false"
				:data="[]"
				:colData="tableHeader"
				:buttons="[]"
				:visible-search="false"
				:visible-export="false"
				:visible-setting="false"
				:visibleIndex="false"
			>
				<template #header>
					<div style="display: flex">
						<el-button style="height: 24px" type="primary" @click="createTableHedaer"
							>预览</el-button
						>
						<el-button style="height: 24px" type="primary" @click="createPhoneViews"
							>移动端预览</el-button
						>
						<div class="header-layout">
							<el-button style="height: 24px" type="primary" @click="getBack()"
								>返回上一步</el-button
							>
							<el-button
								style="height: 24px"
								type="primary"
								@click="submitComfirm"
								:loading="saveLoading"
								>保存</el-button
							>
						</div>
					</div>
				</template>
			</BaseTableComp>
		</div>

		<div v-if="isOpenView" class="phone_modal">
			<div class="content">
				<el-icon @click="ClosePhoneModal" class="icons"><CircleClose /></el-icon>
				<div class="main">
					<div class="phoneView">
						<img class="imgs" :src="phoneImg" :style="{maxWidth: maxWidth + 'px'}" />
						<div class="tree_content" :style="{maxWidth: maxWidth + 'px'}">
							<el-scrollbar height="calc(100% - 50px)">
								<el-tree
									ref="treePhone"
									:data="phoneData"
									hlight-current="true"
									default-expand-all
									node-key="id"
								>
									<template #default="{node, data}">
										<div style="width: 100%" class="item_s">
											<div class="custom-tree-node">
												<div class="txt">{{ data.names }}</div>
											</div>
										</div>
									</template>
								</el-tree>
							</el-scrollbar>
						</div>
					</div>
				</div>
			</div>
		</div>

		<Dialog
			v-model="isOpenSub"
			title="周期修改提示"
			width="400"
			@close="isOpenSub = false"
			@click-close="isOpenSub = false"
		>
			<div style="line-height: 20px">
				你已更改业务表的填报周期，将从确认更改后的第一个符合要求的“填报开始时间”当天0点生成新的填报周期。
			</div>
			<template #footer>
				<el-button mr-5px mt="-5px" type="smiall" @click="handleClose"
					><i class="icon i-ic-outline-cancel"></i>关闭</el-button
				>
				<el-button
					mr-5px
					mt="-5px"
					type="primary"
					@click="handleInopenModel"
					style="color: #fff !important"
					><i class="icon i-ic-round-task-alt"></i>确定修改并调整已有周期</el-button
				>
				<el-button
					mr-5px
					mt="-5px"
					type="primary"
					@click="handleInopenModel2"
					style="color: #fff !important"
					><i class="icon i-ic-round-task-alt"></i>确定修改</el-button
				>
			</template>
		</Dialog>
	</div>
</template>
<route>
	{
		meta: {
			ignoreLabel:true
		}
	}
</route>
<style scoped lang="scss">
.layout {
	display: flex;
	height: 100%;
	overflow: hidden;

	.left {
		background-color: #fff;
		border: 1px solid var(--z-line);
		border-radius: 5px;
		// margin: 10px 0;
		width: 250px;

		.add {
			align-items: center;
			border-bottom: 1px solid var(--z-line);
			display: flex;
			height: 34px;
			padding-left: 10px;
			margin-bottom: 10px;
			justify-content: space-between;
		}
		.addbtns {
			margin-right: 10px;
		}
		.addbtn {
			position: absolute;
			top: 2px;
			right: 10px;
		}
		.items {
			padding: 0 10px;
			line-height: 32px;
		}
		.items-background {
			color: #fff;
			background-color: var(--z-main);
		}
	}

	.right {
		margin-left: 10px;
		width: calc(100% - 260px);
		:deep(.base-table-comp) {
			.header > div {
				margin-top: 0 !important;
			}
		}
	}

	.phoneView {
	}
}
.phone_modal {
	position: absolute;
	background: rgba(0, 0, 0, 0.2);
	width: 100%;
	height: 100%;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	z-index: 99;
	.content {
		position: absolute;
		top: 5%;
		left: 40%;
		transform: translate(-50%, -50%);
	}
	.icons {
		cursor: pointer;
		position: absolute;
		right: -287px;
		top: -16px;
		z-index: 1000;
		font-size: 26px;
	}
	img {
		width: 260px;
		position: absolute;
		// background: #f0f0f0;
		// border-radius: 9.7%;
	}
	.tree_content {
		top: 35px;
		left: 11px;
		position: absolute;
		width: 238px;
		height: 478px;
		background: #f0f0f0;
	}
	:deep(.el-tree-node__expand-icon) {
		display: none;
	}
	:deep(.el-tree) {
		background: #fff;
	}
	:deep(.custom-tree-node) {
		padding: 0 5px;
	}

	:deep(.el-tree-node__content) {
		height: 40px;
		border-bottom: 1px solid #eee;
	}
}

.header-layout {
	flex: 1;
	display: flex;
	justify-content: flex-end;

	:deep(.el-input) {
		margin-right: 10px;
		width: 250px;
	}
}
// .custom-tree-node {
//   flex: 1;
//   display: flex;
//   align-items: center;
//   justify-content: space-between;
//   font-size: 14px;
//   padding-right: 8px;
// }
.custom-tree-node {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
	.txt {
		max-width: 150px;
		white-space: nowrap; /* 禁止换行 */
		overflow: hidden; /* 隐藏溢出的部分 */
		text-overflow: ellipsis;
	}
}
</style>
