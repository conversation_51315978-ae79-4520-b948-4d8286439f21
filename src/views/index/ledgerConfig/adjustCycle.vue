<script setup lang="ts" name="adjustCycle">
import {onActivated, ref, onMounted} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import {adjustCycleList, terminateCycle, adjustCycleItem} from '@/api/LedgerApi'
import {ElMessage, ElMessageBox} from 'element-plus'

const loading = ref(false)
const route = useRoute()
const router = useRouter()
const STATUSENM = [
	{label: '已终止', value: -1, type: 'danger'},
	{label: '未开始', value: 0, type: 'primary'},
	{label: '进行中', value: 1, type: 'warning'},
]
// 表格中的操作列
const buttons: any = [
	// {code: 'view', label: '查看', icon: 'i-majesticons-eye-line', verify: 'true'},
	{
		code: 'edit',
		label: '修改时间',
		type: 'primary',
		icon: 'i-majesticons-pencil-alt-line',
		verify: 'row.deadline !== null',
		disabled: 'row.deadline === null',
	},
	// {
	// 	code: 'download',
	// 	label: '下载',
	// 	type: 'info',
	// 	icon: 'i-ic-baseline-download',
	// 	verify: 'true',
	// },
	{
		code: 'delete',
		label: '终止',
		type: 'danger',
		icon: 'i-ic-round-dangerous',
		verify: 'row.deadline !== null',
		disabled: 'row.deadline === null',
	},
]

const isOpen = ref(false)
const rowData = ref()
// 表格中的操作列
const onTableClickButton = ({btn, row, index}: any) => {
	if (btn.code === 'edit') {
		rowData.value = row
		ruleFormLedgerTemplate.value.startTime = row.startTime
		ruleFormLedgerTemplate.value.deadline = row.deadline
		isOpen.value = true
	}

	if (btn.code === 'delete') {
		let data: any = row.id
		ElMessageBox.confirm(
			'你正在终止正在执行的周期，终止后填报用户建无法选择该周期提交数据，请确认操作。',
			`终止周期提示`,
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}
		)
			.then(() => {
				terminateCycle(data).then(() => {
					ElMessage.success('已终止该周期')
					getList()
				})
			})
			.catch(() => {})
	}
}
// 查询条件
const runwayForm: any = ref({
	UserName: null,
	isActive: '',
	departmentName: '',
	roleName: '区县台账运维员',
})
const cascade = ref()
// 设置表格高度计算
const tableHeight = ref(0)
const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}
// 多选数据
const statisticsList: any = ref([])
const selectedCount = ref(0)
const selectionChange = (selection: []) => {
	selectedCount.value = selection.length
	statisticsList.value = selection
	console.log(statisticsList.value)
}

onMounted(() => {})

// 表中的内容
const tableData = ref([])
// 表头
const colData: any = ref([
	{
		prop: 'name',
		label: '更新周期',
		tooltip: true,
		align: 'center',
	},
	{
		prop: 'startTime',
		label: '开始时间',
		align: 'center',
	},
	{
		prop: 'deadline',
		label: '截止时间',
		align: 'center',
	},
	{
		prop: 'status',
		label: '状态',
		align: 'center',
	},
])
//表格与分页关联
const reqParams = ref({
	skipCount: 0,
	maxResultCount: 10,
})
// 分页相关参数
const pagination = ref({
	total: 0,
	page: 1,
	size: 10,
})
// 查询
const getList = () => {
	console.log(cascade.value?.ad)
	loading.value = true

	adjustCycleList({
		BindObjectId: route.query.id as string,
		skipCount: (pagination.value.page - 1) * pagination.value.size,
		MaxResultCount: pagination.value.size,
	}).then((res: any) => {
		loading.value = false
		tableData.value = res.data.items
		pagination.value.total = res.data.totalCount
	})
}

// 高级查询
const seniorList = () => {
	getList()
}
// 清空
const empty = () => {
	runwayForm.value.UserName = ''
	runwayForm.value.departmentName = ''
	runwayForm.value.RegionId = ''

	cascade.value = {}
	getList()
}
// 分页事件
const onPaginationChange = (val: any, type: any) => {
	if (type === 'page') {
		pagination.value.page = val
		reqParams.value.skipCount = (val - 1) * pagination.value.size
	} else {
		pagination.value.page = 1
		pagination.value.size = val
		reqParams.value.maxResultCount = pagination.value.size
	}
	getList()
}
const getStatus = (status: number) => {
	return STATUSENM.find((item: any) => item.value === status)
}

onActivated(() => {})
onMounted(() => {
	getList()
})
const handleClose = () => {
	rulesLedgerTemplate.value.deadline1 = ''
	isOpen.value = false
}
const handleInopenModel = async () => {
	const res = await adjustCycleItem(rowData.value.id, {
		deadline: ruleFormLedgerTemplate.value.deadline1,
	})
	if (res) {
		ElMessage.success('修改成功！')
		getList()
		handleClose()
	}
}
const rulesLedgerTemplate = ref<any>({
	// name: [{required: true, message: '请输入业务表模板名称', trigger: 'blur'}],
})
const ruleFormLedgerTemplate = ref<any>({
	startTime: '',
	deadline: '',
	startTime1: '',
	deadline1: '',
}) //表单

// 禁用早于原开始时间的日期
const disabledDate = (time: Date) => {
	if (!ruleFormLedgerTemplate.value.startTime) {
		return false
	}
	return time.getTime() < new Date(ruleFormLedgerTemplate.value.startTime).getTime()
}
</script>
<template>
	<div class="dataAnalysis">
		<Block
			:enable-fixed-height="true"
			:enable-expand-content="false"
			:enableBackButton="false"
			:title="'调整业务表周期'"
			@heightChanged="onBlockHeightChanged"
		>
			<!-- <template #topRight>
				<el-dropdown @command="addAnalysis">
					<el-button type="primary" size="small">
						新建分析<el-icon class="el-icon--right"><arrow-down /></el-icon>
					</el-button>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item command="a">空白新建</el-dropdown-item>
							<el-dropdown-item command="b">从模版新建</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
				<el-button
					type="danger"
					size="small"
					v-if="selectedCount > 0"
					@click="ledgerArrDelete"
					style="margin-left: 10px"
				>
					批量删除
				</el-button>
			</template> -->

			<!-- <template #expand>
				<div class="search-box" v-action:enter="seniorList">
					<el-input
						placeholder="请输入用户名称"
						v-model="runwayForm.UserName"
						style="width: 250px; margin-right: 10px"
					></el-input>
					<el-input
						placeholder="请输入所属部门"
						v-model="runwayForm.departmentName"
						style="width: 250px; margin-right: 10px"
					></el-input>



					<el-button size="default" type="default" @click="empty">
						<i class="i-ic-baseline-cleaning-services" mr-3px></i>
						清空
					</el-button>
					<el-button size="default" type="primary" @click="seniorList">
						<i class="i-ic-baseline-send" mr-3px></i>
						查询</el-button
					>
				</div>
			</template> -->
			<TableV2
				ref="tableRef"
				:height="tableHeight"
				:columns="colData"
				:defaultTableData="tableData"
				:headers="{Urlkey: 'ledger'}"
				:enableToolbar="false"
				:enable-create="false"
				:enable-edit="false"
				:enable-delete="false"
				:enableSelection="false"
				:enableIndex="false"
				:req-params="reqParams"
				:buttons="buttons"
				@clickButton="onTableClickButton"
				@selection-change="selectionChange"
			>
				<!-- @before-complete="onTableBeforeComplete" -->

				<template #status="scope">
					<!-- getStatus -->
					<el-tag :type="getStatus(scope.row.status)?.type">{{
						getStatus(scope.row.status)?.label
					}}</el-tag>
				</template>
				<template #__department="scope">
					<p>
						{{
							`${scope.row?.department?.parent?.name || ''}${
								scope.row?.department?.parent?.name ? '-' : ''
							}${scope.row?.department?.name}`
						}}
					</p>
				</template>
			</TableV2>
			<Pagination
				:total="pagination.total"
				:page-size="pagination.size"
				:current-page="pagination.page"
				@current-change="onPaginationChange($event, 'page')"
				@size-change="onPaginationChange($event, 'size')"
			>
			</Pagination>
		</Block>

		<Dialog
			v-model="isOpen"
			title="修改时间"
			width="800"
			@close="isOpen = false"
			@click-close="isOpen = false"
		>
			<div>
				<el-form
					label-width="120px"
					class="demo-ruleForm"
					ref="ruleFormRef"
					:model="ruleFormLedgerTemplate"
					:rules="rulesLedgerTemplate"
					status-icon
				>
					<!-- <div> -->
					<el-form-item label="原开始时间" prop="startTime">
						<el-date-picker
							v-model="ruleFormLedgerTemplate.startTime"
							type="date"
							placeholder="请选择日期"
							format="YYYY-MM-DD HH:mm:ss"
							value-format="YYYY-MM-DD"
							size="small"
							disabled
						/>
					</el-form-item>
					<!-- <el-form-item label="调整后开始时间" prop="startTime1">
							<el-date-picker
								v-model="ruleFormLedgerTemplate.startTime1"
								type="date"
								placeholder="请选择日期"
								format="YYYY-MM-DD HH:mm:ss"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</el-form-item> -->
					<!-- </div>
					<div> -->
					<el-form-item label="原截止时间" prop="deadline">
						<el-date-picker
							v-model="ruleFormLedgerTemplate.deadline"
							type="date"
							placeholder="请选择日期"
							format="YYYY-MM-DD HH:mm:ss"
							value-format="YYYY-MM-DD"
							size="small"
							disabled
						/>
					</el-form-item>
					<el-form-item label="调整后截止时间" prop="deadline1">
						<el-date-picker
							v-model="ruleFormLedgerTemplate.deadline1"
							type="date"
							placeholder="请选择日期"
							format="YYYY-MM-DD HH:mm:ss"
							value-format="YYYY-MM-DD HH:mm:ss"
							size="small"
							:disabled-date="disabledDate"
						/>
					</el-form-item>
					<!-- </div> -->
				</el-form>
			</div>
			<template #footer>
				<el-button mr-5px mt="-5px" type="smiall" @click="handleClose"
					><i class="icon i-ic-outline-cancel"></i>取消</el-button
				>
				<el-button mr-5px mt="-5px" type="primary" @click="handleInopenModel"
					><i class="icon i-ic-round-task-alt"></i>确定</el-button
				>
			</template>
		</Dialog>
	</div>
</template>
<route>
	{
		meta: {
			title: '调整业务表周期',
		},
	}
</route>
<style scoped lang="scss">
.search-box {
	align-items: start;
	display: flex;
	padding: 10px 15px;
	::v-deep(.el-form-item) {
		margin: 0;
	}
}
.demo-ruleForm {
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
}
</style>
