<script setup lang="ts" name="ledgerconfig">
import {computed, onActivated, onMounted, reactive, ref, toRaw} from 'vue'
import {router} from '@/router'
import {
	getLedgerList,
	updateLedgerOnline,
	updateLedgerName,
	updateLedgerDataConfig,
	updateLedgerDataType,
	addLedgerTemplate,
	createLedgerAuxiliaryFilling,
	getOriginLedgerList,
	getLedgerAuxiliaryFilling,
	updateLedgerAuxiliaryFilling,
	removeLedgerAuxiliaryFilling,
	getSingleLedgerData,
	getLedgerListsP,
	deleteOfflineLedger,
	getRunwayLedgerType,
	ledgerManagerrunwayCount,
	getMyManagementType,
	getApiAuxiliaryFillingData,
	CreateApiAuxiliaryFillingData,
	getApiAuxiliaryFillingList,
	removeApiAuxiliaryFillingData,
	getApiList,
	deleteRelations,
	getrunwayledgerList,
	getDataSetList,
	getDataSetInfoById,
	CreateDataSetAuxiliaryFillingData,
	getDataSetAuxiliaryFilling,
	updateDataSetAuxiliaryFillingData,
	removeDataSetAuxiliaryFillingData,
	getDetailByLedgerId,
	associatedSyn,
	getAssociatedSyn,
	putAssociatedSyn,
} from '@/api/LedgerApi'
import {ElMessage, UploadFile, UploadFiles, FormRules, ElNotification} from 'element-plus'
import {RunwayList} from '@/define/ledger.define'
import {useSource} from '@/stores/useSource'
import {APIConfig} from '@/api/config'
import {CirclePlus, View, Delete} from '@element-plus/icons-vue'
import {useArrayToTree} from '@/hooks/useConvertHook'
// import collapseForm from '../ledger/components/collapseForm.vue'
import {useUserStore} from '@/stores/useUserStore'
import {displayName} from '@/define/feedback.define'
import {USER_BASE_ROLES_ENUM} from '@/define/organization.define'
import {IssueDataLeader} from '@/api/ReportApi'
import {useBatchData} from './hook/useBatchData'

const loading = ref(false)
const UserStore = useUserStore()
const searchForm: any = reactive({
	value: '',
	LedgerTypeId: null,
	IsOnline: undefined,
	ReminderInterval: null,
})

const currentLedgerType = ref(-1)
const tableRef = ref()

const colData = [
	{title: '业务表名称', field: 'name'},
	{title: '发布部门', field: 'ledgerType'},
	{title: '所属板块', field: 'runway'},
	{title: '创建时间', field: 'creationTime', sortable: true},
	{title: '创建部门', field: 'createDepartment'},
	{title: '创建人', field: 'creatorName'},
	{title: '数据更新周期', field: 'reminderConfig', width: 120},
	{title: '状态', field: 'isOnline', fixed: 'right', width: 120},
]
const colDataTable = ref(colData)
const currentUser = computed(() => JSON.parse(localStorage.getItem('currentUserInfo') as any))
const currentDepartmentInfo: any = ref(
	computed(() => JSON.parse(localStorage.getItem('currentDepartmentInfo') as any))
)
const tableData = ref([])
const buttons = [
	{
		code: 'view',
		title: '查看',
		icon: '<i i-ic-round-preview></i>',
		verify: 'true',
	},
	// {
	// 	type: 'primary',
	// 	code: 'online',
	// 	title: '上线',
	// 	icon: '<i i-ic-baseline-online-prediction></i>',
	// 	verify: '!row.isOnline',
	// },
	// {
	// 	type: 'danger',
	// 	code: 'offline',
	// 	title: '下线',
	// 	icon: '<i i-ic-outline-offline-bolt></i>',
	// 	verify: 'row.isOnline',
	// },
	{
		code: 'changeName',
		title: '更改名称',
		icon: '',
		verify: 'true',
		more: true,
	},
	{
		code: 'changeType',
		title: '更改类型',
		icon: '',
		verify: 'true',
		more: true,
	},
	{
		code: 'changeConfig',
		title: '更改配置',
		icon: '',
		verify: 'true',
		more: true,
	},
	{
		code: 'changeTime',
		title: '设置提醒',
		icon: '<i i-ic-round-access-alarm></i>',
		verify: 'true',
		more: true,
	},
	{
		code: 'auxiliFilling',
		title: '辅助填报',
		icon: '<i i-ic-round-access-alarm></i>',
		verify: 'true',
		more: true,
	},
	{
		code: 'changeCycle',
		title: '调整周期',
		icon: '<i i-ic-round-access-alarm></i>',
		verify: 'true',
		more: true,
	},
	{
		code: 'synchronous',
		title: '关联同步',
		icon: '<i i-ic-round-access-alarm></i>',
		verify: 'true',
		// verify: `${currentDepartmentInfo.value.region.grade == 4}`,
		more: true,
	},
	{
		code: 'createLedgerTemplate',
		title: '创建模板',
		icon: '<i i-ic-round-access-alarm></i>',
		verify: 'true',
		more: true,
	},
	{
		type: 'danger',
		code: 'deleteLedger',
		title: '删除业务表',
		icon: '<i i-ic-outline-offline-bolt></i>',
		verify: `!row.isOnline `,
	},
	// {
	// 	code: 'uplaodTemplate',
	// 	title: '上传模版',
	// 	verify: 'true',
	// 	more: true,
	// },
]

const options = [
	{value: 1, label: '周一'},
	{value: 2, label: '周二'},
	{value: 3, label: '周三'},
	{value: 4, label: '周四'},
	{value: 5, label: '周五'},
	{value: 6, label: '周六'},
	{value: 7, label: '周日'},
]
const pageation = reactive({
	currentPage: 1,
	pageSize: 10,
	total: 100,
	pageSizeArray: [10, 30, 50, 100, 300, 500],
})

const currentDialogRow = ref<any>({})
const newLedgerName = ref('')

const openChangeLedgerName = ref(false)
const openChangeLedgerType = ref(false)
const openChangeLedgerTime = ref(false)

const source = useSource()
const ledgerType: any = computed(() => source.getLedgerType)
const publishLedgerDepartment = ref<any[]>([])
const newLedgerType = ref('')
const runway = ref()
const openUploadDialog = ref(false)
const uploadRef = ref()
const uploadUrl = ref('')
const ledgerTemplate = ref<any>(null)

const onUploadTemplate = (uploadFile: UploadFile, uploadFiles: UploadFiles) => {
	ledgerTemplate.value = uploadFile
}

const onPushUpload = () => {
	if (ledgerTemplate.value) {
		uploadRef.value.submit()
		openUploadDialog.value = false
	} else {
		ElMessage.warning('请先选择模版文件')
	}
}

const onUploadSuccess = () => {
	ElMessage.success('模版上传成功')
}

const dropdwonTreeRunway = computed(() => {
	const newRunway = JSON.parse(JSON.stringify(RunwayList))
	const items = toRaw(ledgerType.value).items
	newRunway.forEach((f: any, index: number) => {
		f.child = items.filter((ff: any) => ff.runway === f.label)
		f.child.forEach((fc: any) => {
			fc.parentId = f.id
		})
	})
	return newRunway
})

const runwayCountList: any = ref([])

const onDropdownTreeSelected = (checked: any) => {
	// checked.id
	// TODO: 更新业务表类型
	newLedgerType.value = checked.id
}

const onClickLedger = (str: string, id?: string, row?: any) => {
	const query: any = {
		type: str,
	}

	if (id) {
		query.id = id
		query.lastOnlineTime = row?.lastOnlineTime?.split(' ')[0]
	}
	localStorage.removeItem('disPlayMobileList')
	router.push({
		path: '/ledgerConfig/info',
		query,
	})
}
const createLedgerTemplateId = ref<any>('')
const auxiliFillingId = ref<any>('')
const auxiliaryFillingType = ref(3)
const openAuxilifillingDialog = ref(false)
const apilist = ref<any[]>([])
const isSynchronous = ref(false)
const sourceLedgerId = ref(null)
const radio2 = ref('业务表数据')
const apiChange = async (val: string) => {
	// 接口请求封装回显
	const res = await getApiAuxiliaryFillingList(auxiliFillingId.value)

	const sourceNameList = await getApiAuxiliaryFillingData(val)

	auxiliFillingData.value = [
		{
			id: null,
			name: null,
			sourceLedgerId: null,
			fieldId: null,
			apiId: val,
			sourceLedgerFieldList: sourceNameList.data.map((x: any) => ({
				isUnique: false,
				displayName: x.fieldDisplayName,
				id: x.fieldName,
				key: x.id,
			})),
			// .concat({
			// 	isUnique: true,
			// 	displayName: sourceNameList.data[0].keyFieldDisplayName,
			// 	id: sourceNameList.data[0].keyFieldName,
			// }),
			type: 2,
			configs: [
				{
					fieldId: currentLedgerFieldList.value.filter((v) => v.isUnique)[0]?.name,
					sourceFiledId: sourceNameList.data[0].keyFieldName,
					targetLedgerFieldList: currentLedgerFieldList.value,
				},
			],
		},
	]
}
const dataSetChange = async (id: string) => {
	const sourceList = await getDataSetInfoById(id)
	auxiliFillingData.value = [
		{
			id: null,
			name: null,
			sourceLedgerId: null,
			fieldId: null,
			apiId: id,
			sourceLedgerFieldList: sourceList.data.tableDataSetMappingFields.map((x: any) => ({
				isUnique: false,
				displayName: x.displayName,
				id: x.dbTableFieldId,
				key: x.dbTableFieldId,
			})),
			// .concat({
			// 	isUnique: true,
			// 	displayName: sourceNameList.data[0].keyFieldDisplayName,
			// 	id: sourceNameList.data[0].keyFieldName,
			// }),
			type: 3,
			configs: [
				{
					fieldId: null,
					sourceFiledId: null,
					targetLedgerFieldList: currentLedgerFieldList.value,
				},
			],
		},
	]
}
const changeField = async (item) => {
	auxiliFillingData.value[0].configs[0].sourceFiledId = item.fieldId
}
const onTableClickButton = async ({btn, scope}: any) => {
	console.log(btn, scope)
	currentDialogRow.value = scope
	if (btn.code === 'changeConfig') {
		localStorage.removeItem('disPlayMobileList')
		onClickLedger('edit', scope?.id ?? 123, scope)
	}

	if (btn.code === 'view') {
		onClickLedger('view', scope?.id ?? 123, scope)
	}

	if (btn.code === 'changeName') {
		openChangeLedgerName.value = true
	}

	if (btn.code === 'changeType') {
		openChangeLedgerType.value = true
	}

	if (
		btn.code === 'changeTime'

		// || currentDialogRow.value.reminderConfig.interval === 1
	) {
		if (currentDialogRow.value.reminderConfig.interval != 0) {
			console.log(remindConfig.value, 'cesss')
			remindConfig.value = currentDialogRow.value.reminderConfig
			openChangeLedgerTime.value = true
		} else {
			ElMessage.warning('请先设置业务表数据更新周期，再设置提醒。')
		}

		// router.push({
		//   path: "/ledgerConfig/adjustCycle",
		//   query: {
		//     id: scope.id,
		//   },
		// });
		//
	}
	//  else if (btn.code === 'changeTime' && currentDialogRow.value.reminderConfig.interval != 0) {

	// }
	if (btn.code === 'online' || btn.code === 'offline') {
		updateLedgerOnline(scope.id, btn.code === 'online').then((res: any) => {
			ElMessage.success(
				btn.code === 'online' ? `${scope?.name}上线成功` : `${scope?.name}下线成功`
			)
			getLedgerData()
		})
	}
	if (btn.code === 'changeCycle') {
		router.push({
			path: '/ledgerConfig/adjustCycle',
			query: {
				id: scope.id,
			},
		})
	}

	if (btn.code === 'uplaodTemplate') {
		uploadUrl.value = `${APIConfig('ledger')}/api/ledger-service/ledger/${
			scope.id
		}/import-excel-template-file`
		openUploadDialog.value = true
	}
	if (btn.code === 'createLedgerTemplate') {
		createLedgerTemplateId.value = scope.id
		isCreateLedgerTemplate.value = true
	}
	// 关联同步
	console.log('关联同步', btn.code)
	if (btn.code === 'synchronous') {
		console.log(scope)
		const data = await detailByLedgerId(scope.id)
		getAssociatedSyn(scope.id).then((res) => {
			sourceLedgerId.value = null
			if (res.status == 200) {
				getLedgerList('', 0, 1, res.data.ledgerId).then((ress) => {
					isshowData.value = true
					const {data} = ress
					fillTable.value = data
					fillList.value = data.tableInfo.fields
					sourceLedgerId.value = res.data.ledgerId
					editFill.value = res.data.ledgerId
					isSynchronous.value = true
					configs.value = []
					res.data.configs.forEach((item, index) => {
						configs.value.push({
							sourceFiledId: item.sourceFiledId,
							fieldId: item.fieldId,
						})
					})
				})
			} else {
				isshowData.value = false
				configs.value = [
					{
						fieldId: '',
						sourceFiledId: '',
					},
				]
				isSynchronous.value = true
			}
		})
		currentTable.value = data
		isSynchronous.value = true
		currentList.value = data.tableInfo.fields
		sourceLedgerData.value.forEach((item, index) => {
			item.children.forEach((ite) => {
				if (ite.value == scope.id) {
					ite.disabled = true
				}
			})
		})
	}
	// 辅助填报
	if (btn.code === 'auxiliFilling') {
		auxiliFillingId.value = scope.id
		auxiliaryFillingType.value = scope.auxiliaryFillingType
		currentLedgerFieldList.value = await getLedgerInfo(scope.id)
		const app = await getLedgerDetail(scope.id)
		handleData.value = []
		tableFiled.value = []
		formArray.value = []
		getPreviewData(app)
		// if (currentLedgerFieldList.value.every((v) => !v.isUnique)) {
		// 	ElMessage.warning('该业务表暂未设置唯一键，无法设置辅助填报')
		// } else {
		// 业务表辅助填报
		if (app.auxiliaryFillingType === 0) {
			const res = await getLedgerAuxiliaryFilling({
				LedgerId: auxiliFillingId.value,
				IsPaged: false,
			})
			if (res) {
				ledgerAuxiliaryFillingList.value = res.data.items
				if (res.status === 200 && res.data.items.length !== 0) {
					// 当有辅助填报数据时，先清空再填充
					auxiliFillingData.value = []
					disableExchangeFillingType.value = true
					res.data.items.forEach(async (v: any) => {
						sourceLedgerFieldList.value = await getLedgerInfo(v.sourceLedgerId)
						auxiliFillingData.value.push({
							name: v.name,
							id: v.creatorId,
							type: 1,
							sourceLedgerId: [
								RunwayList.filter((x) => x.name === v.sourceLedger.runway)[0].id,
								v.sourceLedgerId,
							],
							fieldId: v.fieldId,
							sourceLedgerFieldList: sourceLedgerFieldList.value,
							configs: [
								...v.configs
									.filter((x) => x.field.isUnique)
									.map((x) => ({
										fieldId: x.fieldId,
										isUnique: x.field.isUnique,
										sourceFiledId: x.sourceFiledId,
										targetLedgerFieldList: currentLedgerFieldList.value,
									})),
								...v.configs
									.filter((x) => !x.field.isUnique)
									.map((x) => ({
										fieldId: x.fieldId,
										isUnique: x.field.isUnique,
										sourceFiledId: x.sourceFiledId,
										targetLedgerFieldList: currentLedgerFieldList.value,
									})),
							],
							previewFields: v.previewFields.map((x) => x.tableFieldId),
						})
					})

					console.log(auxiliFillingData.value)
				} else {
					disableExchangeFillingType.value = false
					auxiliFillingData.value = [
						{
							name: null,
							sourceLedgerId: null,
							fieldId: null,
							sourceLedgerFieldList: sourceLedgerFieldList.value,
							type: 1,
							configs: [
								{
									fieldId: null,
									sourceFiledId: null,
									targetLedgerFieldList: currentLedgerFieldList.value,
								},
							],
						},
					]
					// 当只有1个唯一键时，自动填充查询字段以及数据绑定唯一键绑定值
					if (auxiliFillingData.value[0].configs[0].targetLedgerFieldList.length === 1) {
						auxiliFillingData.value[0].fieldId =
							auxiliFillingData.value[0].configs[0].fieldId =
								auxiliFillingData.value[0].configs[0].targetLedgerFieldList[0].id
					}
				}
			}
		} else if (app.auxiliaryFillingType === 2) {
			// 数据源辅助填报
			const apiScource = await getApiList({})
			if (apiScource.data) {
				apilist.value = apiScource.data.map((v: any) => ({
					id: v.id,
					name: v.apiName,
				}))

				// 接口请求封装回显
				const res = await getApiAuxiliaryFillingList(auxiliFillingId.value)
				ledgerAuxiliaryFillingList.value = [
					{
						...res.data,
						field: {name: res.data.ledgerKey},
						configs: res.data.rules?.map((v: any) => ({
							field: {name: v.fieldKey},
							sourceFiled: {name: v.fieldValue},
						})),
					},
				]
				console.log(res)
				if (res.data) {
					const resdata: any = res.data
					disableExchangeFillingType.value = true
					const sourceNameList = await getApiAuxiliaryFillingData(resdata.apiId)
					console.log(resdata)
					auxiliFillingData.value = [
						{
							id: resdata.apiId,
							name: resdata.apiName,
							sourceLedgerId: null,
							fieldId: resdata.selectFieldId,
							apiId: resdata.apiId,
							sourceLedgerFieldList: sourceNameList.data.map((x: any) => ({
								isUnique: false,
								displayName: x.fieldDisplayName,
								id: x.fieldName,
								key: x.id,
							})),

							type: 2,
							configs: [
								...resdata.rules.map((v: any) => ({
									fieldId: v.fieldKey,
									sourceFiledId: v.fieldValue,
									targetLedgerFieldList: currentLedgerFieldList.value,
								})),
							],
							previewFields: resdata.previewFields.map(
								(x) => x.apiAuxiliaryFilling?.fieldName
							),
						},
					]
				} else {
					disableExchangeFillingType.value = false
				}
			}
		} else if (app.auxiliaryFillingType === 1) {
			// 数据集填报
			getDataSetFillingData()
			const res = await getDataSetAuxiliaryFilling({
				LedgerId: auxiliFillingId.value,
				isPaged: false,
			})
			if (res.data) {
				ledgerAuxiliaryFillingList.value = [
					{
						...res.data.items[0],
						field: {
							name: res.data.items[0].configs.filter(
								(x) => x.dbTableFieldId === res.data.items[0].selectFieldId
							)[0]?.field?.name,
						},
						configs: res.data.items[0].configs.map((v) => ({
							...v,
							sourceFiled: v.dbTableField,
						})),
					},
				]
				const resdata = res.data.items[0]
				// const sourceNameList = await getApiAuxiliaryFillingData(resdata.apiId)

				const sourceList = await getDataSetInfoById(resdata.sourceTableDataSetId)
				auxiliFillingData.value = [
					{
						id: resdata.id,
						name: resdata.name,
						sourceLedgerId: null,
						fieldId: resdata.selectFieldId,
						apiId: resdata.sourceTableDataSetId,
						sourceLedgerFieldList: sourceList.data.tableDataSetMappingFields.map(
							(x: any) => ({
								isUnique: false,
								displayName: x.displayName,
								id: x.dbTableFieldId,
								key: x.dbTableFieldId,
							})
						),
						type: 3,
						configs: [
							...resdata.configs.map((v: any) => ({
								fieldId: v.fieldId,
								sourceFiledId: v.dbTableFieldId,
								targetLedgerFieldList: currentLedgerFieldList.value,
							})),
						],
						previewFields: resdata.previewFields.map((x) => x.dbTableFieldId),
					},
				]
			}
		} else {
			disableExchangeFillingType.value = false
			auxiliFillingData.value = [
				{
					name: null,
					sourceLedgerId: null,
					fieldId: null,
					sourceLedgerFieldList: sourceLedgerFieldList.value,
					type: 1,
					configs: [
						{
							fieldId: null,
							sourceFiledId: null,
							targetLedgerFieldList: currentLedgerFieldList.value,
						},
					],
				},
			]
			// 当只有1个唯一键时，自动填充查询字段以及数据绑定唯一键绑定值
			if (auxiliFillingData.value[0].configs[0].targetLedgerFieldList.length === 1) {
				auxiliFillingData.value[0].fieldId = auxiliFillingData.value[0].configs[0].fieldId =
					auxiliFillingData.value[0].configs[0].targetLedgerFieldList[0].id
			}
		}

		openAuxilifillingDialog.value = true
		// }
	}

	// 删除业务表
	if (btn.code === 'deleteLedger') {
		deleteOfflineLedger({ledgerId: scope.id}).then((res) => {
			ElMessage.success('删除成功！')
			getLedgerData()
		})
	}
}
// 获取业务表详情数据
async function detailByLedgerId(id: string) {
	const res = await getDetailByLedgerId(id)
	if (res.data) {
		return res.data
	} else {
		return {}
	}
}
const sourceLedgerData = ref<any[]>(
	RunwayList.map((x) => ({label: x.name, value: x.id, children: []}))
)

const getLedgerData = () => {
	loading.value = true
	getLedgerListsP({
		Filter: searchForm.value,
		LedgerTypeId: searchForm.LedgerTypeId,
		IsOnline: searchForm.IsOnline,
		ReminderInterval: searchForm.ReminderInterval,
		Runway: RunwayList[currentLedgerType.value]?.name ?? '',
		skipCount: (pageation.currentPage - 1) * pageation.pageSize,
		MaxResultCount: pageation.pageSize,
		Sorting: OrderFields.value,
		ignoreRunways: '市级共性业务表',
	})
		.then((res: any) => {
			console.log('123res', res)
			const list = res.data.items
			// list.forEach((f: any) => {
			// 	f.__ledgerType = f.ledgerType?.name
			// })
			tableData.value = res.data.items.map((item: any) => {
				return {...item, __ledgerType: item.ledgerType?.name}
			})
			pageation.total = res.data.totalCount
			loading.value = false
		})
		.catch((error: any) => {
			if (error.response?.status === 500) {
				ElNotification.error('当前配置业务表人数较多，请5分钟后再试')
			}
		})
	// 获取业务表管理跑道数量
	// ledgerManagerrunwayCount({
	// 	Filter: searchForm.value,
	// 	LedgerTypeId: searchForm.LedgerTypeId,
	// 	IsOnline: searchForm.IsOnline,
	// 	// Runway:RunwayList.map((m: any) => m.name), //内置在接口内了
	// 	// ReminderInterval:0,
	// 	// IsOnlyContainsUniqueField:false,
	// }).then((res: any) => {
	// 	console.log(res.data, 'ledgerManagerrunwayCount')
	// 	runwayCountList.value = res.data
	// })
}
const onPageationChange = (val: number, type: string) => {
	if (type === 'size') {
		pageation.pageSize = val
	} else {
		pageation.currentPage = val
	}
	getLedgerData()
}
const OrderFields = ref()
const onSortableChange = (e) => {
	if (e.order === null) {
		OrderFields.value = ''
	} else {
		OrderFields.value = `${e.prop} ${e.order === 'ascending' ? 'asc' : 'desc'}`
	}
	getLedgerData()
}
const onConfirmChangeLedgerName = () => {
	if (!newLedgerName.value) return ElMessage.error('请填写业务表名称')
	updateLedgerName(currentDialogRow.value.id, newLedgerName.value).then((res: any) => {
		getLedgerData()

		openChangeLedgerName.value = false
		ElMessage.success('更新成功')
	})
}
const onConfirmChangeLedgerType = () => {
	if (!runway.value) return ElMessage.error('请选择更新后业务表所属板块')
	if (!newLedgerType.value) return ElMessage.error('请选择更新后业务表所属部门')
	if (currentDialogRow.value.runway !== runway.value) {
		const params = {
			ledgerId: currentDialogRow.value.id,
			newRunway: runway.value,
			oldRunway: currentDialogRow.value.runway,
		}
		deleteRelations(params)
	}
	updateLedgerDataType(currentDialogRow.value.id, newLedgerType.value, runway.value).then(
		(res: any) => {
			getLedgerData()
			openChangeLedgerType.value = false
			saveData.value = true
			ElMessage.success('更新成功')
		}
	)
}
const flagConfig1 = ref(true)
const onConfirmChangeLedgerTime = () => {
	const newRemindConfig = ref<any>({})
	const flagConfig = ref<boolean>(true)
	newRemindConfig.value.interval = remindConfig.value.interval
	console.log(remindConfig.value, 'cesss')

	if (remindConfig.value.interval == 2) {
		newRemindConfig.value.weeklyStartDayOfWeek = remindConfig.value.weeklyStartDayOfWeek
		newRemindConfig.value.weeklyReminderDayOfWeek = remindConfig.value.weeklyReminderDayOfWeek
		newRemindConfig.value.weeklyDeadlineDayOfWeek = remindConfig.value.weeklyDeadlineDayOfWeek
	}
	if (remindConfig.value.interval == 3) {
		newRemindConfig.value.monthlyStartDay = remindConfig.value.monthlyStartDay
		newRemindConfig.value.monthlyReminderDay = remindConfig.value.monthlyReminderDay
		newRemindConfig.value.monthlyDeadlineDay = remindConfig.value.monthlyDeadlineDay
		flagConfig.value =
			newRemindConfig.value.monthlyReminderDay > remindConfig.value.monthlyDeadlineDay
				? false
				: true
	}
	if (remindConfig.value.interval == 4) {
		newRemindConfig.value.quarterlyStartDay1 = remindConfig.value.quarterlyStartDay1
			.substring(4)
			.replace(/./, '0001-')
		newRemindConfig.value.quarterlyReminderDay1 = remindConfig.value.quarterlyReminderDay1
			.substring(4)
			.replace(/./, '0001-')
		newRemindConfig.value.quarterlyDeadlineDay1 = remindConfig.value.quarterlyDeadlineDay1
			.substring(4)
			.replace(/./, '0001-')
		newRemindConfig.value.quarterlyStartDay2 = remindConfig.value.quarterlyStartDay2
			.substring(4)
			.replace(/./, '0001-')
		newRemindConfig.value.quarterlyReminderDay2 = remindConfig.value.quarterlyReminderDay2
			.substring(4)
			.replace(/./, '0001-')
		newRemindConfig.value.quarterlyDeadlineDay2 = remindConfig.value.quarterlyDeadlineDay2
			.substring(4)
			.replace(/./, '0001-')
		newRemindConfig.value.quarterlyStartDay3 = remindConfig.value.quarterlyStartDay3
			.substring(4)
			.replace(/./, '0001-')
		newRemindConfig.value.quarterlyReminderDay3 = remindConfig.value.quarterlyReminderDay3
			.substring(4)
			.replace(/./, '0001-')
		newRemindConfig.value.quarterlyDeadlineDay3 = remindConfig.value.quarterlyDeadlineDay3
			.substring(4)
			.replace(/./, '0001-')
		newRemindConfig.value.quarterlyStartDay4 = remindConfig.value.quarterlyStartDay4
			.substring(4)
			.replace(/./, '0001-')
		newRemindConfig.value.quarterlyReminderDay4 = remindConfig.value.quarterlyReminderDay4
			.substring(4)
			.replace(/./, '0001-')
		newRemindConfig.value.quarterlyDeadlineDay4 = remindConfig.value.quarterlyDeadlineDay4
			.substring(4)
			.replace(/./, '0001-')
	}
	if (remindConfig.value.interval == 5) {
		console.log(newRemindConfig.value, 'cesss')
		newRemindConfig.value.yearlyStartDay = remindConfig.value.yearlyStartDay
			.substring(4)
			.replace(/./, '0001-')

		newRemindConfig.value.yearlyReminderDay = remindConfig.value.yearlyReminderDay
			.substring(4)
			.replace(/./, '0001-')
		newRemindConfig.value.yearlyDeadlineDay = remindConfig.value.yearlyDeadlineDay
			.substring(4)
			.replace(/./, '0001-')
		flagConfig.value =
			new Date(newRemindConfig.value.yearlyReminderDay) >
			new Date(newRemindConfig.value.yearlyDeadlineDay)
				? false
				: true
	}
	if (remindConfig.value.interval == 6) {
		newRemindConfig.value.upHalfMonthReminderTime = remindConfig.value.upHalfMonthReminderTime
		newRemindConfig.value.upHalfMonthStartTime = remindConfig.value.upHalfMonthStartTime

		newRemindConfig.value.upHalfMonthDateOnlyTime = remindConfig.value.upHalfMonthDateOnlyTime
		newRemindConfig.value.downHalfMonthReminderTime =
			remindConfig.value.downHalfMonthReminderTime
		newRemindConfig.value.downHalfMonthStartTime = remindConfig.value.downHalfMonthStartTime

		newRemindConfig.value.downHalfMonthDateOnlyTime =
			remindConfig.value.downHalfMonthDateOnlyTime
		if (newRemindConfig.value.upHalfMonthReminderTime) {
			flagConfig1.value =
				newRemindConfig.value.upHalfMonthReminderTime >
				newRemindConfig.value.upHalfMonthDateOnlyTime
					? false
					: true
		}
		if (newRemindConfig.value.downHalfMonthReminderTime) {
			flagConfig.value =
				newRemindConfig.value.downHalfMonthReminderTime >
				newRemindConfig.value.downHalfMonthDateOnlyTime
					? false
					: true
		}
	}
	if (remindConfig.value.interval == 7) {
		// upHalfYearReminderTime: '',
		// upHalfYearDateOnlyTime: '',
		// downHalfYearReminderTime:'',
		// downHalfYearDateOnlyTime: '',
		newRemindConfig.value.upHalfYearReminderTime =
			remindConfig.value.upHalfYearReminderTime &&
			remindConfig.value.upHalfYearReminderTime.substring(4).replace(/./, '0001-')
		newRemindConfig.value.upHalfYearStartTime =
			remindConfig.value.upHalfYearStartTime &&
			remindConfig.value.upHalfYearStartTime.substring(4).replace(/./, '0001-')
		newRemindConfig.value.upHalfYearDateOnlyTime =
			remindConfig.value.upHalfYearDateOnlyTime &&
			remindConfig.value.upHalfYearDateOnlyTime.substring(4).replace(/./, '0001-')
		if (newRemindConfig.value.upHalfYearReminderTime) {
			flagConfig.value =
				new Date(newRemindConfig.value.upHalfYearReminderTime) >
				new Date(newRemindConfig.value.upHalfYearDateOnlyTime)
					? false
					: true
		}

		newRemindConfig.value.downHalfYearReminderTime =
			remindConfig.value.downHalfYearReminderTime &&
			remindConfig.value.downHalfYearReminderTime.substring(4).replace(/./, '0001-')

		newRemindConfig.value.downHalfYearStartTime =
			remindConfig.value.downHalfYearStartTime &&
			remindConfig.value.downHalfYearStartTime.substring(4).replace(/./, '0001-')
		newRemindConfig.value.downHalfYearDateOnlyTime =
			remindConfig.value.downHalfYearDateOnlyTime &&
			remindConfig.value.downHalfYearDateOnlyTime.substring(4).replace(/./, '0001-')
		if (newRemindConfig.value.downHalfYearReminderTime) {
			flagConfig1.value =
				new Date(newRemindConfig.value.downHalfYearReminderTime) >
				new Date(newRemindConfig.value.downHalfYearDateOnlyTime)
					? false
					: true
		}
	}

	if (!flagConfig.value || !flagConfig1.value) {
		ElMessage.warning('结束时间不得小于提醒时间')
	} else {
		updateLedgerDataConfig(currentDialogRow.value.id, newRemindConfig.value).then(
			(res: any) => {
				if (res.status == 204) {
					getLedgerData()
					openChangeLedgerTime.value = false
					ElMessage.success('更新成功')
				}
			}
		)
	}
}

const getLedgerDataLists = (str: string) => {
	pageation.currentPage = 1
	if (str === 'empty') {
		searchForm.value = ''
		searchForm.LedgerTypeId = null
		searchForm.IsOnline = undefined
		searchForm.ReminderInterval = null
		currentLedgerType.value = -1
	}
	getLedgerData()
}

const onChangeRunway = (index: number) => {
	searchForm.LedgerTypeId = null
	currentLedgerType.value = index
	getLedgerData()
	// RunwayList[index]?.name
	// getRunwayLedgerType({runway: index == -1 ? '' : RunwayList[index]?.name}).then((res) => {
	// 	publishLedgerDepartment.value = res.data.items
	// })
}

const allCount = ref(0)

const onBatchData = async (status: boolean | null) => {
	const elTableRef = tableRef.value.getElTableRef()
	const ids = elTableRef.getSelectionRows().map((item: any) => item.id)

	if (ids.length === 0) {
		ElMessage.warning('请选择需要操作的数据')
		return
	}
	loading.value = true
	if (status === null) {
		await useBatchData().deleteByIds(ids)
	} else if (status) {
		await useBatchData().onlineByIds(ids, true, false)
	} else {
		await useBatchData().onlineByIds(ids, false, false)
	}
	getLedgerData()
	elTableRef.clearSelection()
}

onMounted(() => {})
onActivated(() => {
	getLedgerData()
	getLedgerByRunway()
	try {
		getRunwayLedgerType().then((res) => {
			publishLedgerDepartment.value = res.data.items
		})
	} catch (error: any) {
		if (error.response?.status === 500) {
			return ElNotification.error('当前配置业务表人数较多，请5分钟后再试')
		}
	}

	ledgerManagerrunwayCount({})
		.then((res: any) => {
			runwayCountList.value = res.data
			allCount.value = res.data
				.filter((item: any) => item.runway !== '市级共性业务表')
				.reduce((accumulator, currentValue) => {
					return accumulator + currentValue.count
				}, 0)
		})
		.catch((error: any) => {
			if (error.response?.status === 500) {
				ElNotification.error('当前配置业务表人数较多，请5分钟后再试')
			}
		})
})

const remindConfig = ref<any>({
	interval: 0,
	// 	dailyReminderTime: "00:00:00",
	//     dailyDeadlineTime: "23:59:59",
	weeklyReminderDayOfWeek: '',
	weeklyDeadlineDayOfWeek: '',
	monthlyReminderDay: 0,
	monthlyDeadlineDay: 0,
	quarterlyStartDay1: '',
	quarterlyReminderDay1: '',
	quarterlyDeadlineDay1: '',
	quarterlyStartDay2: '',
	quarterlyReminderDay2: '',
	quarterlyDeadlineDay2: '',
	quarterlyStartDay3: '',
	quarterlyReminderDay3: '',
	quarterlyDeadlineDay3: '',
	quarterlyStartDay4: '',
	quarterlyReminderDay4: '',
	quarterlyDeadlineDay4: '',
	yearlyStartDay: '',
	yearlyReminderDay: '',
	yearlyDeadlineDay: '',
})
const isCreateLedgerTemplate = ref<any>(false)
const ruleFormLedgerTemplate = ref<any>({
	displayName: '',
	remark: '',
}) //表单
const rulesLedgerTemplate = ref<any>({
	name: [{required: true, message: '请输入业务表模板名称', trigger: 'blur'}],
})
const ruleFormRef = ref<any>(null)
//验证规则
const submitForm = () => {
	ruleFormRef?.value.validate((valid: any) => {
		if (valid) {
			addLedgerTemplate(createLedgerTemplateId.value, ruleFormLedgerTemplate.value).then(
				(res: any) => {
					ElMessage.success('业务表模板添加成功!')
					isCreateLedgerTemplate.value = false
					ruleFormLedgerTemplate.value.displayName = ''
					ruleFormLedgerTemplate.value.remark = ''
				}
			)
		}
	})
}
const auxiliFillingData = ref<any[]>([
	{
		name: null,
		sourceLedgerId: null,
		fieldId: null,
		sourceLedgerFieldList: [],
		type: 1,
		configs: [{fieldId: null, sourceFiledId: null, targetLedgerFieldList: []}],
		previewFields: [],
	},
])
const sourceLedgerFieldList = ref<any[]>([]) // 业务表数据源字段集合
const currentLedgerFieldList = ref<any[]>([]) // 当前数据源字段集合
const activeNames = computed(() =>
	auxiliFillingData.value[auxiliFillingData.value.length - 1]?.name
		? auxiliFillingData.value[auxiliFillingData.value.length - 1]?.name
		: `辅助填报${auxiliFillingData.value.length}`
)
// 添加多个辅助填报

const activeName = ref('所有业务表')
function addAuxilifilling() {
	auxiliFillingData.value.push({
		name: null,
		sourceLedgerId: null,
		fieldId: null,
		type: 1,
		configs: [
			{
				fieldId: null,
				sourceFiledId: null,
				targetLedgerFieldList: currentLedgerFieldList.value,
			},
		],
		previewFields: [],
		sourceLedgerFieldList: sourceLedgerFieldList.value,
		targetLedgerFieldList: currentLedgerFieldList.value,
	})
}
// 添加多个数据绑定
function addDataConfig(item: any) {
	// auxiliFillingData.value[index].configs.push({fieldId: null, sourceFiledId: null})
	item.configs.push({
		fieldId: null,
		sourceFiledId: null,
		targetLedgerFieldList: currentLedgerFieldList.value,
	})
}
// 当选定数据源发生变化时
async function sourceLedgerChange(e: any, index: number) {
	// 切换数据源时数据绑定列表清空
	sourceLedgerFieldList.value = auxiliFillingData.value[index].sourceLedgerFieldList =
		await getLedgerInfo(e[1])

	if (sourceLedgerFieldList.value.filter((v) => v.isUnique).length === 1) {
		auxiliFillingData.value[index].configs[0].sourceFiledId = auxiliFillingData.value[
			index
		].sourceLedgerFieldList.filter((v) => v.isUnique)[0].id
	}
	auxiliFillingData.value[index].configs = [auxiliFillingData.value[index].configs[0]]
	console.log(auxiliFillingData.value[index].configs)
}
async function getLedgerByRunway() {
	try {
		const res = await getrunwayledgerList()
		sourceLedgerData.value.forEach((v: any) => {
			v.children = []

			if (res.data) {
				if (v.label === '党的建设') {
					v.children.push(
						...res.data
							.filter((x) => x.runwayName === '党的建设')[0]
							.ledgerItemDtos.map((x: any) => ({
								label: x.name,
								value: x.id,
								leaf: true,
							}))
					)
				}
				if (v.label === '经济发展') {
					v.children.push(
						...res.data
							.filter((x) => x.runwayName === '经济发展')[0]
							.ledgerItemDtos.map((x: any) => ({
								label: x.name,
								value: x.id,
								leaf: true,
							}))
					)
				}
				if (v.label === '民生服务') {
					v.children.push(
						...res.data
							.filter((x) => x.runwayName === '民生服务')[0]
							.ledgerItemDtos.map((x: any) => ({
								label: x.name,
								value: x.id,
								leaf: true,
							}))
					)
				}
				if (v.label === '平安法治') {
					v.children.push(
						...res.data
							.filter((x) => x.runwayName === '平安法治')[0]
							.ledgerItemDtos.map((x: any) => ({
								label: x.name,
								value: x.id,
								leaf: true,
							}))
					)
				}
				// v.children.push(...res.data.items.map((x: any) => ({label: x.name, value: x.id, leaf: true})))
			} else {
				v.disabled = true
			}
		})
	} catch (error: any) {
		if (error.response?.status === 500) {
			ElNotification.error('当前配置业务表人数较多，请5分钟后再试')
		}
	}
}
// 获取业务表详情数据
async function getLedgerInfo(id: string) {
	const res = await getLedgerList('', 0, 1, id)
	console.log(res)
	if (res.data) {
		// getPreviewData(res.data)
		return res.data.tableInfo.fields
	} else {
		return []
	}
}
async function getLedgerDetail(id: string) {
	const res = await getLedgerList('', 0, 1, id)
	if (res.data) return res.data
	else return null
}
function sourceFiledSelectChange(val: string) {}
const removeConfig = (item: any[], index: number) => {
	if (auxiliFillingData.value[0].type === 3) {
		if (index === 0) return ElMessage.warning('查询字段关联不可删除')
	}

	item.splice(index, 1)
}
const removeAuxilifillingItem = (e: any, data: any, index: number) => {
	e.stopPropagation()
	if (data.id && data.fieldId !== null && !data.apiId) {
		removeLedgerAuxiliaryFilling(auxiliFillingId.value, data.sourceLedgerId[1]).then(
			async (r) => {
				ElMessage.success('删除成功')
				const res = await getLedgerAuxiliaryFilling({
					LedgerId: auxiliFillingId.value,
					IsPaged: false,
				})
				if (res) {
					if (res.status === 200 && res.data.items.length !== 0) {
						// 当有辅助填报数据时，先清空再填充
						auxiliFillingData.value = []
						res.data.items.forEach(async (v: any) => {
							sourceLedgerFieldList.value = await getLedgerInfo(v.sourceLedgerId)
							auxiliFillingData.value.push({
								name: v.name,
								id: v.creatorId,
								sourceLedgerId: [
									RunwayList.filter((x) => x.name === v.sourceLedger.runway)[0]
										.id,
									v.sourceLedgerId,
								],
								fieldId: v.fieldId,
								sourceLedgerFieldList: sourceLedgerFieldList.value,
								configs: [
									...v.configs
										.filter((x) => x.field.isUnique)
										.map((x) => ({
											fieldId: x.fieldId,
											isUnique: x.field.isUnique,
											sourceFiledId: x.sourceFiledId,
											targetLedgerFieldList: currentLedgerFieldList.value,
										})),
									...v.configs
										.filter((x) => !x.field.isUnique)
										.map((x) => ({
											fieldId: x.fieldId,
											isUnique: x.field.isUnique,
											sourceFiledId: x.sourceFiledId,
											targetLedgerFieldList: currentLedgerFieldList.value,
										})),
								],
							})
						})
					} else {
						auxiliFillingData.value = [
							{
								name: null,
								sourceLedgerId: null,
								fieldId: null,
								sourceLedgerFieldList: sourceLedgerFieldList.value,
								type: 1,
								configs: [
									{
										fieldId: null,
										sourceFiledId: null,
										targetLedgerFieldList: currentLedgerFieldList.value,
									},
								],
							},
						]
						// 当只有1个唯一键时，自动填充查询字段以及数据绑定唯一键绑定值
						if (
							auxiliFillingData.value[0].configs[0].targetLedgerFieldList.length === 1
						) {
							auxiliFillingData.value[0].fieldId =
								auxiliFillingData.value[0].configs[0].fieldId =
									auxiliFillingData.value[0].configs[0].targetLedgerFieldList[0].id
						}
					}
				}
			}
		)
	} else {
		if (data.type === 2) {
			removeApiAuxiliaryFillingData(auxiliFillingId.value)
		}
		if (data.type === 3) {
			// console.log(aux);

			removeDataSetAuxiliaryFillingData(data.id)
		}
		auxiliFillingData.value.splice(index, 1)
		if (auxiliFillingData.value.length === 0) {
			addAuxilifilling()
		}
	}
	disableExchangeFillingType.value = false
}
const disableExchangeFillingType = ref(false)
// 业务表辅助填报数据
const getLedgerAuxiliaryFillingData = async (index: number) => {
	auxiliFillingData.value[index] = {
		name: null,
		sourceLedgerId: null,
		fieldId: null,
		sourceLedgerFieldList: sourceLedgerFieldList.value,
		type: 1,
		configs: [
			{
				fieldId: null,
				sourceFiledId: null,
				targetLedgerFieldList: currentLedgerFieldList.value,
			},
		],
		previewFields: [],
	}
}
// 数据源辅助填报数据
const getSourceAuxiliaryFillingData = async (index: number) => {
	// 数据源辅助填报
	const apiScource = await getApiList({})

	if (apiScource.data) {
		apilist.value = apiScource.data.map((v: any) => ({
			id: v.id,
			name: v.apiName,
		}))

		// 接口请求封装回显
		const res = await getApiAuxiliaryFillingList(auxiliFillingId.value)
		if (res.data && res.status !== 204) {
			ledgerAuxiliaryFillingList.value = [
				{
					...res.data,
					field: {name: res.data.ledgerKey},
					configs: res.data.rules?.map((v: any) => ({
						field: {name: v.fieldKey},
						sourceFiled: {name: v.fieldValue},
					})),
				},
			]
			console.log(res)
			const resdata: any = res.data
			const sourceNameList = await getApiAuxiliaryFillingData(resdata.apiId)
			auxiliFillingData.value = [
				{
					id: resdata.apiId,
					name: resdata.apiName,
					sourceLedgerId: null,
					fieldId: null,
					apiId: resdata.apiId,
					sourceLedgerFieldList: sourceNameList.data.map((x: any) => ({
						isUnique: false,
						displayName: x.fieldDisplayName,
						id: x.fieldName,
						key: x.id,
					})),
					// .concat({
					// 	isUnique: true,
					// 	displayName: sourceNameList.data[0].keyFieldDisplayName,
					// 	id: sourceNameList.data[0].keyFieldName,
					// }),
					type: 2,
					configs: [
						{
							fieldId: currentLedgerFieldList.value.filter((v) => v.isUnique)[0]
								?.name,
							sourceFiledId: sourceNameList.data[0].keyFieldName,
							targetLedgerFieldList: currentLedgerFieldList.value,
						},
						...resdata.rules
							.map((v: any) => ({
								fieldId: v.fieldKey,
								sourceFiledId: v.fieldValue,
								targetLedgerFieldList: currentLedgerFieldList.value,
							}))
							.filter(
								(x: any) =>
									x.fieldId !==
									currentLedgerFieldList.value.filter((v) => v.isUnique)[0].name
							),
					],
				},
			]
		}
	}

	const sourceNameList = await getApiAuxiliaryFillingData(apilist.value[0].id)
	auxiliFillingData.value = [
		{
			name: null,
			sourceLedgerId: null,
			fieldId: null, //阿达撒旦
			apiId: sourceNameList.data[0].apiId,
			sourceLedgerFieldList: sourceNameList.data.map((x: any) => ({
				isUnique: false,
				displayName: x.fieldDisplayName,
				id: x.fieldName,
				key: x.id,
			})),
			// .concat({
			// 	isUnique: true,
			// 	displayName: sourceNameList.data[0].keyFieldDisplayName,
			// 	id: sourceNameList.data[0].keyFieldName,
			// }),
			// currentLedgerFieldList.value.filter((v) => v.isUnique)[0]?.name
			type: 2,
			configs: [
				{
					fieldId: null,
					sourceFiledId: sourceNameList.data[0].keyFieldName,
					targetLedgerFieldList: currentLedgerFieldList.value,
				},
			],
		},
	]
	console.log(auxiliFillingData.value)
}
const dataSetList = ref<any[]>([])
const getDataSetFillingData = async (index?: number) => {
	const data = await getDataSetList()

	if (data.data) {
		dataSetList.value = data.data.items.map((v) => ({
			name: v.name,
			id: v.id,
		}))
	}
	auxiliFillingData.value[index ? index : 0] = {
		name: null,
		sourceLedgerId: null,
		fieldId: null,
		sourceLedgerFieldList: [],
		type: 3,
		configs: [
			{
				fieldId: null,
				sourceFiledId: null,
				targetLedgerFieldList: currentLedgerFieldList.value,
			},
		],
		previewFields: [],
	}
}
const AuxilifillingChange = (index: number, e) => {
	console.log(index)
	if (e === 1) {
		getLedgerAuxiliaryFillingData(index)
	}
	if (e === 2) {
		getSourceAuxiliaryFillingData(index)
	}
	if (e === 3) {
		getDataSetFillingData(index)
	}
}
const previewModel = ref(false)
const showAddLedgerDataModelisVisible = ref(false)
function preview() {
	previewModel.value = true
	showAddLedgerDataModelisVisible.value = true
}
const tableFiled = ref<any>([])
const tableGroup = ref<any>([])
const filterField = ref<any>([])
const tableGroupInfo = ref<any>([])
const handleData = ref<any>([])
const fields: any = ref([])
const colDatas = ref([{title: '-', field: '-', fixed: '-'}])
const formArray: any = ref([])
const formSearch: any = ref([])
const formRules = ref<FormRules>({
	// name: [{required: true, message: '请输入姓名', trigger: 'blur'}],
})
// 递归
const recursionFun = (obj: any) => {
	let arr: any = []
	obj.modifyChildren = []
	obj.children.forEach((item: any, index: any) => {
		if (!item.displayName) {
			if (arr.length > 0) {
				obj.modifyChildren.push(arr)
			}
			obj.modifyChildren.push(item)
			// activeNames.value.push(item.id)
			recursionFun(obj.modifyChildren[obj.modifyChildren.length - 1])
			// 把这一段放进去递归
			arr = []
		} else {
			arr.push(item)
		}
		if (obj.children.length - 1 == index) {
			if (arr.length > 0) {
				obj.modifyChildren.push(arr)
			}
			arr = []
		}
	})
}
function getPreviewData(data: any) {
	const {tableFieldGroups} = data.tableInfo

	// 过滤有权限字段
	tableFiled.value = data.tableInfo.fields.filter(
		(f: any) => f.isDepartmentField && f.isListField
	)
	tableGroup.value = data.tableInfo.tableFieldGroups

	let tableFieldField: any = []
	tableFieldGroups.forEach((item: any) => {
		tableFieldField = [
			...tableFieldField,
			...item.tableFields.filter((f: any) => f.isDepartmentField && f.isListField),
		]
	})

	filterField.value = tableFiled.value.filter((f: any) => {
		return !tableFieldField.some((f2: any) => f2.id === f.id)
	})

	const group: any = []
	tableFieldGroups.forEach((item: any, index: any) => {
		if (item.tableFields) {
			group.push(
				Object.assign(item, {
					id: item.id,
					label: item.name,
					parentId: item.parentId,
					tableFields: toRaw(
						item.tableFields.filter((f: any) => f.isDepartmentField && f.isListField)
					),
				})
			)
			item.tableFields.forEach((tf: any) => {
				if (tf.isListField)
					group.push({
						label: tf.displayName,
						field: tf.name,
						parentId: item.id,
						id: tf.id,
						...tf,
					})
			})
		} else {
			group.push({label: item.name, id: item.id, parentId: item.parentId})
		}
	})
	const LedgerGroup = useArrayToTree(group, 'id', 'parentId', 'label', false, 'children', true)

	filterField.value = [...filterField.value, ...LedgerGroup]

	filterField.value.sort((a: any, b: any) => a.sort - b.sort)
	let arr: any = []
	filterField.value.forEach((item: any, index: any) => {
		if (!item.children) {
			arr.push(item)
		} else {
			if (arr.length > 0) {
				handleData.value.push(arr)
			}
			handleData.value.push(item)
			// activeNames.value.push(item.id)
			recursionFun(handleData.value[handleData.value.length - 1])
			// 使用递归去添加item数据
			arr = []
		}
		if (filterField.value.length - 1 == index) {
			if (arr.length > 0) {
				handleData.value.push(arr)
			}
			arr = []
		}
	})

	// handleData.value =
	filterField.value = tableFiled.value.filter((f: any) => {
		return !tableFieldField.some((f2: any) => f2.id === f.id)
	})

	const __fields = tableFiled.value.filter((f: any) => f.isListField)
	const groups: any = []
	const columns: any = tableFieldGroups.length === 0 ? __fields : tableFieldGroups

	columns.forEach((f: any) => {
		if (f.tableFields) {
			groups.push(
				Object.assign(f, {
					id: f.id,
					title: f.name,
					align: 'center',
					parentId: f.parentId,
					__isGroup: true,
					sort: f.sort,
					sortable:
						f.type === 'datetime' ||
						f.type === 'date' ||
						f.type === 'decimal' ||
						f.type === 'int',
				})
			)
			f.tableFields
				.filter((ff: any) => ff.isListField)
				.forEach((tf: any) => {
					groups.push({
						title: tf.displayName ?? tf.name,
						field: tf.name,
						raw: tf,
						parentId: f.id,
						sort: tf.sort,
						sortable:
							tf.type === 'datetime' ||
							tf.type === 'date' ||
							tf.type === 'decimal' ||
							tf.type === 'int',
					})
				})
		} else {
			groups.push({
				id: f.id,
				title: f.displayName ?? f.name,
				align: 'center',
				field: tableFieldGroups.length === 0 ? f.name : null,
				parentId: f.parentId,
				raw: f,
				sort: f.sort,
				__isGroup: tableFieldGroups.length !== 0,
				sortable:
					f.type === 'datetime' ||
					f.type === 'date' ||
					f.type === 'decimal' ||
					f.type === 'int',
			})
		}
	})

	// 处理没分组字段
	__fields.forEach((f: any) => {
		if (!groups.some((g: any) => !g.__isGroup && g.field === f.name)) {
			groups.push({
				title: f.displayName ?? f.name,
				field: f.name,
				raw: f,
				parentId: null,
				sort: f.sort,
				sortable:
					f.type === 'datetime' ||
					f.type === 'date' ||
					f.type === 'decimal' ||
					f.type === 'int',
			})
		}
	})
	groups.forEach((v: any) => {
		if (v.children) v.children = []
	})
	colDatas.value = useArrayToTree(groups, 'id', 'parentId', 'title', false, 'children', true)

	colDatas.value = [...colDatas.value, ...[{title: '状态', field: 'DataStatus', fixed: 'right'}]]

	fields.value = data.tableInfo.fields.sort((a: any, b: any) => a.sort - b.sort)
	fields.value = [
		...fields.value,
		...[
			{title: '状态', name: 'DataStatus', field: 'DataStatus'},
			{title: '锁定人', name: 'LockUserId', field: 'LockUserId'},
			{title: '删除人', name: 'DeleteUserId', field: 'DeleteUserId'},
		],
	]

	// 表格
	fields.value.unshift({
		name: 'Id',
		displayName: 'Id',
		type: 'string',
	})

	const __fieldsFilter = fields.value
		.filter(
			(f: any) =>
				!['Id', 'DataStatus', 'LockUserId', 'DeleteUserId'].includes(f.name) &&
				(f.isListField || !f.isNullable)
		)
		.map((m: any) => {
			const item: any = {
				type: m.type,
				title: m.displayName,
				field: m.name,
				disabled: m.editDisabled,
				__disabled: m.editDisabled,
				raw: m,
			}

			if (m.type === 'date') {
				item.datetype = 'days'
			}

			if (m.type === 'datetime') {
				item.datetype = 'times'
			}

			// 单、复选框
			if (m.type === 'string' && typeof m.multiple === 'boolean') {
				item.__type = m.multiple ? 'checkbox' : 'radio'
				item.multiple = m.multiple
				item.type = 'select'
				item.data = m.options.map((o: any) => ({label: o, value: o}))
				item.full = true
			}
			return item
		})

	// 新增/编辑
	formArray.value = __fieldsFilter.filter(
		(f: any) => !['Informant', 'DataSource', 'Editor', 'UpdateTime'].includes(f.raw.name)
	)

	// 必填字段
	formArray.value
		.filter((f: any) => !f.raw.isNullable)
		.forEach((f: any) => {
			formRules.value[f.raw.name] = [
				{required: true, message: `请输入${f.raw.displayName}`, trigger: 'blur'},
			]
		})

	// 搜索
	formSearch.value = JSON.parse(
		JSON.stringify(__fieldsFilter.filter((f: any) => f.raw.isQueryField))
	)

	formSearch.value.forEach((f: any) => {
		f.disabled = f.__disabled = false
		if (f.__type === 'checkbox' || f.__type === 'radio') {
			f.full = false
		}
	})
}
const ledgerAuxiliaryFillingList = ref<any[]>([])
// 失去焦点---辅助填报功能
const addFormRef = ref()
const onBlur = (val: any, f: any, v) => {
	let fields = []
	if (ledgerAuxiliaryFillingList.value.length !== 0) {
		fields = ledgerAuxiliaryFillingList.value.map((v) => v.field.name)
	}
	if (fields.includes(f)) {
		const cur = ledgerAuxiliaryFillingList.value.filter((v) => v.field.name === f)
		console.log('目标', f)
		let obj: any = {}
		cur[0].configs.forEach((el: any) => {
			obj[el.sourceFiled.name] = el.field.name
		})
		const searchPms = {
			ledgerId:
				auxiliaryFillingType.value === 2 ? auxiliFillingId.value : cur[0].sourceLedgerId,
			maxResultCount: 1,
			skipCount: 0,
			whereFields: {
				[cur[0].configs.filter((e: any) => e.field.name === f)[0].sourceFiled.name]: {
					0: v,
				},
			},
			selectFields: obj,
			keyword: v,
		}
		getSingleLedgerData(searchPms).then((res) => {
			if (res.status === 200) {
				if (!res.data) return ElMessage.warning('暂未查询到数据')

				formArray.value.forEach((v: any) => {
					if (v.field === 'District' || v.field === 'City') return
					if (Object.values(obj).includes(v.field)) {
						v.default = res.data[v.field]
					}
				})
				addFormRef.value?.reset()
				console.log(133, formArray.value)
			}
		})
		console.log(searchPms)
	}
}
// 点击业务表辅助填报配置弹窗的确定
const onAuxilifillingConfirm = () => {
	let promise: any[] = []
	auxiliFillingData.value.forEach((f: any) => {
		if (!f.name) {
			return
		}

		if (f.configs.length === 0) {
			return
		}
		if (!f.configs[0].sourceFiledId || !f.configs[0].fieldId) {
			return
		}
		console.log(!f.previewFields)
		console.log(f.previewFields)
		console.log(f.previewFields === undefined)
		console.log(
			(f.previewFields && f.previewFields.length === 0) || f.previewFields === undefined
		)

		if ((f.previewFields && f.previewFields.length === 0) || f.previewFields === undefined) {
			return ElMessage.error('请选择预览字段')
		}
		if (f.type === 1) {
			if (!f.fieldId) {
				return
			}
			if (!f.sourceLedgerId) {
				return
			}

			// 数据源业务表辅助填报
			const params = {
				name: f.name,
				ledgerId: auxiliFillingId.value,
				sourceLedgerId: f.sourceLedgerId[1],
				fieldId: f.fieldId,
				configs: f.configs
					.filter((v: any) => v.sourceFiledId !== null || v.fieldId !== null)
					.map((d: any) => ({fieldId: d.fieldId, sourceFiledId: d.sourceFiledId})),
				previewFields: f.previewFields.map((v) => ({
					ledgerId: auxiliFillingId.value,
					auxiliaryFillingType: 0,
					sourceLedgerId: f.sourceLedgerId[1],
					tableFieldId: v,
				})),
			}
			promise.push(
				!f.id
					? createLedgerAuxiliaryFilling(params)
					: updateLedgerAuxiliaryFilling(
							auxiliFillingId.value,
							f.sourceLedgerId[1],
							params
					  )
			)
		}
		// api 辅助填报
		if (f.type === 2) {
			if (!f.apiId) {
				return
			}
			const params = {
				ApiName: f.name,
				LedgerId: auxiliFillingId.value,
				ApiId: f.apiId,
				fieldId: f.fieldId,
				Rules: f.configs
					.filter((v: any) => v.sourceFiledId !== null || v.fieldId !== null)
					.map((d: any) => ({FieldKey: d.fieldId, FieldValue: d.sourceFiledId})),
				previewFileds: f.previewFields.map((x) => {
					return f.sourceLedgerFieldList.find((y) => y.id === x)?.key
				}),
			}
			promise.push(CreateApiAuxiliaryFillingData(params))
		}
		// 数据集辅助填报
		if (f.type === 3) {
			if (!f.apiId) {
				return
			}
			const params = {
				name: f.name,
				ledgerId: auxiliFillingId.value,
				SourceTableDataSetId: f.apiId,
				selectFieldId: f.fieldId,
				configs: f.configs
					.filter((v: any) => v.sourceFiledId !== null || v.fieldId !== null)
					.map((d: any) => ({fieldId: d.fieldId, dbTableFieldId: d.sourceFiledId})),
				previewFields: f.previewFields.map((v) => ({
					auxiliaryFillingType: 1,
					dbTableFieldId: v,
				})),
			}
			promise.push(
				!f.id
					? CreateDataSetAuxiliaryFillingData(params)
					: updateDataSetAuxiliaryFillingData(f.id, params)
			)
		}
	})
	if (promise.length === auxiliFillingData.value.length) {
		Promise.all(promise).then((res) => {
			ElMessage.success('设置成功')
			openAuxilifillingDialog.value = false
			getLedgerData()
		})
	} else {
		ElMessage.warning('请完成所有必填项')
	}
}

const openLedgerOnlineModal = ref(false)
const startImmediately = ref(true)
const getReadCycle = (interval: number) => {
	switch (interval) {
		case 0:
			return '不提醒'
		case 2:
			return '每周'
		case 3:
			return '每月'
		case 4:
			return '每季度'
		case 5:
			return '每年'
		case 6:
			return '每半月'
		case 7:
			return '每半年'
		default:
			return '-'
	}
}

function isFirstHalfOfMonth() {
	const currentDate = new Date()
	const dayOfMonth = currentDate.getDate()
	return dayOfMonth <= 15
}
function isFirstHalfOfYear() {
	const currentDate = new Date()
	const monthOfYear = currentDate.getMonth()
	return monthOfYear < 6
}
const getEndTime = (time: any) => {
	const date = new Date()
	const weekDays = ['一', '二', '三', '四', '五', '六', '日']
	const year = date.getFullYear()
	const month = date.getMonth() + 1
	const day = date.getDate()
	const week = date.getDay() == 0 ? 7 : date.getDay()
	const qtr = month <= 3 ? 1 : month <= 6 ? 2 : month <= 9 ? 3 : 4
	if (time.interval == 2) {
		const key = 'weeklyDeadlineDayOfWeek'
		if (time[key] !== null) {
			return `周${weekDays[time[key] - 1]}`
		} else {
			return '未设置'
		}
	} else if (time.interval == 3) {
		const key = 'monthlyDeadlineDay'

		if (time[key]) {
			return `${time[key]}日`
		} else {
			return '未设置'
		}
	} else if (time.interval == 4) {
		const key = 'quarterlyDeadlineDay'

		if (time[key + qtr] !== null) {
			const text = new Date(time[key + qtr].substring(4).replace(/./, year + '-'))
			return `${text.getMonth() + 1}月${text.getDate()}日`
		} else {
			return '未设置'
		}
	} else if (time.interval == 5) {
		const key = 'yearlyDeadlineDay'

		if (time[key] !== null) {
			const text = new Date(time[key].substring(4).replace(/./, year + '-'))
			return `${text.getMonth() + 1}月${text.getDate()}日`
		} else {
			return '未设置'
		}
	} else if (time.interval == 6) {
		if (isFirstHalfOfMonth()) {
			// 上半个月
			const key = 'downHalfMonthReminderTime'

			if (time[key]) {
				return `${time[key]}日`
			} else {
				return '未设置'
			}
		} else {
			const key = 'downHalfMonthDateOnlyTime'

			if (time[key]) {
				return `${time[key]}日`
			} else {
				return '未设置'
			}
		}
	} else if (time.interval == 7) {
		if (isFirstHalfOfYear()) {
			const key = 'upHalfYearDateOnlyTime'

			if (time[key] !== null) {
				const text = new Date(time[key].substring(4).replace(/./, year + '-'))
				return `${text.getMonth() + 1}月${text.getDate()}日`
			} else {
				return '未设置'
			}
		} else {
			const key = 'downHalfYearDateOnlyTime'

			if (time[key] !== null) {
				const text = new Date(time[key].substring(4).replace(/./, year + '-'))
				return `${text.getMonth() + 1}月${text.getDate()}日`
			} else {
				return '未设置'
			}
		}
	} else if (time.interval == 0) {
		// 不提醒
		return '-'
	}
}

const getBeginTime = (time: any) => {
	const date = new Date()
	const month = date.getMonth() + 1
	const qtr = month <= 3 ? 1 : month <= 6 ? 2 : month <= 9 ? 3 : 4
	const year = date.getFullYear()
	switch (time.interval) {
		case 0:
			return '-'
		case 2:
			let daysOfWeekFull = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
			return daysOfWeekFull[time.weeklyStartDayOfWeek - 1]
		case 3:
			return time.monthlyStartDay + '日'
		case 4:
			const key = 'quarterlyStartDay'
			const text = new Date(time[key + qtr].substring(4).replace(/./, year + '-'))
			return `${text.getMonth() + 1}月${text.getDate()}日`
		case 5:
			const timeDay = new Date(time['yearlyStartDay'].substring(4).replace(/./, year + '-'))
			return `${timeDay.getMonth() + 1}月${timeDay.getDate()}日`
		case 6:
			if (isFirstHalfOfMonth()) return time.upHalfMonthStartTime + '日'
			else return time.downHalfMonthStartTime + '日'
		case 7:
			const yearDay = new Date(
				time['upHalfYearStartTime'].substring(4).replace(/./, year + '-')
			)
			const yearDay1 = new Date(
				time['downHalfYearStartTime'].substring(4).replace(/./, year + '-')
			)
			if (isFirstHalfOfYear()) return `${yearDay.getMonth() + 1}月${yearDay.getDate()}日`
			else return `${yearDay1.getMonth() + 1}月${yearDay1.getDate()}日`
	}
}

const formItems = [
	{prop: 'interval', label: '业务表填报周期'},
	{
		prop: 'beginTime',
		label: '每周期填报开始时间',
	},
	{
		prop: 'endTime',
		label: '每周期填报结束时间',
	},
]
const displayForm = ref({
	interval: '',
	beginTime: '',
	endTime: '32',
	id: '',
	isOnline: false,
	name: '',
})
const onChangeSwich = (val: any, rowData: any) => {
	if (val) {
		console.log(rowData, 'cesss')
		rowData.isOnline = !val
		openLedgerOnlineModal.value = true
		displayForm.value = {
			interval: getReadCycle(rowData.reminderConfig?.interval),
			beginTime: getBeginTime(rowData.reminderConfig) as string,
			endTime: getEndTime(rowData.reminderConfig) as string,
			id: rowData.id,
			isOnline: rowData.isOnline,
			name: rowData.name,
		}
	} else {
		updateLedgerOnline(rowData.id, rowData.isOnline).then((res: any) => {
			// rowData.isOnline ? `${rowData.name}上线成功` :
			ElMessage.success(`${rowData.name}下线成功`)
			getLedgerData()
		})
	}
}
const onConfirmLedgerOnline = () => {
	updateLedgerOnline(displayForm.value.id, true, startImmediately.value).then((res: any) => {
		ElMessage.success(`${displayForm.value.name}上线成功`)
		openLedgerOnlineModal.value = false
		startImmediately.value = true
		getLedgerData()
	})
}
const tableOffsetHeight = ref(-165)
const expendSearch = (height: number, expand: boolean) => {
	tableOffsetHeight.value = -(height + 165)
	setTimeout(() => {
		tableRef.value.resize()
	}, 151)
}
const handleClick = (type: any) => {
	searchForm.LedgerTypeId = null
	currentLedgerType.value = type.index - 1
	if (type.props.name === '所有业务表') {
		colDataTable.value = colData
	} else {
		colDataTable.value = colData.filter((item: any) => item.title !== '所属板块')
	}

	pageation.currentPage = 1
	pageation.pageSize = 10
	pageation.total = 0

	getLedgerData()
}
const closeMoadl = () => {
	ruleFormLedgerTemplate.value.displayName = ''
	ruleFormLedgerTemplate.value.remark = ''

	isCreateLedgerTemplate.value = false
}
// 判断编辑或者是添加
const isshowData = ref(false)
// 编辑需要的id
const editFill = ref('')
// 当前业务表数据
const currentTable = ref({})
// 当前业务表表头
const currentList = ref([])
// 需要填充业务表数据
const fillTable = ref({})
// 需要填充业务表表头
const fillList = ref([])
// 当选定数据源发生变化时
async function fillIntheBusinessTable(e: any, index: number) {
	const res = await getLedgerList('', 0, 1, e[1])
	const {data} = res
	fillTable.value = data
	fillList.value = data.tableInfo.fields
	configs.value = [
		{
			fieldId: '',
			sourceFiledId: '',
		},
	]
}
const getFillList = () => {
	if (!fillList.value.length) {
		return ElMessage.warning('请先选需要填充的业务表')
	}
}
const configs = ref([
	{
		fieldId: '',
		sourceFiledId: '',
	},
])
const deleteConfigs = (index: any) => {
	configs.value.splice(index, 1)
}
const addConfigs = () => {
	if (configs.value.length !== 0) {
		if (
			configs.value[configs.value.length - 1].fieldId == '' ||
			configs.value[configs.value.length - 1].sourceFiledId == ''
		) {
			return ElMessage.warning('请完成上一条配置在进行添加!')
		}
	}
	configs.value.push({
		fieldId: '',
		sourceFiledId: '',
	})
}
// 添加或者编辑同步
const onSynchronous = () => {
	if (configs.value.length == 0) {
		return ElMessage.warning('请完成对应关系!')
	}
	if (
		configs.value[configs.value.length - 1].fieldId == '' ||
		configs.value[configs.value.length - 1].sourceFiledId == ''
	) {
		return ElMessage.warning('请完成对应关系配置!')
	}
	const data = {
		ledgerId: fillTable.value.id,
		sourceLedgerId: currentTable.value.id,
		configs: configs.value,
	}
	if (isshowData.value) {
		putAssociatedSyn(editFill.value, data.sourceLedgerId, data).then((res) => {
			ElMessage.success('编辑关联同步成功！')
			isSynchronous.value = false
			isshowData.value = false
			sourceLedgerId.value = null
			configs.value = [
				{
					fieldId: '',
					sourceFiledId: '',
				},
			]
		})
	} else {
		associatedSyn(data).then((res) => {
			ElMessage.success('关联同步成功！')
			isSynchronous.value = false
			sourceLedgerId.value = null
			configs.value = [
				{
					fieldId: '',
					sourceFiledId: '',
				},
			]
		})
	}
}
const saveData = ref()
const closeType = () => {
	runway.value = ''
	newLedgerType.value = ''
	openChangeLedgerType.value = false
}
const closeName = () => {
	newLedgerName.value = ''
	openChangeLedgerName.value = false
}
const routeTo = () => {
	router.push('/ledgerType/type')
}
const routeTo1 = () => {
	router.push('/ledgerTemplate')
}
</script>
<route>
	{
		meta: {
			title:'业务表可视化创建',
			ignoreLabel:false
		}
	}
</route>
<template>
	<div class="authority">
		<Block
			:enable-expand-content="true"
			:enableBackButton="false"
			:enable-fixed-height="true"
			@content-expand="expendSearch"
		>
			<template #title>
				<!-- <el-button
						:type="currentLedgerType === -1 ? 'primary' : ''"
						size="small"
						@click="onChangeRunway(-1)"
					>
						所有业务表
					</el-button>
					<el-button
						:type="currentLedgerType === index ? 'primary' : ''"
						v-for="(item, index) of RunwayList"
						size="small"
						@click="onChangeRunway(index)"
					>
						{{ item.label }}
						({{ runwayCountList.find((f: any) => f.runway === item.name)?.count ?? 0 }})
					</el-button> -->

				<el-tabs
					v-model="activeName"
					class="tabs-ui"
					@tab-click="handleClick"
					style="width: 100%"
				>
					<!-- ${runwayCountList.map((v) => v.count).reduce((a, b) => a + b, 0)} -->
					<el-tab-pane
						:label="`所有业务表 (${allCount})`"
						name="所有业务表"
					></el-tab-pane>
					<el-tab-pane
						v-for="(item, index) of RunwayList.filter((f: any) => f.index !== 0)"
						:label="`${item.label} (${ runwayCountList.find((f: any) => f.runway === item.name)?.count ?? 0})`"
						:name="item.label"
					></el-tab-pane>
				</el-tabs>
			</template>
			<template #topRight>
				<el-button
					type="primary"
					size="small"
					@click="onClickLedger('add')"
					style="margin-left: 10px"
				>
					<i class="icon i-ic-outline-add-card" style="margin-right: 3px !important"></i>
					新建业务表
				</el-button>

				<el-button type="primary" size="small" style="margin-left: 10px" @click="routeTo">
					业务表类型管理
				</el-button>

				<el-button type="primary" size="small" style="margin-left: 10px" @click="routeTo1">
					业务表模版管理
				</el-button>

				<el-button
					type="primary"
					plain
					size="small"
					style="margin-left: 10px"
					@click="onBatchData(true)"
				>
					批量上线
				</el-button>

				<el-button
					type="danger"
					plain
					size="small"
					style="margin-left: 10px"
					@click="onBatchData(false)"
				>
					批量下线
				</el-button>

				<el-button
					type="danger"
					plain
					size="small"
					style="margin-left: 10px"
					@click="onBatchData(null)"
				>
					批量删除
				</el-button>
			</template>
			<template #expand>
				<div class="search-box" v-action:enter="getLedgerDataLists">
					<el-select
						v-model="searchForm.IsOnline"
						filterable
						clearable
						@change="getLedgerDataLists('search')"
						placeholder="是否已上线"
						size="default"
						class="value"
					>
						<el-option label="是" :value="true" />
						<el-option label="否" :value="false" />
					</el-select>
					<el-select
						class="value"
						v-model="searchForm.LedgerTypeId"
						filterable
						@change="getLedgerDataLists('search')"
						clearable
						placeholder="请选择发布部门"
						size="default"
					>
						<el-option
							v-for="item in publishLedgerDepartment"
							:key="item.id"
							:label="item.name"
							:value="item.id"
						/>
					</el-select>
					<el-select
						v-model="searchForm.ReminderInterval"
						filterable
						clearable
						@change="getLedgerDataLists('search')"
						placeholder="请选择更新周期"
						size="default"
						class="value"
					>
						<el-option
							v-for="item of [
								{label: '每周', value: 2},
								{label: '每月', value: 3},
								{label: '每季度', value: 4},
								{label: '每年', value: 5},
								{label: '每半月', value: 6},
								{label: '每半年', value: 7},
								{label: '不提醒', value: 0},
							]"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
					<el-input
						class="value"
						v-model="searchForm.value"
						placeholder="请输入业务表名称"
						size="default"
						clearable
					></el-input>

					<el-button type="default" size="default" @click="getLedgerDataLists('empty')">
						<i class="icon i-ic-outline-cleaning-services" mr-3px></i>
						清空
					</el-button>
					<el-button
						type="primary"
						size="default"
						:loading="loading"
						@click="getLedgerDataLists('search')"
					>
						<i class="i-ic-baseline-send" mr-3px></i>
						查询
					</el-button>
				</div>
			</template>
			<BaseTableComp
				class="ledger-table"
				ref="tableRef"
				:checkbox="true"
				:colData="colDataTable"
				:data="tableData"
				:offsetHeight="tableOffsetHeight"
				:buttons="buttons"
				:visibleHeader="false"
				:visibleSetting="false"
				:currentPage="pageation.currentPage"
				:pageSize="pageation.pageSize"
				:pageSizeArray="pageation.pageSizeArray"
				:total="pageation.total"
				:loading="loading"
				@clickButton="onTableClickButton"
				@size-change="onPageationChange($event, 'size')"
				@current-change="onPageationChange($event, 'current')"
				@sortable-change="onSortableChange"
			>
				<template #createDepartment="{rowData}">
					<!-- <span v-if="!rowData.regionDepartment || !rowData.regionDepartment.department">-</span> -->
					<!-- <div v-else style="white-space: nowrap; text-overflow: ellipsis; overflow: hidden">
						{{ rowData.regionDepartment.department[2] }}-{{
							rowData.regionDepartment.department[rowData.regionDepartment.department.length - 2]
						}}-{{
							rowData.regionDepartment.department[rowData.regionDepartment.department.length - 1]
						}}
					</div> -->
					{{ rowData.createDepartment?.regionName }}-
					{{ rowData.createDepartment?.extendName }}-
					{{ rowData.createDepartment?.name }}
				</template>

				<template #ledgerType="{rowData}">
					{{ rowData.ledgerType?.regionName }}-{{ rowData.ledgerType?.name }}
				</template>
				<template #name="{rowData}">
					<span
						class="link-click"
						@click="
							router.push({
								path: '/ledgerConfig/info',
								query: {id: rowData.id, type: 'view', name: rowData.name},
							})
						"
					>
						{{ rowData.name }}
					</span>
				</template>
				<template #reminderConfig="{rowData}">
					{{
						rowData.reminderConfig?.interval == 2
							? '每周'
							: rowData.reminderConfig?.interval == 3
							? '每月'
							: rowData.reminderConfig?.interval == 4
							? '每季度'
							: rowData.reminderConfig?.interval == 5
							? '每年'
							: rowData.reminderConfig?.interval == 6
							? '每半月'
							: rowData.reminderConfig?.interval == 7
							? '每半年'
							: '不提醒'
					}}
				</template>
				<template #isOnline="{rowData}">
					<el-switch
						v-model="rowData.isOnline"
						:disabled="
							!currentUser?.baseRoles.includes(USER_BASE_ROLES_ENUM.LEDGER_ONLINE)
						"
						inline-prompt
						@change="onChangeSwich($event, rowData)"
						style="--el-switch-on-color: var(--z-main); --el-switch-off-color: red"
						active-text="上线"
						inactive-text="下线"
					/>
				</template>
				<!-- <template #buttons="scope">
				<el-switch class="ml-2" v-model="scope.row.row.isOnline" inline-prompt
					@change="onChangeSwich($event, scope)"
					style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949 ; margin-right:12px"
					active-text="上线" inactive-text="下线" />
			</template> -->
			</BaseTableComp>
		</Block>
		<Dialog
			v-model="isSynchronous"
			title="关联同步配置"
			width="800"
			:destory-on-close="true"
			@clickConfirm="onSynchronous"
			@close="isSynchronous = false"
			@open="
				() => {
					configs = []
					sourceLedgerId = null
				}
			"
		>
			<div class="synchronous">
				<div class="synchronous-label">
					<div class="label">当前业务表为:</div>
					<div class="value">{{ currentTable?.name }}</div>
				</div>
				<div class="synchronous-label">
					<div class="label">需要填充的业务表:</div>
					<div class="value">
						<el-cascader
							v-model="sourceLedgerId"
							:show-all-levels="false"
							filterable
							:options="sourceLedgerData"
							style="width: 220px"
							@change="fillIntheBusinessTable($event, index)"
						></el-cascader>
					</div>
				</div>
				<div class="synchronous-label">
					<div class="label">数据绑定</div>
					<div class="value">
						<el-radio-group v-model="radio2" size="small">
							<el-radio-button label="业务表数据" value="业务表数据" />
						</el-radio-group>
					</div>
				</div>
				<div class="synchronous-label" v-if="fillList.length">
					<div class="mark">
						<p>当前业务表</p>
						<span></span>
						<p>需填充业务表</p>
					</div>
				</div>
				<div
					class="synchronous-label"
					v-for="(item, index) in configs"
					:key="index"
					v-if="fillList.length"
				>
					<div class="association">
						<el-select
							clearable
							v-model="item.sourceFiledId"
							placeholder="请选择字段名"
							style="width: 304px"
						>
							<el-option
								v-for="item of currentList"
								:key="item.name"
								:label="item.displayName"
								:value="item.id"
								:disabled="configs.some((f:any) => f.sourceFiledId === item.id)"
							/>
						</el-select>
						<img src="@/assets/image/analysis4.png" alt="" />
						<el-select
							clearable
							v-model="item.fieldId"
							placeholder="请选择字段名"
							style="width: 304px"
						>
							<el-option
								v-for="item of fillList"
								:key="item.name"
								:label="item.displayName"
								:value="item.id"
								:disabled="configs.some((f:any) => f.fieldId === item.id)"
							/>
						</el-select>
						<el-icon
							style="margin-left: 10px; color: red; cursor: pointer"
							@click="deleteConfigs(index)"
						>
							<Delete />
						</el-icon>
					</div>
				</div>
				<div class="synchronous-label" v-if="fillList.length">
					<p class="button" @click="addConfigs">添加对应关系</p>
				</div>
			</div>
		</Dialog>
		<Dialog
			v-model="openChangeLedgerName"
			title="更改业务表名称"
			width="600"
			@clickConfirm="onConfirmChangeLedgerName"
			@close="closeName"
		>
			<div class="custom-form">
				<div class="item">
					<div class="label">业务表原名称:</div>
					<div class="value">{{ currentDialogRow.name }}</div>
				</div>
				<div class="item">
					<div class="label">更新后业务表名称:</div>
					<div class="value">
						<el-input
							v-model="newLedgerName"
							placeholder="请输入新业务表名称"
						></el-input>
					</div>
				</div>
			</div>
		</Dialog>

		<Dialog
			v-model="openChangeLedgerType"
			title="更改业务表类型"
			width="600"
			@clickConfirm="onConfirmChangeLedgerType"
			@close="closeType"
			style="overflow: visible"
		>
			<div class="custom-form">
				<div class="item">
					<div class="label" style="text-align: left; margin-right: 37px">
						原业务表发布部门:
					</div>
					<div class="value">{{ currentDialogRow.ledgerType.name }}</div>
				</div>
				<div class="item">
					<div class="label" style="text-align: left; margin-right: 37px">
						原业务表所属板块:
					</div>
					<div class="value">{{ currentDialogRow.runway }}</div>
				</div>
				<div class="item">
					<div class="label">更新后业务表所属板块:</div>
					<div class="value">
						<el-select clearable v-model="runway" placeholder="请选择所属板块">
							<el-option
								v-for="item of RunwayList"
								:key="item.label"
								:label="item.label"
								:value="item.label"
							/>
						</el-select>
						<!-- <DropdownTree
								:data="dropdwonTreeRunway"
								:single="true"
								@selected="onDropdownTreeSelected"
							></DropdownTree> -->
					</div>
				</div>
				<div class="item">
					<div class="label">更新后业务表所属部门:</div>
					<div class="value">
						<el-select
							clearable
							filterable
							v-model="newLedgerType"
							placeholder="请选择所属部门"
						>
							<el-option
								v-for="item of publishLedgerDepartment"
								:key="item.id"
								:label="item.name"
								:value="item.id"
							/>
						</el-select>
						<!-- <DropdownTree
								:data="dropdwonTreeRunway"
								:single="true"
								@selected="onDropdownTreeSelected"
							></DropdownTree> -->
					</div>
				</div>
			</div>
		</Dialog>

		<Dialog
			v-model="openChangeLedgerTime"
			:title="currentDialogRow.name"
			width="1100"
			@clickConfirm="onConfirmChangeLedgerTime"
			@close="openChangeLedgerTime = false"
			style="overflow: visible"
		>
			<div class="custom-form">
				<div class="item">
					<div class="label timeTitle">业务表更新周期:</div>
					<div class="value">
						{{
							remindConfig.interval == 2
								? '每周更新'
								: remindConfig.interval == 3
								? '每月更新'
								: remindConfig.interval == 4
								? '每季度更新'
								: remindConfig.interval == 5
								? '每年更新'
								: remindConfig.interval == 6
								? '每半月更新'
								: remindConfig.interval == 7
								? '每半年更新'
								: ''
						}}
					</div>
				</div>
				<div class="item flex" v-if="remindConfig.interval == 2">
					<div class="cloum">
						<div class="label">数据填报提醒时间:</div>
						<div class="value">
							<el-select
								v-model="remindConfig.weeklyReminderDayOfWeek"
								placeholder="请选择日期"
								size="small"
							>
								<el-option
									v-for="item in options"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</div>
					</div>
					<div class="cloum">
						<div class="label">数据填报开始时间:</div>
						<div class="value">
							<el-select
								v-model="remindConfig.weeklyStartDayOfWeek"
								placeholder="请选择日期"
								size="small"
							>
								<el-option
									v-for="item in options"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</div>
					</div>
					<div class="cloum">
						<div class="label">数据填报结束时间:</div>
						<div class="value">
							<el-select
								v-model="remindConfig.weeklyDeadlineDayOfWeek"
								placeholder="请选择日期"
								size="small"
							>
								<template v-for="item in options">
									<template
										v-if="item.value > remindConfig.weeklyReminderDayOfWeek"
									>
										<el-option
											:key="item.value"
											:label="item.label"
											:value="item.value"
										/>
									</template>
								</template>
							</el-select>
						</div>
					</div>
				</div>
				<div class="item flex" v-else-if="remindConfig.interval == 3">
					<div class="cloum">
						<div class="label">数据填报提醒时间:</div>
						<div class="value">
							每月
							<el-input-number
								style="width: 70px"
								v-model="remindConfig.monthlyReminderDay"
								:min="1"
								:max="28"
								size="small"
								controls-position="right"
							/>
							日
						</div>
					</div>
					<div class="cloum">
						<div class="label">数据填报开始时间:</div>
						<div class="value">
							每月
							<el-input-number
								style="width: 70px"
								v-model="remindConfig.monthlyStartDay"
								:min="1"
								:max="28"
								size="small"
								controls-position="right"
							/>
							日
						</div>
					</div>
					<div class="cloum">
						<div class="label">数据填报结束时间:</div>
						<div class="value">
							每月
							<el-input-number
								style="width: 70px"
								v-model="remindConfig.monthlyDeadlineDay"
								:min="1"
								:max="28"
								size="small"
								controls-position="right"
							/>
							日
						</div>
					</div>
				</div>
				<div style="width: 100%" v-else-if="remindConfig.interval == 4">
					<div class="item">
						<div class="cloum">
							<div class="label">数据填报提醒时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">数据填报开始时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">数据填报结束时间:</div>
							<div class="value"></div>
						</div>
					</div>

					<div class="item">
						<div class="label">第一季度:</div>

						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyReminderDay1"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyStartDay1"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyDeadlineDay1"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="item">
						<div class="label">第二季度:</div>

						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyReminderDay2"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyStartDay2"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyDeadlineDay2"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="item">
						<div class="label">第三季度:</div>

						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyReminderDay3"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyStartDay3"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyDeadlineDay3"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="item">
						<div class="label">第四季度:</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyReminderDay4"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyStartDay4"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.quarterlyDeadlineDay4"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
				</div>
				<div class="item flex" v-else-if="remindConfig.interval == 5">
					<div class="cloum">
						<div class="label">数据填报提醒时间:</div>
						<div class="value">
							<el-date-picker
								style="width: 180px"
								v-model="remindConfig.yearlyReminderDay"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="cloum">
						<div class="label">数据填报开始时间:</div>
						<div class="value">
							<el-date-picker
								style="width: 180px"
								v-model="remindConfig.yearlyStartDay"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="cloum">
						<div class="label">数据填报结束时间:</div>
						<div class="value">
							<el-date-picker
								style="width: 180px"
								v-model="remindConfig.yearlyDeadlineDay"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
				</div>
				<div style="width: 100%" v-else-if="remindConfig.interval == 6">
					<div class="item">
						<div class="cloum">
							<div class="label">数据填报提醒时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">数据填报开始时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">数据填报结束时间:</div>
							<div class="value"></div>
						</div>
					</div>
					<div class="item">
						<div class="value">
							上半月
							<el-input-number
								style="width: 70px"
								v-model="remindConfig.upHalfMonthReminderTime"
								:min="1"
								:max="15"
								size="small"
								controls-position="right"
							/>
							日
						</div>
						<div class="value">
							上半月
							<el-input-number
								style="width: 70px"
								v-model="remindConfig.upHalfMonthStartTime"
								:min="1"
								:max="15"
								size="small"
								controls-position="right"
							/>
							日
						</div>
						<div class="value">
							上半月
							<el-input-number
								style="width: 70px"
								v-model="remindConfig.upHalfMonthDateOnlyTime"
								:min="1"
								:max="15"
								size="small"
								controls-position="right"
							/>
							日
						</div>
					</div>
					<div class="item">
						<div class="value">
							下半月
							<el-input-number
								style="width: 70px"
								v-model="remindConfig.downHalfMonthReminderTime"
								:min="16"
								:max="30"
								size="small"
								controls-position="right"
							/>
							日
						</div>
						<div class="value">
							下半月
							<el-input-number
								style="width: 70px"
								v-model="remindConfig.downHalfMonthStartTime"
								:min="16"
								:max="30"
								size="small"
								controls-position="right"
							/>
							日
						</div>
						<div class="value">
							下半月
							<el-input-number
								style="width: 70px"
								v-model="remindConfig.downHalfMonthDateOnlyTime"
								:min="16"
								:max="30"
								size="small"
								controls-position="right"
							/>
							日
						</div>
					</div>
				</div>
				<div style="width: 100%" v-else-if="remindConfig.interval == 7">
					<div class="item">
						<div class="cloum">
							<div class="label">数据填报提醒时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">数据填报开始时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">数据填报结束时间:</div>
							<div class="value"></div>
						</div>
					</div>
					<div class="item">
						<div class="label">上半年:</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.upHalfYearReminderTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.upHalfYearStartTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.upHalfYearDateOnlyTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="item">
						<div class="label">下半年:</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.downHalfYearReminderTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.downHalfYearStartTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								v-model="remindConfig.downHalfYearDateOnlyTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
				</div>
			</div>
		</Dialog>

		<Dialog
			v-model="openUploadDialog"
			:title="`${currentDialogRow.name} - 上传模版`"
			@close="openUploadDialog = false"
			@clickConfirm="onPushUpload"
		>
			<el-upload
				ref="uploadRef"
				class="upload-template"
				accept=".xlsx"
				method="put"
				drag
				:action="uploadUrl"
				:auto-upload="false"
				:on-change="onUploadTemplate"
				:on-success="onUploadSuccess"
			>
				<el-icon class="el-icon--upload"><upload-filled /></el-icon>
				<div class="el-upload__text">拖拽或<em>点击选择文件</em>(支持扩展名：.xlsx)</div>
			</el-upload>
		</Dialog>
		<!-- 业务表辅助填报弹窗 -->
		<Dialog
			v-model="openAuxilifillingDialog"
			title="设置辅助填报"
			width="800"
			@clickConfirm="onAuxilifillingConfirm"
			@close="openAuxilifillingDialog = false"
			class="auxilifilling-dialog"
		>
			<div style="height: 400px; overflow-y: auto">
				<div class="pd-left-10 mg-bottom-10">
					<!-- <el-button type="primary" :icon="CirclePlus" @click="addAuxilifilling"
						>新增辅助填报</el-button
					> -->
				</div>
				<el-collapse v-model="activeNames" accordion>
					<el-collapse-item
						style="padding: 0 20px"
						v-for="(item, index) in auxiliFillingData"
						:name="item.name ? item.name : `辅助填报${index + 1}`"
					>
						<template #title>
							<div style="width: 100%; display: flex; justify-content: space-between">
								<div>{{ item.name ? item.name : `辅助填报${index + 1}` }}</div>
								<div class="mg-right-20">
									<!-- <el-button
										size="small"
										type="danger"
										:icon="Delete"
										@click="removeAuxilifillingItem($event, item, index)"
										>删除</el-button
									> -->
								</div>
							</div>
						</template>
						<div class="form-item">
							<div class="name isRequire">辅助填报类型：</div>
							<el-radio-group
								:disabled="disableExchangeFillingType"
								v-model="item.type"
								@change="AuxilifillingChange(index, $event)"
							>
								<el-radio :value="1">业务表</el-radio>
								<el-radio :value="2">基础库</el-radio>
								<el-radio :value="3">数据集</el-radio>
							</el-radio-group>
						</div>
						<template v-if="item.type === 1">
							<div class="form-item">
								<div class="name isRequire">辅助填报配置名称：</div>
								<el-input
									v-model="item.name"
									placeholder="请输入"
									style="width: 220px; margin-right: 10px"
								></el-input>

								<el-button
									type="primary"
									v-if="item.id"
									:icon="View"
									@click="preview"
									>预览</el-button
								>
							</div>
							<div class="form-item">
								<div class="name isRequire">数据源业务表：</div>
								<el-cascader
									:disabled="item.id"
									v-model="item.sourceLedgerId"
									:show-all-levels="false"
									filterable
									:options="sourceLedgerData"
									style="width: 220px"
									@change="sourceLedgerChange($event, index)"
								></el-cascader>
							</div>
							<div class="form-item">
								<div class="name isRequire">查询字段：</div>
								<el-select
									placeholder="请选择"
									v-model="item.fieldId"
									style="width: 220px"
								>
									<!-- 新需求 非唯一键也可成为查询字段 -->
									<!-- v-for="field in currentLedgerFieldList.filter((v) => v.isUnique)" -->
									<el-option
										v-for="field in currentLedgerFieldList"
										:label="field.displayName"
										:value="field.id"
									></el-option>
								</el-select>
							</div>
							<div
								style="
									display: flex;
									width: 100%;
									padding-left: 20px;
									padding-top: 10px;
								"
							>
								<div
									style="width: 130px; text-align: end; margin-top: 5px"
									class="isRequire"
								>
									数据绑定：
								</div>
								<div class="data-bind-list">
									<div
										class="data-bind-item"
										v-for="(iitem, index) in item.configs"
									>
										<div class="data-bind-item-value" flex items-center>
											<el-select
												placeholder="业务表数据源字段"
												v-model="iitem.sourceFiledId"
												style="width: 220px"
												@change="sourceFiledSelectChange"
												clearable
											>
												<template v-if="index === 0">
													<el-option
														v-for="field in item.sourceLedgerFieldList.filter(
															(v) =>
																v.name !== 'Informant' &&
																v.name !== 'DataSource' &&
																v.name !== 'Editor' &&
																v.name !== 'UpdateTime'
														)"
														:disabled="
															item.configs.filter(
																(v) => v.sourceFiledId === field.id
															).length > 0
														"
														:label="field.displayName"
														:value="field.id"
													></el-option>
												</template>
												<template v-else>
													<el-option
														v-for="field in item.sourceLedgerFieldList.filter(
															(v) =>
																v.name !== 'Informant' &&
																v.name !== 'DataSource' &&
																v.name !== 'Editor' &&
																v.name !== 'UpdateTime'
														)"
														:disabled="
															item.configs.filter(
																(v) => v.sourceFiledId === field.id
															).length > 0
														"
														:label="field.displayName"
														:value="field.id"
													></el-option>
												</template>
											</el-select>
											<span style="margin: 0 10px">-</span>
											<el-select
												placeholder="目标业务表字段"
												v-model="iitem.fieldId"
												style="width: 220px; margin-right: 10px"
												clearable
											>
												<el-option
													v-for="field in iitem.targetLedgerFieldList.filter(
														(v) =>
															v.name !== 'Informant' &&
															v.name !== 'DataSource' &&
															v.name !== 'Editor' &&
															v.name !== 'UpdateTime'
													)"
													:disabled="
														item.configs.filter(
															(v) => v.fieldId === field.id
														).length > 0
													"
													:label="field.displayName"
													:value="field.id"
												></el-option>
											</el-select>
										</div>
										<el-button
											type="danger"
											:icon="Delete"
											@click="removeConfig(item.configs, index)"
											>删除</el-button
										>
									</div>
									<div class="data-bind-item">
										<el-button
											type="primary"
											style="width: 100%; margin-top: 10px"
											@click="addDataConfig(item)"
											>添加数据绑定</el-button
										>
									</div>
								</div>
							</div>
							<div class="form-item">
								<div class="name isRequire">预览字段：</div>
								<!-- {{ item.configs }} -->
								<el-select
									placeholder="下拉多选,已绑定的结果返回字段,作为填报时的展示字段,最多3个"
									v-model="item.previewFields"
									style="width: 100%"
									multiple
									:multiple-limit="3"
								>
									<el-option
										v-for="(iitem, index) in item.configs.filter(
											(x) => x.sourceFiledId !== null
										)"
										:label="
											item.sourceLedgerFieldList.filter(
												(v) => v.id === iitem.sourceFiledId
											)[0]?.displayName
										"
										:value="iitem.sourceFiledId"
										multiple
									></el-option>
								</el-select>
							</div>
						</template>
						<template v-else-if="item.type === 2">
							<div class="form-item">
								<div class="name isRequire">辅助填报配置名称：</div>
								<el-input
									v-model="item.name"
									placeholder="请输入"
									style="width: 220px; margin-right: 10px"
								></el-input>

								<el-button
									type="primary"
									v-if="item.id"
									:icon="View"
									@click="preview"
									>预览</el-button
								>
							</div>
							<!-- <div class="form-item">
								<div class="name isRequire">数据源类型：</div>
								232323
							</div> -->
							<div class="form-item">
								<div class="name isRequire">数据源名称：</div>
								<el-select
									placeholder="请选择"
									v-model="item.apiId"
									style="width: 220px; margin-right: 10px"
									@change="apiChange"
									clearable
								>
									<!-- 何雨陶-----固定死label和value -->
									<el-option
										v-for="apiItem in apilist"
										:label="apiItem.name"
										:value="apiItem.id"
									></el-option>
								</el-select>
							</div>
							<div class="form-item">
								<div class="name isRequire">查询字段：</div>
								<el-select
									placeholder="请选择"
									v-model="item.fieldId"
									style="width: 220px"
								>
									<!-- v-for="field in currentLedgerFieldList.filter((v) => v.isUnique)" -->
									<el-option
										v-for="field in currentLedgerFieldList"
										:label="field.displayName"
										:value="field.id"
									></el-option>
								</el-select>
							</div>
							<div
								style="
									display: flex;
									width: 100%;
									padding-left: 20px;
									padding-top: 10px;
								"
							>
								<div
									style="width: 130px; text-align: end; margin-top: 5px"
									class="isRequire"
								>
									数据绑定：
								</div>
								<div class="data-bind-list">
									<div
										class="data-bind-item"
										v-for="(iitem, index) in item.configs"
									>
										<div
											class="data-bind-item-value"
											style="display: flex; align-items: center"
										>
											<el-select
												placeholder="业务表数据源字段"
												v-model="iitem.sourceFiledId"
												style="width: 220px"
												@change="sourceFiledSelectChange"
												clearable
											>
												<!-- <template v-if="index === 0"> -->
												<el-option
													v-for="field in item.sourceLedgerFieldList"
													:disabled="
														item.configs.filter(
															(v) => v.sourceFiledId === field.id
														).length > 0
													"
													:label="field.displayName"
													:value="field.id"
												></el-option>
												<!-- </template> -->
												<!-- <template v-else>
													<el-option
														v-for="field in item.sourceLedgerFieldList"
														:disabled="
															item.configs.filter((v) => v.sourceFiledId === field.id).length > 0
														"
														:label="field.displayName"
														:value="field.id"
													></el-option>
												</template> -->
											</el-select>
											<span style="margin: 0 10px">-</span>
											<el-select
												placeholder="目标业务表字段"
												v-model="iitem.fieldId"
												style="width: 220px; margin-right: 10px"
												clearable
											>
												<el-option
													v-for="field in iitem.targetLedgerFieldList.filter(
														(v) =>
															v.name !== 'Informant' &&
															v.name !== 'DataSource' &&
															v.name !== 'Editor' &&
															v.name !== 'UpdateTime'
													)"
													:disabled="
														item.configs.filter(
															(v) => v.fieldId === field.name
														).length > 0
													"
													:label="field.displayName"
													:value="field.name"
												></el-option>
											</el-select>
										</div>
										<el-button
											type="danger"
											:icon="Delete"
											@click="removeConfig(item.configs, index)"
											>删除</el-button
										>
									</div>
									<div class="data-bind-item">
										<el-button
											type="primary"
											style="width: 100%; margin-top: 10px"
											@click="addDataConfig(item)"
											>添加数据绑定</el-button
										>
									</div>
								</div>
							</div>
							<div class="form-item">
								<div class="name isRequire">预览字段：</div>
								<!-- {{ item.configs }} -->
								<el-select
									placeholder="下拉多选,已绑定的结果返回字段,作为填报时的展示字段,最多3个"
									v-model="item.previewFields"
									style="width: 100%"
									multiple
									:multiple-limit="3"
								>
									<el-option
										v-for="(iitem, index) in item.configs.filter(
											(x) => x.sourceFiledId !== null
										)"
										:label="
											item.sourceLedgerFieldList.filter(
												(v) => v.id === iitem.sourceFiledId
											)[0]?.displayName
										"
										:value="iitem.sourceFiledId"
										multiple
									></el-option>
								</el-select>
							</div>
						</template>

						<template v-else>
							<div class="form-item">
								<div class="name isRequire">辅助填报配置名称：</div>
								<el-input
									v-model="item.name"
									placeholder="请输入"
									style="width: 220px; margin-right: 10px"
								></el-input>

								<el-button
									type="primary"
									v-if="item.id"
									:icon="View"
									@click="preview"
									>预览</el-button
								>
							</div>
							<!-- <div class="form-item">
								<div class="name isRequire">数据源类型：</div>
								232323
							</div> -->
							<div class="form-item">
								<div class="name isRequire">数据集名称：</div>
								<el-select
									placeholder="请选择"
									filterable
									v-model="item.apiId"
									style="width: 220px; margin-right: 10px"
									@change="dataSetChange"
									clearable
								>
									<!-- 何雨陶-----固定死label和value -->
									<el-option
										v-for="apiItem in dataSetList"
										:label="apiItem.name"
										:value="apiItem.id"
									></el-option>
								</el-select>
							</div>
							<div class="form-item">
								<div class="name isRequire">查询字段：</div>
								<el-select
									placeholder="请选择"
									v-model="item.fieldId"
									@change="changeField(item)"
									style="width: 220px"
								>
									<!-- v-for="field in currentLedgerFieldList.filter((v) => v.isUnique)" -->
									<el-option
										v-for="field in item.sourceLedgerFieldList"
										:label="field.displayName"
										:value="field.id"
									></el-option>
								</el-select>
							</div>
							<div
								style="
									display: flex;
									width: 100%;
									padding-left: 20px;
									padding-top: 10px;
								"
							>
								<div
									style="width: 130px; text-align: end; margin-top: 5px"
									class="isRequire"
								>
									数据绑定：
								</div>
								<div class="data-bind-list">
									<div
										class="data-bind-item"
										v-for="(iitem, index) in item.configs"
									>
										<div
											class="data-bind-item-value"
											style="display: flex; align-items: center"
										>
											<el-select
												placeholder="业务表数据源字段"
												v-model="iitem.sourceFiledId"
												style="width: 220px"
												@change="sourceFiledSelectChange"
												clearable
											>
												<!-- <template v-if="index === 0"> -->
												<el-option
													v-for="field in item.sourceLedgerFieldList"
													:disabled="
														item.configs.filter(
															(v) => v.sourceFiledId === field.id
														).length > 0
													"
													:label="field.displayName"
													:value="field.id"
												></el-option>
												<!-- </template> -->
												<!-- <template v-else>
													<el-option
														v-for="field in item.sourceLedgerFieldList"
														:disabled="
															item.configs.filter((v) => v.sourceFiledId === field.id).length > 0
														"
														:label="field.displayName"
														:value="field.id"
													></el-option>
												</template> -->
											</el-select>
											<span m="0 10px" style="margin: 0 10px">-</span>
											<el-select
												placeholder="目标业务表字段"
												v-model="iitem.fieldId"
												style="width: 220px; margin-right: 10px"
												clearable
											>
												<el-option
													v-for="field in iitem.targetLedgerFieldList.filter(
														(v) =>
															v.name !== 'Informant' &&
															v.name !== 'DataSource' &&
															v.name !== 'Editor' &&
															v.name !== 'UpdateTime'
													)"
													:disabled="
														item.configs.filter(
															(v) => v.fieldId === field.id
														).length > 0
													"
													:label="field.displayName"
													:value="field.id"
												></el-option>
											</el-select>
										</div>
										<el-button
											type="danger"
											:icon="Delete"
											@click="removeConfig(item.configs, index)"
											>删除</el-button
										>
									</div>
									<div class="data-bind-item">
										<el-button
											type="primary"
											style="width: 100%; margin-top: 10px"
											@click="addDataConfig(item)"
											>添加数据绑定</el-button
										>
									</div>
								</div>
							</div>
							<div class="form-item">
								<div class="name isRequire">预览字段：</div>
								<!-- {{ item.configs }} -->
								<el-select
									placeholder="下拉多选,已绑定的结果返回字段,作为填报时的展示字段,最多3个"
									v-model="item.previewFields"
									style="width: 100%"
									multiple
									:multiple-limit="3"
								>
									<el-option
										v-for="(iitem, index) in item.configs.filter(
											(x) => x.sourceFiledId !== null
										)"
										:label="
											item.sourceLedgerFieldList.filter(
												(v) => v.id === iitem.sourceFiledId
											)[0]?.displayName
										"
										:value="iitem.sourceFiledId"
										multiple
									></el-option>
								</el-select>
							</div>
						</template>
					</el-collapse-item>
				</el-collapse>
			</div>
		</Dialog>

		<!-- 预览弹窗 -->
		<Dialog
			width="600"
			:title="'预览'"
			v-model="previewModel"
			:enableButton="false"
			@close=";(previewModel = false), (showAddLedgerDataModelisVisible = false)"
			@clickConfirm=""
		>
			<collapseForm
				v-if="showAddLedgerDataModelisVisible"
				ref="addFormRef"
				label-position="right"
				label-width="80"
				submitIcon="i-ic-twotone-published-with-changes"
				:formGroupArray="handleData"
				:fields="tableFiled"
				:form="formArray"
				:rules="formRules"
				:ledgerAuxiliaryFillingList="ledgerAuxiliaryFillingList"
				:showButton="false"
				@onBlur="onBlur"
			>
			</collapseForm>
		</Dialog>
		<Dialog
			v-model="isCreateLedgerTemplate"
			title="创建业务表模板"
			width="600"
			@clickConfirm="submitForm"
			@close="closeMoadl"
			style="overflow: visible"
		>
			<el-form
				label-width="120px"
				class="demo-ruleForm"
				ref="ruleFormRef"
				:model="ruleFormLedgerTemplate"
				:rules="rulesLedgerTemplate"
				status-icon
			>
				<el-form-item label="模板名称" prop="displayName">
					<el-input v-model="ruleFormLedgerTemplate.displayName" />
				</el-form-item>
				<el-form-item label="说明" prop="remark">
					<el-input
						v-model="ruleFormLedgerTemplate.remark"
						type="textarea"
						:autosize="{minRows: 4}"
					/>
				</el-form-item>
			</el-form>
		</Dialog>
		<!-- 业务表上线 -设置任务启用类型 -->
		<Dialog
			v-model="openLedgerOnlineModal"
			title="业务表上线"
			@clickConfirm="onConfirmLedgerOnline"
			@close="openLedgerOnlineModal = false"
		>
			<Form
				v-model="displayForm"
				:data="formItems"
				:labelWidth="160"
				:grid="true"
				:column-count="3"
				:enable-reset="false"
				:enable-button="false"
				mb-10px
			></Form>

			<div class="modal-value">
				<div mr-10px>是否上线直接创建填报任务：</div>
				<div>
					<el-radio-group v-model="startImmediately">
						<el-radio :value="true" size="large">创建任务</el-radio>
						<el-radio :value="false" size="large">下周期开始创建</el-radio>
					</el-radio-group>
				</div>
			</div>
			<div class="tips">
				<el-icon text="#3063c7"><InfoFilled /></el-icon>

				<span style="font-size: 12px; display: inline-block; margin-left: 10px"
					>业务表上线后不支持修改配置，请确认业务表配置准确性。</span
				>
			</div>
		</Dialog>
	</div>
</template>
<style scoped lang="scss">
.flex {
	display: flex;
	align-items: center;
}
.authority {
	// display: flex;
	height: 100%;
	overflow: hidden;
	width: 100%;
	.tags {
		padding: 1px 20px;
		border-radius: 4px;

		color: #ffffff;
	}
	.creationTime {
		div {
			margin-left: 5px;
		}
	}
	.tags-bg1 {
		background-color: #7ed993;
	}
	.tags-bg2 {
		background-color: #bebebe;
	}
	:deep(.el-date-editor) {
		width: 260px;
		flex-grow: 0;
		margin-right: 10px;
	}
	:deep(.el-range-input) {
		width: 100px;
	}
	:deep(.el-range-separator) {
		flex: none;
	}
}

.header-layout {
	flex: 1;
	display: flex;
	justify-content: flex-end;
}
.search-box {
	padding: 10px 15px;
	.value {
		margin-right: 10px;
		width: 250px;
	}
}

:deep(.block-component .block-title span) {
	border: 0;
}
.ledger-toolbar {
	align-items: center;
	background-color: #fafafa;
	border-bottom: var(--z-border);
	display: flex;
	height: 40px;
	overflow: auto;
	.f1 {
		flex: 1;
	}

	:deep(.search) {
		align-items: center;
		display: flex;
		padding: 0 10px;
		white-space: nowrap;

		& > * {
			margin-left: 10px;
		}

		.label {
			font-size: 12px;
		}

		.date-picker {
			width: 300px;
		}
		.search-input {
			width: 120px;
		}
	}
}

.ledger-options {
	padding: 0 10px;
	white-space: nowrap;
}

.ledger-table {
}
.synchronous {
	padding: 10px 20px;
	margin-top: 10px;
	&-label {
		display: flex;
		align-items: center;
		margin-top: 10px;
		& .label {
			text-align: end;
			// min-width: 140px;
			margin-right: 10px;
		}
		& .value {
			flex: 1;
		}
		& .mark {
			width: 100%;
			display: flex;
			& span {
				display: inline-block;
				width: 114px;
			}
			& p {
				width: 304px;
			}
		}
	}
	.association {
		display: flex;
		align-items: center;
		& img {
			margin: 0px 10px;
			width: 94px;
		}
	}
	.button {
		cursor: pointer;
		border: 1px solid #dcdfe6;
		color: #fdfffd;
		background: rgb(23, 100, 206);
		width: 100%;
		padding: 6px 0px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
}
.custom-form {
	.timeTitle {
		width: 115px;
	}
	.item {
		.cloum {
			width: 45%;
			display: flex;
			justify-content: space-between;
			align-items: center;
			.value {
				margin-right: 12px;
				display: flex;
				align-items: center;
			}
		}
		.flexend {
			display: flex;
			justify-content: end;
		}
	}
}
.auxilifilling-dialog {
	.isRequire {
		&::before {
			content: '*';
			color: red;
		}
	}
	.form-item {
		padding: 10px 20px;
		display: flex;
		align-items: center;
		margin-top: 10px;
		.name {
			width: 150px;
			text-align: end;
		}
		.value {
			flex: 1;
		}
	}
	.data-bind-list {
		.data-bind-item {
			display: flex;
			align-items: center;
			padding-bottom: 5px;
			padding-top: 5px;
			border-bottom: 1px solid #e8e8e8;
			&:first-child {
				padding-top: 0px;
			}
			.data-bind-item-value {
				// width: 350px;
			}
		}
	}
}
.modal-value {
	display: flex;
	height: 40px;
	align-items: center;
}
.tips {
	display: flex;
	align-items: center;
}
</style>
