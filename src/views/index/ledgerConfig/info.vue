<!-- 业务表新增和编辑页面 -->
<script setup lang="ts" name="info">
import {
	computed,
	onActivated,
	onDeactivated,
	reactive,
	ref,
	toRaw,
	watch,
	nextTick,
	onMounted,
	onBeforeUnmount,
} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {
	ElMessage,
	ElNotification,
	FormRules,
	UploadFile,
	UploadFiles,
	FormInstance,
} from 'element-plus'
import {useSource} from '@/stores/useSource'
import {useUserStore} from '@/stores/useUserStore'
import {useArrayToTree} from '@/hooks/useConvertHook'
import departmentFavoriteComp from './components/department-favorite-comp.vue'
import {updateLabelTitle} from '@/hooks/useLabels'
import changeRecord from './components/changeRecord.vue'

import {
	createLedger,
	getLedgerList,
	downloadLedgerTemp,
	updateLedgerById,
	downloadExcelTemplateByLedgerId,
	getledgerTemplateConversionId,
	getWorkflowSchemeInfo,
	workflowSchemeInfoCode,
	getRunwayLedgerType,
	putLedgerType,
	getDetailByLedgerId,
	createLedgerType,
} from '@/api/LedgerApi'
import phoneImg from './assets/phone.png'

import {
	defaultTableData,
	ReminderCycleList,
	RunwayList,
	DataNewTypeList,
	DataNewTypeListOld,
} from '@/define/ledger.define'
import FromDataSourceComponent from './components/fromDataSource.vue'
import DataTypeItems from './components/dataTypeItems.vue'
import DesensitizationRuls from './components/desensitizationRules.vue'
import {APIConfig} from '@/api/config'
import util from '@/plugin/util'
import saveAs from 'file-saver'
import LedgerTemplates from './components/ledgerTemplates.vue'
import CalculationRulesModal from './components/CalculationRulesModal.vue'
import ChoseTimeModal from './components/ChoseTimeModal.vue'
import ChoseIdModal from './components/ChoseIdModal.vue'
import ChoseCardModal from './components/ChoseCardModal.vue'
// import ledgerTemplatePreview from '../ledgerTemplate/components/ledgerTemplatePreview.vue'
import {FlowNodeTypes, FlowType} from '@/define/Workflow'
import {GetWorkflowByCode} from '@/api/WorkflowApi'
import {JSONStringify} from 'lib/tool'
import {getFeedbackById} from '@/api/feedback'

enum ViewType {
	Add = 'add',
	Edit = 'edit',
	View = 'view',
}

enum DepartmentTypes {
	Same = 0, //填报人同部门,
	Upper = 1, //填报人上一级部门,
	UpperTwo = 2, //填报人上二级部门,
	UpperThree = 3, //填报人上三级部门,
	UpperFour = 4, //填报人上四级部门,
	UpperFive = 5, //填报人上五级部门,
}

const route = useRoute()
const router = useRouter()
const source = useSource()
const user = useUserStore()
const superAdmin = user.getUserInfo?.baseRoles.indexOf('ledger-super-admin') > -1
const viewType = ref(route.query.type as string)
const cash = ref(route.query.cash as string)
const viewId = ref(route.query.id as string)
const lastOnlineTime = ref(route.query.lastOnlineTime as string)
const focusSort = ref(-1)
localStorage.setItem('currentRoute', router.currentRoute.value.fullPath)

const form: any = reactive({
	name: '',
	quarterValue: '',
	runway: '',
	ledgerTypeId: '',
	updateDescription: '',
	reminderConfig: {
		interval: null,
		overdueRemindTime: 0,
	},
	tableFields: [],
	departmentIds: [],
	isDepartmentalAuthorization: false,
	auditType: 0,
	workflowSchemeCode: '',
	exportLargeDataWorkflowSchemeCode: '',
	exportSmallDataWorkflowSchemeCode: '',
	businessLedgerCategory: 2,
	cycle: '',
})
// const quarterValue = ref("");

// const disabledQuarter = (val: Date) => {
//   if (val <= new Date()) return false;
//   return true;
// };
const formRule = reactive<FormRules>({
	name: [{required: true, message: '请输入业务表名称', trigger: 'blur'}],
	runway: [{required: true, message: '请选择所属板块', trigger: 'change'}],
	ledgerTypeId: [{required: true, message: '请选择所属部门', trigger: 'change'}],
	workflowSchemeCode: [{required: true, message: '请选择填报流程', trigger: 'change'}],
	exportLargeDataWorkflowSchemeCode: [
		{required: true, message: '请选择导出大数据流程', trigger: 'change'},
	],
	exportSmallDataWorkflowSchemeCode: [
		{required: true, message: '请选择导出小数据流程', trigger: 'change'},
	],
	cycle: [{required: true, message: '请选择更新周期', trigger: 'change'}],
})
const block = ref<any>(null)
const currentDialogRow = ref<any>({})

const colData = ref([
	{title: '序号', field: 'sorsIndex', fixed: true, width: 60},
	{title: '列表名', field: 'displayName', fixed: true},
	// {title: '数据来源', field: '__tableDataSetStr', width: '270px', tableShow: false},
	{title: '数据类型', field: 'type', width: '270px'},
	{title: '最大长度', field: 'maxLength', width: '180px'},
	{title: '是否唯一', field: 'isUnique', width: 90},
	{title: '是否必填', field: 'isNullable', width: 90},
	{title: '脱敏展示', field: '__desensitization', width: 90},
	{title: '列表展示', field: 'isListField', width: 90},
	{title: '作为查询条件', field: 'isQueryField', width: 110},
	{title: '不可编辑', field: 'editDisabled', width: 90},
	{title: '展示在移动端', field: 'clientSettings', width: 110},
])
const showMaxLength = (row: any) => {
	if (viewType.value === ViewType.View) {
		return true
	}
	if (['string_500', 'string_0'].includes(row.type)) {
		return false
	} else {
		return true
	}
}
const departmentOptions = [
	{label: '填报人同部门', value: DepartmentTypes.Same},
	{label: '填报人上一级部门', value: DepartmentTypes.Upper},
	{label: '填报人上二级部门', value: DepartmentTypes.UpperTwo},
	{label: '填报人上三级部门', value: DepartmentTypes.UpperThree},
	{label: '填报人上四级部门', value: DepartmentTypes.UpperFour},
	{label: '填报人上五级部门', value: DepartmentTypes.UpperFive},
]

const tableData: any = ref([])
const defaultData = ref<any>([])
const defaultIndex = ref(defaultTableData.length)

const buttons = [
	{
		code: 'danger',
		type: 'danger',
		title: '删除',
		// verify:`row.__notSource`
		icon: '<i i-majesticons-eye-line></i>',
		verify: `(true && !(row.__notSource && ['UpdateTime', 'Community', 'Street', 'District', 'Informant', 'DataSource','Editor'].includes(row.name)) && row.name === null )|| (row.lastOnlineTime === 'isfield')`,
		// verify: `(true && !(row.__notSource && ['UpdateTime', 'Community', 'Street', 'District', 'Informant', 'DataSource'].includes(row.name)) && row.name === null )`,
	},
]

function addTableData(row?: any, fromSource: boolean = false) {
	row.clientSettings =
		row.clientSettings?.length === 0
			? [
					{
						client: 1,
						sort: null,
						isDisplay: false,
					},
			  ]
			: row.clientSettings

	const maxSort = Math.max(...tableData.value.map((f: any) => f.sort))
	const sorsIndexCount = Math.max(...tableData.value.map((f: any) => f.sorsIndex))
	tableData.value.push(
		fromSource
			? row
			: {
					// 无数据来源
					__id: util._guid(),
					isUnique: false, // 是否唯一
					name: null,
					displayName: '', // 别名
					sorsIndex: sorsIndexCount + 1,
					// tableInfoId: null, // 数据集id
					dbTableFieldId: null, // 所属表数据集映射字段id
					tableDataSetId: null, // 数据集字段id
					__tableDataSetStr: '', // 数据集显示名称
					__desensitization: false, // 脱敏展示
					desensitizationType: null, // 脱敏类型
					type: 'string_500', // 字段类型
					maxLength: 500,
					multiple: null, // true 多选 false 单选 null 自由输入
					isNullable: false, // 是否必填
					isListField: true, // 是否列表字段
					isQueryField: false, // 是否查询字段
					editDisabled: false, // 是否可编辑
					lastOnlineTime: 'field',
					isString: false,
					__isAddNew: true,
					clientSettings: [
						{
							client: 1,
							sort: null,
							isDisplay: false,
						},
					], // 是否展示在移动端
					options: null,
					// displayForm:0,
					isTableTemplate: false,
					fieldMultipleDto: null,
					isChange: false,
					isSelect: false,
					isIdSelect: false,
					isCardSelect: false,
					isCase: false,
					templateISas: false,
					isIdCase: false,
					calculateRule: null,
					relevanceCalculateRule: null,
					sort: maxSort + 1,
					__notSource: true,
					isAddNew: false,
			  }
	)
	console.log(tableData.value)
	tableData.value.forEach((item: any) => {
		item.oldId = item.id ? item.id : item.__id
	})
}

const spliceTableData = (row?: any) => {
	row.clientSettings =
		row.clientSettings?.length === 0
			? [
					{
						client: 1,
						sort: null,
						isDisplay: false,
					},
			  ]
			: row.clientSettings

	// let newIndex = tableData.value[row.sorsIndex-1].sorsIndex + 1;
	tableData.value.splice(row.sorsIndex, 0, {
		// 无数据来源
		__id: util._guid(),
		isUnique: false, // 是否唯一
		name: null,
		displayName: '', // 别名
		sorsIndex: row.sorsIndex + 1,
		// tableInfoId: null, // 数据集id
		dbTableFieldId: null, // 所属表数据集映射字段id
		tableDataSetId: null, // 数据集字段id
		__tableDataSetStr: '', // 数据集显示名称
		__desensitization: false, // 脱敏展示
		desensitizationType: null, // 脱敏类型
		type: 'string_500', // 字段类型
		maxLength: 500,
		multiple: null, // true 多选 false 单选 null 自由输入
		isNullable: false, // 是否必填
		isListField: true, // 是否列表字段
		isQueryField: false, // 是否查询字段
		editDisabled: false, // 是否可编辑
		lastOnlineTime: 'field',
		isString: false,
		__isAddNew: true,
		clientSettings: [
			{
				client: 1,
				sort: null,
				isDisplay: false,
			},
		], // 是否展示在移动端
		options: null,
		// displayForm:0,
		isTableTemplate: false,
		fieldMultipleDto: null,
		isChange: false,
		isSelect: false,
		validationRule: {
			type: 1,
			relatedFieldId: row.oldId,
		},
		isIdSelect: false,
		isCardSelect: true,
		isCase: false,
		templateISas: false,
		isIdCase: false,
		calculateRule: null,
		relevanceCalculateRule: null,
		sort: row.sorsIndex + 1,
		__notSource: true,
		isAddNew: false,
	})
	tableData.value.forEach((item: any) => {
		item.oldId = item.id ? item.id : item.__id
	})
	for (let i = row.sorsIndex + 1; i < tableData.value.length; i++) {
		tableData.value[i].sorsIndex += 1 // 由于我们是在插入点后插入了一个新元素，所以后续元素的 index 都要加1
	}
}

const isShowSelect = ref(false)
watch(
	() => form.workflowSchemeCode,
	(newVal) => {
		if (newVal) {
			isShowSelect.value = true
			workflowSchemeInfoCode(newVal).then((res: any) => {
				let arr = poerssData.value
				let newArr = [{label: res.data.name, value: res.data.code}]
				poerssData.value = [...arr, ...newArr].filter(
					(item, index, self) => index === self.findIndex((t) => t.value === item.value)
				)
				form.workflowSchemeCode = res.data.code
			})
		} else {
			isShowSelect.value = false
		}
	}
)

//
watch(
	() => form.exportSmallDataWorkflowSchemeCode,
	(newVal) => {
		if (newVal) {
			workflowSchemeInfoCode(newVal).then((res: any) => {
				let arr = poerssDataExport.value
				let newArr = [{label: res.data.name, value: res.data.code}]
				poerssDataExport.value = [...arr, ...newArr].filter(
					(item, index, self) => index === self.findIndex((t) => t.value === item.value)
				)
				form.exportSmallDataWorkflowSchemeCode = res.data.code
			})
		}
	}
)

//
watch(
	() => form.exportLargeDataWorkflowSchemeCode,
	(newVal) => {
		if (newVal) {
			isShowSelect.value = true
			workflowSchemeInfoCode(newVal).then((res: any) => {
				let arr = poerssDataExport.value
				let newArr = [{label: res.data.name, value: res.data.code}]
				poerssDataExport.value = [...arr, ...newArr].filter(
					(item, index, self) => index === self.findIndex((t) => t.value === item.value)
				)
				form.exportLargeDataWorkflowSchemeCode = res.data.code
			})
		}
	}
)

function removeTableData({btn, scope}: any) {
	const regex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/
	let rpnExpression = tableData.value
		.filter(
			(item: any) =>
				(item.type === 'int' || item.type === 'decimal') &&
				item.calculateRule?.rpnExpression !== ''
		)
		.map((item: any) => {
			return item.calculateRule?.rpnExpression.split(' ')
		})
		.flat(2)
		.filter((item: any) => regex.test(item))

	let dateString = tableData.value
		.filter(
			(item: any) =>
				(item.type === 'radio' || item.type === 'checkbox') &&
				item.fieldMultipleDto?.multipleArrId !== ''
		)
		.map((item: any) => {
			return item.fieldMultipleDto?.multipleArrId.split(' ')
		})
		.flat(2)
	let IdCardString = tableData.value
		.filter(
			(item: any) =>
				(item.type === 'age' || item.type === 'birthday' || item.type === 'sex') &&
				item.relevanceCalculateRule?.relevanceFieldId !== ''
		)
		.map((item: any) => {
			return item.relevanceCalculateRule?.relevanceFieldId.split(' ')
		})
		.flat(2)

	let IdCardTypeString = tableData.value
		.filter((item: any) => item.validationRule && item.validationRule.relatedFieldId)
		.map((item: any) => {
			return item.validationRule?.relatedFieldId.split(' ')
		})
		.flat(2)
	let str = scope.id || scope.__id

	if (btn.code === 'danger') {
		if (IdCardString.includes(str)) {
			return ElMessage.warning('此条数据关联了身份证配置')
		}
		if (rpnExpression.includes(str)) {
			return ElMessage.warning('此条数据关联了计算配置')
		}
		if (dateString && dateString.includes(str)) {
			return ElMessage.warning('此条数据关联了多级配置')
		}
		if (scope.validationRule) {
			return ElMessage.warning('此条数据关联了证件号配置')
		}
		if (scope.dbTableFieldId) {
			openFromDataSource.value = true
		}
		tableData.value.forEach((item: any) => {
			if (item.validationRule) {
				if (item.validationRule?.relatedFieldId === scope.__id) {
					item.validationRule = null
					item.isCardSelect = false
				}
			}
		})
		tableData.value.splice(
			tableData.value.findIndex((f: any) => f.__id === scope.__id),
			1
		)

		rpnExpression = tableData.value
			.filter(
				(item: any) =>
					(item.type === 'int' || item.type === 'decimal') &&
					item.calculateRule?.rpnExpression !== ''
			)
			.map((item: any) => {
				return item.calculateRule?.rpnExpression.split(' ')
			})
			.flat(2)
			.filter((item: any) => regex.test(item))

		dateString = tableData.value
			.filter(
				(item: any) =>
					(item.type === 'radio' || item.type === 'checkbox') &&
					item.fieldMultipleDto?.multipleArrId !== ''
			)
			.map((item: any) => {
				return item.fieldMultipleDto?.multipleArrId.split(' ')
			})
			.flat(2)
		IdCardString = tableData.value
			.filter(
				(item: any) =>
					(item.type === 'age' || item.type === 'birthday' || item.type === 'sex') &&
					item.relevanceCalculateRule?.relevanceFieldId !== ''
			)
			.map((item: any) => {
				return item.relevanceCalculateRule?.relevanceFieldId.split(' ')
			})
			.flat(2)

		tableData.value.forEach((item: any) => {
			if (rpnExpression.includes(item.id || item.__id)) {
				item.isChange = true
			} else {
				item.isChange = false
			}
			if (IdCardString.includes(item.id || item.__id)) {
				item.isIdSelect = true
				item.isIdCase = true
			} else {
				item.isIdSelect = false
				item.isIdCase = false
			}
			if (dateString.includes(item.id || item.__id)) {
				item.isSelect = true
			} else {
				item.isSelect = false
			}
		})
		console.log(tableData.value)
	}
}

const fixedFields = [
	'Editor',
	'Informant',
	'DataSource',
	'UpdateTime',
	'Community',
	'Street',
	'District',
	'City',
	'Department',
]
const fromDataSourcRef = ref()
const ledger: any = ref(null)
const ledgerType: any = computed(() => source.getLedgerType)
const dropdwonTreeRunway = computed(() => {
	const newRunway = JSON.parse(JSON.stringify(RunwayList))
	const items = toRaw(ledgerType.value).items

	newRunway.forEach((f: any, index: number) => {
		f.child = items.filter((ff: any) => ff.runway === f.label)
		f.child.forEach((fc: any) => {
			fc.parentId = f.id
		})
	})
	return newRunway
})
const openFromDataSource = ref(false)
const openDataTypeItems = ref(false)
const openDesensitizationRuls = ref(false)
const currentRow = ref<any>(null)
const uploadRef = ref()
const uploadUrl = ref('')
const ledgerTemplate = ref<any>(null)
const isChangeDataSource = ref(false)

const onDataSourceChange = () => {
	isChangeDataSource.value = true
}

const onDataSourceConfirm = (checked: []) => {
	let count = 1
	let maxSort = Math.max(...tableData.value.map((f: any) => f.sort))
	if (isChangeDataSource.value) {
		maxSort = 0
		// 暂时不替换City和department
		tableData.value = tableData.value.filter(
			(f: any) => f.name === 'City' || f.name === 'department'
		)
	}

	console.log('onDataSourceConfirm', checked, maxSort)
	if (checked.length > 0) {
		//判断是否使用模板
		isSelectLedgerTemplate.value = false
	}
	checked.forEach((row: any, index: number) => {
		const id = row.tableDataSetId + '/' + row.dbTableFieldId
		if (!tableData.value.some((f: any) => f.__id === id)) {
			let types = ''

			const RegString = /^string$/
			const RegStringNum = /^string\d+$/
			console.log('row.dbTableField.businessType', row)

			console.log('row.dbTableField.businessType', row.dbTableField.businessType)
			if (RegString.test(row.dbTableField.businessType)) {
				if (row.maxLength !== 0 && row.dbTableField.maxLength <= 500) {
					types = row.dbTableField.businessType + '_' + 500
					console.log(1)
				}
				if (row.dbTableField.maxLength === 0) {
					types = row.dbTableField.businessType + '_' + 0
					console.log(types)
				}
			} else {
				types = row.dbTableField.businessType
			}
			console.log('row.dbTableField.businessType', types)

			const data = {
				__id: id,
				isUnique: false, // 是否唯一
				name: row.dbTableField.name, // 列名
				displayName: row.displayName, // 别名
				// tableInfoId: row.dbTableField.sourceDbTableInfoId, // 动态表id
				dbTableFieldId: row.dbTableFieldId, // 所属表数据集映射字段id
				tableDataSetId: row.tableDataSetId, // 所属数据集id
				__tableDataSetStr: `${row.__dataset.name}/${row.displayName}`, // 数据集显示名称
				__desensitization: false, // 脱敏展示
				desensitizationType: null, // 脱敏类型
				__isAddNew: true,
				type: types, // 字段类型
				multiple: null, // true 多选 false 单选 null 自由输入
				isNullable: false, // 是否必填
				isListField: true, // 是否列表字段
				isQueryField: true, // 是否查询字段
				maxLength: 500, // 最大长度
				editDisabled: false, // 是否可编辑
				clientSettings: [
					{
						client: 1,
						sort: null,
						isDisplay: false,
					},
				], // 是否展示在移动端
				sort: maxSort + count,
				sorsIndex: count,
			}
			count++
			addTableData(data, true)
		}
	})
	tableData.value = tableData.value.map((item: any, index: any) => {
		let obj = {sort: 0}
		obj.sort = index + 1
		return {...item, ...obj}
	})
	const verifyData = tableData.value.filter((f: any) => !f.hasOwnProperty('__notSource'))
	const deleteData = verifyData.filter(
		(f: any) =>
			!checked.some((ff: any) => `${ff.tableDataSetId}/${ff.dbTableFieldId}` === f.__id)
	)
	// defaultIndex.value = tableData.value.length
	deleteData.forEach((row: any) => {
		const index = verifyData.findIndex((f: any) => f.__id === row.__id)
		if (row.name !== 'City') {
			tableData.value.splice(index, 1)
			if (defaultData.value.length > 0) {
				defaultData.value.splice(index, 1)
			}
		}
	})

	if (isChangeDataSource.value) {
		defaultData.value = JSON.parse(JSON.stringify(tableData.value))
		isChangeDataSource.value = false
	}
	console.log(defaultData.value)
	openFromDataSource.value = false
}

const onDropdownTreeSelected = (checked: any) => {
	form.ledgerTypeId = checked.id
}

const onChangeDataType = (row: any) => {
	if (row.type === 'int') {
		row.precision = null
	} else if (row.type === 'decimal') {
		row.precision = 3
	}
	if (row.type === 'int' || row.type === 'decimal') {
		row.isListField = true
	} else {
		row.calculateRule = null
		const regex =
			/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/
		const rpnExpression = tableData.value
			.filter(
				(item: any) =>
					(item.type === 'int' || item.type === 'decimal') &&
					item.calculateRule?.rpnExpression !== ''
			)
			.map((item: any) => {
				return item.calculateRule?.rpnExpression.split(' ')
			})
			.flat(2)
			.filter((item: any) => regex.test(item))

		tableData.value.forEach((item: any) => {
			if (rpnExpression.includes(item.id || item.__id)) {
				item.isChange = true
			} else {
				item.isChange = false
			}
		})
	}
	if (row.type === 'radio' || row.type === 'checkbox') {
		row.isListField = true
	} else {
		row.fieldMultipleDto = null

		let dateString = tableData.value
			.filter(
				(item: any) =>
					(item.type === 'radio' || item.type === 'checkbox') &&
					item.fieldMultipleDto?.multipleArrId
			)
			.map((item: any) => {
				return item.fieldMultipleDto?.multipleArrId.split(' ')
			})
			.flat(2)
		tableData.value.forEach((item: any) => {
			if (dateString.includes(item.id || item.__id)) {
				item.isSelect = true
				item.isCase = true
			} else {
				item.isSelect = false
				item.isCase = false
			}
		})
	}

	if (row.type === 'age' || row.type === 'sex' || row.type === 'birthday') {
		row.isListField = true
	} else {
		row.relevanceCalculateRule = null
		let IdCardString = tableData.value
			.filter(
				(item: any) =>
					(item.type === 'age' || item.type === 'birthday' || item.type === 'sex') &&
					item.relevanceCalculateRule?.relevanceFieldId
			)
			.map((item: any) => {
				return (
					item.relevanceCalculateRule?.relevanceFieldId &&
					item.relevanceCalculateRule?.relevanceFieldId.split(' ')
				)
			})
			.flat(2)
		console.log(IdCardString)
		tableData.value.forEach((item: any) => {
			if (IdCardString.includes(item.id || item.__id)) {
				item.isIdSelect = true
				item.isIdCase = true
			} else {
				item.isIdSelect = false
				item.isIdCase = false
			}
		})
	}
	if (row.type === 'checkbox') {
		row.multiple = true
	} else if (row.type === 'radio') {
		row.multiple = false
	} else {
		row.multiple = null
	}
	// if (['string_500', 'string_0'].includes(row.type)) {
	// 	row.MaxLength = Number(row.type.split('_')[1])
	// } else {
	// 	row.MaxLength = 0
	// 	row.maxLength = 0
	// }

	if (row.type === 'attachments' || row.type === 'images') {
		row.isNullable = false
	}

	if (row.type === 'certificate') {
		row.customValueOptions = [{name: 'identification_number'}]
		spliceTableData(row)
	} else {
		tableData.value.forEach((item: any) => {
			if (item.validationRule) {
				if (item.validationRule?.relatedFieldId === row.__id) {
					item.validationRule = null
					item.isCardSelect = false
				}
			}
		})
	}
	console.log(tableData.value)
}

const selectTableInfo = ref([])
const onOpenDataTypeItems = (row: any, type?: string) => {
	console.log(type)
	if (row.displayName === '') {
		ElMessage.warning('请先输入列表名称')
		return
	}
	selectTableInfo.value = tableData.value
		.map((item: any) => {
			let obj: any = {}
			if (item.type === 'radio' || item.type === 'checkbox') {
				obj.label = `${item.displayName}`
				obj.value = `${item.id || item.__id}`
				obj.raw = {...item}
			}
			return {...obj}
		})
		.filter((item: any) => item && item.label)
		.filter((item: any) => item.label !== `${row.displayName}`)
		.filter((item: any) => item.value !== `${row.id || row.__id}`)

	// 数据集字段
	if (type) {
		row.multiple = type === 'checkbox' ? true : false
	}

	currentRow.value = row

	openDataTypeItems.value = true
}

const openDataTypeCount = ref(false)
const currentRowMath = ref<any>()
const calceSelect = ref([])

const onOpenCount = (row: any) => {
	if (row.displayName === '') {
		ElMessage.warning('请先输入列表名称')
		return
	}
	const regex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/
	calceSelect.value = tableData.value
		.filter((item: any) => !item.calculateRule)
		.map((item: any) => {
			let obj: any = {}
			if (item.type === 'int' || item.type === 'decimal') {
				obj.label = `第${item.sorsIndex + ':  '}${item.displayName}`
				obj.value = `${item.id || item.__id}`
			}
			return {...obj}
		})
		.filter((item: any) => item && item.label)

		.filter((item: any) => item.value !== `${row.id || row.__id}`)
	console.log(row)
	currentRowMath.value = row
	openDataTypeCount.value = true
}

const onDataTypeCountCancel = () => {
	if (
		currentRowMath.value.calculateRule?.rpnExpression === '' ||
		!currentRowMath.value.hasOwnProperty('calculateRule')
	) {
		currentRowMath.value.calculateRule = null
	}

	openDataTypeCount.value = false
}

const onDataTypeCount = (checked: any) => {
	console.log(checked)
	if (checked?.modelType === 'number') {
		if (checked.__types === 'int') {
			currentRowMath.value.precision = checked?.precision ?? null
		} else {
			currentRowMath.value.precision = checked?.precision ?? 3
		}
		if (checked?.customValueLimit) delete checked?.customValueLimit.__types

		currentRowMath.value.customValueLimit = checked?.customValueLimit ?? null
		currentRowMath.value.calculateRule = null
		const regex =
			/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/
		const rpnExpression = tableData.value
			.filter(
				(item: any) =>
					(item.type === 'int' || item.type === 'decimal') &&
					item.calculateRule?.rpnExpression !== ''
			)
			.map((item: any) => {
				return item.calculateRule?.rpnExpression.split(' ')
			})
			.flat(2)
			.filter((item: any) => regex.test(item))

		tableData.value.forEach((item: any) => {
			if (rpnExpression.includes(item.id || item.__id)) {
				item.isChange = true
			} else {
				item.isChange = false
			}
		})
	} else {
		currentRowMath.value.precision = checked?.precision ?? null
		currentRowMath.value.customValueLimit = null
		currentRowMath.value.calculateRule = checked ?? null

		const regex =
			/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/

		const rpnExpression = tableData.value
			.filter(
				(item: any) =>
					(item.type === 'int' || item.type === 'decimal') &&
					item.calculateRule?.rpnExpression !== ''
			)
			.map((item: any) => {
				return item.calculateRule?.rpnExpression.split(' ')
			})
			.flat(2)
			.filter((item: any) => regex.test(item))

		tableData.value.forEach((item: any) => {
			let str = item.id || item.__id
			if (rpnExpression.includes(str)) {
				item.isChange = true
			} else {
				item.isChange = false
			}
		})
		console.log(tableData.value)
	}

	openDataTypeCount.value = false
}
// 证件配置
const openCardModal = ref(false)
const cardCurrentRow = ref<any>()
const onOpenCard = (row: any) => {
	if (row.displayName === '') {
		ElMessage.warning('请先输入列表名称')
		return
	}
	cardCurrentRow.value = row
	openCardModal.value = true
}
const onDateCardCancel = () => {
	openCardModal.value = false
}

const onDateCardCount = (checked: any) => {
	cardCurrentRow.value.customValueOptions = checked ?? [{name: 'identification_number'}]
	openCardModal.value = false
}
//  时间配置
const openTimesModal = ref(false)
const onOpenDateTimes = (row: any) => {
	console.log(openDataTypeCount.value)
	if (row.displayName === '') {
		ElMessage.warning('请先输入列表名称')
		return
	}
	currentRow.value = row
	openTimesModal.value = true
}
const onDateTimesCancel = () => {
	if (currentRow.value.displayForm === null || !currentRow.value.hasOwnProperty('displayForm')) {
		currentRow.value.displayForm = 0
	}
	openTimesModal.value = false
	console.log(openDataTypeCount.value)
}
const onDateTimesCount = (checked: any) => {
	if (!checked) {
		currentRow.value.displayForm = 0
	}
	currentRow.value.displayForm = checked ?? 0
	openTimesModal.value = false
	console.log(openDataTypeCount.value)
}

const onDataTypeItems = (checked: any) => {
	console.log('clickConfire', checked)

	// if (!checked || checked.length === 0) {
	// 	currentRow.value.multiple = null
	// }
	currentRow.value.options = checked?.options ?? null
	// currentRow.value.fieldMultipleDto = checked.multipleArrId ?? null
	if (!checked?.multipleArrId && !checked?.multipleArr) {
		currentRow.value.fieldMultipleDto = null
	} else {
		currentRow.value.fieldMultipleDto = checked
	}

	let dateString = tableData.value
		.filter(
			(item: any) =>
				(item.type === 'radio' || item.type === 'checkbox') &&
				item.fieldMultipleDto?.multipleArrId !== ''
		)
		.map((item: any) => {
			return item.fieldMultipleDto?.multipleArrId.split(' ')
		})
		.flat(2)

	//asd
	tableData.value.forEach((item: any) => {
		if (dateString.includes(item.id || item.__id)) {
			item.isSelect = true
			item.isCase = true
		} else {
			item.isSelect = false
			item.isCase = false
		}
	})

	console.log('tableData.value', tableData.value)

	openDataTypeItems.value = false
}
const onDataTypeItemsCancel = () => {
	console.log('onDataTypeItemsCancel', currentRow.value)

	// if (
	// 	currentRow.value.options === null ||
	// 	currentRow.value.options?.length === 0 ||
	// 	!currentRow.value.hasOwnProperty('options')
	// ) {
	// 	currentRow.value.multiple = null
	// }
	// currentRow.value.options
	openDataTypeItems.value = false
}

const onCheckedDesensitiaztion = (checked: boolean, row: any) => {
	if (checked) {
		onOpenDesensitizationRuls(row)
	} else {
		row.desensitizationType = null
	}
}

const onOpenDesensitizationRuls = (row: any) => {
	if (row.displayName === '') {
		ElMessage.warning('请先输入列表名称')
		row.__desensitization = false
		return
	}
	currentRow.value = row
	openDesensitizationRuls.value = true
}

const verifyCreateLedger = (): boolean => {
	if (form.name === '') {
		ElMessage.warning('请输入业务表名称')
		return false
	}
	if (form.runway === '') {
		ElMessage.warning('请选择所属板块')
		return false
	}
	if (form.ledgerTypeId === '') {
		ElMessage.warning('请选择所属部门')
		return false
	}
	if (form.runway === '') {
		ElMessage.warning('请选择所属板块')
		return false
	}

	if (form.reminderConfig.interval === null) {
		ElMessage.warning('请选择更新周期')
		return false
		// 默认不填就是不提醒
		// form.reminderConfig.interval = 0
	}
	console.log(form)
	if (!form.workflowSchemeCode) {
		ElMessage.warning('请选择填报流程')
		return false
	}

	// if (!ledgerTemplate.value && viewType.value === ViewType.Add) {
	// 	ElMessage('请选择上传模版文件')
	// 	return false
	// }

	if (tableData.value.some((s: any) => s.__desensitization && s.desensitizationType === null)) {
		const index = tableData.value.findIndex(
			(f: any) => f.__desensitization && f.desensitizationType === null
		)
		console.log(tableData.value)

		console.log(tableData.value.findIndex((f: any) => f.desensitizationType === null))
		console.log(tableData.value.find((f: any) => f.desensitizationType === null))

		ElMessage.warning(`请配置第 ${index + 1} 行的脱敏规则`)
		return false
	}

	let rowNumber: number = -1
	const isEmpty = tableData.value.some((f: any, index: number) => {
		if (f.displayName === '') {
			rowNumber = index + 1
			return true
		}
		return false
	})

	if (isEmpty) {
		ElMessage.warning(`请输入第 ${rowNumber} 行的列表名`)
		return false
	}

	return true
}

const onNextStep = () => {
	if (form.businessLedgerCategory === 2 && superAdmin) {
		if (!form.exportLargeDataWorkflowSchemeCode || !form.exportSmallDataWorkflowSchemeCode) {
			ElMessage.warning('请选择导出数据审核流程')
			return
		}
	}

	onCreateLedger((params: any) => {
		console.log('onNextStep', params)
		console.log('tableData.value', tableData.value)

		params.isSelectLedgerTemplate = isSelectLedgerTemplate.value
		if (isSelectLedgerTemplate.value) {
			params.LedgertemplateId = LedgertemplateId.value
			params.tableFieldGroups = LedgertemplateGroups.value
		}
		localStorage.setItem('CreateLedger', JSON.stringify(params))
	})
}

const onCreateLedger = (fn?: Function) => {
	if (!verifyCreateLedger()) return
	if (checkRemindTiems()) {
		return ElMessage.warning('请将设置提醒里面的时间填写完整')
	}
	tableData.value.forEach((f: any) => {
		console.log(233, f)

		if (f.clientSettings.length !== 0 && f.clientSettings[0].sort === null) {
			f.clientSettings = [{client: 1, isDisplay: false, sort: null}]
		}
		// if (
		// 	( f.type=== 'string_0') ||
		// 	( f.type=== 'string_500')
		// ) {
		// 	console.log(f.type.split('_')[1])
		// 	console.log(f.type.split('_')[0])

		// 	f.type = f.type.split('_')[0]
		// 	f.MaxLength = f.type.split('_')[1]
		// 	console.log(f)
		// }
		// console.log(f)
	})
	form.tableFields = tableData.value

	const pushForm = JSON.parse(JSON.stringify(form))
	pushForm.tableFields.forEach((f: any) => {
		if (f.type === 'checkbox' || f.type === 'radio') {
			f.type = 'string'
		}

		if (f.name === null) {
			f.name = f.displayName
		}

		f.isNullable = !f.isNullable
	})
	if (typeof fn === 'function') {
		fn(pushForm)
		router.push({
			path: '/ledgerConfig/ledgerDisplay',
			query: {type: viewType.value, ledgerId: ledger?.value?.id},
		})
		return
	}

	if (viewType.value === ViewType.Add) {
		createLedger({
			...toRaw(pushForm),
		}).then((res: any) => {
			const id = res.data.id
			if (ledgerTemplate.value && viewType.value === ViewType.Add) {
				ElMessage.success('业务表创建成功')
				uploadUrl.value = `${APIConfig(
					'ledger'
				)}/api/ledger-service/ledger/${id}/import-excel-template-file`
				uploadRef.value.submit()
			} else {
				router.push('/ledger/config')
			}
		})
	} else {
		// 编辑
		updateLedgerById(viewId.value, {
			...toRaw(pushForm),
		}).then(() => {
			ElMessage.success('业务表更新成功')
			if (ledgerTemplate.value && viewType.value === ViewType.Edit) {
				uploadUrl.value = `${APIConfig('ledger')}/api/ledger-service/ledger/${
					viewId.value
				}/import-excel-template-file`
				uploadRef.value.submit()
			} else {
				router.push('/ledger/config')
			}
		})
	}
}

const onDesensitizationRules = (checked: any) => {
	console.log('onDesensitizationRules', checked)
	currentRow.value.desensitizationType = checked?.value ?? null
	openDesensitizationRuls.value = false
}

const onUploadTemplate = (uploadFile: UploadFile, uploadFiles: UploadFiles) => {
	ledgerTemplate.value = uploadFile
}

const onUploadSuccess = () => {
	ElMessage.success('模版上传成功')
	router.push('/ledger/config')
}

const onDownloadTemplate = () => {
	if (ledger.value.excelTemplateFile) {
		downloadLedgerTemp(viewId.value)
	} else {
		downloadExcelTemplateByLedgerId(viewId.value).then((res: any) => {
			const blob = res.data
			saveAs(
				new Blob([blob], {
					type: 'apilication/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				}),
				`${ledger.value.name}导入模板.xlsx`
			)
		})
	}
}

const onChangeSortInput = (row: any) => {
	if (row.sort !== '') {
		const currentSort = row.sort
		tableData.value.forEach((f: any) => {
			if (f.__id !== row.__id) {
				if (f.sort >= currentSort && f.sort <= focusSort.value) {
					f.sort += 1
				}

				if (f.sort <= currentSort && f.sort >= focusSort.value) {
					f.sort -= 1
				}
			}
		})
		tableData.value = tableData.value.sort((a: any, b: any) => a.sort - b.sort)
	}
}

let noAdd = ''
let initInfo = {}
const getLedgerListFields = async (dataset?: any) => {
	await getinfo()
	const {type} = route.query
	let title = '新增-业务表管理'
	if (viewType.value !== ViewType.Add) {
		// 获取详情
		getLedgerList('', 0, 1, viewId.value).then((res: any) => {
			const data = res.data
			if (type == 'view') {
				title = `查看-业务表管理-${data.name}`
			} else {
				title = `编辑-业务表管理-${data.name}`
			}
			updateLabelTitle({
				path: router.currentRoute.value.fullPath,
				title,
			})
			// 默认值
			let arr = departmentFilter.value.filter((item: any) => {
				if (item.value == detList.value.value) {
					return true
				}
			})
			if (arr.length == 0) {
				departmentFilter.value.push(detList.value)
				console.log(detList.value, 'cesss')
			}
			initInfo = res.data
			const df: any = []
			console.log('当前业务表数据:', data)
			// 设置当前的所属板块 如果所属板块改变会调用其他接口
			localStorage.setItem('currentRunway', data.runway)
			form.name = data.name
			form.runway = data.runway
			form.ledgerTypeId = data.ledgerTypeId
			console.log(departmentFilter.value, '123123')
			form.reminderConfig = data.reminderConfig
			form.cycle = data.reminderConfig.interval
			form.isDepartmentalAuthorization = data.isDepartmentalAuthorization
			form.updateDescription = data.updateDescription
			form.auditType = data.auditType
			form.workflowSchemeCode = data.workflowSchemeCode
			form.exportLargeDataWorkflowSchemeCode = data.exportLargeDataWorkflowSchemeCode
			form.exportSmallDataWorkflowSchemeCode = data.exportSmallDataWorkflowSchemeCode
			form.businessLedgerCategory = data.businessLedgerCategory
			remindConfig.value = data.reminderConfig
			noAdd = res.data.reminderConfig
			form.tableFields = []
			tableData.value = []

			data.tableInfo.fields
				.sort((a: any, b: any) => a.sort - b.sort)
				.filter((fs: any) => fs.name !== 'Department')
				.forEach((row: any, i: number) => {
					row.__id =
						(row.tableDataSetId ?? util._guid()) +
						'/' +
						(row.dbTableFieldId ?? util._guid())
					row.oldId = row.id
					if (row.dbTableFieldId) {
						// const currentDataset = dataset.find((f: any) => f.id === row.tableDataSetId)
						// row.__tableDataSetStr = currentDataset?.name
					} else {
						row.__tableDataSetStr = '无数据来源'
					}
					// string ， string_500 ， string_0 - 500 以内 ， 如果是string 则直接赋值string_500, 如果是除500其他的值 直接赋值string_0
					// 找到匹配 string

					const RegString = /^string$/
					const RegStringNum = /^string\d+$/
					row.sorsIndex = i + 1

					if (RegString.test(row.type) && row.multiple === null) {
						row.isString = false
					} else {
						row.isString = true
					}
					if (RegString.test(row.type)) {
						if (row.maxLength !== 0 && row.maxLength <= 500) {
							row.type = row.type + '_' + 500
						}
						if (row.maxLength === 0) {
							row.type = row.type + '_' + 0
						}
					}

					if (
						(row.type === 'string_0' || row.type === 'string_500') &&
						row.multiple !== null &&
						row.tableDataSetId === null
					) {
						row.type = row.multiple ? 'checkbox' : 'radio'
					}

					if (row.tableDataSetId === null) {
						row.__notSource = true
					}

					row.__desensitization = row.desensitizationType !== null
					row.__isUpDate = true
					console.log(tableData.value)

					tableData.value.forEach((item: any) => {
						if (item.name === 'District') {
							if (item.isNullable) {
								tableData.value.forEach((it: any) => {
									if (it.name === 'City') {
										it.isNodeDis = true
										it.isListField = true
									} else if (it.name === 'District') {
										it.isNodeDis = false
									}
								})
							}
						} else if (item.name === 'Street') {
							if (item.isNullable) {
								tableData.value.forEach((it: any) => {
									if (it.name === 'City') {
										// it.isNullable = false
										it.isNodeDis = true
										it.isListField = true
									} else if (it.name === 'District') {
										// it.isNullable = false
										it.isNodeDis = true
										it.isListField = true
									} else if (it.name === 'Street') {
										it.isNodeDis = false
										it.isListField = true
									}
								})
							}
						} else if (item.name === 'Community') {
							if (item.isNullable) {
								tableData.value.forEach((it: any) => {
									if (it.name === 'City') {
										it.isNodeDis = true
										it.isListField = true
									} else if (it.name === 'District') {
										it.isNodeDis = true
										it.isListField = true
									} else if (it.name === 'Street') {
										it.isNodeDis = true
										it.isListField = true
									} else if (it.name === 'Community') {
										it.isNodeDis = false
										it.isListField = true
									}
								})
							}
						}
					})

					console.log(tableData.value)
					row.isNullable = !row.isNullable
					row.isTableTemplate = false
					//  如果是从来没有上过线的那么就可以删除这条数据
					if (
						![
							'UpdateTime',
							'Community',
							'Street',
							'District',
							'Informant',
							'Editor',
							'DataSource',
							'City',
							'Department',
						].includes(row.name)
					) {
						row.lastOnlineTime = data.lastOnlineTime ? data.lastOnlineTime : 'isfield'
					} else {
						row.lastOnlineTime = 'field'
					}

					// if (/string/.test()) {
					// 	row.type = row.type + '_' + row.maxLength
					// }
					if (row.clientSettings.length === 0) {
						row.clientSettings = [
							{
								client: 1,
								isDisplay: false,
								sort: null,
							},
						]
					} else {
						disPlayMobileList.value.push(i + 1)
						localStorage.setItem(
							'disPlayMobileList',
							JSON.stringify(disPlayMobileList.value)
						)
					}
					console.log(row)
					addTableData(row, true) // sass
					df.push(JSON.parse(JSON.stringify(row)))
				})
			defaultData.value = df

			ledger.value = data
			showDepartmentCheck.value = true
			defaultCheckedData.value = ledger.value?.tableInfo.ledgerDepartmentNames
			departmentSelected.value = defaultCheckedData.value
			form.departmentIds = defaultCheckedData.value.map((f: any) => f.id)

			const regex =
				/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/
			const rpnExpression = tableData.value
				.filter(
					(item: any) =>
						(item.type === 'int' || item.type === 'decimal') &&
						item.calculateRule?.rpnExpression !== ''
				)
				.map((item: any) => {
					return item.calculateRule?.rpnExpression.split(' ')
				})
				.flat(2)
				.filter((item: any) => regex.test(item))

			let dateString = tableData.value
				.filter(
					(item: any) =>
						(item.type === 'radio' || item.type === 'checkbox') &&
						item.fieldMultipleDto?.multipleArrId !== ''
				)
				.map((item: any) => {
					return item.fieldMultipleDto?.multipleArrId.split(' ')
				})
				.flat(2)
			// 列表展示
			tableData.value.forEach((item: any) => {
				if (item.name === 'District') {
					if (item.isListField) {
						tableData.value.forEach((it: any) => {
							if (it.name === 'City') {
								it.isNodeListField = true
								it.isListField = true
							} else if (it.name === 'City') {
								it.isNodeListField = false
								it.isListField = true
							}
						})
					}
				}
			})
			// 列表展示
			tableData.value.forEach((item: any) => {
				if (item.name === 'Street') {
					if (item.isListField) {
						tableData.value.forEach((it: any) => {
							if (it.name === 'City') {
								it.isNodeListField = true
								it.isListField = true
							} else if (it.name === 'District') {
								it.isNodeListField = true
								it.isListField = true
							} else if (it.name === 'Street') {
								it.isNodeListField = false
								it.isListField = true
							}
						})
					}
				}
			})

			// 列表展示
			tableData.value.forEach((item: any) => {
				if (item.name === 'Community') {
					if (item.isListField) {
						tableData.value.forEach((it: any) => {
							if (it.name === 'City') {
								it.isNodeListField = true
								it.isListField = true
							} else if (it.name === 'District') {
								it.isNodeListField = true
								it.isListField = true
							} else if (it.name === 'Street') {
								it.isNodeListField = true
								it.isListField = true
							} else if (it.name === 'Community') {
								it.isNodeListField = false
								it.isListField = true
							}
						})
					}
				}
			})

			let IdCardString = tableData.value
				.filter(
					(item: any) =>
						(item.type === 'age' || item.type === 'birthday' || item.type === 'sex') &&
						item.relevanceCalculateRule?.relevanceFieldId !== ''
				)
				.map((item: any) => {
					return item.relevanceCalculateRule?.relevanceFieldId.split(' ')
				})
				.flat(2)
			let idCardItem = tableData.value
				.filter(
					(item: any) =>
						(item.type === 'age' || item.type === 'birthday' || item.type === 'sex') &&
						item.relevanceCalculateRule?.relevanceFieldId !== ''
				)
				.map((item: any) => {
					return item.isNullable
				})

			tableData.value.forEach((item: any) => {
				let str = item.id || item.__id
				if (rpnExpression.includes(str)) {
					console.log(str)
					item.isChange = true
				} else {
					item.isChange = false
				}
				if (item.validationRule) {
					item.isCardSelect = true
				} else {
					item.isCardSelect = false
				}
				if (dateString.includes(item.id || item.__id)) {
					item.isSelect = true
					item.isCase = true
				} else {
					item.isSelect = false
					item.isCase = false
				}
				if (
					IdCardString.includes(item.id || item.__id) &&
					idCardItem.some((item: any) => item)
				) {
					item.isIdSelect = true
					console.log('IdCardString', IdCardString)
					item.isIdCase = true
				} else {
					item.isIdSelect = false

					item.isIdCase = false
				}
				if (item.isNullable) {
					item.isListField = true
				}
				if (item.isUnique) {
					item.isListField = true
				}
			})
		})
	} else {
		updateLabelTitle({
			path: router.currentRoute.value.fullPath,
			title,
		})
	}
}

// const onFromDataSourceCompleted = (dataset: any) => {
// 	ledgerTemplateDataset.value = dataset
// 	console.log(dataset)
// 	getLedgerListFields(dataset)
// }

const departmentSelected = ref<any[]>([])
const defaultCheckedData = ref<any[]>([])
const onDepartmentFavoriteChange = (value: any) => {
	departmentSelected.value = value
	form.departmentIds = value.map((f: any) => f.id)
}
const showDepartmentCheck = ref(false)

// 添加时候业务表模板
const openSelectLedgerTemplateTableList = ref<any>(false) //模板是否显示
const isSelectLedgerTemplate = ref<any>(false)
const ledgerTemplateDataset = ref<any>([]) //获取的数据来源
const LedgertemplateId = ref<any>('')
const LedgertemplateGroups = ref<any>([])

const onLedgerTemplateConfirm = (val: any) => {
	val = JSON.parse(val)
	LedgertemplateId.value = val
	isSelectLedgerTemplate.value = true
	openSelectLedgerTemplateTableList.value = false
	// 开始调接口处理字段
	getInfoTableDataFiled(val)
}
const getInfoTableDataFiled = (id: any) => {
	getledgerTemplateConversionId({id}).then((res) => {
		const data = res.data
		const df: any = []
		console.log('当前业务表数据:', data)
		form.tableFields = []
		tableData.value = []
		LedgertemplateGroups.value = data.tableFieldGroups
		data.fields
			.sort((a: any, b: any) => a.sort - b.sort)
			.forEach((row: any, i: any) => {
				//momo
				row.__id =
					(row.tableDataSetId ?? util._guid()) +
					'/' +
					(row.dbTableFieldId ?? util._guid())
				if (row.dbTableFieldId) {
					const currentDataset = ledgerTemplateDataset.value.find(
						(f: any) => f.id === row.tableDataSetId
					)
					row.__tableDataSetStr = currentDataset?.name
				} else {
					row.__tableDataSetStr = '无数据来源'
				}
				const RegString = /^string$/
				const RegStringNum = /^string\d+$/

				if (RegString.test(row.type) && row.multiple === null) {
					row.isString = false
				} else {
					row.isString = true
				}
				if (RegString.test(row.type)) {
					if (row.maxLength !== 0 && row.maxLength <= 500) {
						row.type = row.type + '_' + 500
					}

					if (row.maxLength === 0) {
						row.type = row.type + '_' + 0
					}
				}
				row.sorsIndex = i + 1
				row.isTableTemplate = true
				if (
					(row.type === 'string_0' || row.type === 'string_500') &&
					row.multiple !== null &&
					row.tableDataSetId === null
				) {
					row.type = row.multiple ? 'checkbox' : 'radio'
				}

				if (row.tableDataSetId === null) {
					row.__notSource = true
				}

				row.__desensitization = row.desensitizationType !== null
				row.templateISas = true
				console.log(tableData.value)
				row.isNullable = !row.isNullable

				addTableData(row, true)
				df.push(JSON.parse(JSON.stringify(row)))
			})
		defaultData.value = df
		// 是否必填
		tableData.value.forEach((item: any) => {
			if (item.name === 'District') {
				if (item.isNullable) {
					tableData.value.forEach((it: any) => {
						if (it.name === 'City') {
							it.isNodeDis = true
							it.isListField = true
						} else if (it.name === 'District') {
							it.isNodeDis = false
							it.isListField = true
						}
					})
				}
			} else if (item.name === 'Street') {
				if (item.isNullable) {
					tableData.value.forEach((it: any) => {
						if (it.name === 'City') {
							// it.isNullable = false
							it.isNodeDis = true
							it.isListField = true
						} else if (it.name === 'District') {
							// it.isNullable = false
							it.isNodeDis = true
							it.isListField = true
						} else if (it.name === 'Street') {
							it.isNodeDis = false
							it.isListField = true
						}
					})
				}
			} else if (item.name === 'Community') {
				if (item.isNullable) {
					tableData.value.forEach((it: any) => {
						if (it.name === 'City') {
							it.isNodeDis = true
							it.isListField = true
						} else if (it.name === 'District') {
							it.isNodeDis = true
							it.isListField = true
						} else if (it.name === 'Street') {
							it.isNodeDis = true
							it.isListField = true
						} else if (it.name === 'Community') {
							it.isNodeDis = false
							it.isListField = true
						}
					})
				}
			}
		})

		const regex =
			/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/
		const rpnExpression = tableData.value
			.filter(
				(item: any) =>
					(item.type === 'int' || item.type === 'decimal') &&
					item.calculateRule?.rpnExpression !== ''
			)
			.map((item: any) => {
				return item.calculateRule?.rpnExpression.split(' ')
			})
			.flat(2)
			.filter((item: any) => regex.test(item))

		let dateString = tableData.value
			.filter(
				(item: any) =>
					(item.type === 'radio' || item.type === 'checkbox') &&
					item.fieldMultipleDto?.multipleArrId !== ''
			)
			.map((item: any) => {
				return item.fieldMultipleDto?.multipleArrId.split(' ')
			})
			.flat(2)

		// 列表展示
		tableData.value.forEach((item: any) => {
			if (item.name === 'District') {
				if (item.isListField) {
					tableData.value.forEach((it: any) => {
						if (it.name === 'City') {
							it.isNodeListField = true
							it.isListField = true
						} else if (it.name === 'City') {
							it.isNodeListField = false
							it.isListField = true
						}
					})
				}
			}
		})
		// 列表展示
		tableData.value.forEach((item: any) => {
			if (item.name === 'Street') {
				if (item.isListField) {
					tableData.value.forEach((it: any) => {
						if (it.name === 'City') {
							it.isNodeListField = true
							it.isListField = true
						} else if (it.name === 'District') {
							it.isNodeListField = true
							it.isListField = true
						} else if (it.name === 'Street') {
							it.isNodeListField = false
							it.isListField = true
						}
					})
				}
			}
		})

		// 列表展示
		tableData.value.forEach((item: any) => {
			if (item.name === 'Community') {
				if (item.isListField) {
					tableData.value.forEach((it: any) => {
						if (it.name === 'City') {
							it.isNodeListField = true
							it.isListField = true
						} else if (it.name === 'District') {
							it.isNodeListField = true
							it.isListField = true
						} else if (it.name === 'Street') {
							it.isNodeListField = true
							it.isListField = true
						} else if (it.name === 'Community') {
							it.isNodeListField = false
							it.isListField = true
						}
					})
				}
			}
		})
		let IdCardString = tableData.value
			.filter(
				(item: any) =>
					(item.type === 'age' || item.type === 'birthday' || item.type === 'sex') &&
					item.relevanceCalculateRule?.relevanceFieldId !== ''
			)
			.map((item: any) => {
				return item.relevanceCalculateRule?.relevanceFieldId.split(' ')
			})
			.flat(2)
		let idCardItem = tableData.value
			.filter(
				(item: any) =>
					(item.type === 'age' || item.type === 'birthday' || item.type === 'sex') &&
					item.relevanceCalculateRule?.relevanceFieldId !== ''
			)
			.map((item: any) => {
				return item.isNullable
			})
		tableData.value.forEach((item: any) => {
			let str = item.id || item.__id

			if (rpnExpression.indexOf(str) !== -1) {
				console.log(str)
				console.log(rpnExpression)

				item.isChange = true
			} else {
				item.isChange = false
			}

			if (item.validationRule) {
				item.isCardSelect = true
			} else {
				item.isCardSelect = false
			}
			if (dateString.includes(item.id || item.__id)) {
				item.isSelect = true
				item.isCase = true
			} else {
				item.isSelect = false
				item.isCase = false
			}
			if (
				IdCardString.includes(item.id || item.__id) &&
				idCardItem.some((item: any) => item)
			) {
				item.isIdSelect = true
				item.isIdCase = true
			} else {
				item.isIdSelect = false
				item.isIdCase = false
			}
			if (item.isNullable) {
				item.isListField = true
			}
			if (item.isUnique) {
				item.isListField = true
			}
		})
		const isFindEditor = tableData.value.find((item: any) => item.name === 'Editor')
		if (!isFindEditor) {
			tableData.value.splice(7, 0, {
				__id: util._guid(),

				isUnique: false, // 是否唯一
				name: 'Editor', // 列名
				displayName: '编辑人', // 别名
				// tableInfoId: null, // 数据集id
				dbTableFieldId: null, // 所属表数据集映射字段id
				tableDataSetId: null, // 数据集字段id
				__tableDataSetStr: '', // 数据集显示名称
				__desensitization: false, // 脱敏展示
				desensitizationType: null, // 脱敏类型
				__isAddNew: true,
				lastOnlineTime: 'field',
				isString: false,
				type: 'string_500', // 字段类型
				multiple: null, // true 多选 false 单选 null 自由输入
				isNullable: false, // 是否必填
				isListField: true, // 是否列表字段
				isQueryField: true, // 是否查询字段
				maxLength: 500, // 最大长度
				editDisabled: false, // 是否可编辑
				clientSettings: [
					{
						client: 1,
						sort: null,
						isDisplay: false,
					},
				], // 是否展示在移动端
				options: null, // 单选/多选选项

				sort: 7, // 列表展示顺序
				sorsIndex: 7,
				__notSource: true, // 无数据来源
			})
			for (let i = 0; i < tableData.value.length; i++) {
				tableData.value[i].sorsIndex += 1 // 由于我们是在插入点后插入了一个新元素，所以后续元素的 index 都要加1
			}
		}

		// ledger.value = data
		// showDepartmentCheck.value = true
		// defaultCheckedData.value = ledger.value?.tableInfo.ledgerDepartmentnNames
	})
}

const disPlayMobileList = ref<any[]>([])
const onCheckedMobileDisplay = (e: boolean, data: any) => {
	if (e) {
		if (disPlayMobileList.value.length >= 4) {
			data.rowData.clientSettings = [
				{
					client: 1,
					isDisplay: false,
					sort: null,
				},
			]
			return ElNotification.warning('最多只允许配置4个字段展示')
		}
		disPlayMobileList.value.push(data.rowIndex + 1)
		data.rowData.clientSettings = [
			{
				client: 1,
				isDisplay: true,
				sort: disPlayMobileList.value.findIndex((v) => v === data.rowIndex + 1),
			},
		]
	} else {
		disPlayMobileList.value = disPlayMobileList.value.filter(
			(f: any) => f !== data.rowIndex + 1
		)
		// console.log(123, disPlayMobileList.value)
		data.rowData.clientSettings = [{isDisplay: false, client: 1, sort: null}]
		tableData.value.forEach((datas: any, index: number) => {
			if (disPlayMobileList.value.includes(index + 1)) {
				datas.clientSettings = [
					{
						client: 1,
						isDisplay: true,
						sort: disPlayMobileList.value.findIndex((v) => v === index + 1),
					},
				]
			}
		})
	}
	localStorage.setItem('disPlayMobileList', JSON.stringify(disPlayMobileList.value))
}
const departmentFilter = ref<any>([]) //过滤后的
const ledgerRunwayList = () => {
	getRunwayLedgerType({runway: ''}).then((res) => {
		console.log(res.data, 'ggggg')
		let assemblyDepartments = [] as any
		res?.data?.items.forEach((item: any) => {
			let obj = {label: item?.name, value: item?.id}
			assemblyDepartments.push(obj)
		})
		departmentFilter.value = assemblyDepartments
		console.log(departmentFilter.value, 'cesss')
	})
}
const poerssData: any = ref([])
const poerssDataExport: any = ref([])
const getWorkflowSchemeInfoData = (category: FlowType = FlowType.Initiate) => {
	getWorkflowSchemeInfo({enabledMark: true, category}).then((res) => {
		const temp = res.data.items.map((r: any) => {
			return {
				label: r.name,
				value: r.code,
			}
		})

		if (category === FlowType.Initiate) {
			poerssData.value = temp
		} else if (category === FlowType.Export) {
			poerssDataExport.value = temp
		}
	})
}

const getErrorCodeData = () => {
	console.log(form.exportSmallDataWorkflowSchemeCode)
	console.log(form.exportLargeDataWorkflowSchemeCode)
	workflowSchemeInfoCode(form.exportSmallDataWorkflowSchemeCode).then((res: any) => {
		console.log(res)
	})
}
onActivated(() => {
	getLedgerListFields()
	viewType.value = route.query.type as string
	viewId.value = route.query.id as string
	const disPlayMobileLists = JSON.parse(localStorage.getItem('disPlayMobileList'))
	if (disPlayMobileLists) {
		disPlayMobileList.value = disPlayMobileLists
	}
	getWorkflowSchemeInfoData()
	getWorkflowSchemeInfoData(FlowType.Export)
	ledgerRunwayList()
	// viewType.value = route.query.type
	// showDepartmentCheck.value = true
	if (
		route.query.type === ViewType.Add &&
		!history.state.forward?.includes('/ledgerConfig/ledgerDisplay')
	) {
		formRefSearch.value?.resetFields()
		form.name = ''
		form.runway = ''
		form.cycle = ''
		localStorage.removeItem('currentRunway')
		form.ledgerTypeId = ''
		form.updateDescription = ''
		form.reminderConfig = {
			interval: null,
			overdueRemindTime: 0,
		}
		form.tableFields = []
		form.workflowSchemeCode = ''
		form.isDepartmentalAuthorization = false
		// form.businessLedgerCategory = ''
		tableData.value = defaultTableData.slice(0, defaultIndex.value)
		tableData.value.forEach((item: any) => {
			if (item.name === 'City') {
				item.isListField = true
				item.isNodeListField = true
			}
			if (item.name === 'District') {
				item.isListField = true
				item.isNodeListField = true
			}
			if (item.name === 'Street') {
				item.isListField = true
				item.isNodeListField = true
			}
			if (item.name === 'Community') {
				item.isListField = true
				item.isNodeListField = false
			}
		})

		remindConfig.value.downHalfYearDateOnlyTime = null
		remindConfig.value.downHalfYearReminderTime = null
		remindConfig.value.upHalfYearDateOnlyTime = null
		remindConfig.value.upHalfYearReminderTime = null
		remindConfig.value.downHalfMonthDateOnlyTime = 0
		remindConfig.value.downHalfMonthReminderTime = 0
		remindConfig.value.upHalfMonthDateOnlyTime = 0
		remindConfig.value.upHalfMonthReminderTime = 0
		remindConfig.value.yearlyDeadlineDay = null
		remindConfig.value.yearlyReminderDay = null
		remindConfig.value.quarterlyDeadlineDay4 = null
		remindConfig.value.quarterlyReminderDay4 = null
		remindConfig.value.quarterlyDeadlineDay3 = null
		remindConfig.value.quarterlyReminderDay3 = null
		remindConfig.value.quarterlyDeadlineDay2 = null
		remindConfig.value.quarterlyReminderDay2 = null
		remindConfig.value.quarterlyDeadlineDay1 = null
		remindConfig.value.quarterlyReminderDay1 = null
		remindConfig.value.monthlyDeadlineDay = null
		remindConfig.value.monthlyReminderDay = null
		remindConfig.value.weeklyDeadlineDayOfWeek = null
		remindConfig.value.weeklyReminderDayOfWeek = null
		remindConfig.value.interval = 0

		remindConfig.value.weeklyStartDayOfWeek = null
		remindConfig.value.monthlyStartDay = null
		remindConfig.value.quarterlyStartDay1 = null
		remindConfig.value.quarterlyStartDay2 = null
		remindConfig.value.quarterlyStartDay3 = null
		remindConfig.value.quarterlyStartDay4 = null
		remindConfig.value.downHalfMonthStartTime = 0
		remindConfig.value.upHalfMonthStartTime = 0
		remindConfig.value.upHalfYearStartTime = null
		remindConfig.value.downHalfYearStartTime = null

		defaultData.value = []
		// fromDataSourcRef.value.clear()
		departmentSelected.value = []
		showDepartmentCheck.value = true
	} else {
		defaultCheckedData.value = departmentSelected.value
		showDepartmentCheck.value = true
		console.log(tableData.value)

		// tableData.value.forEach((row: any) => {
		// 	if(row.type === "string") {
		// 		row.type = row.type + '_' + (row.maxLength || row.MaxLength  )
		// 	}

		// })
	}
})

onDeactivated(() => {
	disPlayMobileList.value = []
	showDepartmentCheck.value = false
	// form.isDepartmentalAuthorization = false
})

const showRows = ref(true) // 控制前几行是否显示
const onShowTabaleData = () => {
	showRows.value = !showRows.value
}
// 根据 shouldShowFirstEight 来计算显示的数据
const displayedTableData = computed(() => {
	if (showRows.value) {
		return tableData.value
	} else {
		return tableData.value.filter((item: any) => !fixedFields.includes(item.name))
	}
})

const isNullableChange = (row: any) => {
	if (row.name === 'District') {
		tableData.value.forEach((item: any, index) => {
			if (row.isNullable) {
				if (item.name === 'City') {
					item.isListField = true
					item.isNullable = true
					item.isNodeDis = true
				}
			} else {
				if (item.name === 'City') {
					item.isNullable = false
					item.isNodeDis = false
				}
			}
			if (row.isListField) {
				if (item.name === 'City') {
					item.isNodeListField = true
				}
			}
		})
	} else if (row.name === 'Street') {
		tableData.value.forEach((item: any, index) => {
			if (row.isNullable) {
				if (item.name === 'City') {
					item.isNullable = true
					item.isNodeDis = true
					item.isListField = true
				} else if (item.name === 'District') {
					item.isNullable = true
					item.isNodeDis = true
					item.isListField = true
				}
			} else {
				if (item.name === 'City') {
					item.isNullable = false
					item.isNodeDis = false
				} else if (item.name === 'District') {
					item.isNullable = false
					item.isNodeDis = false
				}
			}
			if (row.isListField) {
				if (item.name === 'City') {
					item.isNodeListField = true
				} else if (item.name === 'District') {
					item.isNodeListField = true
				}
			}

			// if(row.isListField) {
			// 	item.isNodeListField = true
			// }else{
			// 	item.isNodeListField = false

			// }
		})
	} else if (row.name === 'Community') {
		tableData.value.forEach((item: any, index) => {
			if (row.isNullable) {
				if (item.name === 'City') {
					item.isNullable = true
					item.isNodeDis = true
					item.isListField = true
				} else if (item.name === 'District') {
					item.isNullable = true
					item.isNodeDis = true
					item.isListField = true
				} else if (item.name === 'Street') {
					item.isNullable = true
					item.isListField = true

					item.isNodeDis = true
				}
			} else {
				if (item.name === 'City') {
					item.isNullable = false
					item.isNodeDis = false
				} else if (item.name === 'District') {
					item.isNullable = false
					item.isNodeDis = false
				} else if (item.name === 'Street') {
					item.isNullable = false
					item.isNodeDis = false
				}
			}
			if (row.isListField) {
				if (item.name === 'City') {
					item.isNodeListField = true
				} else if (item.name === 'District') {
					item.isNodeListField = true
				} else if (item.name === 'Street') {
					item.isNodeListField = true
				}
			}
		})
	}

	const fieldMultiple = tableData.value.map((m: any) => m.relevanceCalculateRule).filter(Boolean)
	console.log(fieldMultiple)
	if (fieldMultiple.length > 0) {
		evalSelectIdCard(row)
	}
	row.isListField = true
}

const isListFieldChange = (row: any) => {
	if (row.name === 'District') {
		tableData.value.forEach((item: any, index) => {
			if (row.isListField) {
				if (item.name === 'City') {
					item.isListField = true
					item.isNodeListField = true
				}
			} else {
				if (item.name === 'City') {
					item.isListField = false
					item.isNodeListField = false
				}
			}
		})
	} else if (row.name === 'Street') {
		tableData.value.forEach((item: any, index) => {
			if (row.isListField) {
				if (item.name === 'City') {
					item.isNodeListField = true
					item.isListField = true
				} else if (item.name === 'District') {
					item.isNodeListField = true
					item.isListField = true
				}
			} else {
				if (item.name === 'City') {
					item.isListField = false
					item.isNodeListField = false
				} else if (item.name === 'District') {
					item.isListField = false
					item.isNodeListField = false
				}
			}
		})
	} else if (row.name === 'Community') {
		tableData.value.forEach((item: any, index) => {
			if (row.isListField) {
				if (item.name === 'City') {
					item.isNodeListField = true
					item.isListField = true
				} else if (item.name === 'District') {
					item.isNodeListField = true
					item.isListField = true
				} else if (item.name === 'Street') {
					item.isListField = true
					item.isNodeListField = true
				}
			} else {
				if (item.name === 'City') {
					item.isNodeListField = false
					item.isListField = false
				} else if (item.name === 'District') {
					item.isListField = false

					item.isNodeListField = false
				} else if (item.name === 'Street') {
					item.isListField = false

					item.isNodeListField = false
				}
			}
		})
	}

	tableData.value.forEach((item: any, index) => {
		if (item.isNullable) {
			item.isListField = true
		}
	})
}
const isUniqueChange = (row: any) => {
	row.isListField = true
	row.isNullable = true
	if (row.name === 'District') {
		tableData.value.forEach((item: any, index) => {
			if (row.isNullable) {
				if (item.name === 'City') {
					item.isListField = true
					item.isNullable = true
					item.isNodeDis = true
				}
			} else {
				if (item.name === 'City') {
					item.isNullable = false
					item.isNodeDis = false
				}
			}
			if (row.isListField) {
				if (item.name === 'City') {
					item.isNodeListField = true
				}
			}
		})
	} else if (row.name === 'Street') {
		tableData.value.forEach((item: any, index) => {
			if (row.isNullable) {
				if (item.name === 'City') {
					item.isNullable = true
					item.isNodeDis = true
					item.isListField = true
				} else if (item.name === 'District') {
					item.isNullable = true
					item.isNodeDis = true
					item.isListField = true
				}
			} else {
				if (item.name === 'City') {
					item.isNullable = false
					item.isNodeDis = false
				} else if (item.name === 'District') {
					item.isNullable = false
					item.isNodeDis = false
				}
			}
			if (row.isListField) {
				if (item.name === 'City') {
					item.isNodeListField = true
				} else if (item.name === 'District') {
					item.isNodeListField = true
				}
			}

			// if(row.isListField) {
			// 	item.isNodeListField = true
			// }else{
			// 	item.isNodeListField = false

			// }
		})
	} else if (row.name === 'Community') {
		tableData.value.forEach((item: any, index) => {
			if (row.isNullable) {
				if (item.name === 'City') {
					item.isNullable = true
					item.isNodeDis = true
					item.isListField = true
				} else if (item.name === 'District') {
					item.isNullable = true
					item.isNodeDis = true
					item.isListField = true
				} else if (item.name === 'Street') {
					item.isNullable = true
					item.isListField = true

					item.isNodeDis = true
				}
			} else {
				if (item.name === 'City') {
					item.isNullable = false
					item.isNodeDis = false
				} else if (item.name === 'District') {
					item.isNullable = false
					item.isNodeDis = false
				} else if (item.name === 'Street') {
					item.isNullable = false
					item.isNodeDis = false
				}
			}
			if (row.isListField) {
				if (item.name === 'City') {
					item.isNodeListField = true
				} else if (item.name === 'District') {
					item.isNodeListField = true
				} else if (item.name === 'Street') {
					item.isNodeListField = true
				}
			}
		})
	}

	const fieldMultiple = tableData.value.map((m: any) => m.relevanceCalculateRule).filter(Boolean)
	console.log(fieldMultiple)
	if (fieldMultiple.length > 0) {
		evalSelectIdCard(row)
	}
}
const screenHeight = window.innerHeight

const tableOffsetHeight = ref(-380)

const openChangeLedgerTime = ref(false)

const remindConfig = ref<any>({
	interval: 0,
	// 	dailyReminderTime: "00:00:00",
	//     dailyDeadlineTime: "23:59:59",
	weeklyReminderDayOfWeek: null,
	weeklyDeadlineDayOfWeek: null,
	monthlyReminderDay: 0,
	monthlyDeadlineDay: 0,
	quarterlyReminderDay1: null,
	quarterlyDeadlineDay1: null,
	quarterlyReminderDay2: null,
	quarterlyDeadlineDay2: null,
	quarterlyReminderDay3: null,
	quarterlyDeadlineDay3: null,
	quarterlyReminderDay4: null,
	quarterlyDeadlineDay4: null,
	yearlyReminderDay: null,
	yearlyDeadlineDay: null,
	upHalfMonthReminderTime: 0,
	upHalfMonthDateOnlyTime: 0,
	downHalfMonthReminderTime: 0,
	downHalfMonthDateOnlyTime: 0,
	upHalfYearReminderTime: null,
	upHalfYearDateOnlyTime: null,
	downHalfYearReminderTime: null,
	downHalfYearDateOnlyTime: null,

	weeklyStartDayOfWeek: null,
	monthlyStartDay: 0,
	quarterlyStartDay1: null,
	quarterlyStartDay2: null,
	quarterlyStartDay3: null,
	quarterlyStartDay4: null,
	yearlyStartDay: null,
	upHalfYearStartTime: 0,
	downHalfYearStartTime: 0,
	upHalfMonthStartTime: 0,
	downHalfMonthStartTime: 0,
})
const isUrging = ref(false)
const urgingList = ref([])
// 催办确定
const onUrging = () => {
	// if (!newLedgerName.value) return ElMessage.error('请填写业务表名称')
	// updateLedgerName(currentDialogRow.value.id, newLedgerName.value).then((res: any) => {
	// 	getLedgerData()
	// 	openChangeLedgerName.value = false
	// 	ElMessage.success('更新成功')
	// })
}
// 催办取消
const closeUrging = () => {
	isUrging.value = false
}
const onCloseTime = () => {
	console.log(remindConfig.value)
	console.log(saveData.value === undefined)
	console.log(form.reminderConfig.interval)
	console.log(noAdd)
	console.log(initInfo)

	if (saveData.value) {
		remindConfig.value = {...saveData.value}
		remindConfig.value.interval = form.reminderConfig.interval
	}
	if (saveData.value === undefined && route.query.type === 'add') {
		remindConfig.value = {}
		remindConfig.value.interval = form.reminderConfig.interval
	}
	if (
		(route.query.type === 'view' || route.query.type === 'edit') &&
		saveData.value === undefined
	) {
		getLedgerList('', 0, 1, viewId.value).then((res: any) => {
			remindConfig.value = res.data.reminderConfig
			remindConfig.value.interval = form.reminderConfig.interval
		})
	}
	openChangeLedgerTime.value = false
}

const onBlur = (type: any) => {
	if (type === 'upHalfMonthReminderTime') {
		if (!remindConfig.value.upHalfMonthReminderTime) {
			remindConfig.value.upHalfMonthReminderTime = 0
		}
	}
	if (type === 'upHalfMonthDateOnlyTime') {
		if (!remindConfig.value.upHalfMonthDateOnlyTime) {
			remindConfig.value.upHalfMonthDateOnlyTime = 0
		}
	}
	if (type === 'downHalfMonthReminderTime') {
		if (!remindConfig.value.downHalfMonthReminderTime) {
			remindConfig.value.downHalfMonthReminderTime = 0
		}
	}
	if (type === 'downHalfMonthDateOnlyTime') {
		if (!remindConfig.value.downHalfMonthDateOnlyTime) {
			remindConfig.value.downHalfMonthDateOnlyTime = 0
		}
	}
	if (type === 'upHalfMonthStartTime') {
		if (!remindConfig.value.upHalfMonthStartTime) {
			remindConfig.value.upHalfMonthStartTime = 0
		}
	}
	if (type === 'downHalfMonthStartTime') {
		if (!remindConfig.value.downHalfMonthStartTime) {
			remindConfig.value.downHalfMonthStartTime = 0
		}
	}
}
const checkRemindTiems = () => {
	let isPass = false
	if (remindConfig.value.interval == 2) {
		if (!remindConfig.value.weeklyDeadlineDayOfWeek) {
			isPass = true
		}
	} else if (remindConfig.value.interval == 4) {
		if (
			!remindConfig.value.quarterlyDeadlineDay1 ||
			!remindConfig.value.quarterlyDeadlineDay2 ||
			!remindConfig.value.quarterlyDeadlineDay3 ||
			!remindConfig.value.quarterlyDeadlineDay4
		) {
			isPass = true
		}
	} else if (remindConfig.value.interval == 3) {
		if (!remindConfig.value.monthlyDeadlineDay) {
			isPass = true
		}
	} else if (remindConfig.value.interval == 5) {
		if (!remindConfig.value.yearlyDeadlineDay) {
			isPass = true
		}
	} else if (remindConfig.value.interval == 6) {
		if (
			!remindConfig.value.upHalfMonthDateOnlyTime ||
			!remindConfig.value.downHalfMonthDateOnlyTime
		) {
			isPass = true
		}
	} else if (remindConfig.value.interval == 7) {
		if (
			!remindConfig.value.upHalfYearDateOnlyTime ||
			!remindConfig.value.downHalfYearDateOnlyTime
		) {
			isPass = true
		}
	}
	return isPass
}
const saveData = ref()
const onConfirmChangeLedgerTime = () => {
	if (checkRemindTiems()) {
		return ElMessage.warning('请将数据填报结束时间填写完整')
	}
	const newRemindConfig = ref<any>({})
	const flagConfig = ref<boolean>(true)
	const flagConfig1 = ref<boolean>(true)
	const flagConfig2 = ref<boolean>(true)
	const flagConfig3 = ref<boolean>(true)
	const flagConfig4 = ref<boolean>(true)
	const flagConfig5 = ref<boolean>(true)
	const flagConfig6 = ref<boolean>(true)
	const flagConfig7 = ref<boolean>(true)
	const flagConfig8 = ref<boolean>(true)
	const flagConfig10 = ref<boolean>(true)

	newRemindConfig.value.interval = remindConfig.value.interval
	if (remindConfig.value.interval == 2) {
		newRemindConfig.value.weeklyReminderDayOfWeek = remindConfig.value.weeklyReminderDayOfWeek
		newRemindConfig.value.weeklyDeadlineDayOfWeek = remindConfig.value.weeklyDeadlineDayOfWeek
		newRemindConfig.value.weeklyStartDayOfWeek = remindConfig.value.weeklyStartDayOfWeek
	}
	if (remindConfig.value.interval == 3) {
		newRemindConfig.value.monthlyReminderDay = remindConfig.value.monthlyReminderDay
		newRemindConfig.value.monthlyDeadlineDay = remindConfig.value.monthlyDeadlineDay
		newRemindConfig.value.monthlyStartDay = remindConfig.value.monthlyStartDay
		if (newRemindConfig.value.monthlyReminderDay) {
			flagConfig1.value =
				newRemindConfig.value.monthlyReminderDay > newRemindConfig.value.monthlyDeadlineDay
					? false
					: true
		}
	}
	if (remindConfig.value.interval == 4) {
		newRemindConfig.value.quarterlyReminderDay1 =
			remindConfig.value.quarterlyReminderDay1 &&
			remindConfig.value.quarterlyReminderDay1.substring(4).replace(/./, '0001-')

		newRemindConfig.value.quarterlyStartDay1 =
			remindConfig.value.quarterlyStartDay1 &&
			remindConfig.value.quarterlyStartDay1.substring(4).replace(/./, '0001-')

		newRemindConfig.value.quarterlyDeadlineDay1 =
			remindConfig.value.quarterlyDeadlineDay1 &&
			remindConfig.value.quarterlyDeadlineDay1.substring(4).replace(/./, '0001-')

		if (newRemindConfig.value.quarterlyReminderDay1) {
			flagConfig2.value =
				new Date(newRemindConfig.value.quarterlyReminderDay1) >
				new Date(newRemindConfig.value.quarterlyDeadlineDay1)
					? false
					: true
		}

		newRemindConfig.value.quarterlyReminderDay2 =
			remindConfig.value.quarterlyReminderDay2 &&
			remindConfig.value.quarterlyReminderDay2.substring(4).replace(/./, '0001-')

		newRemindConfig.value.quarterlyStartDay2 =
			remindConfig.value.quarterlyStartDay2 &&
			remindConfig.value.quarterlyStartDay2.substring(4).replace(/./, '0001-')

		newRemindConfig.value.quarterlyDeadlineDay2 =
			remindConfig.value.quarterlyDeadlineDay2 &&
			remindConfig.value.quarterlyDeadlineDay2.substring(4).replace(/./, '0001-')
		if (newRemindConfig.value.quarterlyReminderDay2) {
			flagConfig3.value =
				new Date(newRemindConfig.value.quarterlyReminderDay2) >
				new Date(newRemindConfig.value.quarterlyDeadlineDay2)
					? false
					: true
		}

		newRemindConfig.value.quarterlyReminderDay3 =
			remindConfig.value.quarterlyReminderDay3 &&
			remindConfig.value.quarterlyReminderDay3.substring(4).replace(/./, '0001-')

		newRemindConfig.value.quarterlyStartDay3 =
			remindConfig.value.quarterlyStartDay3 &&
			remindConfig.value.quarterlyStartDay3.substring(4).replace(/./, '0001-')

		newRemindConfig.value.quarterlyDeadlineDay3 =
			remindConfig.value.quarterlyDeadlineDay3 &&
			remindConfig.value.quarterlyDeadlineDay3.substring(4).replace(/./, '0001-')
		if (newRemindConfig.value.quarterlyReminderDay3) {
			flagConfig4.value =
				new Date(newRemindConfig.value.quarterlyReminderDay3) >
				new Date(newRemindConfig.value.quarterlyDeadlineDay3)
					? false
					: true
		}

		newRemindConfig.value.quarterlyReminderDay4 =
			remindConfig.value.quarterlyReminderDay4 &&
			remindConfig.value.quarterlyReminderDay4.substring(4).replace(/./, '0001-')

		newRemindConfig.value.quarterlyStartDay4 =
			remindConfig.value.quarterlyStartDay4 &&
			remindConfig.value.quarterlyStartDay4.substring(4).replace(/./, '0001-')

		newRemindConfig.value.quarterlyDeadlineDay4 =
			remindConfig.value.quarterlyDeadlineDay4 &&
			remindConfig.value.quarterlyDeadlineDay4.substring(4).replace(/./, '0001-')
		if (newRemindConfig.value.quarterlyReminderDay4) {
			flagConfig5.value =
				new Date(newRemindConfig.value.quarterlyReminderDay4) >
				new Date(newRemindConfig.value.quarterlyDeadlineDay4)
					? false
					: true
		}
	}
	if (remindConfig.value.interval == 5) {
		newRemindConfig.value.yearlyReminderDay =
			remindConfig.value.yearlyReminderDay &&
			remindConfig.value.yearlyReminderDay.substring(4).replace(/./, '0001-')

		newRemindConfig.value.yearlyStartDay =
			remindConfig.value.yearlyStartDay &&
			remindConfig.value.yearlyStartDay.substring(4).replace(/./, '0001-')

		newRemindConfig.value.yearlyDeadlineDay =
			remindConfig.value.yearlyDeadlineDay &&
			remindConfig.value.yearlyDeadlineDay.substring(4).replace(/./, '0001-')
		if (newRemindConfig.value.yearlyReminderDay) {
			flagConfig6.value =
				new Date(newRemindConfig.value.yearlyReminderDay) >
				new Date(newRemindConfig.value.yearlyDeadlineDay)
					? false
					: true
		}
	}
	if (remindConfig.value.interval == 6) {
		newRemindConfig.value.upHalfMonthReminderTime = remindConfig.value.upHalfMonthReminderTime
		newRemindConfig.value.upHalfMonthStartTime = remindConfig.value.upHalfMonthStartTime

		newRemindConfig.value.upHalfMonthDateOnlyTime = remindConfig.value.upHalfMonthDateOnlyTime
		newRemindConfig.value.downHalfMonthReminderTime =
			remindConfig.value.downHalfMonthReminderTime
		newRemindConfig.value.downHalfMonthStartTime = remindConfig.value.downHalfMonthStartTime

		newRemindConfig.value.downHalfMonthDateOnlyTime =
			remindConfig.value.downHalfMonthDateOnlyTime
		if (newRemindConfig.value.upHalfMonthReminderTime) {
			flagConfig7.value =
				newRemindConfig.value.upHalfMonthReminderTime >
				newRemindConfig.value.upHalfMonthDateOnlyTime
					? false
					: true
		}
		console.log(newRemindConfig.value.upHalfMonthReminderTime)
		console.log(newRemindConfig.value.upHalfMonthDateOnlyTime)

		console.log(newRemindConfig.value.downHalfMonthReminderTime)
		console.log(newRemindConfig.value.downHalfMonthDateOnlyTime)

		if (newRemindConfig.value.downHalfMonthReminderTime) {
			flagConfig8.value =
				newRemindConfig.value.downHalfMonthReminderTime >
				newRemindConfig.value.downHalfMonthDateOnlyTime
					? false
					: true
		}
	}

	if (remindConfig.value.interval == 7) {
		// upHalfYearReminderTime: '',
		// upHalfYearDateOnlyTime: '',
		// downHalfYearReminderTime:'',
		// downHalfYearDateOnlyTime: '',
		newRemindConfig.value.upHalfYearReminderTime =
			remindConfig.value.upHalfYearReminderTime &&
			remindConfig.value.upHalfYearReminderTime.substring(4).replace(/./, '0001-')
		newRemindConfig.value.upHalfYearStartTime =
			remindConfig.value.upHalfYearStartTime &&
			remindConfig.value.upHalfYearStartTime.substring(4).replace(/./, '0001-')
		newRemindConfig.value.upHalfYearDateOnlyTime =
			remindConfig.value.upHalfYearDateOnlyTime &&
			remindConfig.value.upHalfYearDateOnlyTime.substring(4).replace(/./, '0001-')
		if (newRemindConfig.value.upHalfYearReminderTime) {
			flagConfig.value =
				new Date(newRemindConfig.value.upHalfYearReminderTime) >
				new Date(newRemindConfig.value.upHalfYearDateOnlyTime)
					? false
					: true
		}

		newRemindConfig.value.downHalfYearReminderTime =
			remindConfig.value.downHalfYearReminderTime &&
			remindConfig.value.downHalfYearReminderTime.substring(4).replace(/./, '0001-')

		newRemindConfig.value.downHalfYearStartTime =
			remindConfig.value.downHalfYearStartTime &&
			remindConfig.value.downHalfYearStartTime.substring(4).replace(/./, '0001-')
		newRemindConfig.value.downHalfYearDateOnlyTime =
			remindConfig.value.downHalfYearDateOnlyTime &&
			remindConfig.value.downHalfYearDateOnlyTime.substring(4).replace(/./, '0001-')
		if (newRemindConfig.value.downHalfYearReminderTime) {
			flagConfig10.value =
				new Date(newRemindConfig.value.downHalfYearReminderTime) >
				new Date(newRemindConfig.value.downHalfYearDateOnlyTime)
					? false
					: true
		}
	}

	if (!flagConfig1.value) {
		return ElMessage.warning('结束时间不得小于提醒时间')
	}
	if (!flagConfig2.value) {
		return ElMessage.warning('结束时间不得小于提醒时间1')
	}
	if (!flagConfig3.value) {
		return ElMessage.warning('结束时间不得小于提醒时间2')
	}
	if (!flagConfig4.value) {
		return ElMessage.warning('结束时间不得小于提醒时间3')
	}
	if (!flagConfig5.value) {
		return ElMessage.warning('结束时间不得小于提醒时间4')
	}
	if (!flagConfig6.value) {
		return ElMessage.warning('结束时间不得小于提醒时间')
	}
	if (!flagConfig7.value) {
		return ElMessage.warning('结束时间不得小于提醒时间')
	}
	if (!flagConfig8.value) {
		return ElMessage.warning('结束时间不得小于提醒时间')
	}
	if (!flagConfig10.value) {
		return ElMessage.warning('结束时间不得小于提醒时间')
	}

	console.log(flagConfig.value)
	if (!flagConfig.value) {
		return ElMessage.warning('结束时间不得小于提醒时间')
	}
	saveData.value = {...newRemindConfig.value}
	form.reminderConfig = {...form.reminderConfig, ...newRemindConfig.value}
	openChangeLedgerTime.value = false
}

const openTips = () => {
	if (!remindConfig.value.interval) return ElMessage.warning(`请先设置更新周期`)
	openChangeLedgerTime.value = true
}

const onReminderConfigChange = () => {
	remindConfig.value.interval = form.reminderConfig.interval
	form.cycle = form.reminderConfig.interval
}

const containerRef = ref<any>(null)
const isScrolling = ref(false)
const handleScroll = () => {
	const tableWrapper = containerRef.value?.$el
	const tableHeader = block.value?.$el.querySelector('.el-table__header-wrapper')
	isScrolling.value = true
	if (!tableHeader || !tableWrapper) {
		// console.error('Table header or wrapper not found')
		return
	}
	const tableRect = tableWrapper.getBoundingClientRect()
	const tableOffsetTop = tableRect.top - 28
	const scrollTop = window.pageYOffset || document.documentElement.scrollTop
	const tableHeaderRect = tableHeader.getBoundingClientRect()
	if (scrollTop > tableOffsetTop) {
		tableHeader.style.position = 'fixed'
		tableHeader.style.top = `86px`
		tableHeader.style.zIndex = '1000'
		tableHeader.style.width = `${tableHeaderRect.width}px`
	} else {
		tableHeader.style.position = 'relative'
		tableHeader.style.top = ''
		tableHeader.style.zIndex = ''
		tableHeader.style.width = ''
	}
}
const detList = ref()
const getinfo = async () => {
	if (!route.query.id) return
	const id = route.query.id
	let res = await getDetailByLedgerId(id as any)
	detList.value = {
		label: res.data.ledgerType.name,
		value: res.data.ledgerType.id,
	}
	console.log(detList.value, '123123111')
}
onMounted(() => {
	if (route.query.type === ViewType.View) {
		// getHeader()
	}
	nextTick(() => {
		const elemcentHeight = block.value
		// console.log(elemcentHeight)
		// console.log(block.value.$el.querySelector('.el-table__header-wrapper').getBoundingClientRect().top)

		window.addEventListener('scroll', handleScroll, true)
	})
	onFlowResize()
})
onBeforeUnmount(() => {
	window.removeEventListener('scroll', handleScroll)
})

const addForm = ref()

const intervalOptions = [
	{value: 1, label: '周一'},
	{value: 2, label: '周二'},
	{value: 3, label: '周三'},
	{value: 4, label: '周四'},
	{value: 5, label: '周五'},
	{value: 6, label: '周六'},
	{value: 7, label: '周日'},
]
const selectShow = ref(null)

const evalSelect = (item: any) => {
	const fields = tableData.value.filter(
		(f: any) => item.fieldMultipleDto?.multipleArrId === (f?.id || f?.__id)
	)
	const fieldMultiple = tableData.value.filter((m: any) => m.fieldMultipleDto).filter(Boolean)
	const target = fieldMultiple.some(
		(f: any) => item?.fieldMultipleDto?.multipleArrId === f.fieldMultipleDto.multipleArrId
	)
	const targetArr = fieldMultiple.filter(
		(f: any) => item?.fieldMultipleDto?.multipleArrId === f.fieldMultipleDto.multipleArrId
	)

	console.log(item)
	console.log(fields)
	console.log(fieldMultiple)
	console.log(target)
	console.log(targetArr)

	if (fields) {
		const Indexs = tableData.value.findIndex(
			(f: any) => (f?.id || f?.__id) === (fields[0].id || fields[0].__id)
		)
		if (target) {
			const targetIsQueryField = targetArr.some((f: any) => f.isQueryField)
			console.log(targetIsQueryField)
			if (targetIsQueryField) {
				tableData.value[Indexs].isQueryField = true
				tableData.value[Indexs].isCase = true
			} else {
				tableData.value[Indexs].isQueryField = false
				tableData.value[Indexs].isCase = false
			}
		}
	}
}
const evalSelectIdCard = (item: any) => {
	const fields = tableData.value.filter(
		(f: any) => item.relevanceCalculateRule?.relevanceFieldId === (f?.id || f?.__id)
	)
	const fieldMultiple = tableData.value
		.filter((m: any) => m.relevanceCalculateRule)
		.filter(Boolean)
	const target = fieldMultiple.some(
		(f: any) =>
			item?.relevanceCalculateRule?.relevanceFieldId ===
			f.relevanceCalculateRule.relevanceFieldId
	)
	const targetArr = fieldMultiple.filter(
		(f: any) =>
			item?.relevanceCalculateRule?.relevanceFieldId ===
			f.relevanceCalculateRule.relevanceFieldId
	)

	if (fields) {
		const Indexs = tableData.value.findIndex(
			(f: any) =>
				(f?.id || f?.__id) ===
				((fields[0] && fields[0].id) || (fields[0] && fields[0].__id))
		)
		if (target) {
			const targetIsQueryField = targetArr.some((f: any) => f.isNullable)
			console.log(targetIsQueryField)
			if (targetIsQueryField) {
				tableData.value[Indexs].isNullable = true
				tableData.value[Indexs].isIdCase = true
				tableData.value[Indexs].isIdSelect = true
			} else {
				tableData.value[Indexs].isNullable = false
				tableData.value[Indexs].isIdSelect = false
				tableData.value[Indexs].isIdCase = false
			}
		}
	}
}
const checkBoxChange = (row: any) => {
	console.log('checkBoxChange_isQueryField', row)
	console.log(tableData.value)
	const fieldMultiple = tableData.value.map((m: any) => m.fieldMultipleDto).filter(Boolean)
	console.log(fieldMultiple)
	if (fieldMultiple.length > 0) {
		evalSelect(row)
	}
}

const phoneModal = ref(null)
const datas = ref([])
const isOpenView = ref<any>(false)
const CreateLedger = ref<any>(null)
const currentfield = ref<any>()
const allfield = ref<any>()
const phoneData = ref([])
const tableHeaderData = ref([])
const tableHeader = ref([])
const maxWidth = ref(window.innerWidth * 0.8) // 初始宽度为屏幕宽度的80%
const isShowPreview = ref<any>(false)
const currentY = ref(8)

const sortTree = (tree: any) => {
	return tree
		.sort((a: any, b: any) => a.sort - b.sort)
		.filter((f: any) => f.isListField)
		.map((node: any) => ({...node, children: node.children ? sortTree(node.children) : []}))
}
function processTree(tree, parentNames = '') {
	const result = []

	for (const node of tree) {
		const displayName = node.displayName || node.label
		// 构建当前节点的完整名称（包含所有父级名称）
		const currentNames = parentNames ? `${parentNames} - ${displayName}` : displayName
		// 检查当前节点是否有符合条件的clientSettings
		const filteredSettings = node.clientSettings?.filter((s) => s.isDisplay)

		if (filteredSettings?.length) {
			// 如果有符合条件的clientSettings，则处理该节点
			const newNode = {...node} // 创建新对象以避免直接修改原始数据
			delete newNode.children // 如果不需要子节点，则删除它们
			newNode.names = currentNames // 设置names属性

			// 保留筛选出的clientSettings（如果需要）
			newNode.clientSettings = filteredSettings.map((s) => ({...s, names: newNode.names}))

			result.push(newNode) // 将处理后的节点添加到结果数组中
		}

		// 如果节点有子节点，则递归处理它们，并传递累积的父级名称
		if (node.children) {
			const childResults = processTree(node.children, currentNames)
			result.push(...childResults) // 将子节点的结果添加到结果数组中
		}
	}

	return result
}

const changeLedgerTemplateVisible = (val: any) => {
	console.log(val)
	isShowPreview.value = val
}

const getHeader = () => {
	datas.value = []
	console.log()
	const ledgerId = route.query.id
	getDetailByLedgerId(ledgerId as any).then((res: any) => {
		console.log(res.data)
		const {data} = res
		currentfield.value = data
		currentfield.value?.tableInfo?.tableFieldGroups.forEach((item: any) => {
			if (item.tableFields) {
				item.tableFields.forEach((item2: any) => {
					if (item2.id) {
						item2.oldId = item2.id
					}
					item2.displayName = currentfield.value.tableInfo.fields.filter(
						(f: any) => f.id === item2.id
					)[0]?.displayName
				})
			}
		})
		allfield.value = currentfield.value?.tableInfo?.fields.sort(
			(a: any, b: any) => a.sort - b.sort
		)
		currentfield.value.tableInfo.tableFieldGroups =
			currentfield.value.tableInfo.tableFieldGroups.sort((a: any, b: any) => a.sort - b.sort)
		currentfield.value?.tableInfo?.tableFieldGroups.forEach((item: any) => {
			item.tableFields.sort((a: any, b: any) => a.sort - b.sort)
		})
		const groups: any = []
		console.log(
			currentfield.value.tableInfo.fields,
			currentfield.value.tableInfo.tableFieldGroups,
			'最初数据'
		)
		currentfield.value?.tableInfo?.tableFieldGroups.forEach((item: any, index: any) => {
			if (item.tableFields) {
				groups.push(
					Object.assign(item, {
						id: item.id,
						label: item.name,
						parentId: item.parentId,
						isDelete: true,
						ischange: true,
						changeType: false,
						isListField: true,
						tableFields: toRaw(item.tableFields),
					})
				)
				item.tableFields.forEach((tf: any) => {
					// 跟字段的isListField同步
					tf.isListField = currentfield.value.tableInfo.fields.filter(
						(f: any) => tf.id == f.id
					)[0]?.isListField
					// 不显示则不push进去
					if (tf.isListField) {
						groups.push({
							label: tf.displayName,
							field: tf.name,
							parentId: item.id,
							id: tf.id,
							...tf,
							isDelete: false,
							ischange: false,
							changeType: false,
						})
					}
				})
			} else {
				groups.push({
					label: item.name,
					id: item.id,
					parentId: item.parentId,
					isDelete: false,
					ischange: false,
					changeType: false,
				})
			}
		})

		const LedgerGroup = useArrayToTree(
			groups,
			'id',
			'parentId',
			'label',
			false,
			'children',
			true
		)
		currentfield.value?.tableInfo?.fields.forEach((item: any, index: any) => {
			console.log(item, item.tableFieldGroupId, 'item.tableFieldGroupId')
			// 有没有父id 并且 需要显示
			if (item.tableFieldGroupId == null && item.isListField) {
				console.log(item.tableFieldGroupId)
				LedgerGroup.push({
					...item,
					label: item.displayName,
					field: item.name,
					isDelete: false,
					ischange: false,
					changeType: false,
				})
			} else if (item.tableFieldGroupId != null) {
				// 父id不为空  并且 需要显示
				if (!item.isListField) {
					LedgerGroup.push({
						...item,
						label: item.displayName,
						field: item.name,
						isDelete: false,
						ischange: false,
						changeType: false,
						sort: 999,
					})
				}
			}
		})

		console.log(LedgerGroup, 'onActivated获取生成好的datas.value')

		datas.value = computed(() => {
			return sortTree([...LedgerGroup])
		})
		datas.value = datas.value.value
	})
}
const getCurrentDialogXY = (event: Event) => {
	const target = event as HTMLElement
	console.log(containerRef.value?.$el.getBoundingClientRect())
	const {bottom, right, height, top, y} = containerRef.value?.$el.getBoundingClientRect()
	return {bottom, right, height, y}
}
// 生成移动端预览
const createPhoneViews = () => {
	console.log(datas.value)
	const {right, bottom, height, y} = getCurrentDialogXY(containerRef.value?.$el)
	console.log()
	if (isScrolling) {
		currentY.value = height / y + (14 + height / y) - 2
	}
	console.log(containerRef.value?.$el.getBoundingClientRect().top)
	console.log(y)

	const res = processTree(datas.value)
	phoneData.value = res
	maxWidth.value = window.innerWidth * 0.5
	isOpenView.value = true
}

const ClosePhoneModal = () => {
	isOpenView.value = false
}

const isOpenFlow = ref(false)
const openFlowModal = () => {
	isOpenFlow.value = true
}
const onIsOpenFlow = () => {
	isOpenFlow.value = false
}
const openIdCardModal = ref(false)
const onOpenIdCard = (row: any) => {
	if (row.displayName === '') {
		ElMessage.warning('请先输入列表名称')
		return
	}

	currentRow.value = row
	openIdCardModal.value = true
	calceSelect.value = tableData.value
		.filter((item: any) => !item.calculateRule)
		.map((item: any) => {
			let obj: any = {}
			if (item.type === 'identification_number') {
				obj.label = `${item.displayName}`
				obj.value = `${item.id || item.__id}`
			}
			return {...obj}
		})
		.filter((item: any) => item && item.label)
		.filter((item: any) => item.label !== `${row.displayName}`)
		.filter((item: any) => item.value !== `${row.id || row.__id}`)
	console.log(row)
	console.log(currentRow.value)
}

const clickConfirmIdCard = (checked: any) => {
	console.log(checked)
	if (!checked || !checked?.relevanceFieldId) {
		currentRow.value.relevanceCalculateRule = null
	}
	if (checked?.relevanceFieldId === undefined) {
		currentRow.value.relevanceCalculateRule = null
	}
	currentRow.value.relevanceCalculateRule = checked ?? null
	let IdCardString = tableData.value
		.filter(
			(item: any) =>
				(item.type === 'age' || item.type === 'birthday' || item.type === 'sex') &&
				item.relevanceCalculateRule?.relevanceFieldId
		)
		.map((item: any) => {
			return (
				item.relevanceCalculateRule?.relevanceFieldId &&
				item.relevanceCalculateRule?.relevanceFieldId.split(' ')
			)
		})
		.flat(2)
	tableData.value.forEach((item: any) => {
		let str = item.id || item.__id

		if (IdCardString.includes(item.id || item.__id)) {
			item.isIdSelect = true
			item.isIdCase = true
		} else {
			item.isIdSelect = false
			item.isIdCase = false
		}
	})

	openIdCardModal.value = false
}

const onDataIdCardCancel = () => {
	if (
		currentRow.value.relevanceCalculateRule?.RelevanceFieldId === '' ||
		!currentRow.value.hasOwnProperty('relevanceCalculateRule')
	) {
		currentRow.value.relevanceCalculateRule = null
	}
	openIdCardModal.value = false
}

const flowRef = ref()
const flowHeight = ref(0)
const curEnterNode: any = ref(null)
const onFlowResize = () => {
	const h = document.body.offsetHeight - 90
	flowHeight.value = h
}
const onOpenFlow = () => {
	flowRef.value.getInstance().fitView()
}

const onFlowNodeMouseEnter = (flowNodeInfo: any, currentNode: any) => {
	console.log(currentNode)
	curEnterNode.value = currentNode
}

const drawer = ref(false)

const isOpenViewEdit = ref(false)
const conditionForm2: any = ref({})
const formRules2 = {
	flowname: [{required: true, message: '请输入说明', trigger: 'blur'}],
	radio: [{required: true, message: '请选择审核结果', trigger: 'change'}],
}
const ladgerFormArray2 = [
	{
		prop: 'radio',
		label: '审核结果',
		type: 'radio',
		options: [
			{
				label: '通过',
				value: 0,
			},
			{
				label: '不通过',
				value: 1,
			},
		],
	},

	{prop: 'flowname', type: 'textarea', label: '说明', placeholder: '请输入说明'},
]
const formSubmit2 = (formRef: any) => {
	// 表单验证
	isOpenViewEdit.value = false
}
const refs = ref({
	ledgerShow: false,
	visibleLedgerType: false,
})
const openLadgerTypeTitle = ref('')
const tableComp = ref()

const openLadgerType = (val?: any) => {
	nextTick(() => {
		defaultCheckedData.value = []
	})
	openLadgerTypeTitle.value = '新增所属部门'
	refs.value.visibleLedgerType = true
	nextTick(() => {
		tableComp.value[0].getList(defaultCheckedData.value)
	})
}
const formType: any = ref({
	typeName: '',
	departmentId: '',
})
function departmentListChange(e: any) {
	formType.value.typeName = e.map((v: any) => v.name)
	formType.value.departmentId = e.map((v: any) => v.id)
}
// 关闭添加
const closed = () => {
	refs.value.visibleLedgerType = false
	formType.value = {}
	addForm.value.clearValidate()
}
const ladgerFormArray: any = [
	{type: 'input', title: '所属部门名称', field: 'typeName', default: ''},
]
const ladgerFormRules = {
	typeName: [{required: true, message: '请选择所属部门名称', trigger: 'blur'}],
}

const itemsIndex = ref(0)

//跑道列表
const items = computed(() => {
	return JSON.parse(JSON.stringify(RunwayList))
})
// 添加类型
const formSubmit = (formRef: FormInstance | undefined) => {
	// 表单验证
	formRef
		?.validate()
		.then((validate: boolean) => {
			console.log('验证通过:', validate, items.value[itemsIndex.value])
			let data = {
				name: formType.value.typeName[0],
				sort: 0,
				bindDepartmentId: formType.value.departmentId[0],
				// runway: items.value[itemsIndex.value].name,
			}
			if (formType.value.LedgerTypeId) {
				putLedgerType(data, formType.value.LedgerTypeId).then((res: any) => {
					if (res.data.id) {
						ElNotification({
							type: 'success',
							title: '提示',
							message: '保存成功',
						})
						ledgerRunwayList()

						closed()
						source.pullLedgerType()
					} else {
						ElNotification({
							title: '警告',
							type: 'warning',
							message: '数据异常',
						})
					}
				})
			} else {
				createLedgerType(data).then((res: any) => {
					if (res.data.id) {
						ElNotification({
							type: 'success',
							title: '提示',
							message: '保存成功',
						})

						ledgerRunwayList()
						closed()
						source.pullLedgerType()
					} else {
						ElNotification({
							title: '警告',
							type: 'warning',
							message: '数据异常',
						})
					}
				})
			}
		})
		.catch(() => {
			console.log('验证失败')
		})
}

const addNumber = ref(1)
const onBlurAdd = (event: any) => {
	console.log(event)
	event.stopPropagation()
	if (!addNumber.value) {
		addNumber.value = 1
	}
}

const addTableeDataNoen = (event: any) => {
	if (addNumber.value <= 0) {
		return ElMessage.warning('至少输入1个数据项')
	}
	for (let index = 0; index < addNumber.value; index++) {
		addTableData({}, false)
	}
}
const formRefSearch = ref<any>()
// 更改记录
const ischangeRecord = ref(false)
const setChangeRecord = () => {
	ischangeRecord.value = true
}
</script>

<template>
	<div>
		<Block
			:enable-expand-content="false"
			:enable-close-button="true"
			:title="
				viewType === ViewType.Add
					? '创建业务表'
					: viewType === ViewType.Edit
					? '编辑业务表'
					: '查看配置'
			"
		>
			<template #topRight>
				<el-button
					v-if="viewType === ViewType.Edit || viewType === ViewType.View"
					link
					type="primary"
					size="small"
					style="text-decoration: underline; margin-left: 10px"
					@click="onDownloadTemplate"
				>
					点击下载模版
				</el-button>
				<el-button
					v-if="viewType === ViewType.Edit || viewType === ViewType.View"
					type="primary"
					size="small"
					style="margin-left: 10px"
					@click="setChangeRecord"
				>
					更改记录
				</el-button>

				<el-button
					v-if="viewType === ViewType.Add || viewType === ViewType.Edit"
					type="primary"
					@click="onNextStep"
					size="small"
					style="margin-left: 10px"
				>
					下一步
				</el-button>

				<span v-if="cash === 'fake'">
					<el-button
						type="primary"
						@click="drawer = true"
						size="small"
						style="margin-left: 10px"
					>
						审核记录
					</el-button>
				</span>
				<span v-if="cash === 'fakes'">
					<el-button
						type="primary"
						@click="drawer = true"
						size="small"
						style="margin-left: 10px"
					>
						审核记录
					</el-button>
					<el-button
						type="primary"
						@click="isOpenViewEdit = true"
						size="small"
						style="margin-left: 10px"
					>
						审核
					</el-button>
				</span>
			</template>
			<div class="create-container">
				<div class="base-form">
					<el-form
						label-width="100px"
						:rules="formRule"
						:model="form"
						ref="formRefSearch"
					>
						<el-form-item label="业务表名称:" prop="name">
							<el-input
								v-model="form.name"
								placeholder="请输入业务表名称"
								:disabled="viewType === ViewType.View"
							/>
						</el-form-item>
						<!-- <el-form-item label="时间joker:" prop="timeJoker"> -->
						<!-- <TimePicker
								ref="quarterPickerRef"
								v-model="form.quarterValue"
								format="YYYY年第q季度"
								value-format="YYYY-MM-DD HH:mm:ss"
							/> -->
						<!-- <el-date-picker
									format="YYYY年"
									v-model="form.quarterValue"
									type="year"
									value-format="YYYY-MM-DD HH:mm:ss"
								/> -->
						<!-- </el-form-item> -->

						<el-form-item label="所属板块:" prop="runway">
							<el-select
								clearable
								v-model="form.runway"
								placeholder="请选择所属板块"
								:disabled="viewType === ViewType.View"
							>
								<el-option
									v-for="item of RunwayList.filter((i) => i.index !== 0)"
									:key="item.label"
									:label="item.label"
									:value="item.label"
								/>
							</el-select>
						</el-form-item>
						<el-form-item label="发布部门:" prop="ledgerTypeId">
							<el-select
								clearable
								filterable
								v-model="form.ledgerTypeId"
								placeholder="请选择发布部门"
								:disabled="viewType === ViewType.View"
							>
								<el-option
									v-for="item of departmentFilter"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
							<el-button
								style="margin-left: 20px"
								type="primary"
								:disabled="viewType === ViewType.View"
								@click="openLadgerType"
								>新增发布部门</el-button
							>
							<!-- <DropdownTree
								:data="dropdwonTreeRunway"
								:defaultData="[form.ledgerTypeId]"
								:single="true"
								:disabled="viewType === ViewType.View || viewType === ViewType.Edit"
								@selected="onDropdownTreeSelected"
							></DropdownTree> -->
						</el-form-item>
						<el-form-item label="更新周期:" prop="cycle">
							<el-select
								v-model="form.reminderConfig.interval"
								placeholder="请选择数据更新周期"
								@change="onReminderConfigChange"
								:disabled="viewType === ViewType.View"
							>
								<el-option
									v-for="item of ReminderCycleList.filter(
										(v) => v.label !== '全部'
									)"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>
						<div class="tips">
							<el-form-item style="margin-right: 20px">
								<el-button
									v-if="form.reminderConfig.interval !== 0"
									type="primary"
									@click="openTips"
									size="default"
									style="margin-right: 20px"
								>
									设置提醒
								</el-button>
							</el-form-item>
						</div>
						<el-form-item
							label="数据权限:"
							prop="isDepartmentalAuthorization"
							style="width: 100%"
						>
							<el-checkbox
								v-model="form.isDepartmentalAuthorization"
								:disabled="viewType === ViewType.View"
							>
								<span style="font-size: 13px; font-weight: normal">按部门控制</span>
							</el-checkbox>
						</el-form-item>
						<!-- <el-form-item label="管理部门:" prop="departmentIds" style="width: 100%">
							<DepartmentFavoriteComp
								v-if="showDepartmentCheck"
								type="modal"
								:disabled="viewType === ViewType.View"
								placeholder="请选择管理部门"
								:defaultCheckedData="defaultCheckedData"
								@change="onDepartmentFavoriteChange"
							></DepartmentFavoriteComp>
						</el-form-item> -->
						<el-form-item
							label="填报说明:"
							prop="updateDescription"
							style="width: 100%"
						>
							<el-input
								v-model="form.updateDescription"
								type="textarea"
								:placeholder="viewType === ViewType.View ? '--' : '请输入填报说明'"
								style="width: 100%"
								:disabled="viewType === ViewType.View"
							/>
						</el-form-item>
						<!-- <el-form-item label="上传模版:">
							<el-upload
								ref="uploadRef"
								class="upload-template"
								accept=".xlsx"
								method="put"
								:action="uploadUrl"
								:auto-upload="false"
								:on-change="onUploadTemplate"
								:on-success="onUploadSuccess"
								:disabled="viewType === ViewType.View"
							>
								<template #trigger>
									<el-button>点击选择模版文件(.xlsx)</el-button>
								</template>

								<template #tip>
									<div class="el-upload__tip">( 支持扩展名：.xlsx )</div>
								</template>
							</el-upload>
						</el-form-item> -->
						<!-- <el-form-item label="填报要求:" prop="auditType">
							<el-select v-model="form.auditType" :disabled="viewType === ViewType.View">
								<el-option
									v-for="item in [
										{label: '填报部门内部审核', value: 0},
										{label: '多级审核', value: 1},
									]"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
							<el-tooltip
								class="box-item"
								effect="dark"
								placement="top"
								content="多级审核会根据业务表授权顺序进行。<br />填报部门提交数据先经部门内部审核，再交由授权部门依次审核。"
								raw-content
							>
								<el-icon ml-5px>
									<QuestionFilled />
								</el-icon>
							</el-tooltip>
						</el-form-item> -->

						<el-form-item
							label="填报流程:"
							prop="workflowSchemeCode"
							style="width: 100%"
						>
							<el-select
								clearable
								v-model="form.workflowSchemeCode"
								:disabled="viewType === ViewType.View"
							>
								<el-option
									v-for="item in poerssData"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
							<el-button
								style="margin-left: 20px; padding: 0 10px"
								type="text"
								:disabled="viewType === ViewType.View"
								v-if="isShowSelect"
								@click="openFlowModal"
								>查看流程详情</el-button
							>
							<el-button
								style="margin-left: 20px"
								type="primary"
								:disabled="viewType === ViewType.View"
								@click="router.push('/flow/step?category=业务表流程')"
								>新建流程</el-button
							>
						</el-form-item>

						<el-form-item label="业务表类型:" prop="businessLedgerCategory">
							<el-select v-model="form.businessLedgerCategory">
								<el-option
									v-for="item in [
										{label: '统计表', value: 1},
										{label: '明细表', value: 2},
									]"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>

						<el-form-item
							v-if="superAdmin && form.businessLedgerCategory === 2"
							label="导出少量数据审核流程:"
							prop="exportSmallDataWorkflowSchemeCode"
							label-width="184px"
						>
							<el-select
								clearable
								v-model="form.exportSmallDataWorkflowSchemeCode"
								:disabled="viewType === ViewType.View"
							>
								<el-option
									v-for="item in poerssDataExport"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>

						<el-form-item
							v-if="superAdmin && form.businessLedgerCategory === 2"
							label="导出大量数据审核流程:"
							prop="exportLargeDataWorkflowSchemeCode"
							label-width="184px"
						>
							<el-select
								clearable
								v-model="form.exportLargeDataWorkflowSchemeCode"
								:disabled="viewType === ViewType.View"
							>
								<el-option
									v-for="item in poerssDataExport"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</el-form-item>
					</el-form>
				</div>
			</div>
		</Block>
		<Block
			ref="containerRef"
			:enable-expand-content="false"
			:enableBackButton="false"
			:auto-height="true"
			:enable-close-button="false"
			:title="'业务表配置'"
		>
			<template #topRight>
				<el-button
					type="primary"
					size="small"
					v-if="viewType === ViewType.View"
					@click="isShowPreview = true"
				>
					PC端预览
				</el-button>
				<el-button
					type="primary"
					size="small"
					v-if="viewType === ViewType.View"
					@click="createPhoneViews"
				>
					移动端预览
				</el-button>
				<el-button
					v-if="viewType !== ViewType.View"
					type="primary"
					size="small"
					:disabled="viewType !== ViewType.Add"
					@click="openSelectLedgerTemplateTableList = true"
				>
					业务表模板添加
				</el-button>
				<!-- 新需求--- 隐藏 -->
				<!-- <el-button
					type="primary"
					size="small"
					:disabled="viewType === ViewType.View"
					@click="openFromDataSource = true"
				>
					来源于数据集添加
				</el-button> -->
				<!-- <el-button
					type="primary"
					size="small"
					:disabled="viewType === ViewType.View"
					@click="addTableData"
				>
					无数据来源添加
				</el-button> -->
				<el-button
					v-if="viewType !== ViewType.View"
					type="primary"
					size="small"
					:disabled="viewType === ViewType.View"
					@click="onShowTabaleData"
				>
					{{ showRows ? '收起固定字段' : '展开固定字段' }}
					<el-icon v-if="showRows">
						<ArrowUp />
					</el-icon>
					<el-icon v-else>
						<ArrowDown />
					</el-icon>
				</el-button>
			</template>
			<div class="create-container">
				<div class="data-config">
					<BaseTableComp
						ref="block"
						:auto-height="true"
						:colData="colData"
						:data="displayedTableData"
						:checkbox="false"
						:show-border="false"
						:visible-header="false"
						:visible-page="false"
						:visible-search="false"
						:visible-export="false"
						:visible-setting="false"
						:visible-index="false"
						:buttons="viewType === ViewType.View ? [] : buttons"
						@click-button="removeTableData"
					>
						<template #isUnique="{rowData}">
							<el-checkbox
								v-model="rowData.isUnique"
								@change="isUniqueChange(rowData)"
								:disabled="
									viewType === ViewType.View ||
									// (rowData.displayName === '所属部门' && rowData.name === 'Department') ||
									// (rowData.displayName === '所属城市' && rowData.name === 'City') ||
									// (rowData.displayName === '所属区县' && rowData.name === 'District') ||
									// (rowData.displayName === '所属街镇' && rowData.name === 'Street') ||
									// (rowData.displayName === '所属村社' && rowData.name === 'Community') ||
									// (rowData.displayName === '更新时间' && rowData.name === 'UpdateTime') ||
									// rowData.displayName === '更新日期' ||
									// (rowData.displayName === '填报人' && rowData.name === 'Informant') ||
									// (rowData.displayName === '编辑人'&&rowData.name === 'Editor') ||
									// (rowData.displayName === '数据来源' && rowData.name === 'DataSource') ||
									// rowData.name !== null ||
									rowData.type == 'images' ||
									rowData.type == 'attachments'
									// (rowData.lastOnlineTime !== 'isfield' && rowData.name !== null&& rowData.isString)

									// viewType === ViewType.Edit
								"
							></el-checkbox>
						</template>
						<template #displayName="{rowData}">
							<el-input
								v-model="rowData.displayName"
								placeholder="请输入列表名"
								size="small"
								:disabled="
									viewType === ViewType.View ||
									(rowData.displayName === '所属部门' &&
										rowData.name === 'Department') ||
									(rowData.displayName === '所属城市' &&
										rowData.name === 'City') ||
									(rowData.displayName === '所属区县' &&
										rowData.name === 'District') ||
									(rowData.displayName === '所属街镇' &&
										rowData.name === 'Street') ||
									(rowData.displayName === '所属村社' &&
										rowData.name === 'Community') ||
									(rowData.displayName === '更新时间' &&
										rowData.name === 'UpdateTime') ||
									rowData.displayName === '更新日期' ||
									(rowData.displayName === '填报人' &&
										rowData.name === 'Informant') ||
									(rowData.displayName === '编辑人' &&
										rowData.name === 'Editor') ||
									(rowData.displayName === '数据来源' &&
										rowData.name === 'DataSource')
								"
							></el-input>
						</template>

						<template #__tableDataSetStr="{rowData}">
							<el-input
								v-model="rowData.__tableDataSetStr"
								size="small"
								disabled
							></el-input>
						</template>

						<template #isNullable="{rowData}">
							<el-checkbox
								@change="isNullableChange(rowData)"
								v-model="rowData.isNullable"
								:disabled="
									viewType === ViewType.View ||
									(rowData.displayName === '所属部门' &&
										rowData.name === 'Department') ||
									// (rowData.displayName === '所属城市' && rowData.name === 'City') ||
									// (rowData.displayName === '所属区县' && rowData.name === 'District') ||
									// (rowData.displayName === '所属街镇' && rowData.name === 'Street') ||
									// (rowData.displayName === '所属村社' && rowData.name === 'Community') ||
									// (rowData.displayName === '更新时间' && rowData.name === 'UpdateTime') ||
									// (rowData.displayName === '填报人' && rowData.name === 'Informant') ||
									// (rowData.displayName === '编辑人'&&rowData.name === 'Editor') ||
									// (rowData.displayName === '数据来源' && rowData.name === 'DataSource') ||
									// rowData.type === 'attachments' ||
									// rowData.type === 'images' ||
									rowData.isNodeDis ||
									rowData.isIdCase ||
									rowData.isUnique
								"
							></el-checkbox>
						</template>

						<template #isListField="{rowData}">
							<div style="display: flex; align-items: center">
								<el-checkbox
									@change="isListFieldChange(rowData)"
									v-model="rowData.isListField"
									:disabled="
										viewType === ViewType.View ||
										rowData.isChange ||
										rowData.isSelect ||
										rowData.isNullable ||
										rowData.isUnique ||
										rowData.isNodeListField
										// (rowData.displayName === '所属城市' && rowData.name === 'City') ||
										// (rowData.displayName === '所属区县' && rowData.name === 'District') ||
										// (rowData.displayName === '所属街镇' && rowData.name === 'Street') ||
										// (rowData.displayName === '所属村社' && rowData.name === 'Community')
									"
								></el-checkbox>
								<template v-if="rowData.isListField">
									<!-- <span ml-15px>显示顺序:</span>
									<el-input
										v-model.number="rowData.sort"
										size="small"
										:disabled="viewType === ViewType.View"
										@focus="focusSort = rowData.sort"
										@change="onChangeSortInput(rowData)"
										style="margin-left: 5px; width: 30px"
									></el-input> -->
								</template>
							</div>
						</template>

						<template #isQueryField="{rowData}">
							<el-checkbox
								@change="checkBoxChange(rowData)"
								v-model="rowData.isQueryField"
								:disabled="
									viewType === ViewType.View ||
									// (rowData.displayName === '所属部门' &&
									// 	rowData.name === 'Department') ||
									// (rowData.displayName === '所属城市' &&
									// 	rowData.name === 'City') ||
									// (rowData.displayName === '所属区县' &&
									// 	rowData.name === 'District') ||
									// (rowData.displayName === '所属街镇' &&
									// 	rowData.name === 'Street') ||
									// (rowData.displayName === '所属村社' &&
									// 	rowData.name === 'Community') ||
									// (rowData.displayName === '更新时间' &&
									// 	rowData.name === 'UpdateTime') ||
									// (rowData.displayName === '填报人' &&
									// 	rowData.name === 'Informant') ||
									// (rowData.displayName === '编辑人' &&
									// 	rowData.name === 'Editor') ||
									// (rowData.displayName === '数据来源' &&
									// 	rowData.name === 'DataSource') ||
									// rowData.displayName === '更新日期' ||
									rowData.type == 'images' ||
									rowData.type == 'attachments' ||
									rowData.isCase
								"
							></el-checkbox>
						</template>
						<!-- // if (['string_500', 'string_0'].includes(row.type)) {
	// 	row.MaxLength = Number(row.type.split('_')[1])
	// } else {
	// 	row.MaxLength = 0
	// 	row.maxLength = 0
	// } -->

						<template #maxLength="{rowData}">
							<el-input
								v-model.number="rowData.maxLength"
								placeholder="请输入最大长度"
								:disabled="showMaxLength(rowData)"
								size="small"
							></el-input>
						</template>
						<template #type="{rowData}">
							<el-select
								v-if="
									rowData.lastOnlineTime !== 'isfield' &&
									rowData.name !== null &&
									!rowData.isString
								"
								ref="selectShow"
								v-model="rowData.type"
								placeholder="请选择类型"
								size="small"
								:disabled="
									viewType === ViewType.View ||
									// rowData.__isUpDate ||
									// !rowData.__notSource ||
									fixedFields.includes(rowData.name) ||
									rowData.isChange ||
									rowData.isSelect ||
									rowData.isIdSelect ||
									rowData.isCardSelect ||
									// (rowData.lastOnlineTime !== 'isfield'&&(rowData.type !== 'string')&&viewType === ViewType.Edit)
									(rowData.lastOnlineTime !== 'isfield' &&
										rowData.name !== null &&
										rowData.isString)
								"
								style="width: 200px"
								@change="onChangeDataType(rowData)"
							>
								<el-option-group
									v-for="group in DataNewTypeListOld"
									:key="group.label"
									:label="group.label"
								>
									<el-option
										v-for="item in group.options"
										:key="item.value"
										:label="item.name"
										:value="item.value"
										:disabled="item.disabled"
									/>
								</el-option-group>
							</el-select>
							<el-select
								v-else
								ref="selectShow"
								v-model="rowData.type"
								placeholder="请选择类型"
								size="small"
								:disabled="
									viewType === ViewType.View ||
									// rowData.__isUpDate ||
									// !rowData.__notSource ||
									fixedFields.includes(rowData.name) ||
									rowData.isChange ||
									rowData.isSelect ||
									rowData.isIdSelect ||
									rowData.isCardSelect ||
									// (rowData.lastOnlineTime !== 'isfield' && rowData.name !== null)
									(rowData.lastOnlineTime !== 'isfield' &&
										rowData.name !== null &&
										rowData.isString)
								"
								style="width: 200px"
								@change="onChangeDataType(rowData)"
							>
								<el-option-group
									v-for="group in DataNewTypeList"
									:key="group.label"
									:label="group.label"
								>
									<el-option
										v-for="item in group.options"
										:key="item.value"
										:label="item.name"
										:value="item.value"
									/>
								</el-option-group>
							</el-select>
							<!-- 无数据源的单/多选 -->
							<el-link
								:disabled="rowData.isSelect && viewType !== ViewType.View"
								v-if="
									rowData.multiple !== null &&
									(rowData.multiple == true || rowData.multiple == false)
								"
								type="primary"
								ml-10px
								@click="onOpenDataTypeItems(rowData)"
							>
								{{ viewType === ViewType.View ? '查看' : '配置' }}
							</el-link>
							<!-- 数据源的文本单选/多选 -->
							<template v-if="!rowData.__notSource && rowData.type === 'string'">
								<el-button
									:type="rowData.multiple === false ? 'primary' : 'default'"
									ml-10px
									@click="onOpenDataTypeItems(rowData, 'radio')"
								>
									单选
								</el-button>
								<el-button
									:type="rowData.multiple ? 'primary' : 'default'"
									ml-10px
									@click="onOpenDataTypeItems(rowData, 'checkbox')"
								>
									多选
								</el-button>
							</template>

							<!-- 无数据源的计算结果 -->
							<el-link
								v-if="
									(rowData.type === 'decimal' || rowData.type === 'int') &&
									rowData.__notSource
								"
								:disabled="rowData.isChange"
								type="primary"
								ml-10px
								@click="onOpenCount(rowData)"
							>
								{{ viewType === ViewType.View ? '查看' : '配置' }}
							</el-link>
							<!-- 身份证号配置 -->
							<el-link
								v-if="
									(rowData.type === 'sex' ||
										rowData.type === 'age' ||
										rowData.type === 'birthday') &&
									rowData.__notSource
								"
								type="primary"
								ml-10px
								@click="onOpenIdCard(rowData)"
							>
								{{ viewType === ViewType.View ? '查看' : '配置' }}
							</el-link>
							<!-- 时间配置 -->
							<el-link
								v-if="
									rowData.type === 'datetime' &&
									rowData.__notSource &&
									!fixedFields.includes(rowData.name)
								"
								type="primary"
								ml-10px
								@click="onOpenDateTimes(rowData)"
							>
								{{ viewType === ViewType.View ? '查看' : '配置' }}
							</el-link>
							<!-- 证件号码配置 -->
							<el-link
								v-if="
									rowData.type === 'certificate' &&
									rowData.__notSource &&
									!fixedFields.includes(rowData.name)
								"
								type="primary"
								ml-10px
								@click="onOpenCard(rowData)"
							>
								{{ viewType === ViewType.View ? '查看' : '配置' }}
							</el-link>
						</template>

						<template #__desensitization="{rowData}">
							<div style="display: flex; align-items: center">
								<el-checkbox
									v-model="rowData.__desensitization"
									:disabled="
										viewType === ViewType.View ||
										// !rowData.__isAddNew ||
										rowData.type == 'images' ||
										rowData.type == 'attachments'
										// fixedFields.includes(rowData.name)
									"
									@change="onCheckedDesensitiaztion($event, rowData)"
								></el-checkbox>
								<el-link
									v-if="rowData.__desensitization"
									type="primary"
									ml-10px
									@click="onOpenDesensitizationRuls(rowData)"
								>
									{{ viewType === ViewType.View ? '查看' : '配置' }}
								</el-link>
							</div>
						</template>

						<template #editDisabled="{rowData}">
							<el-checkbox
								v-model="rowData.editDisabled"
								:disabled="viewType === ViewType.View"
							></el-checkbox>
						</template>

						<template #clientSettings="scope">
							<el-checkbox
								v-model="scope.rowData.clientSettings[0].isDisplay"
								:disabled="viewType === ViewType.View"
								@change="onCheckedMobileDisplay($event, scope)"
							></el-checkbox>
							<span
								inline-block
								ml-10px
								v-if="
									scope.rowData?.clientSettings &&
									scope.rowData?.clientSettings.length !== 0 &&
									scope.rowData?.clientSettings[0].sort !== null
								"
							>
								第{{ scope.rowData?.clientSettings[0]?.sort + 1 }}项
							</span>
						</template>
					</BaseTableComp>
					<div>
						<el-button
							v-if="viewType !== ViewType.View"
							v-scroll
							:disabled="viewType === ViewType.View"
							style="width: 100%; background: #fff; border: 1px dashed"
							type="text"
							@click.stop="addTableeDataNoen"
						>
							<el-icon>
								<Plus />
							</el-icon>
							<span> 无数据来源添加 </span>
							<el-input-number
								style="margin: 0 6px"
								min="1"
								max="10"
								controls-position="right"
								size="small"
								:precision="0"
								v-model="addNumber"
								@click.stop=""
								@blur.stop="onBlurAdd($event)"
							/>
							<span> 个数据项 </span>
						</el-button>
					</div>
				</div>
			</div>
		</Block>
		<!--展示用  -->
		<DrawerComp :drawer="drawer" @closed="drawer = false" title="审核记录">
			<template #body>
				<el-steps direction="vertical" :active="1" align-center style="height: 400px">
					<el-step title="第一次审核">
						<template #description>
							<p>审核人:李达康</p>
							<p>审核结果:驳回</p>
							<p>审核意见:请重新提交</p>
							<p>2024-01-01 12:00:01</p>
						</template>
					</el-step>
					<el-step title="第二次审核">
						<template #description>
							<p>审核人:李达康</p>
							<p>审核结果:通过</p>
							<p>2024-01-01 12:00:01</p>
						</template>
					</el-step>
				</el-steps>
			</template>
		</DrawerComp>
		<Dialog
			v-model="isOpenViewEdit"
			:title="'审核'"
			@close="isOpenViewEdit = false"
			:enable-button="false"
			width="800px"
		>
			<Form
				ref="addForm"
				columnCount="1"
				v-model="conditionForm2"
				:data="ladgerFormArray2"
				:rules="formRules2"
				:enable-button="true"
				label-width="120"
				@submit="formSubmit2"
			>
			</Form>
		</Dialog>
		<!-- <FromDataSourceComponent
			ref="fromDataSourcRef"
			title="从数据集添加"
			width="1250"
			:view-type="viewType"
			:default-data="defaultData.filter((f: any) => f.dbTableFieldId)"
			:visible="openFromDataSource"
			v-model="openFromDataSource"
			:disabled="viewType === ViewType.Edit"
			@close="openFromDataSource = false"
			@changeSource="onDataSourceChange"
			@click-close="openFromDataSource = false"
			@clickConfirm="onDataSourceConfirm"
			@completed="onFromDataSourceCompleted"
		></FromDataSourceComponent> -->

		<DataTypeItems
			title="数据类型配置"
			width="680px"
			:multiple="currentRow?.multiple"
			:label="currentRow?.displayName"
			:tableInfo="selectTableInfo"
			v-model="openDataTypeItems"
			:default-data="currentRow"
			:openDataTypeItems="openDataTypeItems"
			:defaultMacthData="currentRow"
			@close="openDataTypeItems = false"
			@clickClose="onDataTypeItemsCancel"
			@clickConfirm="onDataTypeItems"
		>
		</DataTypeItems>

		<CalculationRulesModal
			title="计算规则"
			key="math"
			width="500px"
			v-model="openDataTypeCount"
			:tableInfo="calceSelect"
			:tableData="tableData"
			:label="currentRowMath?.displayName"
			:numberType="currentRowMath?.type"
			:defaultDataMath="currentRowMath?.calculateRule"
			:defaultDataNumber="currentRowMath"
			@close="openDataTypeCount = false"
			@click-close="onDataTypeCountCancel"
			@click-confirm="onDataTypeCount"
		>
		</CalculationRulesModal>

		<ChoseIdModal
			title="提取规则配置"
			key="IDCard"
			width="600px"
			v-model="openIdCardModal"
			:openCardModal="openIdCardModal"
			:defaultDataCard="currentRow?.relevanceCalculateRule"
			:allData="currentRow"
			:tableInfo="calceSelect"
			@close="openIdCardModal = false"
			@click-close="onDataIdCardCancel"
			@clickConfirmIdCard="clickConfirmIdCard"
		>
		</ChoseIdModal>
		<ChoseTimeModal
			title="时间日期配置"
			key="times"
			width="600px"
			v-model="openTimesModal"
			:default-data-time="currentRow"
			@close="openTimesModal = false"
			@click-close="onDateTimesCancel"
			@clickConfirmTime="onDateTimesCount"
		>
		</ChoseTimeModal>
		<ChoseCardModal
			title="证件号配置"
			width="600px"
			v-model="openCardModal"
			:defaultDataCard="cardCurrentRow"
			@close="openCardModal = false"
			@click-close="onDateCardCancel"
			@clickConfirmCard="onDateCardCount"
		>
		</ChoseCardModal>
		<DesensitizationRuls
			title="脱敏规则配置"
			width="520px"
			:label="currentRow?.displayName"
			v-model="openDesensitizationRuls"
			:default-data="currentRow?.desensitizationType"
			@close="openDesensitizationRuls = false"
			@click-close="openDesensitizationRuls = false"
			@clickConfirm="onDesensitizationRules"
		></DesensitizationRuls>

		<!-- <LedgerTemplates
			title="业务表模板添加"
			width="1250"
			v-model="openSelectLedgerTemplateTableList"
			@close="openSelectLedgerTemplateTableList = false"
			@onLedgerTemplateConfirm="onLedgerTemplateConfirm"
		>
		</LedgerTemplates> -->
		<Dialog
			v-model="isOpenFlow"
			title="台账多级审核流程"
			@opened="onOpenFlow"
			@clickConfirm="onIsOpenFlow"
			@close="isOpenFlow = false"
			:enableConfirm="false"
			:enableButton="false"
			:enableClose="false"
			:fullScreen="true"
		>
			<Flow
				ref="flowRef"
				v-resize="onFlowResize"
				:height="flowHeight"
				:template-code="form.workflowSchemeCode"
				:get-workflow-by-code="GetWorkflowByCode"
				:disabled="true"
				:show-node-info="true"
				width="100%"
				@node-mouse-enter="onFlowNodeMouseEnter"
				@node-mouse-leave="() => (curEnterNode = null)"
			>
				<template #node-info>
					<div
						class="node-info-item"
						v-if="curEnterNode?.type === FlowNodeTypes.Review && curEnterNode"
					>
						<span class="title">审核人</span>
						<div v-for="item of curEnterNode.auditUsers">
							{{
								departmentOptions.find(
									(f: any) => f.value === item.additionalConditionValue
								)?.label
							}}
							- {{ item.value }}
						</div>
						<span class="title">审核方式</span>
						<div>
							{{
								curEnterNode.isCountersign
									? '全部审核人都需审核'
									: '任一审核人审核即可'
							}}
						</div>
					</div>

					<div
						class="node-info-item"
						v-else-if="curEnterNode?.type === FlowNodeTypes.Condition && curEnterNode"
					>
						<span class="title">是否默认分支</span>
						<div>{{ curEnterNode.config.isDefault ? '是' : '否' }}</div>
						<span class="title">对应值</span>
						<div>{{ Object.values(curEnterNode.config.value).join() }}</div>
					</div>
				</template>
			</Flow>
		</Dialog>

		<Dialog
			v-model="openChangeLedgerTime"
			title="设置提醒"
			width="1200"
			@clickConfirm="onConfirmChangeLedgerTime"
			@close="onCloseTime"
			style="overflow: visible"
		>
			<div class="custom-form">
				<div class="item">
					<div class="label timeTitle">业务表更新周期:</div>
					<div class="value">
						{{
							remindConfig.interval == 2
								? '每周更新'
								: remindConfig.interval == 3
								? '每月更新'
								: remindConfig.interval == 4
								? '每季度更新'
								: remindConfig.interval == 5
								? '每年更新'
								: remindConfig.interval == 6
								? '每半月更新'
								: remindConfig.interval == 7
								? '每半年更新'
								: ''
						}}
					</div>
				</div>
				<div class="item flex" v-if="remindConfig.interval == 2">
					<div class="cloum">
						<div class="label">数据填报提醒时间:</div>
						<div class="value">
							<el-select
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.weeklyReminderDayOfWeek"
								placeholder="请选择日期"
								size="small"
							>
								<el-option
									v-for="item in intervalOptions"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</div>
					</div>
					<div class="cloum">
						<div class="label">数据填报开始时间:</div>
						<div class="value">
							<el-select
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.weeklyStartDayOfWeek"
								placeholder="请选择日期"
								size="small"
							>
								<el-option
									v-for="item in intervalOptions"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								/>
							</el-select>
						</div>
					</div>
					<div class="cloum">
						<div class="label">数据填报结束时间:</div>
						<div class="value">
							<el-select
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.weeklyDeadlineDayOfWeek"
								placeholder="请选择日期"
								size="small"
							>
								<template v-for="item in intervalOptions">
									<template
										v-if="item.value > remindConfig.weeklyReminderDayOfWeek"
									>
										<el-option
											:key="item.value"
											:label="item.label"
											:value="item.value"
										/>
									</template>
								</template>
							</el-select>
						</div>
					</div>
				</div>
				<div class="item flex" v-else-if="remindConfig.interval == 3">
					<div class="cloum">
						<div class="label">数据填报提醒时间:</div>
						<div class="value">
							每月
							<el-input-number
								:disabled="viewType === ViewType.View"
								style="width: 70px"
								v-model="remindConfig.monthlyReminderDay"
								:min="1"
								:max="31"
								size="small"
								controls-position="right"
							/>
							日
						</div>
					</div>
					<div class="cloum">
						<div class="label">数据填报开始时间:</div>
						<div class="value">
							每月
							<el-input-number
								:disabled="viewType === ViewType.View"
								style="width: 70px"
								v-model="remindConfig.monthlyStartDay"
								:min="1"
								:max="31"
								size="small"
								controls-position="right"
							/>
							日
						</div>
					</div>
					<div class="cloum">
						<div class="label">数据填报结束时间:</div>
						<div class="value">
							每月
							<el-input-number
								:disabled="viewType === ViewType.View"
								style="width: 70px"
								v-model="remindConfig.monthlyDeadlineDay"
								:min="1"
								:max="31"
								size="small"
								controls-position="right"
							/>
							日
						</div>
					</div>
				</div>
				<div style="width: 100%" v-else-if="remindConfig.interval == 4">
					<div class="item">
						<div class="cloum">
							<div class="label">数据填报提醒时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">数据填报开始时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">数据填报结束时间:</div>
							<div class="value"></div>
						</div>
					</div>
					<div class="item">
						<div class="label">第一季度:</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.quarterlyReminderDay1"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.quarterlyStartDay1"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.quarterlyDeadlineDay1"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="item">
						<div class="label">第二季度:</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.quarterlyReminderDay2"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.quarterlyStartDay2"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.quarterlyDeadlineDay2"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="item">
						<div class="label">第三季度:</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.quarterlyReminderDay3"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.quarterlyStartDay3"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.quarterlyDeadlineDay3"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="item">
						<div class="label">第四季度:</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.quarterlyReminderDay4"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.quarterlyStartDay4"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.quarterlyDeadlineDay4"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
				</div>
				<div class="item flex" v-else-if="remindConfig.interval == 5">
					<div class="cloum">
						<div class="label">数据填报提醒时间:</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								style="width: 180px"
								v-model="remindConfig.yearlyReminderDay"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="cloum">
						<div class="label">数据填报开始时间:</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								style="width: 180px"
								v-model="remindConfig.yearlyStartDay"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="cloum">
						<div class="label">数据填报结束时间:</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								style="width: 180px"
								v-model="remindConfig.yearlyDeadlineDay"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
				</div>
				<div style="width: 100%" v-else-if="remindConfig.interval == 6">
					<div class="item">
						<div class="cloum">
							<div class="label">数据填报提醒时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">数据填报开始时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">数据填报结束时间:</div>
							<div class="value"></div>
						</div>
					</div>
					<div class="item">
						<div class="value">
							上半月
							<el-input-number
								@blur="onBlur('upHalfMonthReminderTime')"
								:disabled="viewType === ViewType.View"
								style="width: 70px"
								v-model="remindConfig.upHalfMonthReminderTime"
								:min="1"
								:max="15"
								size="small"
								controls-position="right"
							/>
							日
						</div>
						<div class="value">
							上半月
							<el-input-number
								@blur="onBlur('upHalfMonthStartTime')"
								:disabled="viewType === ViewType.View"
								style="width: 70px"
								v-model="remindConfig.upHalfMonthStartTime"
								:min="1"
								:max="15"
								size="small"
								controls-position="right"
							/>
							日
						</div>
						<div class="value">
							上半月
							<el-input-number
								@blur="onBlur('upHalfMonthDateOnlyTime')"
								:disabled="viewType === ViewType.View"
								style="width: 70px"
								v-model="remindConfig.upHalfMonthDateOnlyTime"
								:min="1"
								:max="15"
								size="small"
								controls-position="right"
							/>
							日
						</div>
					</div>
					<div class="item">
						<div class="value">
							下半月
							<el-input-number
								@blur="onBlur('downHalfMonthReminderTime')"
								:disabled="viewType === ViewType.View"
								style="width: 70px"
								v-model="remindConfig.downHalfMonthReminderTime"
								:min="16"
								:max="30"
								size="small"
								controls-position="right"
							/>
							日
						</div>
						<div class="value">
							下半月
							<el-input-number
								@blur="onBlur('downHalfMonthStartTime')"
								:disabled="viewType === ViewType.View"
								style="width: 70px"
								v-model="remindConfig.downHalfMonthStartTime"
								:min="16"
								:max="30"
								size="small"
								controls-position="right"
							/>
							日
						</div>
						<div class="value">
							下半月
							<el-input-number
								@blur="onBlur('downHalfMonthDateOnlyTime')"
								:disabled="viewType === ViewType.View"
								style="width: 70px"
								v-model="remindConfig.downHalfMonthDateOnlyTime"
								:min="16"
								:max="30"
								size="small"
								controls-position="right"
							/>
							日
						</div>
					</div>
				</div>
				<div style="width: 100%" v-else-if="remindConfig.interval == 7">
					<div class="item">
						<div class="cloum">
							<div class="label">数据填报提醒时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">数据填报开始时间:</div>
							<div class="value"></div>
						</div>
						<div class="cloum">
							<div class="label">数据填报结束时间:</div>
							<div class="value"></div>
						</div>
					</div>
					<div class="item">
						<div class="label">上半年:</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.upHalfYearReminderTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.upHalfYearStartTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.upHalfYearDateOnlyTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
					<div class="item">
						<div class="label">下半年:</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.downHalfYearReminderTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.downHalfYearStartTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
						<div class="value">
							<el-date-picker
								:disabled="viewType === ViewType.View"
								v-model="remindConfig.downHalfYearDateOnlyTime"
								type="date"
								placeholder="请选择日期"
								format="MM/DD"
								value-format="YYYY-MM-DD"
								size="small"
							/>
						</div>
					</div>
				</div>
			</div>
		</Dialog>
		<Dialog
			v-model="isUrging"
			title="更新周期设置"
			width="600"
			@clickConfirm="onUrging"
			@close="closeUrging"
		>
			<div>
				<p>你已更改业务表的填报周期，涉及到已有周期的处理，请选择处理方式..</p>
				<el-checkbox-group v-model="urgingList" style="margin-top: 10px">
					<el-checkbox label="我希望从当前正在执行的周期开始调整" value="Value A" />
					<el-checkbox
						label="我希望执行完当前周期再开始按最新配置生成周期"
						value="Value B"
					/>
					<el-checkbox label="我希望将已截止的最近周期开始调整" value="Value C" />
				</el-checkbox-group>
			</div>
		</Dialog>
		<!-- :style="{top: currentY + 'px', left: currentX + 'px'}" -->
		<div v-if="isOpenView" class="phone_modal">
			<div class="content" ref="phoneModal" :style="{top: currentY + '%'}">
				<el-icon @click="ClosePhoneModal" class="icons"><CircleClose /></el-icon>
				<div class="main">
					<div class="phoneView">
						<img class="imgs" :src="phoneImg" :style="{maxWidth: maxWidth + 'px'}" />
						<div class="tree_content" :style="{maxWidth: maxWidth + 'px'}">
							<el-scrollbar height="calc(100% - 50px)">
								<el-tree
									ref="treePhone"
									:data="phoneData"
									hlight-current="true"
									default-expand-all
									node-key="id"
								>
									<template #default="{node, data}">
										<div style="width: 100%" class="item_s">
											<div class="custom-tree-node">
												<div class="txt">{{ data.names }}</div>
											</div>
										</div>
									</template>
								</el-tree>
							</el-scrollbar>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 传递一个模板id -->
		<!-- <ledgerTemplatePreview
			width="1000"
			:title="templateName"
			:LedgertemplateId="route.query.id"
			:visible="isShowPreview"
			:isShowIDs="true"
			@changeLedgerTemplateVisible="changeLedgerTemplateVisible"
			@closed="isShowPreview = false"
			@clickCancel="isShowPreview = false"
		>
		</ledgerTemplatePreview> -->

		<Dialog
			v-model="refs.visibleLedgerType"
			:title="openLadgerTypeTitle"
			@close="closed"
			@clickConfirm="formSubmit(addForm)"
			width="700px"
		>
			<!-- <template #body> -->
			<!-- <div style="padding: 5px 20px; margin-bottom: 10px; color: #606266; font-weight: 600">
					板块名称：{{ items[itemsIndex].name }}
				</div> -->
			<el-form
				ref="addForm"
				:model="formType"
				:rules="ladgerFormRules"
				:label-width="110"
				class="form-comp"
				style="width: 100%"
				p-10px
			>
				<el-form-item
					v-for="(item, index) of ladgerFormArray"
					:key="index"
					:prop="item.field"
					:class="{full: item.full, middel: item.middel}"
				>
					<template #label>
						<strong>{{ item.title }}:</strong>
					</template>
					<departmentFavoriteComp
						ref="tableComp"
						placeholder="请选择"
						:type="'modal'"
						:defaultCheckedData="defaultCheckedData"
						@change="departmentListChange"
					></departmentFavoriteComp>
				</el-form-item>
			</el-form>
			<!-- </template> -->
		</Dialog>
		<changeRecord
			v-model="ischangeRecord"
			v-if="ischangeRecord"
			width="20%"
			title="更改记录"
		></changeRecord>
	</div>
</template>
<route>
	{
		meta:{
			title:'业务表管理',
			ignoreLabel:false
		}
	}
</route>
<style lang="scss" scoped>
.tips {
	:deep(.el-form-item__content) {
		margin-left: 20px !important;
	}
}
.phone_modal {
	position: absolute;
	background: rgba(0, 0, 0, 0.2);
	width: 100%;
	height: 100%;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	z-index: 99;
	.content {
		position: absolute;
		left: 40%;
		transform: translate(-50%, -50%);
	}
	.icons {
		cursor: pointer;
		position: absolute;
		right: -287px;
		top: -16px;
		z-index: 1000;
		font-size: 26px;
	}
	img {
		width: 260px;
		position: absolute;
		// background: #f0f0f0;
		// border-radius: 9.7%;
	}
	.tree_content {
		top: 35px;
		left: 11px;
		position: absolute;
		width: 238px;
		height: 478px;
		background: #f0f0f0;
	}
	:deep(.el-tree-node__expand-icon) {
		display: none;
	}
	:deep(.el-tree) {
		background: #fff;
	}
	:deep(.custom-tree-node) {
		padding: 0 5px;
	}

	:deep(.el-tree-node__content) {
		height: 40px;
		border-bottom: 1px solid #eee;
	}
}

.custom-form {
	.timeTitle {
		width: 115px;
	}

	.item {
		display: flex;
		justify-content: space-between;
		.cloum {
			width: 30%;
			display: flex;
			justify-content: space-between;
		}

		.flexend {
			display: flex;
			justify-content: end;
		}
	}
}

:deep(.el-select-group__title) {
	font-weight: 700;
	margin-bottom: 5px;
}

.create-container {
	width: 100%;
	height: calc(100% - 70px);
	max-height: calc(100% - 70px);
	/* 视口高度 */
	overflow-y: auto;

	.base-form {
		align-items: flex-start;
		// border: var(--z-border);
		// border-radius: 5px;
		// background-color: rgb(243, 243, 243);
		display: flex;
		// padding: 10px;
		position: relative;
		width: 100%;

		:deep(.el-form) {
			align-items: center;
			display: flex;
			flex-wrap: wrap;
			flex: 1;

			.el-upload__tip {
				margin-top: 0;
				margin-left: 10px;
			}
		}

		.el-input {
			width: 200px;
		}

		.el-textarea {
			width: 400px;
		}

		.el-select {
			width: 200px;
		}

		.upload-template {
			align-items: center;
			display: flex;
			height: 32px;

			button {
				height: 30px;
			}

			:deep(.el-upload-list) {
				height: 32px;
				min-width: 150px;
				margin: 0 0 0 20px;
				z-index: 1;
			}
		}
	}

	.topIcon {
		cursor: pointer;

		&:hover {
			color: #5a9cf8;
		}
	}

	.bottomIcon {
		cursor: pointer;

		&:hover {
			color: #5a9cf8;
		}
	}
}

::v-deep(.el-input.is-disabled .el-input__inner) {
	-webkit-text-fill-color: rgb(48, 48, 48);
}
::v-deep(.el-textarea.is-disabled .el-textarea__inner) {
	background: #fff;
}

.node-info-item {
	.title {
		border-radius: 5px;
		background-color: var(--z-bg-secondary);
		font-weight: 500;
		font-size: 14px;
		padding: 5px;
		width: 100%;
	}
	> div {
		padding: 5px 15px;
	}
}
.label {
	display: flex;
	align-items: center;
}
</style>
