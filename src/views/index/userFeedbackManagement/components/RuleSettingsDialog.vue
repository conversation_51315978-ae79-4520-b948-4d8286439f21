<template>
  <Dialog
    v-model="visible"
    title="规则设置"
    width="700px"
    :destroy-on-close="true"
    :loading="loading"
    loading-text="保存中"
    @click-confirm="onConfirm"
    @click-cancel="onCancel"
    @closed="resetForm"
  >
    <div class="rule-settings-content">
      <el-form :model="ruleConfig" label-width="150px">
        <!-- 提交成功提示内容设置 -->
        <div class="rule-section">
          <h3 class="section-title">提交成功提示内容设置</h3>
          <el-form-item label="类型：" required>
            <el-select v-model="ruleConfig.successTip.type" placeholder="请选择">
              <el-option label="提交成功" value="提交成功" />
              <el-option label="处理完成" value="处理完成" />
              <el-option label="自定义" value="自定义" />
            </el-select>
          </el-form-item>
          <el-form-item label="内容提示：" required>
            <el-input
              type="textarea"
              :rows="4"
              v-model="ruleConfig.successTip.content"
              placeholder="请输入提示内容"
            />
          </el-form-item>
        </div>

        <!-- 私信沟通反馈机制设置 -->
        <div class="rule-section">
          <h3 class="section-title">私信沟通反馈机制设置</h3>
          <el-form-item label="触发规则：">
            <el-input
              v-model="ruleConfig.privateMessage.triggerRule"
              placeholder="请输入触发关键字，如系统崩溃、紧急"
            />
          </el-form-item>
          <el-form-item label="私信通道配置：">
            <el-checkbox-group v-model="ruleConfig.privateMessage.channels">
              <el-checkbox label="站内信">站内信</el-checkbox>
              <el-checkbox label="渝快政">渝快政</el-checkbox>
              <el-checkbox label="渝快Ding">渝快Ding</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>

        <!-- 用户反馈数据提取索引设置 -->
        <div class="rule-section">
          <h3 class="section-title">用户反馈数据提取索引设置</h3>
          <el-form-item label="提取字段：">
            <el-checkbox-group v-model="ruleConfig.dataExtraction.fields">
              <el-checkbox label="反馈分类">反馈分类</el-checkbox>
              <el-checkbox label="反馈内容">反馈内容</el-checkbox>
              <el-checkbox label="反馈编号">反馈编号</el-checkbox>
              <el-checkbox label="反馈时间">反馈时间</el-checkbox>
              <el-checkbox label="状态">状态</el-checkbox>
              <el-checkbox label="所属区县">所属区县</el-checkbox>
              <el-checkbox label="所属部门">所属部门</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>

        <!-- 用户反馈数据加密规则 -->
        <div class="rule-section">
          <h3 class="section-title">用户反馈数据加密规则</h3>
          <el-form-item label="加密类型：" required>
            <el-select v-model="ruleConfig.encryption.type" placeholder="请选择 AES / RSA / MD5">
              <el-option label="AES" value="AES" />
              <el-option label="RSA" value="RSA" />
              <el-option label="MD5" value="MD5" />
            </el-select>
          </el-form-item>
          <el-form-item label="加密密钥：" required>
            <el-input
              v-model="ruleConfig.encryption.key"
              placeholder="请输入加密密钥"
              type="password"
              show-password
            />
          </el-form-item>
          <el-form-item label="加密字段：">
            <el-checkbox-group v-model="ruleConfig.encryption.fields">
              <el-checkbox label="反馈分类">反馈分类</el-checkbox>
              <el-checkbox label="反馈形式">反馈形式</el-checkbox>
              <el-checkbox label="所属部门">所属部门</el-checkbox>
              <el-checkbox label="所属区县">所属区县</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>

        <!-- 版本记录规则设置 -->
        <div class="rule-section">
          <h3 class="section-title">版本记录规则设置</h3>
          <el-form-item label="版本记录前缀：" required>
            <el-input
              v-model="ruleConfig.versionRecord.prefix"
              placeholder="请输入版本记录前缀"
            />
          </el-form-item>
        </div>

        <!-- 反馈渠道功能优化与升级规则设置 -->
        <div class="rule-section">
          <h3 class="section-title">反馈渠道功能优化与升级规则设置</h3>
          <el-form-item label="反馈数量阈值：">
            <el-input
              v-model="ruleConfig.channelOptimization.threshold"
              placeholder="请输入数量阈值（当同一问题反馈数量达到此值时，自动升级）"
            />
            <div style="font-size: 12px; color: #999; margin-top: 4px;">
              条（当同一问题反馈数量达到此值时，自动升级）
            </div>
          </el-form-item>
          <el-form-item label="处理时限：">
            <el-input
              v-model="ruleConfig.channelOptimization.timeLimit"
              placeholder="请输入处理时限"
            />
            <div style="font-size: 12px; color: #999; margin-top: 4px;">
              小时（当反馈在规定时间内未处理时，自动升级）
            </div>
          </el-form-item>
          <el-form-item label="通知接收人：">
            <el-checkbox-group v-model="ruleConfig.channelOptimization.notifyReceivers">
              <el-checkbox label="站内信">站内信</el-checkbox>
              <el-checkbox label="渝快政">渝快政</el-checkbox>
              <el-checkbox label="渝快Ding">渝快Ding</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
      </el-form>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
interface Props {
  modelValue: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [config: any]
}>()

// 响应式数据
const visible = ref(false)
const loading = ref(false)

// 规则配置数据
const ruleConfig = reactive({
  successTip: {
    type: '提交成功',
    content: ''
  },
  privateMessage: {
    triggerRule: '',
    channels: [] as string[]
  },
  dataExtraction: {
    fields: [] as string[]
  },
  encryption: {
    type: '',
    key: '',
    fields: [] as string[]
  },
  versionRecord: {
    prefix: ''
  },
  channelOptimization: {
    threshold: '',
    timeLimit: '',
    notifyReceivers: [] as string[]
  }
})

// 监听props变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  // 当弹窗打开时，重新加载配置
  if (newVal) {
    loadConfig()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 重置表单
const resetForm = () => {
  Object.assign(ruleConfig, {
    successTip: {
      type: '提交成功',
      content: ''
    },
    privateMessage: {
      triggerRule: '',
      channels: []
    },
    dataExtraction: {
      fields: []
    },
    encryption: {
      type: '',
      key: '',
      fields: []
    },
    versionRecord: {
      prefix: ''
    },
    channelOptimization: {
      threshold: '',
      timeLimit: '',
      notifyReceivers: []
    }
  })
}

// 确认保存
const onConfirm = async () => {
  loading.value = true
  
  try {
    // 模拟异步保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 保存到localStorage
    localStorage.setItem('userFeedbackManagement_rules', JSON.stringify(ruleConfig))
    
    emit('confirm', ruleConfig)
    ElMessage.success('规则设置保存成功')
    visible.value = false
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 取消
const onCancel = () => {
  visible.value = false
}

// 初始化加载配置
const loadConfig = () => {
  const saved = localStorage.getItem('userFeedbackManagement_rules')
  if (saved) {
    try {
      const savedConfig = JSON.parse(saved)
      // 深度合并配置，保持默认值
      Object.keys(ruleConfig).forEach(key => {
        if (savedConfig[key]) {
          Object.assign(ruleConfig[key], savedConfig[key])
        }
      })
      console.log('加载已保存的规则配置:', savedConfig)
    } catch (error) {
      console.error('加载规则配置失败:', error)
    }
  }
}

// 组件挂载时加载配置
loadConfig()
</script>

<style scoped>
.rule-settings-content {
  max-height: 500px;
  overflow-y: auto;
  padding: 20px;
}

.rule-section {
  margin-bottom: 30px;
  padding: 20px 0;
  border-bottom: 1px solid #e4e7ed;
}

.rule-section:last-child {
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

:deep(.el-checkbox) {
  margin-right: 0;
}

.el-input, .el-select {
  width: 100%;
}

/* 滚动条样式 */
.rule-settings-content::-webkit-scrollbar {
  width: 6px;
}

.rule-settings-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.rule-settings-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.rule-settings-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
