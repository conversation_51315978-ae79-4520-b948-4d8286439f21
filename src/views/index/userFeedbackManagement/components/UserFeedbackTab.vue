<template>
  <div class="user-feedback-tab">
    <Block title="用户反馈管理" :enable-fixed-height="true" :enable-expand-content="true" :default-expand="false" @height-changed="onBlockHeightChanged">
      <template #topRight>
        <div class="top-buttons">
          <el-button size="small" type="primary" @click="onClickAdd">新增用户反馈</el-button>
          <el-button size="small" type="primary" @click="onClickCategorySettings">反馈分类设置</el-button>
          <el-button size="small" type="primary" @click="onClickRuleSettings">规则设置</el-button>
          <el-button size="small" type="primary" @click="onClickCustomTags">自定义标签</el-button>
          <el-button size="small" type="primary" @click="onClickDataAnalysis">数据与分析</el-button>
          <el-button size="small" type="primary" @click="onClickIterationPlan">迭代计划制定</el-button>
          <el-button size="small" type="primary" @click="onClickHelpCenter">帮助中心</el-button>
          <el-button size="small" type="primary" @click="onClickPermissionManagement">权限管理</el-button>
          <el-button size="small" type="primary" @click="onClickFeedbackReminder">反馈提醒</el-button>
          <el-button size="small" type="primary" @click="onClickTemplateCreation">反馈模版创建</el-button>
          <el-button size="small" type="primary" @click="onClickCommunityBuilding">反馈社区建设</el-button>
          <el-button size="small" type="primary" @click="onClickChannelIntegration">多渠道整合</el-button>
        </div>
      </template>

      <!-- 查询区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="反馈版本：">
            <el-select v-model="searchForm.version" placeholder="请选择版本" style="width: 120px;">
              <el-option label="全部" value="" />
              <el-option label="v1.0" value="v1.0" />
              <el-option label="v2.0" value="v2.0" />
              <el-option label="v3.0" value="v3.0" />
            </el-select>
          </el-form-item>
          <el-form-item label="反馈标题：">
            <el-input v-model="searchForm.title" placeholder="请输入反馈标题" style="width: 200px;" />
          </el-form-item>
          <el-form-item label="反馈分类：">
            <el-select v-model="searchForm.category" placeholder="请选择分类" style="width: 150px;">
              <el-option label="全部" value="" />
              <el-option 
                v-for="category in categoryOptions" 
                :key="category.value" 
                :label="category.label" 
                :value="category.value" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="处理状态：">
            <el-select v-model="searchForm.status" placeholder="请选择状态" style="width: 120px;">
              <el-option label="全部" value="" />
              <el-option label="待处理" value="待处理" />
              <el-option label="处理中" value="处理中" />
              <el-option label="已完成" value="已完成" />
              <el-option label="已关闭" value="已关闭" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
            <el-button @click="onReset">重置</el-button>
            <el-button @click="onReturn">返回</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <div class="table-area">
        <BaseTableComp
          :data="paginatedData"
          :colData="tableColumns"
          :buttons="tableButtons"
          :height="tableHeight"
          :visible-setting="false"
          :current-page="pagination.page"
          :page-size="pagination.size"
          :total="pagination.total"
          @size-change="onSizeChange"
          @current-change="onPageChange"
          @selection-change="onSelectionChange"
          @clickButton="onTableButtonClick"
          @sortableChange="handleSort"
        />
      </div>
    </Block>

    <!-- 新增用户反馈弹窗 -->
    <Dialog
      v-model="showFeedbackDialog"
      :title="feedbackDialogMode === 'add' ? '新增用户反馈' : feedbackDialogMode === 'edit' ? '编辑用户反馈' : '用户反馈详情'"
      width="800px"
      :destroy-on-close="true"
      :loading="feedbackLoading"
      loading-text="保存中"
      :visible-confirm-button="feedbackDialogMode !== 'view'"
      :confirm-text="feedbackDialogMode === 'add' ? '新增' : '保存'"
      @click-confirm="onFeedbackFormConfirm"
      @click-cancel="showFeedbackDialog = false"
      @closed="resetFeedbackForm"
    >
      <el-form :model="feedbackForm" :rules="feedbackFormRules" ref="feedbackFormRef" label-width="100px">
        <el-form-item label="反馈标题" prop="title">
          <el-input 
            v-model="feedbackForm.title" 
            placeholder="请输入反馈标题" 
            :readonly="feedbackDialogMode === 'view'"
          />
        </el-form-item>
        <el-form-item label="反馈内容" prop="content">
          <el-input 
            type="textarea" 
            :rows="6" 
            v-model="feedbackForm.content" 
            placeholder="请输入反馈内容"
            :readonly="feedbackDialogMode === 'view'"
          />
        </el-form-item>
        <el-form-item label="反馈形式" prop="feedbackType">
          <el-radio-group v-model="feedbackForm.feedbackType" :disabled="feedbackDialogMode === 'view'">
            <el-radio label="电子邮件">电子邮件</el-radio>
            <el-radio label="热线电话">热线电话</el-radio>
            <el-radio label="其他">其他</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="反馈分类" prop="category">
          <el-select
            v-model="feedbackForm.category"
            placeholder="请选择反馈分类"
            :disabled="feedbackDialogMode === 'view'"
            style="width: 100%"
          >
            <el-option
              v-for="category in categoryOptions"
              :key="category.value"
              :label="category.label"
              :value="category.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-select 
            v-model="feedbackForm.priority" 
            placeholder="请选择优先级"
            :disabled="feedbackDialogMode === 'view'"
            style="width: 100%"
          >
            <el-option label="高" value="高" />
            <el-option label="中" value="中" />
            <el-option label="低" value="低" />
          </el-select>
        </el-form-item>
        <el-form-item label="标签选择" prop="tags">
          <el-select 
            v-model="feedbackForm.tags" 
            multiple 
            placeholder="请选择标签"
            :disabled="feedbackDialogMode === 'view'"
            style="width: 100%"
          >
            <el-option 
              v-for="tag in tagOptions" 
              :key="tag.value" 
              :label="tag.label" 
              :value="tag.value" 
            />
          </el-select>
        </el-form-item>
        <el-form-item label="提交用户" prop="submitter" v-if="feedbackDialogMode !== 'add'">
          <el-input v-model="feedbackForm.submitter" readonly />
        </el-form-item>
        <el-form-item label="提交时间" prop="submitTime" v-if="feedbackDialogMode !== 'add'">
          <el-input v-model="feedbackForm.submitTime" readonly />
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 反馈分类设置弹窗 -->
    <Dialog
      v-model="showCategoryDialog"
      title="反馈分类设置"
      width="900px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      @click-cancel="showCategoryDialog = false"
    >
      <div style="margin-bottom: 20px;">
        <el-button type="primary" @click="onAddCategory">新增</el-button>
      </div>
      <el-table :data="categoryList" border style="width: 100%">
        <el-table-column prop="name" label="分类名称" />
        <el-table-column prop="description" label="分类描述" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-switch v-model="row.status" @change="updateCategoryStatus(row)" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="editCategory(row)">修改</el-button>
            <el-button size="small" type="danger" @click="deleteCategory(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </Dialog>

    <!-- 新增/编辑分类弹窗 -->
    <Dialog
      v-model="showCategoryFormDialog"
      :title="categoryDialogMode === 'add' ? '新增反馈分类' : '编辑反馈分类'"
      width="500px"
      :destroy-on-close="true"
      :loading="categoryLoading"
      loading-text="保存中"
      @click-confirm="onCategoryFormConfirm"
      @click-cancel="showCategoryFormDialog = false"
      @closed="resetCategoryForm"
    >
      <el-form :model="categoryForm" :rules="categoryFormRules" ref="categoryFormRef" label-width="100px">
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类描述" prop="description">
          <el-input 
            type="textarea" 
            :rows="3" 
            v-model="categoryForm.description" 
            placeholder="请输入分类描述" 
          />
        </el-form-item>
        <el-form-item label="是否启用" prop="status">
          <el-switch v-model="categoryForm.status" />
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 规则设置弹窗 -->
    <RuleSettingsDialog
      v-model="showRuleDialog"
      @confirm="onRuleSettingsConfirm"
    />

    <!-- 自定义标签弹窗 -->
    <Dialog
      v-model="showCustomTagsDialog"
      title="自定义标签管理"
      width="700px"
      :destroy-on-close="true"
      @click-confirm="saveCustomTags"
      @click-cancel="showCustomTagsDialog = false"
    >
      <div class="custom-tags-content">
        <div class="tags-section">
          <div class="section-header">
            <span class="section-title">自定义标签：</span>
            <el-button type="primary" size="small" @click="addCustomTag">+ 自定义标签</el-button>
          </div>
          <div class="tags-container">
            <div
              v-for="(tag, index) in customTags"
              :key="index"
              class="editable-tag"
            >
              <el-input
                v-if="tag.editing"
                v-model="tag.name"
                size="small"
                @blur="finishEditTag(tag)"
                @keyup.enter="finishEditTag(tag)"
                ref="tagInput"
                style="width: 100px;"
              />
              <el-tag
                v-else
                closable
                @close="removeCustomTag(index)"
                @click="startEditTag(tag)"
                style="cursor: pointer; margin-right: 8px; margin-bottom: 8px;"
              >
                {{ tag.name }}
              </el-tag>
            </div>
          </div>
        </div>

        <div class="tags-section">
          <div class="section-header">
            <span class="section-title">热门标签：</span>
            <el-button type="primary" size="small" @click="addHotTag">+ 添加热门标签</el-button>
          </div>
          <div class="tags-container">
            <div
              v-for="(tag, index) in hotTags"
              :key="index"
              class="editable-tag"
            >
              <el-input
                v-if="tag.editing"
                v-model="tag.name"
                size="small"
                @blur="finishEditTag(tag)"
                @keyup.enter="finishEditTag(tag)"
                style="width: 100px;"
              />
              <el-tag
                v-else
                closable
                @close="removeHotTag(index)"
                @click="startEditTag(tag)"
                style="cursor: pointer; margin-right: 8px; margin-bottom: 8px;"
                type="warning"
              >
                {{ tag.name }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 数据与分析弹窗 -->
    <Dialog
      v-model="showDataAnalysisDialog"
      title="数据与分析"
      width="900px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      @click-cancel="showDataAnalysisDialog = false"
    >
      <div class="data-analysis-content">
        <div class="chart-section">
          <h3 class="chart-title">分类统计分析</h3>
          <div class="chart-container">
            <div ref="pieChartRef" class="chart-item" style="width: 400px; height: 300px;"></div>
            <div class="analysis-text" style="width: 400px; height: 300px;">
              <h4>分析结论：</h4>
              <p>{{ pieChartAnalysis }}</p>
              <h4>线上所述：</h4>
              <p>{{ pieChartDescription }}</p>
            </div>
          </div>
        </div>

        <div class="chart-section">
          <h3 class="chart-title">处理效率分析</h3>
          <div class="chart-container">
            <div ref="barChartRef" class="chart-item" style="width: 500px; height: 300px;"></div>
            <div class="analysis-text" style="width: 300px; height: 300px;">
              <h4>分析结论：</h4>
              <p>{{ barChartAnalysis }}</p>
            </div>
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 权限管理弹窗 -->
    <Dialog
      v-model="showPermissionDialog"
      title="权限管理"
      width="800px"
      :destroy-on-close="true"
      @click-confirm="savePermissions"
      @click-cancel="showPermissionDialog = false"
    >
      <div class="permission-content">
        <!-- 权限配置表单 -->
        <div class="permission-form-section">
          <h4>新增权限配置</h4>
          <el-form :model="permissionForm" label-width="120px">
            <el-form-item label="授权人员：">
              <el-select v-model="permissionForm.selectedObjects" multiple placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="item in allUserOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="操作授权：">
              <el-checkbox-group v-model="permissionForm.permissions">
                <el-checkbox label="查看">查看</el-checkbox>
                <el-checkbox label="新增">新增</el-checkbox>
                <el-checkbox label="编辑">编辑</el-checkbox>
                <el-checkbox label="删除">删除</el-checkbox>
                <el-checkbox label="指派">指派</el-checkbox>
                <el-checkbox label="评论">评论</el-checkbox>
                <el-checkbox label="回复">回复</el-checkbox>
                <el-checkbox label="满意度评价">满意度评价</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </div>

        <!-- 已配置权限列表 -->
        <div class="permission-list-section">
          <h4>已配置权限</h4>
          <el-table :data="permissionList" border style="width: 100%">
            <el-table-column prop="objectName" label="授权人员" width="200" />
            <el-table-column prop="permissions" label="操作权限" />
            <el-table-column label="操作" width="100" align="center">
              <template #default="scope">
                <el-button type="danger" link size="small" @click="removePermission(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div v-if="!permissionList.length" class="empty-permissions">
            暂无权限配置
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 反馈提醒弹窗 -->
    <Dialog
      v-model="showReminderDialog"
      title="反馈提醒设置"
      width="600px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      @click-cancel="showReminderDialog = false"
    >
      <div class="reminder-content">
        <div class="reminder-list">
          <el-table :data="reminderList" border style="width: 100%">
            <el-table-column prop="sequence" label="序号" width="80" align="center" />
            <el-table-column prop="content" label="提醒内容" />
            <el-table-column prop="time" label="提醒时间" width="120" align="center" />
            <el-table-column prop="operator" label="操作人" width="120" align="center" />
          </el-table>
        </div>
      </div>
    </Dialog>

    <!-- 反馈社区建设主弹窗 -->
    <Dialog
      v-model="showCommunityDialog"
      title="反馈社区建设"
      width="900px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      @click-cancel="showCommunityDialog = false"
    >
      <div class="management-dialog-content">
        <!-- 查询区域 -->
        <div class="search-area">
          <el-form :inline="true" class="search-form">
            <el-form-item>
              <el-input placeholder="名称" style="width: 200px" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary">查询</el-button>
              <el-button>重置</el-button>
              <el-button>返回</el-button>
              <el-button type="primary" @click="resetCommunityForm(); showCommunityFormDialog = true">新增</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格区域 -->
        <div class="table-area">
          <el-table :data="communityList" border style="width: 100%">
            <el-table-column prop="sequence" label="序号" width="80" align="center"
              :formatter="(row, column, cellValue, index) => index + 1" />
            <el-table-column prop="name" label="名称" />
            <el-table-column prop="content" label="内容" />
            <el-table-column label="附件" width="100" align="center">
              <template #default="scope">
                <el-button v-if="scope.row.attachments?.length" type="primary" link size="small" @click="downloadAttachment(scope.row.attachments)">下载</el-button>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" align="center">
              <template #default="scope">
                <el-button type="primary" link size="small" @click="editCommunityItem(scope.row)">修改</el-button>
                <el-button type="danger" link size="small" @click="deleteCommunityItem(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </Dialog>

    <!-- 反馈社区建设新增弹窗 -->
    <Dialog
      v-model="showCommunityFormDialog"
      :title="communityEditMode ? '修改反馈社区建设' : '新增反馈社区建设'"
      width="600px"
      :destroy-on-close="true"
      confirm-text="确认"
      @click-confirm="onCommunityFormConfirm"
      @click-cancel="showCommunityFormDialog = false"
      @closed="resetCommunityForm"
    >
      <el-form :model="communityForm" :rules="managementFormRules" ref="communityFormRef" label-width="120px">
        <el-form-item label="社区建设名称" prop="name">
          <el-input v-model="communityForm.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="社区建设内容" prop="content">
          <el-input v-model="communityForm.content" type="textarea" :rows="4" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="附件">
          <div class="upload-section">
            <el-upload
              class="upload-demo"
              action="#"
              :before-upload="(file) => handleFileUpload(file, communityForm.attachments)"
              :show-file-list="false"
            >
              <el-button size="small" type="primary">上传文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持扩展名：.rar .zip .doc .docx .pdf .jpg .png .xls .xlsx
                </div>
              </template>
            </el-upload>
            <div v-if="communityForm.attachments.length" class="file-list">
              <div v-for="(file, index) in communityForm.attachments" :key="index" class="file-item">
                <span class="file-name">{{ file.name || '附件' + (index + 1) }}</span>
                <el-button type="danger" link size="small" @click="removeFile(communityForm.attachments, index)">删除</el-button>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 迭代计划制定主弹窗 -->
    <Dialog
      v-model="showIterationDialog"
      title="迭代计划制定"
      width="900px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      @click-cancel="showIterationDialog = false"
    >
      <div class="management-dialog-content">
        <!-- 查询区域 -->
        <div class="search-area">
          <el-form :inline="true" class="search-form">
            <el-form-item>
              <el-input placeholder="名称" style="width: 200px" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary">查询</el-button>
              <el-button>重置</el-button>
              <el-button>返回</el-button>
              <el-button type="primary" @click="resetIterationForm(); showIterationFormDialog = true">新增</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格区域 -->
        <div class="table-area">
          <el-table :data="iterationList" border style="width: 100%">
            <el-table-column prop="sequence" label="序号" width="80" align="center"
              :formatter="(row, column, cellValue, index) => index + 1" />
            <el-table-column prop="name" label="名称" />
            <el-table-column prop="content" label="内容" />
            <el-table-column label="附件" width="100" align="center">
              <template #default="scope">
                <el-button v-if="scope.row.attachments?.length" type="primary" link size="small" @click="downloadAttachment(scope.row.attachments)">下载</el-button>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" align="center">
              <template #default="scope">
                <el-button type="primary" link size="small" @click="editIterationItem(scope.row)">修改</el-button>
                <el-button type="danger" link size="small" @click="deleteIterationItem(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </Dialog>

    <!-- 迭代计划制定新增弹窗 -->
    <Dialog
      v-model="showIterationFormDialog"
      :title="iterationEditMode ? '修改迭代计划制定' : '新增迭代计划制定'"
      width="600px"
      :destroy-on-close="true"
      confirm-text="确认"
      @click-confirm="onIterationFormConfirm"
      @click-cancel="showIterationFormDialog = false"
      @closed="resetIterationForm"
    >
      <el-form :model="iterationForm" :rules="managementFormRules" ref="iterationFormRef" label-width="120px">
        <el-form-item label="迭代计划名称" prop="name">
          <el-input v-model="iterationForm.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="迭代计划内容" prop="content">
          <el-input v-model="iterationForm.content" type="textarea" :rows="4" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="附件">
          <div class="upload-section">
            <el-upload
              class="upload-demo"
              action="#"
              :before-upload="(file) => handleFileUpload(file, iterationForm.attachments)"
              :show-file-list="false"
            >
              <el-button size="small" type="primary">上传文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持扩展名：.rar .zip .doc .docx .pdf .jpg .png .xls .xlsx
                </div>
              </template>
            </el-upload>
            <div v-if="iterationForm.attachments.length" class="file-list">
              <div v-for="(file, index) in iterationForm.attachments" :key="index" class="file-item">
                <span class="file-name">{{ file.name || '附件' + (index + 1) }}</span>
                <el-button type="danger" link size="small" @click="removeFile(iterationForm.attachments, index)">删除</el-button>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 反馈模板创建主弹窗 -->
    <Dialog
      v-model="showTemplateDialog"
      title="反馈模板创建"
      width="900px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      @click-cancel="showTemplateDialog = false"
    >
      <div class="management-dialog-content">
        <!-- 查询区域 -->
        <div class="search-area">
          <el-form :inline="true" class="search-form">
            <el-form-item>
              <el-input placeholder="名称" style="width: 200px" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary">查询</el-button>
              <el-button>重置</el-button>
              <el-button>返回</el-button>
              <el-button type="primary" @click="resetTemplateForm(); showTemplateFormDialog = true">新增</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格区域 -->
        <div class="table-area">
          <el-table :data="templateList" border style="width: 100%">
            <el-table-column prop="sequence" label="序号" width="80" align="center"
              :formatter="(row, column, cellValue, index) => index + 1" />
            <el-table-column prop="name" label="名称" />
            <el-table-column prop="content" label="内容" />
            <el-table-column label="附件" width="100" align="center">
              <template #default="scope">
                <el-button v-if="scope.row.attachments?.length" type="primary" link size="small" @click="downloadAttachment(scope.row.attachments)">下载</el-button>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" align="center">
              <template #default="scope">
                <el-button type="primary" link size="small" @click="editTemplateItem(scope.row)">修改</el-button>
                <el-button type="danger" link size="small" @click="deleteTemplateItem(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </Dialog>

    <!-- 反馈模板创建新增弹窗 -->
    <Dialog
      v-model="showTemplateFormDialog"
      :title="templateEditMode ? '修改反馈模板创建' : '新增反馈模板创建'"
      width="600px"
      :destroy-on-close="true"
      confirm-text="确认"
      @click-confirm="onTemplateFormConfirm"
      @click-cancel="showTemplateFormDialog = false"
      @closed="resetTemplateForm"
    >
      <el-form :model="templateForm" :rules="managementFormRules" ref="templateFormRef" label-width="120px">
        <el-form-item label="反馈模版名称" prop="name">
          <el-input v-model="templateForm.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="反馈模版内容" prop="content">
          <el-input v-model="templateForm.content" type="textarea" :rows="4" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="附件">
          <div class="upload-section">
            <el-upload
              class="upload-demo"
              action="#"
              :before-upload="(file) => handleFileUpload(file, templateForm.attachments)"
              :show-file-list="false"
            >
              <el-button size="small" type="primary">上传文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持扩展名：.rar .zip .doc .docx .pdf .jpg .png .xls .xlsx
                </div>
              </template>
            </el-upload>
            <div v-if="templateForm.attachments.length" class="file-list">
              <div v-for="(file, index) in templateForm.attachments" :key="index" class="file-item">
                <span class="file-name">{{ file.name || '附件' + (index + 1) }}</span>
                <el-button type="danger" link size="small" @click="removeFile(templateForm.attachments, index)">删除</el-button>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 多渠道整合主弹窗 -->
    <Dialog
      v-model="showChannelDialog"
      title="多渠道整合"
      width="900px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      @click-cancel="showChannelDialog = false"
    >
      <div class="management-dialog-content">
        <!-- 查询区域 -->
        <div class="search-area">
          <el-form :inline="true" class="search-form">
            <el-form-item>
              <el-input placeholder="名称" style="width: 200px" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary">查询</el-button>
              <el-button>重置</el-button>
              <el-button>返回</el-button>
              <el-button type="primary" @click="resetChannelForm(); showChannelFormDialog = true">新增</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格区域 -->
        <div class="table-area">
          <el-table :data="channelList" border style="width: 100%">
            <el-table-column prop="sequence" label="序号" width="80" align="center"
              :formatter="(row, column, cellValue, index) => index + 1" />
            <el-table-column prop="name" label="名称" />
            <el-table-column prop="content" label="内容" />
            <el-table-column label="操作" width="150" align="center">
              <template #default="scope">
                <el-button type="primary" link size="small" @click="editChannelItem(scope.row)">修改</el-button>
                <el-button type="danger" link size="small" @click="deleteChannelItem(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </Dialog>

    <!-- 多渠道整合新增弹窗 -->
    <Dialog
      v-model="showChannelFormDialog"
      :title="channelEditMode ? '修改多渠道整合' : '新增多渠道整合'"
      width="600px"
      :destroy-on-close="true"
      confirm-text="确认"
      @click-confirm="onChannelFormConfirm"
      @click-cancel="showChannelFormDialog = false"
      @closed="resetChannelForm"
    >
      <el-form :model="channelForm" :rules="managementFormRules" ref="channelFormRef" label-width="120px">
        <el-form-item label="渠道名称" prop="name">
          <el-input v-model="channelForm.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="渠道内容" prop="content">
          <el-input v-model="channelForm.content" type="textarea" :rows="4" placeholder="请输入" />
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 帮助中心主弹窗 -->
    <Dialog
      v-model="showHelpCenterDialog"
      title="我们能为您提供什么帮助"
      width="1200px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      @click-cancel="showHelpCenterDialog = false"
    >
      <div class="help-center-content">
        <!-- 搜索框 -->
        <div class="search-section">
          <el-input
            v-model="helpSearchKeyword"
            placeholder="请输入您遇到的问题"
            style="width: 500px; margin: 0 auto; display: block;"
            @input="onHelpSearch"
          />
        </div>

        <!-- 三个区域 -->
        <div class="help-sections">
          <!-- 常见问题解答 -->
          <div class="help-section">
            <div class="section-header">
              <h3>常见问题解答</h3>
              <el-button type="primary" size="small" @click="resetFaqForm(); showFaqFormDialog = true">新增</el-button>
            </div>
            <div class="section-content">
              <div class="help-description">
                常见问题解答用户常见的问题
              </div>
              <div class="help-items">
                <div v-for="item in filteredFaqList" :key="item.id" class="help-item" @click="showHelpDetail(item)">
                  <div class="help-question" v-html="highlightKeyword(item.title, helpSearchKeyword)"></div>
                  <div class="help-answer" v-html="highlightKeyword(item.content, helpSearchKeyword)"></div>
                </div>
                <div v-if="!filteredFaqList.length" class="empty-state">
                  暂无数据
                </div>
              </div>
            </div>
          </div>

          <!-- 帮助中心 -->
          <div class="help-section">
            <div class="section-header">
              <h3>帮助中心</h3>
              <el-button type="primary" size="small" @click="resetHelpForm(); showHelpFormDialog = true">新增</el-button>
            </div>
            <div class="section-content">
              <div class="help-description">
                帮助中心，提供全面、详细的、技术的内容，以及为用户提供技术支持。
              </div>
              <div class="help-items">
                <div v-for="item in filteredHelpList" :key="item.id" class="help-item" @click="showHelpDetail(item)">
                  <div class="help-question" v-html="highlightKeyword(item.title, helpSearchKeyword)"></div>
                  <div class="help-answer" v-html="highlightKeyword(item.content, helpSearchKeyword)"></div>
                </div>
                <div v-if="!filteredHelpList.length" class="empty-state">
                  暂无数据
                </div>
              </div>
            </div>
          </div>

          <!-- 隐私保护政策 -->
          <div class="help-section">
            <div class="section-header">
              <h3>隐私保护政策</h3>
              <el-button type="primary" size="small" @click="resetPrivacyForm(); showPrivacyFormDialog = true">新增</el-button>
            </div>
            <div class="section-content">
              <div class="help-description">
                关于用户隐私保护政策，保护用户隐私，让用户放心使用。
              </div>
              <div class="help-items">
                <div v-for="item in filteredPrivacyList" :key="item.id" class="help-item" @click="showHelpDetail(item)">
                  <div class="help-question" v-html="highlightKeyword(item.title, helpSearchKeyword)"></div>
                  <div class="help-answer" v-html="highlightKeyword(item.content, helpSearchKeyword)"></div>
                </div>
                <div v-if="!filteredPrivacyList.length" class="empty-state">
                  暂无数据
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 常见问题新增弹窗 -->
    <Dialog
      v-model="showFaqFormDialog"
      title="新增常见问题"
      width="600px"
      :destroy-on-close="true"
      confirm-text="确认"
      @click-confirm="onFaqFormConfirm"
      @click-cancel="showFaqFormDialog = false"
    >
      <el-form :model="faqForm" :rules="helpFormRules" ref="faqFormRef" label-width="120px">
        <el-form-item label="常见问题名称" prop="title">
          <el-input v-model="faqForm.title" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="常见问题解决方案" prop="content">
          <el-input v-model="faqForm.content" type="textarea" :rows="4" placeholder="请输入" />
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 帮助中心新增弹窗 -->
    <Dialog
      v-model="showHelpFormDialog"
      title="新增帮助中心"
      width="600px"
      :destroy-on-close="true"
      confirm-text="确认"
      @click-confirm="onHelpFormConfirm"
      @click-cancel="showHelpFormDialog = false"
    >
      <el-form :model="helpForm" :rules="helpFormRules" ref="helpFormRef" label-width="120px">
        <el-form-item label="名称" prop="title">
          <el-input v-model="helpForm.title" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input v-model="helpForm.content" type="textarea" :rows="4" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="附件">
          <div class="upload-section">
            <el-upload
              class="upload-demo"
              action="#"
              :before-upload="(file) => handleFileUpload(file, helpForm.attachments)"
              :show-file-list="false"
            >
              <el-button size="small" type="primary">上传文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持扩展名：.rar .zip .doc .docx .pdf .jpg .png .xls .xlsx
                </div>
              </template>
            </el-upload>
            <div v-if="helpForm.attachments.length" class="file-list">
              <div v-for="(file, index) in helpForm.attachments" :key="index" class="file-item">
                <span class="file-name">{{ file.name || '附件' + (index + 1) }}</span>
                <el-button type="danger" link size="small" @click="removeFile(helpForm.attachments, index)">删除</el-button>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 隐私保护政策新增弹窗 -->
    <Dialog
      v-model="showPrivacyFormDialog"
      title="新增隐私政策"
      width="600px"
      :destroy-on-close="true"
      confirm-text="确认"
      @click-confirm="onPrivacyFormConfirm"
      @click-cancel="showPrivacyFormDialog = false"
    >
      <el-form :model="privacyForm" :rules="helpFormRules" ref="privacyFormRef" label-width="120px">
        <el-form-item label="隐私政策" prop="title">
          <el-input v-model="privacyForm.title" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="隐私内容" prop="content">
          <el-input v-model="privacyForm.content" type="textarea" :rows="4" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="附件">
          <div class="upload-section">
            <el-upload
              class="upload-demo"
              action="#"
              :before-upload="(file) => handleFileUpload(file, privacyForm.attachments)"
              :show-file-list="false"
            >
              <el-button size="small" type="primary">上传文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持扩展名：.rar .zip .doc .docx .pdf .jpg .png .xls .xlsx
                </div>
              </template>
            </el-upload>
            <div v-if="privacyForm.attachments.length" class="file-list">
              <div v-for="(file, index) in privacyForm.attachments" :key="index" class="file-item">
                <span class="file-name">{{ file.name || '附件' + (index + 1) }}</span>
                <el-button type="danger" link size="small" @click="removeFile(privacyForm.attachments, index)">删除</el-button>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 指派弹窗 -->
    <Dialog
      v-model="showAssignDialog"
      title="指派"
      width="400px"
      :destroy-on-close="true"
      confirm-text="确认"
      @click-confirm="onAssignConfirm"
      @click-cancel="showAssignDialog = false"
    >
      <el-form :model="assignForm" :rules="assignFormRules" ref="assignFormRef" label-width="100px">
        <el-form-item label="指派对象" prop="assignee">
          <el-select v-model="assignForm.assignee" placeholder="请选择指派对象" style="width: 100%">
            <el-option label="张三" value="张三" />
            <el-option label="李四" value="李四" />
            <el-option label="王五" value="王五" />
            <el-option label="赵六" value="赵六" />
          </el-select>
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 处理弹窗 -->
    <Dialog
      v-model="showProcessDialog"
      title="处理"
      width="500px"
      :destroy-on-close="true"
      confirm-text="确认"
      @click-confirm="onProcessConfirm"
      @click-cancel="showProcessDialog = false"
    >
      <el-form :model="processForm" :rules="processFormRules" ref="processFormRef" label-width="100px">
        <el-form-item label="处理时间" prop="processTime">
          <el-date-picker
            v-model="processForm.processTime"
            type="datetime"
            placeholder="请选择处理时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="处理结果" prop="processResult">
          <el-input v-model="processForm.processResult" type="textarea" :rows="4" placeholder="请输入处理结果" />
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 评论弹窗 -->
    <Dialog
      v-model="showCommentDialog"
      title="评论"
      width="600px"
      :destroy-on-close="true"
      confirm-text="确认"
      @click-confirm="onCommentConfirm"
      @click-cancel="showCommentDialog = false"
    >
      <div class="comment-dialog-content">
        <!-- 提交信息 -->
        <div class="feedback-info">
          <div class="info-row">
            <span class="label">反馈形式：</span>
            <span class="value">热线反馈</span>
            <span class="label">反馈分类：</span>
            <span class="value">分类1</span>
          </div>
          <div class="info-row">
            <span class="label">标签：</span>
            <el-tag size="small">标签1</el-tag>
            <el-tag size="small">热门标签</el-tag>
          </div>
          <div class="info-row">
            <span class="label">反馈内容：</span>
            <span class="value">这是一个反馈内容</span>
          </div>
          <div class="info-row">
            <span class="label">所属区县：</span>
            <span class="value">xxx</span>
            <span class="label">所属部门：</span>
            <span class="value">xxxx</span>
          </div>
          <div class="info-row">
            <span class="label">提交时间：</span>
            <span class="value">2025-03-07</span>
          </div>
        </div>

        <!-- 评论输入 -->
        <div class="comment-section">
          <h4>评论</h4>
          <el-form :model="commentForm" :rules="commentFormRules" ref="commentFormRef">
            <el-form-item prop="comment">
              <el-input
                v-model="commentForm.comment"
                type="textarea"
                :rows="6"
                placeholder="请输入评论"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </Dialog>

    <!-- 回复主弹窗 -->
    <Dialog
      v-model="showReplyDialog"
      title="回复"
      width="800px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      @click-cancel="showReplyDialog = false"
    >
      <div class="reply-dialog-content">
        <!-- 提交信息 -->
        <div class="feedback-info">
          <div class="info-row">
            <span class="label">反馈形式：</span>
            <span class="value">热线反馈</span>
            <span class="label">反馈分类：</span>
            <span class="value">分类1</span>
          </div>
          <div class="info-row">
            <span class="label">标签：</span>
            <el-tag size="small">标签1</el-tag>
            <el-tag size="small">热门标签</el-tag>
          </div>
          <div class="info-row">
            <span class="label">反馈内容：</span>
            <span class="value">这是一个反馈内容</span>
          </div>
          <div class="info-row">
            <span class="label">所属区县：</span>
            <span class="value">xxx</span>
            <span class="label">所属部门：</span>
            <span class="value">xxxx</span>
          </div>
          <div class="info-row">
            <span class="label">提交时间：</span>
            <span class="value">2025-03-07</span>
          </div>
        </div>

        <!-- 评论列表 -->
        <div class="comments-section">
          <h4>评论</h4>
          <div v-for="item in replyList" :key="item.id" class="comment-item">
            <div class="comment-header">
              <span class="comment-type">{{ item.type === 'comment' ? '评论' : '回复' }}</span>
              <span class="comment-author">{{ item.commentBy || item.replyBy }}</span>
              <span class="comment-time">{{ item.commentTime || item.replyTime }}</span>
            </div>
            <div class="comment-content">{{ item.comment || item.reply }}</div>
            <div class="comment-actions">
              <el-button type="primary" link size="small" @click="resetReplyForm(); showReplyFormDialog = true">回复</el-button>
            </div>
          </div>
          <div v-if="!replyList.length" class="empty-comments">
            暂无评论
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 回复输入弹窗 -->
    <Dialog
      v-model="showReplyFormDialog"
      title="回复"
      width="400px"
      :destroy-on-close="true"
      confirm-text="确认"
      @click-confirm="onReplyConfirm"
      @click-cancel="showReplyFormDialog = false"
    >
      <el-form :model="replyForm" :rules="replyFormRules" ref="replyFormRef" label-width="120px">
        <el-form-item label="请输入回复内容" prop="reply">
          <el-input v-model="replyForm.reply" type="textarea" :rows="4" placeholder="请输入回复内容" />
        </el-form-item>
      </el-form>
    </Dialog>

    <!-- 满意度评价弹窗 -->
    <Dialog
      v-model="showSatisfactionDialog"
      title="满意度评价"
      width="600px"
      :destroy-on-close="true"
      confirm-text="确认"
      @click-confirm="onSatisfactionConfirm"
      @click-cancel="showSatisfactionDialog = false"
    >
      <div class="satisfaction-dialog-content">
        <!-- 提交信息 -->
        <div class="feedback-info">
          <div class="info-row">
            <span class="label">反馈形式：</span>
            <span class="value">热线反馈</span>
            <span class="label">反馈分类：</span>
            <span class="value">分类1</span>
          </div>
          <div class="info-row">
            <span class="label">标签：</span>
            <el-tag size="small">标签1</el-tag>
            <el-tag size="small">热门标签</el-tag>
          </div>
          <div class="info-row">
            <span class="label">反馈内容：</span>
            <span class="value">这是一个反馈内容</span>
          </div>
          <div class="info-row">
            <span class="label">所属区县：</span>
            <span class="value">xxx</span>
            <span class="label">所属部门：</span>
            <span class="value">xxxx</span>
          </div>
          <div class="info-row">
            <span class="label">提交时间：</span>
            <span class="value">2025-03-07</span>
          </div>
        </div>

        <!-- 处理信息 -->
        <div v-if="currentProcessInfo" class="process-info">
          <h4>处理</h4>
          <div class="info-row">
            <span class="label">处理时间：</span>
            <span class="value">{{ currentProcessInfo.processTime }}</span>
          </div>
          <div class="info-row">
            <span class="label">处理内容：</span>
            <span class="value">{{ currentProcessInfo.processResult }}</span>
          </div>
        </div>

        <!-- 满意度评价 -->
        <div class="satisfaction-section">
          <h4>满意度评价</h4>
          <el-form :model="satisfactionForm" :rules="satisfactionFormRules" ref="satisfactionFormRef" label-width="100px">
            <el-form-item label="满意度" prop="rating">
              <el-radio-group v-model="satisfactionForm.rating" class="satisfaction-radio-group">
                <el-radio value="非常满意">非常满意</el-radio>
                <el-radio value="一般满意">一般满意</el-radio>
                <el-radio value="满意">满意</el-radio>
                <el-radio value="非常不满意">非常不满意</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="评价内容" prop="comment">
              <el-input
                v-model="satisfactionForm.comment"
                type="textarea"
                :rows="4"
                placeholder="请输入评价内容"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </Dialog>

    <!-- 详情弹窗 -->
    <Dialog
      v-model="showDetailDialog"
      title="详情"
      width="1000px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      @click-cancel="showDetailDialog = false"
    >
      <div class="detail-dialog-content">
        <div class="detail-layout">
          <!-- 左侧：处理进度 -->
          <div class="detail-left">
            <h4>处理进度</h4>
            <div class="timeline">
              <!-- 创建人信息 -->
              <div class="timeline-item">
                <div class="timeline-icon">📝</div>
                <div class="timeline-content">
                  <div class="timeline-title">创建人</div>
                  <div class="timeline-desc">永川区民政局-办公室-张三</div>
                  <div class="timeline-time">2024-01-21 12:00:01</div>
                </div>
              </div>

              <!-- 指派信息 -->
              <div v-if="detailData.assignInfo" class="timeline-item">
                <div class="timeline-icon">👤</div>
                <div class="timeline-content">
                  <div class="timeline-title">指派人</div>
                  <div class="timeline-desc">指派人：{{ detailData.assignInfo.assignee }}</div>
                  <div class="timeline-time">指派时间：{{ detailData.assignInfo.assignTime }}</div>
                  <div class="timeline-desc">操作人：{{ detailData.assignInfo.assignBy }}</div>
                </div>
              </div>

              <!-- 处理结果 -->
              <div v-if="detailData.processInfo" class="timeline-item">
                <div class="timeline-icon">✅</div>
                <div class="timeline-content">
                  <div class="timeline-title">处理结果</div>
                  <div class="timeline-desc">处理人：{{ detailData.processInfo.processBy }}</div>
                  <div class="timeline-time">处理时间：{{ detailData.processInfo.processTime }}</div>
                  <div class="timeline-desc">处理反馈：{{ detailData.processInfo.processResult }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：提交内容 -->
          <div class="detail-right">
            <h4>提交内容</h4>
            <div class="detail-content">
              <!-- 基本信息 -->
              <div class="detail-section">
                <div class="detail-row">
                  <span class="detail-label">反馈形式：</span>
                  <span class="detail-value">{{ detailData.feedbackType || '热线反馈' }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">反馈分类：</span>
                  <span class="detail-value">{{ detailData.category || '分类1' }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">标签：</span>
                  <div class="detail-tags">
                    <el-tag v-for="tag in detailData.tags" :key="tag" size="small">{{ tag }}</el-tag>
                  </div>
                </div>
                <div class="detail-row">
                  <span class="detail-label">反馈内容：</span>
                  <div class="detail-content-text">{{ detailData.content }}</div>
                </div>
                <div class="detail-row">
                  <span class="detail-label">所属部门：</span>
                  <span class="detail-value">xxx</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">所属区县：</span>
                  <span class="detail-value">xxx</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">创建时间：</span>
                  <span class="detail-value">{{ detailData.submitTime }}</span>
                </div>
              </div>

              <!-- 处理内容 -->
              <div v-if="detailData.processInfo" class="detail-section">
                <h5>处理内容</h5>
                <div class="detail-row">
                  <span class="detail-label">处理时间：</span>
                  <span class="detail-value">{{ detailData.processInfo.processTime }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">处理反馈内容：</span>
                  <div class="detail-content-text">{{ detailData.processInfo.processResult }}</div>
                </div>
              </div>

              <!-- 评论 -->
              <div v-if="detailData.comments?.length" class="detail-section">
                <h5>评论</h5>
                <div v-for="comment in detailData.comments" :key="comment.id" class="comment-item">
                  <div class="comment-text">{{ comment.comment }}</div>
                  <div class="comment-meta">{{ comment.commentBy }} - {{ comment.commentTime }}</div>
                </div>
              </div>

              <!-- 回复 -->
              <div v-if="detailData.replies?.length" class="detail-section">
                <h5>回复</h5>
                <div v-for="reply in detailData.replies" :key="reply.id" class="comment-item">
                  <div class="comment-text">{{ reply.reply }}</div>
                  <div class="comment-meta">{{ reply.replyBy }} - {{ reply.replyTime }}</div>
                </div>
              </div>

              <!-- 满意度评价 -->
              <div v-if="detailData.satisfaction" class="detail-section">
                <h5>满意度评价</h5>
                <div class="detail-row">
                  <span class="detail-label">满意度：</span>
                  <span class="detail-value">{{ detailData.satisfaction.rating }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">评价内容：</span>
                  <div class="detail-content-text">{{ detailData.satisfaction.comment }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 帮助中心详情弹窗 -->
    <Dialog
      v-model="showHelpDetailDialog"
      title="详情"
      width="600px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      @click-cancel="showHelpDetailDialog = false"
    >
      <div v-if="helpDetailData" class="help-detail-content">
        <div class="detail-item">
          <div class="detail-label">标题：</div>
          <div class="detail-value">{{ helpDetailData.title }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">内容：</div>
          <div class="detail-value">{{ helpDetailData.content }}</div>
        </div>
        <div v-if="helpDetailData.attachments?.length" class="detail-item">
          <div class="detail-label">附件：</div>
          <div class="detail-value">
            <div v-for="(file, index) in helpDetailData.attachments" :key="index" class="attachment-item">
              <el-button type="primary" link size="small" @click="downloadAttachment([file])">
                {{ file.name || '附件' + (index + 1) }}
              </el-button>
            </div>
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">创建时间：</div>
          <div class="detail-value">{{ helpDetailData.createTime }}</div>
        </div>
      </div>
    </Dialog>

    <!-- 用户反馈历史记录弹窗 -->
    <Dialog
      v-model="showHistoryDialog"
      title="反馈历史记录"
      width="800px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      @click-cancel="showHistoryDialog = false"
    >
      <div class="history-content">
        <!-- 搜索框 -->
        <div class="search-section" style="margin-bottom: 20px;">
          <el-input
            v-model="historySearchKeyword"
            placeholder="搜索历史记录..."
            style="width: 300px;"
            clearable
          />
        </div>

        <!-- 历史记录列表 -->
        <div class="history-list">
          <div v-for="item in paginatedHistoryList" :key="item.time" class="history-item">
            <div class="history-type">{{ item.type }}</div>
            <div class="history-content-text">{{ item.content }}</div>
            <div class="history-time">{{ item.time }}</div>
          </div>
          <div v-if="!paginatedHistoryList.length" class="empty-history">
            暂无历史记录
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-section" style="margin-top: 20px; text-align: center;">
          <el-pagination
            v-model:current-page="historyPagination.page"
            :page-size="historyPagination.size"
            :total="filteredHistoryList.length"
            layout="prev, pager, next"
            @current-change="onHistoryPageChange"
          />
        </div>
      </div>
    </Dialog>

    <!-- 用户画像构建弹窗 -->
    <Dialog
      v-model="showUserProfileDialog"
      title="用户画像构建"
      width="900px"
      :destroy-on-close="true"
      :visible-confirm-button="false"
      @click-cancel="showUserProfileDialog = false"
    >
      <div class="profile-content">
        <!-- 用户选择 -->
        <div class="user-select-section">
          <el-form label-width="60px">
            <el-form-item label="用户">
              <el-select v-model="selectedProfileUser" placeholder="请选择用户" style="width: 200px;">
                <el-option label="张三" value="张三" />
                <el-option label="李四" value="李四" />
                <el-option label="王五" value="王五" />
                <el-option label="赵六" value="赵六" />
              </el-select>
            </el-form-item>
          </el-form>
          <el-button type="primary" @click="startProfileBuilding" :loading="profileLoading">
            开始画像构建
          </el-button>
        </div>

        <!-- 用户画像信息 -->
        <div v-if="!profileLoading && userProfileData.userName" class="profile-info">
          <div class="profile-basic-info">
            <div class="info-row">
              <div class="info-item">
                <span class="label">用户账号</span>
                <span class="value">{{ userProfileData.userId }}</span>
              </div>
              <div class="info-item">
                <span class="label">所属部门</span>
                <span class="value">{{ userProfileData.department }}</span>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">所属区县</span>
                <span class="value">{{ userProfileData.district }}</span>
              </div>
              <div class="info-item">
                <span class="label">反馈形式</span>
                <div class="value">
                  <el-tag v-for="type in userProfileData.feedbackTypes" :key="type" size="small" style="margin-right: 5px;">
                    {{ type }}
                  </el-tag>
                </div>
              </div>
            </div>
            <div class="info-row">
              <div class="info-item">
                <span class="label">反馈分类</span>
                <div class="value">
                  <el-tag v-for="category in userProfileData.categories" :key="category" type="warning" size="small" style="margin-right: 5px;">
                    {{ category }}
                  </el-tag>
                </div>
              </div>
              <div class="info-item">
                <span class="label">标签</span>
                <div class="value">
                  <el-tag v-for="tag in userProfileData.tags" :key="tag" type="success" size="small" style="margin-right: 5px;">
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>

          <!-- 反馈分类统计图表 -->
          <div class="profile-chart-section">
            <h4>反馈分类统计</h4>
            <div ref="profileChartRef" style="width: 100%; height: 300px;"></div>
          </div>
        </div>

        <!-- Loading状态 -->
        <div v-if="profileLoading" class="profile-loading" v-loading="profileLoading" element-loading-text="正在构建用户画像...">
          <div style="height: 200px;"></div>
        </div>
      </div>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import RuleSettingsDialog from './RuleSettingsDialog.vue'
// Dialog和Table组件已全局注册，无需导入

// 用户反馈数据接口定义
interface UserFeedback {
  id: string
  title: string
  content: string
  feedbackType: string
  category: string
  submitter: string
  submitTime: string
  status: string
  priority: string
  tags: string[]
  attachments?: string[]
}

// 反馈分类接口定义
interface FeedbackCategory {
  id: string
  name: string
  description: string
  status: boolean
  createTime: string
}

// 响应式数据
const loading = ref(false)
const tableHeight = ref(400)
const currentRow = ref<UserFeedback | null>(null)
const selectedRows = ref<UserFeedback[]>([])

// 缓存键
const STORAGE_KEY = 'userFeedbackManagement_data'
const CATEGORY_STORAGE_KEY = 'userFeedbackManagement_categories'
const TAGS_STORAGE_KEY = 'userFeedbackManagement_tags'
const RULES_STORAGE_KEY = 'userFeedbackManagement_rules'

// 查询表单
const searchForm = reactive({
  version: '',
  title: '',
  category: '',
  status: ''
})

// 用户反馈列表数据
const feedbackList = ref<UserFeedback[]>([
  {
    id: '1',
    title: '系统登录问题',
    content: '用户反馈系统登录时经常出现超时问题，希望能够优化登录流程',
    feedbackType: '电子邮件',
    category: '系统问题反馈',
    submitter: '张三',
    submitTime: '2024-01-15 10:30:00',
    status: '待处理',
    priority: '高',
    tags: ['紧急', '登录']
  },
  {
    id: '2',
    title: '数据导出功能建议',
    content: '建议增加批量数据导出功能，提高工作效率',
    feedbackType: '热线电话',
    category: '系统优化建议',
    submitter: '李四',
    submitTime: '2024-01-14 14:20:00',
    status: '处理中',
    priority: '中',
    tags: ['功能优化', '导出']
  },
  {
    id: '3',
    title: '台账数据不准确',
    content: '发现台账中的部分数据与实际情况不符，需要核实和修正',
    feedbackType: '其他',
    category: '台帐问题',
    submitter: '王五',
    submitTime: '2024-01-13 09:15:00',
    status: '已完成',
    priority: '高',
    tags: ['数据质量', '台账']
  }
])

// 反馈分类列表数据
const categoryList = ref<FeedbackCategory[]>([
  {
    id: '1',
    name: '台帐问题',
    description: '与台账数据相关的问题反馈',
    status: true,
    createTime: '2024-01-01 10:00:00'
  },
  {
    id: '2',
    name: '系统优化建议',
    description: '对系统功能优化的建议',
    status: true,
    createTime: '2024-01-01 10:00:00'
  },
  {
    id: '3',
    name: '系统问题反馈',
    description: '系统使用过程中遇到的问题',
    status: true,
    createTime: '2024-01-01 10:00:00'
  },
  {
    id: '4',
    name: '其他',
    description: '其他类型的反馈',
    status: true,
    createTime: '2024-01-01 10:00:00'
  }
])

// 标签选项数据
const tagOptions = ref([
  { label: '紧急', value: '紧急' },
  { label: '重要', value: '重要' },
  { label: '登录', value: '登录' },
  { label: '功能优化', value: '功能优化' },
  { label: '数据质量', value: '数据质量' },
  { label: '台账', value: '台账' },
  { label: '导出', value: '导出' },
  { label: '性能', value: '性能' },
  { label: '界面', value: '界面' },
  { label: '安全', value: '安全' }
])

// 分类选项（用于查询）
const categoryOptions = computed(() => {
  return categoryList.value
    .filter(cat => cat.status)
    .map(cat => ({ label: cat.name, value: cat.name }))
})

// 过滤后的反馈列表
const filteredFeedbackList = computed(() => {
  let filtered = feedbackList.value

  if (searchForm.title) {
    filtered = filtered.filter(item =>
      item.title.toLowerCase().includes(searchForm.title.toLowerCase())
    )
  }

  if (searchForm.category) {
    filtered = filtered.filter(item => item.category === searchForm.category)
  }

  if (searchForm.status) {
    filtered = filtered.filter(item => item.status === searchForm.status)
  }

  return filtered
})

// 表格列配置
const tableColumns = ref([
  { field: 'id', title: '反馈ID', width: 100 },
  { field: 'title', title: '反馈标题', minWidth: 200 },
  { field: 'content', title: '反馈内容', minWidth: 300 },
  { field: 'feedbackType', title: '反馈形式', width: 120 },
  { field: 'category', title: '反馈分类', width: 120 },
  { field: 'submitter', title: '提交用户', width: 100 },
  { field: 'submitTime', title: '提交时间', width: 150 },
  { field: 'status', title: '处理状态', width: 100 }
])

// 分页配置
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 计算分页数据
const paginatedData = computed(() => {
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  const filtered = filteredFeedbackList.value
  pagination.total = filtered.length
  return filtered.slice(start, end)
})

// 表格操作按钮配置
const tableButtons = ref([
  { title: '详情', type: 'info', code: 'view', verify: 'true', more: false },
  { title: '指派', type: 'primary', code: 'assign', verify: 'true', more: false },
  { title: '处理', type: 'success', code: 'process', verify: 'true', more: false },
  { title: '删除', type: 'danger', code: 'delete', verify: 'true', more: false },
  { title: '评论', type: 'primary', code: 'comment', verify: 'true', more: true },
  { title: '回复', type: 'primary', code: 'reply', verify: 'true', more: true },
  { title: '满意度评价', type: 'primary', code: 'satisfaction', verify: 'true', more: true },
  { title: '用户反馈历史', type: 'primary', code: 'history', verify: 'true', more: true },
  { title: '用户画像构建', type: 'primary', code: 'profile', verify: 'true', more: true }
])

// 用户反馈弹窗相关
const showFeedbackDialog = ref(false)
const feedbackDialogMode = ref<'add' | 'edit' | 'view'>('add')
const feedbackLoading = ref(false)
const feedbackFormRef = ref()

// 新增功能弹窗状态
const showCustomTagsDialog = ref(false)
const showDataAnalysisDialog = ref(false)
const showPermissionDialog = ref(false)
const showReminderDialog = ref(false)

// 四个管理功能弹窗状态
const showCommunityDialog = ref(false)
const showCommunityFormDialog = ref(false)
const showIterationDialog = ref(false)
const showIterationFormDialog = ref(false)
const showTemplateDialog = ref(false)
const showTemplateFormDialog = ref(false)
const showChannelDialog = ref(false)
const showChannelFormDialog = ref(false)

// 帮助中心弹窗状态
const showHelpCenterDialog = ref(false)
const showFaqFormDialog = ref(false)
const showHelpFormDialog = ref(false)
const showPrivacyFormDialog = ref(false)

// 用户反馈操作弹窗状态
const showAssignDialog = ref(false)
const showProcessDialog = ref(false)
const showCommentDialog = ref(false)
const showReplyDialog = ref(false)
const showReplyFormDialog = ref(false)
const showSatisfactionDialog = ref(false)
const showDetailDialog = ref(false)
const showHistoryDialog = ref(false)
const showUserProfileDialog = ref(false)

const feedbackForm = reactive({
  id: '',
  title: '',
  content: '',
  feedbackType: '',
  category: '',
  priority: '',
  tags: [] as string[],
  submitter: '',
  submitTime: ''
})

// 四个管理功能的数据结构
interface ManagementItem {
  id: string
  name: string
  content: string
  attachments?: string[]
  createTime: string
}

// 四个管理功能的数据
const communityList = ref<ManagementItem[]>([])
const iterationList = ref<ManagementItem[]>([])
const templateList = ref<ManagementItem[]>([])
const channelList = ref<ManagementItem[]>([])

// 编辑模式标识
const communityEditMode = ref(false)
const iterationEditMode = ref(false)
const templateEditMode = ref(false)
const channelEditMode = ref(false)

// 四个管理功能的表单数据
const communityForm = reactive({
  id: '',
  name: '',
  content: '',
  attachments: [] as string[]
})

const iterationForm = reactive({
  id: '',
  name: '',
  content: '',
  attachments: [] as string[]
})

const templateForm = reactive({
  id: '',
  name: '',
  content: '',
  attachments: [] as string[]
})

const channelForm = reactive({
  id: '',
  name: '',
  content: ''
})

// 帮助中心数据结构
interface HelpCenterItem {
  id: string
  title: string
  content: string
  attachments?: string[]
  createTime: string
}

// 帮助中心数据
const faqList = ref<HelpCenterItem[]>([])
const helpList = ref<HelpCenterItem[]>([])
const privacyList = ref<HelpCenterItem[]>([])

// 帮助中心搜索
const helpSearchKeyword = ref('')

// 帮助中心详情弹窗
const showHelpDetailDialog = ref(false)
const helpDetailData = ref<HelpCenterItem | null>(null)

// 用户反馈历史记录
const historyList = ref<any[]>([])
const historySearchKeyword = ref('')
const historyPagination = reactive({
  page: 1,
  size: 5,
  total: 0
})

// 用户画像构建
const userProfileData = ref<any>({
  userId: '',
  userName: '',
  department: '',
  district: '',
  feedbackTypes: [],
  categories: [],
  tags: []
})
const profileLoading = ref(false)
const profileChartRef = ref()
const selectedProfileUser = ref('')

// 帮助中心表单数据
const faqForm = reactive({
  id: '',
  title: '',
  content: '',
  attachments: [] as string[]
})

const helpForm = reactive({
  id: '',
  title: '',
  content: '',
  attachments: [] as string[]
})

const privacyForm = reactive({
  id: '',
  title: '',
  content: '',
  attachments: [] as string[]
})

// 用户反馈操作相关数据
const currentFeedbackId = ref('')
const currentProcessInfo = ref<any>(null)

const assignForm = reactive({
  assignee: ''
})

const processForm = reactive({
  processTime: '',
  processResult: ''
})

const commentForm = reactive({
  comment: ''
})

const replyForm = reactive({
  reply: ''
})

const satisfactionForm = reactive({
  rating: '',
  comment: ''
})

// 回复列表数据
const replyList = ref<any[]>([])

// 详情数据
const detailData = ref<any>({})

// 表单验证规则
const managementFormRules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  content: [{ required: true, message: '请输入内容', trigger: 'blur' }]
}

const helpFormRules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入内容', trigger: 'blur' }]
}

const assignFormRules = {
  assignee: [{ required: true, message: '请选择指派对象', trigger: 'change' }]
}

const processFormRules = {
  processTime: [{ required: true, message: '请选择处理时间', trigger: 'change' }],
  processResult: [{ required: true, message: '请输入处理结果', trigger: 'blur' }]
}

const commentFormRules = {
  comment: [{ required: true, message: '请输入评论内容', trigger: 'blur' }]
}

const replyFormRules = {
  reply: [{ required: true, message: '请输入回复内容', trigger: 'blur' }]
}

const satisfactionFormRules = {
  rating: [{ required: true, message: '请选择满意度', trigger: 'change' }],
  comment: [{ required: true, message: '请输入评价内容', trigger: 'blur' }]
}

const feedbackFormRules = {
  title: [{ required: true, message: '请输入反馈标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入反馈内容', trigger: 'blur' }],
  feedbackType: [{ required: true, message: '请选择反馈形式', trigger: 'change' }],
  category: [{ required: true, message: '请选择反馈分类', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }]
}

// 反馈分类弹窗相关
const showCategoryDialog = ref(false)
const showCategoryFormDialog = ref(false)
const categoryDialogMode = ref<'add' | 'edit'>('add')
const categoryLoading = ref(false)
const categoryFormRef = ref()
const currentCategory = ref<FeedbackCategory | null>(null)

// 规则设置弹窗相关
const showRuleDialog = ref(false)

// 自定义标签相关数据
const customTags = ref([
  { name: '标签1', editing: false },
  { name: '标签2', editing: false },
  { name: '标签1', editing: false },
  { name: '标签3', editing: false }
])

const hotTags = ref([])

// 数据分析相关
const pieChartRef = ref()
const barChartRef = ref()
const pieChartAnalysis = ref('分类1 120个，分类2 5个，分类3xxx个。')
const pieChartDescription = ref('分类1使用率最高，分类3使用率，较低')
const barChartAnalysis = ref('根据图表可知3月提交数量最多，3月处理的数据效率最高')

// 权限管理相关数据
const permissionForm = reactive({
  selectedObjects: [],
  permissions: []
})

const permissionList = ref([])

const allUserOptions = ref([
  { label: '张三', value: 'zhangsan' },
  { label: '李四', value: 'lisi' },
  { label: '王五', value: 'wangwu' },
  { label: 'AA', value: 'aa' },
  { label: 'BB', value: 'bb' },
  { label: 'CC', value: 'cc' },
  { label: 'DD', value: 'dd' }
])

// 反馈提醒相关数据
const reminderList = ref([
  {
    sequence: 1,
    content: '您提交的反馈，已处理，请及时查看',
    time: '2025.7.8',
    operator: '系统管理员'
  },
  {
    sequence: 2,
    content: '您提交的反馈，已处理，请及时查看',
    time: '2025.7.8',
    operator: '系统管理员'
  },
  {
    sequence: 3,
    content: '您提交的反馈，已处理，请及时查看',
    time: '2025.7.8',
    operator: '系统管理员'
  }
])

// 计算属性（权限管理相关的计算属性已移除）

const categoryForm = reactive({
  id: '',
  name: '',
  description: '',
  status: true
})

const categoryFormRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        const exists = categoryList.value.some(cat =>
          cat.name === value && cat.id !== categoryForm.id
        )
        if (exists) {
          callback(new Error('分类名称已存在'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  description: [{ required: true, message: '请输入分类描述', trigger: 'blur' }]
}

// 数据持久化方法
const saveDataToCache = () => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(feedbackList.value))
}

const loadDataFromCache = () => {
  const cached = localStorage.getItem(STORAGE_KEY)
  if (cached) {
    feedbackList.value = JSON.parse(cached)
  }
}

const saveCategoryToCache = () => {
  localStorage.setItem(CATEGORY_STORAGE_KEY, JSON.stringify(categoryList.value))
}

const loadCategoryFromCache = () => {
  const cached = localStorage.getItem(CATEGORY_STORAGE_KEY)
  if (cached) {
    categoryList.value = JSON.parse(cached)
  }
}

// 生成唯一ID
const generateId = () => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9)
}

// 格式化时间
const formatDateTime = (date: Date = new Date()) => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// localStorage 数据管理
const saveToLocalStorage = (key: string, data: any) => {
  localStorage.setItem(key, JSON.stringify(data))
}

const loadFromLocalStorage = (key: string, defaultValue: any = []) => {
  const stored = localStorage.getItem(key)
  return stored ? JSON.parse(stored) : defaultValue
}

// 四个管理功能数据加载
const loadCommunityList = () => {
  communityList.value = loadFromLocalStorage('feedback_community_list', [])
}

const loadIterationList = () => {
  iterationList.value = loadFromLocalStorage('feedback_iteration_list', [])
}

const loadTemplateList = () => {
  templateList.value = loadFromLocalStorage('feedback_template_list', [])
}

const loadChannelList = () => {
  channelList.value = loadFromLocalStorage('feedback_channel_list', [])
}

// 帮助中心数据加载
const loadHelpCenterData = () => {
  faqList.value = loadFromLocalStorage('feedback_faq_list', [])
  helpList.value = loadFromLocalStorage('feedback_help_list', [])
  privacyList.value = loadFromLocalStorage('feedback_privacy_list', [])
}

// 帮助中心搜索功能
const filteredFaqList = computed(() => {
  if (!helpSearchKeyword.value) return faqList.value
  return faqList.value.filter(item =>
    item.title.toLowerCase().includes(helpSearchKeyword.value.toLowerCase()) ||
    item.content.toLowerCase().includes(helpSearchKeyword.value.toLowerCase())
  )
})

const filteredHelpList = computed(() => {
  if (!helpSearchKeyword.value) return helpList.value
  return helpList.value.filter(item =>
    item.title.toLowerCase().includes(helpSearchKeyword.value.toLowerCase()) ||
    item.content.toLowerCase().includes(helpSearchKeyword.value.toLowerCase())
  )
})

const filteredPrivacyList = computed(() => {
  if (!helpSearchKeyword.value) return privacyList.value
  return privacyList.value.filter(item =>
    item.title.toLowerCase().includes(helpSearchKeyword.value.toLowerCase()) ||
    item.content.toLowerCase().includes(helpSearchKeyword.value.toLowerCase())
  )
})

// 帮助中心搜索事件
const onHelpSearch = () => {
  // 搜索功能通过computed属性自动实现
}

// 高亮搜索关键词
const highlightKeyword = (text: string, keyword: string) => {
  if (!keyword) return text
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case '待处理':
      return 'info'
    case '处理中':
      return 'warning'
    case '已指派':
      return 'primary'
    case '已处理':
      return 'success'
    case '已完成':
      return 'success'
    case '已关闭':
      return 'danger'
    default:
      return 'info'
  }
}

// 显示帮助中心详情
const showHelpDetail = (item: HelpCenterItem) => {
  helpDetailData.value = item
  showHelpDetailDialog.value = true
}

// 加载满意度评价数据
const loadSatisfactionData = (feedbackId: string) => {
  // 加载处理信息
  currentProcessInfo.value = loadFromLocalStorage(`feedback_process_${feedbackId}`, null)

  // 加载已有的满意度评价
  const satisfactionInfo = loadFromLocalStorage(`feedback_satisfaction_${feedbackId}`, null)
  if (satisfactionInfo) {
    Object.assign(satisfactionForm, {
      rating: satisfactionInfo.rating,
      comment: satisfactionInfo.comment
    })
  } else {
    resetSatisfactionForm()
  }
}

// 加载用户历史记录
const loadUserHistory = (userName: string) => {
  try {
    console.log('开始加载用户历史记录，用户名:', userName)

    // 模拟历史记录数据
    const mockHistory = [
      {
        type: '提交反馈',
        content: `重庆大数据局-张三 提交了反馈 - 2025.7.11 10:30`,
        time: '2025.7.11 10:30'
      },
      {
        type: '指派',
        content: `重庆大数据局-张三 指派了重庆大数据局-李四 进行处理 - 2025.7.11 11:45`,
        time: '2025.7.11 11:45'
      },
      {
        type: '处理',
        content: `重庆大数据局-李四 已处理完成 - 2025.7.11 11:45`,
        time: '2025.7.11 11:45'
      }
    ]

    historyList.value = mockHistory
    historyPagination.total = mockHistory.length
    historyPagination.page = 1

    console.log('历史记录加载完成:', historyList.value)
  } catch (error) {
    console.error('加载用户历史记录失败:', error)
    historyList.value = []
  }
}

// 加载用户画像
const loadUserProfile = (userName: string) => {
  try {
    console.log('开始加载用户画像，用户名:', userName)

    selectedProfileUser.value = userName

    // 模拟用户基本信息
    userProfileData.value = {
      userId: 'user12345',
      userName: userName,
      department: '市场部',
      district: '海淀区',
      feedbackTypes: ['电话', '邮件', '在线客服'],
      categories: ['产品建议', '技术支持', '服务投诉'],
      tags: ['高活跃度', '潜在客户', 'VIP用户']
    }

    console.log('用户画像数据设置完成:', userProfileData.value)

    // 自动开始画像构建
    nextTick(() => {
      startProfileBuilding()
    })
  } catch (error) {
    console.error('加载用户画像失败:', error)
  }
}

// 表单重置方法
const resetCommunityForm = () => {
  communityEditMode.value = false
  Object.assign(communityForm, {
    id: '',
    name: '',
    content: '',
    attachments: []
  })
}

const resetIterationForm = () => {
  iterationEditMode.value = false
  Object.assign(iterationForm, {
    id: '',
    name: '',
    content: '',
    attachments: []
  })
}

const resetTemplateForm = () => {
  templateEditMode.value = false
  Object.assign(templateForm, {
    id: '',
    name: '',
    content: '',
    attachments: []
  })
}

const resetChannelForm = () => {
  channelEditMode.value = false
  Object.assign(channelForm, {
    id: '',
    name: '',
    content: ''
  })
}

const resetFaqForm = () => {
  Object.assign(faqForm, {
    id: '',
    title: '',
    content: '',
    attachments: []
  })
}

const resetHelpForm = () => {
  Object.assign(helpForm, {
    id: '',
    title: '',
    content: '',
    attachments: []
  })
}

const resetPrivacyForm = () => {
  Object.assign(privacyForm, {
    id: '',
    title: '',
    content: '',
    attachments: []
  })
}

const resetAssignForm = () => {
  Object.assign(assignForm, {
    assignee: ''
  })
}

const resetProcessForm = () => {
  Object.assign(processForm, {
    processTime: '',
    processResult: ''
  })
}

const resetCommentForm = () => {
  Object.assign(commentForm, {
    comment: ''
  })
}

const resetReplyForm = () => {
  Object.assign(replyForm, {
    reply: ''
  })
}

const resetSatisfactionForm = () => {
  Object.assign(satisfactionForm, {
    rating: '',
    comment: ''
  })
}

// 表格事件处理
const onBlockHeightChanged = (height: number) => {
  tableHeight.value = height - 200
}

const onSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
}

const onPageChange = (page: number) => {
  pagination.page = page
}

const onSelectionChange = (selection: UserFeedback[]) => {
  selectedRows.value = selection
}

const handleSort = (column: any, prop: string, order: string) => {
  // 排序处理
}

// 查询相关方法
const onSearch = () => {
  // 查询逻辑已在computed中实现
  ElMessage.success('查询完成')
}

const onReset = () => {
  Object.assign(searchForm, {
    version: '',
    title: '',
    category: '',
    status: ''
  })
  ElMessage.success('重置完成')
}

const onReturn = () => {
  // 返回上级页面
  ElMessage.info('返回上级页面')
}

// 顶部按钮事件处理
const onClickAdd = () => {
  feedbackDialogMode.value = 'add'
  resetFeedbackForm()
  showFeedbackDialog.value = true
}

const onClickCategorySettings = () => {
  showCategoryDialog.value = true
}

const onClickRuleSettings = () => {
  showRuleDialog.value = true
}

const onClickCustomTags = () => {
  loadCustomTags()
  showCustomTagsDialog.value = true
}

const onClickDataAnalysis = () => {
  showDataAnalysisDialog.value = true
  nextTick(() => {
    initCharts()
  })
}

const onClickIterationPlan = () => {
  loadIterationList()
  showIterationDialog.value = true
}

const onClickHelpCenter = () => {
  loadHelpCenterData()
  showHelpCenterDialog.value = true
}

const onClickPermissionManagement = () => {
  loadPermissions()
  showPermissionDialog.value = true
}

const onClickFeedbackReminder = () => {
  showReminderDialog.value = true
}

const onClickTemplateCreation = () => {
  loadTemplateList()
  showTemplateDialog.value = true
}

const onClickCommunityBuilding = () => {
  loadCommunityList()
  showCommunityDialog.value = true
}

const onClickChannelIntegration = () => {
  loadChannelList()
  showChannelDialog.value = true
}

// 表格按钮事件处理
const onTableButtonClick = (data: any, rowData?: any) => {
  console.log('onTableButtonClick data:', data)
  console.log('rowData:', rowData)

  let btn, row

  // 处理不同的调用格式
  if (data && typeof data === 'object' && data.btn && data.scope) {
    // 格式1: { btn, scope } - 来自普通按钮点击
    btn = data.btn
    row = data.scope
    console.log('使用格式1解析')
  } else if (data && data.code) {
    // 格式2: 直接传递按钮对象
    btn = data
    row = rowData // 第二个参数是行数据
    console.log('使用格式2解析')
  } else {
    console.error('无法解析按钮点击数据:', data)
    return
  }

  console.log('解析后的 btn:', btn, 'row:', row)
  console.log('btn.code:', btn?.code)

  if (!btn || !row) {
    console.error('按钮或行数据缺失:', { btn, row })
    return
  }

  currentRow.value = row

  currentFeedbackId.value = row.id

  switch (btn.code) {
    case 'view':
      loadDetailData(row.id)
      showDetailDialog.value = true
      break
    case 'assign':
      loadAssignData(row.id)
      showAssignDialog.value = true
      break
    case 'process':
      loadProcessData(row.id)
      showProcessDialog.value = true
      break
    case 'delete':
      handleDeleteFeedback(row)
      break
    case 'comment':
      resetCommentForm()
      showCommentDialog.value = true
      break
    case 'reply':
      loadReplyData(row.id)
      showReplyDialog.value = true
      break
    case 'satisfaction':
      loadSatisfactionData(row.id)
      showSatisfactionDialog.value = true
      break
    case 'history':
      console.log('点击历史记录按钮，用户:', row.submitter)
      loadUserHistory(row.submitter)
      showHistoryDialog.value = true
      break
    case 'profile':
      console.log('点击用户画像按钮，用户:', row.submitter)
      loadUserProfile(row.submitter)
      showUserProfileDialog.value = true
      break
    default:
      ElMessage.warning('未知操作')
  }
}

// 处理反馈
const handleProcessFeedback = (row: UserFeedback) => {
  ElMessageBox.confirm(
    `确定要处理反馈"${row.title}"吗？`,
    '确认处理',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = feedbackList.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      feedbackList.value[index].status = '处理中'
      saveDataToCache()
      ElMessage.success('处理成功')
    }
  }).catch(() => {
    ElMessage.info('已取消处理')
  })
}

// 删除反馈
const handleDeleteFeedback = (row: UserFeedback) => {
  ElMessageBox.confirm(
    `确定要删除反馈"${row.title}"吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = feedbackList.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      feedbackList.value.splice(index, 1)
      saveDataToCache()
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 用户反馈表单相关方法
const resetFeedbackForm = () => {
  Object.assign(feedbackForm, {
    id: '',
    title: '',
    content: '',
    feedbackType: '',
    category: '',
    priority: '',
    tags: [],
    submitter: '',
    submitTime: ''
  })

  nextTick(() => {
    feedbackFormRef.value?.clearValidate()
  })
}

const onFeedbackFormConfirm = async () => {
  if (!feedbackFormRef.value) return

  try {
    await feedbackFormRef.value.validate()
    feedbackLoading.value = true

    // 模拟异步操作
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (feedbackDialogMode.value === 'add') {
      const newFeedback: UserFeedback = {
        id: generateId(),
        title: feedbackForm.title,
        content: feedbackForm.content,
        feedbackType: feedbackForm.feedbackType,
        category: feedbackForm.category,
        submitter: '当前用户', // 实际应用中从用户信息获取
        submitTime: formatDateTime(),
        status: '待处理',
        priority: feedbackForm.priority,
        tags: feedbackForm.tags
      }

      feedbackList.value.unshift(newFeedback)
      ElMessage.success('新增成功')
    } else if (feedbackDialogMode.value === 'edit') {
      const index = feedbackList.value.findIndex(item => item.id === feedbackForm.id)
      if (index !== -1) {
        Object.assign(feedbackList.value[index], {
          title: feedbackForm.title,
          content: feedbackForm.content,
          feedbackType: feedbackForm.feedbackType,
          category: feedbackForm.category,
          priority: feedbackForm.priority,
          tags: feedbackForm.tags
        })
        ElMessage.success('编辑成功')
      }
    }

    saveDataToCache()
    showFeedbackDialog.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    feedbackLoading.value = false
  }
}

// 反馈分类相关方法
const onAddCategory = () => {
  categoryDialogMode.value = 'add'
  resetCategoryForm()
  showCategoryFormDialog.value = true
}

const editCategory = (row: FeedbackCategory) => {
  categoryDialogMode.value = 'edit'
  currentCategory.value = row
  Object.assign(categoryForm, row)
  showCategoryFormDialog.value = true
}

const deleteCategory = (row: FeedbackCategory) => {
  ElMessageBox.confirm(
    `确定要删除分类"${row.name}"吗？删除后该分类将不再可用于新增反馈。`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = categoryList.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      categoryList.value.splice(index, 1)
      saveCategoryToCache()
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

const updateCategoryStatus = (row: FeedbackCategory) => {
  saveCategoryToCache()
  ElMessage.success(`分类状态已${row.status ? '启用' : '禁用'}`)
}

const resetCategoryForm = () => {
  Object.assign(categoryForm, {
    id: '',
    name: '',
    description: '',
    status: true
  })

  nextTick(() => {
    categoryFormRef.value?.clearValidate()
  })
}

const onCategoryFormConfirm = async () => {
  if (!categoryFormRef.value) return

  try {
    await categoryFormRef.value.validate()
    categoryLoading.value = true

    // 模拟异步操作
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (categoryDialogMode.value === 'add') {
      const newCategory: FeedbackCategory = {
        id: generateId(),
        name: categoryForm.name,
        description: categoryForm.description,
        status: categoryForm.status,
        createTime: formatDateTime()
      }

      categoryList.value.push(newCategory)
      ElMessage.success('新增成功')
    } else if (categoryDialogMode.value === 'edit') {
      const index = categoryList.value.findIndex(item => item.id === categoryForm.id)
      if (index !== -1) {
        Object.assign(categoryList.value[index], {
          name: categoryForm.name,
          description: categoryForm.description,
          status: categoryForm.status
        })
        ElMessage.success('编辑成功')
      }
    }

    saveCategoryToCache()
    showCategoryFormDialog.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    categoryLoading.value = false
  }
}

// 规则设置确认方法
const onRuleSettingsConfirm = (config: any) => {
  ElMessage.success('规则设置已保存')
}

// ==================== 自定义标签功能 ====================
const loadCustomTags = () => {
  try {
    const cached = localStorage.getItem('userFeedbackManagement_customTags')
    if (cached) {
      const data = JSON.parse(cached)
      customTags.value = data.customTags || []
      hotTags.value = data.hotTags || []
    }
  } catch (error) {
    console.error('加载自定义标签失败:', error)
  }
}

const saveCustomTags = () => {
  try {
    const data = {
      customTags: customTags.value,
      hotTags: hotTags.value
    }
    localStorage.setItem('userFeedbackManagement_customTags', JSON.stringify(data))
    ElMessage.success('自定义标签保存成功')
    showCustomTagsDialog.value = false
  } catch (error) {
    console.error('保存自定义标签失败:', error)
    ElMessage.error('保存失败')
  }
}

const addCustomTag = () => {
  customTags.value.push({ name: '新标签', editing: true })
  nextTick(() => {
    // 聚焦到新添加的输入框
    const inputs = document.querySelectorAll('.custom-tags-content input')
    const lastInput = inputs[inputs.length - 1] as HTMLInputElement
    if (lastInput) {
      lastInput.focus()
      lastInput.select()
    }
  })
}

const addHotTag = () => {
  hotTags.value.push({ name: '热门标签', editing: true })
  nextTick(() => {
    // 聚焦到新添加的输入框
    const inputs = document.querySelectorAll('.custom-tags-content input')
    const lastInput = inputs[inputs.length - 1] as HTMLInputElement
    if (lastInput) {
      lastInput.focus()
      lastInput.select()
    }
  })
}

const startEditTag = (tag: any) => {
  tag.editing = true
  nextTick(() => {
    const input = document.querySelector('.custom-tags-content input') as HTMLInputElement
    if (input) {
      input.focus()
      input.select()
    }
  })
}

const finishEditTag = (tag: any) => {
  if (!tag.name.trim()) {
    tag.name = '标签'
  }
  tag.editing = false
}

const removeCustomTag = (index: number) => {
  customTags.value.splice(index, 1)
}

const removeHotTag = (index: number) => {
  hotTags.value.splice(index, 1)
}

// ==================== 数据分析功能 ====================
const initCharts = () => {
  initPieChart()
  initBarChart()
}

const initPieChart = () => {
  if (!pieChartRef.value) return

  const chart = echarts.init(pieChartRef.value)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'right'
    },
    series: [
      {
        name: '反馈分类',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 120, name: '分类1' },
          { value: 5, name: '分类2' },
          { value: 80, name: '分类3' },
          { value: 45, name: '分类4' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

const initBarChart = () => {
  if (!barChartRef.value) return

  const chart = echarts.init(barChartRef.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['处理数量', '提交数量']
    },
    xAxis: [
      {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月']
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '效率',
        position: 'right'
      }
    ],
    series: [
      {
        name: '处理数量',
        type: 'bar',
        data: [100, 140, 230, 30, 160],
        itemStyle: {
          color: '#5470c6'
        }
      },
      {
        name: '提交数量',
        type: 'line',
        yAxisIndex: 1,
        data: [150, 170, 180, 120, 200],
        itemStyle: {
          color: '#91cc75'
        }
      }
    ]
  }
  chart.setOption(option)
}

// ==================== 权限管理功能 ====================
const loadPermissions = () => {
  try {
    const cached = localStorage.getItem('userFeedbackManagement_permissions')
    if (cached) {
      permissionList.value = JSON.parse(cached)
    }
  } catch (error) {
    console.error('加载权限配置失败:', error)
  }
}

const savePermissions = () => {
  if (!permissionForm.selectedObjects.length || !permissionForm.permissions.length) {
    ElMessage.warning('请选择授权人员和操作权限')
    return
  }

  // 添加到权限列表
  permissionForm.selectedObjects.forEach((objectValue: string) => {
    const objectOption = allUserOptions.value.find(opt => opt.value === objectValue)
    if (objectOption) {
      const existingIndex = permissionList.value.findIndex(
        (item: any) => item.objectValue === objectValue
      )

      const permissionData = {
        objectName: objectOption.label,
        objectValue: objectValue,
        permissions: permissionForm.permissions.join('、')
      }

      if (existingIndex >= 0) {
        permissionList.value[existingIndex] = permissionData
      } else {
        permissionList.value.push(permissionData)
      }
    }
  })

  // 保存到localStorage
  try {
    localStorage.setItem('userFeedbackManagement_permissions', JSON.stringify(permissionList.value))
    ElMessage.success('权限配置保存成功')

    // 重置表单
    permissionForm.selectedObjects = []
    permissionForm.permissions = []
  } catch (error) {
    console.error('保存权限配置失败:', error)
    ElMessage.error('保存失败')
  }
}

const removePermission = (index: number) => {
  permissionList.value.splice(index, 1)
  try {
    localStorage.setItem('userFeedbackManagement_permissions', JSON.stringify(permissionList.value))
    ElMessage.success('权限删除成功')
  } catch (error) {
    console.error('删除权限失败:', error)
    ElMessage.error('删除失败')
  }
}

// 生命周期
// 用户反馈操作相关方法
const loadDetailData = (feedbackId: string) => {
  const feedback = feedbackList.value.find(item => item.id === feedbackId)
  if (feedback) {
    detailData.value = {
      ...feedback,
      assignInfo: loadFromLocalStorage(`feedback_assign_${feedbackId}`, null),
      processInfo: loadFromLocalStorage(`feedback_process_${feedbackId}`, null),
      comments: loadFromLocalStorage(`feedback_comments_${feedbackId}`, []),
      replies: loadFromLocalStorage(`feedback_replies_${feedbackId}`, []),
      satisfaction: loadFromLocalStorage(`feedback_satisfaction_${feedbackId}`, null)
    }
  }
}

const loadReplyList = (feedbackId: string) => {
  replyList.value = loadFromLocalStorage(`feedback_replies_${feedbackId}`, [])
}

// 加载回复数据（包含评论）
const loadReplyData = (feedbackId: string) => {
  // 加载评论作为回复列表显示
  const comments = loadFromLocalStorage(`feedback_comments_${feedbackId}`, [])
  const replies = loadFromLocalStorage(`feedback_replies_${feedbackId}`, [])

  // 合并评论和回复，按时间排序
  const allItems = [
    ...comments.map(item => ({ ...item, type: 'comment' })),
    ...replies.map(item => ({ ...item, type: 'reply' }))
  ].sort((a, b) => new Date(a.commentTime || a.replyTime).getTime() - new Date(b.commentTime || b.replyTime).getTime())

  replyList.value = allItems
}

// 加载指派数据
const loadAssignData = (feedbackId: string) => {
  const assignInfo = loadFromLocalStorage(`feedback_assign_${feedbackId}`, null)
  if (assignInfo) {
    Object.assign(assignForm, {
      assignee: assignInfo.assignee
    })
  } else {
    resetAssignForm()
  }
}

// 加载处理数据
const loadProcessData = (feedbackId: string) => {
  const processInfo = loadFromLocalStorage(`feedback_process_${feedbackId}`, null)
  if (processInfo) {
    Object.assign(processForm, {
      processTime: processInfo.processTime,
      processResult: processInfo.processResult
    })
  } else {
    resetProcessForm()
  }
}

// 指派功能
const onAssignConfirm = async () => {
  try {
    const assignInfo = {
      assignee: assignForm.assignee,
      assignTime: formatDateTime(),
      assignBy: '当前用户'
    }

    saveToLocalStorage(`feedback_assign_${currentFeedbackId.value}`, assignInfo)

    // 更新反馈状态
    const feedback = feedbackList.value.find(item => item.id === currentFeedbackId.value)
    if (feedback) {
      feedback.status = '已指派'
    }
    saveDataToCache()

    ElMessage.success('指派成功')
    showAssignDialog.value = false
  } catch (error) {
    ElMessage.error('指派失败')
  }
}

// 处理功能
const onProcessConfirm = async () => {
  try {
    const processInfo = {
      processTime: processForm.processTime,
      processResult: processForm.processResult,
      processBy: '当前用户'
    }

    saveToLocalStorage(`feedback_process_${currentFeedbackId.value}`, processInfo)

    // 更新反馈状态
    const feedback = feedbackList.value.find(item => item.id === currentFeedbackId.value)
    if (feedback) {
      feedback.status = '已处理'
    }
    saveDataToCache()

    ElMessage.success('处理成功')
    showProcessDialog.value = false
  } catch (error) {
    ElMessage.error('处理失败')
  }
}

// 评论功能
const onCommentConfirm = async () => {
  try {
    const comments = loadFromLocalStorage(`feedback_comments_${currentFeedbackId.value}`, [])
    const newComment = {
      id: generateId(),
      comment: commentForm.comment,
      commentBy: '当前用户',
      commentTime: formatDateTime()
    }

    comments.push(newComment)
    saveToLocalStorage(`feedback_comments_${currentFeedbackId.value}`, comments)

    ElMessage.success('评论成功')
    showCommentDialog.value = false
  } catch (error) {
    ElMessage.error('评论失败')
  }
}

// 回复功能
const onReplyConfirm = async () => {
  try {
    const replies = loadFromLocalStorage(`feedback_replies_${currentFeedbackId.value}`, [])
    const newReply = {
      id: generateId(),
      reply: replyForm.reply,
      replyBy: '当前用户',
      replyTime: formatDateTime()
    }

    replies.push(newReply)
    saveToLocalStorage(`feedback_replies_${currentFeedbackId.value}`, replies)
    replyList.value = replies

    ElMessage.success('回复成功')
    showReplyFormDialog.value = false
  } catch (error) {
    ElMessage.error('回复失败')
  }
}

// 满意度评价功能
const onSatisfactionConfirm = async () => {
  try {
    const satisfactionInfo = {
      rating: satisfactionForm.rating,
      comment: satisfactionForm.comment,
      evaluateBy: '当前用户',
      evaluateTime: formatDateTime()
    }

    saveToLocalStorage(`feedback_satisfaction_${currentFeedbackId.value}`, satisfactionInfo)

    ElMessage.success('评价成功')
    showSatisfactionDialog.value = false
  } catch (error) {
    ElMessage.error('评价失败')
  }
}

// 文件上传处理
const handleFileUpload = (file: any, fileList: string[]) => {
  // 验证文件格式
  const allowedTypes = ['rar', 'zip', 'doc', 'docx', 'pdf', 'jpg', 'png', 'xls', 'xlsx']
  const fileExtension = file.name.split('.').pop()?.toLowerCase()

  if (!allowedTypes.includes(fileExtension)) {
    ElMessage.error('不支持的文件格式')
    return false
  }

  // 模拟文件上传，实际应用中应该上传到服务器
  const fileInfo = {
    name: file.name,
    url: URL.createObjectURL(file),
    size: file.size
  }
  fileList.push(fileInfo)

  ElMessage.success('文件上传成功')
  return false // 阻止自动上传
}

// 文件下载处理
const downloadAttachment = (attachments: any[]) => {
  if (attachments && attachments.length > 0) {
    attachments.forEach(file => {
      const link = document.createElement('a')
      link.href = file.url || file
      link.download = file.name || '附件'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    })
  }
}

// 删除文件
const removeFile = (fileList: any[], index: number) => {
  fileList.splice(index, 1)
}

// 开始画像构建
const startProfileBuilding = () => {
  profileLoading.value = true

  setTimeout(() => {
    profileLoading.value = false
    // 构建完成后渲染图表
    nextTick(() => {
      renderProfileChart()
    })
  }, 1000)
}

// 渲染用户画像图表
const renderProfileChart = () => {
  try {
    console.log('开始渲染图表')

    if (!profileChartRef.value) {
      console.log('图表容器未找到')
      return
    }

    const chart = echarts.init(profileChartRef.value)

    // 模拟用户反馈分类统计数据
    const chartData = [
      { value: 35, name: '产品建议' },
      { value: 25, name: '技术支持' },
      { value: 20, name: '服务投诉' },
      { value: 20, name: '其他' }
    ]

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: chartData.map(item => item.name)
      },
      series: [
        {
          name: '反馈分类统计',
          type: 'pie',
          radius: '50%',
          center: ['60%', '50%'],
          data: chartData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          itemStyle: {
            borderRadius: 5,
            borderColor: '#fff',
            borderWidth: 2
          }
        }
      ],
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666']
    }

    chart.setOption(option)
    console.log('图表渲染完成')

    // 响应式处理
    const resizeHandler = () => {
      chart.resize()
    }
    window.addEventListener('resize', resizeHandler)
  } catch (error) {
    console.error('渲染图表失败:', error)
  }
}

// 历史记录分页
const onHistoryPageChange = (page: number) => {
  historyPagination.page = page
}

// 历史记录搜索
const filteredHistoryList = computed(() => {
  if (!historySearchKeyword.value) return historyList.value
  return historyList.value.filter((item: any) =>
    item.content.toLowerCase().includes(historySearchKeyword.value.toLowerCase()) ||
    item.type.toLowerCase().includes(historySearchKeyword.value.toLowerCase())
  )
})

// 历史记录分页数据
const paginatedHistoryList = computed(() => {
  const start = (historyPagination.page - 1) * historyPagination.size
  const end = start + historyPagination.size
  return filteredHistoryList.value.slice(start, end)
})

// 反馈社区建设的编辑和删除方法
const editCommunityItem = (item: ManagementItem) => {
  communityEditMode.value = true
  Object.assign(communityForm, {
    id: item.id,
    name: item.name,
    content: item.content,
    attachments: item.attachments || []
  })
  showCommunityFormDialog.value = true
}

const deleteCommunityItem = (item: ManagementItem) => {
  ElMessageBox.confirm('确定要删除这条记录吗？', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = communityList.value.findIndex(i => i.id === item.id)
    if (index !== -1) {
      communityList.value.splice(index, 1)
      saveToLocalStorage('feedback_community_list', communityList.value)
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 迭代计划制定的编辑和删除方法
const editIterationItem = (item: ManagementItem) => {
  iterationEditMode.value = true
  Object.assign(iterationForm, {
    id: item.id,
    name: item.name,
    content: item.content,
    attachments: item.attachments || []
  })
  showIterationFormDialog.value = true
}

const deleteIterationItem = (item: ManagementItem) => {
  ElMessageBox.confirm('确定要删除这条记录吗？', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = iterationList.value.findIndex(i => i.id === item.id)
    if (index !== -1) {
      iterationList.value.splice(index, 1)
      saveToLocalStorage('feedback_iteration_list', iterationList.value)
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 反馈模板创建的编辑和删除方法
const editTemplateItem = (item: ManagementItem) => {
  templateEditMode.value = true
  Object.assign(templateForm, {
    id: item.id,
    name: item.name,
    content: item.content,
    attachments: item.attachments || []
  })
  showTemplateFormDialog.value = true
}

const deleteTemplateItem = (item: ManagementItem) => {
  ElMessageBox.confirm('确定要删除这条记录吗？', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = templateList.value.findIndex(i => i.id === item.id)
    if (index !== -1) {
      templateList.value.splice(index, 1)
      saveToLocalStorage('feedback_template_list', templateList.value)
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 多渠道整合的编辑和删除方法
const editChannelItem = (item: ManagementItem) => {
  channelEditMode.value = true
  Object.assign(channelForm, {
    id: item.id,
    name: item.name,
    content: item.content
  })
  showChannelFormDialog.value = true
}

const deleteChannelItem = (item: ManagementItem) => {
  ElMessageBox.confirm('确定要删除这条记录吗？', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const index = channelList.value.findIndex(i => i.id === item.id)
    if (index !== -1) {
      channelList.value.splice(index, 1)
      saveToLocalStorage('feedback_channel_list', channelList.value)
      ElMessage.success('删除成功')
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 表单引用变量
const assignFormRef = ref()
const processFormRef = ref()
const commentFormRef = ref()
const replyFormRef = ref()
const satisfactionFormRef = ref()

// 四个管理功能的表单确认方法
const communityFormRef = ref()
const onCommunityFormConfirm = async () => {
  try {
    await communityFormRef.value.validate()

    if (communityEditMode.value) {
      // 编辑模式
      const index = communityList.value.findIndex(item => item.id === communityForm.id)
      if (index !== -1) {
        communityList.value[index] = {
          ...communityList.value[index],
          name: communityForm.name,
          content: communityForm.content,
          attachments: communityForm.attachments
        }
        ElMessage.success('修改成功')
      }
    } else {
      // 新增模式
      const newItem: ManagementItem = {
        id: generateId(),
        name: communityForm.name,
        content: communityForm.content,
        attachments: communityForm.attachments,
        createTime: formatDateTime()
      }
      communityList.value.push(newItem)
      ElMessage.success('新增成功')
    }

    saveToLocalStorage('feedback_community_list', communityList.value)
    showCommunityFormDialog.value = false
    communityEditMode.value = false
  } catch (error) {
    ElMessage.error('请完善表单信息')
  }
}

const iterationFormRef = ref()
const onIterationFormConfirm = async () => {
  try {
    await iterationFormRef.value.validate()

    if (iterationEditMode.value) {
      // 编辑模式
      const index = iterationList.value.findIndex(item => item.id === iterationForm.id)
      if (index !== -1) {
        iterationList.value[index] = {
          ...iterationList.value[index],
          name: iterationForm.name,
          content: iterationForm.content,
          attachments: iterationForm.attachments
        }
        ElMessage.success('修改成功')
      }
    } else {
      // 新增模式
      const newItem: ManagementItem = {
        id: generateId(),
        name: iterationForm.name,
        content: iterationForm.content,
        attachments: iterationForm.attachments,
        createTime: formatDateTime()
      }
      iterationList.value.push(newItem)
      ElMessage.success('新增成功')
    }

    saveToLocalStorage('feedback_iteration_list', iterationList.value)
    showIterationFormDialog.value = false
    iterationEditMode.value = false
  } catch (error) {
    ElMessage.error('请完善表单信息')
  }
}

const templateFormRef = ref()
const onTemplateFormConfirm = async () => {
  try {
    await templateFormRef.value.validate()

    if (templateEditMode.value) {
      // 编辑模式
      const index = templateList.value.findIndex(item => item.id === templateForm.id)
      if (index !== -1) {
        templateList.value[index] = {
          ...templateList.value[index],
          name: templateForm.name,
          content: templateForm.content,
          attachments: templateForm.attachments
        }
        ElMessage.success('修改成功')
      }
    } else {
      // 新增模式
      const newItem: ManagementItem = {
        id: generateId(),
        name: templateForm.name,
        content: templateForm.content,
        attachments: templateForm.attachments,
        createTime: formatDateTime()
      }
      templateList.value.push(newItem)
      ElMessage.success('新增成功')
    }

    saveToLocalStorage('feedback_template_list', templateList.value)
    showTemplateFormDialog.value = false
    templateEditMode.value = false
  } catch (error) {
    ElMessage.error('请完善表单信息')
  }
}

const channelFormRef = ref()
const onChannelFormConfirm = async () => {
  try {
    await channelFormRef.value.validate()

    if (channelEditMode.value) {
      // 编辑模式
      const index = channelList.value.findIndex(item => item.id === channelForm.id)
      if (index !== -1) {
        channelList.value[index] = {
          ...channelList.value[index],
          name: channelForm.name,
          content: channelForm.content
        }
        ElMessage.success('修改成功')
      }
    } else {
      // 新增模式
      const newItem: ManagementItem = {
        id: generateId(),
        name: channelForm.name,
        content: channelForm.content,
        createTime: formatDateTime()
      }
      channelList.value.push(newItem)
      ElMessage.success('新增成功')
    }

    saveToLocalStorage('feedback_channel_list', channelList.value)
    showChannelFormDialog.value = false
    channelEditMode.value = false
  } catch (error) {
    ElMessage.error('请完善表单信息')
  }
}

// 帮助中心的表单确认方法
const faqFormRef = ref()
const onFaqFormConfirm = async () => {
  try {
    await faqFormRef.value.validate()

    const newItem: HelpCenterItem = {
      id: generateId(),
      title: faqForm.title,
      content: faqForm.content,
      attachments: faqForm.attachments,
      createTime: formatDateTime()
    }

    faqList.value.push(newItem)
    saveToLocalStorage('feedback_faq_list', faqList.value)

    ElMessage.success('新增成功')
    showFaqFormDialog.value = false
  } catch (error) {
    ElMessage.error('请完善表单信息')
  }
}

const helpFormRef = ref()
const onHelpFormConfirm = async () => {
  try {
    await helpFormRef.value.validate()

    const newItem: HelpCenterItem = {
      id: generateId(),
      title: helpForm.title,
      content: helpForm.content,
      attachments: helpForm.attachments,
      createTime: formatDateTime()
    }

    helpList.value.push(newItem)
    saveToLocalStorage('feedback_help_list', helpList.value)

    ElMessage.success('新增成功')
    showHelpFormDialog.value = false
  } catch (error) {
    ElMessage.error('请完善表单信息')
  }
}

const privacyFormRef = ref()
const onPrivacyFormConfirm = async () => {
  try {
    await privacyFormRef.value.validate()

    const newItem: HelpCenterItem = {
      id: generateId(),
      title: privacyForm.title,
      content: privacyForm.content,
      attachments: privacyForm.attachments,
      createTime: formatDateTime()
    }

    privacyList.value.push(newItem)
    saveToLocalStorage('feedback_privacy_list', privacyList.value)

    ElMessage.success('新增成功')
    showPrivacyFormDialog.value = false
  } catch (error) {
    ElMessage.error('请完善表单信息')
  }
}

onMounted(() => {
  loadDataFromCache()
  loadCategoryFromCache()
  loadCustomTags()
  loadPermissions()
  // 加载新功能数据
  loadCommunityList()
  loadIterationList()
  loadTemplateList()
  loadChannelList()
  loadHelpCenterData()
})
</script>

<style scoped>
.user-feedback-tab {
  height: 100%;
}

.top-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.top-buttons .el-button {
  margin-bottom: 8px;
}

.search-area {
  padding: 16px 0;
  margin-bottom: 16px;
}

.table-area {
  flex: 1;
  overflow: hidden;
}

.el-form--inline .el-form-item {
  margin-right: 16px;
  margin-bottom: 8px;
}

.el-dropdown {
  margin-left: 8px;
}

.el-icon--right {
  margin-left: 5px;
}

/* 表格状态标签样式 */
:deep(.el-tag) {
  border-radius: 12px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 弹窗表单样式 */
:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}

/* 分类设置表格样式 */
:deep(.el-table) {
  border-radius: 4px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 500;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .top-buttons {
    flex-direction: column;
    align-items: flex-start;
  }

  .search-area .el-form--inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 12px;
  }
}

@media (max-width: 768px) {
  .search-area {
    padding: 12px;
  }

  .top-buttons .el-button {
    width: 100%;
    margin-bottom: 8px;
  }
}

/* 自定义标签弹窗样式 */
.custom-tags-content {
  padding: 20px;
}

.tags-section {
  margin-bottom: 30px;
}

.tags-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.tags-container {
  min-height: 60px;
  padding: 12px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
}

.editable-tag {
  display: inline-block;
  margin-right: 8px;
  margin-bottom: 8px;
}

/* 数据分析弹窗样式 */
.data-analysis-content {
  padding: 20px;
}

.chart-section {
  margin-bottom: 40px;
}

.chart-section:last-child {
  margin-bottom: 0;
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
}

.chart-container {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

.chart-item {
  flex-shrink: 0;
}

.analysis-text {
  flex-shrink: 0;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  overflow-y: auto;
}

.analysis-text h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.analysis-text p {
  margin: 0 0 15px 0;
  font-size: 13px;
  line-height: 1.6;
  color: #6c757d;
}

.analysis-text p:last-child {
  margin-bottom: 0;
}

/* 权限管理弹窗样式 */
.permission-content {
  padding: 20px;
}

.permission-list {
  margin-top: 30px;
}

.permission-list h4 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 反馈提醒弹窗样式 */
.reminder-content {
  padding: 20px;
}

.reminder-list {
  margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-container {
    flex-direction: column;
    gap: 20px;
  }

  .chart-item {
    width: 100% !important;
    height: 250px !important;
  }

  .analysis-text {
    padding: 15px;
  }

  .tags-container {
    min-height: 80px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

/* 管理功能弹窗样式 */
.management-dialog-content {
  .search-area {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 4px;
  }

  .search-form {
    .el-form-item {
      margin-bottom: 0;
    }
  }

  .table-area {
    .el-table {
      border-radius: 4px;
    }
  }
}

/* 帮助中心弹窗样式 */
.help-center-content {
  .search-section {
    text-align: center;
    margin-bottom: 30px;
  }

  .help-sections {
    display: flex;
    gap: 20px;
    justify-content: space-between;
  }

  .help-section {
    flex: 1;
    border: 1px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    min-height: 400px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;

      h3 {
        margin: 0;
        color: #333;
        font-size: 16px;
      }
    }

    .help-description {
      color: #666;
      font-size: 14px;
      margin-bottom: 15px;
      line-height: 1.5;
    }

    .help-items {
      .help-item {
        margin-bottom: 15px;
        padding: 10px;
        background-color: #f9f9f9;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background-color: #e6f7ff;
        }

        .help-question {
          font-weight: bold;
          color: #333;
          margin-bottom: 5px;
        }

        .help-answer {
          color: #666;
          font-size: 14px;
          line-height: 1.4;
        }

        mark {
          background-color: #ffeb3b;
          color: #333;
          padding: 1px 2px;
          border-radius: 2px;
        }
      }

      .empty-state {
        text-align: center;
        color: #999;
        padding: 40px 0;
      }
    }
  }
}

/* 用户反馈操作弹窗样式 */
.feedback-info {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;

  .info-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      font-weight: bold;
      color: #333;
      min-width: 80px;
    }

    .value {
      color: #666;
      margin-right: 20px;
    }

    .el-tag {
      margin-right: 5px;
    }
  }
}

.comment-section {
  h4 {
    margin: 0 0 15px 0;
    color: #333;
  }
}

.reply-dialog-content {
  .comments-section {
    margin-top: 20px;

    h4 {
      margin: 0 0 15px 0;
      color: #333;
    }

    .comment-item {
      background-color: #f9f9f9;
      padding: 15px;
      border-radius: 4px;
      margin-bottom: 10px;

      .comment-content {
        color: #333;
        margin-bottom: 10px;
        line-height: 1.5;
      }

      .comment-actions {
        text-align: right;
      }
    }

    .empty-comments {
      text-align: center;
      color: #999;
      padding: 40px 0;
    }
  }
}

.satisfaction-dialog-content {
  .process-info {
    margin: 20px 0;
    padding: 15px;
    background-color: #f0f9ff;
    border-radius: 4px;

    h4 {
      margin: 0 0 15px 0;
      color: #333;
    }
  }

  .satisfaction-section {
    h4 {
      margin: 0 0 15px 0;
      color: #333;
    }

    .satisfaction-radio-group {
      display: flex;
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;

      .el-radio {
        margin-right: 0;
        margin-bottom: 0;

        .el-radio__label {
          padding-left: 8px;
        }
      }
    }
  }
}

/* 详情弹窗样式 */
.detail-dialog-content {
  .detail-layout {
    display: flex;
    gap: 30px;
    min-height: 500px;
  }

  .detail-left {
    flex: 0 0 300px;
    border-right: 1px solid #eee;
    padding-right: 20px;

    h4 {
      margin: 0 0 20px 0;
      color: #333;
      font-size: 16px;
      border-bottom: 2px solid #409eff;
      padding-bottom: 10px;
    }

    .timeline {
      .timeline-item {
        display: flex;
        margin-bottom: 20px;
        position: relative;

        &:not(:last-child)::after {
          content: '';
          position: absolute;
          left: 15px;
          top: 40px;
          width: 2px;
          height: calc(100% + 10px);
          background-color: #e4e7ed;
        }

        .timeline-icon {
          flex: 0 0 30px;
          height: 30px;
          border-radius: 50%;
          background-color: #409eff;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          margin-right: 15px;
          z-index: 1;
        }

        .timeline-content {
          flex: 1;

          .timeline-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
          }

          .timeline-desc {
            color: #666;
            font-size: 14px;
            margin-bottom: 3px;
            line-height: 1.4;
          }

          .timeline-time {
            color: #999;
            font-size: 12px;
          }
        }
      }
    }
  }

  .detail-right {
    flex: 1;

    h4 {
      margin: 0 0 20px 0;
      color: #333;
      font-size: 16px;
      border-bottom: 2px solid #409eff;
      padding-bottom: 10px;
    }

    .detail-content {
      .detail-section {
        margin-bottom: 25px;
        padding: 15px;
        background-color: #fafafa;
        border-radius: 4px;

        h5 {
          margin: 0 0 15px 0;
          color: #333;
          font-size: 14px;
          font-weight: bold;
          border-bottom: 1px solid #eee;
          padding-bottom: 8px;
        }

        .detail-row {
          display: flex;
          margin-bottom: 10px;
          align-items: flex-start;

          &:last-child {
            margin-bottom: 0;
          }

          .detail-label {
            flex: 0 0 100px;
            font-weight: bold;
            color: #333;
          }

          .detail-value {
            flex: 1;
            color: #666;
          }

          .detail-tags {
            flex: 1;
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
          }

          .detail-content-text {
            flex: 1;
            color: #666;
            line-height: 1.5;
            background-color: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #e4e7ed;
          }
        }

        .comment-item {
          background-color: white;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 10px;
          border: 1px solid #e4e7ed;

          .comment-text {
            color: #333;
            margin-bottom: 5px;
            line-height: 1.5;
          }

          .comment-meta {
            color: #999;
            font-size: 12px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .detail-layout {
    flex-direction: column !important;
  }

  .detail-left {
    flex: none !important;
    border-right: none !important;
    border-bottom: 1px solid #eee;
    padding-right: 0 !important;
    padding-bottom: 20px;
  }

  .help-sections {
    flex-direction: column !important;
  }

  .help-section {
    margin-bottom: 20px;
  }
}

/* 文件上传样式 */
.upload-section {
  .file-list {
    margin-top: 10px;

    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background-color: #f5f5f5;
      border-radius: 4px;
      margin-bottom: 5px;

      .file-name {
        flex: 1;
        color: #333;
        font-size: 14px;
      }
    }
  }
}

/* 回复弹窗样式增强 */
.reply-dialog-content {
  .comments-section {
    .comment-item {
      .comment-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 8px;
        font-size: 12px;

        .comment-type {
          background-color: #409eff;
          color: white;
          padding: 2px 6px;
          border-radius: 3px;
          font-size: 11px;
        }

        .comment-author {
          font-weight: bold;
          color: #333;
        }

        .comment-time {
          color: #999;
        }
      }
    }
  }
}

/* 帮助中心详情弹窗样式 */
.help-detail-content {
  .detail-item {
    display: flex;
    margin-bottom: 15px;
    align-items: flex-start;

    .detail-label {
      flex: 0 0 80px;
      font-weight: bold;
      color: #333;
    }

    .detail-value {
      flex: 1;
      color: #666;
      line-height: 1.5;

      .attachment-item {
        margin-bottom: 5px;
      }
    }
  }
}

/* 权限管理弹窗样式 */
.permission-content {
  .permission-form-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 6px;

    h4 {
      margin: 0 0 20px 0;
      color: #333;
      font-size: 16px;
      border-bottom: 2px solid #409eff;
      padding-bottom: 10px;
    }
  }

  .permission-list-section {
    h4 {
      margin: 0 0 15px 0;
      color: #333;
      font-size: 16px;
      border-bottom: 2px solid #409eff;
      padding-bottom: 10px;
    }

    .empty-permissions {
      text-align: center;
      color: #999;
      padding: 40px 0;
      background-color: #f5f5f5;
      border-radius: 4px;
      margin-top: 10px;
    }
  }
}

/* 历史记录弹窗样式 */
.history-content {
  .history-list {
    .history-item {
      padding: 15px;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      margin-bottom: 10px;
      background-color: #f8f9fa;

      .history-type {
        font-weight: bold;
        color: #409eff;
        margin-bottom: 8px;
        font-size: 14px;
      }

      .history-content-text {
        color: #333;
        line-height: 1.5;
        margin-bottom: 8px;
      }

      .history-time {
        color: #999;
        font-size: 12px;
        text-align: right;
      }
    }

    .empty-history {
      text-align: center;
      color: #999;
      padding: 40px 0;
      background-color: #f5f5f5;
      border-radius: 4px;
    }
  }
}

/* 用户画像弹窗样式 */
.profile-content {
  .user-select-section {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 6px;
  }

  .profile-info {
    .profile-basic-info {
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      padding: 20px;
      margin-bottom: 30px;

      .info-row {
        display: flex;
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }

        .info-item {
          flex: 1;
          display: flex;
          align-items: flex-start;

          .label {
            flex: 0 0 80px;
            font-weight: bold;
            color: #333;
          }

          .value {
            flex: 1;
            color: #666;
          }
        }
      }
    }

    .profile-chart-section {
      h4 {
        margin: 0 0 20px 0;
        color: #333;
        font-size: 16px;
        border-bottom: 2px solid #409eff;
        padding-bottom: 10px;
      }
    }
  }

  .profile-loading {
    text-align: center;
    padding: 40px 0;
  }
}
</style>
