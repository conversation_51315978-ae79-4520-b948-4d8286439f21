import { test, expect } from '@playwright/test'

test.describe('子任务关系审核页面功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 清理本地存储
    await page.evaluate(() => {
      localStorage.clear()
    })
  })

  test('页面基本加载和显示测试', async ({ page }) => {
    // 导航到审核页面
    await page.goto('http://localhost:5176/taskObjectiveDecomposition/relationAudit/1?free=true')
    
    // 等待页面加载完成
    await page.waitForSelector('.subtask-relation-audit-page', { timeout: 30000 })
    
    // 验证页面标题
    await expect(page.locator('.page-title')).toHaveText('子任务关系审核')
    
    // 验证主要组件是否存在
    await expect(page.locator('.left-panel')).toBeVisible()
    await expect(page.locator('.right-panel')).toBeVisible()
    
    // 验证流程说明
    await expect(page.locator('.panel-title')).toHaveText('流程说明')
    await expect(page.locator('.step-item')).toHaveCount(4)
    
    // 验证当前步骤高亮
    await expect(page.locator('.step-item.active .step-title')).toHaveText('关系审核')
    
    // 验证任务信息卡片
    await expect(page.locator('.info-card .card-title')).toHaveText('任务信息')
    
    // 验证流程图区域
    await expect(page.locator('.flowchart-container .card-title')).toHaveText('子任务关系图')
    await expect(page.locator('.vue-flow-container')).toBeVisible()
    
    // 验证审核表单
    await expect(page.locator('.audit-form-card .card-title')).toHaveText('审核操作')
    await expect(page.locator('.audit-form')).toBeVisible()
    
    console.log('✅ 页面基本加载和显示测试通过')
  })

  test('按钮导航功能测试', async ({ page }) => {
    // 先导航到详情页面
    await page.goto('http://localhost:5176/taskObjectiveDecomposition/detail/1?free=true')
    await page.waitForSelector('.task-detail-page', { timeout: 30000 })
    
    // 查找并点击"子任务关系审核"按钮
    const auditButton = page.locator('text=子任务关系审核')
    await expect(auditButton).toBeVisible()
    await auditButton.click()
    
    // 验证页面跳转
    await page.waitForURL('**/taskObjectiveDecomposition/relationAudit/1**')
    await expect(page.locator('.page-title')).toHaveText('子任务关系审核')
    
    console.log('✅ 按钮导航功能测试通过')
  })

  test('流程图显示测试', async ({ page }) => {
    // 导航到审核页面
    await page.goto('http://localhost:5176/taskObjectiveDecomposition/relationAudit/1?free=true')
    await page.waitForSelector('.subtask-relation-audit-page', { timeout: 30000 })

    // 验证流程图容器存在
    await expect(page.locator('.vue-flow-container')).toBeVisible()
    
    // 验证流程图节点存在
    await expect(page.locator('.vue-flow__node')).toHaveCount.greaterThan(0)
    
    // 验证流程图边存在
    await expect(page.locator('.vue-flow__edge')).toHaveCount.greaterThan(0)
    
    // 验证自定义节点样式
    await expect(page.locator('.custom-node')).toHaveCount.greaterThan(0)
    
    console.log('✅ 流程图显示测试通过')
  })

  test('审核表单验证测试', async ({ page }) => {
    // 导航到审核页面
    await page.goto('http://localhost:5176/taskObjectiveDecomposition/relationAudit/1?free=true')
    await page.waitForSelector('.subtask-relation-audit-page', { timeout: 30000 })
    
    // 测试空表单提交
    const submitButton = page.locator('text=提交审核')
    await submitButton.click()
    
    // 验证审核结果必填提示
    await expect(page.locator('.el-message')).toContainText('请选择审核结果')
    
    // 选择审核结果
    await page.locator('text=通过').click()
    await submitButton.click()
    
    // 验证审核意见必填提示
    await expect(page.locator('.el-message')).toContainText('请填写审核意见')
    
    // 输入过短的审核意见
    await page.locator('textarea[placeholder*="请输入审核意见"]').fill('短')
    await submitButton.click()
    
    // 验证长度限制提示
    await expect(page.locator('.el-message')).toContainText('审核意见长度应在5-500字符之间')
    
    console.log('✅ 审核表单验证测试通过')
  })

  test('审核提交功能测试', async ({ page }) => {
    // 导航到审核页面
    await page.goto('http://localhost:5176/taskObjectiveDecomposition/relationAudit/1?free=true')
    await page.waitForSelector('.subtask-relation-audit-page', { timeout: 30000 })
    
    // 填写审核表单
    await page.locator('text=通过').click()
    await page.locator('textarea[placeholder*="请输入审核意见"]').fill('审核通过，子任务关系合理，可以继续执行。')
    
    // 提交审核
    const submitButton = page.locator('text=提交审核')
    await submitButton.click()
    
    // 确认提交
    await page.locator('.el-message-box__btns .el-button--primary').click()
    
    // 验证提交成功消息
    await expect(page.locator('.el-message')).toContainText('子任务关系通过成功')
    
    // 等待页面跳转
    await page.waitForURL('**/taskObjectiveDecomposition/detail/1**', { timeout: 10000 })
    
    console.log('✅ 审核提交功能测试通过')
  })

  test('数据持久化测试', async ({ page }) => {
    // 导航到审核页面
    await page.goto('http://localhost:5176/taskObjectiveDecomposition/relationAudit/1?free=true')
    await page.waitForSelector('.subtask-relation-audit-page', { timeout: 30000 })
    
    // 填写并提交审核
    await page.locator('text=驳回').click()
    await page.locator('textarea[placeholder*="请输入审核意见"]').fill('存在问题需要修改，请重新提交。')
    
    const submitButton = page.locator('text=提交审核')
    await submitButton.click()
    await page.locator('.el-message-box__btns .el-button--primary').click()
    
    // 等待提交完成
    await page.waitForTimeout(3000)
    
    // 重新访问页面
    await page.goto('http://localhost:5176/taskObjectiveDecomposition/relationAudit/1?free=true')
    await page.waitForSelector('.subtask-relation-audit-page', { timeout: 30000 })
    
    // 验证审核历史记录存在
    await expect(page.locator('.audit-history-card')).toBeVisible()
    await expect(page.locator('.history-item')).toHaveCount.greaterThan(0)
    
    // 验证审核记录内容
    await expect(page.locator('.history-item .opinion')).toContainText('存在问题需要修改')
    
    console.log('✅ 数据持久化测试通过')
  })

  test('页面刷新功能测试', async ({ page }) => {
    // 导航到审核页面
    await page.goto('http://localhost:5176/taskObjectiveDecomposition/relationAudit/1?free=true')
    await page.waitForSelector('.subtask-relation-audit-page', { timeout: 30000 })
    
    // 点击刷新按钮
    const refreshButton = page.locator('.refresh-button')
    await expect(refreshButton).toBeVisible()
    await refreshButton.click()
    
    // 验证刷新成功消息
    await expect(page.locator('.el-message')).toContainText('页面刷新成功')
    
    console.log('✅ 页面刷新功能测试通过')
  })

  test('返回按钮功能测试', async ({ page }) => {
    // 导航到审核页面
    await page.goto('http://localhost:5176/taskObjectiveDecomposition/relationAudit/1?free=true')
    await page.waitForSelector('.subtask-relation-audit-page', { timeout: 30000 })
    
    // 点击返回按钮
    const returnButton = page.locator('.return-button')
    await expect(returnButton).toBeVisible()
    await returnButton.click()
    
    // 验证页面跳转回详情页
    await page.waitForURL('**/taskObjectiveDecomposition/detail/1**')
    
    console.log('✅ 返回按钮功能测试通过')
  })

  test('重置表单功能测试', async ({ page }) => {
    // 导航到审核页面
    await page.goto('http://localhost:5176/taskObjectiveDecomposition/relationAudit/1?free=true')
    await page.waitForSelector('.subtask-relation-audit-page', { timeout: 30000 })
    
    // 填写表单
    await page.locator('text=退回').click()
    await page.locator('textarea[placeholder*="请输入审核意见"]').fill('测试意见内容')
    
    // 点击重置按钮
    const resetButton = page.locator('text=重置')
    await resetButton.click()
    
    // 验证表单已重置
    await expect(page.locator('input[type="radio"]:checked')).toHaveCount(0)
    await expect(page.locator('textarea[placeholder*="请输入审核意见"]')).toHaveValue('')
    
    console.log('✅ 重置表单功能测试通过')
  })

  test('响应式布局测试', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 768, height: 1024 })
    
    // 导航到审核页面
    await page.goto('http://localhost:5176/taskObjectiveDecomposition/relationAudit/1?free=true')
    await page.waitForSelector('.subtask-relation-audit-page', { timeout: 30000 })
    
    // 验证主要组件在移动端仍然可见
    await expect(page.locator('.left-panel')).toBeVisible()
    await expect(page.locator('.right-panel')).toBeVisible()
    await expect(page.locator('.audit-form')).toBeVisible()
    
    console.log('✅ 响应式布局测试通过')
  })
})

test.describe('错误处理测试', () => {
  test('无效任务ID处理测试', async ({ page }) => {
    // 导航到不存在的任务ID
    await page.goto('http://localhost:5176/taskObjectiveDecomposition/relationAudit/999?free=true')
    await page.waitForSelector('.subtask-relation-audit-page', { timeout: 30000 })
    
    // 验证错误提示显示
    await expect(page.locator('.error-container')).toBeVisible()
    await expect(page.locator('.el-alert')).toContainText('未找到对应的任务信息')
    
    console.log('✅ 无效任务ID处理测试通过')
  })
})
