<!-- 子任务关系审核页面 -->
<script setup lang="ts" name="SubTaskRelationAudit">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Refresh, Connection } from '@element-plus/icons-vue'
import { VueFlow, MarkerType } from '@vue-flow/core'
import type { Node, Edge } from '@vue-flow/core'
import { useTaskObjectiveStore } from '@/stores/taskObjectiveStore'

// 路由和参数
const router = useRouter()
const route = useRoute()
const taskStore = useTaskObjectiveStore()

// 页面状态
const loading = ref(true)
const submitLoading = ref(false)
const refreshLoading = ref(false)
const taskId = ref(route.params.id as string)
const error = ref('')

// 从store获取任务数据
const currentTask = computed(() => {
  return taskStore.getTaskById(taskId.value)
})

// 审核表单数据
const auditForm = reactive({
  result: '', // 审核结果：通过、驳回、退回
  opinion: '', // 审核意见
  auditor: '当前用户', // 审核人
  auditTime: new Date().toLocaleString() // 审核时间
})

// 审核结果选项
const auditResultOptions = [
  { label: '通过', value: 'pass' },
  { label: '驳回', value: 'reject' },
  { label: '退回', value: 'return' }
]

// 根据任务ID生成对应的流程图数据
const generateFlowData = (taskId: string) => {
  const taskFlowData = {
    '1': {
      title: '数据收集任务流程',
      nodes: [
        {
          id: 'start',
          type: 'input',
          position: { x: 250, y: 30 },
          data: { label: '开始收集' },
          style: {
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '25px',
            width: '100px',
            height: '50px',
            fontSize: '13px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(102, 126, 234, 0.4)'
          }
        },
        {
          id: 'collect1',
          position: { x: 80, y: 130 },
          data: {
            title: '收集',
            content: '民政局基础数据\n收集整理',
            type: 'collect'
          },
          style: {
            background: 'linear-gradient(135deg, #FFA726 0%, #FF8A65 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            width: '160px',
            height: '70px',
            fontSize: '11px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(255, 167, 38, 0.4)'
          }
        },
        {
          id: 'collect2',
          position: { x: 320, y: 130 },
          data: {
            title: '收集',
            content: '各镇街数据\n统计汇总',
            type: 'collect'
          },
          style: {
            background: 'linear-gradient(135deg, #FFA726 0%, #FF8A65 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            width: '160px',
            height: '70px',
            fontSize: '11px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(255, 167, 38, 0.4)'
          }
        },
        {
          id: 'verify1',
          position: { x: 80, y: 230 },
          data: {
            title: '验证',
            content: '数据质量检查\n完整性验证',
            type: 'verify'
          },
          style: {
            background: 'linear-gradient(135deg, #42A5F5 0%, #1E88E5 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            width: '160px',
            height: '70px',
            fontSize: '11px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(66, 165, 245, 0.4)'
          }
        },
        {
          id: 'verify2',
          position: { x: 320, y: 230 },
          data: {
            title: '验证',
            content: '数据准确性核实\n异常数据处理',
            type: 'verify'
          },
          style: {
            background: 'linear-gradient(135deg, #42A5F5 0%, #1E88E5 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            width: '160px',
            height: '70px',
            fontSize: '11px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(66, 165, 245, 0.4)'
          }
        },
        {
          id: 'end',
          type: 'output',
          position: { x: 250, y: 330 },
          data: { label: '收集完成' },
          style: {
            background: 'linear-gradient(135deg, #66BB6A 0%, #4CAF50 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '25px',
            width: '100px',
            height: '50px',
            fontSize: '13px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(102, 187, 106, 0.4)'
          }
        }
      ]
    },
    '2': {
      title: '报表生成任务流程',
      nodes: [
        {
          id: 'start',
          type: 'input',
          position: { x: 250, y: 30 },
          data: { label: '开始生成' },
          style: {
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '25px',
            width: '100px',
            height: '50px',
            fontSize: '13px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(102, 126, 234, 0.4)'
          }
        },
        {
          id: 'template',
          position: { x: 200, y: 130 },
          data: {
            title: '模板',
            content: '选择报表模板\n配置参数',
            type: 'template'
          },
          style: {
            background: 'linear-gradient(135deg, #FFA726 0%, #FF8A65 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            width: '160px',
            height: '70px',
            fontSize: '11px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(255, 167, 38, 0.4)'
          }
        },
        {
          id: 'generate',
          position: { x: 200, y: 230 },
          data: {
            title: '生成',
            content: '数据处理分析\n报表自动生成',
            type: 'generate'
          },
          style: {
            background: 'linear-gradient(135deg, #42A5F5 0%, #1E88E5 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            width: '160px',
            height: '70px',
            fontSize: '11px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(66, 165, 245, 0.4)'
          }
        },
        {
          id: 'end',
          type: 'output',
          position: { x: 250, y: 330 },
          data: { label: '生成完成' },
          style: {
            background: 'linear-gradient(135deg, #66BB6A 0%, #4CAF50 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '25px',
            width: '100px',
            height: '50px',
            fontSize: '13px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(102, 187, 106, 0.4)'
          }
        }
      ]
    },
    '3': {
      title: '数据分析任务流程',
      nodes: [
        {
          id: 'start',
          type: 'input',
          position: { x: 250, y: 30 },
          data: { label: '开始分析' },
          style: {
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '25px',
            width: '100px',
            height: '50px',
            fontSize: '13px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(102, 126, 234, 0.4)'
          }
        },
        {
          id: 'analyze1',
          position: { x: 80, y: 130 },
          data: {
            title: '分析',
            content: '趋势分析\n统计计算',
            type: 'analyze'
          },
          style: {
            background: 'linear-gradient(135deg, #FFA726 0%, #FF8A65 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            width: '160px',
            height: '70px',
            fontSize: '11px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(255, 167, 38, 0.4)'
          }
        },
        {
          id: 'analyze2',
          position: { x: 320, y: 130 },
          data: {
            title: '分析',
            content: '对比分析\n异常检测',
            type: 'analyze'
          },
          style: {
            background: 'linear-gradient(135deg, #FFA726 0%, #FF8A65 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            width: '160px',
            height: '70px',
            fontSize: '11px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(255, 167, 38, 0.4)'
          }
        },
        {
          id: 'report',
          position: { x: 200, y: 230 },
          data: {
            title: '报告',
            content: '分析结果整理\n生成分析报告',
            type: 'report'
          },
          style: {
            background: 'linear-gradient(135deg, #42A5F5 0%, #1E88E5 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            width: '160px',
            height: '70px',
            fontSize: '11px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(66, 165, 245, 0.4)'
          }
        },
        {
          id: 'end',
          type: 'output',
          position: { x: 250, y: 330 },
          data: { label: '分析完成' },
          style: {
            background: 'linear-gradient(135deg, #66BB6A 0%, #4CAF50 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '25px',
            width: '100px',
            height: '50px',
            fontSize: '13px',
            fontWeight: '500',
            boxShadow: '0 4px 12px rgba(102, 187, 106, 0.4)'
          }
        }
      ]
    }
  }

  // 默认流程图数据
  const defaultFlow = {
    title: '通用任务流程',
    nodes: [
      {
        id: 'start',
        type: 'input',
        position: { x: 250, y: 30 },
        data: { label: '流程开始' },
        style: {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          border: 'none',
          borderRadius: '25px',
          width: '100px',
          height: '50px',
          fontSize: '13px',
          fontWeight: '500',
          boxShadow: '0 4px 12px rgba(102, 126, 234, 0.4)'
        }
      },
      {
        id: 'task1',
        position: { x: 80, y: 130 },
        data: {
          title: '任务',
          content: '执行具体任务\n处理相关事务',
          type: 'task'
        },
        style: {
          background: 'linear-gradient(135deg, #FFA726 0%, #FF8A65 100%)',
          color: 'white',
          border: 'none',
          borderRadius: '8px',
          width: '160px',
          height: '70px',
          fontSize: '11px',
          fontWeight: '500',
          boxShadow: '0 4px 12px rgba(255, 167, 38, 0.4)'
        }
      },
      {
        id: 'task2',
        position: { x: 320, y: 130 },
        data: {
          title: '任务',
          content: '并行处理任务\n协同完成工作',
          type: 'task'
        },
        style: {
          background: 'linear-gradient(135deg, #FFA726 0%, #FF8A65 100%)',
          color: 'white',
          border: 'none',
          borderRadius: '8px',
          width: '160px',
          height: '70px',
          fontSize: '11px',
          fontWeight: '500',
          boxShadow: '0 4px 12px rgba(255, 167, 38, 0.4)'
        }
      },
      {
        id: 'review',
        position: { x: 200, y: 230 },
        data: {
          title: '审核',
          content: '任务结果审核\n质量检查验收',
          type: 'review'
        },
        style: {
          background: 'linear-gradient(135deg, #42A5F5 0%, #1E88E5 100%)',
          color: 'white',
          border: 'none',
          borderRadius: '8px',
          width: '160px',
          height: '70px',
          fontSize: '11px',
          fontWeight: '500',
          boxShadow: '0 4px 12px rgba(66, 165, 245, 0.4)'
        }
      },
      {
        id: 'end',
        type: 'output',
        position: { x: 250, y: 330 },
        data: { label: '流程结束' },
        style: {
          background: 'linear-gradient(135deg, #66BB6A 0%, #4CAF50 100%)',
          color: 'white',
          border: 'none',
          borderRadius: '25px',
          width: '100px',
          height: '50px',
          fontSize: '13px',
          fontWeight: '500',
          boxShadow: '0 4px 12px rgba(102, 187, 106, 0.4)'
        }
      }
    ]
  }

  return taskFlowData[taskId] || defaultFlow
}

// 流程图数据
const flowNodes = ref<Node[]>([])
const flowEdges = ref<Edge[]>([])

// 生成流程图边数据
const generateFlowEdges = (nodes: Node[]) => {
  const edges: Edge[] = []

  // 根据节点数量和类型生成对应的边
  if (nodes.length >= 2) {
    // 从开始节点到第一层节点
    const startNode = nodes.find(n => n.type === 'input')
    const taskNodes = nodes.filter(n => n.data.type === 'task' || n.data.type === 'collect' || n.data.type === 'template' || n.data.type === 'analyze')

    if (startNode && taskNodes.length > 0) {
      taskNodes.forEach((node, index) => {
        edges.push({
          id: `e-start-${node.id}`,
          source: startNode.id,
          target: node.id,
          animated: true,
          style: {
            stroke: '#667eea',
            strokeWidth: 3,
            strokeDasharray: '5,5'
          },
          markerEnd: {
            type: MarkerType.ArrowClosed,
            color: '#667eea'
          }
        })
      })
    }

    // 从任务节点到审核/验证节点
    const reviewNodes = nodes.filter(n => n.data.type === 'review' || n.data.type === 'verify' || n.data.type === 'generate' || n.data.type === 'report')
    if (taskNodes.length > 0 && reviewNodes.length > 0) {
      taskNodes.forEach((taskNode) => {
        reviewNodes.forEach((reviewNode) => {
          edges.push({
            id: `e-${taskNode.id}-${reviewNode.id}`,
            source: taskNode.id,
            target: reviewNode.id,
            animated: true,
            style: {
              stroke: '#FFA726',
              strokeWidth: 3
            },
            markerEnd: {
              type: MarkerType.ArrowClosed,
              color: '#FFA726'
            }
          })
        })
      })
    }

    // 从审核节点到结束节点
    const endNode = nodes.find(n => n.type === 'output')
    if (reviewNodes.length > 0 && endNode) {
      reviewNodes.forEach((reviewNode) => {
        edges.push({
          id: `e-${reviewNode.id}-end`,
          source: reviewNode.id,
          target: endNode.id,
          animated: true,
          style: {
            stroke: '#42A5F5',
            strokeWidth: 3
          },
          markerEnd: {
            type: MarkerType.ArrowClosed,
            color: '#42A5F5'
          }
        })
      })
    }
  }

  return edges
}


// Vue Flow 实例引用
const vueFlowRef = ref(null)

// 审核历史记录
const auditHistory = ref([])

// 生成任务相关的演示审核数据
const generateDemoAuditData = (taskId: string) => {
  const demoData = {
    '1': [
      {
        id: 'demo-1-1',
        taskId: '1',
        result: 'pass',
        opinion: '经过审核，数据收集任务的子任务关系设计合理，流程清晰，符合业务需求。',
        auditor: '张三',
        auditTime: '2024-01-15 14:30:00',
        timestamp: Date.now() - 86400000 * 2
      }
    ],
    '2': [
      {
        id: 'demo-2-1',
        taskId: '2',
        result: 'return',
        opinion: '子任务关系存在循环依赖问题，需要重新梳理任务流程，建议优化后重新提交。',
        auditor: '李四',
        auditTime: '2024-01-16 09:15:00',
        timestamp: Date.now() - 86400000 * 1
      }
    ],
    '3': [
      {
        id: 'demo-3-1',
        taskId: '3',
        result: 'reject',
        opinion: '任务分解不够细化，部分子任务职责不清晰，不符合项目管理规范要求。',
        auditor: '王五',
        auditTime: '2024-01-17 16:45:00',
        timestamp: Date.now() - 86400000 * 0.5
      }
    ]
  }

  return demoData[taskId] || []
}

// 加载审核历史记录
const loadAuditHistory = () => {
  try {
    const records = JSON.parse(localStorage.getItem('subtask-relation-audit-records') || '[]')
    const userRecords = records.filter(record => record.taskId === taskId.value)

    // 如果没有用户记录，使用演示数据
    if (userRecords.length === 0) {
      auditHistory.value = generateDemoAuditData(taskId.value)
    } else {
      // 合并用户记录和演示数据，用户记录优先
      const demoRecords = generateDemoAuditData(taskId.value)
      auditHistory.value = [...userRecords, ...demoRecords]
        .sort((a, b) => b.timestamp - a.timestamp) // 按时间倒序排列
    }
  } catch (error) {
    console.error('加载审核历史失败:', error)
    auditHistory.value = generateDemoAuditData(taskId.value)
  }
}

// 页面初始化
onMounted(async () => {
  try {
    // 确保Store数据已初始化
    taskStore.initializeData()

    // 验证任务ID是否有效
    if (!taskId.value) {
      throw new Error('任务ID无效')
    }

    // 生成对应任务的流程图数据
    const flowData = generateFlowData(taskId.value)
    flowNodes.value = flowData.nodes
    flowEdges.value = generateFlowEdges(flowData.nodes)

    // 加载审核历史记录
    loadAuditHistory()

    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 检查任务是否存在
    if (!currentTask.value) {
      throw new Error('未找到对应的任务信息')
    }

    error.value = ''
  } catch (err) {
    console.error('页面初始化失败:', err)
    error.value = err instanceof Error ? err.message : '页面加载失败'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
  }
})

// 返回上一页
const handleBack = () => {
  const currentQuery = route.query
  router.push({
    path: `/taskObjectiveDecomposition/detail/${taskId.value}`,
    query: currentQuery
  })
}

// 刷新页面
const handleRefresh = async () => {
  try {
    refreshLoading.value = true

    // 重新加载审核历史记录
    loadAuditHistory()

    // 重新初始化表单时间
    auditForm.auditTime = new Date().toLocaleString()

    // 模拟刷新延迟
    await new Promise(resolve => setTimeout(resolve, 1500))

    ElMessage.success('页面刷新成功')
    error.value = ''
  } catch (err) {
    console.error('页面刷新失败:', err)
    ElMessage.error('页面刷新失败，请重试')
  } finally {
    refreshLoading.value = false
  }
}

// 提交审核
const handleSubmitAudit = async () => {
  if (!auditForm.result) {
    ElMessage.warning('请选择审核结果')
    return
  }
  
  if (!auditForm.opinion.trim()) {
    ElMessage.warning('请填写审核意见')
    return
  }
  
  if (auditForm.opinion.length < 5 || auditForm.opinion.length > 500) {
    ElMessage.warning('审核意见长度应在5-500字符之间')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要${auditResultOptions.find(opt => opt.value === auditForm.result)?.label}该子任务关系吗？`,
      '审核确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 模拟提交延迟
    submitLoading.value = true
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 保存审核记录到本地存储
    const auditRecord = {
      id: Date.now().toString(),
      taskId: taskId.value,
      result: auditForm.result,
      opinion: auditForm.opinion,
      auditor: auditForm.auditor,
      auditTime: auditForm.auditTime,
      timestamp: Date.now()
    }

    try {
      const existingRecords = JSON.parse(localStorage.getItem('subtask-relation-audit-records') || '[]')
      existingRecords.unshift(auditRecord)
      localStorage.setItem('subtask-relation-audit-records', JSON.stringify(existingRecords))

      // 更新审核历史显示
      loadAuditHistory()

      ElMessage.success(`子任务关系${auditResultOptions.find(opt => opt.value === auditForm.result)?.label}成功`)

      // 延迟返回上一页
      setTimeout(() => {
        handleBack()
      }, 1500)
    } catch (storageError) {
      console.error('保存审核记录失败:', storageError)
      ElMessage.error('保存审核记录失败，但审核操作已完成')
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核操作失败:', error)
      ElMessage.error('审核操作失败，请重试')
    }
  } finally {
    submitLoading.value = false
  }
}

// 重置表单
const handleReset = () => {
  auditForm.result = ''
  auditForm.opinion = ''
  auditForm.auditTime = new Date().toLocaleString()
}

// 页面卸载时清理
onUnmounted(() => {
  // 清理可能的定时器和事件监听器
  loading.value = false
  submitLoading.value = false
  refreshLoading.value = false
})
</script>

<template>
  <div class="subtask-relation-audit-page" v-loading="loading" element-loading-text="加载中...">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button @click="handleBack" class="return-button">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1 class="page-title">子任务关系审核</h1>
      </div>
      <div class="header-right">
        <el-button @click="handleRefresh" class="refresh-button" :loading="refreshLoading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-container">
      <el-alert
        :title="error"
        type="error"
        :closable="false"
        show-icon
      />
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content" v-if="!error">
      <!-- 左侧流程说明 -->
      <div class="left-panel">
        <div class="panel-title">流程说明</div>
        <div class="process-steps">
          <div class="step-item">
            <div class="step-number">1</div>
            <div class="step-content">
              <div class="step-title">任务接收</div>
              <div class="step-desc">接收任务并进行初步分析</div>
            </div>
          </div>
          <div class="step-item">
            <div class="step-number">2</div>
            <div class="step-content">
              <div class="step-title">数据填报</div>
              <div class="step-desc">完成相关数据的填报工作</div>
            </div>
          </div>
          <div class="step-item active">
            <div class="step-number">3</div>
            <div class="step-content">
              <div class="step-title">关系审核</div>
              <div class="step-desc">审核子任务之间的关系</div>
            </div>
          </div>
          <div class="step-item">
            <div class="step-number">4</div>
            <div class="step-content">
              <div class="step-title">任务完成</div>
              <div class="step-desc">完成所有审核流程</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-panel">
        <!-- 任务信息卡片 -->
        <div class="info-card">
          <div class="card-title">任务信息</div>
          <div class="task-info" v-if="currentTask">
            <div class="info-row">
              <span class="label">任务名称：</span>
              <span class="value">{{ currentTask.taskName }}</span>
            </div>
            <div class="info-row">
              <span class="label">任务类型：</span>
              <span class="value">{{ currentTask.taskType }}</span>
            </div>
            <div class="info-row">
              <span class="label">创建部门：</span>
              <span class="value">{{ currentTask.createdDepartment }}</span>
            </div>
            <div class="info-row">
              <span class="label">责任人：</span>
              <span class="value">{{ currentTask.responsiblePerson }}</span>
            </div>
            <div class="info-row">
              <span class="label">开始时间：</span>
              <span class="value">{{ currentTask.startTime }}</span>
            </div>
            <div class="info-row">
              <span class="label">业务报表子任务：</span>
              <span class="value">{{ currentTask.businessSubTaskCount || 0 }} 个</span>
            </div>
            <div class="info-row">
              <span class="label">临时报表子任务：</span>
              <span class="value">{{ currentTask.temporarySubTaskCount || 0 }} 个</span>
            </div>
            <div class="info-row">
              <span class="label">当前状态：</span>
              <el-tag :type="currentTask.taskStatus === '执行中' ? 'success' : 'info'">
                {{ currentTask.taskStatus }}
              </el-tag>
            </div>
            <div class="info-row">
              <span class="label">任务进度：</span>
              <el-progress
                :percentage="currentTask.progress || 0"
                :stroke-width="6"
                :show-text="true"
              />
            </div>
          </div>
        </div>

        <!-- 流程图展示区域 -->
        <div class="flowchart-container">
          <div class="card-title">子任务关系图</div>
          <div class="flowchart-content">
            <VueFlow
              ref="vueFlowRef"
              :nodes="flowNodes"
              :edges="flowEdges"
              :fit-view-on-init="true"
              :nodes-draggable="false"
              :zoom-on-scroll="true"
              :pan-on-scroll="true"
              :zoom-on-pinch="true"
              :pan-on-drag="true"
              :default-zoom="1.0"
              :min-zoom="0.5"
              :max-zoom="2"
              class="vue-flow-container"
            >
              <template #node-default="{ data, id }">
                <div
                  class="custom-node"
                  :class="[data.type]"
                >
                  <div v-if="data.title" class="node-title">
                    {{ data.title }}
                  </div>
                  <div class="node-content">
                    {{ data.content || data.label }}
                  </div>
                </div>
              </template>
            </VueFlow>
          </div>
        </div>

        <!-- 审核操作区域 -->
        <div class="audit-form-card">
          <div class="card-title">审核操作</div>
          <el-form :model="auditForm" label-width="100px" class="audit-form">
            <el-form-item label="审核结果" required>
              <el-radio-group v-model="auditForm.result">
                <el-radio 
                  v-for="option in auditResultOptions" 
                  :key="option.value" 
                  :value="option.value"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="审核意见" required>
              <el-input
                v-model="auditForm.opinion"
                type="textarea"
                :rows="4"
                placeholder="请输入审核意见（5-500字符）"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            
            <el-form-item label="审核人">
              <el-input v-model="auditForm.auditor" disabled />
            </el-form-item>
            
            <el-form-item label="审核时间">
              <el-input v-model="auditForm.auditTime" disabled />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="handleSubmitAudit" :loading="submitLoading">
                提交审核
              </el-button>
              <el-button @click="handleReset" :disabled="submitLoading">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 审核历史记录 -->
        <div class="audit-history-card" v-if="auditHistory.length > 0">
          <div class="card-title">审核历史</div>
          <div class="history-list">
            <div
              v-for="record in auditHistory"
              :key="record.id"
              class="history-item"
            >
              <div class="history-header">
                <span class="auditor">{{ record.auditor }}</span>
                <span class="audit-time">{{ record.auditTime }}</span>
              </div>
              <div class="history-content">
                <el-tag
                  :type="record.result === 'pass' ? 'success' : record.result === 'reject' ? 'danger' : 'warning'"
                  size="small"
                >
                  {{ auditResultOptions.find(opt => opt.value === record.result)?.label }}
                </el-tag>
                <span class="opinion">{{ record.opinion }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
/* 导入 Vue Flow 必要样式 */
@import '@vue-flow/core/dist/style.css';
@import '@vue-flow/core/dist/theme-default.css';

.subtask-relation-audit-page {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;

  .error-container {
    margin-bottom: 20px;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .return-button {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .page-title {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #303133;
      }
    }

    .header-right {
      .refresh-button {
        display: flex;
        align-items: center;
        gap: 4px;
      }
    }
  }

  .main-content {
    display: flex;
    gap: 20px;
    height: calc(100vh - 120px);

    @media (max-width: 1024px) {
      flex-direction: column;
      height: auto;
    }

    .left-panel {
      width: 300px;
      background: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);

      .panel-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #409eff;
      }

      .process-steps {
        .step-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 24px;
          position: relative;

          &:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 15px;
            top: 32px;
            width: 2px;
            height: 24px;
            background: #e4e7ed;
          }

          &.active {
            .step-number {
              background: #409eff;
              color: white;
            }
            
            .step-title {
              color: #409eff;
              font-weight: 600;
            }

            &::after {
              background: #409eff;
            }
          }

          .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e4e7ed;
            color: #909399;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 12px;
            flex-shrink: 0;
          }

          .step-content {
            flex: 1;
            padding-top: 2px;

            .step-title {
              font-size: 14px;
              font-weight: 500;
              color: #303133;
              margin-bottom: 4px;
            }

            .step-desc {
              font-size: 12px;
              color: #909399;
              line-height: 1.4;
            }
          }
        }
      }
    }

    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .info-card,
      .flowchart-container,
      .audit-form-card,
      .audit-history-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);

        .card-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 16px;
          padding-bottom: 8px;
          border-bottom: 1px solid #e4e7ed;
        }
      }

      .info-card {
        .task-info {
          .info-row {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            .label {
              width: 100px;
              color: #606266;
              font-size: 14px;
            }

            .value {
              color: #303133;
              font-size: 14px;
            }
          }
        }
      }

      .flowchart-container {
        flex: 1;
        min-height: 300px;
        max-height: 400px;
        overflow: hidden;

        .flowchart-content {
          height: 300px;
          border: 1px solid #e4e7ed;
          border-radius: 4px;
          position: relative;
          overflow: hidden;

          .vue-flow-container {
            width: 100%;
            height: 100%;
            border-radius: 4px;
          }
        }
      }

      .audit-form-card {
        .audit-form {
          :deep(.el-form-item__label) {
            font-weight: 500;
          }
        }
      }

      .audit-history-card {
        .history-list {
          .history-item {
            padding: 12px;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            margin-bottom: 12px;

            &:last-child {
              margin-bottom: 0;
            }

            .history-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;

              .auditor {
                font-weight: 500;
                color: #303133;
              }

              .audit-time {
                font-size: 12px;
                color: #909399;
              }
            }

            .history-content {
              display: flex;
              align-items: flex-start;
              gap: 8px;

              .opinion {
                flex: 1;
                color: #606266;
                font-size: 14px;
                line-height: 1.4;
              }
            }
          }
        }
      }
    }
  }
}

/* Vue Flow 自定义样式 */
:deep(.vue-flow__node) {
  border-radius: 12px;
  font-size: 12px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  white-space: pre-line;
  line-height: 1.3;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
  }
}

:deep(.vue-flow__edge) {
  stroke-width: 3;
  transition: all 0.3s ease;
}

:deep(.vue-flow__edge.animated) {
  stroke-dasharray: 8 4;
  animation: dashdraw 1s linear infinite;
}

:deep(.vue-flow__edge:hover) {
  stroke-width: 4;
}

:deep(.vue-flow__controls) {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.custom-node {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .node-title {
    font-weight: 600;
    margin-bottom: 4px;
    font-size: 12px;
  }

  .node-content {
    font-size: 11px;
    line-height: 1.2;
    text-align: center;
  }
}

@keyframes dashdraw {
  to {
    stroke-dashoffset: -12;
  }
}
</style>
