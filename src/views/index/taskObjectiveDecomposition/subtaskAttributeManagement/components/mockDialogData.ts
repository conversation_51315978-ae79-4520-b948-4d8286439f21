/**
 * 弹窗组件Mock数据
 * Mock data for dialog components
 */

// 属性值统计数据
export interface AttributeStatistics {
  subtaskType: string
  attributeValue: number
  settingFrequency: number
  isHighFrequency: boolean
}

export const mockAttributeStatistics: AttributeStatistics[] = [
  {
    subtaskType: '临时报表',
    attributeValue: 5,
    settingFrequency: 5,
    isHighFrequency: true
  },
  {
    subtaskType: '临时报表',
    attributeValue: 4,
    settingFrequency: 5,
    isHighFrequency: true
  },
  {
    subtaskType: '业务报表',
    attributeValue: 3,
    settingFrequency: 4,
    isHighFrequency: false
  }
]

// 权限设置数据
export interface PermissionSetting {
  systemRole: string
  attributeType: string
  permissions: {
    create: boolean
    edit: boolean
    delete: boolean
  }
}

export const mockPermissionSettings: PermissionSetting[] = [
  {
    systemRole: '管理员',
    attributeType: '业务报表',
    permissions: {
      create: true,
      edit: true,
      delete: true
    }
  },
  {
    systemRole: '台账运维员',
    attributeType: '临时报表',
    permissions: {
      create: true,
      edit: true,
      delete: true
    }
  },
  {
    systemRole: '区县台账运维员',
    attributeType: '临时报表',
    permissions: {
      create: true,
      edit: true,
      delete: true
    }
  }
]

// 紧急程度规则数据
export interface UrgencyRule {
  subtaskType: string
  urgencyLevel: string
  rule: string
}

export const mockUrgencyRules: UrgencyRule[] = [
  {
    subtaskType: '业务报表',
    urgencyLevel: '特急',
    rule: '1天内'
  },
  {
    subtaskType: '临时报表',
    urgencyLevel: '紧急',
    rule: '5天内'
  },
  {
    subtaskType: '临时报表',
    urgencyLevel: '平急',
    rule: '10天内'
  }
]

// 紧急程度规则录入表单数据
export interface UrgencyRuleForm {
  subtaskType: string
  urgencyLevel: string
  rule: string
}

export const defaultUrgencyRuleForm: UrgencyRuleForm = {
  subtaskType: '',
  urgencyLevel: '',
  rule: ''
}

// 风险等级管理数据
export interface RiskLevelManagement {
  subtaskType: string
  riskLevel: string
  description: string
}

export const mockRiskLevelManagement: RiskLevelManagement[] = [
  {
    subtaskType: '临时报表',
    riskLevel: '低风险',
    description: '能按期按要求完成'
  },
  {
    subtaskType: '临时报表',
    riskLevel: '中风险',
    description: '能按期完成'
  },
  {
    subtaskType: '业务报表',
    riskLevel: '高风险',
    description: '可能无法按期完成'
  }
]

// 历史记录数据
export interface HistoryRecord {
  subtaskAttributeName: string
  changeRecord: string
  operationTime: string
}

export const mockHistoryRecords: HistoryRecord[] = [
  {
    subtaskAttributeName: '系统填报流程',
    changeRecord: '属性值调整为5',
    operationTime: '2025-5-21'
  },
  {
    subtaskAttributeName: '系统填报流程',
    changeRecord: '重要程度调整为重要',
    operationTime: '2025-5-21'
  },
  {
    subtaskAttributeName: '永川区民政局填报流程',
    changeRecord: '属性值调整为5',
    operationTime: '2025-5-21'
  },
  {
    subtaskAttributeName: '永川区民政局填报流程',
    changeRecord: '重要程度调整为不重要',
    operationTime: '2025-5-21'
  },
  {
    subtaskAttributeName: '永川区民政局填报流程',
    changeRecord: '风险等级调整为低风险',
    operationTime: '2025-5-21'
  },
  {
    subtaskAttributeName: '永川区民政局填报流程',
    changeRecord: '属性值调整为5',
    operationTime: '2025-5-21'
  },
  {
    subtaskAttributeName: '永川区民政局填报流程',
    changeRecord: '重要程度调整为重要',
    operationTime: '2025-5-21'
  },
  {
    subtaskAttributeName: '永川区民政局填报流程',
    changeRecord: '风险等级调整为高风险',
    operationTime: '2025-5-21'
  }
]

// 历史记录统计数据
export interface HistoryStatistics {
  attributeValueChanges: number
  importanceLevelChanges: number
  riskLevelChanges: number
}

export const mockHistoryStatistics: HistoryStatistics = {
  attributeValueChanges: 3,
  importanceLevelChanges: 3,
  riskLevelChanges: 2
}

// 选项数据
export const subtaskTypeOptions = ['业务报表', '临时报表']
export const urgencyLevelOptions = ['特急', '紧急', '平急', '不急']
export const riskLevelOptions = ['高风险', '中风险', '低风险']
export const systemRoleOptions = ['管理员', '台账运维员', '区县台账运维员']
export const attributeTypeOptions = ['业务报表', '临时报表']
