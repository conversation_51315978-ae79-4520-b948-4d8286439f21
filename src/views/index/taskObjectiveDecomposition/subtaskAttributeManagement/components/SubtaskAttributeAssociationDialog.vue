<template>
  <DialogComp
    v-model="visible"
    title="子任务属性关联"
    width="700px"
    :loading="loading"
    @clickConfirm="handleConfirm"
    @clickCancel="handleCancel"
  >
    <div class="attribute-association-content" v-loading="loading">
      <!-- 当前属性信息 -->
      <div class="current-attribute-info">
        <h4 class="section-title">当前子任务属性信息</h4>
        <div class="attribute-card">
          <div class="attribute-item">
            <span class="label">子任务属性名称：</span>
            <span class="value">{{ currentAttribute?.attributeName || '-' }}</span>
          </div>
          <div class="attribute-item">
            <span class="label">子任务类型：</span>
            <span class="value">{{ currentAttribute?.taskType || '-' }}</span>
          </div>
          <div class="attribute-item">
            <span class="label">属性类型：</span>
            <span class="value">{{ currentAttribute?.attributeType || '-' }}</span>
          </div>
          <div class="attribute-item">
            <span class="label">属性值：</span>
            <span class="value">{{ currentAttribute?.attributeValue || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 关联子任务选择 -->
      <div class="association-section">
        <h4 class="section-title">关联子任务</h4>
        <div class="association-form">
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="120px"
          >
            <el-form-item label="关联子任务：" prop="associatedTasks">
              <el-select
                v-model="formData.associatedTasks"
                placeholder="请选择子任务，下拉多选"
                multiple
                filterable
                style="width: 100%"
                :loading="taskLoading"
              >
                <el-option
                  v-for="task in availableTasks"
                  :key="task.id"
                  :label="task.taskName"
                  :value="task.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleRestore" :loading="restoreLoading">
                恢复
              </el-button>
              <div class="restore-hint">
                恢复就是取消属性和子任务的关联
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 已关联子任务列表 -->
      <div class="associated-tasks-section" v-if="associatedTasksList.length > 0">
        <h4 class="section-title">已关联子任务列表</h4>
        <div class="associated-tasks-list">
          <div
            v-for="task in associatedTasksList"
            :key="task.id"
            class="task-item"
          >
            <div class="task-info">
              <span class="task-name">{{ task.taskName }}</span>
              <span class="task-type">{{ task.taskType }}</span>
            </div>
            <el-button
              type="text"
              size="small"
              @click="handleRemoveAssociation(task.id)"
              class="remove-btn"
            >
              移除关联
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'
import type { SubtaskAttribute } from '@/define/subtaskAttribute.define'

// 任务接口定义
interface Task {
  id: string
  taskName: string
  taskType: string
}

// 表单数据接口
interface FormData {
  associatedTasks: string[]
}

// Props
const props = defineProps<{
  modelValue: boolean
  currentAttribute: SubtaskAttribute | null
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: FormData]
}>()

// 响应式数据
const loading = ref(false)
const taskLoading = ref(false)
const restoreLoading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const formData = ref<FormData>({
  associatedTasks: []
})

// 表单验证规则
const formRules: FormRules = {
  associatedTasks: [
    { required: true, message: '请选择关联子任务', trigger: 'change' }
  ]
}

// 可选任务列表
const availableTasks = ref<Task[]>([
  { id: 'task_001', taskName: '永川区民政局数据填报', taskType: '业务报表' },
  { id: 'task_002', taskName: '社区网格化管理统计', taskType: '数据采集' },
  { id: 'task_003', taskName: '环保督察整改跟踪', taskType: '质量控制' },
  { id: 'task_004', taskName: '政务服务事项梳理', taskType: '系统维护' },
  { id: 'task_005', taskName: '招商引资项目审批', taskType: '流程审批' },
  { id: 'task_006', taskName: '基层党组织建设评估', taskType: '业务报表' },
  { id: 'task_007', taskName: '城市管理综合执法', taskType: '数据采集' },
  { id: 'task_008', taskName: '应急管理预案更新', taskType: '系统维护' }
])

// 已关联任务列表
const associatedTasksList = ref<Task[]>([])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 事件处理函数
const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 模拟保存操作
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('confirm', { ...formData.value })
    ElMessage.success('子任务属性关联设置成功')
    handleCancel()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  visible.value = false
  resetForm()
}

const handleRestore = async () => {
  try {
    restoreLoading.value = true
    
    // 模拟恢复操作
    await new Promise(resolve => setTimeout(resolve, 800))
    
    formData.value.associatedTasks = []
    associatedTasksList.value = []
    
    ElMessage.success('已恢复，取消了所有属性关联')
  } catch (error) {
    ElMessage.error('恢复操作失败')
  } finally {
    restoreLoading.value = false
  }
}

const handleRemoveAssociation = (taskId: string) => {
  // 从已关联列表中移除
  associatedTasksList.value = associatedTasksList.value.filter(task => task.id !== taskId)
  
  // 从表单数据中移除
  formData.value.associatedTasks = formData.value.associatedTasks.filter(id => id !== taskId)
  
  ElMessage.success('已移除关联')
}

// 重置表单
const resetForm = () => {
  formData.value = {
    associatedTasks: []
  }
  associatedTasksList.value = []
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  (visible) => {
    if (visible) {
      resetForm()
      loadAssociatedTasks()
    }
  }
)

// 加载已关联任务
const loadAssociatedTasks = async () => {
  if (!props.currentAttribute) return
  
  try {
    taskLoading.value = true
    
    // 模拟加载已关联任务
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟已关联的任务数据
    const mockAssociatedTasks = [
      { id: 'task_001', taskName: '永川区民政局数据填报', taskType: '业务报表' },
      { id: 'task_003', taskName: '环保督察整改跟踪', taskType: '质量控制' }
    ]
    
    associatedTasksList.value = mockAssociatedTasks
    formData.value.associatedTasks = mockAssociatedTasks.map(task => task.id)
  } catch (error) {
    console.error('加载关联任务失败:', error)
    ElMessage.error('加载关联任务失败')
  } finally {
    taskLoading.value = false
  }
}
</script>

<style scoped lang="scss">
.attribute-association-content {
  .section-title {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    border-left: 4px solid #409eff;
    padding-left: 12px;
  }

  .current-attribute-info {
    margin-bottom: 24px;
    
    .attribute-card {
      background: #f8f9fa;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      padding: 16px;
      
      .attribute-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .label {
          font-weight: 500;
          color: #606266;
          min-width: 120px;
          margin-right: 12px;
        }
        
        .value {
          color: #303133;
          font-weight: 500;
        }
      }
    }
  }

  .association-section {
    margin-bottom: 24px;
    
    .restore-hint {
      color: #f56c6c;
      font-size: 12px;
      margin-top: 8px;
      line-height: 1.4;
    }
  }

  .associated-tasks-section {
    .associated-tasks-list {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      
      .task-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        &:hover {
          background-color: #f5f7fa;
        }
        
        .task-info {
          flex: 1;
          
          .task-name {
            font-weight: 500;
            color: #303133;
            margin-right: 12px;
          }
          
          .task-type {
            color: #909399;
            font-size: 12px;
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 3px;
          }
        }
        
        .remove-btn {
          color: #f56c6c;
          
          &:hover {
            color: #f56c6c;
            background-color: #fef0f0;
          }
        }
      }
    }
  }
}
</style>
