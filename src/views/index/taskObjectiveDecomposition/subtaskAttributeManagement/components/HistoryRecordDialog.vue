<template>
  <el-dialog
    v-model="dialogVisible"
    title="子任务属性历史记录"
    width="900px"
    :close-on-click-modal="false"
    destroy-on-close
    draggable
    @close="handleClose"
  >
    <div v-loading="loading" class="history-record-content">
      <!-- 标签页切换 -->
      <el-tabs v-model="activeTab" class="history-tabs">
        <el-tab-pane label="业务报表子任务" name="business">
          <div class="tab-content">

            
            <!-- 统计信息 -->
            <div class="statistics-section">
              <div class="stat-item">
                <span class="stat-label">属性值修改记录统计:</span>
                <span class="stat-value">{{ historyStatistics.attributeValueChanges }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">重要程度调整统计:</span>
                <span class="stat-value">{{ historyStatistics.importanceLevelChanges }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">风险等级调整统计:</span>
                <span class="stat-value">{{ historyStatistics.riskLevelChanges }}</span>
              </div>
            </div>

            <!-- 历史记录表格 -->
            <el-table
              :data="historyRecords"
              border
              style="width: 100%"
              class="history-table"
            >
              <el-table-column
                prop="subtaskAttributeName"
                label="子任务属性名称"
                width="200"
                align="center"
              />
              <el-table-column
                prop="changeRecord"
                label="调整记录"
                align="center"
              />
              <el-table-column
                prop="operationTime"
                label="操作时间"
                width="150"
                align="center"
              />
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="临时报表子任务" name="temporary">
          <div class="tab-content">
            
            <!-- 统计信息 -->
            <div class="statistics-section">
              <div class="stat-item">
                <span class="stat-label">属性值修改记录统计:</span>
                <span class="stat-value">{{ historyStatistics.attributeValueChanges }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">重要程度调整统计:</span>
                <span class="stat-value">{{ historyStatistics.importanceLevelChanges }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">风险等级调整统计:</span>
                <span class="stat-value">{{ historyStatistics.riskLevelChanges }}</span>
              </div>
            </div>

            <!-- 历史记录表格 -->
            <el-table
              :data="historyRecords"
              border
              style="width: 100%"
              class="history-table"
            >
              <el-table-column
                prop="subtaskAttributeName"
                label="子任务属性名称"
                width="200"
                align="center"
              />
              <el-table-column
                prop="changeRecord"
                label="调整记录"
                align="center"
              />
              <el-table-column
                prop="operationTime"
                label="操作时间"
                width="150"
                align="center"
              />
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="confirmLoading">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  mockHistoryRecords,
  mockHistoryStatistics,
  type HistoryRecord,
  type HistoryStatistics
} from './mockDialogData'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: any): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const confirmLoading = ref(false)
const activeTab = ref('business')

// 分别存储两个标签页的数据
const businessData = ref({
  records: [] as HistoryRecord[],
  statistics: {
    attributeValueChanges: 0,
    importanceLevelChanges: 0,
    riskLevelChanges: 0
  } as HistoryStatistics
})

const temporaryData = ref({
  records: [] as HistoryRecord[],
  statistics: {
    attributeValueChanges: 0,
    importanceLevelChanges: 0,
    riskLevelChanges: 0
  } as HistoryStatistics
})

// 计算当前显示的数据
const historyRecords = computed(() => {
  return activeTab.value === 'business' ? businessData.value.records : temporaryData.value.records
})

const historyStatistics = computed(() => {
  return activeTab.value === 'business' ? businessData.value.statistics : temporaryData.value.statistics
})

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 本地存储键名
const STORAGE_KEY = 'subtask-history-records'

// 监听弹窗打开，加载数据
watch(dialogVisible, (newVal) => {
  if (newVal) {
    loadHistoryData()
  }
})

// 监听标签页切换，加载对应数据
watch(activeTab, (newTab) => {
  if (dialogVisible.value) {
    loadTabData(newTab)
  }
})

// 从本地存储加载数据
const loadFromStorage = () => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    if (stored) {
      const data = JSON.parse(stored)
      businessData.value = data.business || businessData.value
      temporaryData.value = data.temporary || temporaryData.value
    }
  } catch (error) {
    console.error('从本地存储加载数据失败:', error)
  }
}

// 保存数据到本地存储
const saveToStorage = () => {
  try {
    const data = {
      business: businessData.value,
      temporary: temporaryData.value
    }
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data))
  } catch (error) {
    console.error('保存数据到本地存储失败:', error)
  }
}

// 加载历史记录数据
const loadHistoryData = async () => {
  // 先从本地存储加载
  loadFromStorage()

  // 如果没有数据，则加载模拟数据
  if (businessData.value.records.length === 0) {
    await loadTabData('business')
  }
  if (temporaryData.value.records.length === 0) {
    await loadTabData('temporary')
  }
}

// 加载特定标签页的数据
const loadTabData = async (tabName: string) => {
  loading.value = true
  try {
    // 模拟异步加载延迟
    await new Promise(resolve => setTimeout(resolve, 1500))

    if (tabName === 'business') {
      // 业务报表子任务数据
      businessData.value.records = [...mockHistoryRecords]
      businessData.value.statistics = { ...mockHistoryStatistics }
    } else {
      // 临时报表子任务数据（不同的数据）
      temporaryData.value.records = [
        {
          subtaskAttributeName: '临时数据统计报表',
          changeRecord: '属性值调整为8',
          operationTime: '2025-1-17'
        },
        {
          subtaskAttributeName: '临时数据统计报表',
          changeRecord: '重要程度调整为非常重要',
          operationTime: '2025-1-17'
        },
        {
          subtaskAttributeName: '应急响应流程',
          changeRecord: '风险等级调整为高风险',
          operationTime: '2025-1-16'
        },
        {
          subtaskAttributeName: '应急响应流程',
          changeRecord: '属性值调整为9',
          operationTime: '2025-1-16'
        }
      ]
      temporaryData.value.statistics = {
        attributeValueChanges: 2,
        importanceLevelChanges: 1,
        riskLevelChanges: 1
      }
    }

    // 保存到本地存储
    saveToStorage()

    ElMessage.success(`${tabName === 'business' ? '业务报表' : '临时报表'}历史记录数据加载完成`)
  } catch (error) {
    console.error('加载历史记录数据失败:', error)
    ElMessage.error('加载历史记录数据失败')
  } finally {
    loading.value = false
  }
}

// 确认操作
const handleConfirm = async () => {
  confirmLoading.value = true
  try {
    // 模拟确认操作延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 重置当前标签页的数据（模拟确认后清空）
    if (activeTab.value === 'business') {
      businessData.value.statistics = {
        attributeValueChanges: 0,
        importanceLevelChanges: 0,
        riskLevelChanges: 0
      }
      businessData.value.records = []
    } else {
      temporaryData.value.statistics = {
        attributeValueChanges: 0,
        importanceLevelChanges: 0,
        riskLevelChanges: 0
      }
      temporaryData.value.records = []
    }

    // 保存到本地存储
    saveToStorage()

    emit('confirm', {
      activeTab: activeTab.value,
      businessData: businessData.value,
      temporaryData: temporaryData.value,
      timestamp: new Date().toISOString()
    })

    ElMessage.success('历史记录确认成功')
    handleClose()
  } catch (error) {
    console.error('确认操作失败:', error)
    ElMessage.error('确认操作失败')
  } finally {
    confirmLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
  handleClose()
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  // 重置数据
  activeTab.value = 'business'
  historyRecords.value = []
  historyStatistics.value = {
    attributeValueChanges: 0,
    importanceLevelChanges: 0,
    riskLevelChanges: 0
  }
  loading.value = false
  confirmLoading.value = false
}
</script>

<style scoped lang="scss">
.history-record-content {
  min-height: 500px;
  
  .history-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 20px;
      
      .el-tabs__nav-wrap {
        &::after {
          background-color: #e4e7ed;
        }
      }
      
      .el-tabs__item {
        font-size: 14px;
        font-weight: 500;
        
        &.is-active {
          color: #409eff;
        }
      }
      
      .el-tabs__active-bar {
        background-color: #409eff;
      }
    }
    
    .tab-content {
      .tab-description {
        color: #f56c6c;
        font-size: 14px;
        margin-bottom: 20px;
        padding: 12px;
        background-color: #fef0f0;
        border: 1px solid #fbc4c4;
        border-radius: 4px;
      }
      
      .statistics-section {
        margin-bottom: 20px;
        padding: 16px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #e4e7ed;
        
        .stat-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .stat-label {
            font-weight: 500;
            color: #303133;
            margin-right: 12px;
          }
          
          .stat-value {
            color: #409eff;
            font-weight: 600;
            font-size: 16px;
          }
        }
      }
      
      .history-table {
        :deep(.el-table__header) {
          background-color: #f5f7fa;
          
          th {
            background-color: #f5f7fa !important;
            color: #303133;
            font-weight: 600;
          }
        }
        
        :deep(.el-table__body) {
          tr:hover > td {
            background-color: #f5f7fa;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 弹窗样式覆盖
:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #e4e7ed;
    background-color: #fafafa;
  }
}
</style>
