<template>
  <DialogComp
    v-model="visible"
    title="搜索记录"
    width="600px"
    :visible-footer-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="search-record-content" v-loading="loading">
      <!-- 操作按钮区域 -->
      <div class="action-buttons">
        <el-button type="danger" @click="handleClearAll" :disabled="searchRecords.length === 0">
          清空所有记录
        </el-button>
      </div>

      <!-- 搜索记录列表 -->
      <div class="record-list" v-if="searchRecords.length > 0">
        <div
          v-for="(record, index) in searchRecords"
          :key="index"
          class="record-item"
          @click="handleApplyRecord(record)"
        >
          <div class="record-content">
            <div class="record-conditions">
              <span v-if="record.keyword" class="condition-tag">
                关键词: {{ record.keyword }}
              </span>
              <span v-if="record.taskType" class="condition-tag">
                任务类型: {{ record.taskType }}
              </span>
              <span v-if="record.attributeType" class="condition-tag">
                属性类型: {{ record.attributeType }}
              </span>
            </div>
            <div class="record-meta">
              <span class="search-time">{{ record.searchTime }}</span>
              <span class="result-count">结果: {{ record.resultCount }} 条</span>
            </div>
          </div>
          <div class="record-actions">
            <el-button
              type="text"
              size="small"
              @click.stop="handleDeleteRecord(index)"
              class="delete-btn"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <el-empty description="暂无搜索记录" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'

// 搜索记录接口定义
interface SearchRecord {
  keyword: string
  taskType: string
  attributeType: string
  searchTime: string
  resultCount: number
}

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'apply-record': [record: SearchRecord]
}>()

// 响应式数据
const loading = ref(false)
const searchRecords = ref<SearchRecord[]>([])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 搜索记录存储键
const SEARCH_RECORD_KEY = 'subtask_attribute_search_records'

// 加载搜索记录
const loadSearchRecords = () => {
  try {
    const records = localStorage.getItem(SEARCH_RECORD_KEY)
    if (records) {
      searchRecords.value = JSON.parse(records)
    }
  } catch (error) {
    console.error('加载搜索记录失败:', error)
    searchRecords.value = []
  }
}

// 保存搜索记录
const saveSearchRecords = () => {
  try {
    localStorage.setItem(SEARCH_RECORD_KEY, JSON.stringify(searchRecords.value))
  } catch (error) {
    console.error('保存搜索记录失败:', error)
  }
}

// 应用搜索记录
const handleApplyRecord = (record: SearchRecord) => {
  emit('apply-record', record)
  handleClose()
}

// 删除单条记录
const handleDeleteRecord = async (index: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这条搜索记录吗？', '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    searchRecords.value.splice(index, 1)
    saveSearchRecords()
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 清空所有记录
const handleClearAll = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有搜索记录吗？', '清空确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    searchRecords.value = []
    saveSearchRecords()
    ElMessage.success('清空成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清空失败')
    }
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 添加搜索记录的方法（供外部调用）
const addSearchRecord = (record: SearchRecord) => {
  // 检查是否已存在相同的搜索条件
  const existingIndex = searchRecords.value.findIndex(
    item => 
      item.keyword === record.keyword &&
      item.taskType === record.taskType &&
      item.attributeType === record.attributeType
  )

  if (existingIndex !== -1) {
    // 更新现有记录
    searchRecords.value[existingIndex] = record
  } else {
    // 添加新记录到开头
    searchRecords.value.unshift(record)
    
    // 限制记录数量（最多保存20条）
    if (searchRecords.value.length > 20) {
      searchRecords.value = searchRecords.value.slice(0, 20)
    }
  }

  saveSearchRecords()
}

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  (visible) => {
    if (visible) {
      loadSearchRecords()
    }
  }
)

// 暴露方法给父组件
defineExpose({
  addSearchRecord
})
</script>

<style scoped lang="scss">
.search-record-content {
  .action-buttons {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e4e7ed;
  }

  .record-list {
    max-height: 400px;
    overflow-y: auto;

    .record-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      margin-bottom: 8px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background-color: #f5f7fa;
        border-color: #409eff;
      }

      .record-content {
        flex: 1;

        .record-conditions {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-bottom: 8px;

          .condition-tag {
            background: #ecf5ff;
            color: #409eff;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
          }
        }

        .record-meta {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
          color: #909399;

          .search-time {
            margin-right: 16px;
          }
        }
      }

      .record-actions {
        .delete-btn {
          color: #f56c6c;
          
          &:hover {
            color: #f56c6c;
            background-color: #fef0f0;
          }
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 0;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
