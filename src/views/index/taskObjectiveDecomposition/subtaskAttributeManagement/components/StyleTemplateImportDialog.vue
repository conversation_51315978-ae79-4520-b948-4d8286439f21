<template>
  <DialogComp
    v-model="visible"
    title="可视化模板导入"
    width="600px"
    :visible-footer-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="style-template-import-content">
      <!-- 文件上传区域 -->
      <div class="upload-area">
        <el-upload
          ref="uploadRef"
          class="upload-dragger"
          drag
          :auto-upload="false"
          :show-file-list="true"
          accept=".json,.xlsx,.xls"
          :limit="1"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :on-exceed="handleExceed"
        >
          <div class="upload-content">
            <!-- 上传图标 -->
            <div class="upload-icon">
              <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
                <circle cx="32" cy="32" r="24" fill="#409EFF"/>
                <path d="M32 20v24M20 32h24" stroke="white" stroke-width="3" stroke-linecap="round"/>
                <path d="M26 26l6-6 6 6" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <!-- 上传文字 -->
            <div class="upload-text">
              点击或将文件拖拽到这里上传
            </div>
          </div>
        </el-upload>
      </div>

      <!-- 文件格式说明 -->
      <div class="file-format-info">
        <el-alert
          title="支持的文件格式"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <div class="format-list">
              <p>• JSON格式文件 (.json)</p>
              <p>• Excel文件 (.xlsx, .xls)</p>
              <p>• 文件大小不超过 10MB</p>
            </div>
          </template>
        </el-alert>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleUpload"
          :disabled="!selectedFile"
          :loading="uploading"
        >
          上传
        </el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, type UploadInstance, type UploadFile, type UploadFiles } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirm: [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const uploadRef = ref<UploadInstance>()
const selectedFile = ref<UploadFile | null>(null)
const uploading = ref(false)

// 事件处理
const handleClose = () => {
  visible.value = false
  resetUpload()
}

const handleCancel = () => {
  visible.value = false
  resetUpload()
}

const handleFileChange = (file: UploadFile, fileList: UploadFiles) => {
  // 验证文件类型
  const allowedTypes = ['.json', '.xlsx', '.xls']
  const fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase()
  
  if (!allowedTypes.includes(fileExtension)) {
    ElMessage.error('不支持的文件格式，请上传 JSON 或 Excel 文件')
    uploadRef.value?.clearFiles()
    selectedFile.value = null
    return
  }

  // 验证文件大小 (10MB)
  const maxSize = 10 * 1024 * 1024
  if (file.size && file.size > maxSize) {
    ElMessage.error('文件大小不能超过 10MB')
    uploadRef.value?.clearFiles()
    selectedFile.value = null
    return
  }

  selectedFile.value = file
  ElMessage.success('文件选择成功')
}

const handleFileRemove = () => {
  selectedFile.value = null
}

const handleExceed = () => {
  ElMessage.warning('只能上传一个文件')
}

const handleUpload = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择文件')
    return
  }

  uploading.value = true

  try {
    // 模拟上传过程
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 模拟解析文件内容
    const mockData = {
      fileName: selectedFile.value.name,
      fileSize: selectedFile.value.size,
      uploadTime: new Date().toISOString(),
      templates: [
        {
          taskType: '临时报表',
          templateName: '导入样式1',
          lowRiskColor: '#409EFF',
          mediumRiskColor: '#E6A23C',
          highRiskColor: '#F56C6C'
        }
      ]
    }

    emit('confirm', mockData)
    visible.value = false
    resetUpload()
    
    ElMessage.success('文件上传成功')
  } catch (error) {
    ElMessage.error('文件上传失败，请重试')
    console.error('上传错误:', error)
  } finally {
    uploading.value = false
  }
}

const resetUpload = () => {
  selectedFile.value = null
  uploading.value = false
  uploadRef.value?.clearFiles()
}
</script>

<style lang="scss" scoped>
.style-template-import-content {
  padding: 20px;

  .upload-area {
    margin-bottom: 20px;

    .upload-dragger {
      :deep(.el-upload-dragger) {
        width: 100%;
        height: 200px;
        border: 2px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: border-color 0.3s;

        &:hover {
          border-color: #409EFF;
        }
      }

      .upload-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: 20px;

        .upload-icon {
          margin-bottom: 16px;
        }

        .upload-text {
          font-size: 16px;
          color: #606266;
          text-align: center;
        }
      }
    }
  }

  .file-format-info {
    .format-list {
      margin: 0;
      
      p {
        margin: 4px 0;
        color: #606266;
        font-size: 14px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 10px 0;
}
</style>
