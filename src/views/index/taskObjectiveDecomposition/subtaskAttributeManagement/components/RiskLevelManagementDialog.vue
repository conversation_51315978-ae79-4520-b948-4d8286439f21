<template>
  <el-dialog
    v-model="dialogVisible"
    title="属性风险等级分类管理"
    width="800px"
    :close-on-click-modal="false"
    destroy-on-close
    draggable
    @close="handleClose"
  >
    <div v-loading="loading" class="risk-level-content">
      <!-- 风险等级管理表格 -->
      <el-table
        :data="riskLevelData"
        border
        style="width: 100%"
        class="risk-level-table"
      >
        <el-table-column
          prop="subtaskType"
          label="子任务类型"
          width="200"
          align="center"
        />
        <el-table-column
          prop="riskLevel"
          label="风险等级"
          width="150"
          align="center"
        >
          <template #default="{ row }">
            <el-tag :type="getRiskTagType(row.riskLevel)">
              {{ row.riskLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="description"
          label="说明"
          align="center"
        />
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="confirmLoading">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  mockRiskLevelManagement,
  type RiskLevelManagement
} from './mockDialogData'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: any): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const confirmLoading = ref(false)
const riskLevelData = ref<RiskLevelManagement[]>([])

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听弹窗打开，加载数据
watch(dialogVisible, (newVal) => {
  if (newVal) {
    loadRiskLevelData()
  }
})

// 获取风险等级标签类型
const getRiskTagType = (riskLevel: string) => {
  const typeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    '高风险': 'danger',
    '中风险': 'warning',
    '低风险': 'success'
  }
  return typeMap[riskLevel] || 'info'
}

// 加载风险等级数据
const loadRiskLevelData = async () => {
  loading.value = true
  try {
    // 模拟异步加载延迟
    await new Promise(resolve => setTimeout(resolve, 1200))
    
    riskLevelData.value = [...mockRiskLevelManagement]
    
    ElMessage.success('风险等级数据加载完成')
  } catch (error) {
    console.error('加载风险等级数据失败:', error)
    ElMessage.error('加载风险等级数据失败')
  } finally {
    loading.value = false
  }
}

// 确认操作
const handleConfirm = async () => {
  confirmLoading.value = true
  try {
    // 模拟确认操作延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('confirm', {
      riskLevelData: riskLevelData.value,
      timestamp: new Date().toISOString()
    })
    
    ElMessage.success('风险等级管理确认成功')
    handleClose()
  } catch (error) {
    console.error('确认操作失败:', error)
    ElMessage.error('确认操作失败')
  } finally {
    confirmLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
  handleClose()
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  // 重置数据
  riskLevelData.value = []
  loading.value = false
  confirmLoading.value = false
}
</script>

<style scoped lang="scss">
.risk-level-content {
  min-height: 300px;
  
  .risk-level-table {
    :deep(.el-table__header) {
      background-color: #f5f7fa;
      
      th {
        background-color: #f5f7fa !important;
        color: #303133;
        font-weight: 600;
      }
    }
    
    :deep(.el-table__body) {
      tr:hover > td {
        background-color: #f5f7fa;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 弹窗样式覆盖
:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #e4e7ed;
    background-color: #fafafa;
  }
}
</style>
