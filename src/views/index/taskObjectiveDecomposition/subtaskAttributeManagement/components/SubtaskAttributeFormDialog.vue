<template>
  <DialogComp
    v-model="dialogVisible"
    :title="title"
    width="600px"
    :loading="loading"
    @clickConfirm="handleConfirm"
    @clickCancel="handleCancel"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="180px"
      :class="formClass"
    >
      <!-- 子任务属性名称设定 -->
      <el-form-item label="子任务属性名称设定" prop="attributeName" required>
        <el-input
          v-model="formData.attributeName"
          placeholder="请输入"
          maxlength="50"
          show-word-limit
          clearable
        />
      </el-form-item>

      <!-- 子任务类型 -->
      <el-form-item label="子任务类型" prop="taskType" required>
        <el-select
          v-model="formData.taskType"
          placeholder="请选择，业务报表、临时报表"
          style="width: 100%"
        >
          <el-option label="业务报表" value="业务报表" />
          <el-option label="临时报表" value="临时报表" />
          <el-option label="数据采集" value="数据采集" />
          <el-option label="流程审批" value="流程审批" />
          <el-option label="系统维护" value="系统维护" />
        </el-select>
      </el-form-item>

      <!-- 子任务属性类型 -->
      <el-form-item label="子任务属性类型" prop="attributeType" required>
        <el-select
          v-model="formData.attributeType"
          placeholder="请选择，选项需要为创建类型保存的内容,支持搜索"
          filterable
          style="width: 100%"
        >
          <el-option
            v-for="type in attributeTypes"
            :key="type"
            :label="type"
            :value="type"
          />
        </el-select>
      </el-form-item>

      <!-- 子任务属性值 -->
      <el-form-item label="子任务属性值" prop="attributeValue">
        <div class="attribute-value-section">
          <div class="value-hint">子任务属性值可输入1-10，数值越大表示重要程度越高</div>
          <div class="value-input-group">
            <el-input
              v-model="formData.attributeValue"
              placeholder="请输入"
              style="flex: 1"
            />
            <el-button type="primary" @click="handleCopyValue">属性值复制</el-button>
            <el-button type="success" @click="handleGenerateValue">生成属性值</el-button>
          </div>
        </div>
      </el-form-item>

      <!-- 子任务属性紧急程度 -->
      <el-form-item label="子任务属性紧急程度" prop="urgencyLevel" required>
        <el-select
          v-model="formData.urgencyLevel"
          placeholder="请选择，特急、紧急、平急"
          style="width: 100%"
        >
          <el-option label="特急" value="特急" />
          <el-option label="紧急" value="紧急" />
          <el-option label="平急" value="平急" />
        </el-select>
      </el-form-item>

      <!-- 子任务属性风险等级设定 -->
      <el-form-item label="子任务属性风险等级设定" prop="riskLevel" required>
        <el-select
          v-model="formData.riskLevel"
          placeholder="请选择，低风险、中风险、高风险"
          style="width: 100%"
        >
          <el-option label="低风险" value="低风险" />
          <el-option label="中风险" value="中风险" />
          <el-option label="高风险" value="高风险" />
        </el-select>
      </el-form-item>

      <!-- 子任务属性重要程度设定 -->
      <el-form-item label="子任务属性重要程度设定" prop="importanceLevel" required>
        <el-select
          v-model="formData.importanceLevel"
          placeholder="请选择，重要、不重要"
          style="width: 100%"
        >
          <el-option label="重要" value="重要" />
          <el-option label="不重要" value="不重要" />
          <el-option label="非常重要" value="非常重要" />
          <el-option label="一般" value="一般" />
        </el-select>
      </el-form-item>

      <!-- 子任务属性风险预警 -->
      <el-form-item label="子任务属性风险预警">
        <div class="risk-warning-section">
          <el-switch
            v-model="formData.riskWarningEnabled"
            active-text="启用"
            inactive-text=""
          />
        </div>
      </el-form-item>

      <!-- 风险预警值 -->
      <el-form-item label="风险预警值" v-if="formData.riskWarningEnabled">
        <div class="warning-value-section">
          <span class="warning-label">超出</span>
          <el-input-number
            v-model="formData.riskWarningValue"
            :min="0"
            :max="100"
            controls-position="right"
            style="width: 120px; margin: 0 8px;"
          />
          <span class="warning-suffix">天未响应触发预警</span>
        </div>
      </el-form-item>

      <!-- 子任务属性提醒频率设置 -->
      <el-form-item label="子任务属性提醒频率设置">
        <el-select
          v-model="formData.reminderFrequency"
          placeholder="请选择提醒频率"
          style="width: 100%"
        >
          <el-option label="每小时" value="每小时" />
          <el-option label="每天" value="每天" />
        </el-select>
      </el-form-item>
    </el-form>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'
import type { SubtaskAttribute } from '@/define/subtaskAttribute.define'

// 定义组件属性
interface Props {
  modelValue: boolean
  title: string
  loading?: boolean
  attributeTypes: string[]
  mode: 'add' | 'edit'
  initialData?: SubtaskAttributeFormData
}

// 定义表单数据类型
interface SubtaskAttributeFormData {
  attributeName: string
  taskType: string
  attributeType: string
  attributeValue: string
  urgencyLevel: string
  riskLevel: string
  importanceLevel: string
  riskWarningEnabled: boolean
  riskWarningValue: number
  reminderFrequency: string
}

// 定义事件
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: SubtaskAttributeFormData): void
  (e: 'cancel'): void
  (e: 'copyValue'): void
  (e: 'generateValue'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  initialData: () => ({
    attributeName: '',
    taskType: '',
    attributeType: '',
    attributeValue: '',
    urgencyLevel: '',
    riskLevel: '',
    importanceLevel: '',
    riskWarningEnabled: false,
    riskWarningValue: 0,
    reminderFrequency: ''
  })
})

const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()
const formData = ref<SubtaskAttributeFormData>({ ...props.initialData })

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formClass = computed(() => {
  return props.mode === 'add' ? 'add-form' : 'adjust-form'
})

// 表单验证规则
const formRules: FormRules = {
  attributeName: [
    { required: true, message: '请输入子任务属性名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  taskType: [
    { required: true, message: '请选择子任务类型', trigger: 'change' }
  ],
  attributeType: [
    { required: true, message: '请选择子任务属性类型', trigger: 'change' }
  ],
  attributeValue: [
    { pattern: /^([1-9]|10)$/, message: '属性值必须是1-10之间的整数', trigger: 'blur' }
  ],
  urgencyLevel: [
    { required: true, message: '请选择紧急程度', trigger: 'change' }
  ],
  riskLevel: [
    { required: true, message: '请选择风险等级', trigger: 'change' }
  ],
  importanceLevel: [
    { required: true, message: '请选择重要程度', trigger: 'change' }
  ]
}

// 监听初始数据变化
watch(
  () => props.initialData,
  (newData) => {
    formData.value = { ...newData }
  },
  { deep: true }
)

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  (visible) => {
    if (visible) {
      // 弹窗打开时重置表单数据
      formData.value = { ...props.initialData }
      nextTick(() => {
        formRef.value?.clearValidate()
      })
    }
  }
)

// 事件处理函数
const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    emit('confirm', { ...formData.value })
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCancel = () => {
  emit('cancel')
}

const handleCopyValue = () => {
  emit('copyValue')
}

const handleGenerateValue = () => {
  emit('generateValue')
}

// 暴露方法供父组件调用
const resetForm = () => {
  formRef.value?.resetFields()
  formData.value = { ...props.initialData }
}

const validateForm = () => {
  return formRef.value?.validate()
}

defineExpose({
  resetForm,
  validateForm,
  formData
})
</script>

<style lang="scss" scoped>
.attribute-value-section {
  .value-hint {
    font-size: 12px;
    color: #909399;
    margin-bottom: 8px;
  }
  
  .value-input-group {
    display: flex;
    gap: 8px;
    align-items: center;
  }
}

.risk-warning-section {
  display: flex;
  align-items: center;
}

.warning-value-section {
  display: flex;
  align-items: center;
  
  .warning-label,
  .warning-suffix {
    font-size: 14px;
    color: #606266;
  }
}

:deep(.add-form),
:deep(.adjust-form) {
  .el-form-item__label {
    font-weight: 500;
  }
  
  .el-input,
  .el-select {
    width: 100%;
  }
}
</style>
