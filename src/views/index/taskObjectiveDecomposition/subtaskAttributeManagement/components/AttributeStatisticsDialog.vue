<template>
  <el-dialog
    v-model="dialogVisible"
    title="子任务属性信息统计"
    width="800px"
    :close-on-click-modal="false"
    destroy-on-close
    draggable
    @close="handleClose"
  >
    <div v-loading="loading" class="statistics-content">
      <!-- 统计表格 -->
      <el-table
        :data="statisticsData"
        border
        style="width: 100%"
        class="statistics-table"
      >
        <el-table-column
          prop="subtaskType"
          label="子任务类型"
          width="150"
          align="center"
        />
        <el-table-column
          prop="attributeValue"
          label="属性值"
          width="100"
          align="center"
        />
        <el-table-column
          prop="settingFrequency"
          label="设置频次"
          width="100"
          align="center"
        />
        <el-table-column
          prop="isHighFrequency"
          label="是否高频"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <span>{{ row.isHighFrequency ? '是' : '否' }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 统计汇总信息 -->
      <div class="statistics-summary">
        <div class="summary-item">
          <span class="label">临时报表高频属性值设置频次:</span>
          <span class="value">10</span>
        </div>
        <div class="summary-item">
          <span class="label">业务报表高频属性值设置频次:</span>
          <span class="value">0</span>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleExport" :loading="exportLoading">
          导出
        </el-button>
        <el-button type="primary" @click="handleConfirm" :loading="confirmLoading">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { mockAttributeStatistics, type AttributeStatistics } from './mockDialogData'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: any): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const confirmLoading = ref(false)
const statisticsData = ref<AttributeStatistics[]>([])

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听弹窗打开，加载数据
watch(dialogVisible, (newVal) => {
  if (newVal) {
    loadStatisticsData()
  }
})

// 加载统计数据
const loadStatisticsData = async () => {
  loading.value = true
  try {
    // 模拟异步加载延迟
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    statisticsData.value = [...mockAttributeStatistics]
    
    ElMessage.success('统计数据加载完成')
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  } finally {
    loading.value = false
  }
}

// 导出功能
const handleExport = async () => {
  exportLoading.value = true
  try {
    // 模拟导出延迟
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 这里可以实现真实的导出逻辑
    // 例如：生成Excel文件或CSV文件
    
    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    exportLoading.value = false
  }
}

// 确认操作
const handleConfirm = async () => {
  confirmLoading.value = true
  try {
    // 模拟确认操作延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('confirm', {
      statisticsData: statisticsData.value,
      timestamp: new Date().toISOString()
    })
    
    ElMessage.success('统计确认成功')
    handleClose()
  } catch (error) {
    console.error('确认操作失败:', error)
    ElMessage.error('确认操作失败')
  } finally {
    confirmLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
  handleClose()
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  // 重置数据
  statisticsData.value = []
  loading.value = false
  exportLoading.value = false
  confirmLoading.value = false
}
</script>

<style scoped lang="scss">
.statistics-content {
  min-height: 300px;
  
  .statistics-table {
    margin-bottom: 20px;
    
    :deep(.el-table__header) {
      background-color: #f5f7fa;
      
      th {
        background-color: #f5f7fa !important;
        color: #303133;
        font-weight: 600;
      }
    }
    
    :deep(.el-table__body) {
      tr:hover > td {
        background-color: #f5f7fa;
      }
    }
  }
  
  .statistics-summary {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e4e7ed;
    
    .summary-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        font-weight: 500;
        color: #303133;
        margin-right: 12px;
      }
      
      .value {
        color: #409eff;
        font-weight: 600;
        font-size: 16px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 弹窗样式覆盖
:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #e4e7ed;
    background-color: #fafafa;
  }
}
</style>
