<template>
  <DialogComp
    v-model="visible"
    title="属性可视化展示"
    width="800px"
    :visible-footer-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="attribute-visualization-content">
      <!-- 操作按钮区域 -->
      <div class="action-buttons">
        <span class="section-title">进度条样式列表</span>
        <div class="button-group">
          <el-button type="primary" @click="handleTemplateSettings">
            样式模板设置
          </el-button>
          <el-button type="success" @click="handleTemplateImport">
            样式模板导入
          </el-button>
        </div>
      </div>

      <!-- 样式列表表格 -->
      <div class="style-table">
        <el-table :data="styleData" border style="width: 100%" :header-cell-style="{ background: '#f5f7fa', color: '#606266' }">
          <el-table-column prop="taskType" label="子任务类型" width="150" align="center" />
          <el-table-column prop="templateName" label="进度条样式模板名称" width="200" align="center" />
          <el-table-column label="低风险配色" width="120" align="center">
            <template #default="{ row }">
              <div class="color-dot" :style="{ backgroundColor: row.lowRiskColor }"></div>
            </template>
          </el-table-column>
          <el-table-column label="中风险配色" width="120" align="center">
            <template #default="{ row }">
              <div class="color-dot" :style="{ backgroundColor: row.mediumRiskColor }"></div>
            </template>
          </el-table-column>
          <el-table-column label="高风险配色" width="120" align="center">
            <template #default="{ row }">
              <div class="color-dot" :style="{ backgroundColor: row.highRiskColor }"></div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>

    <!-- 样式模板设置弹窗 -->
    <StyleTemplateSettingsDialog
      v-model="showTemplateSettings"
      @confirm="handleTemplateSettingsConfirm"
    />

    <!-- 样式模板导入弹窗 -->
    <StyleTemplateImportDialog
      v-model="showTemplateImport"
      @confirm="handleTemplateImportConfirm"
    />
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'
import StyleTemplateSettingsDialog from './StyleTemplateSettingsDialog.vue'
import StyleTemplateImportDialog from './StyleTemplateImportDialog.vue'

// 接口定义
interface StyleData {
  id: string
  taskType: string
  templateName: string
  lowRiskColor: string
  mediumRiskColor: string
  highRiskColor: string
}

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirm: [data: StyleData[]]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const showTemplateSettings = ref(false)
const showTemplateImport = ref(false)

// 样式数据（模拟数据）
const styleData = ref<StyleData[]>([
  {
    id: '1',
    taskType: '临时报表',
    templateName: '默认样式',
    lowRiskColor: '#409EFF',
    mediumRiskColor: '#E6A23C',
    highRiskColor: '#F56C6C'
  },
  {
    id: '2',
    taskType: '临时报表',
    templateName: '深色样式',
    lowRiskColor: '#409EFF',
    mediumRiskColor: '#67C23A',
    highRiskColor: '#67C23A'
  },
  {
    id: '3',
    taskType: '业务报表',
    templateName: '浅色样式',
    lowRiskColor: '#87CEEB',
    mediumRiskColor: '#90EE90',
    highRiskColor: '#90EE90'
  }
])

// 事件处理
const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = () => {
  emit('confirm', styleData.value)
  visible.value = false
  ElMessage.success('属性可视化设置已保存')
}

const handleTemplateSettings = () => {
  showTemplateSettings.value = true
}

const handleTemplateImport = () => {
  showTemplateImport.value = true
}

const handleTemplateSettingsConfirm = (data: any) => {
  // 处理样式模板设置确认
  console.log('样式模板设置数据:', data)

  // 生成新的ID
  const newId = (styleData.value.length + 1).toString()

  // 添加新的样式数据到列表
  const newStyleData: StyleData = {
    id: newId,
    taskType: data.taskType,
    templateName: data.templateName,
    lowRiskColor: data.lowRiskColor,
    mediumRiskColor: data.mediumRiskColor,
    highRiskColor: data.highRiskColor
  }

  styleData.value.push(newStyleData)
  ElMessage.success('样式模板设置成功')
}

const handleTemplateImportConfirm = (data: any) => {
  // 处理样式模板导入确认
  console.log('样式模板导入数据:', data)
  ElMessage.success('样式模板导入成功')
}
</script>

<style lang="scss" scoped>
.attribute-visualization-content {
  padding: 20px;

  .action-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    .button-group {
      display: flex;
      gap: 12px;
    }
  }

  .style-table {
    .color-dot {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      margin: 0 auto;
      border: 1px solid #dcdfe6;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 10px 0;
}
</style>
