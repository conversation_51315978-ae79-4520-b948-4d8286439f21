<template>
  <DialogComp
    v-model="visible"
    title="可视化模板设置"
    width="600px"
    :visible-footer-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="style-template-settings-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="140px"
        label-position="right"
      >
        <!-- 子任务类型 -->
        <el-form-item label="子任务类型：" prop="taskType">
          <el-select
            v-model="formData.taskType"
            placeholder="请选择：临时报表、业务报表"
            style="width: 100%"
            clearable
          >
            <el-option label="临时报表" value="临时报表" />
            <el-option label="业务报表" value="业务报表" />
          </el-select>
        </el-form-item>

        <!-- 样式模板名称 -->
        <el-form-item label="样式模板名称：" prop="templateName">
          <el-input
            v-model="formData.templateName"
            placeholder="请输入名称"
            clearable
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <!-- 低风险配色 -->
        <el-form-item label="低风险配色：" prop="lowRiskColor">
          <div class="color-input-group">
            <el-input
              v-model="formData.lowRiskColor"
              placeholder="请输入颜色的十六进制值"
              clearable
            />
            <el-color-picker
              v-model="formData.lowRiskColor"
              show-alpha
              :predefine="predefineColors"
            />
          </div>
        </el-form-item>

        <!-- 中风险配色 -->
        <el-form-item label="中风险配色：" prop="mediumRiskColor">
          <div class="color-input-group">
            <el-input
              v-model="formData.mediumRiskColor"
              placeholder="请输入颜色的十六进制值"
              clearable
            />
            <el-color-picker
              v-model="formData.mediumRiskColor"
              show-alpha
              :predefine="predefineColors"
            />
          </div>
        </el-form-item>

        <!-- 高风险配色 -->
        <el-form-item label="高风险配色：" prop="highRiskColor">
          <div class="color-input-group">
            <el-input
              v-model="formData.highRiskColor"
              placeholder="请输入颜色的十六进制值"
              clearable
            />
            <el-color-picker
              v-model="formData.highRiskColor"
              show-alpha
              :predefine="predefineColors"
            />
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'

// 接口定义
interface StyleTemplateData {
  taskType: string
  templateName: string
  lowRiskColor: string
  mediumRiskColor: string
  highRiskColor: string
}

// Props
const props = defineProps<{
  modelValue: boolean
  templateData?: StyleTemplateData
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirm: [data: StyleTemplateData]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()

// 表单数据
const formData = ref<StyleTemplateData>({
  taskType: '',
  templateName: '',
  lowRiskColor: '#409EFF',
  mediumRiskColor: '#E6A23C',
  highRiskColor: '#F56C6C'
})

// 预定义颜色
const predefineColors = ref([
  '#409EFF',
  '#67C23A',
  '#E6A23C',
  '#F56C6C',
  '#909399',
  '#FF6B6B',
  '#4ECDC4',
  '#45B7D1',
  '#96CEB4',
  '#FFEAA7',
  '#DDA0DD',
  '#98D8C8'
])

// 表单验证规则
const formRules: FormRules = {
  taskType: [
    { required: true, message: '请选择子任务类型', trigger: 'change' }
  ],
  templateName: [
    { required: true, message: '请输入样式模板名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  lowRiskColor: [
    { required: true, message: '请选择低风险配色', trigger: 'change' }
  ],
  mediumRiskColor: [
    { required: true, message: '请选择中风险配色', trigger: 'change' }
  ],
  highRiskColor: [
    { required: true, message: '请选择高风险配色', trigger: 'change' }
  ]
}

// 事件处理
const handleClose = () => {
  visible.value = false
  resetForm()
}

const handleCancel = () => {
  visible.value = false
  resetForm()
}

const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    const templateData: StyleTemplateData = {
      ...formData.value
    }

    emit('confirm', templateData)
    visible.value = false
    resetForm()
    
    ElMessage.success('样式模板设置成功')
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const resetForm = () => {
  formData.value = {
    taskType: '',
    templateName: '',
    lowRiskColor: '#409EFF',
    mediumRiskColor: '#E6A23C',
    highRiskColor: '#F56C6C'
  }
  formRef.value?.clearValidate()
}

// 监听弹窗打开，加载数据
watch(visible, (newVal) => {
  if (newVal && props.templateData) {
    formData.value = { ...props.templateData }
  } else if (newVal) {
    resetForm()
  }
})
</script>

<style lang="scss" scoped>
.style-template-settings-content {
  padding: 20px;

  .color-input-group {
    display: flex;
    align-items: center;
    gap: 12px;

    .el-input {
      flex: 1;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 10px 0;
}
</style>
