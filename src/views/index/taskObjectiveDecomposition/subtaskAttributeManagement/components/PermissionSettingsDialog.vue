<template>
  <el-dialog
    v-model="dialogVisible"
    title="子任务属性权限设置"
    width="900px"
    :close-on-click-modal="false"
    destroy-on-close
    draggable
    @close="handleClose"
  >
    <div v-loading="loading" class="permission-content">
      <!-- 权限设置表格 -->
      <el-table
        :data="permissionData"
        border
        style="width: 100%"
        class="permission-table"
      >
        <el-table-column
          prop="systemRole"
          label="系统角色"
          width="200"
          align="center"
        />
        <el-table-column
          prop="attributeType"
          label="属性类型"
          width="150"
          align="center"
        />
        <el-table-column
          label="属性权限"
          width="300"
          align="center"
        >
          <template #default="{ row }">
            <div class="permission-checkboxes">
              <el-checkbox
                v-model="row.permissions.create"
                @change="handlePermissionChange(row)"
              >
                新增
              </el-checkbox>
              <el-checkbox
                v-model="row.permissions.edit"
                @change="handlePermissionChange(row)"
              >
                编辑
              </el-checkbox>
              <el-checkbox
                v-model="row.permissions.delete"
                @change="handlePermissionChange(row)"
              >
                删除
              </el-checkbox>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="120"
          align="center"
        >
          <template #default="{ row, $index }">
            <el-button
              type="danger"
              size="small"
              @click="handleDeleteRow($index)"
              :disabled="permissionData.length <= 1"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 添加新权限行 -->
      <div class="add-permission-section">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAddPermission"
          :disabled="loading"
        >
          添加权限设置
        </el-button>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  mockPermissionSettings, 
  systemRoleOptions, 
  attributeTypeOptions,
  type PermissionSetting 
} from './mockDialogData'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: any): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const permissionData = ref<PermissionSetting[]>([])

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听弹窗打开，加载数据
watch(dialogVisible, (newVal) => {
  if (newVal) {
    loadPermissionData()
  }
})

// 加载权限数据
const loadPermissionData = async () => {
  loading.value = true
  try {
    // 模拟异步加载延迟
    await new Promise(resolve => setTimeout(resolve, 1200))
    
    // 深拷贝数据以避免直接修改原始数据
    permissionData.value = JSON.parse(JSON.stringify(mockPermissionSettings))
    
    ElMessage.success('权限数据加载完成')
  } catch (error) {
    console.error('加载权限数据失败:', error)
    ElMessage.error('加载权限数据失败')
  } finally {
    loading.value = false
  }
}

// 权限变更处理
const handlePermissionChange = (row: PermissionSetting) => {
  console.log('权限变更:', row)
  // 这里可以添加实时保存逻辑或标记数据已修改
}

// 添加新权限设置
const handleAddPermission = () => {
  const newPermission: PermissionSetting = {
    systemRole: systemRoleOptions[0],
    attributeType: attributeTypeOptions[0],
    permissions: {
      create: false,
      edit: false,
      delete: false
    }
  }
  
  permissionData.value.push(newPermission)
  ElMessage.success('已添加新的权限设置行')
}

// 删除权限行
const handleDeleteRow = async (index: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条权限设置吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    permissionData.value.splice(index, 1)
    ElMessage.success('权限设置已删除')
  } catch {
    // 用户取消删除
  }
}

// 保存权限设置
const handleSave = async () => {
  saveLoading.value = true
  try {
    // 验证数据完整性
    const hasEmptyRole = permissionData.value.some(item => !item.systemRole)
    const hasEmptyType = permissionData.value.some(item => !item.attributeType)
    
    if (hasEmptyRole || hasEmptyType) {
      ElMessage.error('请完善所有权限设置信息')
      return
    }
    
    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    emit('confirm', {
      permissionData: permissionData.value,
      timestamp: new Date().toISOString()
    })
    
    ElMessage.success('权限设置保存成功')
    handleClose()
  } catch (error) {
    console.error('保存权限设置失败:', error)
    ElMessage.error('保存权限设置失败')
  } finally {
    saveLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
  handleClose()
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  // 重置数据
  permissionData.value = []
  loading.value = false
  saveLoading.value = false
}
</script>

<style scoped lang="scss">
.permission-content {
  min-height: 400px;
  
  .permission-table {
    margin-bottom: 20px;
    
    :deep(.el-table__header) {
      background-color: #f5f7fa;
      
      th {
        background-color: #f5f7fa !important;
        color: #303133;
        font-weight: 600;
      }
    }
    
    :deep(.el-table__body) {
      tr:hover > td {
        background-color: #f5f7fa;
      }
    }
    
    .permission-checkboxes {
      display: flex;
      gap: 16px;
      justify-content: center;
      
      .el-checkbox {
        margin-right: 0;
      }
    }
  }
  
  .add-permission-section {
    display: flex;
    justify-content: center;
    padding: 16px 0;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 弹窗样式覆盖
:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #e4e7ed;
    background-color: #fafafa;
  }
}
</style>
