<template>
  <DialogComp v-model="visible" title="子任务属性类型" width="800px" :visible-footer-button="false"
    :close-on-click-modal="false" @closed="handleClose">
    <div class="attribute-type-management" v-loading="loading">
      <!-- 搜索和操作区域 -->
      <div class="search-section">
        <div class="search-form">
          <el-input v-model="searchForm.keyword" placeholder="请输入名称" class="search-input" clearable
            @clear="handleSearch" />
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button type="success" @click="handleCreateType">属性类型创建</el-button>
          <el-button type="info" @click="handleImportType">属性类型模板导入</el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="table-section">
        <BaseTableComp :data="filteredData" :colData="tableColumns" :loading="tableLoading" :checkbox="false"
          :visible-header="false" :visible-setting="false" :current-page="pagination.currentPage"
          :page-size="pagination.pageSize" :total="pagination.total" :auto-height="true" style="height: 350px;"
          @currentChange="handlePageChange" />
      </div>

      <!-- 底部按钮 -->
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </div>

    <!-- 属性类型创建弹窗 -->
    <DialogComp v-model="showCreateDialog" title="子任务属性类型创建" width="500px" @clickConfirm="handleConfirmCreate"
      @clickCancel="handleCancelCreate">
      <div class="create-form">
        <div class="form-row">
          <span class="label">子任务类型：</span>
          <el-select v-model="createForm.taskType" placeholder="请选择，业务报表、临时报表" style="width: 300px;">
            <el-option label="业务报表" value="业务报表" />
            <el-option label="临时报表" value="临时报表" />
            <el-option label="数据采集" value="数据采集" />
            <el-option label="流程审批" value="流程审批" />
            <el-option label="系统维护" value="系统维护" />
          </el-select>
        </div>
        <div class="form-row">
          <span class="label">属性类型：</span>
          <el-input v-model="createForm.attributeType" placeholder="请输入名称，支持多个类型输入以逗号分隔批量添加" style="width: 300px;" />
        </div>
      </div>
    </DialogComp>

    <!-- 属性类型导入弹窗 -->
    <DialogComp v-model="showImportDialog" title="子任务属性类型导入" width="600px" :visible-footer-button="false"
      @closed="handleCancelImport">
      <div class="import-content">
        <div class="upload-area" @click="handleFileUpload" @drop="handleFileDrop" @dragover.prevent>
          <div class="upload-icon">
            <el-icon size="48" color="#409eff">
              <Upload />
            </el-icon>
          </div>
          <div class="upload-text">
            <div v-if="!selectedFile">点击或将文件拖拽到这里上传</div>
            <div v-else class="selected-file">
              <span>已选择文件: {{ selectedFile.name }}</span>
              <el-button size="small" type="text" @click.stop="selectedFile = null">移除</el-button>
            </div>
          </div>
          <div class="upload-hint">支持 .xlsx, .xls, .csv 格式文件</div>
        </div>
        <div class="import-footer">
          <el-button @click="handleCancelImport">取消</el-button>
          <el-button type="primary" @click="handleConfirmImport" :disabled="!selectedFile">上传</el-button>
        </div>
      </div>
    </DialogComp>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import DialogComp from '@/components/common/dialog-comp.vue'
import BaseTableComp from '@/components/common/basetable-comp.vue'

// Props
interface Props {
  modelValue: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirm: [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const tableLoading = ref(false)

// 搜索表单
const searchForm = ref({
  keyword: ''
})

// 分页数据
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格数据
const attributeTypeData = ref([
  {
    id: 'type_001',
    taskType: '业务报表',
    attributeType: '党的建设',
    createTime: '2025-03-21'
  },
  {
    id: 'type_002',
    taskType: '临时报表',
    attributeType: '公共服务',
    createTime: '2025-03-21'
  },
  {
    id: 'type_003',
    taskType: '临时报表',
    attributeType: '平安法治',
    createTime: '2025-03-21'
  }
])

// 表格列配置
const tableColumns = [
  { field: 'taskType', title: '子任务类型', width: '200px' },
  { field: 'attributeType', title: '属性类型', width: '200px' },
  { field: 'createTime', title: '创建时间', width: '150px' }
]

// 操作按钮配置已移除，按照原型图不需要操作列

// 创建弹窗相关
const showCreateDialog = ref(false)
const createForm = ref({
  taskType: '',
  attributeType: ''
})

// 导入弹窗相关
const showImportDialog = ref(false)
const selectedFile = ref<File | null>(null)

// 过滤后的数据
const filteredData = computed(() => {
  let data = attributeTypeData.value

  if (searchForm.value.keyword) {
    data = data.filter(item =>
      item.attributeType.toLowerCase().includes(searchForm.value.keyword.toLowerCase()) ||
      item.taskType.toLowerCase().includes(searchForm.value.keyword.toLowerCase())
    )
  }

  return data
})

// 事件处理函数
const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = () => {
  ElMessage.success('属性类型管理设置已保存')
  visible.value = false
}

const handleSearch = () => {
  ElMessage.success(`搜索完成，找到 ${filteredData.value.length} 条记录`)
}

const handleCreateType = () => {
  showCreateDialog.value = true
}

const handleImportType = () => {
  showImportDialog.value = true
}

// 操作相关函数已移除，按照原型图不需要编辑删除功能

const handlePageChange = (page: number) => {
  pagination.value.currentPage = page
}

// 创建弹窗处理
const handleConfirmCreate = () => {
  if (!createForm.value.taskType || !createForm.value.attributeType) {
    ElMessage.warning('请填写完整信息')
    return
  }

  // 处理多个类型（逗号分隔）
  const types = createForm.value.attributeType.split(',').map(type => type.trim()).filter(type => type)

  types.forEach(type => {
    const newType = {
      id: `type_${Date.now()}_${Math.random()}`,
      taskType: createForm.value.taskType,
      attributeType: type,
      createTime: new Date().toISOString().split('T')[0]
    }
    attributeTypeData.value.unshift(newType)
  })

  pagination.value.total = attributeTypeData.value.length
  ElMessage.success(`成功创建 ${types.length} 个属性类型`)
  handleCancelCreate()
}

const handleCancelCreate = () => {
  showCreateDialog.value = false
  createForm.value = {
    taskType: '',
    attributeType: ''
  }
}

// 导入弹窗处理
const handleFileUpload = () => {
  // 创建隐藏的文件输入元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.xlsx,.xls,.csv'
  input.onchange = (event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (file) {
      selectedFile.value = file
      ElMessage.success(`已选择文件: ${file.name}`)
    }
  }
  input.click()
}

const handleFileDrop = (event: DragEvent) => {
  event.preventDefault()
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    const file = files[0]
    if (file.name.match(/\.(xlsx|xls|csv)$/)) {
      selectedFile.value = file
      ElMessage.success(`已选择文件: ${file.name}`)
    } else {
      ElMessage.error('请选择Excel或CSV文件')
    }
  }
}

const handleConfirmImport = () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择文件')
    return
  }

  // 模拟文件上传处理
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在上传文件...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  setTimeout(() => {
    loadingInstance.close()

    // 模拟解析文件并添加数据
    const mockImportData = [
      { taskType: '业务报表', attributeType: '导入类型1' },
      { taskType: '临时报表', attributeType: '导入类型2' },
      { taskType: '数据采集', attributeType: '导入类型3' }
    ]

    mockImportData.forEach(item => {
      const newType = {
        id: `type_${Date.now()}_${Math.random()}`,
        taskType: item.taskType,
        attributeType: item.attributeType,
        createTime: new Date().toISOString().split('T')[0]
      }
      attributeTypeData.value.unshift(newType)
    })

    pagination.value.total = attributeTypeData.value.length
    selectedFile.value = null
    showImportDialog.value = false

    ElMessage.success(`文件上传成功，导入了 ${mockImportData.length} 条数据`)
  }, 2000)
}

const handleCancelImport = () => {
  selectedFile.value = null
  showImportDialog.value = false
}

// 初始化数据
onMounted(() => {
  pagination.value.total = attributeTypeData.value.length
})
</script>

<style scoped lang="scss">
.attribute-type-management {
  .search-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    .search-form {
      display: flex;
      gap: 12px;
      align-items: center;
      flex-wrap: wrap;

      .search-input {
        width: 200px;
      }
    }
  }

  .table-section {
    margin-bottom: 20px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 0 0;
    border-top: 1px solid #e4e7ed;
  }
}

.create-form {
  .form-row {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .label {
      width: 120px;
      text-align: right;
      margin-right: 12px;
      color: #303133;
      font-weight: 500;
    }
  }
}

.import-content {
  .upload-area {
    border: 2px dashed #409eff;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      border-color: #66b1ff;
      background-color: #f0f9ff;
    }

    .upload-icon {
      margin-bottom: 16px;
    }

    .upload-text {
      color: #606266;
      font-size: 14px;

      .selected-file {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #409eff;
        font-weight: 500;
      }
    }

    .upload-hint {
      margin-top: 8px;
      color: #909399;
      font-size: 12px;
    }
  }

  .import-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
  }
}
</style>
