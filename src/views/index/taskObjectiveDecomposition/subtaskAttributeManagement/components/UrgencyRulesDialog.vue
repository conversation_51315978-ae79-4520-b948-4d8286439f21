<template>
  <el-dialog
    v-model="dialogVisible"
    title="子任务属性紧急程度规则"
    width="800px"
    :close-on-click-modal="false"
    destroy-on-close
    draggable
    @close="handleClose"
  >
    <div v-loading="loading" class="urgency-rules-content">
      <!-- 规则录入按钮 -->
      <div class="action-section">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAddRule"
          :disabled="loading"
        >
          规则录入
        </el-button>
      </div>

      <!-- 规则列表表格 -->
      <el-table
        :data="rulesData"
        border
        style="width: 100%"
        class="rules-table"
      >
        <el-table-column
          prop="subtaskType"
          label="子任务类型"
          width="200"
          align="center"
        />
        <el-table-column
          prop="urgencyLevel"
          label="紧急程度"
          width="150"
          align="center"
        >
          <template #default="{ row }">
            <el-tag :type="getUrgencyTagType(row.urgencyLevel)">
              {{ row.urgencyLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="rule"
          label="规则"
          align="center"
        />
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="confirmLoading">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 规则录入弹窗 -->
  <el-dialog
    v-model="showRuleInputDialog"
    title="子任务属性紧急程度规则录入"
    width="500px"
    :close-on-click-modal="false"
    destroy-on-close
    append-to-body
  >
    <el-form
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="ruleFormRules"
      label-width="120px"
      class="rule-form"
    >
      <el-form-item label="子任务类型:" prop="subtaskType">
        <el-select
          v-model="ruleForm.subtaskType"
          placeholder="请选择"
          style="width: 100%"
        >
          <el-option
            v-for="type in subtaskTypeOptions"
            :key="type"
            :label="type"
            :value="type"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="紧急程度:" prop="urgencyLevel">
        <el-select
          v-model="ruleForm.urgencyLevel"
          placeholder="请输入"
          style="width: 100%"
        >
          <el-option
            v-for="level in urgencyLevelOptions"
            :key="level"
            :label="level"
            :value="level"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="规则:" prop="rule">
        <div class="rule-input-group">
          <span class="rule-prefix">处理的限定</span>
          <el-input
            v-model="ruleForm.rule"
            placeholder="请输入"
            style="width: 100px; margin: 0 8px;"
          />
          <span class="rule-suffix">天内</span>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancelRuleInput">取消</el-button>
        <el-button type="primary" @click="handleConfirmRuleInput" :loading="ruleInputLoading">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { 
  mockUrgencyRules, 
  defaultUrgencyRuleForm,
  subtaskTypeOptions,
  urgencyLevelOptions,
  type UrgencyRule,
  type UrgencyRuleForm
} from './mockDialogData'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: any): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const confirmLoading = ref(false)
const rulesData = ref<UrgencyRule[]>([])

// 规则录入弹窗相关
const showRuleInputDialog = ref(false)
const ruleInputLoading = ref(false)
const ruleFormRef = ref<FormInstance>()
const ruleForm = ref<UrgencyRuleForm>({ ...defaultUrgencyRuleForm })

// 表单验证规则
const ruleFormRules: FormRules = {
  subtaskType: [
    { required: true, message: '请选择子任务类型', trigger: 'change' }
  ],
  urgencyLevel: [
    { required: true, message: '请选择紧急程度', trigger: 'change' }
  ],
  rule: [
    { required: true, message: '请输入规则', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入有效的数字', trigger: 'blur' }
  ]
}

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听弹窗打开，加载数据
watch(dialogVisible, (newVal) => {
  if (newVal) {
    loadRulesData()
  }
})

// 获取紧急程度标签类型
const getUrgencyTagType = (urgencyLevel: string) => {
  const typeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    '特急': 'danger',
    '紧急': 'warning',
    '平急': 'primary',
    '不急': 'info'
  }
  return typeMap[urgencyLevel] || 'info'
}

// 加载规则数据
const loadRulesData = async () => {
  loading.value = true
  try {
    // 模拟异步加载延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    rulesData.value = [...mockUrgencyRules]
    
    ElMessage.success('规则数据加载完成')
  } catch (error) {
    console.error('加载规则数据失败:', error)
    ElMessage.error('加载规则数据失败')
  } finally {
    loading.value = false
  }
}

// 添加规则
const handleAddRule = () => {
  showRuleInputDialog.value = true
  // 重置表单
  ruleForm.value = { ...defaultUrgencyRuleForm }
}

// 确认规则录入
const handleConfirmRuleInput = async () => {
  if (!ruleFormRef.value) return
  
  try {
    await ruleFormRef.value.validate()
    
    ruleInputLoading.value = true
    
    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 添加新规则到列表
    const newRule: UrgencyRule = {
      subtaskType: ruleForm.value.subtaskType,
      urgencyLevel: ruleForm.value.urgencyLevel,
      rule: `${ruleForm.value.rule}天内`
    }
    
    rulesData.value.push(newRule)
    
    ElMessage.success('规则录入成功')
    showRuleInputDialog.value = false
  } catch (error) {
    console.error('规则录入失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    ruleInputLoading.value = false
  }
}

// 取消规则录入
const handleCancelRuleInput = () => {
  showRuleInputDialog.value = false
  ruleForm.value = { ...defaultUrgencyRuleForm }
}

// 确认操作
const handleConfirm = async () => {
  confirmLoading.value = true
  try {
    // 模拟确认操作延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('confirm', {
      rulesData: rulesData.value,
      timestamp: new Date().toISOString()
    })
    
    ElMessage.success('规则确认成功')
    handleClose()
  } catch (error) {
    console.error('确认操作失败:', error)
    ElMessage.error('确认操作失败')
  } finally {
    confirmLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  emit('cancel')
  handleClose()
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  // 重置数据
  rulesData.value = []
  loading.value = false
  confirmLoading.value = false
  showRuleInputDialog.value = false
}
</script>

<style scoped lang="scss">
.urgency-rules-content {
  min-height: 300px;
  
  .action-section {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-start;
  }
  
  .rules-table {
    :deep(.el-table__header) {
      background-color: #f5f7fa;
      
      th {
        background-color: #f5f7fa !important;
        color: #303133;
        font-weight: 600;
      }
    }
    
    :deep(.el-table__body) {
      tr:hover > td {
        background-color: #f5f7fa;
      }
    }
  }
}

.rule-form {
  .rule-input-group {
    display: flex;
    align-items: center;
    
    .rule-prefix,
    .rule-suffix {
      color: #606266;
      font-size: 14px;
      white-space: nowrap;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

// 弹窗样式覆盖
:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #e4e7ed;
    background-color: #fafafa;
  }
}
</style>
