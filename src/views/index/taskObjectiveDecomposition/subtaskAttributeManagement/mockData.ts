import type {
  SubtaskAttribute
} from '@/define/subtaskAttribute.define'
import { 
  UrgencyLevel, 
  RiskLevel, 
  ImportanceLevel, 
  TaskStatus, 
  TaskType, 
  AttributeType 
} from '@/define/subtaskAttribute.define'

/**
 * 子任务属性模拟数据
 * Mock data for subtask attributes with realistic government management scenarios
 */
export const mockSubtaskAttributes: SubtaskAttribute[] = [
  {
    id: 'attr_001',
    attributeName: '永川区民政局填报流程',
    taskType: TaskType.BUSINESS_REPORT,
    attributeType: AttributeType.PARTY_BUILDING,
    attributeValue: 5,
    urgencyLevel: UrgencyLevel.URGENT,
    riskLevel: RiskLevel.LOW,
    importanceLevel: ImportanceLevel.IMPORTANT,
    status: TaskStatus.IN_PROGRESS,
    lastModified: '2024-01-15',
    modifier: '张三'
  },
  {
    id: 'attr_002',
    attributeName: '社区网格化管理数据统计',
    taskType: TaskType.DATA_COLLECTION,
    attributeType: AttributeType.SOCIAL_GOVERNANCE,
    attributeValue: 8,
    urgencyLevel: UrgencyLevel.HIGH,
    riskLevel: RiskLevel.MEDIUM,
    importanceLevel: ImportanceLevel.CRITICAL,
    status: TaskStatus.IN_PROGRESS,
    lastModified: '2024-01-14',
    modifier: '李四'
  },
  {
    id: 'attr_003',
    attributeName: '环保督察整改进度跟踪',
    taskType: TaskType.QUALITY_CONTROL,
    attributeType: AttributeType.ENVIRONMENTAL_PROTECTION,
    attributeValue: 7,
    urgencyLevel: UrgencyLevel.URGENT,
    riskLevel: RiskLevel.HIGH,
    importanceLevel: ImportanceLevel.CRITICAL,
    status: TaskStatus.PENDING,
    lastModified: '2024-01-13',
    modifier: '王五'
  },
  {
    id: 'attr_004',
    attributeName: '政务服务事项梳理',
    taskType: TaskType.SYSTEM_MAINTENANCE,
    attributeType: AttributeType.PUBLIC_SERVICE,
    attributeValue: 6,
    urgencyLevel: UrgencyLevel.MEDIUM,
    riskLevel: RiskLevel.LOW,
    importanceLevel: ImportanceLevel.IMPORTANT,
    status: TaskStatus.IN_PROGRESS,
    lastModified: '2024-01-12',
    modifier: '赵六'
  },
  {
    id: 'attr_005',
    attributeName: '招商引资项目审批流程',
    taskType: TaskType.WORKFLOW_APPROVAL,
    attributeType: AttributeType.ECONOMIC_DEVELOPMENT,
    attributeValue: 9,
    urgencyLevel: UrgencyLevel.HIGH,
    riskLevel: RiskLevel.MEDIUM,
    importanceLevel: ImportanceLevel.CRITICAL,
    status: TaskStatus.IN_PROGRESS,
    lastModified: '2024-01-11',
    modifier: '孙七'
  },
  {
    id: 'attr_006',
    attributeName: '基层党组织建设评估',
    taskType: TaskType.BUSINESS_REPORT,
    attributeType: AttributeType.PARTY_BUILDING,
    attributeValue: 4,
    urgencyLevel: UrgencyLevel.MEDIUM,
    riskLevel: RiskLevel.LOW,
    importanceLevel: ImportanceLevel.MEDIUM,
    status: TaskStatus.COMPLETED,
    lastModified: '2024-01-10',
    modifier: '周八'
  },
  {
    id: 'attr_007',
    attributeName: '城市管理综合执法数据',
    taskType: TaskType.DATA_COLLECTION,
    attributeType: AttributeType.ADMINISTRATIVE,
    attributeValue: 6,
    urgencyLevel: UrgencyLevel.MEDIUM,
    riskLevel: RiskLevel.MEDIUM,
    importanceLevel: ImportanceLevel.IMPORTANT,
    status: TaskStatus.IN_PROGRESS,
    lastModified: '2024-01-09',
    modifier: '吴九'
  },
  {
    id: 'attr_008',
    attributeName: '应急管理预案更新',
    taskType: TaskType.SYSTEM_MAINTENANCE,
    attributeType: AttributeType.SOCIAL_GOVERNANCE,
    attributeValue: 8,
    urgencyLevel: UrgencyLevel.URGENT,
    riskLevel: RiskLevel.HIGH,
    importanceLevel: ImportanceLevel.CRITICAL,
    status: TaskStatus.PENDING,
    lastModified: '2024-01-08',
    modifier: '郑十'
  },
  {
    id: 'attr_009',
    attributeName: '民生实事项目进度监控',
    taskType: TaskType.QUALITY_CONTROL,
    attributeType: AttributeType.PUBLIC_SERVICE,
    attributeValue: 7,
    urgencyLevel: UrgencyLevel.HIGH,
    riskLevel: RiskLevel.MEDIUM,
    importanceLevel: ImportanceLevel.CRITICAL,
    status: TaskStatus.IN_PROGRESS,
    lastModified: '2024-01-07',
    modifier: '钱一'
  },
  {
    id: 'attr_010',
    attributeName: '产业园区发展规划审核',
    taskType: TaskType.WORKFLOW_APPROVAL,
    attributeType: AttributeType.ECONOMIC_DEVELOPMENT,
    attributeValue: 5,
    urgencyLevel: UrgencyLevel.MEDIUM,
    riskLevel: RiskLevel.LOW,
    importanceLevel: ImportanceLevel.IMPORTANT,
    status: TaskStatus.IN_PROGRESS,
    lastModified: '2024-01-06',
    modifier: '陈二'
  }
]

// 注意：navigationTabs 已被移除，现在使用独立的按钮替代

/**
 * 操作按钮配置
 * Operation buttons configuration
 */
export const operationButtons = [
  {
    type: 'primary' as const,
    code: 'adjust',
    title: '调整',
    icon: 'Edit',
    verify: '',
    more: false,
    showBtn: 'true'
  },
  {
    type: 'success' as const,
    code: 'submit',
    title: '提交',
    icon: 'Upload',
    verify: '',
    more: false,
    showBtn: 'true'
  },
  {
    type: 'warning' as const,
    code: 'audit',
    title: '审核',
    icon: 'View',
    verify: '',
    more: false,
    showBtn: 'true'
  },
  {
    type: 'info' as const,
    code: 'attribute-association',
    title: '属性关联',
    icon: 'Connection',
    verify: '',
    more: true,
    showBtn: 'true'
  },
  {
    type: 'info' as const,
    code: 'more',
    title: '更多',
    icon: 'More',
    verify: '',
    more: true,
    showBtn: 'true'
  }
]

/**
 * 表格列配置
 * Table column configuration
 */
export const tableColumns = [
  { field: 'attributeName', title: '子任务属性名称', width: '200px', sortable: true },
  { field: 'taskType', title: '子任务类型', width: '120px', sortable: true },
  { field: 'attributeType', title: '属性类型', width: '120px', sortable: true },
  { field: 'attributeValue', title: '属性值', width: '80px', sortable: true },
  { field: 'urgencyLevel', title: '紧急程度', width: '100px', sortable: true },
  { field: 'riskLevel', title: '风险等级', width: '100px', sortable: true },
  { field: 'importanceLevel', title: '重要程度', width: '120px', sortable: true },
  { field: 'status', title: '状态', width: '100px', sortable: true },
  { field: 'lastModified', title: '最后修改', width: '120px', sortable: true },
  { field: 'modifier', title: '修改人', width: '100px' }
]

/**
 * 筛选选项配置
 * Filter options configuration
 */
export const filterOptions = {
  taskTypes: Object.values(TaskType),
  attributeTypes: Object.values(AttributeType),
  urgencyLevels: Object.values(UrgencyLevel),
  riskLevels: Object.values(RiskLevel),
  importanceLevels: Object.values(ImportanceLevel),
  statuses: Object.values(TaskStatus)
}