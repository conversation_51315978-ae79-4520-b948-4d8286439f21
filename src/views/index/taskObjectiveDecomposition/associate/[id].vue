<!-- 子任务关联任务计算页面 --> 
<script setup lang="ts" name="SubTaskAssociate">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import BaseTableComp from '@/components/common/basetable-comp.vue'
import AssociationJudgmentDialog from '@/views/index/taskObjectiveDecomposition/associate/components/AssociationJudgmentDialog.vue'

// 路由和参数
const route = useRoute()
const router = useRouter()
const subTaskId = ref(route.params.id as string)

// 页面状态
const loading = ref(false)
const taskInfo = ref<any>({})
const businessTableData = ref<any[]>([])

// 弹窗引用
const associationJudgmentDialogRef = ref()

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格列配置
const colData = [
  { field: 'sequence', title: '序号', width: 80, align: 'center' },
  { field: 'businessTableName', title: '业务表名称', minWidth: 200 },
  { field: 'publishDepartment', title: '发布部门', width: 180 },
  { field: 'belongingModule', title: '所属板块', width: 120, align: 'center' },
  { field: 'belongingChannel', title: '所属通道', width: 120, align: 'center' },
  { field: 'updateCycle', title: '更新周期', width: 100, align: 'center' },
  { field: 'deadline', title: '截止时间', width: 120, align: 'center' },
  { field: 'operation', title: '操作', width: 150, align: 'center', slot: true }
]

// 操作按钮配置
const buttons = [
  { type: 'primary' as const, code: 'calculate', title: '可跳转计算方式', icon: '', verify: 'true', more: false, showBtn: 'true' },
  { type: 'danger' as const, code: 'delete', title: '移除', icon: '', verify: 'true', more: false, showBtn: 'true' }
]

// 初始化Mock数据
const initMockData = () => {
  console.log('initMockData called')

  // 任务基本信息
  taskInfo.value = {
    subTaskName: '永川区民政局填报清单',
    taskType: '业务报表',
    taskCategory: '党的建设',
    responsiblePerson: '张飞',
    participants: '朱元璋-公共服务-刘备、朱元璋-公共服务-工商宇',
    startTime: '2024-01-01',
    endTime: '2024-12-31'
  }
  console.log('taskInfo set:', taskInfo.value)

  // 业务表数据
  businessTableData.value = [
    {
      id: 1,
      sequence: '01',
      businessTableName: '登记失业人员信息表',
      publishDepartment: '渝中区人力资源和社会保障局',
      belongingModule: '民生服务',
      belongingChannel: '创业就业/就业服务',
      updateCycle: '每日',
      deadline: '每周五前'
    },
    {
      id: 2,
      sequence: '02',
      businessTableName: '社会保障缴费记录表',
      publishDepartment: '渝中区社会保险局',
      belongingModule: '社会保障',
      belongingChannel: '社保缴费/缴费管理',
      updateCycle: '每月',
      deadline: '每月25日前'
    },
    {
      id: 3,
      sequence: '03',
      businessTableName: '民政救助申请表',
      publishDepartment: '渝中区民政局',
      belongingModule: '民生服务',
      belongingChannel: '社会救助/救助申请',
      updateCycle: '实时',
      deadline: '当日'
    },
    {
      id: 4,
      sequence: '04',
      businessTableName: '公共服务事项清单表',
      publishDepartment: '渝中区政务服务中心',
      belongingModule: '政务服务',
      belongingChannel: '公共服务/事项管理',
      updateCycle: '每周',
      deadline: '每周三前'
    },
    {
      id: 5,
      sequence: '05',
      businessTableName: '企业注册信息表',
      publishDepartment: '渝中区市场监督管理局',
      belongingModule: '市场监管',
      belongingChannel: '企业服务/注册登记',
      updateCycle: '每日',
      deadline: '次日上午10点前'
    },
    {
      id: 6,
      sequence: '06',
      businessTableName: '教育资助申请表',
      publishDepartment: '渝中区教育委员会',
      belongingModule: '教育服务',
      belongingChannel: '教育资助/资助管理',
      updateCycle: '每季度',
      deadline: '季度末最后一周'
    },
    {
      id: 7,
      sequence: '07',
      businessTableName: '医疗保险报销表',
      publishDepartment: '渝中区医疗保障局',
      belongingModule: '医疗保障',
      belongingChannel: '医保服务/报销管理',
      updateCycle: '每日',
      deadline: '每周一前'
    },
    {
      id: 8,
      sequence: '08',
      businessTableName: '住房公积金缴存表',
      publishDepartment: '渝中区住房公积金管理中心',
      belongingModule: '住房保障',
      belongingChannel: '公积金服务/缴存管理',
      updateCycle: '每月',
      deadline: '每月15日前'
    }
  ]
  console.log('businessTableData set:', businessTableData.value)
  console.log('businessTableData length:', businessTableData.value.length)

  // 更新分页总数
  pagination.total = businessTableData.value.length
  console.log('pagination.total set:', pagination.total)

  // 强制触发计算属性更新
  console.log('tableData after setting:', tableData.value)
  console.log('tableData length:', tableData.value.length)
}

// 计算表格数据（分页）
const tableData = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return businessTableData.value.slice(start, end)
})

// 调试日志
console.log('businessTableData:', businessTableData.value)
console.log('tableData:', tableData.value)

// 处理表格按钮点击
const handleTableButton = (button: any, row: any) => {
  switch (button.code) {
    case 'calculate':
      ElMessage.info(`跳转计算方式: ${row.businessTableName}`)
      // TODO: 实现跳转到计算方式页面
      break
    case 'delete':
      handleRemoveAssociation(row)
      break
  }
}

// 移除关联
const handleRemoveAssociation = (row: any) => {
  ElMessage.success(`已移除关联: ${row.businessTableName}`)
  // 从数据中移除
  const index = businessTableData.value.findIndex(item => item.id === row.id)
  if (index > -1) {
    businessTableData.value.splice(index, 1)
    pagination.total = businessTableData.value.length
  }
}

// 返回上一页
const handleBack = () => {
  router.back()
}

// 关联关系判断
const handleAssociationJudgment = () => {
  associationJudgmentDialogRef.value?.open()
}

// 生命周期
onMounted(() => {
  console.log('Associate page mounted, subTaskId:', subTaskId.value)
  loading.value = true
  // 模拟数据加载
  setTimeout(() => {
    console.log('Initializing mock data...')
    initMockData()
    console.log('Mock data initialized, businessTableData:', businessTableData.value)
    loading.value = false
  }, 500)
})
</script>

<template>
  <div class="subtask-associate" v-loading="loading">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-button type="primary" @click="handleBack" style="margin-right: 16px;">
          返回
        </el-button>
        <h2>子任务关联任务计算</h2>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleAssociationJudgment">
          关联关系判断
        </el-button>
        <el-button>确认关联</el-button>
        <el-button>取消</el-button>
      </div>
    </div>

    <!-- 任务基本信息 -->
    <div class="task-info-section">
      <h3>任务基本信息</h3>
      <div class="info-grid">
        <div class="info-row">
          <div class="info-item">
            <label>子任务名称：</label>
            <span>{{ taskInfo.subTaskName }}</span>
          </div>
          <div class="info-item">
            <label>任务类型：</label>
            <span>{{ taskInfo.taskType }}</span>
          </div>
          <div class="info-item">
            <label>任务分类：</label>
            <span>{{ taskInfo.taskCategory }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <label>责任人：</label>
            <span>{{ taskInfo.responsiblePerson }}</span>
          </div>
          <div class="info-item full-width">
            <label>参与人：</label>
            <span>{{ taskInfo.participants }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 业务表 -->
    <div class="business-table-section">
      <h3>业务表</h3>
      <BaseTableComp
        :data="tableData"
        :col-data="colData"
        :buttons="buttons"
        :pagination="pagination"
        @table-button="handleTableButton"
        @page-change="(page: number) => pagination.currentPage = page"
        @size-change="(size: number) => pagination.pageSize = size"
      />
    </div>

    <!-- 关联关系判断弹窗 -->
    <AssociationJudgmentDialog
      ref="associationJudgmentDialogRef"
      :task-start-time="taskInfo.startTime"
      :task-end-time="taskInfo.endTime"
    />
  </div>
</template>

<style scoped lang="scss">
.subtask-associate {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .header-left {
      display: flex;
      align-items: center;

      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 500;
        color: #303133;
      }
    }

    .header-right {
      display: flex;
      gap: 12px;
    }
  }

  .task-info-section,
  .business-table-section {
    background: white;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      border-left: 4px solid #409eff;
      padding-left: 12px;
    }
  }

  .info-grid {
    .info-row {
      display: flex;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .info-item {
      flex: 1;
      display: flex;
      align-items: center;
      margin-right: 40px;

      &.full-width {
        flex: 2;
      }

      &:last-child {
        margin-right: 0;
      }

      label {
        width: 140px;
        text-align: right;
        color: #606266;
        font-size: 14px;
        margin-right: 12px;
        flex-shrink: 0;
      }

      span {
        color: #303133;
        font-size: 14px;
        word-break: break-all;
      }
    }
  }
}
</style>
