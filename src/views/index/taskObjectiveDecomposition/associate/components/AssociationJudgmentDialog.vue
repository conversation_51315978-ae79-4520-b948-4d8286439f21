<!-- 关联关系判断弹窗 -->
<script setup lang="ts" name="AssociationJudgmentDialog">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import BaseTableComp from '@/components/common/basetable-comp.vue'

// Props
interface Props {
  taskStartTime?: string
  taskEndTime?: string
}

const props = withDefaults(defineProps<Props>(), {
  taskStartTime: '2024-01-01',
  taskEndTime: '2024-12-31'
})

// 弹窗显示控制
const visible = ref(false)

// 判断规则设置
const judgmentRules = reactive({
  inputOutputDependency: true,  // 输入输出依赖
  timeSequenceDependency: true, // 时间顺序依赖
  conditionDependency: true,    // 条件依赖
  resourceExclusivity: true,    // 资源独占性
  resourceTotalLimit: true      // 资源总量限制
})

// 业务表选择
const selectedBusinessTable = ref('')
const businessTableOptions = ref([
  { value: 'table1', label: '登记失业人员信息表' },
  { value: 'table2', label: '社会保障缴费记录表' },
  { value: 'table3', label: '民政救助申请表' },
  { value: 'table4', label: '公共服务事项清单表' }
])

// 时间范围选择
const timeRangeType = ref('taskTime') // 'taskTime' | 'customTime'
const customTimeRange = ref<[string, string]>(['', ''])

// 判断结果数据
const judgmentResults = ref<any[]>([])
const judgmentLoading = ref(false)

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 判断结果表格列配置
const resultColData = [
  { field: 'inputOutput', title: '输入输出依赖', width: 120, align: 'center' },
  { field: 'timeSequence', title: '时间顺序依赖', width: 120, align: 'center' },
  { field: 'condition', title: '条件依赖', width: 100, align: 'center' },
  { field: 'resourceExclusivity', title: '资源独占性', width: 120, align: 'center' },
  { field: 'resourceTotalLimit', title: '资源总量限制', width: 140, align: 'center' }
]

// 计算表格数据（分页）
const tableData = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return judgmentResults.value.slice(start, end)
})

// 显示时间输入框
const showTimeInput = computed(() => {
  return timeRangeType.value === 'taskTime' || timeRangeType.value === 'customTime'
})

// 时间显示值
const timeDisplayValue = computed(() => {
  if (timeRangeType.value === 'taskTime') {
    return `${props.taskStartTime} 至 ${props.taskEndTime}`
  } else if (timeRangeType.value === 'customTime' && customTimeRange.value[0] && customTimeRange.value[1]) {
    return `${customTimeRange.value[0]} 至 ${customTimeRange.value[1]}`
  }
  return ''
})

// 监听时间范围类型变化
watch(timeRangeType, (newType) => {
  if (newType === 'taskTime') {
    // 回显任务起止时间
    // 这里可以从props或者父组件传入的任务信息中获取
  }
})

// 打开弹窗
const open = () => {
  visible.value = true
  loadStoredData()
}

// 关闭弹窗
const close = () => {
  visible.value = false
}

// 开始判断
const startJudgment = () => {
  if (!selectedBusinessTable.value) {
    ElMessage.warning('请选择需判断的业务表')
    return
  }

  judgmentLoading.value = true
  
  // 模拟判断过程
  setTimeout(() => {
    // 生成模拟判断结果
    const mockResults = generateMockResults()
    judgmentResults.value = mockResults
    pagination.total = mockResults.length
    
    // 保存到localStorage
    saveToStorage()
    
    judgmentLoading.value = false
    ElMessage.success('关联关系判断完成')
  }, 2000)
}

// 生成模拟判断结果
const generateMockResults = () => {
  const results = []
  const statusOptions = ['影响', '无', '待定']
  
  for (let i = 1; i <= 5; i++) {
    results.push({
      id: i,
      inputOutput: statusOptions[Math.floor(Math.random() * statusOptions.length)],
      timeSequence: statusOptions[Math.floor(Math.random() * statusOptions.length)],
      condition: statusOptions[Math.floor(Math.random() * statusOptions.length)],
      resourceExclusivity: statusOptions[Math.floor(Math.random() * statusOptions.length)],
      resourceTotalLimit: statusOptions[Math.floor(Math.random() * statusOptions.length)]
    })
  }
  
  return results
}

// 保存数据到localStorage
const saveToStorage = () => {
  const data = {
    judgmentRules: judgmentRules,
    selectedBusinessTable: selectedBusinessTable.value,
    timeRangeType: timeRangeType.value,
    customTimeRange: customTimeRange.value,
    judgmentResults: judgmentResults.value,
    timestamp: Date.now()
  }
  localStorage.setItem('associationJudgmentData', JSON.stringify(data))
}

// 从localStorage加载数据
const loadStoredData = () => {
  const stored = localStorage.getItem('associationJudgmentData')
  if (stored) {
    try {
      const data = JSON.parse(stored)
      Object.assign(judgmentRules, data.judgmentRules || {})
      selectedBusinessTable.value = data.selectedBusinessTable || ''
      timeRangeType.value = data.timeRangeType || 'taskTime'
      customTimeRange.value = data.customTimeRange || ['', '']
      judgmentResults.value = data.judgmentResults || []
      pagination.total = judgmentResults.value.length
    } catch (error) {
      console.error('加载存储数据失败:', error)
    }
  }
}

// 取消操作
const handleCancel = () => {
  close()
}

// 确认操作
const handleConfirm = () => {
  saveToStorage()
  ElMessage.success('设置已保存')
  close()
}

// 暴露方法给父组件
defineExpose({
  open,
  close
})
</script>

<template>
  <el-dialog
    v-model="visible"
    title="关联关系判断"
    width="900px"
    :close-on-click-modal="false"
    :visible-close-button="false"
    :visible-confirm-button="false"
    draggable
    destroy-on-close
    class="association-judgment-dialog"
  >
    <div class="dialog-content">
      <!-- 判断规则设置 -->
      <div class="rule-section">
        <div class="section-title">
          <span class="required">*</span>判断规则设置：
        </div>
        <div class="rule-checkboxes">
          <el-checkbox v-model="judgmentRules.inputOutputDependency">输入输出依赖</el-checkbox>
          <el-checkbox v-model="judgmentRules.timeSequenceDependency">时间顺序依赖</el-checkbox>
          <el-checkbox v-model="judgmentRules.conditionDependency">条件依赖</el-checkbox>
          <el-checkbox v-model="judgmentRules.resourceExclusivity">资源独占性</el-checkbox>
          <el-checkbox v-model="judgmentRules.resourceTotalLimit">资源总量限制</el-checkbox>
        </div>
      </div>

      <!-- 业务表选择 -->
      <div class="business-table-section">
        <div class="section-title">关联关系判断 请选择需判断业务表</div>
        <el-select
          v-model="selectedBusinessTable"
          placeholder="请选择业务表"
          style="width: 300px;"
        >
          <el-option
            v-for="option in businessTableOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </div>

      <!-- 时间范围选择 -->
      <div class="time-range-section">
        <div class="section-title">说明</div>
        <div class="time-options">
          <div class="time-option">
            <el-checkbox v-model="timeRangeType" true-value="taskTime" false-value="">
              任务起止时间
            </el-checkbox>
          </div>
          <div class="time-option">
            <el-checkbox v-model="timeRangeType" true-value="customTime" false-value="">
              自定义时间范围
            </el-checkbox>
          </div>
        </div>
        
        <!-- 时间输入框 -->
        <div v-if="showTimeInput" class="time-input">
          <div v-if="timeRangeType === 'taskTime'" class="task-time-display">
            <el-input
              :value="timeDisplayValue"
              readonly
              placeholder="任务起止时间"
              style="width: 300px;"
            />
          </div>
          <div v-else-if="timeRangeType === 'customTime'" class="custom-time-picker">
            <el-date-picker
              v-model="customTimeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 300px;"
            />
          </div>
        </div>
      </div>

      <!-- 开始判断按钮 -->
      <div class="judgment-action">
        <el-button type="primary" @click="startJudgment" :loading="judgmentLoading">
          开始判断
        </el-button>
      </div>

      <!-- 判断结果 -->
      <div v-if="judgmentResults.length > 0" class="result-section">
        <div class="section-title">判断结果</div>
        <BaseTableComp
          :table-data="tableData"
          :col-data="resultColData"
          :pagination="pagination"
          @page-change="(page: number) => pagination.currentPage = page"
          @size-change="(size: number) => pagination.pageSize = size"
        />
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.association-judgment-dialog {
  .dialog-content {
    padding: 0;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 12px;
      
      .required {
        color: #f56c6c;
        margin-right: 4px;
      }
    }

    .rule-section {
      margin-bottom: 24px;
      
      .rule-checkboxes {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        
        :deep(.el-checkbox) {
          margin-right: 0;
        }
      }
    }

    .business-table-section {
      margin-bottom: 24px;
    }

    .time-range-section {
      margin-bottom: 24px;
      
      .time-options {
        margin-bottom: 12px;
        
        .time-option {
          margin-bottom: 8px;
        }
      }
      
      .time-input {
        margin-left: 24px;
      }
    }

    .judgment-action {
      margin-bottom: 24px;
      text-align: center;
    }

    .result-section {
      .section-title {
        margin-bottom: 16px;
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
}
</style>
