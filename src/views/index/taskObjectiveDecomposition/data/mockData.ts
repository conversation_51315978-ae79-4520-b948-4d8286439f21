/**
 * 模拟数据文件
 */

// 时长分析数据 - 根据原型设计更新
export const timeAnalysisData = [
  { stage: '任务接收', duration: '2天' },
  { stage: '任务填报', duration: '5天' }
]

// 提醒历史数据 - 根据原型设计更新
export const reminderHistoryData = [
  { reminderTime: '2025-04-23', reminderPerson: '朱沅镇-公共服务-王五、朱沅镇-公共服务-李四' },
  { reminderTime: '2025-05-23', reminderPerson: '朱沅镇-公共服务-王五、朱沅镇-公共服务-李四' },
  { reminderTime: '2025-06-23', reminderPerson: '朱沅镇-公共服务-王五、朱沅镇-公共服务-李四' }
]

// 风险分析数据
export const riskAnalysisData = [
  { riskItem: '任务接受响应时间过长', probability: 3, impact: 4, riskLevel: '中高', priority: 1 },
  { riskItem: '任务审核时间过长', probability: 4, impact: 3, riskLevel: '中高', priority: 2 },
  { riskItem: '任务审核退回', probability: 2, impact: 5, riskLevel: '高', priority: 3 },
  { riskItem: '任务审核驳回', probability: 3, impact: 3, riskLevel: '中', priority: 4 }
]

// 子任务历史记录数据
export const subTaskHistoryData = [
  {
    taskName: '永川区民政局填报流程',
    taskType: '业务报表',
    taskCategory: '党的建设',
    attributeValue: 5,
    responsiblePerson: '程飞',
    participants: '朱沅镇 公共服务-刘晓等',
    urgencyLevel: 'P2'
  },
  {
    taskName: '永川区民政局填报流程',
    taskType: '业务报表',
    taskCategory: '党的建设',
    attributeValue: 5,
    responsiblePerson: '程飞',
    participants: '朱沅镇 公共服务-刘晓等',
    urgencyLevel: 'P1'
  },
  {
    taskName: '系统填报流程',
    taskType: '业务报表',
    taskCategory: '民生服务',
    attributeValue: 4,
    responsiblePerson: '程飞',
    participants: '朱沅镇 公共服务-刘晓等',
    urgencyLevel: 'P3'
  },
  {
    taskName: '系统填报流程',
    taskType: '临时报表',
    taskCategory: '民生服务',
    attributeValue: 4,
    responsiblePerson: '程飞',
    participants: '朱沅镇 公共服务-刘晓等',
    urgencyLevel: 'P2'
  }
]

// 进度追踪数据
export const progressTrackingData = [
  { milestone: '任务接收', completionTime: '2025-04-23', completionRate: '11%' },
  { milestone: '数据填报', completionTime: '2025-04-30', completionRate: '17%' },
  { milestone: '数据审核', completionTime: '2025-05-03', completionRate: '24%' }
]

// 生成动态时长分析数据
export const generateTimeAnalysisData = (task: any) => {
  if (!task) return timeAnalysisData

  const startDate = new Date(task.startTime || '2025-04-01')
  const currentDate = new Date()
  const daysDiff = Math.ceil((currentDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))

  return [
    { stage: '任务接收', duration: '1天' },
    { stage: '需求分析', duration: '2天' },
    { stage: '数据收集', duration: `${Math.max(1, Math.floor(daysDiff * 0.4))}天` },
    { stage: '数据填报', duration: `${Math.max(2, Math.floor(daysDiff * 0.3))}天` },
    { stage: '数据审核', duration: `${Math.max(1, Math.floor(daysDiff * 0.2))}天` },
    { stage: '任务完成', duration: `${Math.max(1, Math.floor(daysDiff * 0.1))}天` }
  ]
}

// 默认合并表单数据
export const getDefaultMergeFormData = () => ({
  taskType: '', // 子任务类型
  taskName: '', // 合并后子任务名称
  taskCategory: '', // 子任务分类
  taskDescription: '', // 子任务描述
  priorityLevel: '', // 子任务优先级设置
  responsiblePerson: '', // 责任人
  participants: [] as string[] // 参与人（多选数组）
})

// 进度配置默认数据
export const getDefaultProgressConfig = () => ({
  progressCalculationEnabled: true
})

// 紧急程度选项配置
export const urgencyLevelOptions = [
  { label: 'P1 - 特急', value: 'P1', color: '#f56c6c', description: '需要立即处理' },
  { label: 'P2 - 加急', value: 'P2', color: '#e6a23c', description: '需要优先处理' },
  { label: 'P3 - 平急', value: 'P3', color: '#409eff', description: '正常处理' },
  { label: 'P4 - 不重要', value: 'P4', color: '#909399', description: '可延后处理' }
]

// 紧急程度颜色映射
export const urgencyColorMap = {
  'P1': 'danger',
  'P2': 'warning',
  'P3': 'primary',
  'P4': 'info'
} as const

// 重要程度选项配置
export const importanceLevelOptions = [
  { label: 'I1 - 非常重要', value: 'I1', color: '#f56c6c', description: '核心业务，影响重大' },
  { label: 'I2 - 重要', value: 'I2', color: '#e6a23c', description: '重要业务，需要关注' },
  { label: 'I3 - 一般重要', value: 'I3', color: '#409eff', description: '常规业务，正常处理' },
  { label: 'I4 - 不重要', value: 'I4', color: '#909399', description: '辅助业务，可延后' }
]

// 重要程度颜色映射
export const importanceColorMap = {
  'I1': 'danger',
  'I2': 'warning',
  'I3': 'primary',
  'I4': 'info'
} as const

// 知识搜索模拟结果
export const getKnowledgeSearchResult = (searchInput: string) => {
  // 模拟知识搜索结果
  const results = [
    '......建议精简审核流程或跳过审核环节...',
    '......可以考虑并行处理多个审核环节...',
    '......建议增加自动化审核机制...',
    '......优化任务分配策略，减少等待时间...'
  ]

  return results[Math.floor(Math.random() * results.length)]
}

// 审核历史数据生成函数
export const generateAuditHistoryData = (subTaskId: string) => {
  const auditPersons = ['张三', '李四', '王五', '赵六', '钱七']
  const auditResults = ['通过', '驳回', '退回']
  const auditComments = [
    '数据填报完整，审核通过',
    '部分数据存在错误，需要重新核实',
    '材料不完整，请补充相关文档',
    '数据质量良好，符合要求',
    '格式不规范，请按要求重新整理',
    '内容详实，数据准确，审核通过'
  ]

  const history = []
  const historyCount = Math.floor(Math.random() * 3) + 1 // 1-3条历史记录

  for (let i = 0; i < historyCount; i++) {
    const date = new Date()
    date.setDate(date.getDate() - (historyCount - i) * 2) // 间隔2天

    history.push({
      id: `audit_${subTaskId}_${i}`,
      auditTime: date.toLocaleString(),
      auditPerson: auditPersons[Math.floor(Math.random() * auditPersons.length)],
      auditResult: auditResults[Math.floor(Math.random() * auditResults.length)],
      auditComment: auditComments[Math.floor(Math.random() * auditComments.length)]
    })
  }

  return history
}

// 相关文档数据生成函数
export const generateRelatedDocumentsData = (subTaskId: string) => {
  const fileTypes = [
    { name: '数据填报表', ext: 'xlsx', size: '2.5MB' },
    { name: '说明文档', ext: 'pdf', size: '1.2MB' },
    { name: '补充材料', ext: 'docx', size: '800KB' },
    { name: '数据源文件', ext: 'csv', size: '3.1MB' },
    { name: '审核清单', ext: 'xlsx', size: '1.8MB' }
  ]

  const uploaders = ['程飞', '刘晓', '王五', '李四']
  const documents = []
  const docCount = Math.floor(Math.random() * 3) + 2 // 2-4个文档

  for (let i = 0; i < docCount; i++) {
    const fileType = fileTypes[Math.floor(Math.random() * fileTypes.length)]
    const date = new Date()
    date.setDate(date.getDate() - Math.floor(Math.random() * 7)) // 最近7天内

    documents.push({
      id: `doc_${subTaskId}_${i}`,
      fileName: `${fileType.name}_${subTaskId}.${fileType.ext}`,
      fileSize: fileType.size,
      uploadTime: date.toLocaleString(),
      uploadPerson: uploaders[Math.floor(Math.random() * uploaders.length)]
    })
  }

  return documents
}
