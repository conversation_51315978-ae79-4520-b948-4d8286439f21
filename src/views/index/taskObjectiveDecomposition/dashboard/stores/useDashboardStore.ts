/**
 * 仪表板状态管理 Store
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  DashboardData,
  ChartConfig,
  ExtendedChartConfig,
  DashboardError,
  DashboardSaveConfig,
  SaveDashboardRequest,
  SaveDashboardResponse,
  LoadDashboardResponse
} from '../types/dashboard.types'
import { ErrorType, ViewType } from '../types/dashboard.types'
import { generateDashboardData, updateDashboardData } from '../services/mockDataService'
import { storageService } from '../services/storageService'
import { saveOrUpdateDashboardConfig, getDashboardConfig } from '@/api/dashboardApi'
import { validateDashboardConfig, cleanupDashboardConfig } from '../utils/configValidator'

// 默认图表配置
const defaultChartConfig: ExtendedChartConfig = {
  common: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true
  },
  barChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    orientation: 'vertical',
    showDataLabels: false,
    barWidth: 40,
    spacing: 10
  },
  lineChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    smooth: true,
    showPoints: true,
    showArea: false,
    lineWidth: 2
  },
  pieChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    showPercentage: true,
    innerRadius: 0,
    outerRadius: 80,
    startAngle: 90
  },
  // 扩展图表配置
  radarChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    shape: 'polygon',
    splitNumber: 5,
    axisName: {
      show: true,
      formatter: undefined
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: '#e0e6ed',
        width: 1
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
      }
    }
  },
  scatterChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    symbolSize: 8,
    showSymbol: true,
    smooth: false,
    connectNulls: false,
    emphasis: {
      focus: 'none',
      scale: true
    }
  },
  mapChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    map: 'china',
    roam: true,
    zoom: 1,
    center: undefined,
    aspectScale: 0.75,
    boundingCoords: undefined,
    layoutCenter: undefined,
    layoutSize: undefined
  },
  heatmapChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    xAxis: {
      type: 'category',
      data: undefined
    },
    yAxis: {
      type: 'category',
      data: undefined
    },
    visualMap: {
      min: 0,
      max: 100,
      calculable: true,
      orient: 'vertical',
      left: 'right',
      bottom: 'center'
    }
  },
  donutChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    showPercentage: true,
    innerRadius: 40,
    outerRadius: 80,
    startAngle: 90,
    roseType: false
  },
  multiDonutChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    series: []
  },
  compositePieChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    series: []
  },
  stackedLineChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    stack: 'total',
    stackStrategy: 'samesign',
    percentage: false,
    smooth: true,
    showPoints: true,
    showArea: false,
    lineWidth: 2
  },
  stackedBarChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    stack: 'total',
    stackStrategy: 'samesign',
    percentage: false,
    orientation: 'vertical',
    showDataLabels: false,
    barWidth: 40,
    spacing: 10
  },
  taskRelation: {
    type: 'business',
    enabled: true,
    relationshipType: 'dependency',
    showLabels: true,
    nodeStyle: {
      size: 30,
      color: '#409EFF',
      borderColor: '#ffffff',
      borderWidth: 2
    },
    edgeStyle: {
      color: '#606266',
      width: 2,
      type: 'solid'
    }
  }
}

export const useDashboardStore = defineStore('dashboard', () => {
  // 状态
  const loading = ref(false)
  const error = ref<DashboardError | null>(null)
  const currentView = ref<ViewType>(ViewType.OVERVIEW)
  const isConfigPanelVisible = ref(false)
  const dashboardData = ref<DashboardData | null>(null)
  const chartConfig = ref<ExtendedChartConfig>(defaultChartConfig)
  const autoRefreshEnabled = ref(true)
  const autoRefreshInterval = ref(30000) // 30秒

  // 保存相关状态
  const saving = ref(false)
  const saveError = ref<string | null>(null)
  const lastSaveTime = ref<Date | null>(null)
  const hasUnsavedChanges = ref(false)

  // 自动刷新定时器
  let refreshTimer: NodeJS.Timeout | null = null

  // 计算属性
  const hasData = computed(() => dashboardData.value !== null)
  
  const lastUpdateTime = computed(() => {
    return dashboardData.value?.lastUpdated || null
  })

  const isChartView = computed(() => {
    return ['bar-chart', 'line-chart', 'pie-chart'].includes(currentView.value)
  })

  // 错误处理
  const setError = (type: ErrorType, message: string, details?: any) => {
    const dashboardError: DashboardError = {
      type,
      message,
      details,
      timestamp: new Date()
    }
    error.value = dashboardError
    console.error(`[${type}] ${message}`, details)
  }

  const clearError = () => {
    error.value = null
  }

  // 数据操作
  const loadData = async (useCache = true): Promise<void> => {
    loading.value = true
    clearError()

    try {
      let data: DashboardData | null = null

      // 尝试从缓存加载
      if (useCache) {
        data = storageService.getDashboardData()
      }

      // 如果没有缓存数据或不使用缓存，生成新数据
      if (!data) {
        data = generateDashboardData()
        
        // 保存到缓存
        const saved = storageService.saveDashboardData(data)
        if (!saved) {
          setError(ErrorType.STORAGE_ERROR, '保存数据到本地存储失败')
        }
      }

      dashboardData.value = data
    } catch (err) {
      setError(ErrorType.DATA_LOAD_ERROR, '加载数据失败', err)
    } finally {
      loading.value = false
    }
  }

  const refreshData = async (): Promise<void> => {
    if (!dashboardData.value) {
      await loadData(false)
      return
    }

    loading.value = true
    clearError()

    try {
      const updatedData = updateDashboardData(dashboardData.value)
      dashboardData.value = updatedData

      // 保存到缓存
      const saved = storageService.saveDashboardData(updatedData)
      if (!saved) {
        setError(ErrorType.STORAGE_ERROR, '保存更新数据失败')
      }
    } catch (err) {
      setError(ErrorType.DATA_LOAD_ERROR, '刷新数据失败', err)
    } finally {
      loading.value = false
    }
  }

  // 视图控制
  const setCurrentView = (view: ViewType) => {
    currentView.value = view
  }

  const toggleConfigPanel = () => {
    isConfigPanelVisible.value = !isConfigPanelVisible.value
  }

  const showConfigPanel = () => {
    isConfigPanelVisible.value = true
  }

  const hideConfigPanel = () => {
    isConfigPanelVisible.value = false
  }

  // 配置管理
  const updateChartConfig = (newConfig: Partial<ExtendedChartConfig>) => {
    try {
      chartConfig.value = {
        ...chartConfig.value,
        ...newConfig
      }
      
      // 保存到本地存储
      const saved = storageService.saveChartConfig(chartConfig.value)
      if (!saved) {
        setError(ErrorType.CONFIG_SAVE_ERROR, '保存配置失败')
      }
    } catch (err) {
      setError(ErrorType.CONFIG_SAVE_ERROR, '更新配置失败', err)
    }
  }

  const resetChartConfig = () => {
    chartConfig.value = { ...defaultChartConfig }
    storageService.saveChartConfig(chartConfig.value)
  }

  const loadChartConfig = () => {
    const savedConfig = storageService.getChartConfig()
    if (savedConfig) {
      chartConfig.value = savedConfig
    }
  }

  // 自动刷新控制
  const startAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
    }

    if (autoRefreshEnabled.value) {
      refreshTimer = setInterval(() => {
        refreshData()
      }, autoRefreshInterval.value)
    }
  }

  const stopAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }

  const setAutoRefreshInterval = (interval: number) => {
    autoRefreshInterval.value = interval
    if (autoRefreshEnabled.value) {
      stopAutoRefresh()
      startAutoRefresh()
    }
  }

  const toggleAutoRefresh = () => {
    autoRefreshEnabled.value = !autoRefreshEnabled.value
    if (autoRefreshEnabled.value) {
      startAutoRefresh()
    } else {
      stopAutoRefresh()
    }
  }

  // 数据导出
  const exportData = () => {
    if (!dashboardData.value) return null

    const exportData = {
      dashboardData: dashboardData.value,
      chartConfig: chartConfig.value,
      exportTime: new Date().toISOString(),
      version: '1.0.0'
    }

    return JSON.stringify(exportData, null, 2)
  }

  const exportConfig = () => {
    const configData = {
      chartConfig: chartConfig.value,
      exportTime: new Date().toISOString(),
      version: '1.0.0'
    }

    return JSON.stringify(configData, null, 2)
  }

  // 数据导入
  const importConfig = (configData: any) => {
    try {
      if (configData.chartConfig) {
        chartConfig.value = configData.chartConfig
        storageService.saveChartConfig(chartConfig.value)
      }
    } catch (err) {
      setError(ErrorType.CONFIG_SAVE_ERROR, '导入配置失败', err)
    }
  }

  // 清理数据
  const clearData = () => {
    dashboardData.value = null
    stopAutoRefresh()
    storageService.clear()
  }

  // ==================== 仪表板配置保存相关方法 ====================

  // 保存仪表板配置（直接使用本地存储）
  const saveDashboardConfig = async (taskId: string, config: DashboardSaveConfig): Promise<boolean> => {
    if (saving.value) {
      console.warn('保存操作正在进行中，请稍候')
      return false
    }

    saving.value = true
    saveError.value = null

    try {
      // 验证配置数据
      const validation = validateDashboardConfig(config)
      if (!validation.isValid) {
        throw new Error(`配置验证失败: ${validation.errors.join(', ')}`)
      }

      // 清理配置数据
      const cleanup = cleanupDashboardConfig(config)
      const finalConfig = cleanup.config

      if (cleanup.cleaned) {
        console.log('配置已清理:', cleanup.changes)
      }

      // 直接保存到本地存储（演示页面不使用API）
      storageService.setItem(`dashboard-config-${taskId}`, finalConfig)

      lastSaveTime.value = new Date()
      hasUnsavedChanges.value = false

      console.log('仪表板配置保存成功（本地存储）')
      return true
    } catch (err: any) {
      const errorMessage = err.message || '保存配置时发生未知错误'
      saveError.value = errorMessage
      setError('CONFIG_SAVE_ERROR' as ErrorType, errorMessage, err)
      console.error('保存仪表板配置失败:', err)
      return false
    } finally {
      saving.value = false
    }
  }

  // 加载仪表板配置（直接从本地存储）
  const loadDashboardConfig = async (taskId: string): Promise<DashboardSaveConfig | null> => {
    try {
      // 直接从本地存储加载（演示页面不使用API）
      return loadDashboardConfigFromLocal(taskId)
    } catch (err) {
      console.error('从本地存储加载配置失败:', err)
      return null
    }
  }

  // 从本地存储加载配置
  const loadDashboardConfigFromLocal = (taskId: string): DashboardSaveConfig | null => {
    try {
      const localConfig = storageService.getItem<DashboardSaveConfig>(`dashboard-config-${taskId}`)
      if (localConfig) {
        // 使用存储服务的验证功能
        const validation = storageService.validateDashboardConfig(localConfig)
        if (validation.isValid) {
          console.log('本地配置验证通过')
          return localConfig
        } else {
          console.warn('本地配置验证失败:', validation.errors)
          // 自动清理无效配置
          storageService.removeItem(`dashboard-config-${taskId}`)
        }
      }
    } catch (err) {
      console.error('从本地存储加载配置失败:', err)
      // 清理可能损坏的配置
      try {
        storageService.removeItem(`dashboard-config-${taskId}`)
      } catch (cleanupErr) {
        console.error('清理损坏配置失败:', cleanupErr)
      }
    }
    return null
  }

  // 清理所有无效配置
  const cleanupAllInvalidConfigs = (): number => {
    return storageService.cleanupInvalidConfigs()
  }

  // 标记有未保存的更改
  const markUnsavedChanges = () => {
    hasUnsavedChanges.value = true
  }

  // 清除保存错误
  const clearSaveError = () => {
    saveError.value = null
  }

  // 初始化
  const initialize = async () => {
    // 加载保存的配置
    loadChartConfig()
    
    // 加载数据
    await loadData()
    
    // 启动自动刷新
    startAutoRefresh()
  }

  // 销毁
  const destroy = () => {
    stopAutoRefresh()
  }

  return {
    // 状态
    loading,
    error,
    currentView,
    isConfigPanelVisible,
    dashboardData,
    chartConfig,
    autoRefreshEnabled,
    autoRefreshInterval,
    saving,
    saveError,
    lastSaveTime,
    hasUnsavedChanges,

    // 计算属性
    hasData,
    lastUpdateTime,
    isChartView,

    // 方法
    loadData,
    refreshData,
    setCurrentView,
    toggleConfigPanel,
    showConfigPanel,
    hideConfigPanel,
    updateChartConfig,
    resetChartConfig,
    loadChartConfig,
    startAutoRefresh,
    stopAutoRefresh,
    setAutoRefreshInterval,
    toggleAutoRefresh,
    exportData,
    exportConfig,
    importConfig,
    clearData,
    clearError,
    initialize,
    destroy,
    saveDashboardConfig,
    loadDashboardConfig,
    loadDashboardConfigFromLocal,
    cleanupAllInvalidConfigs,
    markUnsavedChanges,
    clearSaveError
  }
})
