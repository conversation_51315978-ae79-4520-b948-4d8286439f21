/**
 * 任务数据可视化仪表板类型定义
 */

// 视图类型枚举
export enum ViewType {
  OVERVIEW = 'overview',
  TABLE = 'table',
  BAR_CHART = 'bar-chart',
  LINE_CHART = 'line-chart',
  PIE_CHART = 'pie-chart'
}

// 视图类型字符串联合类型，用于支持字符串值
export type ViewTypeString = 'overview' | 'table' | 'bar-chart' | 'line-chart' | 'pie-chart'

// 仪表板主数据结构
export interface DashboardData {
  overview: OverviewData
  tableData: TableData
  chartData: ChartData
  lastUpdated: Date
}

// 概览数据
export interface OverviewData {
  cards: OverviewCard[]
}

export interface OverviewCard {
  id: string
  title: string
  value: number
  unit?: string
  trend?: {
    direction: 'up' | 'down' | 'stable'
    percentage: number
  }
  icon?: string
  color?: string
}

// 表格数据
export interface TableData {
  columns: TableColumn[]
  rows: TableRow[]
  pagination: PaginationInfo
}

export interface TableColumn {
  key: string
  label: string
  type: 'text' | 'number' | 'date' | 'status'
  sortable?: boolean
  filterable?: boolean
  width?: number
}

export interface TableRow {
  id: string
  [key: string]: any
}

export interface PaginationInfo {
  currentPage: number
  pageSize: number
  total: number
}

// 图表数据
export interface ChartData {
  barChart: BarChartData
  lineChart: LineChartData
  pieChart: PieChartData
}

export interface BarChartData {
  categories: string[]
  series: BarSeries[]
}

export interface BarSeries {
  name: string
  data: number[]
  color?: string
}

export interface LineChartData {
  xAxis: (string | number)[]
  series: LineSeries[]
}

export interface LineSeries {
  name: string
  data: (number | null)[]
  color?: string
  lineStyle?: LineStyle
}

export interface LineStyle {
  width?: number
  type?: 'solid' | 'dashed' | 'dotted'
}

export interface PieChartData {
  series: PieSeries[]
}

export interface PieSeries {
  name: string
  data: PieDataItem[]
}

export interface PieDataItem {
  name: string
  value: number
  color?: string
}

// 图表配置
export interface ChartConfig {
  common: CommonConfig
  barChart: BarChartConfig
  lineChart: LineChartConfig
  pieChart: PieChartConfig
}

export interface CommonConfig {
  theme: 'light' | 'dark'
  animation: boolean
  responsive: boolean
  showLegend: boolean
  showTooltip: boolean
}

export interface BarChartConfig extends CommonConfig {
  orientation: 'vertical' | 'horizontal'
  showDataLabels: boolean
  barWidth: number
  spacing: number
}

export interface LineChartConfig extends CommonConfig {
  smooth: boolean
  showPoints: boolean
  showArea: boolean
  lineWidth: number
}

export interface PieChartConfig extends CommonConfig {
  showPercentage: boolean
  innerRadius: number
  outerRadius: number
  startAngle: number
}

// 仪表板状态
export interface DashboardState {
  currentView: ViewType
  dashboardData: DashboardData
  chartConfig: ChartConfig
  isConfigPanelVisible: boolean
  loading: boolean
}

// 错误类型
export enum ErrorType {
  DATA_LOAD_ERROR = 'DATA_LOAD_ERROR',
  CHART_RENDER_ERROR = 'CHART_RENDER_ERROR',
  CONFIG_SAVE_ERROR = 'CONFIG_SAVE_ERROR',
  STORAGE_ERROR = 'STORAGE_ERROR'
}

export interface DashboardError {
  type: ErrorType
  message: string
  details?: any
  timestamp: Date
}

// 导航菜单项
export interface NavigationItem {
  id: string
  label: string
  icon: string
  view: ViewType
  disabled?: boolean
}

// 数据点接口
export interface DataPoint {
  name: string
  value: number
  category?: string
  timestamp?: Date
  [key: string]: any
}

// ECharts事件接口
export interface ChartEvents {
  onDataPointClick?: (data: DataPoint) => void
  onLegendClick?: (legend: string) => void
  onChartReady?: (chart: any) => void
}

// 基础图表组件Props
export interface BaseChartProps {
  data: any
  config: any
  loading?: boolean
  height?: string | number
  events?: ChartEvents
}

// ==================== 新增图表类型配置 ====================

// 雷达图配置
export interface RadarChartConfig extends CommonConfig {
  shape: 'polygon' | 'circle'
  splitNumber: number
  axisName: {
    show: boolean
    formatter?: string | ((name: string) => string)
  }
  splitLine: {
    show: boolean
    lineStyle: {
      color: string
      width: number
    }
  }
  splitArea: {
    show: boolean
    areaStyle: {
      color: string[]
    }
  }
}

// 散点图配置
export interface ScatterChartConfig extends CommonConfig {
  symbolSize: number | ((value: any) => number)
  showSymbol: boolean
  smooth: boolean
  connectNulls: boolean
  emphasis: {
    focus: 'none' | 'self' | 'series'
    scale: boolean
  }
}

// 地图配置
export interface MapChartConfig extends CommonConfig {
  map: string
  roam: boolean | 'scale' | 'move'
  zoom: number
  center?: [number, number]
  aspectScale: number
  boundingCoords?: [[number, number], [number, number]]
  layoutCenter?: [string, string]
  layoutSize?: string | number
}

// 热力图配置
export interface HeatmapChartConfig extends CommonConfig {
  xAxis: {
    type: 'category' | 'value'
    data?: string[]
  }
  yAxis: {
    type: 'category' | 'value'
    data?: string[]
  }
  visualMap: {
    min: number
    max: number
    calculable: boolean
    orient: 'horizontal' | 'vertical'
    left?: string | number
    bottom?: string | number
  }
}

// 环形图配置
export interface DonutChartConfig extends PieChartConfig {
  innerRadius: number
  outerRadius: number
  roseType?: false | 'radius' | 'area'
}

// 多层环形图配置
export interface MultiDonutChartConfig extends CommonConfig {
  series: Array<{
    name: string
    innerRadius: number
    outerRadius: number
    data: Array<{ name: string; value: number }>
  }>
}

// 复合饼图配置
export interface CompositePieChartConfig extends CommonConfig {
  series: Array<{
    name: string
    type: 'pie'
    radius: [number, number] | number
    center: [string, string]
    data: Array<{ name: string; value: number }>
  }>
}

// 堆积图表配置
export interface StackedChartConfig extends CommonConfig {
  stack: string
  stackStrategy: 'samesign' | 'all' | 'positive' | 'negative'
  percentage: boolean
}

// 任务关系配置
export interface TaskRelationConfig {
  type: 'business' | 'temporary'
  enabled: boolean
  relationshipType: 'dependency' | 'sequence' | 'parallel' | 'conditional'
  showLabels: boolean
  nodeStyle: {
    size: number
    color: string
    borderColor: string
    borderWidth: number
  }
  edgeStyle: {
    color: string
    width: number
    type: 'solid' | 'dashed' | 'dotted'
  }
}

// 扩展的图表配置接口
export interface ExtendedChartConfig extends ChartConfig {
  radarChart: RadarChartConfig
  scatterChart: ScatterChartConfig
  mapChart: MapChartConfig
  heatmapChart: HeatmapChartConfig
  donutChart: DonutChartConfig
  multiDonutChart: MultiDonutChartConfig
  compositePieChart: CompositePieChartConfig
  stackedLineChart: StackedChartConfig & LineChartConfig
  stackedBarChart: StackedChartConfig & BarChartConfig
  taskRelation: TaskRelationConfig
}

// 图表类型定义
export type ExtendedChartType =
  // 基础类型
  | 'bar-chart'
  | 'line-chart'
  | 'pie-chart'
  | 'radar-chart'
  | 'scatter-chart'
  | 'map-chart'
  | 'heatmap'
  // 高级类型
  | 'donut-chart'
  | 'multi-donut-chart'
  | 'composite-pie-chart'
  | 'multi-pie-chart'
  | '3d-pie-chart'
  | 'smooth-scatter-chart'
  | 'stacked-line-chart'
  | 'percentage-stacked-line-chart'
  | 'data-point-line-chart'
  | 'stacked-bar-chart'
  | 'percentage-stacked-bar-chart'

// 图表数据接口
export interface ExtendedChartData {
  radarChart?: RadarChartData
  scatterChart?: ScatterChartData
  mapChart?: MapChartData
  heatmapChart?: HeatmapChartData
  donutChart?: DonutChartData
  multiDonutChart?: MultiDonutChartData
  compositePieChart?: CompositePieChartData
}

// 雷达图数据
export interface RadarChartData {
  indicator: Array<{
    name: string
    max: number
    min?: number
  }>
  series: Array<{
    name: string
    data: number[]
    color?: string
  }>
}

// 散点图数据
export interface ScatterChartData {
  xAxis: {
    type: 'value' | 'category'
    data?: string[]
  }
  yAxis: {
    type: 'value' | 'category'
    data?: string[]
  }
  series: Array<{
    name: string
    data: Array<[number, number] | { value: [number, number]; name?: string }>
    color?: string
  }>
}

// 地图数据
export interface MapChartData {
  mapName: string
  series: Array<{
    name: string
    data: Array<{
      name: string
      value: number
      itemStyle?: {
        color?: string
      }
    }>
  }>
}

// 热力图数据
export interface HeatmapChartData {
  xAxis: string[]
  yAxis: string[]
  data: Array<[number, number, number]> // [x, y, value]
}

// 环形图数据
export interface DonutChartData extends PieChartData {
  // 继承饼图数据结构
}

// 多层环形图数据
export interface MultiDonutChartData {
  series: Array<{
    name: string
    innerRadius: number
    outerRadius: number
    data: Array<{
      name: string
      value: number
      itemStyle?: {
        color?: string
      }
    }>
  }>
}

// 复合饼图数据
export interface CompositePieChartData {
  series: Array<{
    name: string
    data: Array<{
      name: string
      value: number
      itemStyle?: {
        color?: string
      }
    }>
  }>
}

// ==================== 仪表板保存相关类型 ====================

// 仪表板保存配置
export interface DashboardSaveConfig {
  taskId: string
  charts: ChartSaveData[]
  layout: LayoutConfig
  metadata: DashboardMetadata
  version: string
}

// 图表保存数据
export interface ChartSaveData {
  id: string
  type: ExtendedChartType
  title: string
  position: ChartPosition
  config: any
  data?: any
  visible: boolean
  createdAt: string
  updatedAt: string
}

// 图表位置信息
export interface ChartPosition {
  x: number
  y: number
  width: number
  height: number
  zIndex?: number
}

// 布局配置
export interface LayoutConfig {
  type: 'grid' | 'free' | 'auto'
  columns?: number
  rows?: number
  gap?: number
  padding?: number
}

// 仪表板元数据
export interface DashboardMetadata {
  name?: string
  description?: string
  tags?: string[]
  createdBy?: string
  createdAt: string
  updatedAt: string
  version: string
}

// 保存请求数据
export interface SaveDashboardRequest {
  config: DashboardSaveConfig
  options?: SaveOptions
}

// 保存选项
export interface SaveOptions {
  overwrite?: boolean
  backup?: boolean
  validate?: boolean
}

// 保存响应数据
export interface SaveDashboardResponse {
  success: boolean
  configId?: string
  message?: string
  errors?: string[]
  timestamp: string
}

// 加载响应数据
export interface LoadDashboardResponse {
  success: boolean
  config?: DashboardSaveConfig
  message?: string
  errors?: string[]
  timestamp: string
}
