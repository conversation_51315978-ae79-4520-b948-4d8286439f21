/**
 * 图表工厂类
 * 负责创建和配置不同类型的图表
 */

import type {
  ExtendedChartType,
  DashboardData,
  ChartConfig,
  BarChartData,
  LineChartData,
  PieChartData,
  RadarChartData,
  ScatterChartData,
  MapChartData,
  HeatmapChartData,
  DonutChartData,
  MultiDonutChartData,
  CompositePieChartData
} from '../types/dashboard.types'
import { getChartTypeById } from '../config/chartTypes.config'
import { generateChartDataByType, generateTaskRelatedData } from '../services/mockDataService'

export class ChartFactory {
  /**
   * 创建图表数据
   */
  static createChartData(
    type: ExtendedChartType,
    dashboardData: DashboardData | null
  ): any {
    try {
      console.log('开始生成图表数据，类型:', type)

      // 使用新的模拟数据服务生成图表数据
      const mockData = generateChartDataByType(type)
      if (!mockData) {
        throw new Error(`无法为图表类型 ${type} 生成数据`)
      }

      const taskData = generateTaskRelatedData()

      // 合并任务相关数据，为图表提供更丰富的上下文
      const result = {
        ...mockData,
        taskContext: taskData,
        metadata: {
          chartType: type,
          generatedAt: new Date(),
          dataSource: 'mock'
        }
      }

      console.log('图表数据生成成功:', type, result)
      return result
    } catch (error) {
      console.error('生成图表数据失败:', error)

      // 返回基础的默认数据结构
      return this.createFallbackData(type)
    }
  }

  /**
   * 创建后备数据（当主要数据生成失败时使用）
   */
  static createFallbackData(type: ExtendedChartType): any {
    console.log('使用后备数据生成器，类型:', type)

    switch (type) {
      case 'bar-chart':
      case 'stacked-bar-chart':
      case 'percentage-stacked-bar-chart':
        return {
          categories: ['类别1', '类别2', '类别3'],
          series: [{
            name: '数据系列',
            data: [10, 20, 30],
            color: '#409EFF'
          }]
        }

      case 'line-chart':
      case 'stacked-line-chart':
      case 'percentage-stacked-line-chart':
        return {
          xAxis: ['1月', '2月', '3月'],
          series: [{
            name: '数据趋势',
            data: [10, 20, 15],
            color: '#67C23A'
          }]
        }

      case 'pie-chart':
      case 'donut-chart':
        return {
          series: [{
            name: '数据分布',
            data: [
              { name: '类别A', value: 30 },
              { name: '类别B', value: 70 }
            ]
          }]
        }

      default:
        return {
          message: '暂无数据',
          type: type,
          timestamp: new Date()
        }
    }
  }

  /**
   * 创建图表配置
   */
  static createChartConfig(
    type: ExtendedChartType,
    baseConfig: ChartConfig
  ): any {
    const chartTypeInfo = getChartTypeById(type)
    const defaultConfig = chartTypeInfo?.defaultConfig || {}

    switch (type) {
      case 'bar-chart':
      case 'stacked-bar-chart':
      case 'percentage-stacked-bar-chart':
        return { ...baseConfig.barChart, ...defaultConfig }

      case 'line-chart':
      case 'stacked-line-chart':
      case 'percentage-stacked-line-chart':
        return { ...baseConfig.lineChart, ...defaultConfig }

      case 'pie-chart':
      case 'donut-chart':
        return { ...baseConfig.pieChart, ...defaultConfig }

      default:
        return { ...baseConfig.common, ...defaultConfig }
    }
  }

  /**
   * 创建柱状图数据
   */
  private static createBarChartData(dashboardData: DashboardData): BarChartData | null {
    return dashboardData.chartData?.barChart || {
      categories: ['类别A', '类别B', '类别C', '类别D'],
      series: [{
        name: '数据系列',
        data: [120, 200, 150, 80],
        color: '#409EFF'
      }]
    }
  }

  /**
   * 创建折线图数据
   */
  private static createLineChartData(dashboardData: DashboardData): LineChartData | null {
    return dashboardData.chartData?.lineChart || {
      xAxis: ['1月', '2月', '3月', '4月', '5月', '6月'],
      series: [{
        name: '数据趋势',
        data: [120, 132, 101, 134, 90, 230],
        color: '#67C23A'
      }]
    }
  }

  /**
   * 创建饼图数据
   */
  private static createPieChartData(dashboardData: DashboardData): PieChartData | null {
    return dashboardData.chartData?.pieChart || {
      series: [{
        name: '数据分布',
        data: [
          { name: '类别A', value: 335 },
          { name: '类别B', value: 310 },
          { name: '类别C', value: 234 },
          { name: '类别D', value: 135 }
        ]
      }]
    }
  }

  /**
   * 创建环形图数据
   */
  private static createDonutChartData(dashboardData: DashboardData): DonutChartData | null {
    return this.createPieChartData(dashboardData)
  }

  /**
   * 创建多层环形图数据
   */
  private static createMultiDonutChartData(dashboardData: DashboardData): MultiDonutChartData {
    return {
      series: [
        {
          name: '内层数据',
          innerRadius: 20,
          outerRadius: 60,
          data: [
            { name: '内层A', value: 200 },
            { name: '内层B', value: 300 }
          ]
        },
        {
          name: '外层数据',
          innerRadius: 70,
          outerRadius: 100,
          data: [
            { name: '外层A', value: 150 },
            { name: '外层B', value: 180 },
            { name: '外层C', value: 120 },
            { name: '外层D', value: 200 }
          ]
        }
      ]
    }
  }

  /**
   * 创建复合饼图数据
   */
  private static createCompositePieChartData(dashboardData: DashboardData): CompositePieChartData {
    return {
      series: [
        {
          name: '左侧饼图',
          data: [
            { name: '类别1', value: 100 },
            { name: '类别2', value: 200 }
          ]
        },
        {
          name: '右侧饼图',
          data: [
            { name: '类别3', value: 150 },
            { name: '类别4', value: 250 }
          ]
        }
      ]
    }
  }

  /**
   * 创建雷达图数据
   */
  private static createRadarChartData(dashboardData: DashboardData): RadarChartData {
    return {
      indicator: [
        { name: '指标1', max: 100 },
        { name: '指标2', max: 100 },
        { name: '指标3', max: 100 },
        { name: '指标4', max: 100 },
        { name: '指标5', max: 100 }
      ],
      series: [{
        name: '数据系列',
        data: [80, 90, 70, 85, 75],
        color: '#E6A23C'
      }]
    }
  }

  /**
   * 创建散点图数据
   */
  private static createScatterChartData(dashboardData: DashboardData): ScatterChartData {
    return {
      xAxis: { type: 'value' },
      yAxis: { type: 'value' },
      series: [{
        name: '散点数据',
        data: [
          [10, 20], [15, 25], [20, 30], [25, 35], [30, 40],
          [35, 45], [40, 50], [45, 55], [50, 60], [55, 65]
        ],
        color: '#F56C6C'
      }]
    }
  }

  /**
   * 创建地图数据
   */
  private static createMapChartData(dashboardData: DashboardData): MapChartData {
    return {
      mapName: 'china',
      series: [{
        name: '地区数据',
        data: [
          { name: '北京', value: 100 },
          { name: '上海', value: 200 },
          { name: '广东', value: 300 },
          { name: '浙江', value: 150 }
        ]
      }]
    }
  }

  /**
   * 创建热力图数据
   */
  private static createHeatmapChartData(dashboardData: DashboardData): HeatmapChartData {
    return {
      xAxis: ['周一', '周二', '周三', '周四', '周五'],
      yAxis: ['上午', '下午', '晚上'],
      data: [
        [0, 0, 5], [0, 1, 1], [0, 2, 0],
        [1, 0, 1], [1, 1, 9], [1, 2, 3],
        [2, 0, 2], [2, 1, 7], [2, 2, 8],
        [3, 0, 3], [3, 1, 6], [3, 2, 4],
        [4, 0, 4], [4, 1, 2], [4, 2, 6]
      ]
    }
  }

  /**
   * 验证图表类型是否支持
   */
  static isChartTypeSupported(type: ExtendedChartType): boolean {
    const supportedTypes = [
      'bar-chart', 'line-chart', 'pie-chart', 'radar-chart', 'scatter-chart',
      'map-chart', 'heatmap', 'donut-chart', 'multi-donut-chart', 'composite-pie-chart',
      'stacked-line-chart', 'stacked-bar-chart', 'percentage-stacked-line-chart', 'percentage-stacked-bar-chart',
      'multi-pie-chart', '3d-pie-chart', 'smooth-scatter-chart', 'data-point-line-chart'
    ]
    const isSupported = supportedTypes.includes(type)
    console.log('检查图表类型支持:', type, '支持:', isSupported)
    return isSupported
  }

  /**
   * 获取图表类型的显示名称
   */
  static getChartTypeName(type: ExtendedChartType): string {
    const chartTypeInfo = getChartTypeById(type)
    return chartTypeInfo?.name || '未知图表'
  }
}
