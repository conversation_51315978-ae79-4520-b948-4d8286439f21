/**
 * 仪表板配置验证和清理工具
 */

import type { 
  DashboardSaveConfig, 
  ChartSaveData, 
  ExtendedChartType 
} from '../types/dashboard.types'
import { allChartTypes } from '../config/chartTypes.config'

// 验证结果接口
export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// 清理结果接口
export interface CleanupResult {
  config: DashboardSaveConfig
  cleaned: boolean
  changes: string[]
}

/**
 * 验证仪表板配置
 */
export function validateDashboardConfig(config: any): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  // 基础结构验证
  if (!config || typeof config !== 'object') {
    errors.push('配置数据必须是一个对象')
    return { isValid: false, errors, warnings }
  }

  // 必需字段验证
  if (!config.taskId || typeof config.taskId !== 'string') {
    errors.push('taskId 字段是必需的且必须是字符串')
  }

  if (!config.charts || !Array.isArray(config.charts)) {
    errors.push('charts 字段是必需的且必须是数组')
  }

  if (!config.version || typeof config.version !== 'string') {
    errors.push('version 字段是必需的且必须是字符串')
  }

  // 图表数据验证
  if (config.charts && Array.isArray(config.charts)) {
    config.charts.forEach((chart: any, index: number) => {
      const chartErrors = validateChartData(chart, index)
      errors.push(...chartErrors.errors)
      warnings.push(...chartErrors.warnings)
    })
  }

  // 布局配置验证
  if (config.layout) {
    const layoutErrors = validateLayoutConfig(config.layout)
    errors.push(...layoutErrors.errors)
    warnings.push(...layoutErrors.warnings)
  }

  // 元数据验证
  if (config.metadata) {
    const metadataErrors = validateMetadata(config.metadata)
    errors.push(...metadataErrors.errors)
    warnings.push(...metadataErrors.warnings)
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * 验证单个图表数据
 */
function validateChartData(chart: any, index: number): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []
  const prefix = `图表[${index}]`

  // 必需字段验证
  if (!chart.id || typeof chart.id !== 'string') {
    errors.push(`${prefix}: id 字段是必需的且必须是字符串`)
  }

  if (!chart.type || typeof chart.type !== 'string') {
    errors.push(`${prefix}: type 字段是必需的且必须是字符串`)
  } else {
    // 验证图表类型是否支持
    const supportedTypes = allChartTypes.map(t => t.id)
    if (!supportedTypes.includes(chart.type as ExtendedChartType)) {
      warnings.push(`${prefix}: 图表类型 '${chart.type}' 可能不被支持`)
    }
  }

  if (!chart.title || typeof chart.title !== 'string') {
    errors.push(`${prefix}: title 字段是必需的且必须是字符串`)
  }

  // 位置信息验证
  if (!chart.position || typeof chart.position !== 'object') {
    errors.push(`${prefix}: position 字段是必需的且必须是对象`)
  } else {
    const pos = chart.position
    if (typeof pos.x !== 'number' || pos.x < 0) {
      errors.push(`${prefix}: position.x 必须是非负数`)
    }
    if (typeof pos.y !== 'number' || pos.y < 0) {
      errors.push(`${prefix}: position.y 必须是非负数`)
    }
    if (typeof pos.width !== 'number' || pos.width <= 0) {
      errors.push(`${prefix}: position.width 必须是正数`)
    }
    if (typeof pos.height !== 'number' || pos.height <= 0) {
      errors.push(`${prefix}: position.height 必须是正数`)
    }
  }

  // 可见性验证
  if (typeof chart.visible !== 'boolean') {
    warnings.push(`${prefix}: visible 字段应该是布尔值，将使用默认值 true`)
  }

  // 时间戳验证
  if (chart.createdAt && !isValidISOString(chart.createdAt)) {
    warnings.push(`${prefix}: createdAt 不是有效的 ISO 时间字符串`)
  }

  if (chart.updatedAt && !isValidISOString(chart.updatedAt)) {
    warnings.push(`${prefix}: updatedAt 不是有效的 ISO 时间字符串`)
  }

  return { isValid: errors.length === 0, errors, warnings }
}

/**
 * 验证布局配置
 */
function validateLayoutConfig(layout: any): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  if (layout.type && !['grid', 'free', 'auto'].includes(layout.type)) {
    warnings.push('布局类型应该是 grid、free 或 auto 之一')
  }

  if (layout.columns && (typeof layout.columns !== 'number' || layout.columns <= 0)) {
    warnings.push('columns 应该是正整数')
  }

  if (layout.rows && (typeof layout.rows !== 'number' || layout.rows <= 0)) {
    warnings.push('rows 应该是正整数')
  }

  if (layout.gap && (typeof layout.gap !== 'number' || layout.gap < 0)) {
    warnings.push('gap 应该是非负数')
  }

  if (layout.padding && (typeof layout.padding !== 'number' || layout.padding < 0)) {
    warnings.push('padding 应该是非负数')
  }

  return { isValid: errors.length === 0, errors, warnings }
}

/**
 * 验证元数据
 */
function validateMetadata(metadata: any): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  if (metadata.createdAt && !isValidISOString(metadata.createdAt)) {
    warnings.push('metadata.createdAt 不是有效的 ISO 时间字符串')
  }

  if (metadata.updatedAt && !isValidISOString(metadata.updatedAt)) {
    warnings.push('metadata.updatedAt 不是有效的 ISO 时间字符串')
  }

  if (metadata.version && typeof metadata.version !== 'string') {
    warnings.push('metadata.version 应该是字符串')
  }

  if (metadata.tags && !Array.isArray(metadata.tags)) {
    warnings.push('metadata.tags 应该是数组')
  }

  return { isValid: errors.length === 0, errors, warnings }
}

/**
 * 清理和修复配置数据
 */
export function cleanupDashboardConfig(config: any): CleanupResult {
  const changes: string[] = []
  let cleaned = false

  // 创建配置副本
  const cleanedConfig = JSON.parse(JSON.stringify(config))

  // 清理图表数据
  if (cleanedConfig.charts && Array.isArray(cleanedConfig.charts)) {
    cleanedConfig.charts = cleanedConfig.charts.map((chart: any, index: number) => {
      const cleanedChart = { ...chart }

      // 修复可见性字段
      if (typeof cleanedChart.visible !== 'boolean') {
        cleanedChart.visible = true
        changes.push(`图表[${index}]: 修复 visible 字段为 true`)
        cleaned = true
      }

      // 修复时间戳
      if (!cleanedChart.createdAt || !isValidISOString(cleanedChart.createdAt)) {
        cleanedChart.createdAt = new Date().toISOString()
        changes.push(`图表[${index}]: 修复 createdAt 时间戳`)
        cleaned = true
      }

      if (!cleanedChart.updatedAt || !isValidISOString(cleanedChart.updatedAt)) {
        cleanedChart.updatedAt = new Date().toISOString()
        changes.push(`图表[${index}]: 修复 updatedAt 时间戳`)
        cleaned = true
      }

      // 确保位置信息为正数
      if (cleanedChart.position) {
        if (cleanedChart.position.x < 0) {
          cleanedChart.position.x = 0
          changes.push(`图表[${index}]: 修复 position.x 为 0`)
          cleaned = true
        }
        if (cleanedChart.position.y < 0) {
          cleanedChart.position.y = 0
          changes.push(`图表[${index}]: 修复 position.y 为 0`)
          cleaned = true
        }
        if (cleanedChart.position.width <= 0) {
          cleanedChart.position.width = 400
          changes.push(`图表[${index}]: 修复 position.width 为 400`)
          cleaned = true
        }
        if (cleanedChart.position.height <= 0) {
          cleanedChart.position.height = 300
          changes.push(`图表[${index}]: 修复 position.height 为 300`)
          cleaned = true
        }
      }

      return cleanedChart
    })
  }

  // 确保元数据存在
  if (!cleanedConfig.metadata) {
    cleanedConfig.metadata = {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: '1.0.0'
    }
    changes.push('添加缺失的元数据')
    cleaned = true
  }

  // 确保版本信息存在
  if (!cleanedConfig.version) {
    cleanedConfig.version = '1.0.0'
    changes.push('添加缺失的版本信息')
    cleaned = true
  }

  return {
    config: cleanedConfig,
    cleaned,
    changes
  }
}

/**
 * 检查是否为有效的 ISO 时间字符串
 */
function isValidISOString(dateString: string): boolean {
  try {
    const date = new Date(dateString)
    return date.toISOString() === dateString
  } catch {
    return false
  }
}

/**
 * 安全地解析 JSON 配置
 */
export function safeParseConfig(configString: string): any {
  try {
    return JSON.parse(configString)
  } catch (error) {
    console.error('配置解析失败:', error)
    return null
  }
}

/**
 * 安全地序列化配置
 */
export function safeStringifyConfig(config: any): string | null {
  try {
    return JSON.stringify(config, null, 2)
  } catch (error) {
    console.error('配置序列化失败:', error)
    return null
  }
}
