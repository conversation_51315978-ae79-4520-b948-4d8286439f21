<!-- 任务数据可视化仪表板主页面 -->
<script setup lang="ts" name="TaskDataVisualizationDashboard">
import {ref, onMounted, onUnmounted, nextTick} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {ElMessage, ElMessageBox} from 'element-plus'
import {ArrowLeft} from '@element-plus/icons-vue'
import NavigationSidebar from './components/NavigationSidebar.vue'
import ChartDisplayArea from './components/ChartDisplayArea.vue'
import {useDashboardStore} from './stores/useDashboardStore'
import type {OverviewCard, TableRow, DataPoint, ExtendedChartType} from './types/dashboard.types'

// 路由和参数
const route = useRoute()
const router = useRouter()
const taskId = route.params.id as string

// 使用仪表板store
const dashboardStore = useDashboardStore()

// 响应式状态
const sidebarCollapsed = ref(false)

// 组件引用
const chartDisplayAreaRef = ref()

// 返回上一页
const handleBack = () => {
	router.back()
}

// 处理图表添加
const handleAddChart = (chartType: ExtendedChartType) => {
	console.log('添加图表:', chartType)
	// 调用 ChartDisplayArea 的添加图表方法
	if (chartDisplayAreaRef.value) {
		chartDisplayAreaRef.value.handleAddChart(chartType)
	}
}

// 处理侧边栏折叠
const handleToggleCollapse = () => {
	sidebarCollapsed.value = !sidebarCollapsed.value
}

// 处理数据刷新
const handleRefresh = () => {
	dashboardStore.refreshData()
}

// 处理卡片点击
const handleCardClick = (card: OverviewCard) => {
	ElMessage.info(`点击了卡片: ${card.title}`)
}

// 处理表格行点击
const handleRowClick = (row: TableRow) => {
	ElMessage.info(`点击了行: ${row.taskName}`)
}

// 处理数据点点击
const handleDataPointClick = (point: DataPoint) => {
	ElMessage.info(`点击了数据点: ${point.name} - ${point.value}`)
}

// 处理导出
const handleExport = () => {
	const data = dashboardStore.exportData()
	if (data) {
		const blob = new Blob([data], {type: 'application/json'})
		const url = URL.createObjectURL(blob)
		const a = document.createElement('a')
		a.href = url
		a.download = `dashboard-data-${taskId}-${new Date().toISOString().split('T')[0]}.json`
		a.click()
		URL.revokeObjectURL(url)
		ElMessage.success('数据导出成功')
	}
}

// 处理图表添加成功
const handleChartAdded = (chartId: string) => {
	console.log('图表添加成功:', chartId)
	ElMessage.success('图表添加成功')
}

// 处理图表删除
const handleChartRemoved = (chartId: string) => {
	console.log('图表已删除:', chartId)
	ElMessage.success('图表已删除')
}

// 手动清理配置
const handleClearConfig = async () => {
	try {
		await ElMessageBox.confirm('确定要清理所有保存的图表配置吗？此操作不可恢复。', '确认清理', {
			type: 'warning',
		})

		const configKey = `dashboard-config-${taskId}`
		localStorage.removeItem(configKey)

		// 重新加载页面以应用更改
		location.reload()
	} catch {
		// 用户取消操作
	}
}

// 页面初始化
onMounted(async () => {
	console.log('任务数据可视化仪表板初始化', taskId)
	await dashboardStore.initialize()

	// 等待多个tick确保组件完全挂载和初始化
	await nextTick()
	await nextTick()

	// 延迟加载保存的仪表板配置，确保所有组件都已准备就绪
	setTimeout(async () => {
		await loadSavedDashboardConfig()
	}, 100)
})

// 验证配置数据的有效性
const validateDashboardConfig = (config: any): boolean => {
	if (!config || typeof config !== 'object') {
		console.warn('配置数据无效：不是对象')
		return false
	}

	if (!config.taskId || !config.charts || !Array.isArray(config.charts)) {
		console.warn('配置数据无效：缺少必需字段')
		return false
	}

	// 检查图表数据
	for (const chart of config.charts) {
		if (!chart.id || !chart.type || !chart.title) {
			console.warn('发现无效图表配置：', chart)
			return false
		}
	}

	return true
}

// 清理无效的本地存储配置
const cleanupInvalidConfigs = async () => {
	try {
		const configKey = `dashboard-config-${taskId}`
		const existingConfig = localStorage.getItem(configKey)

		if (existingConfig) {
			try {
				const parsedConfig = JSON.parse(existingConfig)
				if (!validateDashboardConfig(parsedConfig)) {
					console.log('发现无效配置，正在清理...')
					localStorage.removeItem(configKey)
					ElMessage.info('已清理无效的图表配置')
				}
			} catch (parseError) {
				console.warn('配置解析失败，清理损坏的配置')
				localStorage.removeItem(configKey)
			}
		}
	} catch (error) {
		console.error('清理配置时发生错误:', error)
	}
}

// 加载保存的仪表板配置
const loadSavedDashboardConfig = async () => {
	if (!taskId) {
		console.warn('taskId为空，无法加载配置')
		return
	}

	try {
		console.log('开始加载仪表板配置，taskId:', taskId)

		// 首先清理无效配置
		await cleanupInvalidConfigs()

		const savedConfig = await dashboardStore.loadDashboardConfig(taskId)
		console.log('从本地存储加载的配置:', savedConfig)

		if (savedConfig && validateDashboardConfig(savedConfig)) {
			if (!chartDisplayAreaRef.value) {
				console.warn('ChartDisplayArea组件引用不存在，等待组件挂载...')
				// 等待组件挂载
				await new Promise((resolve) => setTimeout(resolve, 200))
			}

			if (chartDisplayAreaRef.value) {
				console.log('开始导入配置到图表管理器...')
				// 导入配置到图表管理器
				const success = await chartDisplayAreaRef.value.importDashboardConfig(savedConfig)
				if (success) {
					console.log('仪表板配置加载成功')
					ElMessage.success('已恢复保存的图表配置')
				} else {
					console.warn('仪表板配置导入失败，将显示空白仪表板')
					ElMessage.warning('图表配置恢复失败，已重置为空白仪表板')
				}
			} else {
				console.error('ChartDisplayArea组件引用仍然不存在')
				ElMessage.error('组件初始化失败，无法恢复图表配置')
			}
		} else {
			console.log('没有找到有效的保存配置，将显示空白仪表板')
		}
	} catch (error) {
		console.error('加载仪表板配置失败:', error)
		ElMessage.error('加载图表配置时发生错误，已重置为空白仪表板')

		// 发生错误时清理可能损坏的配置
		try {
			const configKey = `dashboard-config-${taskId}`
			localStorage.removeItem(configKey)
		} catch (cleanupError) {
			console.error('清理损坏配置失败:', cleanupError)
		}
	}
}

onUnmounted(() => {
	console.log('任务数据可视化仪表板销毁')
	dashboardStore.destroy()
})
</script>

<template>
	<div class="dashboard-container">
		<!-- 页面头部 -->
		<div class="dashboard-header">
			<div class="header-left">
				<el-button type="text" :icon="ArrowLeft" @click="handleBack" class="back-button">
					返回
				</el-button>
				<h1 class="page-title">任务关联数据可视化</h1>
				<div class="task-info">
					<span class="task-id">任务ID: {{ taskId }}</span>
				</div>
			</div>
			<div class="header-right">
				<el-button
					type="warning"
					size="small"
					@click="handleClearConfig"
					title="清理无效的图表配置"
				>
					清理配置
				</el-button>

				<el-button
					type="primary"
					size="small"
					@click="handleExport"
					:loading="dashboardStore.loading"
				>
					导出数据
				</el-button>
			</div>
		</div>

		<!-- 主要内容区域 -->
		<div class="dashboard-content">
			<!-- 侧边栏 -->
			<NavigationSidebar
				:collapsed="sidebarCollapsed"
				@add-chart="handleAddChart"
				@toggle-collapse="handleToggleCollapse"
				class="dashboard-sidebar"
			/>

			<!-- 主显示区域 -->
			<ChartDisplayArea
				ref="chartDisplayAreaRef"
				:data="dashboardStore.dashboardData"
				:config="dashboardStore.chartConfig"
				:loading="dashboardStore.loading"
				@refresh="handleRefresh"
				@card-click="handleCardClick"
				@row-click="handleRowClick"
				@data-point-click="handleDataPointClick"
				@export="handleExport"
				@chart-added="handleChartAdded"
				@chart-removed="handleChartRemoved"
				class="dashboard-main"
			/>
		</div>

		<!-- 错误提示 -->
		<el-alert
			v-if="dashboardStore.error"
			:title="dashboardStore.error.message"
			type="error"
			:closable="true"
			@close="dashboardStore.clearError"
			class="error-alert"
		/>
	</div>
</template>

<style lang="scss" scoped>
.dashboard-container {
	height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: #f5f7fa;
	position: relative;
}

.dashboard-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px 24px;
	background: #409eff;
	border-bottom: 1px solid #337ecc;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	z-index: 100;

	.header-left {
		display: flex;
		align-items: center;
		gap: 16px;

		.back-button {
			font-size: 14px;
			color: rgba(255, 255, 255, 0.8);

			&:hover {
				color: white;
			}
		}

		.page-title {
			margin: 0;
			font-size: 20px;
			font-weight: 600;
			color: white;
		}

		.task-info {
			.task-id {
				font-size: 12px;
				color: rgba(255, 255, 255, 0.8);
				background: rgba(255, 255, 255, 0.1);
				padding: 4px 8px;
				border-radius: 4px;
			}
		}
	}

	.header-right {
		display: flex;
		align-items: center;
		gap: 12px;

		.settings-button {
			color: #606266;

			&:hover {
				color: #409eff;
			}
		}
	}
}

.dashboard-content {
	flex: 1;
	display: flex;
	min-height: 0;
	overflow: hidden;

	.dashboard-sidebar {
		flex-shrink: 0;
		transition: width 0.3s ease;
	}

	.dashboard-main {
		flex: 1;
		min-width: 0;
		transition: margin-left 0.3s ease;
	}
}

.error-alert {
	position: fixed;
	top: 80px;
	right: 20px;
	z-index: 2000;
	max-width: 400px;
}

// 响应式设计
@media (max-width: 768px) {
	.dashboard-header {
		flex-direction: column;
		gap: 12px;
		align-items: stretch;
		padding: 12px 16px;

		.header-left {
			justify-content: space-between;

			.page-title {
				font-size: 18px;
			}

			.task-info {
				.task-id {
					font-size: 11px;
				}
			}
		}

		.header-right {
			justify-content: flex-end;
		}
	}

	.dashboard-content {
		flex-direction: column;

		.dashboard-sidebar {
			order: 2;
			height: auto;
		}

		.dashboard-main {
			order: 1;
			flex: 1;
		}
	}

	.error-alert {
		top: 120px;
		right: 16px;
		left: 16px;
		max-width: none;
	}
}
</style>
