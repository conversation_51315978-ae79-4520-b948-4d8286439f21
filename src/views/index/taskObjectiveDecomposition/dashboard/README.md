# 任务数据可视化仪表板

## 功能概述

任务数据可视化仪表板是一个纯前端演示功能，用于展示任务相关数据的多维度可视化分析。该功能将模拟数据存储在浏览器本地环境中，提供数据概览、表格展示、图表分析等多种数据展示方式，帮助用户直观地了解任务执行情况和数据分布。

## 主要功能

1. **数据概览**：显示5个关键指标的概览卡片，包括总任务数、已完成任务、待处理任务等
2. **表格视图**：以表格形式展示详细的任务数据，支持排序、筛选和分页
3. **柱状图分析**：通过柱状图展示不同类别数据的对比分析
4. **趋势分析**：通过折线图展示数据随时间的变化趋势
5. **分布分析**：通过饼图展示数据的构成比例
6. **配置面板**：支持自定义图表的显示参数，如主题、动画效果、图例显示等
7. **本地存储**：所有数据和配置都存储在浏览器本地环境中，支持导入导出

## 技术栈

- **前端框架**：Vue 3.5.13 + Composition API + TypeScript
- **UI组件库**：Element Plus 2.8.8
- **图表库**：ECharts 5.5.1
- **状态管理**：Pinia 2.2.6
- **本地存储**：localStorage

## 目录结构

```
dashboard/
├── components/            # 组件目录
│   ├── BarChart.vue       # 柱状图组件
│   ├── ChartDisplayArea.vue # 图表显示区域组件
│   ├── ConfigPanel.vue    # 配置面板组件
│   ├── DataOverviewCards.vue # 数据概览卡片组件
│   ├── DataTable.vue      # 数据表格组件
│   ├── LineChart.vue      # 折线图组件
│   ├── NavigationSidebar.vue # 导航侧边栏组件
│   ├── OverviewCard.vue   # 概览卡片组件
│   └── PieChart.vue       # 饼图组件
├── composables/           # 自定义Hooks目录
│   ├── useECharts.ts      # ECharts Hook
│   ├── useLocalStorage.ts # 本地存储 Hook
│   └── useDashboard.ts    # 仪表板数据管理 Hook
├── services/              # 服务目录
│   ├── mockDataService.ts # 数据模拟服务
│   └── storageService.ts  # 本地存储管理服务
├── stores/                # 状态管理目录
│   └── useDashboardStore.ts # 仪表板状态管理 Store
├── types/                 # 类型定义目录
│   └── dashboard.types.ts # 仪表板类型定义
├── [id].vue               # 仪表板主页面
└── README.md              # 说明文档
```

## 使用方法

### 访问仪表板

1. 在任务详情页面，点击"任务数据可视化"按钮进入仪表板
2. 也可以直接访问URL：`/taskObjectiveDecomposition/dashboard/{taskId}`
3. 可以添加`?free=true`参数跳过权限验证，如：`/taskObjectiveDecomposition/dashboard/1?free=true`

### 导航和视图切换

1. 使用左侧导航菜单切换不同的视图（概览、表格、柱状图、折线图、饼图）
2. 点击导航菜单底部的"设置"按钮或页面右上角的"配置"按钮打开配置面板

### 数据交互

1. 点击概览卡片可以查看相关详细信息
2. 在表格视图中可以点击行查看详细信息
3. 在图表视图中可以点击数据点查看详细信息
4. 可以通过右上角的"导出数据"按钮导出当前数据

### 配置自定义

1. 在配置面板中可以自定义图表的显示参数
2. 支持导入导出配置，方便在不同环境中复用
3. 可以随时重置配置回到默认状态

## 数据模型

### 概览卡片数据

```typescript
interface OverviewCard {
  id: string
  title: string
  value: number
  unit?: string
  trend?: {
    direction: 'up' | 'down' | 'stable'
    percentage: number
  }
  icon?: string
  color?: string
}
```

### 表格数据

```typescript
interface TableData {
  columns: TableColumn[]
  rows: TableRow[]
  pagination: PaginationInfo
}

interface TableColumn {
  key: string
  label: string
  type: 'text' | 'number' | 'date' | 'status'
  sortable?: boolean
  filterable?: boolean
  width?: number
}

interface TableRow {
  id: string
  [key: string]: any
}
```

### 图表数据

```typescript
interface ChartData {
  barChart: BarChartData
  lineChart: LineChartData
  pieChart: PieChartData
}
```

## 本地存储

仪表板使用localStorage存储以下数据：

1. **仪表板数据**：存储在`task_dashboard_data`键下
2. **图表配置**：存储在`task_dashboard_config`键下
3. **最后更新时间**：存储在`task_dashboard_last_update`键下

数据会在24小时后过期，并在存储空间不足时自动清理。

## 性能优化

1. **按需加载**：图表组件按需加载，减少初始加载时间
2. **数据缓存**：使用本地存储缓存数据，减少重复请求
3. **虚拟滚动**：表格视图使用虚拟滚动，提高大数据量下的性能
4. **懒加载**：图表在视图切换时才进行渲染，减少不必要的计算

## 错误处理

仪表板实现了完善的错误处理机制：

1. **数据加载错误**：显示友好的错误提示，提供重试机制
2. **图表渲染错误**：降级到简单的数据展示，记录错误日志
3. **配置保存错误**：提示用户保存失败，保留当前配置状态
4. **存储空间不足**：自动清理旧数据，提示用户存储状态

## 注意事项

1. 该功能是纯前端演示，所有数据都是模拟生成的
2. 数据存储在浏览器本地，清除浏览器缓存会导致数据丢失
3. 不同浏览器之间的数据不会同步
4. 建议使用现代浏览器（Chrome、Firefox、Edge等）访问，以获得最佳体验
