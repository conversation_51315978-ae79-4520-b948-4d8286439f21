<!-- 数据表格组件 -->
<script setup lang="ts" name="DataTable">
import { ref, computed, watch } from 'vue'
import { Search, Download, Refresh } from '@element-plus/icons-vue'
import type { TableData, TableColumn, TableRow } from '../types/dashboard.types'

interface Props {
  data: TableData | null
  loading?: boolean
  height?: string | number
  compact?: boolean
  showHeader?: boolean
}

interface Emits {
  (e: 'refresh'): void
  (e: 'export'): void
  (e: 'row-click', row: TableRow): void
  (e: 'selection-change', selection: TableRow[]): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  height: 400,
  compact: false,
  showHeader: true
})

const emit = defineEmits<Emits>()

// 表格状态
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const sortField = ref('')
const sortOrder = ref<'ascending' | 'descending' | ''>('')
const selectedRows = ref<TableRow[]>([])

// 计算属性
const tableData = computed(() => {
  if (!props.data) return []
  
  let filteredData = props.data.rows

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filteredData = filteredData.filter(row => {
      return Object.values(row).some(value => 
        String(value).toLowerCase().includes(keyword)
      )
    })
  }

  // 排序
  if (sortField.value && sortOrder.value) {
    filteredData = [...filteredData].sort((a, b) => {
      const aValue = a[sortField.value]
      const bValue = b[sortField.value]
      
      let result = 0
      if (aValue < bValue) result = -1
      else if (aValue > bValue) result = 1
      
      return sortOrder.value === 'ascending' ? result : -result
    })
  }

  return filteredData
})

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return tableData.value.slice(start, end)
})

const totalCount = computed(() => tableData.value.length)

const columns = computed(() => props.data?.columns || [])

// 处理排序
const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  sortField.value = prop
  sortOrder.value = order as 'ascending' | 'descending' | ''
}

// 处理分页
const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

// 处理行点击
const handleRowClick = (row: TableRow) => {
  emit('row-click', row)
}

// 处理选择变化
const handleSelectionChange = (selection: TableRow[]) => {
  selectedRows.value = selection
  emit('selection-change', selection)
}

// 处理刷新
const handleRefresh = () => {
  emit('refresh')
}

// 处理导出
const handleExport = () => {
  emit('export')
}

// 清空搜索
const clearSearch = () => {
  searchKeyword.value = ''
}

// 格式化单元格值
const formatCellValue = (row: TableRow, column: TableColumn) => {
  const value = row[column.key]
  
  switch (column.type) {
    case 'date':
      return value ? new Date(value).toLocaleDateString() : '-'
    case 'number':
      return typeof value === 'number' ? value.toLocaleString() : value
    case 'status':
      return value || '-'
    default:
      return value || '-'
  }
}

// 获取状态标签类型
const getStatusType = (status: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const statusMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    '已完成': 'success',
    '进行中': 'primary',
    '待开始': 'warning',
    '已暂停': 'info',
    '已取消': 'danger'
  }
  return statusMap[status] || 'info'
}

// 监听数据变化重置分页
watch(() => props.data, () => {
  currentPage.value = 1
})
</script>

<template>
  <div class="data-table" :class="{ 'compact': compact }">
    <!-- 表格工具栏 -->
    <div v-if="showHeader" class="table-toolbar">
      <div class="toolbar-left">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索..."
          :prefix-icon="Search"
          clearable
          style="width: 300px"
          @clear="clearSearch"
        />
      </div>
      <div class="toolbar-right">
        <el-button
          type="text"
          :icon="Refresh"
          :loading="loading"
          @click="handleRefresh"
        >
          刷新
        </el-button>
        <el-button
          type="text"
          :icon="Download"
          @click="handleExport"
        >
          导出
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-table
      :data="paginatedData"
      :height="height"
      v-loading="loading"
      @sort-change="handleSortChange"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
      stripe
      border
      class="dashboard-table"
    >
      <!-- 选择列 -->
      <el-table-column type="selection" width="55" />

      <!-- 数据列 -->
      <el-table-column
        v-for="column in columns"
        :key="column.key"
        :prop="column.key"
        :label="column.label"
        :width="column.width"
        :sortable="column.sortable"
        :show-overflow-tooltip="true"
      >
        <template #default="{ row }">
          <!-- 状态列特殊处理 -->
          <template v-if="column.type === 'status'">
            <el-tag
              :type="getStatusType(row[column.key])"
              size="small"
            >
              {{ formatCellValue(row, column) }}
            </el-tag>
          </template>
          
          <!-- 进度列特殊处理 -->
          <template v-else-if="column.key === 'progress'">
            <div class="progress-cell">
              <el-progress
                :percentage="row[column.key]"
                :stroke-width="6"
                :show-text="false"
                class="progress-bar"
              />
              <span class="progress-text">{{ row[column.key] }}%</span>
            </div>
          </template>
          
          <!-- 普通列 -->
          <template v-else>
            {{ formatCellValue(row, column) }}
          </template>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <div class="table-pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.data-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;

  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .dashboard-table {
    .progress-cell {
      display: flex;
      align-items: center;
      gap: 6px;
      width: 100%;
      max-width: 120px;

      .progress-bar {
        flex: 1;
        min-width: 50px;
        max-width: 80px;
      }

      .progress-text {
        font-size: 12px;
        color: #606266;
        white-space: nowrap;
        min-width: 35px;
        text-align: right;
      }
    }
  }

  .table-pagination {
    padding: 16px 20px;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #f0f0f0;
  }

  // Compact模式样式
  &.compact {
    .table-toolbar {
      padding: 8px 12px;

      .toolbar-left {
        .el-input {
          width: 200px !important;
        }
      }
    }

    .dashboard-table {
      font-size: 12px;

      :deep(.el-table__cell) {
        padding: 6px 8px;
      }
    }

    .table-pagination {
      padding: 8px 12px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .data-table {
    .table-toolbar {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .toolbar-left {
        justify-content: center;

        .el-input {
          width: 100% !important;
        }
      }

      .toolbar-right {
        justify-content: center;
      }
    }

    .table-pagination {
      padding: 12px;
      justify-content: center;

      :deep(.el-pagination) {
        flex-wrap: wrap;
        justify-content: center;
      }
    }
  }
}


</style>
