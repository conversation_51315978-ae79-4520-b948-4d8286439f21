<!-- 地图组件 -->
<script setup lang="ts" name="MapChart">
import { computed, watch, nextTick } from 'vue'
import { useECharts, getDefaultChartOption, mergeChartOption } from '../composables/useECharts'
import type { MapChartData, MapChartConfig, ChartEvents } from '../types/dashboard.types'
import type { EChartsOption } from 'echarts'

interface Props {
  data: MapChartData | null
  config?: Partial<MapChartConfig>
  loading?: boolean
  height?: string | number
  events?: ChartEvents
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  height: 400
})

// 使用ECharts Hook
const { chartRef, chartInstance, setOption, showLoading, hideLoading } = useECharts({
  autoResize: true
})

// 默认配置
const defaultConfig: MapChartConfig = {
  theme: 'light',
  animation: true,
  responsive: true,
  showLegend: true,
  showTooltip: true,
  map: 'china',
  roam: true,
  zoom: 1,
  aspectScale: 0.75
}

// 合并配置
const chartConfig = computed(() => ({
  ...defaultConfig,
  ...props.config
}))

// 生成图表选项
const chartOption = computed((): EChartsOption => {
  if (!props.data || !props.data.series.length) return {}

  const config = chartConfig.value
  const baseOption = getDefaultChartOption()

  // 计算数据范围用于视觉映射
  const allValues = props.data.series.flatMap(s => s.data.map(d => d.value))
  const minValue = Math.min(...allValues)
  const maxValue = Math.max(...allValues)

  const option: EChartsOption = {
    title: {
      show: false
    },
    tooltip: {
      show: config.showTooltip,
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.name}<br/>${params.seriesName}: ${params.value || '暂无数据'}`
      }
    },
    visualMap: {
      min: minValue,
      max: maxValue,
      left: 'left',
      top: 'bottom',
      text: ['高', '低'],
      calculable: true,
      inRange: {
        color: ['#e0f3ff', '#006edd']
      }
    },
    series: props.data.series.map((series) => ({
      name: series.name,
      type: 'map',
      map: props.data!.mapName,
      roam: config.roam,
      zoom: config.zoom,
      center: config.center,
      aspectScale: config.aspectScale,
      boundingCoords: config.boundingCoords,
      layoutCenter: config.layoutCenter,
      layoutSize: config.layoutSize,
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 1,
        areaColor: '#f3f3f3'
      },
      emphasis: {
        itemStyle: {
          areaColor: '#389BB7',
          borderWidth: 2,
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        },
        label: {
          show: true,
          color: '#fff'
        }
      },
      select: {
        itemStyle: {
          areaColor: '#389BB7'
        },
        label: {
          show: true,
          color: '#fff'
        }
      },
      data: series.data.map(item => ({
        name: item.name,
        value: item.value,
        itemStyle: item.itemStyle || {}
      }))
    })),
    animation: config.animation,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }

  return mergeChartOption(baseOption, option)
})

// 监听加载状态
watch(() => props.loading, (loading) => {
  if (loading) {
    showLoading()
  } else {
    hideLoading()
  }
}, { immediate: true })

// 监听数据变化
watch(() => props.data, () => {
  if (chartInstance.value && props.data) {
    setOption(chartOption.value)
  }
}, { deep: true })

// 监听配置变化
watch(() => props.config, () => {
  if (chartInstance.value && props.data) {
    setOption(chartOption.value)
  }
}, { deep: true })

// 初始化图表
watch(
  chartInstance,
  async (instance) => {
    if (instance && props.data) {
      await nextTick()
      setOption(chartOption.value)
      
      // 绑定事件
      if (props.events) {
        if (props.events.onDataPointClick) {
          instance.on('click', (params: any) => {
            props.events!.onDataPointClick!({
              name: params.name,
              value: params.value,
              category: params.seriesName
            })
          })
        }
        
        if (props.events.onLegendClick) {
          instance.on('legendselectchanged', (params: any) => {
            props.events!.onLegendClick!(params.name)
          })
        }
        
        if (props.events.onChartReady) {
          props.events.onChartReady(instance)
        }
      }
    }
  },
  { immediate: true }
)
</script>

<template>
  <div class="map-chart">
    <div 
      ref="chartRef" 
      class="chart-container"
      :style="{ height: typeof height === 'number' ? `${height}px` : height }"
    />
    
    <!-- 空状态 -->
    <div v-if="!data || !data.series.length" class="empty-state">
      <el-empty description="暂无数据" />
    </div>
    
    <!-- 地图说明 -->
    <div v-if="data && data.series.length" class="map-info">
      <div class="info-item">
        <span class="info-label">地图:</span>
        <span class="info-value">{{ data.mapName }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">数据系列:</span>
        <span class="info-value">{{ data.series.length }}个</span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.map-chart {
  width: 100%;
  height: 100%;
  position: relative;

  .chart-container {
    width: 100%;
    min-height: 300px;
  }

  .empty-state {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
  }

  .map-info {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    padding: 8px 12px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-size: 12px;

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        color: #666;
        margin-right: 8px;
      }

      .info-value {
        color: #333;
        font-weight: 500;
      }
    }
  }
}
</style>
