<!-- 导航侧边栏组件 -->
<script setup lang="ts" name="NavigationSidebar">
import { computed } from 'vue'
import {
  DataBoard,
  Grid,
  Histogram,
  TrendCharts,
  PieChart,
  Setting,
  Expand,
  Fold,
  Plus,
  Monitor,
  Location,
  Sunny
} from '@element-plus/icons-vue'
import type { ExtendedChartType } from '../types/dashboard.types'
import { getChartTypesByCategory } from '../config/chartTypes.config'

interface Props {
  collapsed?: boolean
}

interface Emits {
  (e: 'add-chart', chartType: ExtendedChartType): void
  (e: 'toggle-collapse'): void
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false
})

const emit = defineEmits<Emits>()

// 基础图表类型
const basicChartTypes = computed(() => getChartTypesByCategory('basic'))

// 高级图表类型
const advancedChartTypes = computed(() => getChartTypesByCategory('advanced'))

// 计算属性用于模板访问
const collapsed = computed(() => props.collapsed)

// 图标映射
const iconMap = {
  DataBoard,
  Grid,
  Histogram,
  TrendCharts,
  PieChart,
  Setting,
  Expand,
  Fold,
  Plus,
  Monitor,
  Location,
  Sunny
}

// 获取图标组件
const getIconComponent = (iconName: any) => {
  if (typeof iconName === 'string') {
    return iconMap[iconName as keyof typeof iconMap] || DataBoard
  }
  return iconName || DataBoard
}

// 处理图表添加
const handleAddChart = (chartType: ExtendedChartType) => {
  emit('add-chart', chartType)
}

// 处理折叠切换
const handleToggleCollapse = () => {
  emit('toggle-collapse')
}


</script>

<template>
  <div 
    class="navigation-sidebar"
    :class="{ 'collapsed': collapsed }"
  >
    <!-- 侧边栏头部 -->
    <div class="sidebar-header">
      <div class="header-content">
        <div class="logo-area">
          <el-icon :size="24" class="logo-icon">
            <DataBoard />
          </el-icon>
          <span v-if="!collapsed" class="logo-text">数据仪表板</span>
        </div>
        <el-button
          type="text"
          @click="handleToggleCollapse"
          class="collapse-btn"
          size="small"
        >
          <el-icon>
            <component :is="collapsed ? 'Expand' : 'Fold'" />
          </el-icon>
        </el-button>
      </div>
    </div>

    <!-- 导航菜单 -->
    <div class="sidebar-menu">
      <!-- 基础图表 -->
      <div class="menu-section">
        <div v-if="!collapsed" class="section-title">基础图表</div>
        <div class="menu-items">
          <div
            v-for="chartType in basicChartTypes"
            :key="chartType.id"
            class="menu-item"
            :class="{ 'disabled': chartType.disabled }"
            @click="handleAddChart(chartType.id)"
            :title="chartType.description"
          >
            <el-icon :size="18" class="item-icon">
              <component :is="getIconComponent(chartType.icon)" />
            </el-icon>
            <span v-if="!collapsed" class="item-label">{{ chartType.name }}</span>
            <el-icon v-if="!collapsed" :size="14" class="add-icon">
              <Plus />
            </el-icon>
          </div>
        </div>
      </div>

      <!-- 高级图表 -->
      <div class="menu-section">
        <div v-if="!collapsed" class="section-title">高级图表</div>
        <div class="menu-items">
          <div
            v-for="chartType in advancedChartTypes"
            :key="chartType.id"
            class="menu-item"
            :class="{ 'disabled': chartType.disabled }"
            @click="handleAddChart(chartType.id)"
            :title="chartType.description"
          >
            <el-icon :size="18" class="item-icon">
              <component :is="getIconComponent(chartType.icon)" />
            </el-icon>
            <span v-if="!collapsed" class="item-label">{{ chartType.name }}</span>
            <el-icon v-if="!collapsed" :size="14" class="add-icon">
              <Plus />
            </el-icon>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<style lang="scss" scoped>
.navigation-sidebar {
  width: 240px;
  height: 100%;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  position: relative;

  &.collapsed {
    width: 64px;

    .sidebar-header {
      .header-content {
        justify-content: center;

        .logo-area {
          .logo-text {
            display: none;
          }
        }

        .collapse-btn {
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }

    .sidebar-menu {
      .section-title {
        display: none;
      }

      .menu-item {
        justify-content: center;
        padding: 12px;

        .item-label {
          display: none;
        }

        .active-indicator {
          right: 0;
          width: 3px;
          height: 100%;
          border-radius: 0 2px 2px 0;
        }
      }
    }

    .sidebar-footer {
      .settings-item {
        justify-content: center;
        padding: 12px;

        .item-label {
          display: none;
        }
      }
    }
  }

  .sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .logo-area {
        display: flex;
        align-items: center;
        gap: 12px;

        .logo-icon {
          color: #409EFF;
          flex-shrink: 0;
        }

        .logo-text {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          white-space: nowrap;
        }
      }

      .collapse-btn {
        color: #909399;
        
        &:hover {
          color: #409EFF;
        }
      }
    }
  }

  .sidebar-menu {
    flex: 1;
    padding: 16px 0;
    overflow-y: auto;

    .menu-section {
      .section-title {
        padding: 0 16px 8px;
        font-size: 12px;
        color: #909399;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .menu-items {
        .menu-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px 16px;
          margin: 2px 8px;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
          position: relative;
          user-select: none;

          &:hover {
            background-color: #f5f7fa;
            color: #409EFF;

            .item-icon {
              color: #409EFF;
            }

            .add-icon {
              opacity: 1;
              color: #409EFF;
            }
          }

          &.disabled {
            opacity: 0.5;
            cursor: not-allowed;

            &:hover {
              background-color: transparent;
              color: inherit;

              .item-icon {
                color: inherit;
              }

              .add-icon {
                opacity: 0;
              }
            }
          }

          .item-icon {
            color: #606266;
            flex-shrink: 0;
            transition: color 0.2s ease;
          }

          .item-label {
            font-size: 14px;
            color: inherit;
            white-space: nowrap;
            flex: 1;
          }

          .add-icon {
            color: #909399;
            opacity: 0;
            transition: all 0.2s ease;
            flex-shrink: 0;
          }
        }
      }
    }
  }

  .sidebar-footer {
    padding: 16px 0;
    border-top: 1px solid #f0f0f0;

    .settings-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      margin: 0 8px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      color: #606266;

      &:hover {
        background-color: #f5f7fa;
        color: #409EFF;

        .item-icon {
          color: #409EFF;
        }
      }

      .item-icon {
        color: #909399;
        flex-shrink: 0;
        transition: color 0.2s ease;
      }

      .item-label {
        font-size: 14px;
        white-space: nowrap;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .navigation-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

    &.collapsed {
      transform: translateX(-100%);
    }
  }
}


</style>
