<!-- 图表类型选择器组件 -->
<script setup lang="ts" name="ChartTypeSelector">
import { ref, computed } from 'vue'
import {
  getChartTypesByCategory,
  type ChartTypeInfo
} from '../config/chartTypes.config'
import type { ExtendedChartType, TaskRelationConfig } from '../types/dashboard.types'
import TaskRelationConfigDialog from './TaskRelationConfig.vue'

interface Props {
  selectedType?: ExtendedChartType | string
  category?: 'all' | 'basic' | 'advanced'
}

interface Emits {
  (e: 'select', chartType: ExtendedChartType, options?: any): void
  (e: 'configure', chartType: ChartTypeInfo, relationType: 'business' | 'temporary'): void
  (e: 'update:category', category: 'all' | 'basic' | 'advanced'): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  selectedType: '',
  category: 'all'
})

const emit = defineEmits<Emits>()

// 任务关系配置状态
const relationConfigVisible = ref(false)
const currentChartType = ref<ChartTypeInfo | null>(null)
const currentRelationType = ref<'business' | 'temporary'>('business')

// 计算显示的图表类型
const displayedChartTypes = computed(() => {
  return getChartTypesByCategory(props.category || 'all')
})

// 计算属性用于模板访问
const category = computed(() => props.category)
const selectedType = computed(() => props.selectedType)

// 处理图表选择
const handleChartSelect = (chartType: ChartTypeInfo) => {
  emit('select', chartType.id)
}

// 处理任务关系配置
const handleRelationConfigure = (chartType: ChartTypeInfo, relationType: 'business' | 'temporary') => {
  currentChartType.value = chartType
  currentRelationType.value = relationType
  relationConfigVisible.value = true
  emit('configure', chartType, relationType)
}

// 处理任务关系配置保存
const handleRelationConfigSave = (config: TaskRelationConfig) => {
  console.log('保存任务关系配置:', config)

  // 配置保存后，触发图表选择事件
  if (currentChartType.value) {
    emit('select', currentChartType.value.id, {
      relationType: currentRelationType.value,
      relationConfig: config
    })
  }

  // 关闭配置对话框
  relationConfigVisible.value = false
}

// 处理分类变化
const handleCategoryChange = (value: string | number | boolean | undefined) => {
  if (value) {
    emit('update:category', value as 'all' | 'basic' | 'advanced')
  }
}

// 处理任务关系配置预览
const handleRelationConfigPreview = (config: TaskRelationConfig) => {
  console.log('预览任务关系配置:', config)
  // TODO: 实现配置预览逻辑
}

// 关闭任务关系配置
const handleRelationConfigClose = () => {
  relationConfigVisible.value = false
  currentChartType.value = null
}
</script>

<template>
  <div class="chart-type-selector">
    <!-- 分类标签 -->
    <div class="category-tabs">
      <el-radio-group
        :model-value="category"
        @update:model-value="handleCategoryChange"
        size="small"
      >
        <el-radio-button label="all">全部</el-radio-button>
        <el-radio-button label="basic">基础图表</el-radio-button>
        <el-radio-button label="advanced">高级图表</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 图表类型网格 -->
    <div class="chart-types-grid">
      <div
        v-for="chartType in displayedChartTypes"
        :key="chartType.id"
        class="chart-type-item"
        :class="{ 'selected': selectedType === chartType.id }"
        @click="handleChartSelect(chartType)"
      >
        <!-- 图表图标和名称 -->
        <div class="chart-icon">
          <el-icon :size="24">
            <component :is="chartType.icon" />
          </el-icon>
        </div>
        <div class="chart-name">{{ chartType.name }}</div>
        <div class="chart-description">{{ chartType.description }}</div>
        
        <!-- 任务关系配置按钮 -->
        <div class="relation-buttons">
          <el-button
            v-if="chartType.supportsBusiness"
            type="primary"
            size="small"
            plain
            @click.stop="handleRelationConfigure(chartType, 'business')"
          >
            业务报表
          </el-button>
          <el-button
            v-if="chartType.supportsTemporary"
            type="success"
            size="small"
            plain
            @click.stop="handleRelationConfigure(chartType, 'temporary')"
          >
            临时报表
          </el-button>
        </div>
      </div>
    </div>

    <!-- 任务关系配置对话框 -->
    <TaskRelationConfigDialog
      :visible="relationConfigVisible"
      :chart-type="currentChartType"
      :relation-type="currentRelationType"
      @close="handleRelationConfigClose"
      @save="handleRelationConfigSave"
      @preview="handleRelationConfigPreview"
    />
  </div>
</template>

<style lang="scss" scoped>
.chart-type-selector {
  .category-tabs {
    margin-bottom: 20px;
    text-align: center;
  }

  .chart-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;

    .chart-type-item {
      background: white;
      border: 2px solid #e4e7ed;
      border-radius: 8px;
      padding: 16px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409EFF;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
        transform: translateY(-2px);
      }

      &.selected {
        border-color: #409EFF;
        background-color: #ecf5ff;
      }

      .chart-icon {
        color: #409EFF;
        margin-bottom: 8px;
      }

      .chart-name {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
      }

      .chart-description {
        font-size: 12px;
        color: #909399;
        margin-bottom: 12px;
        line-height: 1.4;
      }

      .relation-buttons {
        display: flex;
        gap: 8px;
        justify-content: center;
        flex-wrap: wrap;

        .el-button {
          font-size: 11px;
          padding: 4px 8px;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .chart-types-grid {
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 12px;

      .chart-type-item {
        padding: 12px;

        .chart-name {
          font-size: 13px;
        }

        .chart-description {
          font-size: 11px;
        }
      }
    }
  }
}
</style>
