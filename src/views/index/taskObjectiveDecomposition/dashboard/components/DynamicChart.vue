<!-- 动态图表加载器组件 -->
<script setup lang="ts" name="DynamicChart">
import { computed, defineAsyncComponent, toRefs } from 'vue'
import { ElLoading } from 'element-plus'
import type { 
  ExtendedChartType, 
  ChartEvents,
  BaseChartProps
} from '../types/dashboard.types'

interface Props {
  type: ExtendedChartType
  data: any
  config: any
  loading?: boolean
  width?: string | number
  height?: string | number
  events?: ChartEvents
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  width: '100%',
  height: 400
})

// 解构props以便在模板中使用
const { type, data, config, loading, width, height, events } = toRefs(props)

// 动态导入图表组件
const chartComponents = {
  'bar-chart': defineAsyncComponent(() => import('./BarChart.vue')),
  'line-chart': defineAsyncComponent(() => import('./LineChart.vue')),
  'pie-chart': defineAsyncComponent(() => import('./PieChart.vue')),
  'radar-chart': defineAsyncComponent(() => import('./RadarChart.vue')),
  'scatter-chart': defineAsyncComponent(() => import('./ScatterChart.vue')),
  'map-chart': defineAsyncComponent(() => import('./MapChart.vue')),
  'heatmap': defineAsyncComponent(() => import('./HeatmapChart.vue')),
  'donut-chart': defineAsyncComponent(() => import('./DonutChart.vue')),
  'multi-donut-chart': defineAsyncComponent(() => import('./MultiLayerChart.vue')),
  'composite-pie-chart': defineAsyncComponent(() => import('./MultiLayerChart.vue')),
  'multi-pie-chart': defineAsyncComponent(() => import('./MultiLayerChart.vue')),
  '3d-pie-chart': defineAsyncComponent(() => import('./PieChart.vue')),
  'smooth-scatter-chart': defineAsyncComponent(() => import('./ScatterChart.vue')),
  'stacked-line-chart': defineAsyncComponent(() => import('./LineChart.vue')),
  'stacked-bar-chart': defineAsyncComponent(() => import('./BarChart.vue')),
  'percentage-stacked-line-chart': defineAsyncComponent(() => import('./LineChart.vue')),
  'percentage-stacked-bar-chart': defineAsyncComponent(() => import('./BarChart.vue')),
  'data-point-line-chart': defineAsyncComponent(() => import('./LineChart.vue'))
} as const

// 获取图表组件
const chartComponent = computed(() => {
  return chartComponents[props.type] || chartComponents['bar-chart']
})

// 计算样式
const containerStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height
}))

// 处理加载错误
const handleError = (error: Error) => {
  console.error(`Failed to load chart component: ${props.type}`, error)
}
</script>

<template>
  <div class="dynamic-chart" :style="containerStyle">
    <Suspense>
      <template #default>
        <component
          :is="chartComponent"
          :data="data"
          :config="config"
          :loading="loading"
          :height="height"
          :events="events"
        />
      </template>
      
      <template #fallback>
        <div class="chart-loading">
          <el-loading 
            :loading="true" 
            text="加载图表组件中..."
            background="rgba(255, 255, 255, 0.8)"
          >
            <div class="loading-placeholder" :style="containerStyle" />
          </el-loading>
        </div>
      </template>
    </Suspense>

    <!-- 错误边界 -->
    <div v-if="!chartComponent" class="chart-error">
      <el-alert
        title="图表组件加载失败"
        :description="`不支持的图表类型: ${type}`"
        type="error"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.dynamic-chart {
  position: relative;
  width: 100%;
  height: 100%;

  .chart-loading {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    .loading-placeholder {
      background: #f5f7fa;
      border-radius: 8px;
      border: 1px dashed #dcdfe6;
    }
  }

  .chart-error {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
  }
}
</style>
