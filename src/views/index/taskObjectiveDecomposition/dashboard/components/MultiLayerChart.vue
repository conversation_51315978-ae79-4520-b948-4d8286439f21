<!-- 多层图表组件 -->
<script setup lang="ts" name="MultiLayerChart">
import { computed, watch, nextTick } from 'vue'
import { useECharts, getDefaultChartOption, mergeChartOption } from '../composables/useECharts'
import type { MultiDonutChartData, MultiDonutChartConfig, ChartEvents } from '../types/dashboard.types'
import type { EChartsOption } from 'echarts'

interface Props {
  data: MultiDonutChartData | null
  config?: Partial<MultiDonutChartConfig>
  loading?: boolean
  height?: string | number
  events?: ChartEvents
  type?: 'donut' | 'pie' // 图表类型
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  height: 400,
  type: 'donut'
})

// 使用ECharts Hook
const { chartRef, chartInstance, setOption, showLoading, hideLoading, isLoading } = useECharts({
  autoResize: true
})

// 默认配置
const defaultConfig: MultiDonutChartConfig = {
  theme: 'light',
  animation: true,
  responsive: true,
  showLegend: true,
  showTooltip: true,
  series: [
    {
      name: '内层',
      innerRadius: 0,
      outerRadius: 40,
      data: []
    },
    {
      name: '外层',
      innerRadius: 50,
      outerRadius: 80,
      data: []
    }
  ]
}

// 合并配置
const chartConfig = computed(() => ({
  ...defaultConfig,
  ...props.config
}))

// 生成图表选项
const chartOption = computed((): EChartsOption => {
  if (!props.data || !props.data.series || !props.data.series.length) return {}

  const config = chartConfig.value
  const baseOption = getDefaultChartOption()

  // 收集所有图例数据
  const legendData = props.data.series.flatMap(series =>
    series.data?.map(item => item.name) || []
  )

  const option: EChartsOption = {
    title: {
      show: false
    },
    tooltip: {
      show: config.showTooltip,
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.seriesName}<br/>${params.name}: ${params.value}`
      }
    },
    legend: {
      show: config.showLegend,
      orient: 'vertical',
      left: 'left',
      data: legendData
    },
    series: props.data.series.map((seriesData, seriesIndex) => {
      const total = (seriesData.data || []).reduce((sum, item) => sum + item.value, 0)
      
      return {
        name: seriesData.name,
        type: 'pie',
        radius: props.type === 'donut' 
          ? [seriesData.innerRadius, seriesData.outerRadius]
          : seriesData.outerRadius,
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: props.type === 'donut' ? 6 : 0,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          position: seriesIndex === 0 ? 'inner' : 'outside',
          formatter: (params: any) => {
            const percent = ((params.value / total) * 100).toFixed(1)
            return seriesIndex === 0 ? `${percent}%` : `${params.name}\n${percent}%`
          },
          fontSize: seriesIndex === 0 ? 10 : 12
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: {
          show: seriesIndex > 0
        },
        data: (seriesData.data || []).map((item, index) => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.itemStyle?.color || `hsl(${(seriesIndex * 180 + index * 45) % 360}, 70%, ${60 - seriesIndex * 10}%)`
          }
        }))
      }
    }),
    animation: config.animation,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }

  return mergeChartOption(baseOption, option)
})

// 监听加载状态
watch(() => props.loading, (loading) => {
  if (loading) {
    showLoading()
  } else {
    hideLoading()
  }
}, { immediate: true })

// 监听数据变化
watch(() => props.data, () => {
  if (chartInstance.value && props.data) {
    setOption(chartOption.value)
  }
}, { deep: true })

// 监听配置变化
watch(() => props.config, () => {
  if (chartInstance.value && props.data) {
    setOption(chartOption.value)
  }
}, { deep: true })

// 初始化图表
watch(
  chartInstance,
  async (instance) => {
    if (instance && props.data) {
      await nextTick()
      setOption(chartOption.value)
      
      // 绑定事件
      if (props.events) {
        if (props.events.onDataPointClick) {
          instance.on('click', (params: any) => {
            props.events!.onDataPointClick!({
              name: params.name,
              value: params.value,
              category: params.seriesName
            })
          })
        }
        
        if (props.events.onLegendClick) {
          instance.on('legendselectchanged', (params: any) => {
            props.events!.onLegendClick!(params.name)
          })
        }
        
        if (props.events.onChartReady) {
          props.events.onChartReady(instance)
        }
      }
    }
  },
  { immediate: true }
)
</script>

<template>
  <div class="multi-layer-chart">
    <div 
      ref="chartRef" 
      class="chart-container"
      :style="{ height: typeof height === 'number' ? `${height}px` : height }"
    />
    
    <!-- 空状态 -->
    <div v-if="!data || !data.series || !data.series.length" class="empty-state">
      <el-empty description="暂无数据" />
    </div>

    <!-- 图表信息 -->
    <div v-if="data && data.series && data.series.length" class="chart-info">
      <div class="info-title">{{ type === 'donut' ? '多层环形图' : '多层饼图' }}</div>
      <div class="info-stats">
        <div class="stat-item">
          <span class="stat-label">层数:</span>
          <span class="stat-value">{{ data.series.length }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">总数据点:</span>
          <span class="stat-value">{{ data.series.reduce((sum, s) => sum + (s.data?.length || 0), 0) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.multi-layer-chart {
  width: 100%;
  height: 100%;
  position: relative;

  .chart-container {
    width: 100%;
    min-height: 300px;
  }

  .empty-state {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
  }

  .chart-info {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    padding: 12px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-width: 120px;

    .info-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
      text-align: center;
    }

    .info-stats {
      .stat-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 4px;
        font-size: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .stat-label {
          color: #666;
        }

        .stat-value {
          color: #333;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
