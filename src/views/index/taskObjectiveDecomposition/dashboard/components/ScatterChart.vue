<!-- 散点图组件 -->
<script setup lang="ts" name="<PERSON>att<PERSON><PERSON><PERSON>">
import { computed, watch, nextTick } from 'vue'
import { useECharts, getDefaultChartOption, mergeChartOption } from '../composables/useECharts'
import type { ScatterChartData, ScatterChartConfig, ChartEvents } from '../types/dashboard.types'
import type { EChartsOption } from 'echarts'

interface Props {
  data: ScatterChartData | null
  config?: Partial<ScatterChartConfig>
  loading?: boolean
  height?: string | number
  events?: ChartEvents
  smooth?: boolean // 是否为平滑散点图
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  height: 400,
  smooth: false
})

// 使用ECharts Hook
const { chartRef, chartInstance, setOption, showLoading, hideLoading, isLoading } = useECharts({
  autoResize: true
})

// 默认配置
const defaultConfig: ScatterChartConfig = {
  theme: 'light',
  animation: true,
  responsive: true,
  showLegend: true,
  showTooltip: true,
  symbolSize: 8,
  showSymbol: true,
  smooth: false,
  connectNulls: false,
  emphasis: {
    focus: 'series',
    scale: true
  }
}

// 合并配置
const chartConfig = computed(() => ({
  ...defaultConfig,
  ...props.config,
  smooth: props.smooth || defaultConfig.smooth
}))

// 生成图表选项
const chartOption = computed((): EChartsOption => {
  if (!props.data || !props.data.series.length) return {}

  const config = chartConfig.value
  const baseOption = getDefaultChartOption()

  const option: EChartsOption = {
    title: {
      show: false
    },
    tooltip: {
      show: config.showTooltip,
      trigger: 'item',
      formatter: (params: any) => {
        const value = params.value
        if (Array.isArray(value) && value.length >= 2) {
          return `${params.seriesName}<br/>X: ${value[0]}<br/>Y: ${value[1]}`
        }
        return `${params.seriesName}: ${value}`
      }
    },
    legend: {
      show: config.showLegend,
      data: props.data.series.map(s => s.name),
      bottom: 10
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: props.data.xAxis.type,
      data: props.data.xAxis.data,
      name: 'X轴',
      nameLocation: 'middle',
      nameGap: 30,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#e0e6ed'
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#e0e6ed'
        }
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      }
    },
    yAxis: {
      type: props.data.yAxis.type,
      data: props.data.yAxis.data,
      name: 'Y轴',
      nameLocation: 'middle',
      nameGap: 40,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#e0e6ed'
        }
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#e0e6ed'
        }
      },
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      }
    },
    series: props.data.series.map((series, index) => {
      const baseColor = series.color || `hsl(${(index * 60) % 360}, 70%, 60%)`
      
      if (config.smooth) {
        // 平滑散点图 - 使用线图类型
        return {
          name: series.name,
          type: 'line',
          data: series.data,
          smooth: true,
          showSymbol: config.showSymbol,
          symbol: 'circle',
          symbolSize: typeof config.symbolSize === 'function' ? 8 : config.symbolSize,
          lineStyle: {
            color: baseColor,
            width: 2
          },
          itemStyle: {
            color: baseColor
          },
          emphasis: {
            focus: config.emphasis.focus,
            scale: config.emphasis.scale,
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          }
        }
      } else {
        // 普通散点图
        return {
          name: series.name,
          type: 'scatter',
          data: series.data,
          symbolSize: typeof config.symbolSize === 'function'
            ? (value: any) => (config.symbolSize as (value: any) => number)(value)
            : config.symbolSize,
          itemStyle: {
            color: baseColor,
            opacity: 0.8
          },
          emphasis: {
            focus: config.emphasis.focus,
            scale: config.emphasis.scale,
            itemStyle: {
              color: baseColor,
              opacity: 1,
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          }
        }
      }
    }),
    animation: config.animation,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }

  return mergeChartOption(baseOption, option)
})

// 监听加载状态
watch(() => props.loading, (loading) => {
  if (loading) {
    showLoading()
  } else {
    hideLoading()
  }
}, { immediate: true })

// 监听数据变化
watch(() => props.data, () => {
  if (chartInstance.value && props.data) {
    setOption(chartOption.value)
  }
}, { deep: true })

// 监听配置变化
watch(() => props.config, () => {
  if (chartInstance.value && props.data) {
    setOption(chartOption.value)
  }
}, { deep: true })

// 监听smooth属性变化
watch(() => props.smooth, () => {
  if (chartInstance.value && props.data) {
    setOption(chartOption.value)
  }
})

// 初始化图表
watch(
  chartInstance,
  async (instance) => {
    if (instance && props.data) {
      await nextTick()
      setOption(chartOption.value)
      
      // 绑定事件
      if (props.events) {
        if (props.events.onDataPointClick) {
          instance.on('click', (params: any) => {
            props.events!.onDataPointClick!({
              name: params.name,
              value: params.value,
              category: params.seriesName
            })
          })
        }
        
        if (props.events.onLegendClick) {
          instance.on('legendselectchanged', (params: any) => {
            props.events!.onLegendClick!(params.name)
          })
        }
        
        if (props.events.onChartReady) {
          props.events.onChartReady(instance)
        }
      }
    }
  },
  { immediate: true }
)
</script>

<template>
  <div class="scatter-chart">
    <div 
      ref="chartRef" 
      class="chart-container"
      :style="{ height: typeof height === 'number' ? `${height}px` : height }"
    />
    
    <!-- 空状态 -->
    <div v-if="!data || !data.series.length" class="empty-state">
      <el-empty description="暂无数据" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.scatter-chart {
  width: 100%;
  height: 100%;
  position: relative;

  .chart-container {
    width: 100%;
    min-height: 300px;
  }

  .empty-state {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
  }
}
</style>
