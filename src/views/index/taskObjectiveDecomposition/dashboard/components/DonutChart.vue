<!-- 环形图组件 -->
<script setup lang="ts" name="Donut<PERSON>hart">
import { computed, watch, nextTick } from 'vue'
import { useECharts, getDefaultChartOption, mergeChartOption } from '../composables/useECharts'
import type { DonutChartData, DonutChartConfig, ChartEvents } from '../types/dashboard.types'
import type { EChartsOption } from 'echarts'

interface Props {
  data: DonutChartData | null
  config?: Partial<DonutChartConfig>
  loading?: boolean
  height?: string | number
  events?: ChartEvents
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  height: 400
})

// 使用ECharts Hook
const { chartRef, chartInstance, setOption, showLoading, hideLoading, isLoading } = useECharts({
  autoResize: true
})

// 默认配置
const defaultConfig: DonutChartConfig = {
  theme: 'light',
  animation: true,
  responsive: true,
  showLegend: true,
  showTooltip: true,
  showPercentage: true,
  innerRadius: 40,
  outerRadius: 80,
  startAngle: 90,
  roseType: false
}

// 合并配置
const chartConfig = computed(() => ({
  ...defaultConfig,
  ...props.config
}))

// 生成图表选项
const chartOption = computed((): EChartsOption => {
  if (!props.data || !props.data.series.length) return {}

  const config = chartConfig.value
  const baseOption = getDefaultChartOption()
  const series = props.data.series[0] // 环形图通常只有一个系列

  // 计算总值用于百分比计算
  const total = (series.data || []).reduce((sum, item) => sum + (item?.value || 0), 0)

  const option: EChartsOption = {
    title: {
      show: false
    },
    tooltip: {
      show: config.showTooltip,
      trigger: 'item',
      formatter: (params: any) => {
        const percent = ((params.value / total) * 100).toFixed(1)
        return `${params.name}<br/>${params.seriesName}: ${params.value} (${percent}%)`
      }
    },
    legend: {
      show: config.showLegend,
      orient: 'vertical',
      left: 'left',
      data: (series.data || []).filter(item => item?.name).map(item => item.name)
    },
    series: [
      {
        name: series.name,
        type: 'pie',
        radius: [config.innerRadius, config.outerRadius],
        center: ['50%', '50%'],
        startAngle: config.startAngle,
        roseType: config.roseType === false ? undefined : config.roseType,
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: config.showPercentage,
          position: 'outside',
          formatter: (params: any) => {
            const percent = ((params.value / total) * 100).toFixed(1)
            return `${params.name}\n${percent}%`
          }
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: {
          show: config.showPercentage
        },
        data: (series.data || []).filter(item => item?.name && item?.value !== undefined).map((item, index) => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.color || `hsl(${(index * 45) % 360}, 70%, 60%)`
          }
        }))
      }
    ],
    animation: config.animation,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }

  return mergeChartOption(baseOption, option)
})

// 监听加载状态
watch(() => props.loading, (loading) => {
  if (loading) {
    showLoading()
  } else {
    hideLoading()
  }
}, { immediate: true })

// 监听数据变化
watch(() => props.data, () => {
  if (chartInstance.value && props.data) {
    setOption(chartOption.value)
  }
}, { deep: true })

// 监听配置变化
watch(() => props.config, () => {
  if (chartInstance.value && props.data) {
    setOption(chartOption.value)
  }
}, { deep: true })

// 初始化图表
watch(
  chartInstance,
  async (instance) => {
    if (instance && props.data) {
      await nextTick()
      setOption(chartOption.value)
      
      // 绑定事件
      if (props.events) {
        if (props.events.onDataPointClick) {
          instance.on('click', (params: any) => {
            props.events!.onDataPointClick!({
              name: params.name,
              value: params.value,
              category: params.name
            })
          })
        }
        
        if (props.events.onLegendClick) {
          instance.on('legendselectchanged', (params: any) => {
            props.events!.onLegendClick!(params.name)
          })
        }
        
        if (props.events.onChartReady) {
          props.events.onChartReady(instance)
        }
      }
    }
  },
  { immediate: true }
)
</script>

<template>
  <div class="donut-chart">
    <div 
      ref="chartRef" 
      class="chart-container"
      :style="{ height: typeof height === 'number' ? `${height}px` : height }"
    />
    
    <!-- 空状态 -->
    <div v-if="!data || !data.series.length" class="empty-state">
      <el-empty description="暂无数据" />
    </div>
    
    <!-- 中心统计信息 -->
    <div v-if="data && data.series.length" class="center-info">
      <div class="total-value">
        {{ data.series[0].data.reduce((sum, item) => sum + item.value, 0) }}
      </div>
      <div class="total-label">总计</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.donut-chart {
  width: 100%;
  height: 100%;
  position: relative;

  .chart-container {
    width: 100%;
    min-height: 300px;
  }

  .empty-state {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
  }

  .center-info {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    pointer-events: none;

    .total-value {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
      line-height: 1;
    }

    .total-label {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
    }
  }
}
</style>
