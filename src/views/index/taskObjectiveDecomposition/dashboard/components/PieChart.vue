<!-- 饼图组件 -->
<script setup lang="ts" name="<PERSON><PERSON><PERSON>">
import { computed, watch, nextTick } from 'vue'
import { useECharts, getDefaultChartOption, mergeChartOption } from '../composables/useECharts'
import type { PieChartData, PieChartConfig, ChartEvents } from '../types/dashboard.types'
import type { EChartsOption } from 'echarts'

interface Props {
  data: PieChartData | null
  config?: Partial<PieChartConfig>
  loading?: boolean
  height?: string | number
  events?: ChartEvents
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  height: 400
})

// 使用ECharts Hook
const { chartRef, chartInstance, setOption, showLoading, hideLoading, isLoading } = useECharts({
  autoResize: true
})

// 默认配置
const defaultConfig: PieChartConfig = {
  theme: 'light',
  animation: true,
  responsive: true,
  showLegend: true,
  showTooltip: true,
  showPercentage: true,
  innerRadius: 0,
  outerRadius: 80,
  startAngle: 90
}

// 合并配置
const chartConfig = computed(() => ({
  ...defaultConfig,
  ...props.config
}))

// 生成图表选项
const chartOption = computed((): EChartsOption => {
  if (!props.data || !props.data.series.length) return {}

  const config = chartConfig.value
  const baseOption = getDefaultChartOption()
  const series = props.data.series[0] // 饼图通常只有一个系列

  // 计算总值用于百分比计算
  const total = (series.data || []).reduce((sum, item) => sum + (item?.value || 0), 0)

  const option: EChartsOption = {
    title: {
      text: '饼图',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
        color: '#303133'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const percentage = ((params.value / total) * 100).toFixed(1)
        return `${params.name}<br/>${params.marker}${params.value} (${percentage}%)`
      }
    },
    legend: {
      show: config.showLegend,
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      data: (series.data || []).filter(item => item?.name).map(item => item.name),
      textStyle: {
        color: '#606266'
      }
    },
    series: [
      {
        name: series.name,
        type: 'pie',
        radius: config.innerRadius > 0 
          ? [`${config.innerRadius}%`, `${config.outerRadius}%`]
          : `${config.outerRadius}%`,
        center: ['50%', '50%'],
        startAngle: config.startAngle,
        data: (series.data || []).filter(item => item?.name && item?.value !== undefined).map((item, index) => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.color || `hsl(${(index * 45) % 360}, 70%, 60%)`
          },
          label: {
            show: config.showPercentage,
            formatter: (params: any) => {
              const percentage = ((params.value / total) * 100).toFixed(1)
              return `${params.name}\n${percentage}%`
            },
            fontSize: 12,
            color: '#606266'
          },
          labelLine: {
            show: config.showPercentage,
            length: 15,
            length2: 10
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            },
            label: {
              show: true,
              fontSize: 14,
              fontWeight: 'bold'
            }
          }
        })),
        animation: config.animation,
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: (idx: number) => Math.random() * 200
      }
    ]
  }

  return mergeChartOption(baseOption, option)
})

// 监听数据变化更新图表
watch(
  () => [props.data, chartConfig.value],
  () => {
    if (chartInstance.value && props.data) {
      setOption(chartOption.value)
    }
  },
  { deep: true }
)

// 监听加载状态
watch(
  () => props.loading,
  (loading) => {
    if (loading) {
      showLoading({
        text: '加载中...',
        color: '#409EFF'
      })
    } else {
      hideLoading()
    }
  },
  { immediate: true }
)

// 初始化图表
watch(
  chartInstance,
  async (instance) => {
    if (instance && props.data) {
      await nextTick()
      setOption(chartOption.value)
      
      // 绑定事件
      if (props.events) {
        if (props.events.onDataPointClick) {
          instance.on('click', (params: any) => {
            props.events!.onDataPointClick!({
              name: params.name,
              value: params.value,
              category: params.name
            })
          })
        }
        
        if (props.events.onLegendClick) {
          instance.on('legendselectchanged', (params: any) => {
            props.events!.onLegendClick!(params.name)
          })
        }
        
        if (props.events.onChartReady) {
          props.events.onChartReady(instance)
        }
      }
    }
  },
  { immediate: true }
)
</script>

<template>
  <div class="pie-chart">
    <div 
      ref="chartRef" 
      class="chart-container"
      :style="{ height: typeof height === 'number' ? `${height}px` : height }"
    />
    
    <!-- 空状态 -->
    <div v-if="!data && !loading" class="empty-state">
      <el-empty description="暂无数据">
        <template #image>
          <div class="empty-icon">🥧</div>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.pie-chart {
  position: relative;
  width: 100%;
  background: white;
  border-radius: 8px;
  overflow: hidden;

  .chart-container {
    width: 100%;
    min-height: 300px;
  }

  .empty-state {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .pie-chart {
    .chart-container {
      min-height: 250px;
    }
  }
}


</style>
