<!-- 配置面板组件 -->
<script setup lang="ts" name="ConfigPanel">
import { ref, computed, watch } from 'vue'
import { 
  Setting, 
  Close, 
  Refresh,
  Download,
  Upload
} from '@element-plus/icons-vue'
import type { ViewType, ChartConfig } from '../types/dashboard.types'

interface Props {
  visible: boolean
  currentView: ViewType
  config: ChartConfig
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'config-change', config: Partial<ChartConfig>): void
  (e: 'config-save', config: ChartConfig): void
  (e: 'config-reset'): void
  (e: 'export-config'): void
  (e: 'import-config', config: ChartConfig): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 本地配置副本
const localConfig = ref<ChartConfig>({ ...props.config })

// 文件上传引用
const fileInputRef = ref<HTMLInputElement>()

// 计算属性
const panelVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 当前视图的配置
const currentConfig = computed(() => {
  switch (props.currentView) {
    case 'bar-chart':
      return localConfig.value.barChart
    case 'line-chart':
      return localConfig.value.lineChart
    case 'pie-chart':
      return localConfig.value.pieChart
    default:
      return localConfig.value.common
  }
})

// 配置项标题
const configTitles = {
  'overview': '概览配置',
  'table': '表格配置',
  'bar-chart': '柱状图配置',
  'line-chart': '折线图配置',
  'pie-chart': '饼图配置'
}

// 监听外部配置变化
watch(
  () => props.config,
  (newConfig) => {
    localConfig.value = { ...newConfig }
  },
  { deep: true }
)

// 处理配置变化
const handleConfigChange = () => {
  emit('config-change', localConfig.value)
}

// 保存配置
const handleSave = () => {
  emit('config-save', localConfig.value)
}

// 重置配置
const handleReset = () => {
  emit('config-reset')
}

// 导出配置
const handleExport = () => {
  emit('export-config')
}

// 导入配置
const handleImport = () => {
  fileInputRef.value?.click()
}

// 处理文件选择
const handleFileChange = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const config = JSON.parse(e.target?.result as string)
      emit('import-config', config)
    } catch (error) {
      console.error('配置文件格式错误:', error)
    }
  }
  reader.readAsText(file)
}

// 关闭面板
const handleClose = () => {
  panelVisible.value = false
}
</script>

<template>
  <el-drawer
    v-model="panelVisible"
    title="仪表板配置"
    direction="rtl"
    size="400px"
    :before-close="handleClose"
  >
    <template #header>
      <div class="panel-header">
        <div class="header-title">
          <el-icon><Setting /></el-icon>
          <span>{{ configTitles[currentView] }}</span>
        </div>
      </div>
    </template>

    <div class="config-content">
      <!-- 通用配置 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <span class="section-title">通用设置</span>
        </template>
        
        <el-form label-position="top" size="small">
          <el-form-item label="主题">
            <el-radio-group 
              v-model="localConfig.common.theme"
              @change="handleConfigChange"
            >
              <el-radio-button label="light">浅色</el-radio-button>
              <el-radio-button label="dark">深色</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="动画效果">
            <el-switch
              v-model="localConfig.common.animation"
              @change="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="显示图例">
            <el-switch
              v-model="localConfig.common.showLegend"
              @change="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="显示提示框">
            <el-switch
              v-model="localConfig.common.showTooltip"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 柱状图特定配置 -->
      <el-card 
        v-if="currentView === 'bar-chart'" 
        class="config-section" 
        shadow="never"
      >
        <template #header>
          <span class="section-title">柱状图设置</span>
        </template>
        
        <el-form label-position="top" size="small">
          <el-form-item label="方向">
            <el-radio-group 
              v-model="localConfig.barChart.orientation"
              @change="handleConfigChange"
            >
              <el-radio-button label="vertical">垂直</el-radio-button>
              <el-radio-button label="horizontal">水平</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="柱子宽度">
            <el-slider
              v-model="localConfig.barChart.barWidth"
              :min="20"
              :max="80"
              @change="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="显示数据标签">
            <el-switch
              v-model="localConfig.barChart.showDataLabels"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 折线图特定配置 -->
      <el-card 
        v-if="currentView === 'line-chart'" 
        class="config-section" 
        shadow="never"
      >
        <template #header>
          <span class="section-title">折线图设置</span>
        </template>
        
        <el-form label-position="top" size="small">
          <el-form-item label="平滑曲线">
            <el-switch
              v-model="localConfig.lineChart.smooth"
              @change="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="显示数据点">
            <el-switch
              v-model="localConfig.lineChart.showPoints"
              @change="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="显示面积">
            <el-switch
              v-model="localConfig.lineChart.showArea"
              @change="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="线条宽度">
            <el-slider
              v-model="localConfig.lineChart.lineWidth"
              :min="1"
              :max="5"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 饼图特定配置 -->
      <el-card 
        v-if="currentView === 'pie-chart'" 
        class="config-section" 
        shadow="never"
      >
        <template #header>
          <span class="section-title">饼图设置</span>
        </template>
        
        <el-form label-position="top" size="small">
          <el-form-item label="显示百分比">
            <el-switch
              v-model="localConfig.pieChart.showPercentage"
              @change="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="内半径">
            <el-slider
              v-model="localConfig.pieChart.innerRadius"
              :min="0"
              :max="60"
              @change="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="外半径">
            <el-slider
              v-model="localConfig.pieChart.outerRadius"
              :min="40"
              :max="100"
              @change="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="起始角度">
            <el-slider
              v-model="localConfig.pieChart.startAngle"
              :min="0"
              :max="360"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="panel-footer">
        <div class="footer-actions">
          <el-button 
            type="text" 
            :icon="Upload"
            @click="handleImport"
          >
            导入
          </el-button>
          <el-button 
            type="text" 
            :icon="Download"
            @click="handleExport"
          >
            导出
          </el-button>
          <el-button 
            type="text" 
            :icon="Refresh"
            @click="handleReset"
          >
            重置
          </el-button>
        </div>
        <div class="footer-buttons">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </div>
    </template>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      accept=".json"
      style="display: none"
      @change="handleFileChange"
    />
  </el-drawer>
</template>

<style lang="scss" scoped>
.panel-header {
  .header-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.config-content {
  .config-section {
    margin-bottom: 16px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    :deep(.el-card__body) {
      padding: 16px;
    }
  }
}

.panel-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0 0;
  border-top: 1px solid #e4e7ed;

  .footer-actions {
    display: flex;
    gap: 8px;
  }

  .footer-buttons {
    display: flex;
    gap: 12px;
  }
}


</style>
