<!-- 柱状图组件 -->
<script setup lang="ts" name="Bar<PERSON><PERSON>">
import { computed, watch, nextTick } from 'vue'
import { useECharts, getDefaultChartOption, mergeChartOption } from '../composables/useECharts'
import type { BarChartData, BarChartConfig, ChartEvents } from '../types/dashboard.types'
import type { EChartsOption } from 'echarts'

interface Props {
  data: BarChartData | null
  config?: Partial<BarChartConfig>
  loading?: boolean
  height?: string | number
  events?: ChartEvents
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  height: 400
})

// 使用ECharts Hook
const { chartRef, chartInstance, setOption, showLoading, hideLoading, isLoading } = useECharts({
  autoResize: true
})

// 默认配置
const defaultConfig: BarChartConfig = {
  theme: 'light',
  animation: true,
  responsive: true,
  showLegend: true,
  showTooltip: true,
  orientation: 'vertical',
  showDataLabels: false,
  barWidth: 40,
  spacing: 10
}

// 合并配置
const chartConfig = computed(() => ({
  ...defaultConfig,
  ...props.config
}))

// 生成图表选项
const chartOption = computed((): EChartsOption => {
  if (!props.data) return {}

  const config = chartConfig.value
  const baseOption = getDefaultChartOption()

  const option: EChartsOption = {
    title: {
      text: '柱状图',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'normal',
        color: '#303133'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        if (Array.isArray(params)) {
          let result = `${params[0].axisValue}<br/>`
          params.forEach((param: any) => {
            result += `${param.marker}${param.seriesName}: ${param.value}<br/>`
          })
          return result
        }
        return `${params.axisValue}<br/>${params.marker}${params.seriesName}: ${params.value}`
      }
    },
    legend: {
      show: config.showLegend,
      top: 30,
      data: props.data.series.map(s => s.name)
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: config.showLegend ? '15%' : '10%',
      containLabel: true
    },
    xAxis: {
      type: config.orientation === 'vertical' ? 'category' : 'value',
      data: config.orientation === 'vertical' ? props.data.categories : undefined,
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisLabel: {
        color: '#606266',
        fontSize: 12
      }
    },
    yAxis: {
      type: config.orientation === 'vertical' ? 'value' : 'category',
      data: config.orientation === 'horizontal' ? props.data.categories : undefined,
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisLabel: {
        color: '#606266',
        fontSize: 12
      },
      splitLine: {
        lineStyle: {
          color: '#f5f7fa',
          type: 'dashed'
        }
      }
    },
    series: props.data.series.map((series, index) => ({
      name: series.name,
      type: 'bar',
      data: series.data,
      barWidth: config.barWidth,
      itemStyle: {
        color: series.color || `hsl(${(index * 60) % 360}, 70%, 60%)`,
        borderRadius: config.orientation === 'vertical' ? [4, 4, 0, 0] : [0, 4, 4, 0]
      },
      label: {
        show: config.showDataLabels,
        position: config.orientation === 'vertical' ? 'top' : 'right',
        color: '#606266',
        fontSize: 12
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      }
    })),
    animation: config.animation,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }

  return mergeChartOption(baseOption, option)
})

// 监听数据变化更新图表
watch(
  () => [props.data, chartConfig.value],
  () => {
    if (chartInstance.value && props.data) {
      setOption(chartOption.value)
    }
  },
  { deep: true }
)

// 监听加载状态
watch(
  () => props.loading,
  (loading) => {
    if (loading) {
      showLoading({
        text: '加载中...',
        color: '#409EFF'
      })
    } else {
      hideLoading()
    }
  },
  { immediate: true }
)

// 初始化图表
watch(
  chartInstance,
  async (instance) => {
    if (instance && props.data) {
      await nextTick()
      setOption(chartOption.value)
      
      // 绑定事件
      if (props.events) {
        if (props.events.onDataPointClick) {
          instance.on('click', (params: any) => {
            props.events!.onDataPointClick!({
              name: params.name,
              value: params.value,
              category: params.name
            })
          })
        }
        
        if (props.events.onLegendClick) {
          instance.on('legendselectchanged', (params: any) => {
            props.events!.onLegendClick!(params.name)
          })
        }
        
        if (props.events.onChartReady) {
          props.events.onChartReady(instance)
        }
      }
    }
  },
  { immediate: true }
)
</script>

<template>
  <div class="bar-chart">
    <div 
      ref="chartRef" 
      class="chart-container"
      :style="{ height: typeof height === 'number' ? `${height}px` : height }"
    />
    
    <!-- 空状态 -->
    <div v-if="!data && !loading" class="empty-state">
      <el-empty description="暂无数据">
        <template #image>
          <div class="empty-icon">📊</div>
        </template>
      </el-empty>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.bar-chart {
  position: relative;
  width: 100%;
  background: white;
  border-radius: 8px;
  overflow: hidden;

  .chart-container {
    width: 100%;
    min-height: 300px;
  }

  .empty-state {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .bar-chart {
    .chart-container {
      min-height: 250px;
    }
  }
}


</style>
