<!-- 数据概览卡片容器组件 -->
<script setup lang="ts" name="DataOverviewCards">
import { computed } from 'vue'
import OverviewCard from './OverviewCard.vue'
import type { OverviewData, OverviewCard as OverviewCardType } from '../types/dashboard.types'

interface Props {
  data: OverviewData | null
  loading?: boolean
}

interface Emits {
  (e: 'card-click', card: OverviewCardType): void
  (e: 'refresh'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 计算卡片数据
const cards = computed(() => {
  return props.data?.cards || []
})

// 处理卡片点击
const handleCardClick = (card: OverviewCardType) => {
  emit('card-click', card)
}

// 生成骨架屏数据
const skeletonCards = Array.from({ length: 5 }, (_, index) => ({
  id: `skeleton-${index}`,
  title: '',
  value: 0,
  color: '#f0f0f0'
}))
</script>

<template>
  <div class="data-overview-cards">
    <!-- 标题区域 -->
    <div class="cards-header">
      <h2 class="cards-title">数据概览</h2>
      <div class="cards-actions">
        <el-button 
          type="text" 
          size="small"
          :loading="loading"
          @click="$emit('refresh')"
        >
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 卡片网格 -->
    <div class="cards-grid">
      <!-- 正常状态 -->
      <template v-if="!loading && cards.length > 0">
        <OverviewCard
          v-for="card in cards"
          :key="card.id"
          :card="card"
          @click="handleCardClick"
        />
      </template>

      <!-- 加载状态 -->
      <template v-else-if="loading">
        <div
          v-for="skeleton in skeletonCards"
          :key="skeleton.id"
          class="skeleton-card"
        >
          <el-skeleton animated>
            <template #template>
              <div class="skeleton-header">
                <el-skeleton-item variant="circle" style="width: 48px; height: 48px;" />
                <div class="skeleton-info">
                  <el-skeleton-item variant="text" style="width: 80px; height: 14px;" />
                  <el-skeleton-item variant="text" style="width: 60px; height: 28px; margin-top: 8px;" />
                </div>
              </div>
              <div class="skeleton-trend">
                <el-skeleton-item variant="text" style="width: 100px; height: 12px;" />
              </div>
            </template>
          </el-skeleton>
        </div>
      </template>

      <!-- 空状态 -->
      <template v-else>
        <div class="empty-state">
          <el-empty description="暂无数据">
            <template #image>
              <div class="empty-icon">📊</div>
            </template>
            <el-button type="primary" @click="$emit('refresh')">
              重新加载
            </el-button>
          </el-empty>
        </div>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.data-overview-cards {
  .cards-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .cards-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }

    .cards-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    min-height: 120px;

    // 响应式网格
    @media (max-width: 1200px) {
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 16px;
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 12px;
    }

    @media (max-width: 480px) {
      grid-template-columns: 1fr;
      gap: 8px;
    }
  }

  .skeleton-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;

    .skeleton-header {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      margin-bottom: 16px;

      .skeleton-info {
        flex: 1;
      }
    }

    .skeleton-trend {
      margin-top: 16px;
    }
  }

  .empty-state {
    grid-column: 1 / -1;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }
  }
}

// 动画效果
.cards-grid {
  .overview-card {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;

    @for $i from 1 through 5 {
      &:nth-child(#{$i}) {
        animation-delay: #{($i - 1) * 0.1}s;
      }
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}


</style>
