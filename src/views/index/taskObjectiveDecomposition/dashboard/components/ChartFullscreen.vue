<!-- 图表全屏预览组件 -->
<script setup lang="ts" name="ChartFullscreen">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { 
  Close, 
  Download, 
  Setting, 
  FullScreen,
  ZoomIn,
  ZoomOut,
  Refresh
} from '@element-plus/icons-vue'
import DynamicChart from './DynamicChart.vue'
import type { ChartInstance } from '../composables/useChartManager'

interface Props {
  visible: boolean
  chart: ChartInstance | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'configure', chartId: string): void
  (e: 'download', chartId: string): void
  (e: 'refresh', chartId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式状态
const overlayRef = ref<HTMLElement>()
const chartContainerRef = ref<HTMLElement>()
const zoomLevel = ref(1)
const showToolbar = ref(true)

// 计算属性
const fullscreenVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const chartTitle = computed(() => props.chart?.title || '图表预览')

// 缩放控制
const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value + 0.1, 2)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value - 0.1, 0.5)
}

const resetZoom = () => {
  zoomLevel.value = 1
}

// 工具栏操作
const handleConfigure = () => {
  if (props.chart) {
    emit('configure', props.chart.id)
  }
}

const handleDownload = () => {
  if (props.chart) {
    emit('download', props.chart.id)
  }
}

const handleRefresh = () => {
  if (props.chart) {
    emit('refresh', props.chart.id)
  }
}

const handleClose = () => {
  fullscreenVisible.value = false
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (!props.visible) return
  
  switch (event.key) {
    case 'Escape':
      handleClose()
      break
    case '+':
    case '=':
      event.preventDefault()
      zoomIn()
      break
    case '-':
      event.preventDefault()
      zoomOut()
      break
    case '0':
      event.preventDefault()
      resetZoom()
      break
    case 'f':
    case 'F':
      event.preventDefault()
      showToolbar.value = !showToolbar.value
      break
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// 监听可见性变化
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      document.body.style.overflow = 'hidden'
      zoomLevel.value = 1
      showToolbar.value = true
    } else {
      document.body.style.overflow = ''
    }
  }
)
</script>

<template>
  <teleport to="body">
    <transition name="fullscreen-fade">
      <div
        v-if="visible"
        ref="overlayRef"
        class="chart-fullscreen-overlay"
        @click.self="handleClose"
      >
        <!-- 工具栏 -->
        <transition name="toolbar-slide">
          <div v-if="showToolbar" class="fullscreen-toolbar">
            <div class="toolbar-left">
              <h3 class="chart-title">{{ chartTitle }}</h3>
              <div class="zoom-controls">
                <el-button-group size="small">
                  <el-button :icon="ZoomOut" @click="zoomOut" :disabled="zoomLevel <= 0.5" />
                  <el-button @click="resetZoom">{{ Math.round(zoomLevel * 100) }}%</el-button>
                  <el-button :icon="ZoomIn" @click="zoomIn" :disabled="zoomLevel >= 2" />
                </el-button-group>
              </div>
            </div>
            
            <div class="toolbar-right">
              <el-button :icon="Refresh" size="small" @click="handleRefresh">
                刷新
              </el-button>
              <el-button :icon="Setting" size="small" @click="handleConfigure">
                配置
              </el-button>
              <el-button :icon="Download" size="small" @click="handleDownload">
                下载
              </el-button>
              <el-button :icon="Close" size="small" @click="handleClose">
                关闭
              </el-button>
            </div>
          </div>
        </transition>

        <!-- 图表容器 -->
        <div class="fullscreen-content">
          <div
            ref="chartContainerRef"
            class="chart-container"
            :style="{
              transform: `scale(${zoomLevel})`,
              transformOrigin: 'center center'
            }"
          >
            <DynamicChart
              v-if="chart"
              :type="chart.type"
              :data="chart.data"
              :config="chart.config"
              :loading="chart.loading"
              width="100%"
              height="100%"
            />
          </div>
        </div>

        <!-- 快捷键提示 -->
        <div class="keyboard-hints">
          <div class="hint-item">ESC: 关闭</div>
          <div class="hint-item">+/-: 缩放</div>
          <div class="hint-item">0: 重置</div>
          <div class="hint-item">F: 切换工具栏</div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<style lang="scss" scoped>
.chart-fullscreen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  z-index: 9999;
  display: flex;
  flex-direction: column;

  .fullscreen-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 24px;

      .chart-title {
        margin: 0;
        color: white;
        font-size: 18px;
        font-weight: 600;
      }

      .zoom-controls {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .fullscreen-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px;
    overflow: hidden;

    .chart-container {
      width: 90%;
      height: 90%;
      background: white;
      border-radius: 8px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      transition: transform 0.3s ease;
      overflow: hidden;
    }
  }

  .keyboard-hints {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    opacity: 0.7;

    .hint-item {
      color: white;
      font-size: 12px;
      background: rgba(0, 0, 0, 0.5);
      padding: 4px 8px;
      border-radius: 4px;
      backdrop-filter: blur(5px);
    }
  }
}

// 过渡动画
.fullscreen-fade-enter-active,
.fullscreen-fade-leave-active {
  transition: all 0.3s ease;
}

.fullscreen-fade-enter-from,
.fullscreen-fade-leave-to {
  opacity: 0;
}

.toolbar-slide-enter-active,
.toolbar-slide-leave-active {
  transition: all 0.3s ease;
}

.toolbar-slide-enter-from,
.toolbar-slide-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

// 响应式设计
@media (max-width: 768px) {
  .chart-fullscreen-overlay {
    .fullscreen-toolbar {
      flex-direction: column;
      gap: 12px;
      padding: 12px 16px;

      .toolbar-left {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;

        .chart-title {
          font-size: 16px;
        }
      }

      .toolbar-right {
        justify-content: center;
        flex-wrap: wrap;
      }
    }

    .fullscreen-content {
      padding: 16px;

      .chart-container {
        width: 100%;
        height: 100%;
      }
    }

    .keyboard-hints {
      bottom: 10px;
      right: 10px;
      left: 10px;
      flex-direction: row;
      justify-content: center;
      flex-wrap: wrap;
    }
  }
}
</style>
