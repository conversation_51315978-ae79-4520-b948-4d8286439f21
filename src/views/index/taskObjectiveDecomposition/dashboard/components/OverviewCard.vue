<!-- 概览卡片组件 -->
<script setup lang="ts" name="OverviewCard">
import { computed } from 'vue'
import { 
  Document, 
  CircleCheck, 
  Clock, 
  Warning, 
  User,
  ArrowUp,
  ArrowDown,
  Minus
} from '@element-plus/icons-vue'
import type { OverviewCard } from '../types/dashboard.types'

interface Props {
  card: OverviewCard
  loading?: boolean
  clickable?: boolean
}

interface Emits {
  (e: 'click', card: OverviewCard): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  clickable: true
})

const emit = defineEmits<Emits>()

// 图标映射
const iconMap = {
  Document,
  CircleCheck,
  Clock,
  Warning,
  User
}

// 计算图标组件
const iconComponent = computed(() => {
  return iconMap[props.card.icon as keyof typeof iconMap] || Document
})

// 趋势图标
const trendIcon = computed(() => {
  if (!props.card.trend) return null
  
  switch (props.card.trend.direction) {
    case 'up':
      return ArrowUp
    case 'down':
      return ArrowDown
    case 'stable':
    default:
      return Minus
  }
})

// 趋势颜色
const trendColor = computed(() => {
  if (!props.card.trend) return '#909399'
  
  switch (props.card.trend.direction) {
    case 'up':
      return '#67C23A'
    case 'down':
      return '#F56C6C'
    case 'stable':
    default:
      return '#909399'
  }
})

// 处理点击事件
const handleClick = () => {
  if (props.clickable && !props.loading) {
    emit('click', props.card)
  }
}
</script>

<template>
  <div 
    class="overview-card"
    :class="{ 
      'clickable': clickable && !loading,
      'loading': loading 
    }"
    @click="handleClick"
    v-loading="loading"
  >
    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="icon-wrapper" :style="{ backgroundColor: card.color }">
        <el-icon :size="24" class="card-icon">
          <component :is="iconComponent" />
        </el-icon>
      </div>
      <div class="card-info">
        <div class="card-title">{{ card.title }}</div>
        <div class="card-value">
          {{ card.value }}
          <span v-if="card.unit" class="card-unit">{{ card.unit }}</span>
        </div>
      </div>
    </div>

    <!-- 趋势指示器 -->
    <div v-if="card.trend" class="card-trend">
      <el-icon 
        :size="14" 
        :color="trendColor"
        class="trend-icon"
      >
        <component :is="trendIcon" />
      </el-icon>
      <span 
        class="trend-text"
        :style="{ color: trendColor }"
      >
        {{ card.trend.percentage.toFixed(1) }}%
      </span>
      <span class="trend-label">较上期</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.overview-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &.clickable {
    cursor: pointer;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
      transform: translateY(-2px);
      border-color: #409EFF;
    }
  }

  &.loading {
    pointer-events: none;
  }

  .card-header {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 16px;

    .icon-wrapper {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      .card-icon {
        color: white;
      }
    }

    .card-info {
      flex: 1;
      min-width: 0;

      .card-title {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
        line-height: 1.4;
      }

      .card-value {
        font-size: 28px;
        font-weight: 600;
        color: #303133;
        line-height: 1.2;
        display: flex;
        align-items: baseline;
        gap: 4px;

        .card-unit {
          font-size: 14px;
          font-weight: 400;
          color: #909399;
        }
      }
    }
  }

  .card-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;

    .trend-icon {
      flex-shrink: 0;
    }

    .trend-text {
      font-weight: 500;
    }

    .trend-label {
      color: #909399;
    }
  }

  // 加载状态样式
  &.loading {
    .card-header,
    .card-trend {
      opacity: 0.6;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .card-header {
      gap: 12px;
      margin-bottom: 12px;

      .icon-wrapper {
        width: 40px;
        height: 40px;
        border-radius: 10px;

        .card-icon {
          font-size: 20px;
        }
      }

      .card-info {
        .card-title {
          font-size: 13px;
        }

        .card-value {
          font-size: 24px;

          .card-unit {
            font-size: 12px;
          }
        }
      }
    }
  }
}


</style>
