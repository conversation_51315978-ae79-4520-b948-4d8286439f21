<!-- 任务关系配置组件 -->
<script setup lang="ts" name="TaskRelationConfig">
import { ref, computed, watch } from 'vue'
import { Setting, Connection, Link } from '@element-plus/icons-vue'
import type { TaskRelationConfig, ExtendedChartType } from '../types/dashboard.types'
import type { ChartTypeInfo } from '../config/chartTypes.config'
import { defaultTaskRelationConfig } from '../config/chartTypes.config'

interface Props {
  visible: boolean
  chartType: ChartTypeInfo | null
  relationType: 'business' | 'temporary'
  config?: TaskRelationConfig
}

interface Emits {
  (e: 'close'): void
  (e: 'save', config: TaskRelationConfig): void
  (e: 'preview', config: TaskRelationConfig): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  config: () => ({ ...defaultTaskRelationConfig })
})

const emit = defineEmits<Emits>()

// 本地配置副本
const localConfig = ref<TaskRelationConfig>({ ...defaultTaskRelationConfig })

// 关系类型选项
const relationshipTypes = [
  { value: 'dependency', label: '依赖关系', description: '表示任务间的依赖关系' },
  { value: 'sequence', label: '顺序关系', description: '表示任务的执行顺序' },
  { value: 'parallel', label: '并行关系', description: '表示可以并行执行的任务' },
  { value: 'conditional', label: '条件关系', description: '表示有条件的任务关系' }
]

// 边线类型选项
const edgeTypes = [
  { value: 'solid', label: '实线' },
  { value: 'dashed', label: '虚线' },
  { value: 'dotted', label: '点线' }
]

// 预设颜色
const presetColors = [
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', 
  '#909399', '#C0C4CC', '#606266', '#303133'
]

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => {
    if (!value) {
      emit('close')
    }
  }
})

const configTitle = computed(() => {
  const typeText = props.relationType === 'business' ? '业务报表' : '临时报表'
  const chartText = props.chartType?.name || '图表'
  return `${chartText} - ${typeText}任务关系配置`
})

// 方法
const handleSave = () => {
  emit('save', { ...localConfig.value })
  dialogVisible.value = false
}

const handlePreview = () => {
  emit('preview', { ...localConfig.value })
}

const handleReset = () => {
  localConfig.value = { ...defaultTaskRelationConfig }
}

const handleColorSelect = (color: string, type: 'node' | 'edge') => {
  if (type === 'node') {
    localConfig.value.nodeStyle.color = color
  } else {
    localConfig.value.edgeStyle.color = color
  }
}

// 监听props变化
watch(() => props.config, (newConfig) => {
  if (newConfig) {
    localConfig.value = { ...newConfig }
  }
}, { immediate: true, deep: true })

watch(() => props.relationType, (newType) => {
  localConfig.value.type = newType
}, { immediate: true })
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="configTitle"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="task-relation-config">
      <!-- 基础配置 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Setting /></el-icon>
            <span>基础配置</span>
          </div>
        </template>

        <el-form :model="localConfig" label-width="120px" size="small">
          <el-form-item label="启用任务关系">
            <el-switch v-model="localConfig.enabled" />
          </el-form-item>

          <el-form-item label="关系类型">
            <el-select v-model="localConfig.relationshipType" style="width: 100%">
              <el-option
                v-for="type in relationshipTypes"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              >
                <div>
                  <div>{{ type.label }}</div>
                  <div style="font-size: 12px; color: #999;">{{ type.description }}</div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="显示标签">
            <el-switch v-model="localConfig.showLabels" />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 节点样式配置 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Connection /></el-icon>
            <span>节点样式</span>
          </div>
        </template>

        <el-form :model="localConfig.nodeStyle" label-width="120px" size="small">
          <el-form-item label="节点大小">
            <el-slider
              v-model="localConfig.nodeStyle.size"
              :min="20"
              :max="60"
              show-input
            />
          </el-form-item>

          <el-form-item label="节点颜色">
            <div class="color-config">
              <el-color-picker v-model="localConfig.nodeStyle.color" />
              <div class="preset-colors">
                <div
                  v-for="color in presetColors"
                  :key="color"
                  class="color-item"
                  :style="{ backgroundColor: color }"
                  @click="handleColorSelect(color, 'node')"
                />
              </div>
            </div>
          </el-form-item>

          <el-form-item label="边框颜色">
            <el-color-picker v-model="localConfig.nodeStyle.borderColor" />
          </el-form-item>

          <el-form-item label="边框宽度">
            <el-slider
              v-model="localConfig.nodeStyle.borderWidth"
              :min="0"
              :max="5"
              show-input
            />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 连线样式配置 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Link /></el-icon>
            <span>连线样式</span>
          </div>
        </template>

        <el-form :model="localConfig.edgeStyle" label-width="120px" size="small">
          <el-form-item label="连线颜色">
            <div class="color-config">
              <el-color-picker v-model="localConfig.edgeStyle.color" />
              <div class="preset-colors">
                <div
                  v-for="color in presetColors"
                  :key="color"
                  class="color-item"
                  :style="{ backgroundColor: color }"
                  @click="handleColorSelect(color, 'edge')"
                />
              </div>
            </div>
          </el-form-item>

          <el-form-item label="连线宽度">
            <el-slider
              v-model="localConfig.edgeStyle.width"
              :min="1"
              :max="5"
              show-input
            />
          </el-form-item>

          <el-form-item label="连线类型">
            <el-radio-group v-model="localConfig.edgeStyle.type">
              <el-radio
                v-for="type in edgeTypes"
                :key="type.value"
                :label="type.value"
              >
                {{ type.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleReset">重置</el-button>
        <el-button type="info" @click="handlePreview">预览</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.task-relation-config {
  .config-section {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
    }
  }

  .color-config {
    display: flex;
    align-items: center;
    gap: 12px;

    .preset-colors {
      display: flex;
      gap: 4px;

      .color-item {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        cursor: pointer;
        border: 1px solid #ddd;
        transition: transform 0.2s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
