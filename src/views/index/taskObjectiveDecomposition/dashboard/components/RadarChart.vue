<!-- 雷达图组件 -->
<script setup lang="ts" name="Radar<PERSON><PERSON>">
import { computed, watch, nextTick, toRefs } from 'vue'
import { useECharts, getDefaultChartOption, mergeChartOption } from '../composables/useECharts'
import type { RadarChartData, RadarChartConfig, ChartEvents } from '../types/dashboard.types'
import type { EChartsOption } from 'echarts'

interface Props {
  data: RadarChartData | null
  config?: Partial<RadarChartConfig>
  loading?: boolean
  height?: string | number
  events?: ChartEvents
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  height: 400
})

// 解构 props 以便在模板中使用
const { data, loading, height, config, events } = toRefs(props)

// 使用ECharts Hook
const { chartRef, chartInstance, setOption, showLoading, hideLoading, isLoading } = useECharts({
  autoResize: true
})

// 默认配置
const defaultConfig: RadarChartConfig = {
  theme: 'light',
  animation: true,
  responsive: true,
  showLegend: true,
  showTooltip: true,
  shape: 'polygon',
  splitNumber: 5,
  axisName: {
    show: true
  },
  splitLine: {
    show: true,
    lineStyle: {
      color: '#e0e6ed',
      width: 1
    }
  },
  splitArea: {
    show: false,
    areaStyle: {
      color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
    }
  }
}

// 合并配置
const chartConfig = computed(() => ({
  ...defaultConfig,
  ...props.config
}))

// 生成图表选项
const chartOption = computed((): EChartsOption => {
  if (!props.data || !props.data.indicator || !props.data.indicator.length) return {}

  const config = chartConfig.value
  const baseOption = getDefaultChartOption()

  const option: EChartsOption = {
    title: {
      show: false
    },
    tooltip: {
      show: config.showTooltip,
      trigger: 'item',
      formatter: (params: any) => {
        if (Array.isArray(params.value)) {
          const indicators = props.data!.indicator
          let content = `<strong>${params.seriesName}</strong><br/>`
          params.value.forEach((value: number, index: number) => {
            if (indicators[index]) {
              content += `${indicators[index].name}: ${value}<br/>`
            }
          })
          return content
        }
        return params.name + ': ' + params.value
      }
    },
    legend: {
      show: config.showLegend,
      data: props.data.series.map(s => s.name),
      bottom: 10
    },
    radar: {
      shape: config.shape,
      splitNumber: config.splitNumber,
      axisName: {
        show: config.axisName.show,
        formatter: typeof config.axisName.formatter === 'function'
          ? (name?: string) => (config.axisName.formatter as (name: string) => string)(name || '')
          : config.axisName.formatter || '{value}',
        color: '#666',
        fontSize: 12
      },
      splitLine: {
        show: config.splitLine.show,
        lineStyle: {
          color: config.splitLine.lineStyle.color,
          width: config.splitLine.lineStyle.width,
          type: 'solid'
        }
      },
      splitArea: {
        show: config.splitArea.show,
        areaStyle: {
          color: config.splitArea.areaStyle.color
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#e0e6ed'
        }
      },
      indicator: props.data.indicator.map(item => ({
        name: item.name,
        max: item.max,
        min: item.min || 0
      }))
    },
    series: [
      {
        type: 'radar',
        data: props.data.series.map((series, index) => ({
          name: series.name,
          value: series.data,
          itemStyle: {
            color: series.color || `hsl(${(index * 60) % 360}, 70%, 60%)`
          },
          lineStyle: {
            color: series.color || `hsl(${(index * 60) % 360}, 70%, 60%)`,
            width: 2
          },
          areaStyle: {
            color: series.color || `hsl(${(index * 60) % 360}, 70%, 60%)`,
            opacity: 0.3
          },
          symbol: 'circle',
          symbolSize: 6,
          emphasis: {
            focus: 'series',
            areaStyle: {
              opacity: 0.6
            }
          }
        }))
      }
    ],
    animation: config.animation,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }

  return mergeChartOption(baseOption, option)
})

// 监听加载状态
watch(() => props.loading, (loading) => {
  if (loading) {
    showLoading()
  } else {
    hideLoading()
  }
}, { immediate: true })

// 监听数据变化
watch(() => props.data, () => {
  if (chartInstance.value && props.data) {
    setOption(chartOption.value)
  }
}, { deep: true })

// 监听配置变化
watch(() => props.config, () => {
  if (chartInstance.value && props.data) {
    setOption(chartOption.value)
  }
}, { deep: true })

// 初始化图表
watch(
  chartInstance,
  async (instance) => {
    if (instance && props.data) {
      await nextTick()
      setOption(chartOption.value)
      
      // 绑定事件
      if (props.events) {
        if (props.events.onDataPointClick) {
          instance.on('click', (params: any) => {
            props.events!.onDataPointClick!({
              name: params.name,
              value: params.value,
              category: params.name
            })
          })
        }
        
        if (props.events.onLegendClick) {
          instance.on('legendselectchanged', (params: any) => {
            props.events!.onLegendClick!(params.name)
          })
        }
        
        if (props.events.onChartReady) {
          props.events.onChartReady(instance)
        }
      }
    }
  },
  { immediate: true }
)
</script>

<template>
  <div class="radar-chart">
    <div 
      ref="chartRef" 
      class="chart-container"
      :style="{ height: typeof height === 'number' ? `${height}px` : height }"
    />
    
    <!-- 空状态 -->
    <div v-if="!data || !data.indicator || !data.indicator.length" class="empty-state">
      <el-empty description="暂无数据" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.radar-chart {
  width: 100%;
  height: 100%;
  position: relative;

  .chart-container {
    width: 100%;
    min-height: 300px;
  }

  .empty-state {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
  }
}
</style>
