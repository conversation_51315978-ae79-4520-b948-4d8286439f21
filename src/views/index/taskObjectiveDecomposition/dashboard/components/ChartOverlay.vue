<!-- 图表覆盖层组件 -->
<script setup lang="ts" name="ChartOverlay">
import { ref, computed, watch, nextTick } from 'vue'
import { Close, FullScreen, Download, Setting } from '@element-plus/icons-vue'
import BarChart from './BarChart.vue'
import LineChart from './LineChart.vue'
import PieChart from './PieChart.vue'
import RadarChart from './RadarChart.vue'
import ScatterChart from './ScatterChart.vue'
import MapChart from './MapChart.vue'
import HeatmapChart from './HeatmapChart.vue'
import DonutChart from './DonutChart.vue'
import MultiLayerChart from './MultiLayerChart.vue'
import ChartTypeSelector from './ChartTypeSelector.vue'
import type {
  ExtendedChartType,
  ExtendedChartConfig,
  DataPoint
} from '../types/dashboard.types'
import type { ChartTypeInfo } from '../config/chartTypes.config'

interface Props {
  visible: boolean
  chartType?: ExtendedChartType | string
  chartData?: any
  config?: ExtendedChartConfig
  loading?: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'chart-change', chartType: ExtendedChartType): void
  (e: 'export'): void
  (e: 'configure'): void
  (e: 'data-point-click', point: DataPoint): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  loading: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const overlayRef = ref<HTMLElement>()
const showSelector = ref(false)
const isFullscreen = ref(false)

// 计算属性
const overlayVisible = computed({
  get: () => props.visible,
  set: (value) => {
    if (!value) {
      emit('close')
    }
  }
})

const currentChartType = computed(() => {
  return props.chartType || 'bar-chart'
})

const overlayClass = computed(() => ({
  'chart-overlay': true,
  'fullscreen': isFullscreen.value
}))

// 图表事件配置
const chartEvents = {
  onDataPointClick: (point: DataPoint) => {
    emit('data-point-click', point)
  }
}

// 方法
const handleClose = () => {
  overlayVisible.value = false
}

const handleExport = () => {
  emit('export')
}

const handleConfigure = () => {
  emit('configure')
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

const toggleSelector = () => {
  showSelector.value = !showSelector.value
}

const handleChartSelect = (chartTypeId: string, options?: any) => {
  emit('chart-change', chartTypeId as ExtendedChartType)
  showSelector.value = false
}

const handleRelationConfigure = (chartTypeInfo: ChartTypeInfo, relationType: 'business' | 'temporary') => {
  // TODO: 实现任务关系配置逻辑
  console.log('Configure relation:', chartTypeInfo.name, relationType)
}

// 监听visible变化，处理动画
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    await nextTick()
    // 可以在这里添加进入动画逻辑
  }
})

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    handleClose()
  } else if (event.key === 'F11') {
    event.preventDefault()
    toggleFullscreen()
  }
}

// 组件挂载时添加键盘监听
watch(overlayVisible, (visible) => {
  if (visible) {
    document.addEventListener('keydown', handleKeydown)
  } else {
    document.removeEventListener('keydown', handleKeydown)
  }
})
</script>

<template>
  <teleport to="body">
    <transition name="overlay-fade">
      <div
        v-if="overlayVisible"
        ref="overlayRef"
        :class="overlayClass"
        @click.self="handleClose"
      >
        <!-- 覆盖层背景 -->
        <div class="overlay-backdrop" @click="handleClose" />
        
        <!-- 主内容区域 -->
        <div class="overlay-content">
          <!-- 头部工具栏 -->
          <div class="overlay-header">
            <div class="header-left">
              <h3 class="overlay-title">图表详情</h3>
              <el-button
                type="text"
                size="small"
                @click="toggleSelector"
              >
                切换图表类型
              </el-button>
            </div>
            
            <div class="header-right">
              <el-button
                type="text"
                size="small"
                @click="handleExport"
                :loading="loading"
              >
                <el-icon><Download /></el-icon>
                导出
              </el-button>
              
              <el-button
                type="text"
                size="small"
                @click="handleConfigure"
              >
                <el-icon><Setting /></el-icon>
                配置
              </el-button>
              
              <el-button
                type="text"
                size="small"
                @click="toggleFullscreen"
              >
                <el-icon><FullScreen /></el-icon>
                {{ isFullscreen ? '退出全屏' : '全屏' }}
              </el-button>
              
              <el-button
                type="text"
                size="small"
                @click="handleClose"
              >
                <el-icon><Close /></el-icon>
                关闭
              </el-button>
            </div>
          </div>

          <!-- 图表选择器 -->
          <div v-if="showSelector" class="chart-selector-panel">
            <ChartTypeSelector
              :selected-type="currentChartType"
              @select="handleChartSelect"
              @configure="handleRelationConfigure"
            />
          </div>

          <!-- 图表内容区域 -->
          <div class="overlay-body">
            <div class="chart-container">
              <!-- 加载状态 -->
              <div v-if="loading" class="loading-container">
                <el-loading
                  element-loading-text="加载中..."
                  element-loading-spinner="el-icon-loading"
                />
              </div>

              <!-- 图表组件 -->
              <template v-else>
                <!-- 柱状图 -->
                <BarChart
                  v-if="currentChartType === 'bar-chart'"
                  :data="chartData"
                  :config="config?.barChart"
                  :events="chartEvents"
                  height="100%"
                />
                
                <!-- 折线图 -->
                <LineChart
                  v-else-if="currentChartType === 'line-chart'"
                  :data="chartData"
                  :config="config?.lineChart"
                  :events="chartEvents"
                  height="100%"
                />
                
                <!-- 饼图 -->
                <PieChart
                  v-else-if="currentChartType === 'pie-chart'"
                  :data="chartData"
                  :config="config?.pieChart"
                  :events="chartEvents"
                  height="100%"
                />

                <!-- 雷达图 -->
                <RadarChart
                  v-else-if="currentChartType === 'radar-chart'"
                  :data="chartData"
                  :config="config?.radarChart"
                  :events="chartEvents"
                  height="100%"
                />

                <!-- 散点图 -->
                <ScatterChart
                  v-else-if="currentChartType === 'scatter-chart'"
                  :data="chartData"
                  :config="config?.scatterChart"
                  :events="chartEvents"
                  height="100%"
                />

                <!-- 平滑散点图 -->
                <ScatterChart
                  v-else-if="currentChartType === 'smooth-scatter-chart'"
                  :data="chartData"
                  :config="config?.scatterChart"
                  :events="chartEvents"
                  :smooth="true"
                  height="100%"
                />

                <!-- 地图 -->
                <MapChart
                  v-else-if="currentChartType === 'map-chart'"
                  :data="chartData"
                  :config="config?.mapChart"
                  :events="chartEvents"
                  height="100%"
                />

                <!-- 热力图 -->
                <HeatmapChart
                  v-else-if="currentChartType === 'heatmap'"
                  :data="chartData"
                  :config="config?.heatmapChart"
                  :events="chartEvents"
                  height="100%"
                />

                <!-- 环形图 -->
                <DonutChart
                  v-else-if="currentChartType === 'donut-chart'"
                  :data="chartData"
                  :config="config?.donutChart"
                  :events="chartEvents"
                  height="100%"
                />

                <!-- 多层环形图 -->
                <MultiLayerChart
                  v-else-if="currentChartType === 'multi-donut-chart'"
                  :data="chartData"
                  :config="config?.multiDonutChart"
                  :events="chartEvents"
                  type="donut"
                  height="100%"
                />

                <!-- 复合饼图 -->
                <MultiLayerChart
                  v-else-if="currentChartType === 'composite-pie-chart'"
                  :data="chartData"
                  :config="config?.multiDonutChart"
                  :events="chartEvents"
                  type="pie"
                  height="100%"
                />

                <!-- 其他图表类型的占位符 -->
                <div v-else class="chart-placeholder">
                  <el-empty :description="`${currentChartType} 图表类型正在开发中`" />
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<style lang="scss" scoped>
.chart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;

  &.fullscreen {
    padding: 0;

    .overlay-content {
      width: 100vw;
      height: 100vh;
      max-width: none;
      max-height: none;
      border-radius: 0;
    }
  }

  .overlay-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
  }

  .overlay-content {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    width: 90vw;
    height: 80vh;
    max-width: 1200px;
    max-height: 800px;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .overlay-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      border-bottom: 1px solid #e4e7ed;
      background: #fafafa;

      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;

        .overlay-title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
        }
      }

      .header-right {
        display: flex;
        align-items: center;
        gap: 8px;

        .el-button {
          color: #606266;

          &:hover {
            color: #409EFF;
          }
        }
      }
    }

    .chart-selector-panel {
      border-bottom: 1px solid #e4e7ed;
      padding: 16px;
      background: #f8f9fa;
      max-height: 300px;
      overflow-y: auto;
    }

    .overlay-body {
      flex: 1;
      padding: 20px;
      overflow: hidden;

      .chart-container {
        width: 100%;
        height: 100%;
        position: relative;

        .loading-container {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .chart-placeholder {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}

// 动画效果
.overlay-fade-enter-active,
.overlay-fade-leave-active {
  transition: all 0.3s ease;
}

.overlay-fade-enter-from,
.overlay-fade-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

.overlay-fade-enter-to,
.overlay-fade-leave-from {
  opacity: 1;
  transform: scale(1);
}

// 响应式设计
@media (max-width: 768px) {
  .chart-overlay {
    padding: 10px;

    .overlay-content {
      width: 100vw;
      height: 100vh;
      border-radius: 0;

      .overlay-header {
        padding: 12px 16px;

        .header-left {
          gap: 12px;

          .overlay-title {
            font-size: 16px;
          }
        }

        .header-right {
          gap: 4px;

          .el-button {
            padding: 4px 8px;
            font-size: 12px;
          }
        }
      }

      .overlay-body {
        padding: 16px;
      }
    }
  }
}
</style>
