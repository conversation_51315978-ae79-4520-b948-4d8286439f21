<!-- 多图表显示区域组件 -->
<script setup lang="ts" name="ChartDisplayArea">
import {ref, computed, watch, nextTick, onMounted, onUnmounted} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {useRoute} from 'vue-router'
import {
	Plus,
	Delete,
	FullScreen,
	Setting,
	Grid,
	Refresh,
	CopyDocument,
	Histogram,
	TrendCharts,
	PieChart,
	DocumentAdd,
} from '@element-plus/icons-vue'
import OverviewDashboard from './OverviewDashboard.vue'
import DynamicChart from './DynamicChart.vue'
import ChartTypeSelector from './ChartTypeSelector.vue'
import ChartConfigPanel from './ChartConfigPanel.vue'
import ChartFullscreen from './ChartFullscreen.vue'
import {useChartManager, type ChartInstance} from '../composables/useChartManager'
import {useDragDrop} from '../composables/useDragDrop'
import {useResize} from '../composables/useResize'
import {useLazyLoad} from '../composables/useLazyLoad'
import {useMemoryManager} from '../composables/useMemoryManager'
import {usePerformanceMonitor} from '../composables/usePerformanceMonitor'
import {useDashboardStore} from '../stores/useDashboardStore'
import {ChartFactory} from '../utils/chartFactory'
import type {
	DashboardData,
	ChartConfig,
	ExtendedChartConfig,
	OverviewCard,
	TableRow,
	DataPoint,
	ExtendedChartType,
} from '../types/dashboard.types'

interface Props {
	data: DashboardData | null
	config: ExtendedChartConfig
	loading?: boolean
}

interface Emits {
	(e: 'refresh'): void
	(e: 'card-click', card: OverviewCard): void
	(e: 'row-click', row: TableRow): void
	(e: 'data-point-click', point: DataPoint): void
	(e: 'export'): void
	(e: 'chart-added', chartId: string): void
	(e: 'chart-removed', chartId: string): void
}

const props = withDefaults(defineProps<Props>(), {
	loading: false,
})

const emit = defineEmits<Emits>()

// 路由和store
const route = useRoute()
const dashboardStore = useDashboardStore()
const taskId = route.params.id as string

// 使用图表管理器
const chartManager = useChartManager({
	maxCharts: 12,
	defaultChartSize: {width: 400, height: 300},
	autoLayout: true,
})

// 添加调试监听
watch(
	() => chartManager.charts.value,
	(newCharts) => {
		console.log('图表数组变化:', newCharts?.length || 'undefined')
		chartManager.debugCharts()
	},
	{deep: true, immediate: true}
)

// 创建一个计算属性来获取图表列表
const chartsList = computed(() => {
	const charts = chartManager.charts
	console.log('计算属性 chartsList - 原始:', charts)

	// 如果charts是ref对象，需要访问.value
	const chartsArray = charts?.value || charts || []
	console.log('计算属性 chartsList - 解包后:', chartsArray?.length || 'undefined', chartsArray)

	return Array.isArray(chartsArray) ? chartsArray : []
})

// 使用拖拽功能
const dragDrop = useDragDrop()

// 使用大小调整功能
const resize = useResize({
	minWidth: 200,
	minHeight: 150,
	maxWidth: 1200,
	maxHeight: 800,
})

// 使用懒加载功能
const lazyLoad = useLazyLoad({
	rootMargin: '100px',
	threshold: 0.1,
	enabled: true,
})

// 使用内存管理功能
const memoryManager = useMemoryManager({
	maxCharts: 15,
	maxInactiveTime: 3 * 60 * 1000, // 3分钟
	autoCleanup: true,
	cleanupInterval: 30 * 1000, // 30秒
	memoryThreshold: 80, // 80MB
})

// 使用性能监控功能
const performanceMonitor = usePerformanceMonitor({
	fpsThreshold: 30,
	memoryThreshold: 100,
	renderTimeThreshold: 100,
	alertEnabled: true,
	monitoringInterval: 2000, // 2秒
})

// 响应式状态
const showChartSelector = ref(false)
const showConfigPanel = ref(false)
const showFullscreen = ref(false)
const fullscreenChart = ref<ChartInstance | null>(null)
const containerRef = ref<HTMLElement>()

// 计算属性
const hasOverview = computed(() => {
	return chartManager.charts.value.some(
		(chart) => chart.type === ('overview' as ExtendedChartType)
	)
})

// 检查图表类型是否支持
const isChartTypeSupported = (type: ExtendedChartType) => {
	return ChartFactory.isChartTypeSupported(type)
}

// 处理刷新
const handleRefresh = () => {
	emit('refresh')
}

// 处理卡片点击
const handleCardClick = (card: OverviewCard) => {
	emit('card-click', card)
}

// 处理表格行点击
const handleRowClick = (row: TableRow) => {
	emit('row-click', row)
}

// 处理数据点击
const handleDataPointClick = (point: DataPoint) => {
	emit('data-point-click', point)
}

// 处理导出
const handleExport = () => {
	emit('export')
}

// 图表事件配置
const getChartEvents = (chartId: string) => ({
	onDataPointClick: (point: any) => {
		memoryManager.recordActivity(chartId)
		handleDataPointClick(point)
	},
	onLegendClick: (legend: string) => {
		memoryManager.recordActivity(chartId)
		console.log('Legend clicked:', legend, 'Chart:', chartId)
	},
	onChartReady: (chart: any) => {
		memoryManager.recordActivity(chartId)
		console.log('Chart ready:', chartId, chart)
	},
})

// 添加图表
const handleAddChart = async (chartType: ExtendedChartType, options?: any) => {
	if (!isChartTypeSupported(chartType)) {
		ElMessage.error(`不支持的图表类型: ${chartType}`)
		return
	}

	const chartData = ChartFactory.createChartData(chartType, props.data)
	let chartConfig = ChartFactory.createChartConfig(chartType, props.config)

	// 如果有关系配置，合并到图表配置中
	if (options?.relationConfig) {
		chartConfig = {
			...chartConfig,
			relationConfig: options.relationConfig,
			relationType: options.relationType,
		}
	}

	const chartName = ChartFactory.getChartTypeName(chartType)

	const chartId = await chartManager.addChart(chartType, chartData, chartConfig, chartName)

	if (chartId) {
		emit('chart-added', chartId)
		showChartSelector.value = false
		ElMessage.success(`${chartName}添加成功`)
	}
}

// 处理图表配置
const handleChartConfigure = (chartType: any, relationType: 'business' | 'temporary') => {
	console.log('配置图表:', chartType, relationType)
	// 配置完成后，我们需要等待配置对话框的保存事件
	// 这里暂时不做处理，等待 ChartTypeSelector 组件触发配置完成事件
}

// 暴露方法给父组件
defineExpose({
	handleAddChart,
	importDashboardConfig: chartManager.importDashboardConfig,
	exportDashboardConfig: chartManager.exportDashboardConfig,
})

// 删除图表
const handleRemoveChart = async (chartId: string) => {
	try {
		await ElMessageBox.confirm('确定要删除这个图表吗？', '确认删除', {
			type: 'warning',
		})

		if (chartManager.removeChart(chartId)) {
			emit('chart-removed', chartId)
		}
	} catch {
		// 用户取消删除
	}
}

// 编辑图表
const handleEditChart = (chartId: string) => {
	chartManager.selectChart(chartId)
	showConfigPanel.value = true
}

// 全屏显示图表
const handleFullscreenChart = (chartId: string) => {
	const chart = chartManager.charts.value.find((c) => c.id === chartId)
	if (!chart) return

	fullscreenChart.value = chart
	showFullscreen.value = true
}

// 配置图表
const handleConfigChart = (chartId: string) => {
	chartManager.selectChart(chartId)
	showConfigPanel.value = true
}

// 清空所有图表
const handleClearAll = async () => {
	if (!chartManager.hasCharts.value) return

	try {
		await ElMessageBox.confirm('确定要清空所有图表吗？', '确认清空', {
			type: 'warning',
		})

		chartManager.clearAllCharts()
	} catch {
		// 用户取消
	}
}

// 自动布局
const handleAutoLayout = () => {
	chartManager.autoLayout()
	ElMessage.success('已应用自动布局')
}

// 响应式布局
const handleResponsiveLayout = () => {
	chartManager.responsiveLayout()
	ElMessage.success('已应用响应式布局')
}

// 保存仪表板配置
const handleSaveDashboard = async () => {
	if (!chartManager.hasCharts.value) {
		ElMessage.warning('没有图表可保存')
		return
	}

	if (!taskId) {
		ElMessage.error('缺少任务ID，无法保存配置')
		return
	}

	try {
		// 导出当前图表配置
		const config = chartManager.exportDashboardConfig(taskId)

		// 调用store的保存方法
		const success = await dashboardStore.saveDashboardConfig(taskId, config)

		if (success) {
			ElMessage.success('仪表板配置保存成功')
		} else {
			ElMessage.error(dashboardStore.saveError || '保存失败')
		}
	} catch (error: any) {
		console.error('保存仪表板配置失败:', error)
		ElMessage.error(error.message || '保存过程中发生错误')
	}
}

// 窗口大小变化处理
const handleResize = () => {
	if (chartManager.hasCharts.value) {
		performanceMonitor.startRenderTimer('layout')
		chartManager.responsiveLayout()
		performanceMonitor.endRenderTimer('layout')

		// 更新懒加载可见性
		nextTick(() => {
			lazyLoad.updateVisibility([...chartManager.charts.value], containerRef.value)
			// 更新性能监控的图表计数
			performanceMonitor.updateChartCount(
				chartManager.chartCount.value,
				lazyLoad.getVisibleChartCount()
			)
		})
	}
}

// 获取图标组件
const getIconComponent = (icon: any) => {
	if (typeof icon === 'string') {
		const iconMap: Record<string, any> = {
			Histogram: Histogram,
			TrendCharts: TrendCharts,
			PieChart: PieChart,
			Grid: Grid,
		}
		return iconMap[icon] || Histogram
	}
	return icon || Histogram
}

// 生命周期
onMounted(() => {
	window.addEventListener('resize', handleResize)

	// 启动内存管理
	memoryManager.startAutoCleanup(
		() => [...chartManager.charts.value],
		(chartId) => chartManager.removeChart(chartId)
	)
})

onUnmounted(() => {
	window.removeEventListener('resize', handleResize)
	memoryManager.cleanup()
})

// 处理布局命令
const handleLayoutCommand = (command: string) => {
	switch (command) {
		case 'auto':
			handleAutoLayout()
			break
		case 'responsive':
			handleResponsiveLayout()
			break
	}
}

// 拖拽处理
const handleDragStart = (chart: ChartInstance, event: MouseEvent) => {
	event.preventDefault()

	dragDrop.startDrag(chart, event, (dragChart, position) => {
		chartManager.updateChartPosition(dragChart.id, position)
	})
}

// 获取图表样式
const getChartStyle = (chart: ChartInstance) => {
	// 使用网格布局，不需要绝对定位
	return {
		transition: 'all 0.3s ease',
	}
}

// 大小调整处理
const handleResizeStart = (chart: ChartInstance, handle: string, event: MouseEvent) => {
	event.preventDefault()
	event.stopPropagation()

	resize.startResize(chart, handle, event, (resizeChart, size, position) => {
		chartManager.updateChartPosition(resizeChart.id, {...size, ...position})
	})
}

// 全屏事件处理
const handleFullscreenConfigure = (chartId: string) => {
	showFullscreen.value = false
	chartManager.selectChart(chartId)
	showConfigPanel.value = true
}

const handleFullscreenDownload = (chartId: string) => {
	// TODO: 实现图表下载功能
	ElMessage.info('图表下载功能开发中...')
}

const handleFullscreenRefresh = (chartId: string) => {
	const chart = chartManager.charts.value.find((c) => c.id === chartId)
	if (chart) {
		chartManager.setChartLoading(chartId, true)
		// 模拟刷新
		setTimeout(() => {
			chartManager.setChartLoading(chartId, false)
			ElMessage.success('图表已刷新')
		}, 1000)
	}
}

// 复制图表
const handleCopyChart = async (chartId: string) => {
	const chart = chartManager.charts.value.find((c) => c.id === chartId)
	if (!chart) return

	const newChartId = await chartManager.addChart(
		chart.type,
		chart.data,
		{...chart.config},
		`${chart.title} (副本)`
	)

	if (newChartId) {
		ElMessage.success('图表复制成功')
		emit('chart-added', newChartId)
	}
}
</script>

<template>
	<div class="chart-display-area">
		<!-- 区域头部 -->
		<div class="display-header">
			<div class="header-left">
				<h2 class="view-title">多图表仪表板</h2>
				<div class="config-notice">
					<i class="el-icon-warning-outline"></i>
					<span>图表配置说明：离开页面前请先保存。</span>
				</div>
				<div class="view-description">
					<span class="chart-count">已添加 {{ chartManager.chartCount }} 个图表</span>
					<span v-if="chartManager.selectedChart.value" class="selected-chart">
						当前选中：{{ chartManager.selectedChart.value.title }}
					</span>
				</div>
			</div>
			<div class="header-right">
				<el-button
					type="primary"
					:icon="Plus"
					size="small"
					@click="showChartSelector = true"
					:disabled="!chartManager.canAddChart"
				>
					添加图表
				</el-button>

				<el-dropdown @command="handleLayoutCommand">
					<el-button :icon="Grid" size="small" :disabled="!chartManager.hasCharts">
						布局选项
					</el-button>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item command="auto">自动布局</el-dropdown-item>
							<el-dropdown-item command="responsive">响应式布局</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>

				<el-button :icon="Refresh" size="small" :loading="loading" @click="handleRefresh">
					刷新数据
				</el-button>

				<el-button
					:icon="DocumentAdd"
					size="small"
					type="success"
					:loading="dashboardStore.saving"
					@click="handleSaveDashboard"
					:disabled="!chartManager.hasCharts"
				>
					保存配置
				</el-button>

				<el-button
					:icon="Delete"
					size="small"
					type="danger"
					@click="handleClearAll"
					:disabled="!chartManager.hasCharts"
				>
					清空
				</el-button>
			</div>
		</div>

		<!-- 内容区域 -->
		<div class="display-content" ref="containerRef">
			<!-- 概览仪表板 -->
			<div v-if="!hasOverview && !chartManager.hasCharts" class="overview-section">
				<OverviewDashboard
					:data="data"
					:config="config"
					:loading="loading"
					@card-click="handleCardClick"
					@row-click="handleRowClick"
					@data-point-click="handleDataPointClick"
					@refresh="handleRefresh"
					@export="handleExport"
				/>
			</div>

			<!-- 图表网格容器 -->
			<div v-if="chartManager.hasCharts" class="charts-grid">
				<div
					v-for="chart in chartsList"
					:key="chart?.id || Math.random()"
					class="chart-item"
					:class="{
						selected: chartManager.selectedChartId === chart.id,
						loading: chart.loading,
					}"
					:style="getChartStyle(chart)"
					@click="chartManager.selectChart(chart.id)"
				>
					<!-- 图表头部工具栏 -->
					<div class="chart-header">
						<div class="chart-title">{{ chart.title }}</div>
						<div class="chart-actions">
							<el-button
								type="text"
								:icon="Setting"
								size="small"
								@click.stop="handleConfigChart(chart.id)"
								title="配置"
							/>
							<el-button
								type="text"
								:icon="CopyDocument"
								size="small"
								@click.stop="handleCopyChart(chart.id)"
								title="复制"
							/>
							<el-button
								type="text"
								:icon="FullScreen"
								size="small"
								@click.stop="handleFullscreenChart(chart.id)"
								title="全屏"
							/>
							<el-button
								type="text"
								:icon="Delete"
								size="small"
								@click.stop="handleRemoveChart(chart.id)"
								title="删除"
							/>
						</div>
					</div>

					<!-- 图表内容 -->
					<div class="chart-content">
						<!-- 直接渲染图表，不使用懒加载 -->
						<DynamicChart
							v-if="chart && chart.type && chart.data"
							:type="chart.type"
							:data="chart.data"
							:config="chart.config"
							:loading="chart.loading"
							:events="getChartEvents(chart.id)"
							height="300px"
						/>

						<!-- 调试信息 -->
						<div v-else class="chart-placeholder">
							<div class="placeholder-content">
								<div class="placeholder-icon">📊</div>
								<div class="placeholder-text">
									{{
										chart
											? chart.type
												? '数据加载中...'
												: '图表类型未定义'
											: '图表对象未定义'
									}}
								</div>
								<div
									class="placeholder-hint"
									style="font-size: 10px; margin-top: 8px"
								>
									调试信息:
									{{
										JSON.stringify({
											hasChart: !!chart,
											type: chart?.type,
											hasData: !!chart?.data,
											id: chart?.id,
										})
									}}
								</div>
							</div>
						</div>

						<!-- 错误状态 -->
						<div v-if="chart.error" class="chart-error">
							<el-alert
								:title="chart.error"
								type="error"
								:closable="false"
								show-icon
							/>
						</div>
					</div>

					<!-- 调整手柄 -->
					<div
						v-if="
							chartManager.selectedChartId === chart.id && !dragDrop.isDragging.value
						"
						class="resize-handles"
					>
						<div
							v-for="handle in resize.getResizeHandles()"
							:key="handle.name"
							class="resize-handle"
							:style="resize.getHandleStyle(handle)"
							@mousedown="handleResizeStart(chart, handle.name, $event)"
						/>
					</div>
				</div>
			</div>

			<!-- 空状态 -->
			<div v-if="!chartManager.hasCharts && hasOverview" class="empty-state">
				<el-empty description="暂无图表">
					<template #image>
						<div class="empty-icon">📊</div>
					</template>
					<el-button type="primary" @click="showChartSelector = true">
						添加第一个图表
					</el-button>
				</el-empty>
			</div>
		</div>

		<!-- 图表类型选择器对话框 -->
		<el-dialog
			v-model="showChartSelector"
			title="选择图表类型"
			width="800px"
			:close-on-click-modal="false"
		>
			<ChartTypeSelector
				@select="handleAddChart"
				@configure="handleChartConfigure"
				@close="showChartSelector = false"
			/>
		</el-dialog>

		<!-- 图表配置面板 -->
		<ChartConfigPanel
			v-model:visible="showConfigPanel"
			:chart="chartManager.selectedChart.value"
			@config-change="
				(chartId, newConfig) => {
					chartManager.updateChartConfig(chartId, newConfig)
				}
			"
			@title-change="
				(chartId, newTitle) => {
					chartManager.updateChartTitle(chartId, newTitle)
				}
			"
			@config-reset="
				(chartId) => {
					const chart = chartManager.charts.value.find((c) => c.id === chartId)
					if (chart) {
						const chartType = chartManager.getChartTypeInfo(chart.type)
						if (chartType) {
							chartManager.updateChartConfig(chartId, chartType.defaultConfig)
						}
					}
				}
			"
		/>

		<!-- 全屏预览 -->
		<ChartFullscreen
			v-model:visible="showFullscreen"
			:chart="fullscreenChart"
			@configure="handleFullscreenConfigure"
			@download="handleFullscreenDownload"
			@refresh="handleFullscreenRefresh"
		/>
	</div>
</template>

<style lang="scss" scoped>
.chart-display-area {
	flex: 1;
	display: flex;
	flex-direction: column;
	background: #f5f7fa;
	min-height: 0;

	.display-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		padding: 20px 24px 16px;
		background: white;
		border-bottom: 1px solid #e4e7ed;

		.header-left {
			.view-title {
				margin: 0 0 8px 0;
				font-size: 20px;
				font-weight: 600;
				color: #303133;
			}

			.config-notice {
				display: flex;
				align-items: center;
				gap: 6px;
				padding: 6px 12px;
				margin-bottom: 8px;
				background: #fff7e6;
				border: 1px solid #ffd591;
				border-radius: 4px;
				font-size: 13px;
				color: #d48806;
				line-height: 1.4;

				i {
					font-size: 14px;
					color: #faad14;
				}

				span {
					font-weight: 500;
				}
			}

			.view-description {
				font-size: 14px;
				color: #909399;
				line-height: 1.4;
				display: flex;
				align-items: center;
				gap: 16px;

				.chart-count {
					color: #409eff;
					font-weight: 500;
				}

				.selected-chart {
					color: #67c23a;
					font-weight: 500;
				}
			}
		}

		.header-right {
			display: flex;
			align-items: center;
			gap: 8px;
		}
	}

	.display-content {
		flex: 1;
		padding: 24px;
		overflow: auto;
		min-height: 0;
		position: relative;

		.overview-section {
			margin-bottom: 24px;
		}

		.charts-grid {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
			gap: 20px;
			min-height: 600px;

			.chart-item {
				background: white;
				border-radius: 8px;
				border: 1px solid #e4e7ed;
				box-shadow: none;
				transition: all 0.3s ease;
				cursor: pointer;
				user-select: none;
				overflow: hidden;
				display: flex;
				flex-direction: column;
				min-height: 400px;

				&:hover {
					border-color: #409eff;
					box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
					transform: translateY(-2px);
				}

				&:active {
					cursor: grabbing;
				}

				&.selected {
					border-color: #409eff;
					box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
				}

				&.loading {
					opacity: 0.7;
				}

				.chart-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 16px 20px;
					border-bottom: 1px solid #f0f0f0;
					background: #fafafa;

					.chart-title {
						font-size: 16px;
						font-weight: 600;
						color: #303133;
						margin: 0;
						flex: 1;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					.chart-actions {
						display: flex;
						gap: 4px;
						opacity: 0;
						transition: opacity 0.2s ease;
					}
				}

				&:hover .chart-actions {
					opacity: 1;
				}

				.chart-content {
					flex: 1;
					padding: 16px;
					min-height: 0;
					position: relative;

					.chart-placeholder {
						position: absolute;
						top: 0;
						left: 0;
						right: 0;
						bottom: 0;
						display: flex;
						align-items: center;
						justify-content: center;
						background: rgba(255, 255, 255, 0.9);

						.placeholder-content {
							text-align: center;
							color: #909399;

							.placeholder-icon {
								margin-bottom: 12px;
								opacity: 0.5;
							}

							.placeholder-text {
								font-size: 16px;
								font-weight: 500;
								margin-bottom: 4px;
							}

							.placeholder-hint {
								font-size: 12px;
								opacity: 0.7;
							}
						}
					}

					.chart-error {
						position: absolute;
						top: 0;
						left: 0;
						right: 0;
						bottom: 0;
						display: flex;
						align-items: center;
						justify-content: center;
						background: rgba(255, 255, 255, 0.9);
						z-index: 10;
					}
				}

				.resize-handles {
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					pointer-events: none;

					.resize-handle {
						pointer-events: all;
						opacity: 0;
						transition: opacity 0.2s ease;

						&:hover {
							opacity: 1;
							background: #67c23a;
						}
					}
				}

				&.selected .resize-handles .resize-handle {
					opacity: 0.7;
				}
			}
		}

		.empty-state {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 400px;

			.empty-icon {
				font-size: 48px;
				margin-bottom: 16px;
			}
		}
	}
}

// 响应式设计
@media (max-width: 1400px) {
	.chart-display-area {
		.display-content {
			.charts-grid {
				grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
				gap: 16px;

				.chart-item {
					min-height: 350px;

					.chart-header {
						padding: 14px 18px;

						.chart-title {
							font-size: 15px;
						}
					}

					.chart-content {
						padding: 14px;
					}
				}
			}
		}
	}
}

@media (max-width: 1200px) {
	.chart-display-area {
		.display-content {
			.charts-grid {
				grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
				gap: 16px;

				.chart-item {
					min-height: 400px;
				}
			}
		}
	}
}

@media (max-width: 768px) {
	.chart-display-area {
		.display-header {
			flex-direction: column;
			gap: 12px;
			align-items: stretch;
			padding: 16px 20px;

			.header-left {
				.view-title {
					font-size: 18px;
				}

				.view-description {
					font-size: 13px;
					flex-direction: column;
					align-items: flex-start;
					gap: 8px;
				}
			}

			.header-right {
				justify-content: flex-start;
				flex-wrap: wrap;
			}
		}

		.display-content {
			padding: 16px 20px;

			.charts-grid {
				grid-template-columns: 1fr;
				gap: 12px;

				.chart-item {
					min-height: 300px;
					border-radius: 6px;

					.chart-header {
						padding: 12px 16px;

						.chart-title {
							font-size: 14px;
						}
					}

					.chart-content {
						padding: 12px;
					}

					&:hover {
						transform: translateY(-1px);
					}
				}
			}
		}
	}
}

@media (max-width: 480px) {
	.chart-display-area {
		.display-content {
			padding: 12px 16px;

			.charts-grid {
				gap: 8px;

				.chart-item {
					.chart-header {
						padding: 10px 12px;
						flex-direction: column;
						align-items: flex-start;
						gap: 4px;

						.chart-title {
							font-size: 13px;
						}

						.chart-actions {
							opacity: 1; // 在小屏幕上始终显示
						}
					}

					.chart-content {
						padding: 10px;
					}
				}
			}
		}
	}
}

// 图表选中样式
.chart-item.selected {
	border-color: #409eff;
	box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
}

// 图表选择器对话框样式
:deep(.el-dialog) {
	.el-dialog__body {
		padding: 20px;
	}
}

// 配置面板样式
:deep(.config-panel) {
	.el-drawer__body {
		padding: 20px;
	}
}
</style>
