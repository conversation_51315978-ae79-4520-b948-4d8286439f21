<!-- 图表配置面板组件 -->
<script setup lang="ts" name="ChartConfigPanel">
import { ref, computed, watch } from 'vue'
import { 
  Setting, 
  Close, 
  Refresh,
  Download,
  Upload,
  Edit
} from '@element-plus/icons-vue'
import type { 
  ExtendedChartType, 
  ChartConfig,
  BarChartConfig,
  LineChartConfig,
  PieChartConfig,
  RadarChartConfig,
  ScatterChartConfig,
  MapChartConfig,
  HeatmapChartConfig,
  DonutChartConfig
} from '../types/dashboard.types'
import type { ChartInstance } from '../composables/useChartManager'

interface Props {
  visible: boolean
  chart: ChartInstance | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'config-change', chartId: string, config: any): void
  (e: 'title-change', chartId: string, title: string): void
  (e: 'config-reset', chartId: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 本地配置副本
const localConfig = ref<any>({})
const localTitle = ref('')

// 计算属性
const panelVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const chartType = computed(() => props.chart?.type || 'bar-chart')
const chartId = computed(() => props.chart?.id || '')

// 配置项标题映射
const configTitles: Record<ExtendedChartType, string> = {
  'bar-chart': '柱状图配置',
  'line-chart': '折线图配置',
  'pie-chart': '饼图配置',
  'radar-chart': '雷达图配置',
  'scatter-chart': '散点图配置',
  'map-chart': '地图配置',
  'heatmap': '热力图配置',
  'donut-chart': '环形图配置',
  'multi-donut-chart': '多层环形图配置',
  'composite-pie-chart': '复合饼图配置',
  'multi-pie-chart': '多饼图配置',
  '3d-pie-chart': '3D饼图配置',
  'smooth-scatter-chart': '平滑散点图配置',
  'stacked-line-chart': '堆积折线图配置',
  'stacked-bar-chart': '堆积柱状图配置',
  'percentage-stacked-line-chart': '百分比堆积折线图配置',
  'percentage-stacked-bar-chart': '百分比堆积柱状图配置',
  'data-point-line-chart': '数据点折线图配置'
}

const currentConfigTitle = computed(() => {
  return configTitles[chartType.value] || '图表配置'
})

// 监听图表变化
watch(
  () => props.chart,
  (newChart) => {
    if (newChart) {
      localConfig.value = { ...newChart.config }
      localTitle.value = newChart.title
    }
  },
  { immediate: true, deep: true }
)

// 处理配置变化
const handleConfigChange = () => {
  if (chartId.value) {
    emit('config-change', chartId.value, localConfig.value)
  }
}

// 处理标题变化
const handleTitleChange = () => {
  if (chartId.value) {
    emit('title-change', chartId.value, localTitle.value)
  }
}

// 重置配置
const handleReset = () => {
  if (chartId.value) {
    emit('config-reset', chartId.value)
  }
}

// 导出配置
const handleExport = () => {
  if (!props.chart) return
  
  const configData = {
    chartId: props.chart.id,
    chartType: props.chart.type,
    title: props.chart.title,
    config: props.chart.config,
    exportTime: new Date().toISOString()
  }
  
  const blob = new Blob([JSON.stringify(configData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `chart-config-${props.chart.id}.json`
  a.click()
  URL.revokeObjectURL(url)
}

// 导入配置
const fileInputRef = ref<HTMLInputElement>()
const handleImport = () => {
  fileInputRef.value?.click()
}

const handleFileChange = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return
  
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const configData = JSON.parse(e.target?.result as string)
      if (configData.config) {
        localConfig.value = configData.config
        if (configData.title) {
          localTitle.value = configData.title
        }
        handleConfigChange()
        handleTitleChange()
      }
    } catch (error) {
      console.error('导入配置失败:', error)
    }
  }
  reader.readAsText(file)
}
</script>

<template>
  <el-drawer
    v-model="panelVisible"
    title="图表配置"
    direction="rtl"
    size="400px"
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="config-header">
        <el-icon :size="20">
          <Setting />
        </el-icon>
        <span>{{ currentConfigTitle }}</span>
      </div>
    </template>

    <div v-if="chart" class="config-content">
      <!-- 基本信息 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Edit /></el-icon>
            <span>基本信息</span>
          </div>
        </template>
        
        <el-form label-position="top" size="small">
          <el-form-item label="图表标题">
            <el-input
              v-model="localTitle"
              placeholder="请输入图表标题"
              @blur="handleTitleChange"
            />
          </el-form-item>
          
          <el-form-item label="图表类型">
            <el-input :value="chartType" disabled />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 通用配置 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Setting /></el-icon>
            <span>通用配置</span>
          </div>
        </template>
        
        <el-form label-position="top" size="small">
          <el-form-item label="主题">
            <el-radio-group 
              v-model="localConfig.theme"
              @change="handleConfigChange"
            >
              <el-radio-button label="light">浅色</el-radio-button>
              <el-radio-button label="dark">深色</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="动画效果">
            <el-switch
              v-model="localConfig.animation"
              @change="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="显示图例">
            <el-switch
              v-model="localConfig.showLegend"
              @change="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="显示提示框">
            <el-switch
              v-model="localConfig.showTooltip"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 特定配置 -->
      <el-card class="config-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Setting /></el-icon>
            <span>专项配置</span>
          </div>
        </template>
        
        <!-- 柱状图配置 -->
        <el-form v-if="chartType.includes('bar')" label-position="top" size="small">
          <el-form-item label="方向">
            <el-radio-group 
              v-model="localConfig.orientation"
              @change="handleConfigChange"
            >
              <el-radio-button label="vertical">垂直</el-radio-button>
              <el-radio-button label="horizontal">水平</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="显示数据标签">
            <el-switch
              v-model="localConfig.showDataLabels"
              @change="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="柱宽度">
            <el-slider
              v-model="localConfig.barWidth"
              :min="20"
              :max="100"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-form>

        <!-- 折线图配置 -->
        <el-form v-else-if="chartType.includes('line')" label-position="top" size="small">
          <el-form-item label="平滑曲线">
            <el-switch
              v-model="localConfig.smooth"
              @change="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="显示数据点">
            <el-switch
              v-model="localConfig.showPoints"
              @change="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="显示区域">
            <el-switch
              v-model="localConfig.showArea"
              @change="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="线条宽度">
            <el-slider
              v-model="localConfig.lineWidth"
              :min="1"
              :max="10"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-form>

        <!-- 饼图配置 -->
        <el-form v-else-if="chartType.includes('pie') || chartType.includes('donut')" label-position="top" size="small">
          <el-form-item label="显示百分比">
            <el-switch
              v-model="localConfig.showPercentage"
              @change="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="内半径">
            <el-slider
              v-model="localConfig.innerRadius"
              :min="0"
              :max="80"
              @change="handleConfigChange"
            />
          </el-form-item>

          <el-form-item label="外半径">
            <el-slider
              v-model="localConfig.outerRadius"
              :min="40"
              :max="120"
              @change="handleConfigChange"
            />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 操作按钮 -->
      <div class="config-actions">
        <el-button @click="handleReset" :icon="Refresh">
          重置配置
        </el-button>
        <el-button @click="handleExport" :icon="Download">
          导出配置
        </el-button>
        <el-button @click="handleImport" :icon="Upload">
          导入配置
        </el-button>
      </div>

      <!-- 隐藏的文件输入 -->
      <input
        ref="fileInputRef"
        type="file"
        accept=".json"
        style="display: none"
        @change="handleFileChange"
      />
    </div>

    <div v-else class="no-chart">
      <el-empty description="请选择一个图表进行配置" />
    </div>
  </el-drawer>
</template>

<style lang="scss" scoped>
.config-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
}

.config-content {
  .config-section {
    margin-bottom: 16px;
    
    .section-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .config-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 24px;
  }
}

.no-chart {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

:deep(.el-card__header) {
  padding: 12px 16px;
  background: #f8f9fa;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-slider) {
  margin: 8px 0;
}
</style>
