<!-- 热力图组件 -->
<script setup lang="ts" name="Heatmap<PERSON>hart">
import { computed, watch, nextTick } from 'vue'
import { useECharts, getDefaultChartOption, mergeChartOption } from '../composables/useECharts'
import type { HeatmapChartData, HeatmapChartConfig, ChartEvents } from '../types/dashboard.types'
import type { EChartsOption } from 'echarts'

interface Props {
  data: HeatmapChartData | null
  config?: Partial<HeatmapChartConfig>
  loading?: boolean
  height?: string | number
  events?: ChartEvents
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  height: 400
})

// 使用ECharts Hook
const { chartRef, chartInstance, setOption, showLoading, hideLoading, isLoading } = useECharts({
  autoResize: true
})

// 默认配置
const defaultConfig: HeatmapChartConfig = {
  theme: 'light',
  animation: true,
  responsive: true,
  showLegend: true,
  showTooltip: true,
  xAxis: {
    type: 'category'
  },
  yAxis: {
    type: 'category'
  },
  visualMap: {
    min: 0,
    max: 100,
    calculable: true,
    orient: 'vertical',
    left: 'right',
    bottom: 'bottom'
  }
}

// 合并配置
const chartConfig = computed(() => ({
  ...defaultConfig,
  ...props.config
}))

// 生成图表选项
const chartOption = computed((): EChartsOption => {
  if (!props.data || !props.data.data || !Array.isArray(props.data.data) || !props.data.data.length) return {}

  const config = chartConfig.value
  const baseOption = getDefaultChartOption()

  // 计算数据范围
  const values = props.data.data.map(item => item[2])
  const minValue = Math.min(...values)
  const maxValue = Math.max(...values)

  const option: EChartsOption = {
    title: {
      show: false
    },
    tooltip: {
      show: config.showTooltip,
      position: 'top',
      formatter: (params: any) => {
        const value = params.value
        if (Array.isArray(value) && value.length >= 3) {
          const xLabel = props.data!.xAxis[value[0]] || value[0]
          const yLabel = props.data!.yAxis[value[1]] || value[1]
          return `${xLabel} - ${yLabel}<br/>数值: ${value[2]}`
        }
        return `数值: ${value}`
      }
    },
    grid: {
      height: '50%',
      top: '10%'
    },
    xAxis: {
      type: config.xAxis.type,
      data: props.data.xAxis,
      splitArea: {
        show: true
      },
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: {
      type: config.yAxis.type,
      data: props.data.yAxis,
      splitArea: {
        show: true
      },
      axisLabel: {
        color: '#666'
      }
    },
    visualMap: {
      min: config.visualMap.min || minValue,
      max: config.visualMap.max || maxValue,
      calculable: config.visualMap.calculable,
      orient: config.visualMap.orient,
      left: config.visualMap.left,
      bottom: config.visualMap.bottom,
      inRange: {
        color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffbf', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
      }
    },
    series: [{
      name: '热力图',
      type: 'heatmap',
      data: props.data.data,
      label: {
        show: true,
        formatter: (params: any) => {
          return params.value[2]
        }
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }],
    animation: config.animation,
    animationDuration: 1000,
    animationEasing: 'cubicOut'
  }

  return mergeChartOption(baseOption, option)
})

// 监听加载状态
watch(() => props.loading, (loading) => {
  if (loading) {
    showLoading()
  } else {
    hideLoading()
  }
}, { immediate: true })

// 监听数据变化
watch(() => props.data, () => {
  if (chartInstance.value && props.data) {
    setOption(chartOption.value)
  }
}, { deep: true })

// 监听配置变化
watch(() => props.config, () => {
  if (chartInstance.value && props.data) {
    setOption(chartOption.value)
  }
}, { deep: true })

// 初始化图表
watch(
  chartInstance,
  async (instance) => {
    if (instance && props.data) {
      await nextTick()
      setOption(chartOption.value)
      
      // 绑定事件
      if (props.events) {
        if (props.events.onDataPointClick) {
          instance.on('click', (params: any) => {
            const value = params.value
            props.events!.onDataPointClick!({
              name: `${props.data!.xAxis[value[0]]}-${props.data!.yAxis[value[1]]}`,
              value: value[2],
              category: 'heatmap'
            })
          })
        }
        
        if (props.events.onChartReady) {
          props.events.onChartReady(instance)
        }
      }
    }
  },
  { immediate: true }
)
</script>

<template>
  <div class="heatmap-chart">
    <div 
      ref="chartRef" 
      class="chart-container"
      :style="{ height: typeof height === 'number' ? `${height}px` : height }"
    />
    
    <!-- 空状态 -->
    <div v-if="!data || !data.data.length" class="empty-state">
      <el-empty description="暂无数据" />
    </div>
    
    <!-- 数据统计 -->
    <div v-if="data && data.data.length" class="heatmap-stats">
      <div class="stat-item">
        <span class="stat-label">数据点:</span>
        <span class="stat-value">{{ data.data.length }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">X轴维度:</span>
        <span class="stat-value">{{ data.xAxis.length }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Y轴维度:</span>
        <span class="stat-value">{{ data.yAxis.length }}</span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.heatmap-chart {
  width: 100%;
  height: 100%;
  position: relative;

  .chart-container {
    width: 100%;
    min-height: 300px;
  }

  .empty-state {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
  }

  .heatmap-stats {
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(255, 255, 255, 0.9);
    padding: 8px 12px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-size: 12px;

    .stat-item {
      display: flex;
      align-items: center;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }

      .stat-label {
        color: #666;
        margin-right: 8px;
      }

      .stat-value {
        color: #333;
        font-weight: 500;
      }
    }
  }
}
</style>
