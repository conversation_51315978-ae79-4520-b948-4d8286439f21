<!-- 概览仪表板组件 - 显示多个图表 -->
<script setup lang="ts" name="OverviewDashboard">
import { ref } from 'vue'
import DataTable from './DataTable.vue'
import BarChart from './BarChart.vue'
import LineChart from './LineChart.vue'
import PieChart from './PieChart.vue'
import ChartOverlay from './ChartOverlay.vue'
import type {
  DashboardData,
  ExtendedChartConfig,
  TableRow,
  DataPoint,
  ExtendedChartType
} from '../types/dashboard.types'

interface Props {
  data: DashboardData | null
  config: ExtendedChartConfig
  loading?: boolean
}

interface Emits {
  (e: 'refresh'): void
  (e: 'row-click', row: TableRow): void
  (e: 'data-point-click', point: DataPoint): void
  (e: 'export'): void
  (e: 'chart-click', chartType: string, data: any): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// Overlay状态管理
const overlayVisible = ref(false)
const overlayChartType = ref<ExtendedChartType | string>('bar-chart')
const overlayChartData = ref<any>(null)
const overlayError = ref<string | null>(null)

// 处理表格行点击
const handleRowClick = (row: TableRow) => {
  emit('row-click', row)
}

// 处理数据点点击
const handleDataPointClick = (point: DataPoint) => {
  emit('data-point-click', point)
}

// 处理导出
const handleExport = () => {
  emit('export')
}

// 防抖处理图表点击
let clickTimeout: NodeJS.Timeout | null = null

// 处理图表点击 - 新的overlay显示逻辑
const handleChartClick = (chartType: string, data: any) => {
  // 防抖处理，避免重复点击
  if (clickTimeout) {
    clearTimeout(clickTimeout)
  }

  clickTimeout = setTimeout(() => {
    try {
      overlayError.value = null
      overlayChartType.value = chartType
      overlayChartData.value = data
      overlayVisible.value = true
      emit('chart-click', chartType, data)
    } catch (error) {
      overlayError.value = `加载${getChartTitle(chartType)}时出错`
      console.error('Chart click error:', error)
    }
  }, 200)
}

// 处理overlay关闭
const handleOverlayClose = () => {
  overlayVisible.value = false
  overlayError.value = null
}

// 处理overlay图表类型切换
const handleOverlayChartChange = (chartType: ExtendedChartType) => {
  overlayChartType.value = chartType
  // 根据图表类型获取对应的数据
  switch (chartType) {
    case 'bar-chart':
      overlayChartData.value = props.data?.chartData?.barChart
      break
    case 'line-chart':
      overlayChartData.value = props.data?.chartData?.lineChart
      break
    case 'pie-chart':
      overlayChartData.value = props.data?.chartData?.pieChart
      break
    default:
      overlayChartData.value = null
  }
}

// 图表事件配置
const chartEvents = {
  onDataPointClick: handleDataPointClick
}

// 获取图表标题
const getChartTitle = (chartType: string): string => {
  const titles: Record<string, string> = {
    'table': '数据表格',
    'line-chart': '折线图',
    'bar-chart': '柱状图',
    'pie-chart': '饼图'
  }
  return titles[chartType] || '图表'
}
</script>

<template>
  <div class="overview-dashboard">
    <!-- 图表网格 -->
    <div class="charts-grid">
      <!-- 表格区域 -->
      <div class="chart-container table-container" @click="handleChartClick('table', data?.tableData)">
        <div class="chart-header">
          <h3 class="chart-title">表格</h3>
          <div class="click-hint">点击查看详情</div>
        </div>
        <div class="chart-content">
          <div v-if="loading" class="chart-loading">
            <el-loading element-loading-text="加载中..." />
          </div>
          <div v-else-if="!data?.tableData" class="chart-error">
            <el-empty description="暂无数据" />
          </div>
          <DataTable
            v-else
            :data="data.tableData"
            :loading="loading"
            @export="handleExport"
            @row-click="handleRowClick"
            :show-header="false"
            :compact="true"
          />
        </div>
      </div>

      <!-- 折线图区域 -->
      <div class="chart-container line-container" @click="handleChartClick('line-chart', data?.chartData?.lineChart)">
        <div class="chart-header">
          <h3 class="chart-title">折线图</h3>
          <div class="click-hint">点击查看详情</div>
        </div>
        <div class="chart-content">
          <LineChart
            :data="data?.chartData?.lineChart || null"
            :config="config.lineChart"
            :loading="loading"
            :events="chartEvents"
            height="300px"
          />
        </div>
      </div>

      <!-- 柱状图区域 -->
      <div class="chart-container bar-container" @click="handleChartClick('bar-chart', data?.chartData?.barChart)">
        <div class="chart-header">
          <h3 class="chart-title">柱状图</h3>
          <div class="click-hint">点击查看详情</div>
        </div>
        <div class="chart-content">
          <BarChart
            :data="data?.chartData?.barChart || null"
            :config="config.barChart"
            :loading="loading"
            :events="chartEvents"
            height="300px"
          />
        </div>
      </div>

      <!-- 饼图区域 -->
      <div class="chart-container pie-container" @click="handleChartClick('pie-chart', data?.chartData?.pieChart)">
        <div class="chart-header">
          <h3 class="chart-title">饼图</h3>
          <div class="click-hint">点击查看详情</div>
        </div>
        <div class="chart-content">
          <PieChart
            :data="data?.chartData?.pieChart || null"
            :config="config.pieChart"
            :loading="loading"
            :events="chartEvents"
            height="300px"
          />
        </div>
      </div>
    </div>

    <!-- 图表覆盖层 -->
    <ChartOverlay
      :visible="overlayVisible"
      :chart-type="overlayChartType"
      :chart-data="overlayChartData"
      :config="config"
      :loading="loading"
      @close="handleOverlayClose"
      @chart-change="handleOverlayChartChange"
      @export="handleExport"
      @data-point-click="handleDataPointClick"
    />
  </div>
</template>

<style lang="scss" scoped>
.overview-dashboard {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 0;
  height: 100%;

  .charts-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 20px;
    flex: 1;

    .chart-container {
      background: white;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #409EFF;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
        transform: translateY(-2px);
      }

      &.table-container {
        grid-column: 1;
        grid-row: 1;
      }

      &.line-container {
        grid-column: 2;
        grid-row: 1;
      }

      &.bar-container {
        grid-column: 1;
        grid-row: 2;
      }

      &.pie-container {
        grid-column: 2;
        grid-row: 2;
      }

      .chart-header {
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
        background: #fafafa;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .chart-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }

        .click-hint {
          font-size: 12px;
          color: #909399;
          opacity: 0;
          transition: opacity 0.3s ease;
        }
      }

      &:hover .chart-header .click-hint {
        opacity: 1;
      }

      .chart-content {
        flex: 1;
        padding: 16px;
        min-height: 0;
        position: relative;

        .chart-loading,
        .chart-error {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 255, 255, 0.9);
        }

        .chart-loading {
          z-index: 10;
        }
      }
    }
  }

  // 响应式布局
  @media (max-width: 1400px) {
    .charts-grid {
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      .chart-container {
        .chart-header {
          padding: 14px 18px;

          .chart-title {
            font-size: 15px;
          }

          .click-hint {
            font-size: 11px;
          }
        }

        .chart-content {
          padding: 14px;
        }
      }
    }
  }

  @media (max-width: 1200px) {
    .charts-grid {
      grid-template-columns: 1fr;
      gap: 16px;

      .chart-container {
        &.table-container,
        &.line-container,
        &.bar-container,
        &.pie-container {
          grid-column: 1;
          grid-row: auto;
        }
      }
    }
  }

  @media (max-width: 768px) {
    gap: 12px;
    padding: 0;

    .charts-grid {
      gap: 12px;

      .chart-container {
        border-radius: 6px;

        .chart-header {
          padding: 12px 16px;

          .chart-title {
            font-size: 14px;
          }

          .click-hint {
            font-size: 10px;
          }
        }

        .chart-content {
          padding: 12px;
        }

        &:hover {
          transform: translateY(-1px);
        }
      }
    }
  }

  @media (max-width: 480px) {
    gap: 8px;

    .charts-grid {
      gap: 8px;

      .chart-container {
        .chart-header {
          padding: 10px 12px;
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;

          .chart-title {
            font-size: 13px;
          }

          .click-hint {
            font-size: 9px;
            opacity: 1; // 在小屏幕上始终显示
          }
        }

        .chart-content {
          padding: 10px;
        }
      }
    }
  }
}
</style>
