/**
 * 数据模拟服务
 * 用于生成仪表板所需的模拟数据
 */

import type {
  DashboardData,
  OverviewCard,
  TableRow,
  BarChartData,
  LineChartData,
  PieChartData,
  TableColumn,
  ScatterChartData,
  RadarChartData,
  MapChartData,
  HeatmapChartData,
  MultiDonutChartData,
  CompositePieChartData
} from '../types/dashboard.types'

// 扩展数据类型定义（使用从types文件导入的类型）

export interface DonutChartData extends PieChartData {
  innerRadius?: string
}

export interface StackedChartData {
  categories: string[]
  series: Array<{
    name: string
    data: number[]
    stack?: string
    color?: string
  }>
}

/**
 * 生成随机数
 */
function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * 生成随机日期
 */
function randomDate(start: Date, end: Date): Date {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
}

/**
 * 生成概览卡片数据
 */
export function generateOverviewCards(): OverviewCard[] {
  return [
    {
      id: 'total-tasks',
      title: '总任务数',
      value: 123,
      unit: '个',
      trend: {
        direction: 'up',
        percentage: 12.5
      },
      icon: 'Document',
      color: '#409EFF'
    },
    {
      id: 'completed-tasks',
      title: '已完成任务',
      value: 89,
      unit: '个',
      trend: {
        direction: 'up',
        percentage: 8.3
      },
      icon: 'CircleCheck',
      color: '#67C23A'
    },
    {
      id: 'pending-tasks',
      title: '待处理任务',
      value: 34,
      unit: '个',
      trend: {
        direction: 'down',
        percentage: 5.2
      },
      icon: 'Clock',
      color: '#E6A23C'
    },
    {
      id: 'overdue-tasks',
      title: '逾期任务',
      value: 7,
      unit: '个',
      trend: {
        direction: 'stable',
        percentage: 0
      },
      icon: 'Warning',
      color: '#F56C6C'
    },
    {
      id: 'team-members',
      title: '团队成员',
      value: 25,
      unit: '人',
      trend: {
        direction: 'up',
        percentage: 4.2
      },
      icon: 'User',
      color: '#909399'
    }
  ]
}

/**
 * 生成表格列定义
 */
export function generateTableColumns(): TableColumn[] {
  return [
    { key: 'taskName', label: '任务名称', type: 'text', sortable: true, width: 200 },
    { key: 'assignee', label: '负责人', type: 'text', sortable: true, width: 120 },
    { key: 'status', label: '状态', type: 'status', filterable: true, width: 100 },
    { key: 'priority', label: '优先级', type: 'text', filterable: true, width: 100 },
    { key: 'startDate', label: '开始时间', type: 'date', sortable: true, width: 120 },
    { key: 'endDate', label: '结束时间', type: 'date', sortable: true, width: 120 },
    { key: 'progress', label: '进度', type: 'number', sortable: true, width: 130 }
  ]
}

/**
 * 生成表格数据
 */
export function generateTableData(count: number = 50): TableRow[] {
  const statuses = ['进行中', '已完成', '待开始', '已暂停', '已取消']
  const priorities = ['高', '中', '低']
  const assignees = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  
  const data: TableRow[] = []
  
  for (let i = 1; i <= count; i++) {
    const startDate = randomDate(new Date(2024, 0, 1), new Date(2024, 11, 31))
    const endDate = new Date(startDate.getTime() + randomInt(7, 90) * 24 * 60 * 60 * 1000)
    
    data.push({
      id: `task-${i}`,
      taskName: `任务${i.toString().padStart(3, '0')}`,
      assignee: assignees[randomInt(0, assignees.length - 1)],
      status: statuses[randomInt(0, statuses.length - 1)],
      priority: priorities[randomInt(0, priorities.length - 1)],
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      progress: randomInt(0, 100)
    })
  }
  
  return data
}

/**
 * 生成柱状图数据
 */
export function generateBarChartData(): BarChartData {
  return {
    categories: ['一月', '二月', '三月', '四月', '五月', '六月'],
    series: [
      {
        name: '新增任务',
        data: [20, 32, 28, 45, 38, 52],
        color: '#409EFF'
      },
      {
        name: '完成任务',
        data: [18, 28, 25, 40, 35, 48],
        color: '#67C23A'
      }
    ]
  }
}

/**
 * 生成折线图数据
 */
export function generateLineChartData(): LineChartData {
  const dates = []
  const values = []
  
  for (let i = 0; i < 30; i++) {
    const date = new Date()
    date.setDate(date.getDate() - (29 - i))
    dates.push(date.toISOString().split('T')[0])
    values.push(randomInt(10, 100))
  }
  
  return {
    xAxis: dates,
    series: [
      {
        name: '任务完成率',
        data: values,
        color: '#409EFF'
      }
    ]
  }
}

/**
 * 生成饼图数据
 */
export function generatePieChartData(): PieChartData {
  const taskStatusData = [
    { name: '已完成', value: 156, color: '#67C23A' },
    { name: '进行中', value: 89, color: '#409EFF' },
    { name: '待审核', value: 45, color: '#E6A23C' },
    { name: '待开始', value: 23, color: '#F56C6C' },
    { name: '已暂停', value: 12, color: '#909399' },
    { name: '已退回', value: 8, color: '#FF7F50' },
    { name: '已取消', value: 5, color: '#C0C4CC' }
  ]

  const departmentData = [
    { name: '技术部', value: 128, color: '#409EFF' },
    { name: '市场部', value: 95, color: '#67C23A' },
    { name: '财务部', value: 67, color: '#E6A23C' },
    { name: '人事部', value: 54, color: '#F56C6C' },
    { name: '运营部', value: 43, color: '#909399' },
    { name: '法务部', value: 21, color: '#FF7F50' }
  ]

  const priorityData = [
    { name: '高优先级', value: 89, color: '#F56C6C' },
    { name: '中优先级', value: 156, color: '#E6A23C' },
    { name: '低优先级', value: 93, color: '#67C23A' },
    { name: '紧急', value: 34, color: '#FF4500' }
  ]

  // 根据随机数选择不同的数据集
  const rand = Math.random()
  let selectedData, seriesName
  
  if (rand < 0.4) {
    selectedData = taskStatusData
    seriesName = '任务状态分布'
  } else if (rand < 0.7) {
    selectedData = departmentData
    seriesName = '部门任务分布'
  } else {
    selectedData = priorityData
    seriesName = '任务优先级分布'
  }

  return {
    series: [
      {
        name: seriesName,
        data: selectedData
      }
    ]
  }
}

/**
 * 生成3D饼图数据
 */
export function generate3DPieChartData(): PieChartData {
  const projectData = [
    { name: '核心业务系统', value: 245, color: '#409EFF' },
    { name: '数据分析平台', value: 189, color: '#67C23A' },
    { name: '移动端应用', value: 134, color: '#E6A23C' },
    { name: '运维监控系统', value: 98, color: '#F56C6C' },
    { name: '安全防护系统', value: 76, color: '#909399' },
    { name: '第三方集成', value: 54, color: '#FF7F50' },
    { name: '测试环境', value: 32, color: '#9370DB' },
    { name: '文档管理', value: 21, color: '#20B2AA' }
  ]

  const regionData = [
    { name: '华东地区', value: 312, color: '#FF6B6B' },
    { name: '华南地区', value: 287, color: '#4ECDC4' },
    { name: '华北地区', value: 234, color: '#45B7D1' },
    { name: '西南地区', value: 178, color: '#96CEB4' },
    { name: '华中地区', value: 145, color: '#FFEAA7' },
    { name: '西北地区', value: 89, color: '#DDA0DD' },
    { name: '东北地区', value: 67, color: '#98D8C8' }
  ]

  const businessData = [
    { name: '电商平台', value: 428, color: '#FF9F43' },
    { name: '金融服务', value: 356, color: '#10AC84' },
    { name: '物流配送', value: 289, color: '#5F27CD' },
    { name: '客户服务', value: 234, color: '#00D2D3' },
    { name: '数据分析', value: 178, color: '#FF6348' },
    { name: '营销推广', value: 123, color: '#7BED9F' },
    { name: '风险控制', value: 89, color: '#FFA502' }
  ]

  // 随机选择数据集
  const datasets = [
    { data: projectData, name: '项目任务分布' },
    { data: regionData, name: '区域业务分布' },
    { data: businessData, name: '业务线任务分布' }
  ]
  
  const selected = datasets[Math.floor(Math.random() * datasets.length)]

  return {
    series: [
      {
        name: selected.name,
        data: selected.data
      }
    ]
  }
}

/**
 * 生成雷达图数据
 */
export function generateRadarChartData(): RadarChartData {
  const indicators = [
    { name: '执行力', max: 100, min: 0 },
    { name: '创新力', max: 100, min: 0 },
    { name: '协作力', max: 100, min: 0 },
    { name: '学习力', max: 100, min: 0 },
    { name: '领导力', max: 100, min: 0 },
    { name: '沟通力', max: 100, min: 0 }
  ]

  return {
    indicator: indicators,
    series: [
      {
        name: '团队A',
        data: indicators.map(() => randomInt(60, 100)),
        color: '#409EFF'
      },
      {
        name: '团队B',
        data: indicators.map(() => randomInt(50, 95)),
        color: '#67C23A'
      },
      {
        name: '团队C',
        data: indicators.map(() => randomInt(40, 90)),
        color: '#E6A23C'
      }
    ]
  }
}

/**
 * 生成散点图数据
 */
export function generateScatterChartData(): ScatterChartData {
  const generateScatterSeries = (name: string, color: string, count: number = 20) => ({
    name,
    color,
    data: Array.from({ length: count }, () => [
      randomInt(10, 100), // x轴：任务复杂度
      randomInt(20, 90)   // y轴：完成率
    ] as [number, number])
  })

  return {
    xAxis: {
      type: 'value' as const
    },
    yAxis: {
      type: 'value' as const
    },
    series: [
      generateScatterSeries('高优先级', '#F56C6C'),
      generateScatterSeries('中优先级', '#E6A23C'),
      generateScatterSeries('低优先级', '#67C23A')
    ]
  }
}

/**
 * 生成热力图数据
 */
export function generateHeatmapData(): HeatmapChartData {
  const xAxis = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  const yAxis = ['00:00', '02:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00']

  const data: Array<[number, number, number]> = []

  for (let i = 0; i < xAxis.length; i++) {
    for (let j = 0; j < yAxis.length; j++) {
      // 模拟工作时间的活跃度
      let value = randomInt(0, 100)
      if (j >= 4 && j <= 9) { // 工作时间更活跃
        value = randomInt(60, 100)
      }
      data.push([i, j, value])
    }
  }

  return {
    xAxis,
    yAxis,
    data
  }
}

/**
 * 生成地图数据
 */
export function generateMapData(): MapChartData {
  const regions = [
    '北京', '上海', '广东', '浙江', '江苏', '山东', '河南', '四川',
    '湖北', '湖南', '河北', '福建', '安徽', '陕西', '辽宁', '重庆'
  ]

  return {
    mapName: 'china',
    series: [{
      name: '任务分布',
      data: regions.map(name => ({
        name,
        value: randomInt(10, 500),
        itemStyle: {
          color: `hsl(${randomInt(0, 360)}, 70%, 60%)`
        }
      }))
    }]
  }
}

/**
 * 生成环形图数据
 */
export function generateDonutChartData(): DonutChartData {
  const pieData = generatePieChartData()
  return {
    ...pieData,
    innerRadius: '40%'
  }
}

/**
 * 生成堆积图数据
 */
export function generateStackedChartData(): StackedChartData {
  const categories = ['1月', '2月', '3月', '4月', '5月', '6月']

  return {
    categories,
    series: [
      {
        name: '已完成',
        stack: 'total',
        data: categories.map(() => randomInt(20, 50)),
        color: '#67C23A'
      },
      {
        name: '进行中',
        stack: 'total',
        data: categories.map(() => randomInt(10, 30)),
        color: '#409EFF'
      },
      {
        name: '待开始',
        stack: 'total',
        data: categories.map(() => randomInt(5, 20)),
        color: '#E6A23C'
      },
      {
        name: '已暂停',
        stack: 'total',
        data: categories.map(() => randomInt(0, 10)),
        color: '#F56C6C'
      }
    ]
  }
}

/**
 * 生成多层环形图数据
 */
export function generateMultiLayerDonutData(): MultiDonutChartData {
  return {
    series: [
      {
        name: '内层数据',
        innerRadius: 20,
        outerRadius: 60,
        data: [
          { name: '研发', value: 45, itemStyle: { color: '#409EFF' } },
          { name: '测试', value: 25, itemStyle: { color: '#67C23A' } },
          { name: '运维', value: 20, itemStyle: { color: '#E6A23C' } },
          { name: '其他', value: 10, itemStyle: { color: '#F56C6C' } }
        ]
      },
      {
        name: '外层数据',
        innerRadius: 70,
        outerRadius: 100,
        data: [
          { name: '前端开发', value: 25, itemStyle: { color: '#409EFF' } },
          { name: '后端开发', value: 20, itemStyle: { color: '#5470C6' } },
          { name: '功能测试', value: 15, itemStyle: { color: '#67C23A' } },
          { name: '自动化测试', value: 10, itemStyle: { color: '#91CC75' } },
          { name: '部署运维', value: 12, itemStyle: { color: '#E6A23C' } },
          { name: '监控运维', value: 8, itemStyle: { color: '#FAC858' } },
          { name: '文档', value: 6, itemStyle: { color: '#F56C6C' } },
          { name: '培训', value: 4, itemStyle: { color: '#EE6666' } }
        ]
      }
    ]
  }
}

/**
 * 生成复合饼图数据
 */
export function generateCompositePieChartData(): MultiDonutChartData {
  return {
    series: [
      {
        name: '任务状态分布',
        innerRadius: 0,
        outerRadius: 50,
        data: [
          { name: '已完成', value: 45, itemStyle: { color: '#67C23A' } },
          { name: '进行中', value: 30, itemStyle: { color: '#E6A23C' } },
          { name: '待开始', value: 25, itemStyle: { color: '#F56C6C' } }
        ]
      },
      {
        name: '优先级分布',
        innerRadius: 60,
        outerRadius: 90,
        data: [
          { name: '高优先级', value: 35, itemStyle: { color: '#F56C6C' } },
          { name: '中优先级', value: 40, itemStyle: { color: '#E6A23C' } },
          { name: '低优先级', value: 25, itemStyle: { color: '#67C23A' } }
        ]
      },
      {
        name: '部门分布',
        innerRadius: 100,
        outerRadius: 120,
        data: [
          { name: '研发部', value: 50, itemStyle: { color: '#409EFF' } },
          { name: '测试部', value: 30, itemStyle: { color: '#67C23A' } },
          { name: '运维部', value: 20, itemStyle: { color: '#E6A23C' } }
        ]
      }
    ]
  }
}

/**
 * 生成数据点折线图数据
 */
export function generateDataPointLineChartData(): LineChartData {
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  
  return {
    xAxis: months,
    series: [
      {
        name: '任务完成率',
        data: [78, 82, 85, 79, 88, 92, 89, 94, 91, 87, 93, 96],
        color: '#409EFF'
      },
      {
        name: '质量达标率',
        data: [85, 88, 82, 86, 90, 94, 92, 96, 93, 89, 95, 98],
        color: '#67C23A'
      },
      {
        name: '按时交付率',
        data: [72, 75, 79, 76, 83, 87, 84, 89, 86, 82, 88, 91],
        color: '#E6A23C'
      }
    ]
  }
}

/**
 * 生成平滑散点图数据
 */
export function generateSmoothScatterChartData(): ScatterChartData {
  const generatePoints = (count: number, baseX: number, baseY: number, spread: number): [number, number][] => {
    return Array.from({ length: count }, (): [number, number] => [
      baseX + (Math.random() - 0.5) * spread,
      baseY + (Math.random() - 0.5) * spread
    ])
  }

  return {
    xAxis: { type: 'value', data: undefined },
    yAxis: { type: 'value', data: undefined },
    series: [
      {
        name: '高性能团队',
        data: generatePoints(30, 80, 85, 20),
        color: '#67C23A'
      },
      {
        name: '中等团队',
        data: generatePoints(45, 60, 65, 25),
        color: '#409EFF'
      },
      {
        name: '待提升团队',
        data: generatePoints(25, 40, 45, 20),
        color: '#E6A23C'
      },
      {
        name: '新组建团队',
        data: generatePoints(20, 25, 30, 15),
        color: '#F56C6C'
      }
    ]
  }
}

/**
 * 生成百分比堆积图数据
 */
export function generatePercentageStackedChartData(): StackedChartData {
  const categories = ['Q1', 'Q2', 'Q3', 'Q4']
  
  return {
    categories,
    series: [
      {
        name: '已完成',
        data: [45, 52, 48, 58],
        stack: 'percentage',
        color: '#67C23A'
      },
      {
        name: '进行中',
        data: [35, 28, 32, 25],
        stack: 'percentage',
        color: '#409EFF'
      },
      {
        name: '待开始',
        data: [15, 12, 15, 12],
        stack: 'percentage',
        color: '#E6A23C'
      },
      {
        name: '已暂停',
        data: [5, 8, 5, 5],
        stack: 'percentage',
        color: '#909399'
      }
    ]
  }
}

/**
 * 生成多饼图数据
 */
export function generateMultiPieChartData(): MultiDonutChartData {
  return {
    series: [
      {
        name: '内层任务分布',
        innerRadius: 0,
        outerRadius: 60,
        data: [
          { name: '研发任务', value: 156, itemStyle: { color: '#409EFF' } },
          { name: '测试任务', value: 89, itemStyle: { color: '#67C23A' } },
          { name: '部署任务', value: 45, itemStyle: { color: '#E6A23C' } },
          { name: '维护任务', value: 32, itemStyle: { color: '#F56C6C' } }
        ]
      },
      {
        name: '外层任务分布',
        innerRadius: 70,
        outerRadius: 100,
        data: [
          { name: '前端开发', value: 98, itemStyle: { color: '#409EFF' } },
          { name: '后端开发', value: 127, itemStyle: { color: '#67C23A' } },
          { name: '数据库', value: 67, itemStyle: { color: '#E6A23C' } },
          { name: '运维', value: 43, itemStyle: { color: '#F56C6C' } },
          { name: '文档', value: 28, itemStyle: { color: '#909399' } }
        ]
      }
    ]
  }
}

/**
 * 生成任务关系图数据
 */
export function generateTaskRelationData() {
  return {
    nodes: [
      { id: 'task-1', name: '需求分析', type: 'start', x: 100, y: 100 },
      { id: 'task-2', name: '系统设计', type: 'process', x: 250, y: 100 },
      { id: 'task-3', name: '前端开发', type: 'process', x: 400, y: 50 },
      { id: 'task-4', name: '后端开发', type: 'process', x: 400, y: 150 },
      { id: 'task-5', name: '数据库设计', type: 'process', x: 250, y: 200 },
      { id: 'task-6', name: '集成测试', type: 'process', x: 550, y: 100 },
      { id: 'task-7', name: '部署上线', type: 'end', x: 700, y: 100 }
    ],
    edges: [
      { source: 'task-1', target: 'task-2', type: 'dependency' },
      { source: 'task-2', target: 'task-3', type: 'dependency' },
      { source: 'task-2', target: 'task-4', type: 'dependency' },
      { source: 'task-2', target: 'task-5', type: 'dependency' },
      { source: 'task-3', target: 'task-6', type: 'dependency' },
      { source: 'task-4', target: 'task-6', type: 'dependency' },
      { source: 'task-5', target: 'task-6', type: 'dependency' },
      { source: 'task-6', target: 'task-7', type: 'dependency' }
    ]
  }
}

/**
 * 生成完整的仪表板数据
 */
export function generateDashboardData(): DashboardData {
  return {
    overview: {
      cards: generateOverviewCards()
    },
    tableData: {
      columns: generateTableColumns(),
      rows: generateTableData(),
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 50
      }
    },
    chartData: {
      barChart: generateBarChartData(),
      lineChart: generateLineChartData(),
      pieChart: generatePieChartData()
    },
    lastUpdated: new Date()
  }
}

/**
 * 根据图表类型生成对应的模拟数据
 */
export function generateChartDataByType(chartType: string): any {
  console.log('生成图表数据，类型:', chartType)

  try {
    switch (chartType) {
      case 'bar-chart':
        return generateBarChartData()
      case 'stacked-bar-chart':
        return generateStackedChartData()
      case 'percentage-stacked-bar-chart':
        return generatePercentageStackedChartData()

      case 'line-chart':
        return generateLineChartData()
      case 'data-point-line-chart':
        return generateDataPointLineChartData()
      case 'stacked-line-chart':
        return generateStackedChartData()
      case 'percentage-stacked-line-chart':
        return generatePercentageStackedChartData()

      case 'pie-chart':
        return generatePieChartData()
        
      case '3d-pie-chart':
      case 'pie-3d':
      case '3D饼状图':
        return generate3DPieChartData()

      case 'donut-chart':
        return generateDonutChartData()

      case 'multi-donut-chart':
      case 'multi-layer-donut-chart':
        return generateMultiLayerDonutData()

      case 'radar-chart':
        return generateRadarChartData()

      case 'scatter-chart':
        return generateScatterChartData()
      case 'smooth-scatter-chart':
        return generateSmoothScatterChartData()

      case 'map-chart':
        return generateMapData()

      case 'heatmap':
      case 'heatmap-chart':
        return generateHeatmapData()

      case 'composite-pie-chart':
        return generateCompositePieChartData()
      case 'multi-pie-chart':
        return generateMultiPieChartData()
        
      case 'task-relation-chart':
      case 'task-relation':
        return generateTaskRelationData()

      default:
        console.warn('未知图表类型，使用默认柱状图数据:', chartType)
        return generateBarChartData()
    }
  } catch (error) {
    console.error('生成图表数据失败:', error)
    // 返回基础的柱状图数据作为后备
    return {
      categories: ['默认类别'],
      series: [{
        name: '默认数据',
        data: [1],
        color: '#409EFF'
      }]
    }
  }
}

/**
 * 生成任务相关的模拟数据
 */
export function generateTaskRelatedData() {
  return {
    // 任务状态统计
    taskStatus: {
      completed: randomInt(40, 60),
      inProgress: randomInt(20, 35),
      pending: randomInt(10, 25),
      paused: randomInt(5, 15),
      cancelled: randomInt(2, 8)
    },

    // 优先级分布
    priority: {
      high: randomInt(15, 25),
      medium: randomInt(35, 50),
      low: randomInt(20, 35)
    },

    // 部门分布
    departments: [
      { name: '研发部', taskCount: randomInt(20, 40), completionRate: randomInt(70, 95) },
      { name: '测试部', taskCount: randomInt(15, 30), completionRate: randomInt(75, 90) },
      { name: '产品部', taskCount: randomInt(10, 25), completionRate: randomInt(65, 85) },
      { name: '运维部', taskCount: randomInt(8, 20), completionRate: randomInt(80, 95) },
      { name: '市场部', taskCount: randomInt(5, 15), completionRate: randomInt(60, 80) }
    ],

    // 时间趋势数据
    timeline: Array.from({ length: 12 }, (_, i) => ({
      month: `${i + 1}月`,
      created: randomInt(20, 50),
      completed: randomInt(15, 45),
      efficiency: randomInt(60, 95)
    })),

    // 人员效率数据
    personnel: [
      { name: '张三', completedTasks: randomInt(15, 30), efficiency: randomInt(80, 95) },
      { name: '李四', completedTasks: randomInt(12, 28), efficiency: randomInt(75, 90) },
      { name: '王五', completedTasks: randomInt(10, 25), efficiency: randomInt(70, 88) },
      { name: '赵六', completedTasks: randomInt(8, 22), efficiency: randomInt(65, 85) },
      { name: '钱七', completedTasks: randomInt(6, 20), efficiency: randomInt(60, 82) }
    ]
  }
}

/**
 * 模拟数据更新
 */
export function updateDashboardData(existingData: DashboardData): DashboardData {
  // 更新概览卡片数值
  const updatedCards = existingData.overview.cards.map(card => ({
    ...card,
    value: card.value + randomInt(-5, 10),
    trend: {
      direction: Math.random() > 0.5 ? 'up' : 'down' as 'up' | 'down',
      percentage: Math.random() * 10
    }
  }))

  return {
    ...existingData,
    overview: {
      cards: updatedCards
    },
    chartData: {
      barChart: generateBarChartData(),
      lineChart: generateLineChartData(),
      pieChart: generatePieChartData()
    },
    lastUpdated: new Date()
  }
}

/**
 * 生成实时数据更新
 */
export function generateRealTimeUpdate() {
  return {
    timestamp: new Date(),
    metrics: {
      activeUsers: randomInt(50, 200),
      completedToday: randomInt(5, 25),
      avgResponseTime: randomInt(100, 500),
      systemLoad: randomInt(20, 80)
    },
    alerts: [
      {
        id: Date.now(),
        type: 'info',
        message: `新增 ${randomInt(1, 5)} 个任务`,
        timestamp: new Date()
      }
    ]
  }
}
