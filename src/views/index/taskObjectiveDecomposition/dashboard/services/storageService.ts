/**
 * 本地存储管理服务
 * 用于管理仪表板数据的本地存储和读取
 */

import type { DashboardData, ChartConfig, ExtendedChartConfig } from '../types/dashboard.types'

// 存储键名常量
const STORAGE_KEYS = {
  DASHBOARD_DATA: 'task_dashboard_data',
  CHART_CONFIG: 'task_dashboard_config',
  LAST_UPDATE: 'task_dashboard_last_update'
} as const

/**
 * 存储配置选项
 */
interface StorageOptions {
  compress?: boolean
  expireTime?: number // 过期时间（毫秒）
}

/**
 * 存储项结构
 */
interface StorageItem<T> {
  data: T
  timestamp: number
  expireTime?: number
}

/**
 * 本地存储服务类
 */
export class StorageService {
  private static instance: StorageService
  private readonly prefix = 'task_dashboard_'

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): StorageService {
    if (!StorageService.instance) {
      StorageService.instance = new StorageService()
    }
    return StorageService.instance
  }

  /**
   * 生成完整的存储键名
   */
  private getKey(key: string): string {
    return `${this.prefix}${key}`
  }

  /**
   * 检查localStorage是否可用
   */
  private isStorageAvailable(): boolean {
    try {
      const test = '__storage_test__'
      localStorage.setItem(test, test)
      localStorage.removeItem(test)
      return true
    } catch {
      return false
    }
  }

  /**
   * 获取存储空间使用情况
   */
  getStorageUsage(): { used: number; available: number; percentage: number } {
    if (!this.isStorageAvailable()) {
      return { used: 0, available: 0, percentage: 0 }
    }

    let used = 0
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        used += localStorage[key].length + key.length
      }
    }

    // 估算可用空间（大多数浏览器限制为5-10MB）
    const available = 5 * 1024 * 1024 // 5MB
    const percentage = (used / available) * 100

    return { used, available, percentage }
  }

  /**
   * 清理过期数据
   */
  cleanExpiredData(): void {
    if (!this.isStorageAvailable()) return

    const now = Date.now()
    const keysToRemove: string[] = []

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(this.prefix)) {
        try {
          const item = JSON.parse(localStorage.getItem(key) || '{}')
          if (item.expireTime && now > item.expireTime) {
            keysToRemove.push(key)
          }
        } catch {
          // 如果解析失败，也删除这个项
          keysToRemove.push(key)
        }
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key))
  }

  /**
   * 存储数据
   */
  setItem<T>(key: string, data: T, options: StorageOptions = {}): boolean {
    if (!this.isStorageAvailable()) {
      console.warn('localStorage不可用')
      return false
    }

    try {
      const item: StorageItem<T> = {
        data,
        timestamp: Date.now(),
        expireTime: options.expireTime ? Date.now() + options.expireTime : undefined
      }

      const serializedData = JSON.stringify(item)
      
      // 检查存储空间
      const usage = this.getStorageUsage()
      if (usage.percentage > 90) {
        console.warn('存储空间不足，正在清理过期数据...')
        this.cleanExpiredData()
      }

      localStorage.setItem(this.getKey(key), serializedData)
      return true
    } catch (error) {
      console.error('存储数据失败:', error)
      return false
    }
  }

  /**
   * 获取数据
   */
  getItem<T>(key: string): T | null {
    if (!this.isStorageAvailable()) {
      return null
    }

    try {
      const serializedData = localStorage.getItem(this.getKey(key))
      if (!serializedData) {
        return null
      }

      const item: StorageItem<T> = JSON.parse(serializedData)
      
      // 检查是否过期
      if (item.expireTime && Date.now() > item.expireTime) {
        this.removeItem(key)
        return null
      }

      return item.data
    } catch (error) {
      console.error('读取数据失败:', error)
      return null
    }
  }

  /**
   * 删除数据
   */
  removeItem(key: string): void {
    if (!this.isStorageAvailable()) return
    localStorage.removeItem(this.getKey(key))
  }

  /**
   * 清空所有仪表板相关数据
   */
  clear(): void {
    if (!this.isStorageAvailable()) return

    const keysToRemove: string[] = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(this.prefix)) {
        keysToRemove.push(key)
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key))
  }

  /**
   * 保存仪表板数据
   */
  saveDashboardData(data: DashboardData): boolean {
    return this.setItem(STORAGE_KEYS.DASHBOARD_DATA, data, {
      expireTime: 24 * 60 * 60 * 1000 // 24小时过期
    })
  }

  /**
   * 获取仪表板数据
   */
  getDashboardData(): DashboardData | null {
    return this.getItem<DashboardData>(STORAGE_KEYS.DASHBOARD_DATA)
  }

  /**
   * 保存图表配置
   */
  saveChartConfig(config: ExtendedChartConfig): boolean {
    return this.setItem(STORAGE_KEYS.CHART_CONFIG, config)
  }

  /**
   * 获取图表配置
   */
  getChartConfig(): ExtendedChartConfig | null {
    return this.getItem<ExtendedChartConfig>(STORAGE_KEYS.CHART_CONFIG)
  }

  /**
   * 保存最后更新时间
   */
  saveLastUpdateTime(timestamp: number): boolean {
    return this.setItem(STORAGE_KEYS.LAST_UPDATE, timestamp)
  }

  /**
   * 获取最后更新时间
   */
  getLastUpdateTime(): number | null {
    return this.getItem<number>(STORAGE_KEYS.LAST_UPDATE)
  }

  /**
   * 导出所有数据
   */
  exportData(): Record<string, any> {
    const data: Record<string, any> = {}
    
    if (!this.isStorageAvailable()) return data

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(this.prefix)) {
        try {
          data[key] = JSON.parse(localStorage.getItem(key) || '{}')
        } catch {
          data[key] = localStorage.getItem(key)
        }
      }
    }

    return data
  }

  /**
   * 导入数据
   */
  importData(data: Record<string, any>): boolean {
    if (!this.isStorageAvailable()) return false

    try {
      Object.entries(data).forEach(([key, value]) => {
        if (key.startsWith(this.prefix)) {
          localStorage.setItem(key, typeof value === 'string' ? value : JSON.stringify(value))
        }
      })
      return true
    } catch (error) {
      console.error('导入数据失败:', error)
      return false
    }
  }

  /**
   * 验证仪表板配置数据
   */
  validateDashboardConfig(config: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!config || typeof config !== 'object') {
      errors.push('配置不是有效的对象')
      return { isValid: false, errors }
    }

    if (!config.taskId || typeof config.taskId !== 'string') {
      errors.push('缺少有效的taskId')
    }

    if (!config.charts || !Array.isArray(config.charts)) {
      errors.push('charts字段必须是数组')
    } else {
      config.charts.forEach((chart: any, index: number) => {
        if (!chart.id || typeof chart.id !== 'string') {
          errors.push(`图表${index + 1}缺少有效的id`)
        }
        if (!chart.type || typeof chart.type !== 'string') {
          errors.push(`图表${index + 1}缺少有效的type`)
        }
        if (!chart.title || typeof chart.title !== 'string') {
          errors.push(`图表${index + 1}缺少有效的title`)
        }
        if (!chart.position || typeof chart.position !== 'object') {
          errors.push(`图表${index + 1}缺少有效的position`)
        }
      })
    }

    return { isValid: errors.length === 0, errors }
  }

  /**
   * 清理无效的仪表板配置
   */
  cleanupInvalidConfigs(): number {
    if (!this.isStorageAvailable()) return 0

    let cleanedCount = 0
    const keysToRemove: string[] = []

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.includes('dashboard-config-')) {
        try {
          const configStr = localStorage.getItem(key)
          if (configStr) {
            const config = JSON.parse(configStr)
            const validation = this.validateDashboardConfig(config)
            if (!validation.isValid) {
              console.warn(`发现无效配置 ${key}:`, validation.errors)
              keysToRemove.push(key)
            }
          }
        } catch (error) {
          console.warn(`配置解析失败 ${key}:`, error)
          keysToRemove.push(key)
        }
      }
    }

    // 删除无效配置
    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
      cleanedCount++
    })

    if (cleanedCount > 0) {
      console.log(`已清理 ${cleanedCount} 个无效配置`)
    }

    return cleanedCount
  }

  /**
   * 获取所有仪表板配置键
   */
  getDashboardConfigKeys(): string[] {
    if (!this.isStorageAvailable()) return []

    const keys: string[] = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.includes('dashboard-config-')) {
        keys.push(key)
      }
    }
    return keys
  }
}

// 导出单例实例
export const storageService = StorageService.getInstance()
