/**
 * 任务关系数据服务
 */

import type { 
  TaskRelationConfig, 
  ExtendedChartType,
  RadarChartData,
  ScatterChartData,
  MapChartData,
  HeatmapChartData
} from '../types/dashboard.types'

// 模拟任务关系数据
export interface TaskRelationData {
  nodes: TaskNode[]
  edges: TaskEdge[]
  config: TaskRelationConfig
}

export interface TaskNode {
  id: string
  name: string
  type: 'main' | 'sub' | 'completed' | 'in-progress' | 'pending'
  position: { x: number; y: number }
  data: {
    description?: string
    assignee?: string
    deadline?: string
    priority?: 'high' | 'medium' | 'low'
  }
}

export interface TaskEdge {
  id: string
  source: string
  target: string
  type: 'dependency' | 'sequence' | 'parallel' | 'conditional'
  label?: string
}

// 任务关系数据服务类
export class TaskRelationService {
  private static instance: TaskRelationService
  private relationData: Map<string, TaskRelationData> = new Map()

  static getInstance(): TaskRelationService {
    if (!TaskRelationService.instance) {
      TaskRelationService.instance = new TaskRelationService()
    }
    return TaskRelationService.instance
  }

  // 获取任务关系数据
  getTaskRelationData(chartType: ExtendedChartType, relationType: 'business' | 'temporary'): TaskRelationData | null {
    const key = `${chartType}-${relationType}`
    return this.relationData.get(key) || null
  }

  // 保存任务关系数据
  saveTaskRelationData(chartType: ExtendedChartType, relationType: 'business' | 'temporary', data: TaskRelationData): void {
    const key = `${chartType}-${relationType}`
    this.relationData.set(key, data)
  }

  // 生成模拟任务关系数据
  generateMockData(chartType: ExtendedChartType, relationType: 'business' | 'temporary'): TaskRelationData {
    const nodes: TaskNode[] = [
      {
        id: 'task-1',
        name: '数据收集',
        type: 'completed',
        position: { x: 100, y: 100 },
        data: {
          description: '收集业务数据',
          assignee: '张三',
          deadline: '2024-01-15',
          priority: 'high'
        }
      },
      {
        id: 'task-2',
        name: '数据处理',
        type: 'in-progress',
        position: { x: 300, y: 100 },
        data: {
          description: '处理和清洗数据',
          assignee: '李四',
          deadline: '2024-01-20',
          priority: 'medium'
        }
      },
      {
        id: 'task-3',
        name: '图表生成',
        type: 'pending',
        position: { x: 500, y: 100 },
        data: {
          description: '生成可视化图表',
          assignee: '王五',
          deadline: '2024-01-25',
          priority: 'medium'
        }
      }
    ]

    const edges: TaskEdge[] = [
      {
        id: 'edge-1',
        source: 'task-1',
        target: 'task-2',
        type: 'dependency',
        label: '数据依赖'
      },
      {
        id: 'edge-2',
        source: 'task-2',
        target: 'task-3',
        type: 'sequence',
        label: '顺序执行'
      }
    ]

    return {
      nodes,
      edges,
      config: {
        type: relationType,
        enabled: true,
        relationshipType: 'dependency',
        showLabels: true,
        nodeStyle: {
          size: 30,
          color: '#409EFF',
          borderColor: '#ffffff',
          borderWidth: 2
        },
        edgeStyle: {
          color: '#909399',
          width: 2,
          type: 'solid'
        }
      }
    }
  }

  // 将任务关系数据转换为图表数据
  convertToChartData(relationData: TaskRelationData, chartType: ExtendedChartType): any {
    switch (chartType) {
      case 'radar-chart':
        return this.convertToRadarData(relationData)
      case 'scatter-chart':
        return this.convertToScatterData(relationData)
      case 'map-chart':
        return this.convertToMapData(relationData)
      case 'heatmap':
        return this.convertToHeatmapData(relationData)
      default:
        return null
    }
  }

  // 转换为雷达图数据
  private convertToRadarData(relationData: TaskRelationData): RadarChartData {
    const indicators = [
      { name: '完成度', max: 100 },
      { name: '优先级', max: 10 },
      { name: '复杂度', max: 10 },
      { name: '依赖度', max: 10 },
      { name: '风险度', max: 10 }
    ]

    const series = relationData.nodes.map(node => ({
      name: node.name,
      data: [
        node.type === 'completed' ? 100 : node.type === 'in-progress' ? 60 : 20,
        node.data.priority === 'high' ? 9 : node.data.priority === 'medium' ? 6 : 3,
        Math.floor(Math.random() * 10) + 1,
        relationData.edges.filter(e => e.target === node.id).length * 2,
        Math.floor(Math.random() * 10) + 1
      ]
    }))

    return { indicator: indicators, series }
  }

  // 转换为散点图数据
  private convertToScatterData(relationData: TaskRelationData): ScatterChartData {
    const xAxis = { type: 'value' as const }
    const yAxis = { type: 'value' as const }

    const series = [{
      name: '任务分布',
      data: relationData.nodes.map(node => [
        node.position.x,
        node.position.y
      ] as [number, number])
    }]

    return { xAxis, yAxis, series }
  }

  // 转换为地图数据
  private convertToMapData(relationData: TaskRelationData): MapChartData {
    const series = [{
      name: '任务分布',
      data: [
        { name: '北京', value: relationData.nodes.filter(n => n.type === 'completed').length },
        { name: '上海', value: relationData.nodes.filter(n => n.type === 'in-progress').length },
        { name: '广州', value: relationData.nodes.filter(n => n.type === 'pending').length }
      ]
    }]

    return { mapName: 'china', series }
  }

  // 转换为热力图数据
  private convertToHeatmapData(relationData: TaskRelationData): HeatmapChartData {
    const xAxis = ['周一', '周二', '周三', '周四', '周五']
    const yAxis = relationData.nodes.map(node => node.name)

    const data: [number, number, number][] = []
    yAxis.forEach((_, yIndex) => {
      xAxis.forEach((_, xIndex) => {
        data.push([xIndex, yIndex, Math.floor(Math.random() * 100)])
      })
    })

    return { xAxis, yAxis, data }
  }

  // 获取或生成图表数据
  getChartDataWithRelation(chartType: ExtendedChartType, relationType: 'business' | 'temporary'): any {
    let relationData = this.getTaskRelationData(chartType, relationType)
    
    if (!relationData) {
      relationData = this.generateMockData(chartType, relationType)
      this.saveTaskRelationData(chartType, relationType, relationData)
    }

    return this.convertToChartData(relationData, chartType)
  }
}

// 导出单例实例
export const taskRelationService = TaskRelationService.getInstance()
