/**
 * 图表类型配置数据
 */

import {
  Histogram,
  TrendCharts,
  PieChart,
  Grid,
  DataBoard,
  Location,
  Sunny
} from '@element-plus/icons-vue'
import type { ExtendedChartType } from '../types/dashboard.types'
import type {
  RadarChartConfig,
  ScatterChartConfig,
  MapChartConfig,
  HeatmapChartConfig,
  DonutChartConfig,
  MultiDonutChartConfig,
  CompositePieChartConfig,
  StackedChartConfig,
  TaskRelationConfig
} from '../types/dashboard.types'

// 图表类型信息接口
export interface ChartTypeInfo {
  id: ExtendedChartType
  name: string
  category: 'basic' | 'advanced'
  icon: any
  description: string
  supportsBusiness: boolean
  supportsTemporary: boolean
  defaultConfig: any
  features: string[]
  disabled?: boolean
}

// 基础图表类型配置
export const basicChartTypes: ChartTypeInfo[] = [
  {
    id: 'bar-chart',
    name: '柱状图',
    category: 'basic',
    icon: Histogram,
    description: '用于比较不同类别的数据',
    supportsBusiness: true,
    supportsTemporary: true,
    features: ['数据对比', '分类展示', '趋势分析'],
    defaultConfig: {
      orientation: 'vertical',
      showDataLabels: true,
      barWidth: 60,
      spacing: 10
    }
  },
  {
    id: 'line-chart',
    name: '折线图',
    category: 'basic',
    icon: TrendCharts,
    description: '用于显示数据随时间的变化趋势',
    supportsBusiness: true,
    supportsTemporary: true,
    features: ['时间序列', '趋势分析', '数据连续性'],
    defaultConfig: {
      smooth: false,
      showPoints: true,
      showArea: false,
      lineWidth: 2
    }
  },
  {
    id: 'pie-chart',
    name: '饼图',
    category: 'basic',
    icon: PieChart,
    description: '用于显示数据的比例关系',
    supportsBusiness: true,
    supportsTemporary: true,
    features: ['比例展示', '占比分析', '分类统计'],
    defaultConfig: {
      showPercentage: true,
      innerRadius: 0,
      outerRadius: 80,
      startAngle: 90
    }
  },
  {
    id: 'radar-chart',
    name: '雷达图',
    category: 'basic',
    icon: DataBoard,
    description: '用于多维度数据的对比分析',
    supportsBusiness: true,
    supportsTemporary: false,
    features: ['多维对比', '能力评估', '综合分析'],
    defaultConfig: {
      shape: 'polygon',
      splitNumber: 5,
      axisName: {
        show: true
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#e0e6ed',
          width: 1
        }
      },
      splitArea: {
        show: false,
        areaStyle: {
          color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
        }
      }
    } as RadarChartConfig
  },
  {
    id: 'scatter-chart',
    name: '散点图',
    category: 'basic',
    icon: Grid,
    description: '用于显示两个变量之间的关系',
    supportsBusiness: true,
    supportsTemporary: true,
    features: ['相关性分析', '分布展示', '异常检测'],
    defaultConfig: {
      symbolSize: 8,
      showSymbol: true,
      smooth: false,
      connectNulls: false,
      emphasis: {
        focus: 'series',
        scale: true
      }
    } as ScatterChartConfig
  },
  {
    id: 'map-chart',
    name: '地图',
    category: 'basic',
    icon: Location,
    description: '用于显示地理位置相关的数据',
    supportsBusiness: true,
    supportsTemporary: false,
    features: ['地理分布', '区域分析', '空间统计'],
    defaultConfig: {
      map: 'china',
      roam: true,
      zoom: 1,
      aspectScale: 0.75
    } as MapChartConfig
  },
  {
    id: 'heatmap',
    name: '热力图',
    category: 'basic',
    icon: Sunny,
    description: '用于显示数据密度和分布',
    supportsBusiness: true,
    supportsTemporary: true,
    features: ['密度分析', '热点识别', '模式发现'],
    defaultConfig: {
      xAxis: {
        type: 'category'
      },
      yAxis: {
        type: 'category'
      },
      visualMap: {
        min: 0,
        max: 100,
        calculable: true,
        orient: 'vertical',
        left: 'right',
        bottom: 'bottom'
      }
    } as HeatmapChartConfig
  }
]

// 高级图表类型配置
export const advancedChartTypes: ChartTypeInfo[] = [
  {
    id: 'donut-chart',
    name: '环形图',
    category: 'advanced',
    icon: PieChart,
    description: '饼图的变体，中心留空',
    supportsBusiness: true,
    supportsTemporary: true,
    features: ['中心留空', '层次展示', '美观设计'],
    defaultConfig: {
      showPercentage: true,
      innerRadius: 40,
      outerRadius: 80,
      startAngle: 90,
      roseType: false
    } as DonutChartConfig
  },
  {
    id: 'multi-donut-chart',
    name: '多层环形图',
    category: 'advanced',
    icon: PieChart,
    description: '多层数据的环形展示',
    supportsBusiness: true,
    supportsTemporary: false,
    features: ['多层展示', '层级关系', '复杂数据'],
    defaultConfig: {
      theme: 'light',
      animation: true,
      responsive: true,
      showLegend: true,
      showTooltip: true,
      series: [
        {
          name: '内层',
          innerRadius: 0,
          outerRadius: 40,
          data: []
        },
        {
          name: '外层',
          innerRadius: 50,
          outerRadius: 80,
          data: []
        }
      ]
    } as MultiDonutChartConfig
  },
  {
    id: 'composite-pie-chart',
    name: '复合饼图',
    category: 'advanced',
    icon: PieChart,
    description: '多个饼图的组合展示',
    supportsBusiness: true,
    supportsTemporary: true,
    features: ['多图组合', '对比分析', '并列展示'],
    defaultConfig: {
      theme: 'light',
      animation: true,
      responsive: true,
      showLegend: true,
      showTooltip: true,
      series: [
        {
          name: '左侧饼图',
          type: 'pie',
          radius: 50,
          center: ['25%', '50%'],
          data: []
        },
        {
          name: '右侧饼图',
          type: 'pie',
          radius: 50,
          center: ['75%', '50%'],
          data: []
        }
      ]
    } as CompositePieChartConfig
  },
  {
    id: 'stacked-line-chart',
    name: '堆积折线图',
    category: 'advanced',
    icon: TrendCharts,
    description: '多系列数据的堆积展示',
    supportsBusiness: true,
    supportsTemporary: true,
    features: ['数据堆积', '总量展示', '构成分析'],
    defaultConfig: {
      theme: 'light',
      animation: true,
      responsive: true,
      showLegend: true,
      showTooltip: true,
      stack: 'total',
      stackStrategy: 'samesign',
      percentage: false,
      smooth: false,
      showPoints: true,
      showArea: true,
      lineWidth: 2
    } as StackedChartConfig & { smooth: boolean; showPoints: boolean; showArea: boolean; lineWidth: number }
  },
  {
    id: 'percentage-stacked-line-chart',
    name: '百分比堆积折线图',
    category: 'advanced',
    icon: TrendCharts,
    description: '百分比形式的堆积折线图',
    supportsBusiness: true,
    supportsTemporary: false,
    features: ['百分比展示', '比例分析', '构成变化'],
    defaultConfig: {
      theme: 'light',
      animation: true,
      responsive: true,
      showLegend: true,
      showTooltip: true,
      stack: 'total',
      stackStrategy: 'samesign',
      percentage: true,
      smooth: false,
      showPoints: true,
      showArea: true,
      lineWidth: 2
    } as StackedChartConfig & { smooth: boolean; showPoints: boolean; showArea: boolean; lineWidth: number }
  },
  {
    id: 'stacked-bar-chart',
    name: '堆积柱状图',
    category: 'advanced',
    icon: Histogram,
    description: '多系列数据的堆积柱状图',
    supportsBusiness: true,
    supportsTemporary: true,
    features: ['数据堆积', '分类对比', '构成分析'],
    defaultConfig: {
      theme: 'light',
      animation: true,
      responsive: true,
      showLegend: true,
      showTooltip: true,
      stack: 'total',
      stackStrategy: 'samesign',
      percentage: false,
      orientation: 'vertical',
      showDataLabels: true,
      barWidth: 60,
      spacing: 10
    } as StackedChartConfig & { orientation: string; showDataLabels: boolean; barWidth: number; spacing: number }
  },
  {
    id: 'percentage-stacked-bar-chart',
    name: '百分比堆积柱状图',
    category: 'advanced',
    icon: Histogram,
    description: '百分比形式的堆积柱状图',
    supportsBusiness: true,
    supportsTemporary: false,
    features: ['百分比展示', '比例对比', '构成分析'],
    defaultConfig: {
      theme: 'light',
      animation: true,
      responsive: true,
      showLegend: true,
      showTooltip: true,
      stack: 'total',
      stackStrategy: 'samesign',
      percentage: true,
      orientation: 'vertical',
      showDataLabels: true,
      barWidth: 60,
      spacing: 10
    } as StackedChartConfig & { orientation: string; showDataLabels: boolean; barWidth: number; spacing: number }
  },
  {
    id: 'multi-pie-chart',
    name: '多饼图',
    category: 'advanced',
    icon: PieChart,
    description: '多个独立饼图的展示',
    supportsBusiness: true,
    supportsTemporary: true,
    features: ['多图展示', '独立分析', '对比展示'],
    defaultConfig: {
      theme: 'light',
      animation: true,
      responsive: true,
      showLegend: true,
      showTooltip: true
    }
  },
  {
    id: '3d-pie-chart',
    name: '3D饼图',
    category: 'advanced',
    icon: PieChart,
    description: '立体效果的饼图',
    supportsBusiness: true,
    supportsTemporary: false,
    features: ['3D效果', '立体展示', '视觉冲击'],
    defaultConfig: {
      theme: 'light',
      animation: true,
      responsive: true,
      showLegend: true,
      showTooltip: true,
      depth: 20
    }
  },
  {
    id: 'smooth-scatter-chart',
    name: '平滑散点图',
    category: 'advanced',
    icon: Grid,
    description: '带平滑曲线的散点图',
    supportsBusiness: true,
    supportsTemporary: true,
    features: ['平滑曲线', '趋势展示', '数据拟合'],
    defaultConfig: {
      symbolSize: 8,
      showSymbol: true,
      smooth: true,
      connectNulls: true,
      emphasis: {
        focus: 'series',
        scale: true
      }
    }
  },
  {
    id: 'data-point-line-chart',
    name: '数据点折线图',
    category: 'advanced',
    icon: TrendCharts,
    description: '突出显示数据点的折线图',
    supportsBusiness: true,
    supportsTemporary: true,
    features: ['数据点突出', '精确展示', '详细分析'],
    defaultConfig: {
      smooth: false,
      showPoints: true,
      showArea: false,
      lineWidth: 2,
      symbolSize: 10,
      emphasis: {
        focus: 'series',
        scale: true
      }
    }
  }
]

// 所有图表类型
export const allChartTypes = [...basicChartTypes, ...advancedChartTypes]

// 根据ID获取图表类型信息
export const getChartTypeById = (id: ExtendedChartType): ChartTypeInfo | undefined => {
  return allChartTypes.find(type => type.id === id)
}

// 根据分类获取图表类型
export const getChartTypesByCategory = (category: 'basic' | 'advanced' | 'all'): ChartTypeInfo[] => {
  switch (category) {
    case 'basic':
      return basicChartTypes
    case 'advanced':
      return advancedChartTypes
    default:
      return allChartTypes
  }
}

// 任务关系默认配置
export const defaultTaskRelationConfig: TaskRelationConfig = {
  type: 'business',
  enabled: false,
  relationshipType: 'dependency',
  showLabels: true,
  nodeStyle: {
    size: 30,
    color: '#409EFF',
    borderColor: '#ffffff',
    borderWidth: 2
  },
  edgeStyle: {
    color: '#909399',
    width: 2,
    type: 'solid'
  }
}
