/**
 * 本地存储 Hook
 * 提供响应式的本地存储功能
 */

import { ref, watch, type Ref } from 'vue'

/**
 * 序列化器接口
 */
export interface Serializer<T> {
  read: (value: string) => T
  write: (value: T) => string
}

/**
 * useLocalStorage配置选项
 */
export interface UseLocalStorageOptions<T> {
  defaultValue: T
  serializer?: Serializer<T>
  syncAcrossTabs?: boolean
  onError?: (error: Error) => void
}

/**
 * 默认序列化器
 */
const defaultSerializer: Serializer<any> = {
  read: (value: string) => {
    try {
      return JSON.parse(value)
    } catch {
      return value
    }
  },
  write: (value: any) => {
    try {
      return JSON.stringify(value)
    } catch {
      return String(value)
    }
  }
}

/**
 * 字符串序列化器
 */
const stringSerializer: Serializer<string> = {
  read: (value: string) => value,
  write: (value: string) => value
}

/**
 * 数字序列化器
 */
const numberSerializer: Serializer<number> = {
  read: (value: string) => Number(value),
  write: (value: number) => String(value)
}

/**
 * 布尔值序列化器
 */
const booleanSerializer: Serializer<boolean> = {
  read: (value: string) => value === 'true',
  write: (value: boolean) => String(value)
}

/**
 * 获取合适的序列化器
 */
function getSerializer<T>(defaultValue: T): Serializer<T> {
  if (typeof defaultValue === 'string') {
    return stringSerializer as unknown as Serializer<T>
  }
  if (typeof defaultValue === 'number') {
    return numberSerializer as unknown as Serializer<T>
  }
  if (typeof defaultValue === 'boolean') {
    return booleanSerializer as unknown as Serializer<T>
  }
  return defaultSerializer
}

/**
 * 检查localStorage是否可用
 */
function isStorageAvailable(): boolean {
  try {
    const test = '__localStorage_test__'
    localStorage.setItem(test, test)
    localStorage.removeItem(test)
    return true
  } catch {
    return false
  }
}

/**
 * useLocalStorage Hook
 */
export function useLocalStorage<T>(
  key: string,
  options: UseLocalStorageOptions<T>
): [Ref<T>, (value: T) => void, () => void] {
  const {
    defaultValue,
    serializer = getSerializer(defaultValue),
    syncAcrossTabs = true,
    onError = (error) => console.error('localStorage error:', error)
  } = options

  const storedValue = ref<T>(defaultValue) as Ref<T>

  /**
   * 从localStorage读取值
   */
  const read = (): T => {
    if (!isStorageAvailable()) {
      return defaultValue
    }

    try {
      const item = localStorage.getItem(key)
      if (item === null) {
        return defaultValue
      }
      return serializer.read(item)
    } catch (error) {
      onError(error as Error)
      return defaultValue
    }
  }

  /**
   * 写入值到localStorage
   */
  const write = (value: T): void => {
    if (!isStorageAvailable()) {
      return
    }

    try {
      if (value === null || value === undefined) {
        localStorage.removeItem(key)
      } else {
        localStorage.setItem(key, serializer.write(value))
      }
    } catch (error) {
      onError(error as Error)
    }
  }

  /**
   * 删除localStorage中的值
   */
  const remove = (): void => {
    if (!isStorageAvailable()) {
      return
    }

    try {
      localStorage.removeItem(key)
      storedValue.value = defaultValue
    } catch (error) {
      onError(error as Error)
    }
  }

  /**
   * 设置值
   */
  const setValue = (value: T): void => {
    storedValue.value = value
    write(value)
  }

  // 初始化时读取存储的值
  storedValue.value = read()

  // 监听值的变化并同步到localStorage
  watch(
    storedValue,
    (newValue) => {
      write(newValue)
    },
    { deep: true }
  )

  // 跨标签页同步
  if (syncAcrossTabs && isStorageAvailable()) {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          const newValue = serializer.read(e.newValue)
          storedValue.value = newValue
        } catch (error) {
          onError(error as Error)
        }
      }
    }

    window.addEventListener('storage', handleStorageChange)

    // 清理函数
    const cleanup = () => {
      window.removeEventListener('storage', handleStorageChange)
    }

    // 在Vue组件卸载时自动清理（如果在组件中使用）
    if (typeof window !== 'undefined' && 'onUnmounted' in window) {
      const { onUnmounted } = require('vue')
      onUnmounted(cleanup)
    }
  }

  return [storedValue, setValue, remove]
}

/**
 * 简化版本的useLocalStorage，只返回响应式值
 */
export function useLocalStorageState<T>(
  key: string,
  defaultValue: T,
  options?: Partial<UseLocalStorageOptions<T>>
): Ref<T> {
  const [storedValue] = useLocalStorage(key, {
    defaultValue,
    ...options
  })
  return storedValue
}

/**
 * 批量操作localStorage
 */
export function useBatchLocalStorage() {
  const operations: Array<() => void> = []

  const addOperation = (operation: () => void) => {
    operations.push(operation)
  }

  const execute = () => {
    operations.forEach(operation => {
      try {
        operation()
      } catch (error) {
        console.error('批量操作失败:', error)
      }
    })
    operations.length = 0
  }

  const clear = () => {
    operations.length = 0
  }

  return {
    addOperation,
    execute,
    clear
  }
}

/**
 * localStorage容量管理
 */
export function useStorageCapacity() {
  const getUsage = () => {
    if (!isStorageAvailable()) {
      return { used: 0, available: 0, percentage: 0 }
    }

    let used = 0
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        used += localStorage[key].length + key.length
      }
    }

    const available = 5 * 1024 * 1024 // 估算5MB
    const percentage = (used / available) * 100

    return { used, available, percentage }
  }

  const clearExpired = (prefix?: string) => {
    if (!isStorageAvailable()) return

    const keysToRemove: string[] = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && (!prefix || key.startsWith(prefix))) {
        try {
          const item = JSON.parse(localStorage.getItem(key) || '{}')
          if (item.expireTime && Date.now() > item.expireTime) {
            keysToRemove.push(key)
          }
        } catch {
          // 解析失败的项也可以考虑清理
        }
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key))
    return keysToRemove.length
  }

  return {
    getUsage,
    clearExpired
  }
}
