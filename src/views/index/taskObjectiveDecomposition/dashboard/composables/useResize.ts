/**
 * 图表大小调整功能 Composable
 */

import { ref, computed } from 'vue'
import type { ChartInstance } from './useChartManager'

export interface ResizeState {
  isResizing: boolean
  resizeChart: ChartInstance | null
  resizeHandle: string | null
  startSize: { width: number; height: number }
  startPosition: { x: number; y: number }
  startMouse: { x: number; y: number }
}

export interface ResizeOptions {
  minWidth?: number
  minHeight?: number
  maxWidth?: number
  maxHeight?: number
  aspectRatio?: number
  snapToGrid?: boolean
  gridSize?: number
}

export function useResize(options: ResizeOptions = {}) {
  const defaultOptions = {
    minWidth: 200,
    minHeight: 150,
    maxWidth: 1200,
    maxHeight: 800,
    snapToGrid: false,
    gridSize: 20,
    ...options
  }

  // 调整状态
  const resizeState = ref<ResizeState>({
    isResizing: false,
    resizeChart: null,
    resizeHandle: null,
    startSize: { width: 0, height: 0 },
    startPosition: { x: 0, y: 0 },
    startMouse: { x: 0, y: 0 }
  })

  // 计算属性
  const isResizing = computed(() => resizeState.value.isResizing)
  const resizeChart = computed(() => resizeState.value.resizeChart)

  // 开始调整大小
  const startResize = (
    chart: ChartInstance,
    handle: string,
    event: MouseEvent | TouchEvent,
    onUpdate?: (chart: ChartInstance, size: { width: number; height: number }, position?: { x: number; y: number }) => void
  ) => {
    const clientX = 'touches' in event ? event.touches[0].clientX : event.clientX
    const clientY = 'touches' in event ? event.touches[0].clientY : event.clientY

    resizeState.value = {
      isResizing: true,
      resizeChart: chart,
      resizeHandle: handle,
      startSize: { width: chart.position.width, height: chart.position.height },
      startPosition: { x: chart.position.x, y: chart.position.y },
      startMouse: { x: clientX, y: clientY }
    }

    // 添加全局事件监听
    const handleMouseMove = (e: MouseEvent | TouchEvent) => {
      if (!resizeState.value.isResizing || !resizeState.value.resizeChart) return

      e.preventDefault()
      
      const moveClientX = 'touches' in e ? e.touches[0].clientX : e.clientX
      const moveClientY = 'touches' in e ? e.touches[0].clientY : e.clientY

      const deltaX = moveClientX - resizeState.value.startMouse.x
      const deltaY = moveClientY - resizeState.value.startMouse.y

      const newSize = calculateNewSize(
        resizeState.value.startSize,
        resizeState.value.resizeHandle!,
        deltaX,
        deltaY
      )

      const newPosition = calculateNewPosition(
        resizeState.value.startPosition,
        resizeState.value.resizeHandle!,
        resizeState.value.startSize,
        newSize
      )

      // 更新图表大小和位置
      if (onUpdate) {
        onUpdate(resizeState.value.resizeChart, newSize, newPosition)
      }
    }

    const handleMouseUp = () => {
      if (resizeState.value.isResizing) {
        endResize()
      }
    }

    // 绑定事件
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    document.addEventListener('touchmove', handleMouseMove, { passive: false })
    document.addEventListener('touchend', handleMouseUp)

    // 清理函数
    const cleanup = () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.removeEventListener('touchmove', handleMouseMove)
      document.removeEventListener('touchend', handleMouseUp)
    }

    // 存储清理函数
    ;(resizeState.value as any).cleanup = cleanup
  }

  // 计算新尺寸
  const calculateNewSize = (
    startSize: { width: number; height: number },
    handle: string,
    deltaX: number,
    deltaY: number
  ) => {
    let newWidth = startSize.width
    let newHeight = startSize.height

    switch (handle) {
      case 'se': // 右下角
        newWidth = startSize.width + deltaX
        newHeight = startSize.height + deltaY
        break
      case 'sw': // 左下角
        newWidth = startSize.width - deltaX
        newHeight = startSize.height + deltaY
        break
      case 'ne': // 右上角
        newWidth = startSize.width + deltaX
        newHeight = startSize.height - deltaY
        break
      case 'nw': // 左上角
        newWidth = startSize.width - deltaX
        newHeight = startSize.height - deltaY
        break
      case 'e': // 右边
        newWidth = startSize.width + deltaX
        break
      case 'w': // 左边
        newWidth = startSize.width - deltaX
        break
      case 's': // 下边
        newHeight = startSize.height + deltaY
        break
      case 'n': // 上边
        newHeight = startSize.height - deltaY
        break
    }

    // 应用约束
    newWidth = Math.max(defaultOptions.minWidth, Math.min(defaultOptions.maxWidth, newWidth))
    newHeight = Math.max(defaultOptions.minHeight, Math.min(defaultOptions.maxHeight, newHeight))

    // 保持宽高比
    if (defaultOptions.aspectRatio) {
      if (handle.includes('e') || handle.includes('w')) {
        newHeight = newWidth / defaultOptions.aspectRatio
      } else if (handle.includes('n') || handle.includes('s')) {
        newWidth = newHeight * defaultOptions.aspectRatio
      }
    }

    // 网格对齐
    if (defaultOptions.snapToGrid) {
      newWidth = Math.round(newWidth / defaultOptions.gridSize) * defaultOptions.gridSize
      newHeight = Math.round(newHeight / defaultOptions.gridSize) * defaultOptions.gridSize
    }

    return { width: newWidth, height: newHeight }
  }

  // 计算新位置（某些调整手柄需要调整位置）
  const calculateNewPosition = (
    startPosition: { x: number; y: number },
    handle: string,
    startSize: { width: number; height: number },
    newSize: { width: number; height: number }
  ) => {
    let newX = startPosition.x
    let newY = startPosition.y

    // 左边调整需要调整X位置
    if (handle.includes('w')) {
      newX = startPosition.x - (newSize.width - startSize.width)
    }

    // 上边调整需要调整Y位置
    if (handle.includes('n')) {
      newY = startPosition.y - (newSize.height - startSize.height)
    }

    return { x: Math.max(0, newX), y: Math.max(0, newY) }
  }

  // 结束调整
  const endResize = () => {
    if ((resizeState.value as any).cleanup) {
      ;(resizeState.value as any).cleanup()
    }

    resizeState.value = {
      isResizing: false,
      resizeChart: null,
      resizeHandle: null,
      startSize: { width: 0, height: 0 },
      startPosition: { x: 0, y: 0 },
      startMouse: { x: 0, y: 0 }
    }
  }

  // 获取调整手柄
  const getResizeHandles = () => {
    return [
      { name: 'nw', cursor: 'nw-resize', position: { top: '-4px', left: '-4px' } },
      { name: 'n', cursor: 'n-resize', position: { top: '-4px', left: '50%', transform: 'translateX(-50%)' } },
      { name: 'ne', cursor: 'ne-resize', position: { top: '-4px', right: '-4px' } },
      { name: 'e', cursor: 'e-resize', position: { top: '50%', right: '-4px', transform: 'translateY(-50%)' } },
      { name: 'se', cursor: 'se-resize', position: { bottom: '-4px', right: '-4px' } },
      { name: 's', cursor: 's-resize', position: { bottom: '-4px', left: '50%', transform: 'translateX(-50%)' } },
      { name: 'sw', cursor: 'sw-resize', position: { bottom: '-4px', left: '-4px' } },
      { name: 'w', cursor: 'w-resize', position: { top: '50%', left: '-4px', transform: 'translateY(-50%)' } }
    ]
  }

  // 获取调整手柄样式
  const getHandleStyle = (handle: { name: string; cursor: string; position: any }) => {
    return {
      position: 'absolute',
      width: '8px',
      height: '8px',
      background: '#409EFF',
      border: '1px solid white',
      borderRadius: '2px',
      cursor: handle.cursor,
      zIndex: 10,
      ...handle.position
    }
  }

  // 重置调整状态
  const resetResize = () => {
    endResize()
  }

  return {
    // 状态
    resizeState,
    isResizing,
    resizeChart,

    // 方法
    startResize,
    endResize,
    getResizeHandles,
    getHandleStyle,
    resetResize
  }
}
