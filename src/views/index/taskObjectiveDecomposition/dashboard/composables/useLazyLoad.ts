/**
 * 懒加载功能 Composable
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { ChartInstance } from './useChartManager'

export interface LazyLoadOptions {
  rootMargin?: string
  threshold?: number
  enabled?: boolean
}

export function useLazyLoad(options: LazyLoadOptions = {}) {
  const defaultOptions = {
    rootMargin: '50px',
    threshold: 0.1,
    enabled: true,
    ...options
  }

  // 可见性状态
  const visibleCharts = ref<Set<string>>(new Set())
  const observer = ref<IntersectionObserver | null>(null)
  const observedElements = ref<Map<string, Element>>(new Map())

  // 计算属性
  const isEnabled = computed(() => defaultOptions.enabled)

  // 初始化观察器
  const initObserver = () => {
    if (!defaultOptions.enabled || typeof IntersectionObserver === 'undefined') {
      return
    }

    observer.value = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const chartId = entry.target.getAttribute('data-chart-id')
          if (!chartId) return

          if (entry.isIntersecting) {
            visibleCharts.value.add(chartId)
          } else {
            visibleCharts.value.delete(chartId)
          }
        })
      },
      {
        rootMargin: defaultOptions.rootMargin,
        threshold: defaultOptions.threshold
      }
    )
  }

  // 观察元素
  const observe = (element: Element, chartId: string) => {
    if (!observer.value || !defaultOptions.enabled) {
      // 如果懒加载未启用，直接标记为可见
      visibleCharts.value.add(chartId)
      return
    }

    element.setAttribute('data-chart-id', chartId)
    observer.value.observe(element)
    observedElements.value.set(chartId, element)
  }

  // 停止观察元素
  const unobserve = (chartId: string) => {
    if (!observer.value) return

    const element = observedElements.value.get(chartId)
    if (element) {
      observer.value.unobserve(element)
      observedElements.value.delete(chartId)
    }
    visibleCharts.value.delete(chartId)
  }

  // 检查图表是否可见
  const isChartVisible = (chartId: string) => {
    if (!defaultOptions.enabled) return true
    return visibleCharts.value.has(chartId)
  }

  // 获取可见图表列表
  const getVisibleCharts = () => {
    return Array.from(visibleCharts.value)
  }

  // 获取可见图表数量
  const getVisibleChartCount = () => {
    return visibleCharts.value.size
  }

  // 预加载图表
  const preloadChart = (chartId: string) => {
    visibleCharts.value.add(chartId)
  }

  // 卸载图表
  const unloadChart = (chartId: string) => {
    visibleCharts.value.delete(chartId)
  }

  // 批量更新可见性
  const updateVisibility = (charts: ChartInstance[], containerElement?: Element) => {
    if (!defaultOptions.enabled || !containerElement) {
      // 如果懒加载未启用，所有图表都可见
      charts.forEach(chart => visibleCharts.value.add(chart.id))
      return
    }

    const containerRect = containerElement.getBoundingClientRect()
    const viewportHeight = window.innerHeight
    const viewportWidth = window.innerWidth

    charts.forEach(chart => {
      const chartRect = {
        top: chart.position.y,
        left: chart.position.x,
        bottom: chart.position.y + chart.position.height,
        right: chart.position.x + chart.position.width
      }

      // 检查图表是否在视口内或接近视口
      const margin = 100 // 提前加载边距
      const isVisible = (
        chartRect.right >= -margin &&
        chartRect.left <= viewportWidth + margin &&
        chartRect.bottom >= -margin &&
        chartRect.top <= viewportHeight + margin
      )

      if (isVisible) {
        visibleCharts.value.add(chart.id)
      } else {
        visibleCharts.value.delete(chart.id)
      }
    })
  }

  // 启用懒加载
  const enable = () => {
    defaultOptions.enabled = true
    initObserver()
  }

  // 禁用懒加载
  const disable = () => {
    defaultOptions.enabled = false
    if (observer.value) {
      observer.value.disconnect()
      observer.value = null
    }
    // 禁用时所有图表都可见
    observedElements.value.forEach((_, chartId) => {
      visibleCharts.value.add(chartId)
    })
  }

  // 重置状态
  const reset = () => {
    visibleCharts.value.clear()
    observedElements.value.clear()
    if (observer.value) {
      observer.value.disconnect()
    }
    initObserver()
  }

  // 获取性能统计
  const getPerformanceStats = () => {
    return {
      totalObserved: observedElements.value.size,
      visibleCount: visibleCharts.value.size,
      hiddenCount: observedElements.value.size - visibleCharts.value.size,
      enabled: defaultOptions.enabled
    }
  }

  // 生命周期
  onMounted(() => {
    initObserver()
  })

  onUnmounted(() => {
    if (observer.value) {
      observer.value.disconnect()
    }
  })

  return {
    // 状态
    visibleCharts,
    isEnabled,

    // 方法
    observe,
    unobserve,
    isChartVisible,
    getVisibleCharts,
    getVisibleChartCount,
    preloadChart,
    unloadChart,
    updateVisibility,
    enable,
    disable,
    reset,
    getPerformanceStats
  }
}
