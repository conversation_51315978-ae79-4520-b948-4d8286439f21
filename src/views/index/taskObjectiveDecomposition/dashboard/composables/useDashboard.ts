/**
 * 仪表板数据管理 Hook
 * 统一管理仪表板的数据获取、更新和状态
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ViewType, ErrorType } from '../types/dashboard.types'
import type {
  DashboardData,
  ChartConfig,
  DashboardState,
  DashboardError
} from '../types/dashboard.types'
import { generateDashboardData, updateDashboardData } from '../services/mockDataService'
import { storageService } from '../services/storageService'
import { useLocalStorage } from './useLocalStorage'

/**
 * 默认图表配置
 */
const defaultChartConfig: ChartConfig = {
  common: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true
  },
  barChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    orientation: 'vertical',
    showDataLabels: false,
    barWidth: 40,
    spacing: 10
  },
  lineChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    smooth: true,
    showPoints: true,
    showArea: false,
    lineWidth: 2
  },
  pieChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    showPercentage: true,
    innerRadius: 0,
    outerRadius: 80,
    startAngle: 90
  }
}

/**
 * useDashboard Hook
 */
export function useDashboard() {
  // 基础状态
  const loading = ref(false)
  const error = ref<DashboardError | null>(null)
  const currentView = ref<ViewType>(ViewType.OVERVIEW)
  const isConfigPanelVisible = ref(false)

  // 数据状态
  const dashboardData = ref<DashboardData | null>(null)
  
  // 配置状态 - 使用localStorage持久化
  const [chartConfig, setChartConfig] = useLocalStorage('dashboard_chart_config', {
    defaultValue: defaultChartConfig
  })

  // 自动刷新定时器
  let refreshTimer: NodeJS.Timeout | null = null
  const autoRefreshInterval = ref(30000) // 30秒

  /**
   * 计算属性
   */
  const dashboardState = computed<DashboardState>(() => ({
    currentView: currentView.value,
    dashboardData: dashboardData.value!,
    chartConfig: chartConfig.value,
    isConfigPanelVisible: isConfigPanelVisible.value,
    loading: loading.value
  }))

  const hasData = computed(() => dashboardData.value !== null)

  const lastUpdateTime = computed(() => {
    return dashboardData.value?.lastUpdated || null
  })

  /**
   * 错误处理
   */
  const handleError = (type: ErrorType, message: string, details?: any) => {
    const dashboardError: DashboardError = {
      type,
      message,
      details,
      timestamp: new Date()
    }
    
    error.value = dashboardError
    console.error(`[${type}] ${message}`, details)
    
    // 可以在这里添加错误上报逻辑
  }

  const clearError = () => {
    error.value = null
  }

  /**
   * 数据加载
   */
  const loadData = async (useCache = true): Promise<void> => {
    loading.value = true
    clearError()

    try {
      let data: DashboardData | null = null

      // 尝试从缓存加载
      if (useCache) {
        data = storageService.getDashboardData()
      }

      // 如果没有缓存数据或不使用缓存，生成新数据
      if (!data) {
        data = generateDashboardData()
        
        // 保存到缓存
        const saved = storageService.saveDashboardData(data)
        if (!saved) {
          handleError(ErrorType.STORAGE_ERROR, '保存数据到本地存储失败')
        }
      }

      dashboardData.value = data
    } catch (err) {
      handleError(ErrorType.DATA_LOAD_ERROR, '加载数据失败', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 刷新数据
   */
  const refreshData = async (): Promise<void> => {
    if (!dashboardData.value) {
      await loadData(false)
      return
    }

    loading.value = true
    clearError()

    try {
      const updatedData = updateDashboardData(dashboardData.value)
      dashboardData.value = updatedData

      // 保存到缓存
      const saved = storageService.saveDashboardData(updatedData)
      if (!saved) {
        handleError(ErrorType.STORAGE_ERROR, '保存更新数据失败')
      }
    } catch (err) {
      handleError(ErrorType.DATA_LOAD_ERROR, '刷新数据失败', err)
    } finally {
      loading.value = false
    }
  }

  /**
   * 视图切换
   */
  const switchView = (view: ViewType) => {
    currentView.value = view
  }

  /**
   * 配置面板控制
   */
  const toggleConfigPanel = () => {
    isConfigPanelVisible.value = !isConfigPanelVisible.value
  }

  const showConfigPanel = () => {
    isConfigPanelVisible.value = true
  }

  const hideConfigPanel = () => {
    isConfigPanelVisible.value = false
  }

  /**
   * 图表配置更新
   */
  const updateChartConfig = (newConfig: Partial<ChartConfig>) => {
    try {
      const updatedConfig = {
        ...chartConfig.value,
        ...newConfig
      }
      setChartConfig(updatedConfig)
    } catch (err) {
      handleError(ErrorType.CONFIG_SAVE_ERROR, '保存配置失败', err)
    }
  }

  const resetChartConfig = () => {
    setChartConfig(defaultChartConfig)
  }

  /**
   * 自动刷新控制
   */
  const startAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
    }

    refreshTimer = setInterval(() => {
      refreshData()
    }, autoRefreshInterval.value)
  }

  const stopAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
    }
  }

  const setAutoRefreshInterval = (interval: number) => {
    autoRefreshInterval.value = interval
    if (refreshTimer) {
      stopAutoRefresh()
      startAutoRefresh()
    }
  }

  /**
   * 数据导出
   */
  const exportData = () => {
    if (!dashboardData.value) return null

    const exportData = {
      dashboardData: dashboardData.value,
      chartConfig: chartConfig.value,
      exportTime: new Date().toISOString()
    }

    return JSON.stringify(exportData, null, 2)
  }

  /**
   * 清理数据
   */
  const clearData = () => {
    dashboardData.value = null
    storageService.clear()
  }

  // 生命周期
  onMounted(async () => {
    await loadData()
    startAutoRefresh()
  })

  onUnmounted(() => {
    stopAutoRefresh()
  })

  return {
    // 状态
    loading,
    error,
    currentView,
    isConfigPanelVisible,
    dashboardData,
    chartConfig,
    dashboardState,
    hasData,
    lastUpdateTime,

    // 方法
    loadData,
    refreshData,
    switchView,
    toggleConfigPanel,
    showConfigPanel,
    hideConfigPanel,
    updateChartConfig,
    resetChartConfig,
    startAutoRefresh,
    stopAutoRefresh,
    setAutoRefreshInterval,
    exportData,
    clearData,
    clearError,

    // 工具
    handleError
  }
}
