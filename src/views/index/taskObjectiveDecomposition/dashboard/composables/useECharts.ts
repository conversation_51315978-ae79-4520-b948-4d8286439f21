/**
 * ECharts Hook
 * 用于管理ECharts实例的创建、更新和销毁
 */

import { ref, onMounted, onUnmounted, nextTick, type Ref } from 'vue'
import * as echarts from 'echarts/core'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Heatmap<PERSON>hart
} from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent,
  VisualMapComponent,
  GeoComponent,
  CalendarComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import type { EChartsOption } from 'echarts'

// 注册必要的组件
echarts.use([
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Pie<PERSON><PERSON>,
  Radar<PERSON><PERSON>,
  Scatter<PERSON>hart,
  MapChart,
  HeatmapChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent,
  VisualMapComponent,
  GeoComponent,
  CalendarComponent,
  CanvasRenderer
])

/**
 * useECharts配置选项
 */
export interface UseEChartsOptions {
  autoResize?: boolean
  theme?: string
  renderer?: 'canvas' | 'svg'
  width?: number
  height?: number
}

/**
 * useECharts返回值
 */
export interface UseEChartsReturn {
  chartRef: Ref<HTMLElement | null>
  chartInstance: Ref<any>
  setOption: (option: EChartsOption, opts?: any) => void
  resize: () => void
  dispose: () => void
  showLoading: (opts?: any) => void
  hideLoading: () => void
  isLoading: Ref<boolean>
}

/**
 * ECharts Hook
 */
export function useECharts(options: UseEChartsOptions = {}): UseEChartsReturn {
  const {
    autoResize = true,
    theme = 'default',
    renderer = 'canvas',
    width,
    height
  } = options

  const chartRef = ref<HTMLElement | null>(null)
  const chartInstance = ref<any>(null)
  const isLoading = ref(false)
  const resizeObserver = ref<ResizeObserver | null>(null)

  /**
   * 初始化图表
   */
  const initChart = async () => {
    if (!chartRef.value) return

    try {
      // 销毁已存在的实例
      if (chartInstance.value) {
        chartInstance.value.dispose()
      }

      // 创建新实例
      chartInstance.value = echarts.init(chartRef.value, theme, {
        renderer,
        width,
        height
      })

      // 设置自动调整大小
      if (autoResize) {
        setupAutoResize()
      }

      console.log('ECharts实例初始化成功')
    } catch (error) {
      console.error('ECharts初始化失败:', error)
    }
  }

  /**
   * 设置自动调整大小
   */
  const setupAutoResize = () => {
    if (!chartRef.value || !chartInstance.value) return

    // 使用ResizeObserver监听容器大小变化
    if (window.ResizeObserver) {
      resizeObserver.value = new ResizeObserver(() => {
        resize()
      })
      resizeObserver.value.observe(chartRef.value)
    } else {
      // 降级到window resize事件
      window.addEventListener('resize', resize)
    }
  }

  /**
   * 设置图表选项
   */
  const setOption = (option: EChartsOption, opts: any = {}) => {
    if (!chartInstance.value) {
      console.warn('图表实例未初始化')
      return
    }

    try {
      const defaultOpts = {
        notMerge: false,
        replaceMerge: undefined,
        silent: false
      }

      chartInstance.value.setOption(option, { ...defaultOpts, ...opts })
    } catch (error) {
      console.error('设置图表选项失败:', error)
    }
  }

  /**
   * 调整图表大小
   */
  const resize = () => {
    if (!chartInstance.value) return

    try {
      chartInstance.value.resize()
    } catch (error) {
      console.error('调整图表大小失败:', error)
    }
  }

  /**
   * 显示加载动画
   */
  const showLoading = (opts: any = {}) => {
    if (!chartInstance.value) return

    const defaultOpts = {
      text: '加载中...',
      color: '#409EFF',
      textColor: '#000',
      maskColor: 'rgba(255, 255, 255, 0.8)',
      zlevel: 0,
      fontSize: 12,
      showSpinner: true,
      spinnerRadius: 10,
      lineWidth: 5
    }

    try {
      chartInstance.value.showLoading({ ...defaultOpts, ...opts })
      isLoading.value = true
    } catch (error) {
      console.error('显示加载动画失败:', error)
    }
  }

  /**
   * 隐藏加载动画
   */
  const hideLoading = () => {
    if (!chartInstance.value) return

    try {
      chartInstance.value.hideLoading()
      isLoading.value = false
    } catch (error) {
      console.error('隐藏加载动画失败:', error)
    }
  }

  /**
   * 销毁图表实例
   */
  const dispose = () => {
    try {
      // 清理ResizeObserver
      if (resizeObserver.value) {
        resizeObserver.value.disconnect()
        resizeObserver.value = null
      }

      // 移除window resize监听
      window.removeEventListener('resize', resize)

      // 销毁图表实例
      if (chartInstance.value) {
        chartInstance.value.dispose()
        chartInstance.value = null
      }

      console.log('ECharts实例已销毁')
    } catch (error) {
      console.error('销毁图表实例失败:', error)
    }
  }

  // 组件挂载时初始化
  onMounted(async () => {
    await nextTick()
    await initChart()
  })

  // 组件卸载时清理
  onUnmounted(() => {
    dispose()
  })

  return {
    chartRef,
    chartInstance,
    setOption,
    resize,
    dispose,
    showLoading,
    hideLoading,
    isLoading
  }
}

/**
 * 获取默认的图表配置
 */
export function getDefaultChartOption(): EChartsOption {
  return {
    backgroundColor: 'transparent',
    textStyle: {
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
    },
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: 'rgba(50, 50, 50, 0.9)',
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      textStyle: {
        color: '#606266'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    }
  }
}

/**
 * 合并图表配置
 */
export function mergeChartOption(baseOption: EChartsOption, customOption: EChartsOption): EChartsOption {
  return echarts.util.merge(baseOption, customOption, true)
}
