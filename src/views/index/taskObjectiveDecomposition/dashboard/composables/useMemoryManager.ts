/**
 * 内存管理 Composable
 */

import { ref, computed, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { ChartInstance } from './useChartManager'

export interface MemoryStats {
  totalCharts: number
  activeCharts: number
  inactiveCharts: number
  memoryUsage: number
  lastCleanup: Date | null
}

export interface MemoryOptions {
  maxCharts?: number
  maxInactiveTime?: number // 毫秒
  autoCleanup?: boolean
  cleanupInterval?: number // 毫秒
  memoryThreshold?: number // MB
}

export function useMemoryManager(options: MemoryOptions = {}) {
  const defaultOptions = {
    maxCharts: 20,
    maxInactiveTime: 5 * 60 * 1000, // 5分钟
    autoCleanup: true,
    cleanupInterval: 60 * 1000, // 1分钟
    memoryThreshold: 100, // 100MB
    ...options
  }

  // 内存状态
  const chartActivity = ref<Map<string, Date>>(new Map())
  const cleanupTimer = ref<NodeJS.Timeout | null>(null)
  const memoryStats = ref<MemoryStats>({
    totalCharts: 0,
    activeCharts: 0,
    inactiveCharts: 0,
    memoryUsage: 0,
    lastCleanup: null
  })

  // 计算属性
  const isMemoryPressure = computed(() => {
    return memoryStats.value.memoryUsage > defaultOptions.memoryThreshold ||
           memoryStats.value.totalCharts > defaultOptions.maxCharts
  })

  // 记录图表活动
  const recordActivity = (chartId: string) => {
    chartActivity.value.set(chartId, new Date())
  }

  // 移除图表活动记录
  const removeActivity = (chartId: string) => {
    chartActivity.value.delete(chartId)
  }

  // 获取非活跃图表
  const getInactiveCharts = (charts: ChartInstance[]) => {
    const now = new Date()
    const inactiveCharts: ChartInstance[] = []

    charts.forEach(chart => {
      const lastActivity = chartActivity.value.get(chart.id)
      if (!lastActivity || (now.getTime() - lastActivity.getTime()) > defaultOptions.maxInactiveTime) {
        inactiveCharts.push(chart)
      }
    })

    return inactiveCharts
  }

  // 获取内存使用估算
  const estimateMemoryUsage = (charts: ChartInstance[]) => {
    // 简单的内存使用估算（实际应用中可能需要更精确的计算）
    let totalSize = 0
    
    charts.forEach(chart => {
      // 基础图表大小
      let chartSize = 1 // 1MB 基础大小
      
      // 根据图表类型调整
      if (chart.type.includes('map')) {
        chartSize += 5 // 地图数据较大
      } else if (chart.type.includes('heatmap')) {
        chartSize += 3 // 热力图数据较大
      } else if (chart.type.includes('scatter')) {
        chartSize += 2 // 散点图数据较大
      }
      
      // 根据数据量调整
      if (chart.data) {
        const dataStr = JSON.stringify(chart.data)
        chartSize += Math.ceil(dataStr.length / (1024 * 1024)) // 转换为MB
      }
      
      totalSize += chartSize
    })
    
    return totalSize
  }

  // 更新内存统计
  const updateMemoryStats = (charts: ChartInstance[]) => {
    const now = new Date()
    const inactiveCharts = getInactiveCharts(charts)
    
    memoryStats.value = {
      totalCharts: charts.length,
      activeCharts: charts.length - inactiveCharts.length,
      inactiveCharts: inactiveCharts.length,
      memoryUsage: estimateMemoryUsage(charts),
      lastCleanup: memoryStats.value.lastCleanup
    }
  }

  // 执行清理
  const performCleanup = (
    charts: ChartInstance[],
    onRemoveChart?: (chartId: string) => void
  ) => {
    const inactiveCharts = getInactiveCharts(charts)
    let cleanedCount = 0

    // 如果内存压力大，清理非活跃图表
    if (isMemoryPressure.value && inactiveCharts.length > 0) {
      // 按最后活动时间排序，优先清理最久未使用的
      inactiveCharts.sort((a, b) => {
        const aTime = chartActivity.value.get(a.id)?.getTime() || 0
        const bTime = chartActivity.value.get(b.id)?.getTime() || 0
        return aTime - bTime
      })

      // 清理一半的非活跃图表，或者清理到内存压力缓解
      const cleanupCount = Math.min(
        Math.ceil(inactiveCharts.length / 2),
        Math.max(1, memoryStats.value.totalCharts - defaultOptions.maxCharts)
      )

      for (let i = 0; i < cleanupCount; i++) {
        const chart = inactiveCharts[i]
        if (onRemoveChart) {
          onRemoveChart(chart.id)
        }
        removeActivity(chart.id)
        cleanedCount++
      }
    }

    // 强制垃圾回收（如果浏览器支持）
    if (window.gc && typeof window.gc === 'function') {
      try {
        window.gc()
      } catch (e) {
        // 忽略错误
      }
    }

    memoryStats.value.lastCleanup = new Date()

    if (cleanedCount > 0) {
      ElMessage.info(`已清理 ${cleanedCount} 个非活跃图表以释放内存`)
    }

    return cleanedCount
  }

  // 启动自动清理
  const startAutoCleanup = (
    getCharts: () => ChartInstance[],
    onRemoveChart?: (chartId: string) => void
  ) => {
    if (!defaultOptions.autoCleanup) return

    cleanupTimer.value = setInterval(() => {
      const charts = getCharts()
      updateMemoryStats(charts)
      
      if (isMemoryPressure.value) {
        performCleanup(charts, onRemoveChart)
      }
    }, defaultOptions.cleanupInterval)
  }

  // 停止自动清理
  const stopAutoCleanup = () => {
    if (cleanupTimer.value) {
      clearInterval(cleanupTimer.value)
      cleanupTimer.value = null
    }
  }

  // 手动清理
  const manualCleanup = (
    charts: ChartInstance[],
    onRemoveChart?: (chartId: string) => void
  ) => {
    updateMemoryStats(charts)
    return performCleanup(charts, onRemoveChart)
  }

  // 获取内存报告
  const getMemoryReport = () => {
    return {
      ...memoryStats.value,
      isMemoryPressure: isMemoryPressure.value,
      options: defaultOptions,
      activityCount: chartActivity.value.size
    }
  }

  // 清理所有数据
  const cleanup = () => {
    stopAutoCleanup()
    chartActivity.value.clear()
    memoryStats.value = {
      totalCharts: 0,
      activeCharts: 0,
      inactiveCharts: 0,
      memoryUsage: 0,
      lastCleanup: null
    }
  }

  // 获取性能建议
  const getPerformanceSuggestions = () => {
    const suggestions: string[] = []
    
    if (memoryStats.value.totalCharts > defaultOptions.maxCharts) {
      suggestions.push(`图表数量过多（${memoryStats.value.totalCharts}/${defaultOptions.maxCharts}），建议删除一些不需要的图表`)
    }
    
    if (memoryStats.value.memoryUsage > defaultOptions.memoryThreshold) {
      suggestions.push(`内存使用过高（${memoryStats.value.memoryUsage.toFixed(1)}MB），建议启用懒加载或减少图表数量`)
    }
    
    if (memoryStats.value.inactiveCharts > 5) {
      suggestions.push(`有 ${memoryStats.value.inactiveCharts} 个图表长时间未使用，建议清理`)
    }
    
    return suggestions
  }

  // 生命周期清理
  onUnmounted(() => {
    cleanup()
  })

  return {
    // 状态
    memoryStats,
    isMemoryPressure,

    // 方法
    recordActivity,
    removeActivity,
    getInactiveCharts,
    updateMemoryStats,
    performCleanup,
    startAutoCleanup,
    stopAutoCleanup,
    manualCleanup,
    getMemoryReport,
    getPerformanceSuggestions,
    cleanup
  }
}
