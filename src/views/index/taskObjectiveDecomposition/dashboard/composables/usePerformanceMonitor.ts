/**
 * 性能监控 Composable
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { performanceMonitor } from '../utils/helpers'

export interface PerformanceMetrics {
  fps: number
  memoryUsage: number
  renderTime: number
  chartCount: number
  visibleChartCount: number
  lastUpdate: Date
}

export interface PerformanceAlert {
  type: 'warning' | 'error'
  message: string
  timestamp: Date
  metric: string
  value: number
  threshold: number
}

export interface PerformanceOptions {
  fpsThreshold?: number
  memoryThreshold?: number
  renderTimeThreshold?: number
  alertEnabled?: boolean
  monitoringInterval?: number
}

export function usePerformanceMonitor(options: PerformanceOptions = {}) {
  const defaultOptions = {
    fpsThreshold: 30,
    memoryThreshold: 100, // MB
    renderTimeThreshold: 100, // ms
    alertEnabled: true,
    monitoringInterval: 1000, // 1秒
    ...options
  }

  // 性能状态
  const metrics = ref<PerformanceMetrics>({
    fps: 60,
    memoryUsage: 0,
    renderTime: 0,
    chartCount: 0,
    visibleChartCount: 0,
    lastUpdate: new Date()
  })

  const alerts = ref<PerformanceAlert[]>([])
  const monitoringTimer = ref<NodeJS.Timeout | null>(null)
  const frameCount = ref(0)
  const lastFrameTime = ref(performance.now())
  const renderTimes = ref<number[]>([])

  // 计算属性
  const isPerformanceGood = computed(() => {
    return metrics.value.fps >= defaultOptions.fpsThreshold &&
           metrics.value.memoryUsage <= defaultOptions.memoryThreshold &&
           metrics.value.renderTime <= defaultOptions.renderTimeThreshold
  })

  const performanceGrade = computed(() => {
    const score = calculatePerformanceScore()
    if (score >= 90) return 'A'
    if (score >= 80) return 'B'
    if (score >= 70) return 'C'
    if (score >= 60) return 'D'
    return 'F'
  })

  // 计算性能分数
  const calculatePerformanceScore = () => {
    let score = 100

    // FPS 评分 (40%)
    const fpsScore = Math.min(metrics.value.fps / 60, 1) * 40
    score = score - 40 + fpsScore

    // 内存使用评分 (30%)
    const memoryScore = Math.max(0, 1 - metrics.value.memoryUsage / defaultOptions.memoryThreshold) * 30
    score = score - 30 + memoryScore

    // 渲染时间评分 (30%)
    const renderScore = Math.max(0, 1 - metrics.value.renderTime / defaultOptions.renderTimeThreshold) * 30
    score = score - 30 + renderScore

    return Math.max(0, Math.min(100, score))
  }

  // 测量FPS
  const measureFPS = () => {
    const now = performance.now()
    frameCount.value++

    if (now - lastFrameTime.value >= 1000) {
      metrics.value.fps = Math.round((frameCount.value * 1000) / (now - lastFrameTime.value))
      frameCount.value = 0
      lastFrameTime.value = now
    }

    requestAnimationFrame(measureFPS)
  }

  // 测量内存使用
  const measureMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      metrics.value.memoryUsage = Math.round(memory.usedJSHeapSize / (1024 * 1024))
    }
  }

  // 记录渲染时间
  const recordRenderTime = (time: number) => {
    renderTimes.value.push(time)
    
    // 保持最近10次的渲染时间
    if (renderTimes.value.length > 10) {
      renderTimes.value.shift()
    }
    
    // 计算平均渲染时间
    metrics.value.renderTime = Math.round(
      renderTimes.value.reduce((sum, t) => sum + t, 0) / renderTimes.value.length
    )
  }

  // 开始渲染计时
  const startRenderTimer = (name: string = 'render') => {
    performanceMonitor.start(name)
  }

  // 结束渲染计时
  const endRenderTimer = (name: string = 'render') => {
    const time = performanceMonitor.end(name)
    recordRenderTime(time)
    return time
  }

  // 更新图表计数
  const updateChartCount = (total: number, visible: number) => {
    metrics.value.chartCount = total
    metrics.value.visibleChartCount = visible
  }

  // 检查性能警告
  const checkPerformanceAlerts = () => {
    const now = new Date()

    // 禁用所有性能警告，因为这是演示页面
    // FPS 警告 - 已禁用
    // 内存警告 - 已禁用
    // 渲染时间警告 - 已禁用
  }

  // 添加警告
  const addAlert = (alert: PerformanceAlert) => {
    // 避免重复警告（5秒内相同类型的警告只显示一次）
    const recentAlert = alerts.value.find(a => 
      a.metric === alert.metric && 
      (alert.timestamp.getTime() - a.timestamp.getTime()) < 5000
    )

    if (!recentAlert) {
      alerts.value.push(alert)
      
      // 保持最近20个警告
      if (alerts.value.length > 20) {
        alerts.value.shift()
      }

      // 显示警告消息
      if (defaultOptions.alertEnabled) {
        if (alert.type === 'error') {
          ElMessage.error(alert.message)
        } else {
          ElMessage.warning(alert.message)
        }
      }
    }
  }

  // 清除警告
  const clearAlerts = () => {
    alerts.value = []
  }

  // 获取性能报告
  const getPerformanceReport = () => {
    return {
      metrics: { ...metrics.value },
      grade: performanceGrade.value,
      score: calculatePerformanceScore(),
      isGood: isPerformanceGood.value,
      alerts: [...alerts.value],
      suggestions: getPerformanceSuggestions()
    }
  }

  // 获取性能建议
  const getPerformanceSuggestions = () => {
    const suggestions: string[] = []

    if (metrics.value.fps < defaultOptions.fpsThreshold) {
      suggestions.push('帧率较低，建议减少图表数量或启用懒加载')
    }

    if (metrics.value.memoryUsage > defaultOptions.memoryThreshold) {
      suggestions.push('内存使用过高，建议清理不需要的图表或启用自动清理')
    }

    if (metrics.value.renderTime > defaultOptions.renderTimeThreshold) {
      suggestions.push('渲染时间过长，建议优化图表配置或减少数据量')
    }

    if (metrics.value.chartCount > 10) {
      suggestions.push('图表数量较多，建议使用分页或虚拟滚动')
    }

    return suggestions
  }

  // 开始监控
  const startMonitoring = () => {
    // 开始FPS监控
    measureFPS()

    // 开始定期监控
    monitoringTimer.value = setInterval(() => {
      measureMemoryUsage()
      metrics.value.lastUpdate = new Date()
      
      if (defaultOptions.alertEnabled) {
        checkPerformanceAlerts()
      }
    }, defaultOptions.monitoringInterval)
  }

  // 停止监控
  const stopMonitoring = () => {
    if (monitoringTimer.value) {
      clearInterval(monitoringTimer.value)
      monitoringTimer.value = null
    }
  }

  // 重置监控数据
  const resetMonitoring = () => {
    metrics.value = {
      fps: 60,
      memoryUsage: 0,
      renderTime: 0,
      chartCount: 0,
      visibleChartCount: 0,
      lastUpdate: new Date()
    }
    alerts.value = []
    renderTimes.value = []
    frameCount.value = 0
    lastFrameTime.value = performance.now()
  }

  // 生命周期
  onMounted(() => {
    startMonitoring()
  })

  onUnmounted(() => {
    stopMonitoring()
  })

  return {
    // 状态
    metrics,
    alerts,
    isPerformanceGood,
    performanceGrade,

    // 方法
    startRenderTimer,
    endRenderTimer,
    updateChartCount,
    addAlert,
    clearAlerts,
    getPerformanceReport,
    getPerformanceSuggestions,
    startMonitoring,
    stopMonitoring,
    resetMonitoring
  }
}
