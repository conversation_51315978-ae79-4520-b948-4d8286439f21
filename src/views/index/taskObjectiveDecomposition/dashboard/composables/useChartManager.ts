/**
 * 图表实例管理器 Composable
 * 用于管理多个图表实例的添加、删除、配置和状态
 */

import { ref, computed, nextTick, readonly } from 'vue'
import { ElMessage } from 'element-plus'
import type {
  ExtendedChartType,
  ChartConfig,
  DashboardData,
  DataPoint,
  ChartEvents,
  DashboardSaveConfig,
  ChartSaveData,
  LayoutConfig,
  DashboardMetadata
} from '../types/dashboard.types'
import { getChartTypeById, allChartTypes } from '../config/chartTypes.config'
import { generateId } from '../utils/helpers'
import { ChartFactory } from '../utils/chartFactory'

// 图表实例接口
export interface ChartInstance {
  id: string
  type: ExtendedChartType
  title: string
  data: any
  config: any
  position: { x: number; y: number; width: number; height: number }
  visible: boolean
  loading: boolean
  error: string | null
  createdAt: Date
  updatedAt: Date
}

// 图表管理器选项
export interface ChartManagerOptions {
  maxCharts?: number
  defaultChartSize?: { width: number; height: number }
  autoLayout?: boolean
}

export function useChartManager(options: ChartManagerOptions = {}) {
  // 默认选项
  const defaultOptions = {
    maxCharts: 12,
    defaultChartSize: { width: 400, height: 300 },
    autoLayout: true,
    ...options
  }

  // 响应式状态
  const charts = ref<ChartInstance[]>([])
  const selectedChartId = ref<string | null>(null)
  const isLayoutMode = ref(false)
  const loading = ref(false)

  // 计算属性
  const chartCount = computed(() => charts.value.length)
  const hasCharts = computed(() => chartCount.value > 0)
  const canAddChart = computed(() => chartCount.value < defaultOptions.maxCharts)
  const selectedChart = computed(() => 
    charts.value.find(chart => chart.id === selectedChartId.value) || null
  )

  // 获取下一个图表位置
  const getNextPosition = () => {
    const cols = Math.floor(window.innerWidth / defaultOptions.defaultChartSize.width)
    const rows = Math.ceil(chartCount.value / cols)
    const col = chartCount.value % cols
    const row = Math.floor(chartCount.value / cols)

    return {
      x: col * defaultOptions.defaultChartSize.width,
      y: row * defaultOptions.defaultChartSize.height,
      width: defaultOptions.defaultChartSize.width,
      height: defaultOptions.defaultChartSize.height
    }
  }

  // 生成唯一的图表ID
  const generateUniqueChartId = (): string => {
    let id: string
    let attempts = 0
    const maxAttempts = 100

    do {
      id = generateId()
      attempts++
      if (attempts > maxAttempts) {
        // 如果生成了太多次，使用时间戳确保唯一性
        id = `chart_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
        break
      }
    } while (charts.value.some(chart => chart.id === id))

    return id
  }

  // 添加图表
  const addChart = async (
    type: ExtendedChartType,
    data?: any,
    config?: any,
    title?: string
  ): Promise<string | null> => {
    console.log('开始添加图表:', type, title)

    if (!canAddChart.value) {
      ElMessage.warning(`最多只能添加 ${defaultOptions.maxCharts} 个图表`)
      return null
    }

    const chartTypeInfo = getChartTypeById(type)
    if (!chartTypeInfo) {
      console.error('不支持的图表类型:', type)
      ElMessage.error('不支持的图表类型')
      return null
    }

    const chartId = generateUniqueChartId()
    const position = defaultOptions.autoLayout ? getNextPosition() : {
      x: 0, y: 0,
      width: defaultOptions.defaultChartSize.width,
      height: defaultOptions.defaultChartSize.height
    }

    // 如果没有提供数据，生成模拟数据
    let chartData
    try {
      chartData = data || ChartFactory.createChartData(type, null)
      console.log('图表数据生成成功:', chartData)
    } catch (dataError) {
      console.error('生成图表数据失败:', dataError)
      ElMessage.error('生成图表数据失败')
      return null
    }

    const newChart: ChartInstance = {
      id: chartId,
      type,
      title: title || chartTypeInfo.name,
      data: chartData,
      config: config || chartTypeInfo.defaultConfig,
      position,
      visible: true,
      loading: false,
      error: null,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    charts.value.push(newChart)
    selectedChartId.value = chartId

    console.log('图表添加成功:', chartId, newChart.title)
    console.log('当前图表列表:', charts.value.map(c => ({ id: c.id, title: c.title })))
    ElMessage.success(`已添加${chartTypeInfo.name}`)
    return chartId
  }

  // 删除图表
  const removeChart = (chartId: string) => {
    console.log('尝试删除图表:', chartId)
    console.log('当前图表列表:', charts.value.map(c => ({ id: c.id, title: c.title })))

    const index = charts.value.findIndex(chart => chart.id === chartId)
    if (index === -1) {
      console.error('图表不存在，无法删除:', chartId)
      ElMessage.error(`图表不存在（ID: ${chartId}）`)
      return false
    }

    const chart = charts.value[index]
    charts.value.splice(index, 1)

    // 如果删除的是选中的图表，清除选中状态
    if (selectedChartId.value === chartId) {
      selectedChartId.value = null
    }

    console.log(`图表已删除: ${chartId} - ${chart.title}`)
    ElMessage.success(`已删除${chart.title}`)
    return true
  }

  // 更新图表数据
  const updateChartData = (chartId: string, data: any) => {
    const chart = charts.value.find(c => c.id === chartId)
    if (!chart) {
      ElMessage.error('图表不存在')
      return false
    }

    chart.data = data
    chart.updatedAt = new Date()
    return true
  }

  // 更新图表配置
  const updateChartConfig = (chartId: string, config: any) => {
    const chart = charts.value.find(c => c.id === chartId)
    if (!chart) {
      ElMessage.error('图表不存在')
      return false
    }

    chart.config = { ...chart.config, ...config }
    chart.updatedAt = new Date()
    return true
  }

  // 更新图表位置
  const updateChartPosition = (chartId: string, position: Partial<ChartInstance['position']>) => {
    const chart = charts.value.find(c => c.id === chartId)
    if (!chart) return false

    chart.position = { ...chart.position, ...position }
    chart.updatedAt = new Date()
    return true
  }

  // 更新图表标题
  const updateChartTitle = (chartId: string, title: string) => {
    const chart = charts.value.find(c => c.id === chartId)
    if (!chart) return false

    chart.title = title
    chart.updatedAt = new Date()
    return true
  }

  // 设置图表加载状态
  const setChartLoading = (chartId: string, loading: boolean) => {
    const chart = charts.value.find(c => c.id === chartId)
    if (!chart) return false

    chart.loading = loading
    return true
  }

  // 设置图表错误状态
  const setChartError = (chartId: string, error: string | null) => {
    const chart = charts.value.find(c => c.id === chartId)
    if (!chart) return false

    chart.error = error
    return true
  }

  // 选择图表
  const selectChart = (chartId: string | null) => {
    selectedChartId.value = chartId
  }

  // 切换图表可见性
  const toggleChartVisibility = (chartId: string) => {
    const chart = charts.value.find(c => c.id === chartId)
    if (!chart) return false

    chart.visible = !chart.visible
    return true
  }

  // 清空所有图表
  const clearAllCharts = () => {
    charts.value = []
    selectedChartId.value = null
    ElMessage.success('已清空所有图表')
  }

  // 自动布局
  const autoLayout = () => {
    const containerWidth = window.innerWidth - 300 // 减去侧边栏宽度
    const containerHeight = window.innerHeight - 200 // 减去头部高度

    // 计算最佳网格布局
    const chartCount = charts.value.length
    if (chartCount === 0) return

    const cols = Math.ceil(Math.sqrt(chartCount))
    const rows = Math.ceil(chartCount / cols)

    const chartWidth = Math.floor(containerWidth / cols) - 20 // 减去间距
    const chartHeight = Math.floor(containerHeight / rows) - 20 // 减去间距

    charts.value.forEach((chart, index) => {
      const col = index % cols
      const row = Math.floor(index / cols)

      chart.position = {
        x: col * (chartWidth + 20) + 10,
        y: row * (chartHeight + 20) + 10,
        width: Math.max(chartWidth, 300), // 最小宽度
        height: Math.max(chartHeight, 250) // 最小高度
      }
    })
  }

  // 响应式布局
  const responsiveLayout = () => {
    const containerWidth = window.innerWidth - 300

    if (containerWidth < 768) {
      // 移动端：单列布局
      charts.value.forEach((chart, index) => {
        chart.position = {
          x: 10,
          y: index * 320 + 10,
          width: containerWidth - 20,
          height: 300
        }
      })
    } else if (containerWidth < 1200) {
      // 平板：双列布局
      charts.value.forEach((chart, index) => {
        const col = index % 2
        const row = Math.floor(index / 2)
        const chartWidth = Math.floor(containerWidth / 2) - 20

        chart.position = {
          x: col * (chartWidth + 20) + 10,
          y: row * 320 + 10,
          width: chartWidth,
          height: 300
        }
      })
    } else {
      // 桌面端：自动网格布局
      autoLayout()
    }
  }

  // 切换布局模式
  const toggleLayoutMode = () => {
    isLayoutMode.value = !isLayoutMode.value
  }

  // 导出图表配置
  const exportCharts = () => {
    const exportData = {
      charts: charts.value,
      exportTime: new Date().toISOString(),
      version: '1.0.0'
    }
    return JSON.stringify(exportData, null, 2)
  }

  // 导入图表配置
  const importCharts = (data: string) => {
    try {
      const importData = JSON.parse(data)
      if (importData.charts && Array.isArray(importData.charts)) {
        charts.value = importData.charts.map((chart: any) => ({
          ...chart,
          createdAt: new Date(chart.createdAt),
          updatedAt: new Date(chart.updatedAt)
        }))
        selectedChartId.value = null
        ElMessage.success('图表配置导入成功')
        return true
      }
    } catch (error) {
      ElMessage.error('导入失败：数据格式错误')
    }
    return false
  }

  // 获取图表类型信息
  const getChartTypeInfo = (type: ExtendedChartType) => {
    return getChartTypeById(type)
  }

  // 获取所有可用图表类型
  const getAvailableChartTypes = () => {
    return allChartTypes
  }

  // 导出仪表板配置
  const exportDashboardConfig = (taskId: string): DashboardSaveConfig => {
    const chartsData: ChartSaveData[] = charts.value.map(chart => ({
      id: chart.id,
      type: chart.type,
      title: chart.title,
      position: {
        x: chart.position.x,
        y: chart.position.y,
        width: chart.position.width,
        height: chart.position.height
      },
      config: chart.config,
      data: chart.data,
      visible: chart.visible,
      createdAt: chart.createdAt.toISOString(),
      updatedAt: chart.updatedAt.toISOString()
    }))

    const layoutConfig: LayoutConfig = {
      type: 'free', // 当前使用自由布局
      gap: 10,
      padding: 20
    }

    const metadata: DashboardMetadata = {
      name: `任务${taskId}仪表板`,
      description: '任务数据可视化仪表板配置',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: '1.0.0'
    }

    return {
      taskId,
      charts: chartsData,
      layout: layoutConfig,
      metadata,
      version: '1.0.0'
    }
  }

  // 验证单个图表配置
  const validateChartData = (chartData: any): boolean => {
    if (!chartData || typeof chartData !== 'object') {
      console.warn('图表数据无效：不是对象')
      return false
    }

    if (!chartData.id || !chartData.type || !chartData.title) {
      console.warn('图表数据无效：缺少必需字段', chartData)
      return false
    }

    if (!chartData.position || typeof chartData.position !== 'object') {
      console.warn('图表位置数据无效', chartData)
      return false
    }

    const { x, y, width, height } = chartData.position
    if (typeof x !== 'number' || typeof y !== 'number' ||
        typeof width !== 'number' || typeof height !== 'number') {
      console.warn('图表位置数值无效', chartData.position)
      return false
    }

    return true
  }

  // 导入仪表板配置
  const importDashboardConfig = async (config: DashboardSaveConfig): Promise<boolean> => {
    try {
      console.log('开始导入仪表板配置:', config)

      // 验证配置格式
      if (!config || !config.charts || !Array.isArray(config.charts)) {
        console.error('配置格式无效')
        ElMessage.error('配置格式无效')
        return false
      }

      // 清空现有图表
      clearAllCharts()

      let successCount = 0
      let failCount = 0

      // 导入图表
      for (const chartData of config.charts) {
        try {
          // 验证图表数据
          if (!validateChartData(chartData)) {
            console.warn('跳过无效图表配置:', chartData)
            failCount++
            continue
          }

          // 使用原有的图表ID，如果没有则生成新的
          const chartId = chartData.id || generateId()

          // 验证图表类型是否支持
          if (!ChartFactory.isChartTypeSupported(chartData.type)) {
            console.warn('不支持的图表类型:', chartData.type)
            failCount++
            continue
          }

          // 如果图表没有数据或数据为空，生成模拟数据
          let data = chartData.data
          if (!data) {
            try {
              data = ChartFactory.createChartData(chartData.type, null)
            } catch (dataError) {
              console.error('生成图表数据失败:', dataError)
              failCount++
              continue
            }
          }

          const newChart: ChartInstance = {
            id: chartId,
            type: chartData.type,
            title: chartData.title || '未命名图表',
            data: data,
            config: chartData.config || {},
            position: chartData.position,
            visible: chartData.visible !== false, // 默认为true
            loading: false,
            error: null,
            createdAt: chartData.createdAt ? new Date(chartData.createdAt) : new Date(),
            updatedAt: chartData.updatedAt ? new Date(chartData.updatedAt) : new Date()
          }

          charts.value.push(newChart)
          successCount++
          console.log('成功导入图表:', chartId, chartData.title)
          console.log('导入的图表数据:', newChart)
        } catch (chartError) {
          console.error('导入单个图表失败:', chartError, chartData)
          failCount++
        }
      }

      if (successCount > 0) {
        ElMessage.success(`仪表板配置导入成功，成功导入 ${successCount} 个图表${failCount > 0 ? `，跳过 ${failCount} 个无效图表` : ''}`)
        console.log(`导入完成：成功 ${successCount} 个，失败 ${failCount} 个`)
        return true
      } else {
        ElMessage.warning('没有成功导入任何图表')
        return false
      }
    } catch (error) {
      console.error('导入仪表板配置失败:', error)
      ElMessage.error('导入失败：配置处理错误')
      return false
    }
  }

  // 验证配置数据
  const validateDashboardConfig = (config: any): boolean => {
    if (!config || typeof config !== 'object') {
      return false
    }

    // 检查必需字段
    if (!config.taskId || !config.charts || !Array.isArray(config.charts)) {
      return false
    }

    // 检查图表数据格式
    for (const chart of config.charts) {
      if (!chart.id || !chart.type || !chart.position) {
        return false
      }

      if (typeof chart.position !== 'object' ||
          typeof chart.position.x !== 'number' ||
          typeof chart.position.y !== 'number' ||
          typeof chart.position.width !== 'number' ||
          typeof chart.position.height !== 'number') {
        return false
      }
    }

    return true
  }

  // 添加调试方法
  const debugCharts = () => {
    console.log('=== 图表管理器调试信息 ===')
    console.log('图表数量:', charts.value.length)
    console.log('图表列表:', charts.value.map(chart => ({
      id: chart.id,
      type: chart.type,
      title: chart.title,
      hasData: !!chart.data,
      dataKeys: chart.data ? Object.keys(chart.data) : [],
      visible: chart.visible
    })))
    console.log('原始图表数组:', charts.value)
  }

  return {
    // 状态
    charts: readonly(charts),
    selectedChartId: readonly(selectedChartId),
    isLayoutMode: readonly(isLayoutMode),
    loading: readonly(loading),

    // 计算属性
    chartCount,
    hasCharts,
    canAddChart,
    selectedChart,

    // 方法
    addChart,
    removeChart,
    updateChartData,
    updateChartConfig,
    updateChartPosition,
    updateChartTitle,
    setChartLoading,
    setChartError,
    selectChart,
    toggleChartVisibility,
    clearAllCharts,
    autoLayout,
    responsiveLayout,
    toggleLayoutMode,
    exportCharts,
    importCharts,
    getChartTypeInfo,
    getAvailableChartTypes,
    exportDashboardConfig,
    importDashboardConfig,
    validateDashboardConfig,

    // 调试方法
    debugCharts
  }
}
