/**
 * 拖拽功能 Composable
 */

import { ref, computed } from 'vue'
import type { ChartInstance } from './useChartManager'

export interface DragState {
  isDragging: boolean
  dragChart: ChartInstance | null
  dragOffset: { x: number; y: number }
  startPosition: { x: number; y: number }
}

export function useDragDrop() {
  // 拖拽状态
  const dragState = ref<DragState>({
    isDragging: false,
    dragChart: null,
    dragOffset: { x: 0, y: 0 },
    startPosition: { x: 0, y: 0 }
  })

  // 计算属性
  const isDragging = computed(() => dragState.value.isDragging)
  const dragChart = computed(() => dragState.value.dragChart)

  // 开始拖拽
  const startDrag = (
    chart: ChartInstance, 
    event: MouseEvent | TouchEvent,
    onUpdate?: (chart: ChartInstance, position: { x: number; y: number }) => void
  ) => {
    const clientX = 'touches' in event ? event.touches[0].clientX : event.clientX
    const clientY = 'touches' in event ? event.touches[0].clientY : event.clientY

    dragState.value = {
      isDragging: true,
      dragChart: chart,
      dragOffset: {
        x: clientX - chart.position.x,
        y: clientY - chart.position.y
      },
      startPosition: {
        x: chart.position.x,
        y: chart.position.y
      }
    }

    // 添加全局事件监听
    const handleMouseMove = (e: MouseEvent | TouchEvent) => {
      if (!dragState.value.isDragging || !dragState.value.dragChart) return

      e.preventDefault()
      
      const moveClientX = 'touches' in e ? e.touches[0].clientX : e.clientX
      const moveClientY = 'touches' in e ? e.touches[0].clientY : e.clientY

      const newPosition = {
        x: Math.max(0, moveClientX - dragState.value.dragOffset.x),
        y: Math.max(0, moveClientY - dragState.value.dragOffset.y)
      }

      // 更新图表位置
      if (onUpdate) {
        onUpdate(dragState.value.dragChart, newPosition)
      }
    }

    const handleMouseUp = () => {
      if (dragState.value.isDragging) {
        endDrag()
      }
    }

    // 绑定事件
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    document.addEventListener('touchmove', handleMouseMove, { passive: false })
    document.addEventListener('touchend', handleMouseUp)

    // 清理函数
    const cleanup = () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.removeEventListener('touchmove', handleMouseMove)
      document.removeEventListener('touchend', handleMouseUp)
    }

    // 存储清理函数
    ;(dragState.value as any).cleanup = cleanup
  }

  // 结束拖拽
  const endDrag = () => {
    if ((dragState.value as any).cleanup) {
      ;(dragState.value as any).cleanup()
    }

    dragState.value = {
      isDragging: false,
      dragChart: null,
      dragOffset: { x: 0, y: 0 },
      startPosition: { x: 0, y: 0 }
    }
  }

  // 取消拖拽（恢复原位置）
  const cancelDrag = (onUpdate?: (chart: ChartInstance, position: { x: number; y: number }) => void) => {
    if (dragState.value.dragChart && onUpdate) {
      onUpdate(dragState.value.dragChart, dragState.value.startPosition)
    }
    endDrag()
  }

  // 检查是否可以放置
  const canDrop = (
    targetChart: ChartInstance,
    charts: ChartInstance[],
    threshold: number = 50
  ): boolean => {
    if (!dragState.value.dragChart || dragState.value.dragChart.id === targetChart.id) {
      return false
    }

    // 检查是否重叠
    const dragPos = dragState.value.dragChart.position
    const targetPos = targetChart.position

    const distance = Math.sqrt(
      Math.pow(dragPos.x - targetPos.x, 2) + Math.pow(dragPos.y - targetPos.y, 2)
    )

    return distance < threshold
  }

  // 交换图表位置
  const swapCharts = (
    chart1: ChartInstance,
    chart2: ChartInstance,
    onUpdate?: (chart: ChartInstance, position: { x: number; y: number }) => void
  ) => {
    const temp = { ...chart1.position }
    
    if (onUpdate) {
      onUpdate(chart1, chart2.position)
      onUpdate(chart2, temp)
    }
  }

  // 获取拖拽样式
  const getDragStyle = (chart: ChartInstance) => {
    if (dragState.value.isDragging && dragState.value.dragChart?.id === chart.id) {
      return {
        transform: `translate(${chart.position.x}px, ${chart.position.y}px) scale(1.05) rotate(2deg)`,
        zIndex: 1000,
        opacity: 0.8,
        transition: 'none',
        cursor: 'grabbing'
      }
    }
    
    return {
      transform: `translate(${chart.position.x}px, ${chart.position.y}px)`,
      zIndex: 1,
      opacity: 1,
      transition: 'all 0.3s ease',
      cursor: 'grab'
    }
  }

  // 获取容器样式
  const getContainerStyle = () => {
    return {
      userSelect: dragState.value.isDragging ? 'none' : 'auto',
      cursor: dragState.value.isDragging ? 'grabbing' : 'default'
    }
  }

  // 重置拖拽状态
  const resetDrag = () => {
    endDrag()
  }

  return {
    // 状态
    dragState,
    isDragging,
    dragChart,

    // 方法
    startDrag,
    endDrag,
    cancelDrag,
    canDrop,
    swapCharts,
    getDragStyle,
    getContainerStyle,
    resetDrag
  }
}
