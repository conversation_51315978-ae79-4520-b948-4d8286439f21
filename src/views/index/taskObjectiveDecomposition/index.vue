<!-- 任务目标拆解主页面 -->
<script setup lang="ts" name="TaskObjectiveDecomposition">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useTaskObjectiveStore, type TaskObjective } from '@/stores/taskObjectiveStore'
import TaskConfigDialog from './components/TaskConfigDialog.vue'
import BusinessReportSelectDialog from './components/BusinessReportSelectDialog.vue'
import BaseTableComp from '@/components/common/basetable-comp.vue'

// Store和Router
const taskStore = useTaskObjectiveStore()
const router = useRouter()

// 页面状态
const loading = ref(false)
const searchKeyword = ref('')
const groupDialogVisible = ref(false)
const selectedGroupField = ref('')
const tempGroupField = ref('') // 临时选择的分组字段
const highlightEnabled = ref(false)

// 搜索建议相关状态
const searchSuggestions = ref<string[]>([])
const showSuggestions = ref(false)

// 分组选项配置
const groupOptions = [
  { value: 'taskType', label: '任务类型' },
  { value: 'executionCycle', label: '执行周期' },
  { value: 'taskStatus', label: '任务状态' }
]

// 表格数据
const tableData = computed(() => {
  let filteredData = taskStore.tasks

  // 搜索过滤
  if (searchKeyword.value) {
    filteredData = filteredData.filter((task: TaskObjective) =>
      task.taskName.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 分组排序
  if (selectedGroupField.value) {
    filteredData = [...filteredData].sort((a: any, b: any) => {
      const fieldA = a[selectedGroupField.value] || ''
      const fieldB = b[selectedGroupField.value] || ''
      return fieldA.localeCompare(fieldB)
    })
  }

  return filteredData
})

// 搜索建议计算
const filteredSuggestions = computed(() => {
  if (!searchKeyword.value || searchKeyword.value.length < 1) {
    return []
  }

  const keyword = searchKeyword.value.toLowerCase()
  const suggestions = taskStore.tasks
    .map(task => task.taskName)
    .filter(name => name.toLowerCase().includes(keyword))
    .filter(name => name.toLowerCase() !== keyword) // 排除完全匹配的项
    .slice(0, 8) // 最多显示8个建议

  return [...new Set(suggestions)] // 去重
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: computed(() => tableData.value.length)
})

// 弹窗状态
const taskConfigVisible = ref(false)
const businessReportSelectVisible = ref(false)
const currentTask = ref<any>(null)
const taskConfigRef = ref()

// 表格列配置
const colData = [
  { field: 'taskName', title: '任务名称', minWidth: 150, slot: true },
  { field: 'taskType', title: '任务类型', width: 100, align: 'center' },
  { field: 'createDepartment', title: '创建部门创建科室管理人员', width: 200 },
  { field: 'startTime', title: '开始时间', width: 120, align: 'center' },
  { field: 'executionCycle', title: '执行周期', width: 100, align: 'center' },
  { field: 'taskStatus', title: '任务状态', width: 100, align: 'center', slot: true },
  { field: 'taskSwitch', title: '任务启停', width: 100, align: 'center', slot: true }
]

// 操作按钮配置
const buttons = [
  { type: 'primary' as const, code: 'detail', title: '详情', icon: '', verify: 'true', more: false, showBtn: 'true' },
  { type: 'primary' as const, code: 'decompose', title: '任务目标拆解', icon: '', verify: 'true', more: false, showBtn: 'true' }
]

// 生命周期
onMounted(() => {
  taskStore.initializeData()
  console.log('buttons:', buttons)
  console.log('tableData:', tableData.value)
})

// 方法
const handleSearch = async () => {
  loading.value = true
  // 模拟接口请求延迟
  await new Promise(resolve => setTimeout(resolve, 100))
  pagination.currentPage = 1
  showSuggestions.value = false // 搜索时隐藏建议
  loading.value = false
}

// 搜索建议相关方法
const handleSearchInput = () => {
  showSuggestions.value = searchKeyword.value.length > 0 && filteredSuggestions.value.length > 0
}

const handleSuggestionClick = (suggestion: string) => {
  searchKeyword.value = suggestion
  showSuggestions.value = false
  handleSearch()
}

const handleSearchFocus = () => {
  if (searchKeyword.value.length > 0 && filteredSuggestions.value.length > 0) {
    showSuggestions.value = true
  }
}

const handleSearchBlur = () => {
  // 延迟隐藏，以便点击建议项
  setTimeout(() => {
    showSuggestions.value = false
  }, 200)
}

const handleTaskSwitchChange = (task: any, value: boolean) => {
  taskStore.updateTaskSwitch(task.id, value)
  ElMessage.success(`任务已${value ? '启用' : '停用'}`)
}

const handleTableButton = (button: any, row: any) => {
  if (button.code === 'detail') {
    // 跳转到详情页面，保持当前页面的查询参数
    const currentQuery = router.currentRoute.value.query
    router.push({
      path: `/taskObjectiveDecomposition/detail/${row.id}`,
      query: currentQuery
    })
  } else if (button.code === 'decompose') {
    currentTask.value = row
    taskConfigVisible.value = true
  }
}

// 处理任务名称点击跳转
const handleTaskNameClick = (row: any) => {
  const currentQuery = router.currentRoute.value.query
  router.push({
    path: `/taskObjectiveDecomposition/detail/${row.id}`,
    query: currentQuery
  })
}

// 处理分组功能
const handleGroupClick = () => {
  tempGroupField.value = selectedGroupField.value // 初始化临时选择值
  groupDialogVisible.value = true
}

const handleGroupConfirm = () => {
  if (!tempGroupField.value) {
    ElMessage.warning('请选择分组字段')
    return
  }

  selectedGroupField.value = tempGroupField.value
  groupDialogVisible.value = false
  pagination.currentPage = 1

  const selectedOption = groupOptions.find(opt => opt.value === tempGroupField.value)
  ElMessage.success(`已按${selectedOption?.label}分组`)
}

const handleGroupCancel = () => {
  groupDialogVisible.value = false
  tempGroupField.value = '' // 重置临时选择
}

// 高亮文本处理
const highlightText = (text: string, keyword: string) => {
  if (!highlightEnabled.value || !keyword || !text) {
    return text
  }

  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<mark class="highlight-text">$1</mark>')
}

const handleTaskConfigSave = (formData: any) => {
  if (currentTask.value) {
    taskStore.saveTaskConfig(currentTask.value.id, formData)
    taskConfigVisible.value = false
    ElMessage.success('任务配置保存成功')
  }
}

const handleBusinessReportSelect = (selectedReports: any[]) => {
  // 处理业务报表选择
  businessReportSelectVisible.value = false
  // 调用TaskConfigDialog的方法来更新关联业务报表
  if (taskConfigRef.value && selectedReports.length > 0) {
    taskConfigRef.value.handleBusinessReportConfirm(selectedReports)
  }
}

const getSelectedReportNames = (): string[] => {
  if (!currentTask.value || !taskConfigRef.value) {
    return []
  }

  // 从TaskConfigDialog获取当前关联的业务报表
  const savedConfig = taskStore.getTaskConfig(currentTask.value.id)
  if (savedConfig && savedConfig.relatedBusinessReport) {
    // 将逗号分隔的报表名称拆分为数组
    return savedConfig.relatedBusinessReport.split('、').filter(name => name.trim())
  }

  return []
}

// 分页数据
const paginatedData = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return tableData.value.slice(start, end)
})

const handleCurrentChange = async (page: number) => {
  loading.value = true
  // 模拟接口请求延迟
  await new Promise(resolve => setTimeout(resolve, 100))
  pagination.currentPage = page
  loading.value = false
}

const handleSizeChange = async (size: number) => {
  loading.value = true
  // 模拟接口请求延迟
  await new Promise(resolve => setTimeout(resolve, 100))
  pagination.pageSize = size
  pagination.currentPage = 1
  loading.value = false
}
</script>

<route>
{
  meta: {
    title: '任务目标拆解',
    ignoreLabel: false
  }
}
</route>

<template>
  <div class="task-objective-decomposition">
    <div class="page-header">
      <h2>任务目标拆解</h2>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-controls">
        <div class="search-input-wrapper">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入任务名称"
            style="width: 300px"
            clearable
            @input="handleSearchInput"
            @focus="handleSearchFocus"
            @blur="handleSearchBlur"
            @keyup.enter="handleSearch"
          >
            <template #append>
              <el-button type="primary" @click="handleSearch">查询</el-button>
            </template>
          </el-input>

          <!-- 搜索建议下拉列表 -->
          <div v-if="showSuggestions" class="search-suggestions">
            <div
              v-for="suggestion in filteredSuggestions"
              :key="suggestion"
              class="suggestion-item"
              @click="handleSuggestionClick(suggestion)"
            >
              {{ suggestion }}
            </div>
          </div>
        </div>

        <el-button @click="handleGroupClick" style="margin-left: 12px;">
          分组
        </el-button>

        <div class="highlight-control" style="margin-left: 12px;">
          <span class="highlight-label">查询结果高亮</span>
          <el-switch
            v-model="highlightEnabled"
            style="margin-left: 8px;"
          />
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section" style="height: 500px;">
      <BaseTableComp
        :data="paginatedData"
        :colData="colData"
        :buttons="buttons"
        :loading="loading"
        :checkbox="false"
        :visibleHeader="false"
        :visibleSetting="false"
        :visibleIndex="false"
        :currentPage="pagination.currentPage"
        :pageSize="pagination.pageSize"
        :total="pagination.total"
        :auto-height="true"
        @clickButton="(data: any) => handleTableButton(data.btn, data.scope)"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <template #taskName="{ rowData }">
          <span
            class="task-name-link"
            @click="handleTaskNameClick(rowData)"
            :title="rowData.taskName"
            v-html="highlightText(rowData.taskName, searchKeyword)"
          >
          </span>
        </template>

        <template #taskStatus="{ rowData }">
          <el-tag :type="rowData.taskStatus === '执行中' ? 'success' : 'warning'">
            {{ rowData.taskStatus }}
          </el-tag>
        </template>

        <template #taskSwitch="{ rowData }">
          <el-switch
            v-model="rowData.taskSwitch"
            @change="(value) => handleTaskSwitchChange(rowData, value as boolean)"
          />
        </template>
      </BaseTableComp>
    </div>

    <!-- 任务配置弹窗 -->
    <TaskConfigDialog
      ref="taskConfigRef"
      v-model="taskConfigVisible"
      :task="currentTask"
      @save="handleTaskConfigSave"
      @open-business-report-select="businessReportSelectVisible = true"
    />

    <BusinessReportSelectDialog
      v-model="businessReportSelectVisible"
      :selected-report-names="getSelectedReportNames()"
      @confirm="handleBusinessReportSelect"
    />

    <!-- 分组弹窗 -->
    <el-dialog
      v-model="groupDialogVisible"
      title="数据分组"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="group-dialog-content">
        <p class="group-instruction">请选择分组字段：</p>
        <el-select
          v-model="tempGroupField"
          placeholder="请选择分组字段"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="option in groupOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleGroupCancel">取消</el-button>
          <el-button type="primary" @click="handleGroupConfirm">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
.task-objective-decomposition {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: #303133;
    }
  }
  
  .search-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f5f7fa;
    border-radius: 4px;
  }
  
  .table-section {
    background: white;
    border-radius: 4px;
    padding: 16px;
  }
  
  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }

  .search-controls {
    display: flex;
    align-items: center;
  }

  .search-input-wrapper {
    position: relative;
    display: inline-block;
  }

  .search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #dcdfe6;
    border-top: none;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
  }

  .suggestion-item {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 14px;
    color: #606266;
    border-bottom: 1px solid #f5f7fa;

    &:hover {
      background-color: #f5f7fa;
      color: #409eff;
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .highlight-control {
    display: flex;
    align-items: center;

    .highlight-label {
      font-size: 14px;
      color: #606266;
    }
  }

  .task-name-link {
    color: #409eff;
    cursor: pointer;
    text-decoration: none;

    &:hover {
      color: #66b1ff;
      text-decoration: underline;
    }
  }

  .group-dialog-content {
    .group-instruction {
      margin-bottom: 16px;
      font-size: 14px;
      color: #303133;
    }
  }
}

:deep(.highlight-text) {
  background-color: #fff2cc;
  color: #e6a23c;
  padding: 1px 2px;
  border-radius: 2px;
}
</style>
