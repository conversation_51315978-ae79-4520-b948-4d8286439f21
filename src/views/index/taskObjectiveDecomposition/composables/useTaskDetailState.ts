import { ref, reactive, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useTaskObjectiveStore, type TaskObjective } from '@/stores/taskObjectiveStore'

/**
 * 任务详情页面状态管理
 */
export function useTaskDetailState() {
  const route = useRoute()
  const taskStore = useTaskObjectiveStore()

  // 基础状态
  const loading = ref(false)
  const taskId = ref(route.params.id as string)
  const taskDetail = ref<TaskObjective | undefined>(undefined)

  // 弹窗状态
  const dialogStates = reactive({
    progressConfig: false,
    styleTemplate: false,
    difficultyAnalysis: false,
    timeAnalysis: false,
    reminderHistory: false,
    editTask: false,
    progressTracking: false,
    subTaskEdit: false,
    reminderSetting: false,
    riskSetting: false,
    propertyManagement: false,
    riskAnalysis: false,
    riskProcessing: false,
    subTaskHistory: false,
    subTaskMerge: false,
    subTaskSplit: false,
    batchStatusDialog: false,
    batchAssignDialog: false,
    urgencyAdjustment: false,
    importanceAdjustment: false,
    subTaskResource: false
  })

  // 加载状态
  const loadingStates = reactive({
    riskAnalysis: false,
    knowledgeSearch: false,
    subTaskHistory: false,
    subTaskMerge: false
  })

  // 当前编辑的数据
  const currentEditData = reactive({
    subTask: null as any,
    reminderSubTask: null as any,
    riskSubTask: null as any,
    propertySubTask: null as any,
    splitSubTask: null as any
  })

  // 搜索表单
  const searchForm = reactive({
    taskName: '',
    taskStatus: ''
  })

  // 分页配置
  const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0
  })

  // 选中的行数据
  const selectedRows = ref<any[]>([])

  // 批量操作相关
  const batchOperationData = reactive({
    selectedStatus: '',
    selectedResponsiblePerson: ''
  })

  // 风险分析相关
  const riskAnalysisData = reactive({
    searchInput: '',
    searchResult: '',
    selectedProcessingOption: ''
  })

  // 子任务数据
  const subTaskData = ref<any[]>([])

  // 任务统计计算属性
  const taskStatistics = computed(() => {
    const currentTaskSubTasks = subTaskData.value.filter(task => task.taskId === taskId.value)
    const totalCount = currentTaskSubTasks.length

    if (totalCount === 0) {
      return {
        businessReportCount: 0,
        submittedProgress: '0/0',
        rejectedCount: 0,
        approvedCount: 0,
        rejectionRatio: '0/0'
      }
    }

    // 模拟提交状态数据 - 基于任务进度生成合理的统计
    const submittedCount = Math.floor(totalCount * 0.8) // 80%的任务已提交
    const rejectedCount = Math.floor(totalCount * 0.1) // 10%的任务被退回
    const approvedCount = submittedCount - rejectedCount // 已通过 = 已提交 - 被退回

    return {
      businessReportCount: currentTaskSubTasks.filter(task => task.taskType === '业务报表').length,
      submittedProgress: `${submittedCount}/${totalCount}`,
      rejectedCount: rejectedCount,
      approvedCount: approvedCount,
      rejectionRatio: `${rejectedCount}/${totalCount}`
    }
  })

  // 加载子任务数据
  const loadSubTasks = () => {
    console.log('开始加载子任务数据，任务ID:', taskId.value)
    console.log('Store中的所有子任务:', taskStore.subTasks)

    const result = taskStore.subTasks
      .filter(subTask => subTask.taskId === taskId.value)
      .sort((a, b) => a.sequence - b.sequence) // 按序号排序

    console.log('过滤后的子任务数据:', result.map(st => ({ id: st.id, sequence: st.sequence, name: st.taskName })))
    subTaskData.value = result
    console.log('设置subTaskData.value完成')
    return result
  }

  // 加载任务详情
  const loadTaskDetail = async () => {
    loading.value = true
    try {
      // 模拟接口请求延迟
      await new Promise(resolve => setTimeout(resolve, 100))

      taskDetail.value = taskStore.getTaskById(taskId.value)
      if (!taskDetail.value) {
        throw new Error('任务不存在')
      }

      // 加载子任务数据
      const subTasks = loadSubTasks()
      console.log('子任务数据:', subTasks)
      pagination.total = subTasks.length
    } catch (error) {
      console.error('Load task detail error:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 重置搜索
  const resetSearch = () => {
    searchForm.taskName = ''
    searchForm.taskStatus = ''
    const allSubTasks = loadSubTasks()
    pagination.total = allSubTasks.length
    pagination.currentPage = 1
  }

  // 搜索子任务
  const searchSubTasks = () => {
    let filteredData = taskStore.subTasks.filter(subTask => subTask.taskId === taskId.value)

    // 按任务名称筛选
    if (searchForm.taskName) {
      filteredData = filteredData.filter(task =>
        task.taskName.toLowerCase().includes(searchForm.taskName.toLowerCase())
      )
    }

    // 按任务状态筛选
    if (searchForm.taskStatus) {
      filteredData = filteredData.filter(task => task.taskStatus === searchForm.taskStatus)
    }

    subTaskData.value = filteredData
    pagination.total = filteredData.length
    pagination.currentPage = 1

    return filteredData.length
  }

  return {
    // 状态
    loading,
    taskId,
    taskDetail,
    dialogStates,
    loadingStates,
    currentEditData,
    searchForm,
    pagination,
    selectedRows,
    batchOperationData,
    riskAnalysisData,
    subTaskData,

    // 计算属性
    taskStatistics,

    // 方法
    loadSubTasks,
    loadTaskDetail,
    resetSearch,
    searchSubTasks,

    // Store引用
    taskStore
  }
}
