import { ElMessage, ElMessageBox } from 'element-plus'
import { useTaskObjectiveStore } from '@/stores/taskObjectiveStore'

/**
 * 批量操作相关逻辑
 */
export function useBatchOperations() {
  const taskStore = useTaskObjectiveStore()

  // 批量删除
  const handleBatchDelete = async (selectedRows: any[], onSuccess: () => void) => {
    if (selectedRows.length === 0) {
      ElMessage.warning('请先选择要删除的子任务')
      return
    }

    try {
      await ElMessageBox.confirm(
        `确定要删除选中的 ${selectedRows.length} 个子任务吗？`,
        '批量删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )

      const selectedIds = selectedRows.map(row => row.id)
      const deletedSubTasks = taskStore.deleteSubTasks(selectedIds)

      if (deletedSubTasks.length > 0) {
        ElMessage.success(`成功删除 ${deletedSubTasks.length} 个子任务`)
        onSuccess()
      } else {
        ElMessage.error('删除失败')
      }
    } catch {
      ElMessage.info('已取消删除')
    }
  }

  // 批量复制
  const handleBatchCopy = async (selectedRows: any[], onSuccess: () => void) => {
    if (selectedRows.length === 0) {
      ElMessage.warning('请先选择要复制的子任务')
      return
    }

    try {
      await ElMessageBox.confirm(
        `确定要复制选中的 ${selectedRows.length} 个子任务吗？`,
        '批量复制确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info',
        }
      )

      let successCount = 0
      const copiedTasks: any[] = []

      selectedRows.forEach(row => {
        const copiedSubTask = taskStore.copySubTask(row.id)
        if (copiedSubTask) {
          successCount++
          copiedTasks.push(copiedSubTask)
        }
      })

      if (successCount > 0) {
        ElMessage.success(`成功复制 ${successCount} 个子任务`)
        onSuccess()

        // 显示复制的任务名称
        const taskNames = copiedTasks.map(task => task.taskName).join('、')
        ElMessage.info(`复制的任务：${taskNames}`)
      } else {
        ElMessage.error('复制失败')
      }
    } catch {
      ElMessage.info('已取消复制')
    }
  }

  // 批量状态更新
  const handleBatchStatusUpdate = (selectedRows: any[], selectedStatus: string, onSuccess: () => void) => {
    if (selectedRows.length === 0) {
      ElMessage.warning('请先选择要更新的子任务')
      return false
    }

    if (!selectedStatus) {
      ElMessage.warning('请选择要更新的状态')
      return false
    }

    let successCount = 0
    selectedRows.forEach(row => {
      const updated = taskStore.updateSubTask(row.id, { taskStatus: selectedStatus as any })
      if (updated) successCount++
    })

    if (successCount > 0) {
      ElMessage.success(`成功更新 ${successCount} 个子任务状态`)
      onSuccess()
      return true
    } else {
      ElMessage.error('状态更新失败')
      return false
    }
  }

  // 批量分配责任人
  const handleBatchAssign = (selectedRows: any[], selectedResponsiblePerson: string, onSuccess: () => void) => {
    if (selectedRows.length === 0) {
      ElMessage.warning('请先选择要分配的子任务')
      return false
    }

    if (!selectedResponsiblePerson) {
      ElMessage.warning('请选择责任人')
      return false
    }

    let successCount = 0
    selectedRows.forEach(row => {
      const updated = taskStore.updateSubTask(row.id, { responsiblePerson: selectedResponsiblePerson })
      if (updated) successCount++
    })

    if (successCount > 0) {
      ElMessage.success(`成功分配 ${successCount} 个子任务责任人`)
      onSuccess()
      return true
    } else {
      ElMessage.error('责任人分配失败')
      return false
    }
  }

  // 子任务合并
  const handleSubTaskMerge = async (
    selectedRows: any[], 
    mergeFormData: any, 
    taskId: string, 
    onSuccess: () => void
  ) => {
    if (selectedRows.length < 2) {
      ElMessage.warning('请至少选择2个子任务进行合并')
      return false
    }

    // 表单验证
    if (!mergeFormData.taskType) {
      ElMessage.warning('请选择子任务类型')
      return false
    }
    if (!mergeFormData.taskName.trim()) {
      ElMessage.warning('请输入合并后子任务名称')
      return false
    }
    if (!mergeFormData.taskCategory) {
      ElMessage.warning('请选择子任务分类')
      return false
    }
    if (!mergeFormData.priorityLevel) {
      ElMessage.warning('请选择子任务优先级设置')
      return false
    }
    if (!mergeFormData.responsiblePerson.trim()) {
      ElMessage.warning('请选择责任人')
      return false
    }
    if (!mergeFormData.participants || mergeFormData.participants.length === 0) {
      ElMessage.warning('请选择参与人')
      return false
    }

    try {
      // 模拟合并处理过程
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 获取选中的任务信息
      const selectedTaskNames = selectedRows.map(row => row.taskName).join('、')
      const selectedIds = selectedRows.map(row => row.id)

      // 删除原来的子任务
      const deletedSubTasks = taskStore.deleteSubTasks(selectedIds)

      if (deletedSubTasks.length > 0) {
        // 创建新的合并后的子任务
        const newSubTask = taskStore.addSubTask({
          taskId: taskId,
          taskName: mergeFormData.taskName,
          taskType: mergeFormData.taskType,
          taskCategory: mergeFormData.taskCategory,
          responsiblePerson: mergeFormData.responsiblePerson,
          participants: mergeFormData.participants.join('、'), // 将数组转换为字符串
          taskStatus: '执行中' as any,
          progress: 0
        })

        if (newSubTask) {
          ElMessage.success(`成功合并 ${deletedSubTasks.length} 个子任务为："${mergeFormData.taskName}"`)
          onSuccess()
          return true
        } else {
          ElMessage.error('合并失败，请重试')
          return false
        }
      } else {
        ElMessage.error('删除原任务失败')
        return false
      }
    } catch (error) {
      console.error('合并失败:', error)
      ElMessage.error('合并失败，请重试')
      return false
    }
  }

  return {
    handleBatchDelete,
    handleBatchCopy,
    handleBatchStatusUpdate,
    handleBatchAssign,
    handleSubTaskMerge
  }
}
