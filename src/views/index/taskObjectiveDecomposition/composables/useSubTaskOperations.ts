import { nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useTaskObjectiveStore } from '@/stores/taskObjectiveStore'

/**
 * 子任务操作相关逻辑
 */
export function useSubTaskOperations() {
  const router = useRouter()
  const taskStore = useTaskObjectiveStore()

  // 检查是否可以上移的函数
  const canMoveUp = (row: any, subTaskData: any[], taskId: string) => {
    const sortedTasks = subTaskData
      .filter(task => task.taskId === taskId)
      .sort((a, b) => a.sequence - b.sequence)
    const currentIndex = sortedTasks.findIndex(task => task.id === row.id)
    return currentIndex > 0
  }

  // 检查是否可以下移的函数
  const canMoveDown = (row: any, subTaskData: any[], taskId: string) => {
    const sortedTasks = subTaskData
      .filter(task => task.taskId === taskId)
      .sort((a, b) => a.sequence - b.sequence)
    const currentIndex = sortedTasks.findIndex(task => task.id === row.id)
    return currentIndex < sortedTasks.length - 1
  }

  // 详情子任务
  const handleDetailSubTask = (row: any) => {
    // 跳转到子任务详情页面
    const currentQuery = router.currentRoute.value.query
    router.push({
      path: `/taskObjectiveDecomposition/subtask/${row.id}`,
      query: currentQuery
    })
  }

  // 关联子任务
  const handleAssociateSubTask = (row: any) => {
    // 跳转到子任务关联任务计算页面
    const currentQuery = router.currentRoute.value.query
    router.push({
      path: `/taskObjectiveDecomposition/associate/${row.id}`,
      query: currentQuery
    })
  }

  // 删除子任务
  const handleDeleteSubTask = async (row: any, onSuccess: () => void) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除子任务"${row.taskName}"吗？`,
        '删除确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )

      const deletedSubTask = taskStore.deleteSubTask(row.id)
      if (deletedSubTask) {
        ElMessage.success('删除成功')
        onSuccess()
      } else {
        ElMessage.error('删除失败')
      }
    } catch {
      ElMessage.info('已取消删除')
    }
  }

  // 复制子任务
  const handleCopySubTask = (row: any, onSuccess: () => void) => {
    const copiedSubTask = taskStore.copySubTask(row.id)
    if (copiedSubTask) {
      ElMessage.success(`复制成功: ${copiedSubTask.taskName}`)
      onSuccess()
    } else {
      ElMessage.error('复制失败')
    }
  }

  // 提交子任务
  const handleSubmitSubTask = (row: any, onSuccess: () => void) => {
    ElMessageBox.confirm(
      '你正在进行子任务提交操作，提交后审核人可以查看任务信息，请确认操作。',
      '子任务提交确认',
      {
        confirmButtonText: '确认提交',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(() => {
      // 更新任务状态为待审核
      const updated = taskStore.updateSubTask(row.id, { taskStatus: '待审核' })
      if (updated) {
        ElMessage.success(`子任务"${row.taskName}"提交成功，状态已更新为待审核`)
        onSuccess()
      } else {
        ElMessage.error('提交失败')
      }
    }).catch(() => {
      ElMessage.info('已取消提交')
    })
  }

  // 审核子任务 - 跳转到审核页面
  const handleAuditSubTask = (row: any, onSuccess?: () => void) => {
    // 跳转到子任务审核页面
    const currentQuery = router.currentRoute.value.query
    router.push({
      path: `/taskObjectiveDecomposition/audit/${row.id}`,
      query: currentQuery
    })
  }

  // 上移子任务
  const handleMoveUp = async (row: any, subTaskData: any[], taskId: string, onSuccess: () => void) => {
    // 检查是否可以上移
    const sortedTasks = subTaskData
      .filter(task => task.taskId === taskId)
      .sort((a, b) => a.sequence - b.sequence)
    const currentIndex = sortedTasks.findIndex(task => task.id === row.id)

    if (currentIndex <= 0) {
      ElMessage.warning(`子任务"${row.taskName}"已经是第一个，无法上移`)
      return
    }

    const success = taskStore.moveSubTaskUp(row.id)
    if (success) {
      ElMessage.success(`子任务"${row.taskName}"上移成功`)
      // 使用nextTick确保DOM更新
      await nextTick()
      onSuccess()
      console.log('上移后的子任务数据')
    } else {
      ElMessage.warning(`子任务"${row.taskName}"上移失败`)
    }
  }

  // 下移子任务
  const handleMoveDown = async (row: any, subTaskData: any[], taskId: string, onSuccess: () => void) => {
    // 检查是否可以下移
    const sortedTasks = subTaskData
      .filter(task => task.taskId === taskId)
      .sort((a, b) => a.sequence - b.sequence)
    const currentIndex = sortedTasks.findIndex(task => task.id === row.id)

    if (currentIndex >= sortedTasks.length - 1) {
      ElMessage.warning(`子任务"${row.taskName}"已经是最后一个，无法下移`)
      return
    }

    const success = taskStore.moveSubTaskDown(row.id)
    if (success) {
      ElMessage.success(`子任务"${row.taskName}"下移成功`)
      // 使用nextTick确保DOM更新
      await nextTick()
      onSuccess()
      console.log('下移后的子任务数据')
    } else {
      ElMessage.warning(`子任务"${row.taskName}"下移失败`)
    }
  }

  // 进度追踪
  const handleProgressTracking = (row: any) => {
    // 根据选中的任务动态生成进度追踪数据
    const taskProgress = row.progress || 0
    return [
      {
        milestone: '任务接收',
        completionTime: row.createTime || '2025-04-23',
        completionRate: taskProgress >= 10 ? '100%' : '0%'
      },
      {
        milestone: '数据填报',
        completionTime: taskProgress >= 30 ? row.updateTime || '2025-04-30' : '未完成',
        completionRate: taskProgress >= 30 ? '100%' : `${Math.min(taskProgress * 3, 100)}%`
      },
      {
        milestone: '数据审核',
        completionTime: taskProgress >= 70 ? '2025-05-03' : '未完成',
        completionRate: taskProgress >= 70 ? '100%' : `${Math.max(0, (taskProgress - 30) * 2)}%`
      },
      {
        milestone: '任务完成',
        completionTime: taskProgress >= 100 ? '2025-05-10' : '未完成',
        completionRate: taskProgress >= 100 ? '100%' : '0%'
      }
    ]
  }

  // 子任务编辑确认处理
  const handleSubTaskEditConfirm = (data: any, taskId: string, onSuccess: () => void) => {
    if (data.id) {
      // 编辑模式
      const updatedSubTask = taskStore.updateSubTask(data.id, data)
      if (updatedSubTask) {
        ElMessage.success('编辑成功')
        onSuccess()
      } else {
        ElMessage.error('编辑失败')
      }
    } else {
      // 新增模式
      const newSubTask = taskStore.addSubTask({
        ...data,
        taskId: taskId
      })
      if (newSubTask) {
        ElMessage.success('新增成功')
        onSuccess()
      } else {
        ElMessage.error('新增失败')
      }
    }
  }

  // 子任务拆分处理
  const handleSubTaskSplit = async (
    originalTask: any,
    splitData: any,
    onSuccess: () => void
  ) => {
    try {
      // 表单验证
      if (!splitData.splitCount || splitData.splitCount < 2) {
        ElMessage.warning('拆分数量必须至少为2个')
        return false
      }
      if (!splitData.taskType) {
        ElMessage.warning('请选择子任务类型')
        return false
      }
      if (!splitData.taskCategory) {
        ElMessage.warning('请选择子任务分类')
        return false
      }
      if (!splitData.priorityLevel) {
        ElMessage.warning('请选择子任务优先级')
        return false
      }
      if (!splitData.responsiblePerson) {
        ElMessage.warning('请选择责任人')
        return false
      }
      if (!splitData.participants || splitData.participants.length === 0) {
        ElMessage.warning('请选择参与人')
        return false
      }

      // 模拟拆分处理过程
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 删除原任务
      const deletedTask = taskStore.deleteSubTask(originalTask.id)
      if (!deletedTask) {
        ElMessage.error('删除原任务失败')
        return false
      }

      // 创建拆分后的子任务
      const createdTasks: any[] = []
      for (let i = 1; i <= splitData.splitCount; i++) {
        const newSubTask = taskStore.addSubTask({
          taskId: originalTask.taskId,
          taskName: `${originalTask.taskName}_拆分${i}`,
          taskType: splitData.taskType,
          taskCategory: splitData.taskCategory,
          responsiblePerson: splitData.responsiblePerson,
          participants: splitData.participants.join('、'),
          taskStatus: '未提交' as any,
          progress: 0,
          urgencyLevel: originalTask.urgencyLevel || 'P3',
          importanceLevel: originalTask.importanceLevel || 'I3'
        })

        if (newSubTask) {
          createdTasks.push(newSubTask)
        }
      }

      if (createdTasks.length === splitData.splitCount) {
        ElMessage.success(`成功将任务"${originalTask.taskName}"拆分为 ${splitData.splitCount} 个子任务`)
        onSuccess()
        return true
      } else {
        ElMessage.error('部分子任务创建失败')
        return false
      }
    } catch (error) {
      console.error('拆分失败:', error)
      ElMessage.error('拆分失败，请重试')
      return false
    }
  }

  return {
    canMoveUp,
    canMoveDown,
    handleDetailSubTask,
    handleAssociateSubTask,
    handleDeleteSubTask,
    handleCopySubTask,
    handleSubmitSubTask,
    handleAuditSubTask,
    handleMoveUp,
    handleMoveDown,
    handleProgressTracking,
    handleSubTaskEditConfirm,
    handleSubTaskSplit
  }
}
