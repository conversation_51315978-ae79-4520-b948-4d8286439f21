import { ElMessage } from 'element-plus'

/**
 * 数据导出工具函数
 */

// 导出子任务列表
export const exportSubTaskList = (subTaskData: any[], taskName?: string) => {
  try {
    // 准备导出数据
    const exportData = subTaskData.map((item, index) => ({
      '序号': index + 1,
      '子任务名称': item.taskName,
      '子任务类型': item.taskType,
      '子任务分类': item.taskCategory,
      '责任人': item.responsiblePerson,
      '参与人': item.participants,
      '任务状态': item.taskStatus,
      '任务进度': `${item.progress}%`,
      '创建时间': item.createTime,
      '更新时间': item.updateTime
    }))

    // 创建CSV内容
    const headers = ['序号', '子任务名称', '子任务类型', '子任务分类', '责任人', '参与人', '任务状态', '任务进度', '创建时间', '更新时间']
    const csvContent = [
      headers.join(','),
      ...exportData.map(row =>
        headers.map(header => `"${(row as any)[header] || ''}"`).join(',')
      )
    ].join('\n')

    // 创建下载链接
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)

    // 生成文件名
    const fileName = `${taskName || '任务'}_子任务列表_${new Date().toISOString().split('T')[0]}.csv`
    link.setAttribute('download', fileName)
    link.style.visibility = 'hidden'

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 导出风险分析数据
export const exportRiskAnalysis = (riskAnalysisData: any[], taskName?: string) => {
  try {
    // 准备导出数据
    const exportData = riskAnalysisData.map((item, index) => ({
      '序号': index + 1,
      '风险项': item.riskItem,
      '发生概率(P)': item.probability,
      '影响程度(I)': item.impact,
      '风险等级': item.riskLevel,
      '优先级': item.priority
    }))

    // 创建CSV内容
    const headers = ['序号', '风险项', '发生概率(P)', '影响程度(I)', '风险等级', '优先级']
    const csvContent = [
      headers.join(','),
      ...exportData.map(row =>
        headers.map(header => `"${(row as any)[header] || ''}"`).join(',')
      )
    ].join('\n')

    // 创建下载链接
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)

    // 生成文件名
    const fileName = `${taskName || '任务'}_风险分析_${new Date().toISOString().split('T')[0]}.csv`
    link.setAttribute('download', fileName)
    link.style.visibility = 'hidden'

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success('风险分析导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

// 通用CSV导出函数
export const exportToCSV = (
  data: any[], 
  headers: string[], 
  fileName: string,
  successMessage: string = '导出成功'
) => {
  try {
    // 创建CSV内容
    const csvContent = [
      headers.join(','),
      ...data.map(row =>
        headers.map(header => `"${row[header] || ''}"`).join(',')
      )
    ].join('\n')

    // 创建下载链接
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', fileName)
    link.style.visibility = 'hidden'

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success(successMessage)
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}
