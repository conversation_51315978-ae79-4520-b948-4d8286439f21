/**
 * 图谱布局算法 Composable
 * 集成 dagre 自动布局算法
 */

import { ref, computed, onMounted } from 'vue'
import dagre from 'dagre'
import type { Node, Edge, Position } from '@vue-flow/core'
import type { TaskGraphNode, TaskGraphEdge } from '@/define/taskGraph.define'
import { ElMessage } from 'element-plus'

/**
 * 布局方向枚举
 */
export enum LayoutDirection {
  /** 从上到下 */
  TOP_BOTTOM = 'TB',
  /** 从下到上 */
  BOTTOM_TOP = 'BT',
  /** 从左到右 */
  LEFT_RIGHT = 'LR',
  /** 从右到左 */
  RIGHT_LEFT = 'RL'
}

/**
 * 布局配置接口
 */
export interface LayoutConfig {
  /** 布局方向 */
  direction: LayoutDirection
  /** 节点间距 */
  nodeSpacing: number
  /** 层级间距 */
  rankSpacing: number
  /** 边缘间距 */
  edgeSpacing: number
  /** 是否对齐 */
  align?: 'UL' | 'UR' | 'DL' | 'DR'
  /** 排序器 */
  ranker?: 'network-simplex' | 'tight-tree' | 'longest-path'
}

/**
 * 默认布局配置
 */
const DEFAULT_LAYOUT_CONFIG: LayoutConfig = {
  direction: LayoutDirection.TOP_BOTTOM,
  nodeSpacing: 50,
  rankSpacing: 100,
  edgeSpacing: 10,
  align: 'UL',
  ranker: 'network-simplex'
}

/**
 * 图谱布局 Composable
 */
export const useGraphLayout = () => {
  // ==================== 响应式状态 ====================
  
  /** 当前布局配置 */
  const layoutConfig = ref<LayoutConfig>({ ...DEFAULT_LAYOUT_CONFIG })
  
  /** 是否正在布局 */
  const isLayouting = ref(false)
  
  /** 布局历史 */
  const layoutHistory = ref<Array<{ nodes: Node[], timestamp: number, config: LayoutConfig }>>([])
  
  /** 当前历史索引 */
  const currentHistoryIndex = ref(-1)

  // ==================== 数据持久化 ====================

  /** 布局历史存储键名 */
  const LAYOUT_HISTORY_STORAGE_KEY = 'graph-layout-history'

  /**
   * 从 localStorage 加载布局历史
   */
  const loadLayoutHistoryFromStorage = () => {
    try {
      // 首先尝试从新的存储键加载
      const stored = localStorage.getItem(LAYOUT_HISTORY_STORAGE_KEY)

      if (stored) {
        const parsedHistory = JSON.parse(stored)

        if (Array.isArray(parsedHistory)) {
          layoutHistory.value = parsedHistory
          currentHistoryIndex.value = parsedHistory.length - 1
          return
        }
      }

      // 如果新存储键没有数据，尝试从旧的确认布局数据中迁移
      const allKeys = Object.keys(localStorage)
      const layoutKeys = allKeys.filter(key => key.startsWith('layout_confirmed_graph-'))

      if (layoutKeys.length > 0) {
        const migratedHistory = []

        layoutKeys.forEach(key => {
          try {
            const layoutData = localStorage.getItem(key)
            if (layoutData) {
              const parsed = JSON.parse(layoutData)
              if (parsed.nodes && parsed.timestamp) {
                migratedHistory.push({
                  nodes: parsed.nodes,
                  timestamp: parsed.timestamp,
                  config: { mode: parsed.mode || 'manual' }
                })
              }
            }
          } catch (error) {
            console.warn('迁移布局数据失败:', key, error)
          }
        })

        // 按时间戳排序
        migratedHistory.sort((a, b) => a.timestamp - b.timestamp)

        if (migratedHistory.length > 0) {
          layoutHistory.value = migratedHistory
          currentHistoryIndex.value = migratedHistory.length - 1

          // 保存迁移后的数据到新存储键
          saveLayoutHistoryToStorage()
          return
        }
      }
    } catch (error) {
      console.error('加载布局历史失败:', error)
      layoutHistory.value = []
      currentHistoryIndex.value = -1
    }
  }

  /**
   * 保存布局历史到 localStorage
   */
  const saveLayoutHistoryToStorage = () => {
    try {
      localStorage.setItem(LAYOUT_HISTORY_STORAGE_KEY, JSON.stringify(layoutHistory.value))
    } catch (error) {
      console.error('保存布局历史失败:', error)
    }
  }

  // ==================== 计算属性 ====================
  
  /** 是否可以撤销 */
  const canUndo = computed(() => currentHistoryIndex.value > 0)
  
  /** 是否可以重做 */
  const canRedo = computed(() => currentHistoryIndex.value < layoutHistory.value.length - 1)
  
  // ==================== 核心布局方法 ====================
  
  /**
   * 应用 dagre 自动布局
   */
  const applyDagreLayout = (
    nodes: TaskGraphNode[], 
    edges: TaskGraphEdge[], 
    config: Partial<LayoutConfig> = {}
  ): TaskGraphNode[] => {
    const finalConfig = { ...layoutConfig.value, ...config }
    
    try {
      isLayouting.value = true
      
      // 创建 dagre 图
      const graph = new dagre.graphlib.Graph()
      
      // 设置图的默认属性
      graph.setDefaultEdgeLabel(() => ({}))
      graph.setGraph({
        rankdir: finalConfig.direction,
        nodesep: finalConfig.nodeSpacing,
        ranksep: finalConfig.rankSpacing,
        edgesep: finalConfig.edgeSpacing,
        align: finalConfig.align,
        ranker: finalConfig.ranker
      })
      
      // 添加节点到图中
      nodes.forEach(node => {
        graph.setNode(node.id, {
          width: getNodeWidth(node),
          height: getNodeHeight(node)
        })
      })
      
      // 添加边到图中
      edges.forEach(edge => {
        if (edge.source && edge.target) {
          graph.setEdge(edge.source, edge.target)
        }
      })
      
      // 执行布局计算
      dagre.layout(graph)
      
      // 应用计算结果到节点
      const layoutedNodes = nodes.map(node => {
        const nodeWithPosition = graph.node(node.id)
        
        return {
          ...node,
          position: {
            x: nodeWithPosition.x - nodeWithPosition.width / 2,
            y: nodeWithPosition.y - nodeWithPosition.height / 2
          }
        }
      })
      
      // 保存到历史记录
      saveToHistory(layoutedNodes, finalConfig)
      
      ElMessage.success('自动布局完成')
      return layoutedNodes
      
    } catch (error) {
      console.error('布局计算失败:', error)
      ElMessage.error('自动布局失败')
      return nodes
    } finally {
      isLayouting.value = false
    }
  }
  
  /**
   * 应用层次布局（适用于树形结构）
   */
  const applyHierarchicalLayout = (
    nodes: TaskGraphNode[], 
    edges: TaskGraphEdge[]
  ): TaskGraphNode[] => {
    return applyDagreLayout(nodes, edges, {
      direction: LayoutDirection.TOP_BOTTOM,
      nodeSpacing: 80,
      rankSpacing: 120,
      ranker: 'tight-tree'
    })
  }
  
  /**
   * 应用网格布局
   */
  const applyGridLayout = (
    nodes: TaskGraphNode[], 
    columns: number = 4
  ): TaskGraphNode[] => {
    try {
      isLayouting.value = true
      
      const nodeWidth = 200
      const nodeHeight = 100
      const spacing = 50
      
      const layoutedNodes = nodes.map((node, index) => {
        const row = Math.floor(index / columns)
        const col = index % columns
        
        return {
          ...node,
          position: {
            x: col * (nodeWidth + spacing),
            y: row * (nodeHeight + spacing)
          }
        }
      })
      
      saveToHistory(layoutedNodes, layoutConfig.value)
      ElMessage.success('网格布局完成')
      return layoutedNodes
      
    } catch (error) {
      console.error('网格布局失败:', error)
      ElMessage.error('网格布局失败')
      return nodes
    } finally {
      isLayouting.value = false
    }
  }
  
  /**
   * 应用圆形布局
   */
  const applyCircularLayout = (
    nodes: TaskGraphNode[], 
    radius: number = 300
  ): TaskGraphNode[] => {
    try {
      isLayouting.value = true
      
      const centerX = radius
      const centerY = radius
      const angleStep = (2 * Math.PI) / nodes.length
      
      const layoutedNodes = nodes.map((node, index) => {
        const angle = index * angleStep
        
        return {
          ...node,
          position: {
            x: centerX + radius * Math.cos(angle) - 100, // 减去节点宽度的一半
            y: centerY + radius * Math.sin(angle) - 50   // 减去节点高度的一半
          }
        }
      })
      
      saveToHistory(layoutedNodes, layoutConfig.value)
      ElMessage.success('圆形布局完成')
      return layoutedNodes
      
    } catch (error) {
      console.error('圆形布局失败:', error)
      ElMessage.error('圆形布局失败')
      return nodes
    } finally {
      isLayouting.value = false
    }
  }
  
  // ==================== 辅助方法 ====================
  
  /**
   * 获取节点宽度
   */
  const getNodeWidth = (node: TaskGraphNode): number => {
    // 根据节点类型和内容计算宽度
    const baseWidth = 160
    const labelLength = node.data?.label?.length || 0
    return Math.max(baseWidth, labelLength * 8 + 40)
  }
  
  /**
   * 获取节点高度
   */
  const getNodeHeight = (node: TaskGraphNode): number => {
    // 根据节点类型和内容计算高度
    const baseHeight = 80
    const hasDescription = !!node.data?.description
    return hasDescription ? baseHeight + 20 : baseHeight
  }
  
  /**
   * 保存布局到历史记录
   */
  const saveToHistory = (nodes: TaskGraphNode[], config: LayoutConfig) => {
    const historyItem = {
      nodes: JSON.parse(JSON.stringify(nodes)),
      timestamp: Date.now(),
      config: { ...config }
    }

    // 如果当前不在历史记录的末尾，删除后面的记录
    if (currentHistoryIndex.value < layoutHistory.value.length - 1) {
      layoutHistory.value = layoutHistory.value.slice(0, currentHistoryIndex.value + 1)
    }

    layoutHistory.value.push(historyItem)
    currentHistoryIndex.value = layoutHistory.value.length - 1

    // 限制历史记录数量
    if (layoutHistory.value.length > 20) {
      layoutHistory.value.shift()
      currentHistoryIndex.value--
    }

    // 保存到 localStorage
    saveLayoutHistoryToStorage()
  }
  
  /**
   * 撤销布局
   */
  const undoLayout = (): TaskGraphNode[] | null => {
    if (!canUndo.value) return null
    
    currentHistoryIndex.value--
    const historyItem = layoutHistory.value[currentHistoryIndex.value]
    
    if (historyItem) {
      layoutConfig.value = { ...historyItem.config }
      ElMessage.success('撤销布局')
      return JSON.parse(JSON.stringify(historyItem.nodes))
    }
    
    return null
  }
  
  /**
   * 重做布局
   */
  const redoLayout = (): TaskGraphNode[] | null => {
    if (!canRedo.value) return null
    
    currentHistoryIndex.value++
    const historyItem = layoutHistory.value[currentHistoryIndex.value]
    
    if (historyItem) {
      layoutConfig.value = { ...historyItem.config }
      ElMessage.success('重做布局')
      return JSON.parse(JSON.stringify(historyItem.nodes))
    }
    
    return null
  }
  
  /**
   * 清空历史记录
   */
  const clearHistory = () => {
    layoutHistory.value = []
    currentHistoryIndex.value = -1
    // 清除 localStorage 中的数据
    localStorage.removeItem(LAYOUT_HISTORY_STORAGE_KEY)
  }
  
  /**
   * 更新布局配置
   */
  const updateLayoutConfig = (config: Partial<LayoutConfig>) => {
    layoutConfig.value = { ...layoutConfig.value, ...config }
  }
  
  /**
   * 重置布局配置
   */
  const resetLayoutConfig = () => {
    layoutConfig.value = { ...DEFAULT_LAYOUT_CONFIG }
  }

  /**
   * 从历史记录还原布局
   */
  const restoreFromHistory = (historyIndex: number, updateNodesFn?: (nodes: TaskGraphNode[]) => void) => {
    if (historyIndex < 0 || historyIndex >= layoutHistory.value.length) {
      console.error('无效的历史记录索引:', historyIndex)
      return false
    }

    const historyItem = layoutHistory.value[historyIndex]
    if (!historyItem || !historyItem.nodes) {
      console.error('历史记录数据无效:', historyItem)
      return false
    }

    try {
      // 如果提供了更新函数，使用它来更新节点
      if (updateNodesFn) {
        updateNodesFn(historyItem.nodes)
      }

      // 更新当前历史索引
      currentHistoryIndex.value = historyIndex

      // 更新布局配置
      if (historyItem.config) {
        layoutConfig.value = { ...layoutConfig.value, ...historyItem.config }
      }

      return true
    } catch (error) {
      console.error('还原布局失败:', error)
      return false
    }
  }

  // ==================== 初始化 ====================

  // 在组件挂载时加载历史记录
  onMounted(() => {
    loadLayoutHistoryFromStorage()
  })

  // ==================== 返回接口 ====================
  
  return {
    // 状态
    layoutConfig,
    isLayouting,
    layoutHistory,
    canUndo,
    canRedo,
    
    // 布局方法
    applyDagreLayout,
    applyHierarchicalLayout,
    applyGridLayout,
    applyCircularLayout,
    
    // 历史操作
    undoLayout,
    redoLayout,
    clearHistory,
    restoreFromHistory,
    
    // 配置管理
    updateLayoutConfig,
    resetLayoutConfig,
    
    // 常量
    LayoutDirection,
    DEFAULT_LAYOUT_CONFIG
  }
}
