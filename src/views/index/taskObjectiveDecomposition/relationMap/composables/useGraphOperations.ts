/**
 * 图谱操作组合式API
 * 封装图谱编辑相关的业务逻辑
 */

import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useGraphStore } from '../stores/useGraphStore'
import { useGraphLayout } from './useGraphLayout'
import {
  TaskNodeType,
  EdgeType,
  TaskStatus,
  TaskPriority,
  NodeSize,
  NODE_STYLE_MAP
} from '@/define/taskGraph.define'
import type {
  TaskGraphNode,
  TaskGraphEdge,
  TaskNodeData,
  EdgeData
} from '@/define/taskGraph.define'

/**
 * 图谱操作Composable
 */
export const useGraphOperations = () => {
  const graphStore = useGraphStore()
  const graphLayout = useGraphLayout()
  
  // ==================== 响应式状态 ====================
  
  /** 当前编辑的节点 */
  const editingNode = ref<TaskGraphNode | null>(null)
  
  /** 当前编辑的连线 */
  const editingEdge = ref<TaskGraphEdge | null>(null)
  
  /** 是否显示节点编辑对话框 */
  const showNodeEditDialog = ref(false)
  
  /** 是否显示连线编辑对话框 */
  const showEdgeEditDialog = ref(false)
  
  /** 拖拽状态 */
  const isDragging = ref(false)
  
  /** 连线创建状态 */
  const isConnecting = ref(false)
  const connectionSource = ref<string | null>(null)

  // ==================== 计算属性 ====================
  
  /** 是否有选中的元素 */
  const hasSelection = computed(() => graphStore.selectedElements.length > 0)
  
  /** 选中元素的数量 */
  const selectionCount = computed(() => graphStore.selectedElements.length)
  
  /** 是否可以删除选中的元素 */
  const canDeleteSelection = computed(() => {
    return graphStore.selectedNodes.every(node => node.deletable !== false) &&
           graphStore.selectedEdges.length > 0
  })

  // ==================== 节点操作方法 ====================
  
  /**
   * 创建新节点
   */
  const createNode = (
    type: TaskNodeType, 
    position: { x: number; y: number },
    data?: Partial<TaskNodeData>
  ) => {
    const styleConfig = NODE_STYLE_MAP[type]
    
    const nodeData: TaskNodeData = {
      label: data?.label || `新${type === 'main-task' ? '主任务' : type === 'sub-task' ? '子任务' : '节点'}`,
      description: data?.description || '',
      status: data?.status || TaskStatus.NOT_STARTED,
      priority: data?.priority || TaskPriority.MEDIUM,
      assignee: data?.assignee || '',
      participants: data?.participants || [],
      tags: data?.tags || [],
      progress: data?.progress || 0,
      color: styleConfig.backgroundColor,
      size: styleConfig.size.width > 100 ? NodeSize.LARGE :
            styleConfig.size.width > 90 ? NodeSize.MEDIUM : NodeSize.SMALL,
      ...data
    }
    
    const newNode = graphStore.addNode({
      type: 'default' as any, // 使用 default 类型以匹配 Vue Flow 模板
      position,
      data: {
        ...nodeData,
        originalType: type // 保留原始类型信息用于样式计算
      },
      draggable: true,
      selectable: true,
      deletable: type !== 'main-task', // 主任务节点不可删除
      connectable: true
    })
    
    if (newNode) {
      // 选中新创建的节点
      graphStore.clearSelection()
      graphStore.selectElement(newNode.id)
      
      ElMessage.success(`创建${nodeData.label}成功`)
    }
    
    return newNode
  }
  
  /**
   * 编辑节点
   */
  const editNode = (nodeId: string) => {
    const node = graphStore.nodes.find(n => n.id === nodeId)
    if (!node) {
      ElMessage.error('节点不存在')
      return
    }

    editingNode.value = { ...node }
    showNodeEditDialog.value = true
  }

  /**
   * 快速编辑节点标签
   */
  const quickEditNodeLabel = (nodeId: string, newLabel: string) => {
    const node = graphStore.nodes.find(n => n.id === nodeId)
    if (!node) {
      ElMessage.error('节点不存在')
      return false
    }

    const result = graphStore.updateNode(nodeId, {
      data: {
        ...node.data,
        label: newLabel.trim()
      }
    })

    if (result) {
      ElMessage.success('节点标签更新成功')
      return true
    } else {
      ElMessage.error('节点标签更新失败')
      return false
    }
  }
  
  /**
   * 保存节点编辑
   */
  const saveNodeEdit = (updates: Partial<TaskGraphNode>) => {
    if (!editingNode.value) return
    
    const result = graphStore.updateNode(editingNode.value.id, updates)
    if (result) {
      ElMessage.success('节点更新成功')
      showNodeEditDialog.value = false
      editingNode.value = null
    } else {
      ElMessage.error('节点更新失败')
    }
  }
  
  /**
   * 删除节点
   */
  const deleteNode = async (nodeId: string) => {
    const node = graphStore.nodes.find(n => n.id === nodeId)
    if (!node) return
    
    // 主任务节点不可删除
    if (node.type === 'main-task') {
      ElMessage.warning('主任务节点不可删除')
      return
    }
    
    try {
      await ElMessageBox.confirm(
        `确定要删除节点"${node.data.label}"吗？删除后将同时删除相关连线。`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      const success = graphStore.deleteNode(nodeId)
      if (success) {
        ElMessage.success('节点删除成功')
      } else {
        ElMessage.error('节点删除失败')
      }
    } catch {
      // 用户取消删除
    }
  }
  
  /**
   * 复制节点
   */
  const duplicateNode = (nodeId: string) => {
    const node = graphStore.nodes.find(n => n.id === nodeId)
    if (!node) return
    
    // 计算新位置（偏移50px）
    const newPosition = {
      x: node.position.x + 50,
      y: node.position.y + 50
    }
    
    const newNode = graphStore.addNode({
      ...node,
      position: newPosition,
      data: {
        ...node.data,
        label: `${node.data.label} (副本)`
      }
    })
    
    if (newNode) {
      // 选中新复制的节点
      graphStore.clearSelection()
      graphStore.selectElement(newNode.id)
      
      ElMessage.success('节点复制成功')
    }
    
    return newNode
  }

  // ==================== 连线操作方法 ====================
  
  /**
   * 开始创建连线
   */
  const startConnection = (sourceNodeId: string) => {
    isConnecting.value = true
    connectionSource.value = sourceNodeId
  }
  
  /**
   * 完成连线创建
   */
  const completeConnection = (targetNodeId: string, edgeType: EdgeType = EdgeType.DEPENDENCY) => {
    if (!connectionSource.value || connectionSource.value === targetNodeId) {
      cancelConnection()
      return
    }
    
    const sourceNode = graphStore.nodes.find(n => n.id === connectionSource.value)
    const targetNode = graphStore.nodes.find(n => n.id === targetNodeId)
    
    if (!sourceNode || !targetNode) {
      ElMessage.error('连线节点不存在')
      cancelConnection()
      return
    }
    
    const edgeData: EdgeData = {
      label: `${sourceNode.data.label} → ${targetNode.data.label}`,
      description: '任务依赖关系',
      animated: edgeType === EdgeType.SEQUENCE
    }
    
    const newEdge = graphStore.addEdge({
      type: edgeType,
      source: connectionSource.value,
      target: targetNodeId,
      data: edgeData,
      animated: edgeData.animated
    })
    
    if (newEdge) {
      ElMessage.success('连线创建成功')
      // 选中新创建的连线
      graphStore.clearSelection()
      if (newEdge && 'id' in newEdge) {
        graphStore.selectElement(newEdge.id as string)
      }
    } else {
      ElMessage.warning('连线已存在')
    }
    
    cancelConnection()
  }
  
  /**
   * 取消连线创建
   */
  const cancelConnection = () => {
    isConnecting.value = false
    connectionSource.value = null
  }
  
  /**
   * 编辑连线
   */
  const editEdge = (edgeId: string) => {
    const edge = graphStore.edges.find(e => (e as any).id === edgeId) as TaskGraphEdge
    if (!edge) {
      ElMessage.error('连线不存在')
      return
    }

    editingEdge.value = { ...edge }
    showEdgeEditDialog.value = true
  }
  
  /**
   * 保存连线编辑
   */
  const saveEdgeEdit = (updates: Partial<TaskGraphEdge>) => {
    if (!editingEdge.value) return

    const result = graphStore.updateEdge((editingEdge.value as any).id, updates)
    if (result) {
      ElMessage.success('连线更新成功')
      showEdgeEditDialog.value = false
      editingEdge.value = null
    } else {
      ElMessage.error('连线更新失败')
    }
  }

  /**
   * 快速编辑边标签
   */
  const quickEditEdgeLabel = (edgeId: string, newLabel: string) => {
    const edge = graphStore.edges.find(e => (e as any).id === edgeId) as TaskGraphEdge
    if (!edge) {
      ElMessage.error('连线不存在')
      return false
    }

    const result = graphStore.updateEdge(edgeId, {
      data: {
        ...edge.data,
        label: newLabel.trim()
      }
    })

    if (result) {
      ElMessage.success('连线标签更新成功')
      return true
    } else {
      ElMessage.error('连线标签更新失败')
      return false
    }
  }

  /**
   * 更新边类型
   */
  const updateEdgeType = (edgeId: string, newType: EdgeType) => {
    const edge = graphStore.edges.find(e => (e as any).id === edgeId) as TaskGraphEdge
    if (!edge) {
      ElMessage.error('连线不存在')
      return false
    }

    const result = graphStore.updateEdge(edgeId, {
      type: newType,
      animated: newType === EdgeType.SEQUENCE
    })

    if (result) {
      ElMessage.success('连线类型更新成功')
      return true
    } else {
      ElMessage.error('连线类型更新失败')
      return false
    }
  }
  
  /**
   * 删除连线
   */
  const deleteEdge = async (edgeId: string) => {
    const edge = graphStore.edges.find(e => (e as any).id === edgeId) as TaskGraphEdge
    if (!edge) return

    try {
      await ElMessageBox.confirm(
        `确定要删除连线"${edge.data?.label || (edge as any).id}"吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      const success = graphStore.deleteEdge(edgeId)
      if (success) {
        ElMessage.success('连线删除成功')
      } else {
        ElMessage.error('连线删除失败')
      }
    } catch {
      // 用户取消删除
    }
  }

  // ==================== 批量操作方法 ====================

  /**
   * 删除选中的元素
   */
  const deleteSelection = async () => {
    if (!hasSelection.value) return

    const nodeCount = graphStore.selectedNodes.length
    const edgeCount = graphStore.selectedEdges.length

    try {
      await ElMessageBox.confirm(
        `确定要删除选中的 ${nodeCount} 个节点和 ${edgeCount} 个连线吗？`,
        '确认批量删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 删除选中的连线
      for (const edge of graphStore.selectedEdges) {
        graphStore.deleteEdge((edge as any).id)
      }

      // 删除选中的节点（跳过主任务节点）
      for (const node of graphStore.selectedNodes) {
        if (node.type !== 'main-task') {
          graphStore.deleteNode(node.id)
        }
      }

      graphStore.clearSelection()
      ElMessage.success('批量删除成功')
    } catch {
      // 用户取消删除
    }
  }

  /**
   * 批量更新节点样式
   */
  const batchUpdateNodeStyle = (styleUpdates: Partial<TaskNodeData>) => {
    if (graphStore.selectedNodes.length === 0) {
      ElMessage.warning('请先选择要更新的节点')
      return false
    }

    let successCount = 0
    for (const node of graphStore.selectedNodes) {
      const result = graphStore.updateNode(node.id, {
        data: {
          ...node.data,
          ...styleUpdates
        }
      })
      if (result) successCount++
    }

    if (successCount > 0) {
      ElMessage.success(`成功更新 ${successCount} 个节点的样式`)
      return true
    } else {
      ElMessage.error('批量更新失败')
      return false
    }
  }

  /**
   * 批量更新边样式
   */
  const batchUpdateEdgeStyle = (styleUpdates: Partial<EdgeData>) => {
    if (graphStore.selectedEdges.length === 0) {
      ElMessage.warning('请先选择要更新的边')
      return false
    }

    let successCount = 0
    for (const edge of graphStore.selectedEdges) {
      const result = graphStore.updateEdge((edge as any).id, {
        data: {
          ...edge.data,
          ...styleUpdates
        }
      })
      if (result) successCount++
    }

    if (successCount > 0) {
      ElMessage.success(`成功更新 ${successCount} 个边的样式`)
      return true
    } else {
      ElMessage.error('批量更新失败')
      return false
    }
  }

  /**
   * 批量设置节点状态
   */
  const batchSetNodeStatus = (status: TaskStatus) => {
    if (graphStore.selectedNodes.length === 0) {
      ElMessage.warning('请先选择要更新的节点')
      return false
    }

    let successCount = 0
    for (const node of graphStore.selectedNodes) {
      const result = graphStore.updateNode(node.id, {
        data: {
          ...node.data,
          status
        }
      })
      if (result) successCount++
    }

    if (successCount > 0) {
      ElMessage.success(`成功设置 ${successCount} 个节点的状态`)
      return true
    } else {
      ElMessage.error('批量设置状态失败')
      return false
    }
  }
  
  /**
   * 对齐选中的节点
   */
  const alignNodes = (direction: 'left' | 'right' | 'top' | 'bottom' | 'center-h' | 'center-v') => {
    const selectedNodes = graphStore.selectedNodes
    if (selectedNodes.length < 2) {
      ElMessage.warning('请选择至少两个节点')
      return
    }
    
    let referenceValue: number
    
    switch (direction) {
      case 'left':
        referenceValue = Math.min(...selectedNodes.map(n => n.position.x))
        selectedNodes.forEach(node => {
          graphStore.moveNode(node.id, { ...node.position, x: referenceValue })
        })
        break
        
      case 'right':
        referenceValue = Math.max(...selectedNodes.map(n => n.position.x))
        selectedNodes.forEach(node => {
          graphStore.moveNode(node.id, { ...node.position, x: referenceValue })
        })
        break
        
      case 'top':
        referenceValue = Math.min(...selectedNodes.map(n => n.position.y))
        selectedNodes.forEach(node => {
          graphStore.moveNode(node.id, { ...node.position, y: referenceValue })
        })
        break
        
      case 'bottom':
        referenceValue = Math.max(...selectedNodes.map(n => n.position.y))
        selectedNodes.forEach(node => {
          graphStore.moveNode(node.id, { ...node.position, y: referenceValue })
        })
        break
        
      case 'center-h':
        const avgX = selectedNodes.reduce((sum, n) => sum + n.position.x, 0) / selectedNodes.length
        selectedNodes.forEach(node => {
          graphStore.moveNode(node.id, { ...node.position, x: avgX })
        })
        break
        
      case 'center-v':
        const avgY = selectedNodes.reduce((sum, n) => sum + n.position.y, 0) / selectedNodes.length
        selectedNodes.forEach(node => {
          graphStore.moveNode(node.id, { ...node.position, y: avgY })
        })
        break
    }
    
    ElMessage.success('节点对齐成功')
  }

  // ==================== 布局操作方法 ====================

  /**
   * 应用自动布局
   */
  const applyAutoLayout = (layoutType: 'dagre' | 'hierarchical' | 'grid' | 'circular' = 'dagre') => {
    const nodes = graphStore.nodes as TaskGraphNode[]
    const edges = graphStore.edges as TaskGraphEdge[]

    if (nodes.length === 0) {
      ElMessage.warning('没有节点可以布局')
      return
    }

    let layoutedNodes: TaskGraphNode[]

    switch (layoutType) {
      case 'dagre':
        layoutedNodes = graphLayout.applyDagreLayout(nodes, edges)
        break
      case 'hierarchical':
        layoutedNodes = graphLayout.applyHierarchicalLayout(nodes, edges)
        break
      case 'grid':
        layoutedNodes = graphLayout.applyGridLayout(nodes)
        break
      case 'circular':
        layoutedNodes = graphLayout.applyCircularLayout(nodes)
        break
      default:
        layoutedNodes = graphLayout.applyDagreLayout(nodes, edges)
    }

    // 批量更新节点位置
    layoutedNodes.forEach(node => {
      graphStore.updateNode(node.id, { position: node.position })
    })
  }

  /**
   * 撤销布局
   */
  const undoLayout = () => {
    const previousNodes = graphLayout.undoLayout()
    if (previousNodes) {
      previousNodes.forEach(node => {
        graphStore.updateNode(node.id, { position: node.position })
      })
    }
  }

  /**
   * 重做布局
   */
  const redoLayout = () => {
    const nextNodes = graphLayout.redoLayout()
    if (nextNodes) {
      nextNodes.forEach(node => {
        graphStore.updateNode(node.id, { position: node.position })
      })
    }
  }

  /**
   * 更新布局配置
   */
  const updateLayoutConfig = (config: any) => {
    graphLayout.updateLayoutConfig(config)
  }

  /**
   * 重置布局配置
   */
  const resetLayoutConfig = () => {
    graphLayout.resetLayoutConfig()
  }

  /**
   * 从历史记录还原布局
   */
  const restoreLayoutFromHistory = (historyIndex: number) => {
    const success = graphLayout.restoreFromHistory(historyIndex, (nodes) => {
      // 批量更新节点位置
      nodes.forEach(node => {
        graphStore.updateNode(node.id, { position: node.position })
      })
    })

    if (success) {
      ElMessage.success('布局还原成功')
    } else {
      ElMessage.error('布局还原失败')
    }

    return success
  }

  // ==================== 返回接口 ====================

  return {
    // 状态
    editingNode,
    editingEdge,
    showNodeEditDialog,
    showEdgeEditDialog,
    isDragging,
    isConnecting,
    connectionSource,
    
    // 计算属性
    hasSelection,
    selectionCount,
    canDeleteSelection,
    
    // 节点操作
    createNode,
    editNode,
    quickEditNodeLabel,
    saveNodeEdit,
    deleteNode,
    duplicateNode,
    
    // 连线操作
    startConnection,
    completeConnection,
    cancelConnection,
    editEdge,
    quickEditEdgeLabel,
    updateEdgeType,
    saveEdgeEdit,
    deleteEdge,
    
    // 批量操作
    deleteSelection,
    batchUpdateNodeStyle,
    batchUpdateEdgeStyle,
    batchSetNodeStatus,
    alignNodes,

    // 布局操作
    applyAutoLayout,
    undoLayout,
    redoLayout,
    updateLayoutConfig,
    resetLayoutConfig,
    restoreLayoutFromHistory,

    // 布局状态
    graphLayout, // 导出布局实例以便共享历史记录
    isLayouting: graphLayout.isLayouting,
    canUndoLayout: graphLayout.canUndo,
    canRedoLayout: graphLayout.canRedo,
    layoutConfig: graphLayout.layoutConfig
  }
}
