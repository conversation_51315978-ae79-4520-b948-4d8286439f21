<!-- 右侧图例面板组件 -->
<script setup lang="ts" name="LegendPanel">
import { ref, computed } from 'vue'
import { 
  InfoFilled, 
  Plus, 
  QuestionFilled,
  TrendCharts,
  User,
  Clock
} from '@element-plus/icons-vue'
import { useGraphStore } from '../stores/useGraphStore'
import { useGraphOperations } from '../composables/useGraphOperations'
import { 
  LEGEND_ITEMS, 
  EDGE_STYLE_MAP,
  EdgeType,
  type TaskNodeType 
} from '@/define/taskGraph.define'

interface Props {
  /** 是否只读模式 */
  readonly?: boolean
  /** 面板宽度 */
  width?: string
  /** 是否可折叠 */
  collapsible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  width: '300px',
  collapsible: true
})

// ==================== 状态管理 ====================

const graphStore = useGraphStore()
const graphOperations = useGraphOperations()

// ==================== 响应式数据 ====================

/** 面板是否折叠 */
const isCollapsed = ref(false)

/** 当前激活的标签页 */
const activeTab = ref<'legend' | 'stats' | 'help'>('legend')

/** 连线类型图例 */
const edgeTypeLegend = [
  { type: EdgeType.DEPENDENCY, label: '依赖关系', description: '表示任务间的依赖关系' },
  { type: EdgeType.SEQUENCE, label: '顺序关系', description: '表示任务的执行顺序' },
  { type: EdgeType.PARALLEL, label: '并行关系', description: '表示可以并行执行的任务' },
  { type: EdgeType.CONDITIONAL, label: '条件关系', description: '表示有条件的任务关系' },
  { type: EdgeType.CUSTOM, label: '自定义关系', description: '用户自定义的关系类型' }
]

/** 帮助信息 */
const helpSections = [
  {
    title: '基本操作',
    items: [
      '拖拽节点：移动节点位置',
      '双击节点：编辑节点信息',
      '右键节点：显示上下文菜单',
      '拖拽连线：创建节点间连接',
      '框选：按住鼠标左键拖拽选择多个元素'
    ]
  },
  {
    title: '快捷键',
    items: [
      'Ctrl+Z：撤销操作',
      'Ctrl+Shift+Z：重做操作',
      'Ctrl+A：全选所有元素',
      'Delete：删除选中元素',
      'Esc：取消当前操作'
    ]
  },
  {
    title: '节点类型',
    items: [
      '圆明说明：用于添加说明和注释',
      '主任务节点：核心业务流程节点',
      '子任务节点：从属于主任务的子流程',
      '已完成任务：标记为已完成的任务',
      '进行中任务：正在执行的任务',
      '未开始任务：尚未开始的任务'
    ]
  }
]

// ==================== 计算属性 ====================

/** 面板样式 */
const panelStyle = computed(() => ({
  width: isCollapsed.value ? '48px' : props.width,
  transition: 'width 0.3s ease'
}))

/** 图谱统计信息 */
const graphStats = computed(() => {
  const stats = graphStore.graphStats
  const totalNodes = stats.nodeCount
  const totalEdges = stats.edgeCount
  
  return {
    总节点数: totalNodes,
    总连线数: totalEdges,
    已选择: stats.selectedCount,
    ...Object.entries(stats.nodesByType).reduce((acc, [type, count]) => {
      const legendItem = LEGEND_ITEMS.find(item => item.type === type)
      if (legendItem) {
        acc[legendItem.label] = count
      }
      return acc
    }, {} as Record<string, number>)
  }
})

/** 当前图谱信息 */
const currentGraphInfo = computed(() => {
  const graph = graphStore.currentGraph
  if (!graph) return null
  
  return {
    名称: graph.name,
    创建时间: new Date(graph.createdAt).toLocaleString(),
    更新时间: new Date(graph.updatedAt).toLocaleString(),
    版本: graph.metadata.version,
    创建者: graph.metadata.creator || '未知'
  }
})

// ==================== 事件处理方法 ====================

/**
 * 切换面板折叠状态
 */
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

/**
 * 快速创建节点
 */
const quickCreateNode = (nodeType: TaskNodeType) => {
  if (props.readonly) return
  
  // 在画布中心偏右的位置创建节点
  const position = {
    x: 500 + Math.random() * 100 - 50,
    y: 300 + Math.random() * 100 - 50
  }
  
  graphOperations.createNode(nodeType, position)
}

/**
 * 显示节点类型详情
 */
const showNodeTypeDetail = (nodeType: TaskNodeType) => {
  const legendItem = LEGEND_ITEMS.find(item => item.type === nodeType)
  if (legendItem) {
    // 这里可以显示更详细的信息或打开帮助对话框
    console.log('节点类型详情:', legendItem)
  }
}
</script>

<template>
  <div 
    class="legend-panel"
    :style="panelStyle"
    :class="{ 'legend-panel--collapsed': isCollapsed }"
  >
    <!-- 折叠按钮 -->
    <div 
      v-if="collapsible"
      class="collapse-button"
      @click="toggleCollapse"
    >
      <el-icon>
        <component :is="isCollapsed ? 'ArrowLeft' : 'ArrowRight'" />
      </el-icon>
    </div>
    
    <!-- 面板内容 -->
    <div v-if="!isCollapsed" class="panel-content">
      <!-- 标签页 -->
      <!-- 图例说明 -->
      <div class="legend-section">
        <div class="section-title">
          <el-icon><InfoFilled /></el-icon>
          <span>图例说明</span>
        </div>

        <div class="legend-items">
          <!-- 主要节点类型 -->
          <div class="legend-category">
            <div class="category-title">主要节点</div>
            <div class="legend-item">
              <div class="legend-preview">
                <div class="node-preview main-node"></div>
              </div>
              <span class="legend-label">主任务节点</span>
            </div>
            <div class="legend-item">
              <div class="legend-preview">
                <div class="node-preview sub-node"></div>
              </div>
              <span class="legend-label">子任务节点</span>
            </div>
          </div>

          <!-- 辅助节点类型 -->
          <div class="legend-category">
            <div class="category-title">辅助节点</div>
            <div class="legend-item">
              <div class="legend-preview">
                <div class="node-preview helper-node"></div>
              </div>
              <span class="legend-label">辅助节点</span>
            </div>
          </div>

          <!-- 状态说明 -->
          <div class="legend-category">
            <div class="category-title">状态说明</div>
            <div class="legend-item">
              <div class="legend-preview">
                <div class="status-indicator completed"></div>
              </div>
              <span class="legend-label">已完成任务</span>
            </div>
            <div class="legend-item">
              <div class="legend-preview">
                <div class="status-indicator in-progress"></div>
              </div>
              <span class="legend-label">进行中任务</span>
            </div>
            <div class="legend-item">
              <div class="legend-preview">
                <div class="status-indicator pending"></div>
              </div>
              <span class="legend-label">未开始任务</span>
            </div>
            <div class="legend-item">
              <div class="legend-preview">
                <div class="status-indicator blocked"></div>
              </div>
              <span class="legend-label">未开始任务</span>
            </div>
          </div>
        </div>
      </div>

      <el-divider />

      <!-- 数据统计 -->
      <div class="legend-section">
        <div class="section-title">
          <el-icon><TrendCharts /></el-icon>
          <span>数据统计</span>
        </div>

        <div class="stats-container">
          <div class="stats-chart">
            <!-- 这里可以放置一个简单的饼图或统计图表 -->
            <div class="chart-placeholder">
              <div class="chart-segment main-tasks" :style="{ '--percentage': '60%' }"></div>
              <div class="chart-segment sub-tasks" :style="{ '--percentage': '40%' }"></div>
            </div>
            <div class="chart-legend">
              <div class="chart-legend-item">
                <div class="legend-color main-color"></div>
                <span>主任务</span>
              </div>
              <div class="chart-legend-item">
                <div class="legend-color sub-color"></div>
                <span>子任务</span>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
    
    <!-- 折叠状态的快捷信息 -->
    <div v-else class="collapsed-info">
      <el-tooltip content="图例面板" placement="left">
        <el-button 
          class="shortcut-button"
          :icon="InfoFilled"
          @click="toggleCollapse"
        />
      </el-tooltip>
      
      <div class="quick-stats">
        <div class="quick-stat">
          <span class="stat-number">{{ graphStats.总节点数 }}</span>
          <span class="stat-label">节点</span>
        </div>
        <div class="quick-stat">
          <span class="stat-number">{{ graphStats.总连线数 }}</span>
          <span class="stat-label">连线</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.legend-panel {
  position: relative;
  height: 100%;
  background: #ffffff;
  border-left: 1px solid #e8e8e8;
  box-shadow: -2px 0 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  
  &--collapsed {
    .collapse-button {
      right: 8px;
    }
  }
  
  .collapse-button {
    position: absolute;
    top: 12px;
    left: 8px;
    z-index: 10;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background: #e6f7ff;
      border-color: #1890ff;
      color: #1890ff;
    }
  }
  
  .panel-content {
    height: 100%;
    padding: 16px;
    padding-top: 48px;
    overflow-y: auto;
  }
  
  .panel-tabs {
    height: 100%;
    
    :deep(.el-tabs__content) {
      height: calc(100% - 40px);
      overflow-y: auto;
    }
  }
  
  .legend-section,
  .help-section {
    margin-bottom: 20px;
    
    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      color: #262626;
      margin-bottom: 12px;
      font-size: 14px;
    }
  }
  
  .legend-items {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .legend-category {
    .category-title {
      font-size: 13px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 8px;
      padding-bottom: 4px;
      border-bottom: 1px solid #f0f0f0;
    }
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;

    .legend-preview {
      flex-shrink: 0;

      .node-preview {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.8);

        &.main-node {
          background: #1890ff;
          width: 24px;
          height: 24px;
        }

        &.sub-node {
          background: #1890ff;
          width: 20px;
          height: 20px;
        }

        &.helper-node {
          background: #13c2c2;
          width: 16px;
          height: 16px;
        }
      }

      .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid white;

        &.completed {
          background: #52c41a;
        }

        &.in-progress {
          background: #faad14;
        }

        &.pending {
          background: #d9d9d9;
        }

        &.blocked {
          background: #ff4d4f;
        }
      }
    }

    .legend-label {
      font-size: 12px;
      color: #595959;
    }
  }

  // 统计图表样式
  .stats-container {
    .stats-chart {
      text-align: center;

      .chart-placeholder {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: conic-gradient(
          #1890ff 0% 60%,
          #13c2c2 60% 100%
        );
        margin: 0 auto 12px;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 40px;
          height: 40px;
          background: white;
          border-radius: 50%;
        }
      }

      .chart-legend {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .chart-legend-item {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 11px;
          color: #595959;

          .legend-color {
            width: 8px;
            height: 8px;
            border-radius: 50%;

            &.main-color {
              background: #1890ff;
            }

            &.sub-color {
              background: #13c2c2;
            }
          }
        }
      }
    }
  }
  
  .edge-legend {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .edge-legend-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 6px 8px;
    border-radius: 4px;
    
    &:hover {
      background: #fafafa;
    }
  }
  
  .edge-preview {
    flex-shrink: 0;
  }
  
  .edge-legend-info {
    flex: 1;
  }
  
  .edge-legend-label {
    font-size: 12px;
    font-weight: 500;
    color: #262626;
  }
  
  .edge-legend-description {
    font-size: 11px;
    color: #8c8c8c;
    margin-top: 2px;
  }
  
  .info-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    
    .info-label {
      color: #8c8c8c;
      font-weight: 500;
    }
    
    .info-value {
      color: #262626;
      text-align: right;
      max-width: 60%;
      word-break: break-word;
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .stat-item {
    text-align: center;
    padding: 12px 8px;
    background: #f8f9fa;
    border-radius: 6px;
    
    .stat-value {
      font-size: 20px;
      font-weight: bold;
      color: #1890ff;
      line-height: 1;
    }
    
    .stat-label {
      font-size: 11px;
      color: #8c8c8c;
      margin-top: 4px;
    }
  }
  
  .selection-info {
    .selection-summary {
      font-size: 13px;
      color: #1890ff;
      font-weight: 500;
      margin-bottom: 12px;
    }
    
    .selection-details {
      margin-bottom: 12px;
      
      .detail-title {
        font-size: 12px;
        color: #595959;
        font-weight: 500;
        margin-bottom: 6px;
      }
    }
    
    .selection-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 4px 0;
      font-size: 11px;
      color: #262626;
      
      &__indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        flex-shrink: 0;
      }
    }
    
    .more-items {
      font-size: 11px;
      color: #8c8c8c;
      font-style: italic;
      margin-top: 4px;
    }
  }
  
  .help-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .help-item {
    font-size: 12px;
    color: #595959;
    line-height: 1.5;
    margin-bottom: 6px;
    padding-left: 12px;
    position: relative;
    
    &::before {
      content: '•';
      position: absolute;
      left: 0;
      color: #1890ff;
    }
  }
  
  .collapsed-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 16px 8px;
    
    .shortcut-button {
      width: 32px;
      height: 32px;
      padding: 0;
      border-radius: 6px;
    }
    
    .quick-stats {
      display: flex;
      flex-direction: column;
      gap: 8px;
      width: 100%;
    }
    
    .quick-stat {
      text-align: center;
      
      .stat-number {
        display: block;
        font-size: 16px;
        font-weight: bold;
        color: #1890ff;
        line-height: 1;
      }
      
      .stat-label {
        font-size: 10px;
        color: #8c8c8c;
        margin-top: 2px;
      }
    }
  }
}

// 滚动条样式
.panel-content::-webkit-scrollbar {
  width: 4px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 2px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}
</style>
