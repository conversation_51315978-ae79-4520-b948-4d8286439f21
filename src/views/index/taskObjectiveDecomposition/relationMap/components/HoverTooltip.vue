<!-- 悬停提示组件 -->
<script setup lang="ts" name="HoverTooltip">
import { ref, computed, watch } from 'vue'
import type { TaskGraphNode, TaskGraphEdge } from '@/define/taskGraph.define'

interface Props {
  /** 是否显示提示 */
  visible?: boolean
  /** 提示位置 */
  position?: { x: number; y: number }
  /** 节点数据 */
  node?: TaskGraphNode | null
  /** 边数据 */
  edge?: TaskGraphEdge | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  position: () => ({ x: 0, y: 0 }),
  node: null,
  edge: null
})

// ==================== 计算属性 ====================

/** 提示内容 */
const tooltipContent = computed(() => {
  if (props.node) {
    return {
      title: props.node.data.label,
      items: [
        { label: '状态', value: getStatusText(props.node.data.status) },
        { label: '优先级', value: getPriorityText(props.node.data.priority) },
        { label: '负责人', value: props.node.data.assignee || '未分配' },
        { label: '进度', value: `${props.node.data.progress || 0}%` }
      ].filter(item => item.value && item.value !== '未分配')
    }
  } else if (props.edge) {
    return {
      title: props.edge.data?.label || '连线',
      items: [
        { label: '类型', value: getEdgeTypeText(props.edge.type) },
        { label: '描述', value: props.edge.data?.description || '' }
      ].filter(item => item.value)
    }
  }
  return null
})

/** 提示框样式 */
const tooltipStyle = computed(() => ({
  left: `${props.position.x + 10}px`,
  top: `${props.position.y - 10}px`,
  transform: 'translateY(-100%)'
}))

// ==================== 辅助方法 ====================

/**
 * 获取状态文本
 */
function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    'not-started': '未开始',
    'in-progress': '进行中',
    'completed': '已完成',
    'blocked': '已阻塞',
    'cancelled': '已取消'
  }
  return statusMap[status] || status
}

/**
 * 获取优先级文本
 */
function getPriorityText(priority?: string) {
  const priorityMap: Record<string, string> = {
    'low': '低',
    'medium': '中',
    'high': '高',
    'urgent': '紧急'
  }
  return priority ? priorityMap[priority] || priority : ''
}

/**
 * 获取边类型文本
 */
function getEdgeTypeText(type: string) {
  const typeMap: Record<string, string> = {
    'dependency': '依赖关系',
    'sequence': '顺序关系',
    'parallel': '并行关系',
    'conditional': '条件关系',
    'custom': '自定义关系'
  }
  return typeMap[type] || type
}
</script>

<template>
  <Teleport to="body">
    <Transition name="tooltip-fade">
      <div
        v-if="visible && tooltipContent"
        class="hover-tooltip"
        :style="tooltipStyle"
      >
        <div class="tooltip-header">
          <span class="tooltip-title">{{ tooltipContent.title }}</span>
        </div>
        
        <div class="tooltip-content">
          <div
            v-for="item in tooltipContent.items"
            :key="item.label"
            class="tooltip-item"
          >
            <span class="item-label">{{ item.label }}:</span>
            <span class="item-value">{{ item.value }}</span>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<style lang="scss" scoped>
.hover-tooltip {
  position: fixed;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  line-height: 1.4;
  max-width: 200px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  pointer-events: none;
  
  .tooltip-header {
    margin-bottom: 6px;
    
    .tooltip-title {
      font-weight: 500;
      font-size: 13px;
      color: #fff;
    }
  }
  
  .tooltip-content {
    .tooltip-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 2px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .item-label {
        color: #ccc;
        margin-right: 8px;
        flex-shrink: 0;
      }
      
      .item-value {
        color: #fff;
        text-align: right;
        word-break: break-word;
      }
    }
  }
}

// 过渡动画
.tooltip-fade-enter-active,
.tooltip-fade-leave-active {
  transition: opacity 0.2s ease;
}

.tooltip-fade-enter-from,
.tooltip-fade-leave-to {
  opacity: 0;
}
</style>
