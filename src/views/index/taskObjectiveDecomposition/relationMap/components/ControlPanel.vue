<!-- 左侧控制面板组件 -->
<script setup lang="ts" name="ControlPanel">
import { ref, computed, watch } from 'vue'
import {
  Plus,
  Setting,
  Connection,
  Grid,
  View,
  User,
  Share,
  Document,
  Edit,
  Delete,
  Clock,
  Check,
  Download,
  ArrowDown,
  Back,
  Right
} from '@element-plus/icons-vue'
import { useGraphStore } from '../stores/useGraphStore'
import { useGraphOperations } from '../composables/useGraphOperations'
import PropertyPanel from './PropertyPanel.vue'
import LayoutHistoryDialog from './LayoutHistoryDialog.vue'
import StyleCustomizerDialog from './StyleCustomizerDialog.vue'
import {
  TaskNodeType,
  EdgeType,
  LEGEND_ITEMS
} from '@/define/taskGraph.define'

interface Props {
  /** 是否只读模式 */
  readonly?: boolean
  /** 面板宽度 */
  width?: string
  /** 是否可折叠 */
  collapsible?: boolean
  /** 是否显示悬停信息 */
  showHoverInfo?: boolean
  /** 选择时是否高亮 */
  highlightOnSelect?: boolean
  /** 是否显示节点标签 */
  showNodeLabels?: boolean
  /** 是否显示边标签 */
  showEdgeLabels?: boolean
  /** 布局模式 */
  layoutMode?: string
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  width: '280px',
  collapsible: true,
  showHoverInfo: true,
  highlightOnSelect: true,
  showNodeLabels: true,
  showEdgeLabels: true,
  layoutMode: 'manual'
})

const emit = defineEmits<{
  'update:showHoverInfo': [value: boolean]
  'update:highlightOnSelect': [value: boolean]
  'update:showNodeLabels': [value: boolean]
  'update:showEdgeLabels': [value: boolean]
  'update:layoutMode': [value: string]
}>()

// ==================== 状态管理 ====================

const graphStore = useGraphStore()
const graphOperations = useGraphOperations()
// 使用 graphOperations 中的 graphLayout 实例以确保历史记录共享
const graphLayout = graphOperations.graphLayout

// ==================== 响应式数据 ====================

/** 面板是否折叠 */
const isCollapsed = ref(false)

/** 当前激活的报表类型 */
const activeReportType = ref<'business' | 'temporary'>('business')

/** 选择时高亮设置 */
const highlightOnSelect = computed({
  get: () => props.highlightOnSelect ?? true,
  set: (value) => emit('update:highlightOnSelect', value)
})

/** 显示节点标签 */
const showNodeLabels = computed({
  get: () => props.showNodeLabels ?? true,
  set: (value) => emit('update:showNodeLabels', value)
})

/** 显示边标签 */
const showEdgeLabels = computed({
  get: () => props.showEdgeLabels ?? true,
  set: (value) => emit('update:showEdgeLabels', value)
})

/** 布局模式 */
const layoutMode = computed({
  get: () => props.layoutMode ?? 'manual',
  set: (value: string) => emit('update:layoutMode', value)
})

/** 是否显示属性面板 */
const showPropertyPanel = ref(false)

/** 是否显示批量编辑对话框 */
const showBatchEditDialog = ref(false)

/** 批量编辑类型 */
const batchEditType = ref<'nodes' | 'edges'>('nodes')

/** 是否显示布局历史对话框 */
const showLayoutHistoryDialog = ref(false)

/** 是否显示样式自定义对话框 */
const showStyleCustomizerDialog = ref(false)

/** 样式模板 */
const styleTemplates = ref([
  {
    name: '商务风格',
    description: '适合商务场景的专业配色',
    nodeStyle: {
      backgroundColor: '#1890ff',
      borderColor: '#096dd9',
      textColor: '#ffffff'
    }
  },
  {
    name: '清新风格',
    description: '清新自然的绿色主题',
    nodeStyle: {
      backgroundColor: '#52c41a',
      borderColor: '#389e0d',
      textColor: '#ffffff'
    }
  },
  {
    name: '警示风格',
    description: '用于重要或紧急任务',
    nodeStyle: {
      backgroundColor: '#ff4d4f',
      borderColor: '#cf1322',
      textColor: '#ffffff'
    }
  },
  {
    name: '优雅风格',
    description: '紫色主题，优雅大方',
    nodeStyle: {
      backgroundColor: '#722ed1',
      borderColor: '#531dab',
      textColor: '#ffffff'
    }
  }
])



// ==================== 计算属性 ====================

/** 面板样式 */
const panelStyle = computed(() => ({
  width: isCollapsed.value ? '48px' : props.width,
  transition: 'width 0.3s ease'
}))

/** 节点工具列表 */
const nodeTools = computed(() =>
  LEGEND_ITEMS.map(item => ({
    ...item,
    disabled: props.readonly
  }))
)

/** 是否有选中的节点 */
const hasSelectedNodes = computed(() => graphStore.selectedNodes.length > 0)

/** 是否有选中的元素（节点或边） */
const hasSelectedElements = computed(() => graphStore.selectedElements.length > 0)

/** 当前选中的节点 */
const selectedNode = computed(() => {
  return graphStore.selectedNodes.length > 0 ? graphStore.selectedNodes[0] : null
})

/** 样式预设 */
const stylePresets = ref([
  { name: '蓝色', value: '#1890ff' },
  { name: '绿色', value: '#52c41a' },
  { name: '橙色', value: '#fa8c16' },
  { name: '红色', value: '#ff4d4f' },
  { name: '紫色', value: '#722ed1' },
  { name: '青色', value: '#13c2c2' },
  { name: '粉色', value: '#eb2f96' },
  { name: '黄色', value: '#faad14' },
  { name: '深蓝', value: '#2f54eb' },
  { name: '深绿', value: '#389e0d' },
  { name: '深红', value: '#cf1322' },
  { name: '灰色', value: '#8c8c8c' }
])

// ==================== 事件处理方法 ====================

/**
 * 切换面板折叠状态
 */
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

/**
 * 创建节点
 */
const createNode = (nodeType: TaskNodeType) => {
  if (props.readonly) return
  
  // 在画布中心创建节点
  const centerPosition = {
    x: 400 + Math.random() * 100 - 50, // 添加随机偏移避免重叠
    y: 300 + Math.random() * 100 - 50
  }
  
  graphOperations.createNode(nodeType, centerPosition)
}

/**
 * 开始连线模式
 */
const startConnectionMode = (edgeType: EdgeType) => {
  if (props.readonly) return
  
  // 这里需要设置连线类型，然后让用户点击节点创建连线
  console.log('开始连线模式:', edgeType)
  // 可以通过store或emit通知父组件进入连线模式
}

/**
 * 处理新增操作
 */
const handleNewAction = () => {
  if (props.readonly) return

  // 在画布中心创建新节点
  const centerPosition = {
    x: 400 + Math.random() * 100 - 50,
    y: 300 + Math.random() * 100 - 50
  }

  graphOperations.createNode(TaskNodeType.SUB_TASK, centerPosition)
}

/**
 * 打开属性配置对话框
 */
const openPropertyDialog = () => {
  if (hasSelectedNodes.value) {
    showPropertyPanel.value = true
  } else {
    console.log('请先选择一个节点')
  }
}

/**
 * 关闭属性面板
 */
const closePropertyPanel = () => {
  showPropertyPanel.value = false
}

/**
 * 添加任务关系
 */
const addTaskRelation = () => {
  if (props.readonly) return

  console.log('添加任务关系')
  // 这里可以实现添加任务关系功能
}

/**
 * 处理协作实践按钮点击
 */
const handleCollaborationPractice = () => {
  console.log('打开协作实践弹窗')
  // 这里预留协作实践弹窗接口
  // 后续可以实现：
  // - 显示协作实践对话框
  // - 展示团队协作工具
  // - 实时协作功能等
}

/**
 * 处理协作关系分析按钮点击
 */
const handleCollaborationAnalysis = () => {
  console.log('打开协作关系分析弹窗')
  // 这里预留协作关系分析弹窗接口
  // 后续可以实现：
  // - 显示关系分析图表
  // - 团队协作统计
  // - 协作效率分析等
}

/**
 * 处理编辑操作
 */
const handleEditAction = () => {
  if (props.readonly || !hasSelectedElements.value) return

  const selectedNodes = graphStore.selectedNodes
  const selectedEdges = graphStore.selectedEdges

  if (selectedNodes.length === 1) {
    // 编辑单个节点
    graphOperations.editNode(selectedNodes[0].id)
  } else if (selectedEdges.length === 1) {
    // 编辑单个边
    const edge = selectedEdges[0] as any
    graphOperations.editEdge(edge.id)
  } else if (selectedNodes.length > 1) {
    // 批量编辑节点
    handleBatchEditNodes()
  } else if (selectedEdges.length > 1) {
    // 批量编辑边
    handleBatchEditEdges()
  }
}

/**
 * 批量编辑节点
 */
const handleBatchEditNodes = () => {
  showBatchEditDialog.value = true
  batchEditType.value = 'nodes'
}

/**
 * 批量编辑边
 */
const handleBatchEditEdges = () => {
  showBatchEditDialog.value = true
  batchEditType.value = 'edges'
}

/**
 * 批量设置节点状态
 */
const batchSetStatus = (status: string) => {
  if (graphStore.selectedNodes.length === 0) return

  graphOperations.batchSetNodeStatus(status as any)
}

/**
 * 批量应用样式
 */
const batchApplyStyle = (styleUpdates: any) => {
  if (batchEditType.value === 'nodes' && graphStore.selectedNodes.length > 0) {
    graphOperations.batchUpdateNodeStyle(styleUpdates)
  } else if (batchEditType.value === 'edges' && graphStore.selectedEdges.length > 0) {
    graphOperations.batchUpdateEdgeStyle(styleUpdates)
  }
  showBatchEditDialog.value = false
}

/**
 * 处理删除操作
 */
const handleDeleteAction = () => {
  if (props.readonly || !hasSelectedElements.value) return

  // 使用批量删除功能
  graphOperations.deleteSelection()
}

/**
 * 获取布局模式文本
 */
const getLayoutModeText = (mode: string) => {
  const modeMap: Record<string, string> = {
    'locked': '锁定布局',
    'auto': '自动布局',
    'manual': '手动布局'
  }
  return modeMap[mode] || mode
}

/**
 * 监听布局模式变化
 */
watch(layoutMode, (newMode) => {
  console.log('布局模式切换到:', newMode)
  // 根据布局模式执行相应的操作
  handleLayoutModeChange(newMode as 'locked' | 'auto' | 'manual')
})

/**
 * 监听高亮设置变化 - 修复版本
 */
watch(highlightOnSelect, (enabled) => {
  // 如果禁用高亮，立即清除当前高亮效果
  if (!enabled) {
    // 清除所有选中状态的视觉高亮
    graphStore.clearSelection()
    console.log('清除高亮效果')
  }
})

/**
 * 监听节点标签显示设置变化 - 修复版本
 */
watch(showNodeLabels, (enabled) => {
  emit('update:showNodeLabels', enabled)
  console.log('节点标签显示:', enabled)
})

/**
 * 监听边标签显示设置变化 - 修复版本
 */
watch(showEdgeLabels, (enabled) => {
  emit('update:showEdgeLabels', enabled)
  console.log('边标签显示:', enabled)
})

/**
 * 确认布局
 */
const confirmLayout = () => {
  if (props.readonly) return

  try {
    // 确认当前布局，将当前节点位置保存为最终布局
    const currentNodes = graphStore.nodes
    if (currentNodes.length === 0) {
      ElMessage.warning('没有节点可以确认')
      return
    }

    // 保存当前布局状态
    const layoutData = {
      nodes: currentNodes.map(node => ({
        id: node.id,
        position: node.position
      })),
      timestamp: Date.now(),
      mode: layoutMode.value
    }

    // 保存到本地存储
    const graphId = graphStore.currentGraph?.id || 'unknown'
    const layoutKey = `layout_confirmed_${graphId}`
    localStorage.setItem(layoutKey, JSON.stringify(layoutData))

    ElMessage.success('布局已确认并保存')
    console.log('确认布局:', layoutData)
  } catch (error) {
    console.error('确认布局失败:', error)
    ElMessage.error('确认布局失败')
  }
}

/**
 * 保存布局
 */
const saveLayout = () => {
  if (props.readonly) return

  try {
    const success = graphStore.saveGraph()
    if (success) {
      ElMessage.success('布局保存成功')
      console.log('布局保存成功')
    } else {
      ElMessage.error('布局保存失败')
      console.log('布局保存失败')
    }
  } catch (error) {
    console.error('保存布局时出错:', error)
    ElMessage.error('保存布局时出错')
  }
}

/**
 * 显示布局历史
 */
const showLayoutHistory = () => {
  try {
    // 获取布局历史 - 从 graphLayout 获取
    const history = graphLayout.layoutHistory.value || []

    if (!Array.isArray(history) || history.length === 0) {
      ElMessage.info('暂无布局历史记录')
      return
    }

    // 打开历史记录对话框
    showLayoutHistoryDialog.value = true
  } catch (error) {
    console.error('获取布局历史失败:', error)
    ElMessage.error('获取布局历史失败')
  }
}

/**
 * 处理历史记录还原
 */
const handleHistoryRestore = (index: number) => {
  try {
    const success = graphOperations.restoreLayoutFromHistory(index)
    if (success) {
      showLayoutHistoryDialog.value = false
    }
  } catch (error) {
    console.error('还原布局失败:', error)
  }
}

/**
 * 处理清空历史记录
 */
const handleHistoryClear = () => {
  try {
    graphLayout.clearHistory()
    showLayoutHistoryDialog.value = false
  } catch (error) {
    console.error('清空历史记录失败:', error)
  }
}

/**
 * 创建关系
 */
const createRelation = () => {
  if (props.readonly || graphStore.selectedNodes.length < 2) return

  const sourceNode = graphStore.selectedNodes[0]
  const targetNode = graphStore.selectedNodes[1]

  // 使用默认的依赖关系类型
  graphOperations.completeConnection(targetNode.id, EdgeType.DEPENDENCY)
}

/**
 * 编辑关系
 */
const editRelation = () => {
  if (props.readonly || graphStore.selectedEdges.length === 0) return

  const selectedEdge = graphStore.selectedEdges[0] as any
  graphOperations.editEdge(selectedEdge.id)
}

/**
 * 删除关系
 */
const deleteRelation = () => {
  if (props.readonly || graphStore.selectedEdges.length === 0) return

  const selectedEdge = graphStore.selectedEdges[0] as any
  graphOperations.deleteEdge(selectedEdge.id)
}

/**
 * 应用颜色预设
 */
const applyColorPreset = (color: string) => {
  if (!hasSelectedNodes.value) {
    ElMessage.warning('请先选择要应用样式的节点')
    return
  }

  // 为选中的节点应用颜色
  graphStore.selectedNodes.forEach(node => {
    graphStore.updateNode(node.id, {
      data: {
        ...node.data,
        color: color,
        metadata: {
          ...node.data.metadata,
          // 存储样式信息到 metadata 中
          customStyle: {
            backgroundColor: color,
            borderColor: darkenColor(color, 20),
            textColor: getContrastColor(color)
          }
        }
      }
    })
  })

  ElMessage.success(`已为 ${graphStore.selectedNodes.length} 个节点应用颜色`)
}

/**
 * 加深颜色
 */
const darkenColor = (color: string, percent: number) => {
  // 简单的颜色加深算法
  const num = parseInt(color.replace("#", ""), 16)
  const amt = Math.round(2.55 * percent)
  const R = (num >> 16) - amt
  const G = (num >> 8 & 0x00FF) - amt
  const B = (num & 0x0000FF) - amt
  return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1)
}

/**
 * 获取对比色（黑色或白色）
 */
const getContrastColor = (hexColor: string) => {
  // 移除 # 号
  const color = hexColor.replace('#', '')

  // 转换为 RGB
  const r = parseInt(color.substring(0, 2), 16)
  const g = parseInt(color.substring(2, 4), 16)
  const b = parseInt(color.substring(4, 6), 16)

  // 计算亮度
  const brightness = (r * 299 + g * 587 + b * 114) / 1000

  // 根据亮度返回黑色或白色
  return brightness > 128 ? '#000000' : '#ffffff'
}

/**
 * 打开样式自定义器
 */
const openStyleCustomizer = () => {
  showStyleCustomizerDialog.value = true
}

/**
 * 应用节点样式
 */
const handleApplyNodeStyle = (styleConfig: any) => {
  if (!hasSelectedNodes.value) {
    ElMessage.warning('请先选择要应用样式的节点')
    return
  }

  // 为选中的节点应用样式
  graphStore.selectedNodes.forEach(node => {
    const updates: any = {
      data: {
        ...node.data,
        // 更新基础颜色属性
        color: styleConfig.backgroundColor || node.data.color,
        // 更新尺寸属性
        size: styleConfig.width && styleConfig.height ? {
          width: styleConfig.width,
          height: styleConfig.height
        } : node.data.size,
        // 将样式信息存储到 metadata 中
        metadata: {
          ...node.data.metadata,
          customStyle: {
            backgroundColor: styleConfig.backgroundColor,
            borderColor: styleConfig.borderColor,
            textColor: styleConfig.textColor,
            borderRadius: styleConfig.borderRadius !== undefined ? `${styleConfig.borderRadius}%` : undefined,
            fontSize: styleConfig.fontSize ? `${styleConfig.fontSize}px` : undefined,
            fontWeight: styleConfig.fontWeight,
            width: styleConfig.width,
            height: styleConfig.height
          }
        }
      }
    }

    graphStore.updateNode(node.id, updates)
  })

  ElMessage.success('节点样式应用成功')
}

/**
 * 应用连线样式
 */
const handleApplyEdgeStyle = (styleConfig: any) => {
  console.log('🔗 应用连线样式:', {
    selectedEdges: graphStore.selectedEdges,
    selectedEdgesLength: graphStore.selectedEdges?.length,
    selectedElements: graphStore.selectedElements,
    styleConfig
  })

  if (!graphStore.selectedEdges || graphStore.selectedEdges.length === 0) {
    ElMessage.warning('请先选择要应用样式的连线')
    return
  }

  // 为选中的连线应用样式
  graphStore.selectedEdges.forEach((edge: any) => {
    const updates: any = {
      data: {
        ...edge.data
      }
    }

    // 应用连线样式
    if (styleConfig.stroke) {
      updates.data.stroke = styleConfig.stroke
    }
    if (styleConfig.strokeWidth) {
      updates.data.strokeWidth = styleConfig.strokeWidth
    }
    if (styleConfig.strokeDasharray !== undefined) {
      // 将 'none' 转换为空字符串，表示实线
      updates.data.strokeDasharray = styleConfig.strokeDasharray === 'none' ? '' : styleConfig.strokeDasharray
    }
    if (styleConfig.markerEnd) {
      updates.data.markerEnd = styleConfig.markerEnd
    }

    graphStore.updateEdge(edge.id, updates)
  })

  ElMessage.success('连线样式应用成功')
}

/**
 * 应用样式模板
 */
const applyStyleTemplate = (template: any) => {
  if (!hasSelectedNodes.value) {
    ElMessage.warning('请先选择要应用样式的节点')
    return
  }

  // 为选中的节点应用模板样式
  graphStore.selectedNodes.forEach(node => {
    graphStore.updateNode(node.id, {
      data: {
        ...node.data,
        color: template.nodeStyle.backgroundColor,
        metadata: {
          ...node.data.metadata,
          customStyle: {
            backgroundColor: template.nodeStyle.backgroundColor,
            borderColor: template.nodeStyle.borderColor,
            textColor: template.nodeStyle.textColor
          }
        }
      }
    })
  })

  ElMessage.success(`已应用"${template.name}"样式模板`)
}

/**
 * 处理自动布局
 */
const handleAutoLayout = (layoutType: string) => {
  if (props.readonly) return

  graphOperations.applyAutoLayout(layoutType as any)
}

/**
 * 撤销布局
 */
const handleUndoLayout = () => {
  if (props.readonly) return

  graphOperations.undoLayout()
}

/**
 * 重做布局
 */
const handleRedoLayout = () => {
  if (props.readonly) return

  graphOperations.redoLayout()
}

/**
 * 处理布局模式变化
 */
const handleLayoutModeChange = (mode: 'locked' | 'auto' | 'manual') => {
  switch (mode) {
    case 'locked':
      // 锁定布局：禁用节点拖拽和连线创建
      console.log('切换到锁定布局模式')
      // 这里可以通过store或emit通知GraphCanvas组件锁定交互
      break

    case 'auto':
      // 自动布局：启用自动布局算法
      console.log('切换到自动布局模式')
      if (graphStore.nodes.length > 0) {
        // 自动应用智能布局
        graphOperations.applyAutoLayout('dagre')
      }
      break

    case 'manual':
      // 手动布局：启用所有交互功能
      console.log('切换到手动布局模式')
      // 恢复所有交互功能
      break

    default:
      console.warn('未知的布局模式:', mode)
  }
}
</script>

<template>
  <div 
    class="control-panel"
    :style="panelStyle"
    :class="{ 'control-panel--collapsed': isCollapsed }"
  >
    <!-- 折叠按钮 -->
    <div 
      v-if="collapsible"
      class="collapse-button"
      @click="toggleCollapse"
    >
      <el-icon>
        <component :is="isCollapsed ? 'ArrowRight' : 'ArrowLeft'" />
      </el-icon>
    </div>
    
    <!-- 面板内容 -->
    <div v-if="!isCollapsed" class="panel-content">
      <!-- 报表类型选择 -->
      <div class="panel-section">
        <div class="section-title">
          <el-icon><Document /></el-icon>
          <span>报表类型</span>
        </div>

        <div class="report-type-selector">
          <el-button
            :type="activeReportType === 'business' ? 'primary' : 'default'"
            size="small"
            @click="activeReportType = 'business'"
            class="report-type-btn"
          >
            业务报表
          </el-button>
          <el-button
            :type="activeReportType === 'temporary' ? 'primary' : 'default'"
            size="small"
            @click="activeReportType = 'temporary'"
            class="report-type-btn"
          >
            临时报表
          </el-button>
        </div>
      </div>

      <el-divider />

      <!-- 协作功能 -->
      <div class="panel-section">
        <div class="section-title">
          <el-icon><Share /></el-icon>
          <span>协作</span>
        </div>

        <div class="collaboration-tools">
          <el-button
            type="primary"
            size="small"
            @click="handleCollaborationPractice"
            class="collaboration-btn"
          >
            <el-icon><User /></el-icon>
            <span>协作实践</span>
          </el-button>

          <el-button
            type="primary"
            size="small"
            @click="handleCollaborationAnalysis"
            class="collaboration-btn"
          >
            <el-icon><Connection /></el-icon>
            <span>协作关系分析</span>
          </el-button>
        </div>
      </div>

      <el-divider />

      <!-- 交互功能 -->
      <div class="panel-section">
        <div class="section-title">
          <el-icon><Plus /></el-icon>
          <span>交互</span>
        </div>

        <div class="interaction-tools">
          <el-button
            type="primary"
            size="small"
            :disabled="readonly"
            @click="handleNewAction"
            class="interaction-btn"
          >
            <el-icon><Plus /></el-icon>
            <span>新增</span>
          </el-button>

          <el-button
            type="warning"
            size="small"
            :disabled="readonly || !hasSelectedNodes"
            @click="handleEditAction"
            class="interaction-btn"
          >
            <el-icon><Edit /></el-icon>
            <span>编辑</span>
          </el-button>

          <el-button
            type="danger"
            size="small"
            :disabled="readonly || !hasSelectedElements"
            @click="handleDeleteAction"
            class="interaction-btn"
          >
            <el-icon><Delete /></el-icon>
            <span>删除</span>
          </el-button>
        </div>
      </div>

      <el-divider />

      <!-- 显示控制 -->
      <div class="panel-section">
        <div class="section-title">
          <el-icon><View /></el-icon>
          <span>显示控制</span>
        </div>

        <div class="display-settings">
          <div class="setting-item">
            <span class="setting-label">选择时高亮</span>
            <el-switch
              v-model="highlightOnSelect"
              size="small"
            />
          </div>

          <div class="setting-item">
            <span class="setting-label">悬停显示简要信息</span>
            <el-switch
              :model-value="showHoverInfo"
              @update:model-value="(value: any) => emit('update:showHoverInfo', Boolean(value))"
              size="small"
            />
          </div>

          <div class="setting-item">
            <span class="setting-label">显示节点标签</span>
            <el-switch
              v-model="showNodeLabels"
              size="small"
            />
          </div>

          <div class="setting-item">
            <span class="setting-label">显示边标签</span>
            <el-switch
              v-model="showEdgeLabels"
              size="small"
            />
          </div>
        </div>
      </div>

      <el-divider />

      <!-- 布局控制 -->
      <div class="panel-section">
        <div class="section-title">
          <el-icon><Grid /></el-icon>
          <span>布局</span>
        </div>

        <div class="layout-controls">
          <div class="layout-mode-selector">
            <el-radio-group v-model="layoutMode" size="small">
              <el-radio-button label="locked">锁定布局</el-radio-button>
              <el-radio-button label="auto">自动布局</el-radio-button>
              <el-radio-button label="manual">手动布局</el-radio-button>
            </el-radio-group>
          </div>

          <div class="layout-status">
            <span class="status-label">当前模式:</span>
            <span class="status-value" :class="`mode-${layoutMode}`">
              {{ getLayoutModeText(layoutMode) }}
            </span>
          </div>

          <!-- 自动布局按钮组 -->
          <div v-if="layoutMode !== 'locked'" class="auto-layout-buttons">
            <el-dropdown
              trigger="click"
              @command="handleAutoLayout"
              :disabled="readonly"
            >
              <el-button
                type="primary"
                size="small"
                :loading="graphOperations.isLayouting?.value"
                class="auto-layout-btn"
              >
                <el-icon><Grid /></el-icon>
                <span>自动布局</span>
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="dagre">智能布局</el-dropdown-item>
                  <el-dropdown-item command="hierarchical">层次布局</el-dropdown-item>
                  <el-dropdown-item command="grid">网格布局</el-dropdown-item>
                  <el-dropdown-item command="circular">圆形布局</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <div class="layout-history-buttons">
              <el-button
                size="small"
                :disabled="!graphOperations.canUndoLayout"
                @click="handleUndoLayout"
                title="撤销布局"
              >
                <el-icon><Back /></el-icon>
              </el-button>

              <el-button
                size="small"
                :disabled="!graphOperations.canRedoLayout"
                @click="handleRedoLayout"
                title="重做布局"
              >
                <el-icon><Right /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <el-divider />

      <!-- 属性设置 -->
      <div class="panel-section">
        <div class="section-title">
          <el-icon><Setting /></el-icon>
          <span>属性设置</span>
        </div>

        <div class="property-settings">
          <el-button
            size="small"
            :disabled="!hasSelectedNodes"
            @click="openPropertyDialog"
            class="property-btn"
          >
            配置属性
          </el-button>
        </div>

        <!-- 属性设置面板 -->
        <PropertyPanel
          :visible="showPropertyPanel"
          :selected-node="selectedNode"
          @close="closePropertyPanel"
        />
      </div>

      <el-divider />

      <!-- 布局操作 -->
      <div class="panel-section">
        <div class="section-title">
          <el-icon><Setting /></el-icon>
          <span>布局操作</span>
        </div>

        <div class="layout-actions">
          <el-button
            type="success"
            size="small"
            @click="confirmLayout"
            class="layout-action-btn"
          >
            <el-icon><Check /></el-icon>
            <span>确认布局</span>
          </el-button>

          <el-button
            type="primary"
            size="small"
            @click="saveLayout"
            class="layout-action-btn"
          >
            <el-icon><Download /></el-icon>
            <span>保存布局</span>
          </el-button>

          <el-button
            type="default"
            size="small"
            @click="showLayoutHistory"
            class="layout-action-btn"
          >
            <el-icon><Clock /></el-icon>
            <span>历史</span>
          </el-button>
        </div>
      </div>

      <el-divider />

      <!-- 样式设置 -->
      <div class="panel-section">
        <div class="section-title">
          <el-icon><Setting /></el-icon>
          <span>样式设置</span>
        </div>

        <div class="style-settings">
          <!-- 颜色预设 -->
          <div class="style-presets">
            <h6 class="preset-title">颜色预设</h6>
            <div class="preset-colors">
              <div
                v-for="color in stylePresets"
                :key="color.name"
                class="color-preset"
                :style="{ backgroundColor: color.value }"
                :title="color.name"
                @click="applyColorPreset(color.value)"
              />
            </div>
          </div>

          <!-- 样式模板 -->
          <div class="style-templates">
            <h6 class="template-title">样式模板</h6>
            <div class="template-list">
              <div
                v-for="template in styleTemplates"
                :key="template.name"
                class="template-item"
                :title="template.description"
                @click="applyStyleTemplate(template)"
              >
                <div
                  class="template-preview"
                  :style="{ backgroundColor: template.nodeStyle.backgroundColor }"
                />
                <span class="template-name">{{ template.name }}</span>
              </div>
            </div>
          </div>

          <!-- 自定义样式 -->
          <div class="custom-style">
            <h6 class="custom-title">自定义样式</h6>
            <el-button
              size="small"
              @click="openStyleCustomizer"
              class="style-btn"
            >
              <el-icon><Edit /></el-icon>
              <span>自定义样式</span>
            </el-button>
          </div>
        </div>
      </div>

      <el-divider />

      <!-- 关系操作 -->
      <div class="panel-section">
        <div class="section-title">
          <el-icon><Connection /></el-icon>
          <span>关系操作</span>
        </div>

        <div class="relation-operations">
          <el-button
            type="success"
            size="small"
            :disabled="readonly || !hasSelectedNodes || graphStore.selectedNodes.length < 2"
            @click="createRelation"
            class="relation-btn"
          >
            <el-icon><Connection /></el-icon>
            <span>创建关系</span>
          </el-button>

          <el-button
            type="warning"
            size="small"
            :disabled="readonly || graphStore.selectedEdges.length === 0"
            @click="editRelation"
            class="relation-btn"
          >
            <el-icon><Edit /></el-icon>
            <span>编辑关系</span>
          </el-button>

          <el-button
            type="danger"
            size="small"
            :disabled="readonly || graphStore.selectedEdges.length === 0"
            @click="deleteRelation"
            class="relation-btn"
          >
            <el-icon><Delete /></el-icon>
            <span>删除关系</span>
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 折叠状态的快捷按钮 -->
    <div v-else class="collapsed-shortcuts">
      <el-tooltip content="添加节点" placement="right">
        <el-button 
          class="shortcut-button"
          :icon="Plus"
          @click="toggleCollapse"
        />
      </el-tooltip>
      
      <el-tooltip content="画布设置" placement="right">
        <el-button 
          class="shortcut-button"
          :icon="Setting"
          @click="toggleCollapse"
        />
      </el-tooltip>
      
      <el-tooltip content="协作设置" placement="right">
        <el-button 
          class="shortcut-button"
          :icon="Share"
          @click="toggleCollapse"
        />
      </el-tooltip>
    </div>
  </div>

  <!-- 布局历史对话框 -->
  <LayoutHistoryDialog
    v-model="showLayoutHistoryDialog"
    :history-data="graphLayout.layoutHistory.value"
    @restore="handleHistoryRestore"
    @clear="handleHistoryClear"
  />

  <!-- 样式自定义对话框 -->
  <StyleCustomizerDialog
    v-model="showStyleCustomizerDialog"
    :selected-nodes="graphStore.selectedNodes"
    :selected-edges="graphStore.selectedEdges"
    @apply-node-style="handleApplyNodeStyle"
    @apply-edge-style="handleApplyEdgeStyle"
  />
</template>

<style lang="scss" scoped>
.control-panel {
  position: relative;
  height: 100%;
  background: #ffffff;
  border-right: 1px solid #e8e8e8;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  
  &--collapsed {
    .collapse-button {
      left: 8px;
    }
  }
  
  .collapse-button {
    position: absolute;
    top: 12px;
    right: 8px;
    z-index: 10;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background: #e6f7ff;
      border-color: #1890ff;
      color: #1890ff;
    }
  }
  
  .panel-content {
    height: 100%;
    padding: 16px;
    padding-top: 48px; // 为折叠按钮留出空间
    overflow-y: auto;
  }

  .panel-section {
    margin-bottom: 20px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      color: #262626;
      margin-bottom: 12px;
      font-size: 14px;
    }
  }

  // 报表类型选择器
  .report-type-selector {
    display: flex;
    gap: 8px;

    .report-type-btn {
      flex: 1;
    }
  }

  // 协作工具
  .collaboration-tools {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .collaboration-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;

    :deep(.el-button__content) {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 8px;
      width: 100%;
    }
  }

  // 交互工具
  .interaction-tools {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .interaction-btn {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 8px;

      :deep(.el-button__content) {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 8px;
        width: 100%;
      }
    }
  }
  


  // 显示设置
  .display-settings {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .setting-item {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .setting-label {
        font-size: 13px;
        color: #666;
      }
    }
  }

  // 布局控制
  .layout-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .layout-mode-selector {
      :deep(.el-radio-group) {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .el-radio-button {
          margin: 0;

          .el-radio-button__inner {
            width: 100%;
            text-align: center;
            border-radius: 4px;
            margin-bottom: 2px;
          }
        }
      }
    }

    .layout-status {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6px 8px;
      background: #f5f5f5;
      border-radius: 4px;
      font-size: 12px;

      .status-label {
        color: #666;
      }

      .status-value {
        font-weight: 500;

        &.mode-locked {
          color: #ff4d4f;
        }

        &.mode-auto {
          color: #52c41a;
        }

        &.mode-manual {
          color: #1890ff;
        }
      }
    }

    .auto-layout-buttons {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-top: 12px;

      .auto-layout-btn {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;

        :deep(.el-button__content) {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 4px;
          width: 100%;
        }
      }

      .layout-history-buttons {
        display: flex;
        gap: 4px;

        .el-button {
          flex: 1;
          padding: 4px 8px;
        }
      }
    }
  }

  // 布局操作
  .layout-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .layout-action-btn {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 8px;

      :deep(.el-button__content) {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 8px;
        width: 100%;
      }
    }
  }

  // 属性设置
  .property-settings {
    .property-btn {
      width: 100%;
    }
  }



  // 样式设置
  .style-settings {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .preset-title,
    .template-title,
    .custom-title {
      margin: 0 0 8px 0;
      font-size: 12px;
      font-weight: 500;
      color: #666;
    }

    .preset-colors {
      display: grid;
      grid-template-columns: repeat(6, 1fr);
      gap: 6px;

      .color-preset {
        width: 24px;
        height: 24px;
        border-radius: 4px;
        cursor: pointer;
        border: 2px solid #fff;
        box-shadow: 0 0 0 1px #e8e8e8;
        transition: all 0.2s ease;

        &:hover {
          transform: scale(1.1);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
      }
    }

    .template-list {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .template-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          border-color: #1890ff;
          background-color: #f0f8ff;
        }

        .template-preview {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .template-name {
          font-size: 12px;
          color: #333;
          flex: 1;
        }
      }
    }

    .style-btn {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 8px;

      :deep(.el-button__content) {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 8px;
        width: 100%;
      }
    }
  }

  // 关系操作
  .relation-operations {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .relation-btn {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 8px;

      :deep(.el-button__content) {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 8px;
        width: 100%;
      }
    }
  }

  
  .collapsed-shortcuts {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px 8px;
    
    .shortcut-button {
      width: 32px;
      height: 32px;
      padding: 0;
      border-radius: 6px;
    }
  }
}

// 滚动条样式
.panel-content::-webkit-scrollbar {
  width: 4px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 2px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}
</style>
