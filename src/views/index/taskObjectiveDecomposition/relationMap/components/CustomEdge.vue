<!-- 自定义连线组件 -->
<script setup lang="ts" name="CustomEdge">
import { computed } from 'vue'
import { BaseEdge, EdgeLabelRenderer, getBezierPath } from '@vue-flow/core'
import type { EdgeProps } from '@vue-flow/core'
import { 
  EDGE_STYLE_MAP, 
  type EdgeData, 
  type EdgeType 
} from '@/define/taskGraph.define'

interface Props extends EdgeProps {
  data?: EdgeData
  type: EdgeType
}

const props = defineProps<Props>()

// ==================== 计算属性 ====================

/** 连线样式配置 */
const styleConfig = computed(() => EDGE_STYLE_MAP[props.type])

/** 贝塞尔曲线路径 */
const edgePath = computed(() => {
  return getBezierPath({
    sourceX: props.sourceX,
    sourceY: props.sourceY,
    sourcePosition: props.sourcePosition,
    targetX: props.targetX,
    targetY: props.targetY,
    targetPosition: props.targetPosition,
    curvature: 0.25
  })
})

/** 连线样式 */
const edgeStyle = computed(() => ({
  stroke: styleConfig.value.stroke,
  strokeWidth: styleConfig.value.strokeWidth,
  strokeDasharray: styleConfig.value.strokeDasharray === 'none' ? 
    undefined : styleConfig.value.strokeDasharray,
  markerEnd: styleConfig.value.markerEnd
}))

/** 标签位置 */
const labelPosition = computed(() => ({
  x: edgePath.value[1],
  y: edgePath.value[2]
}))

/** 是否显示标签 */
const showLabel = computed(() => {
  return props.data?.label || props.label
})

/** 显示的标签文本 */
const displayLabel = computed(() => {
  return props.data?.label || props.label || ''
})

/** 连线类型显示名称 */
const typeDisplayName = computed(() => {
  switch (props.type) {
    case 'dependency':
      return '依赖'
    case 'sequence':
      return '顺序'
    case 'parallel':
      return '并行'
    case 'conditional':
      return '条件'
    case 'custom':
      return '自定义'
    default:
      return '连线'
  }
})

/** 是否为动画连线 */
const isAnimated = computed(() => {
  return props.animated || styleConfig.value.animated || props.data?.animated
})

/** 连线权重 */
const edgeWeight = computed(() => {
  return props.data?.weight || 1
})

/** 连线描述 */
const edgeDescription = computed(() => {
  return props.data?.description || ''
})

// ==================== 事件处理 ====================

/**
 * 处理连线点击
 */
const handleEdgeClick = (event: MouseEvent) => {
  event.stopPropagation()
  // 这里可以触发连线选择或编辑事件
}

/**
 * 处理标签点击
 */
const handleLabelClick = (event: MouseEvent) => {
  event.stopPropagation()
  // 这里可以触发标签编辑
}
</script>

<template>
  <g class="custom-edge" :class="`custom-edge--${type}`">
    <!-- 主连线路径 -->
    <BaseEdge 
      :id="id"
      :path="edgePath[0]" 
      :style="edgeStyle"
      :class="{
        'animated': isAnimated,
        'selected': selected
      }"
      @click="handleEdgeClick"
    />
    
    <!-- 连线箭头标记 -->
    <defs>
      <marker
        :id="`arrow-${id}`"
        viewBox="0 0 10 10"
        refX="9"
        refY="3"
        markerWidth="6"
        markerHeight="6"
        orient="auto"
        markerUnits="strokeWidth"
      >
        <path
          d="M0,0 L0,6 L9,3 z"
          :fill="styleConfig.stroke"
        />
      </marker>
      
      <!-- 条件连线的菱形标记 -->
      <marker
        v-if="type === 'conditional'"
        :id="`diamond-${id}`"
        viewBox="0 0 10 10"
        refX="5"
        refY="5"
        markerWidth="8"
        markerHeight="8"
        orient="auto"
        markerUnits="strokeWidth"
      >
        <path
          d="M5,0 L10,5 L5,10 L0,5 z"
          :fill="styleConfig.stroke"
          :stroke="styleConfig.stroke"
          stroke-width="1"
        />
      </marker>
    </defs>
    
    <!-- 权重指示器 -->
    <circle
      v-if="edgeWeight > 1"
      :cx="labelPosition.x"
      :cy="labelPosition.y - 15"
      r="8"
      :fill="styleConfig.stroke"
      opacity="0.8"
    />
    <text
      v-if="edgeWeight > 1"
      :x="labelPosition.x"
      :y="labelPosition.y - 11"
      text-anchor="middle"
      fill="white"
      font-size="10"
      font-weight="bold"
    >
      {{ edgeWeight }}
    </text>
  </g>
  
  <!-- 连线标签 -->
  <EdgeLabelRenderer v-if="showLabel">
    <div
      class="edge-label"
      :class="{
        'edge-label--selected': selected,
        [`edge-label--${type}`]: true
      }"
      :style="{
        position: 'absolute',
        transform: `translate(-50%, -50%) translate(${labelPosition.x}px, ${labelPosition.y}px)`,
        pointerEvents: 'all'
      }"
      @click="handleLabelClick"
    >
      <div class="edge-label__content">
        <div class="edge-label__text">
          {{ displayLabel }}
        </div>
        
        <!-- 连线类型标识 -->
        <div class="edge-label__type">
          {{ typeDisplayName }}
        </div>
        
        <!-- 条件表达式 -->
        <div 
          v-if="data?.condition"
          class="edge-label__condition"
        >
          {{ data.condition }}
        </div>
        
        <!-- 描述信息 -->
        <div 
          v-if="edgeDescription"
          class="edge-label__description"
          :title="edgeDescription"
        >
          {{ edgeDescription.length > 20 ? 
            `${edgeDescription.substring(0, 20)}...` : 
            edgeDescription 
          }}
        </div>
      </div>
      
      <!-- 选中指示器 */
      <div 
        v-if="selected"
        class="edge-label__selection-indicator"
      />
    </div>
  </EdgeLabelRenderer>
</template>

<style lang="scss" scoped>
.custom-edge {
  cursor: pointer;
  
  &--dependency {
    .vue-flow__edge-path {
      stroke-dasharray: none;
    }
  }
  
  &--sequence {
    .vue-flow__edge-path {
      stroke-dasharray: none;
    }
    
    &.animated .vue-flow__edge-path {
      stroke-dasharray: 8 4;
      animation: dash-flow 1s linear infinite;
    }
  }
  
  &--parallel {
    .vue-flow__edge-path {
      stroke-dasharray: 5 5;
    }
  }
  
  &--conditional {
    .vue-flow__edge-path {
      stroke-dasharray: 10 5;
    }
  }
  
  &--custom {
    .vue-flow__edge-path {
      stroke-dasharray: none;
    }
  }
}

.vue-flow__edge-path {
  transition: all 0.2s ease;
  
  &:hover {
    stroke-width: 3;
  }
  
  &.selected {
    stroke: #1890ff !important;
    stroke-width: 3;
  }
  
  &.animated {
    animation: dash-flow 1s linear infinite;
  }
}

.edge-label {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  max-width: 150px;
  
  &:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: scale(1.05);
  }
  
  &--selected {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
  
  &--dependency {
    border-left: 3px solid #1890ff;
  }
  
  &--sequence {
    border-left: 3px solid #52c41a;
  }
  
  &--parallel {
    border-left: 3px solid #fa8c16;
  }
  
  &--conditional {
    border-left: 3px solid #722ed1;
  }
  
  &--custom {
    border-left: 3px solid #8c8c8c;
  }
  
  &__content {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }
  
  &__text {
    font-weight: 500;
    color: #262626;
    line-height: 1.2;
  }
  
  &__type {
    font-size: 10px;
    color: #8c8c8c;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  &__condition {
    font-size: 10px;
    color: #722ed1;
    font-style: italic;
    background: rgba(114, 46, 209, 0.1);
    padding: 2px 4px;
    border-radius: 2px;
  }
  
  &__description {
    font-size: 10px;
    color: #595959;
    line-height: 1.2;
    max-width: 120px;
    word-break: break-word;
  }
  
  &__selection-indicator {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid #1890ff;
    border-radius: 8px;
    pointer-events: none;
    animation: pulse 2s infinite;
  }
}

// 动画
@keyframes dash-flow {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: 12;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 不同连线类型的特殊效果
.custom-edge--sequence.animated {
  .vue-flow__edge-path {
    stroke-dasharray: 8 4;
    animation: dash-flow 1s linear infinite;
  }
}

.custom-edge--parallel {
  .vue-flow__edge-path {
    opacity: 0.8;
  }
}

.custom-edge--conditional {
  .vue-flow__edge-path {
    opacity: 0.9;
  }
}

// 选中状态的特殊样式
.custom-edge .vue-flow__edge-path.selected {
  filter: drop-shadow(0 0 6px rgba(24, 144, 255, 0.6));
}
</style>
