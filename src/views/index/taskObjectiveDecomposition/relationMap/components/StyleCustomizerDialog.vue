<template>
  <el-dialog
    v-model="visible"
    title="自定义样式"
    width="600px"
    :before-close="handleClose"
  >
    <div class="style-customizer">
      <!-- 样式类型选择 -->
      <div class="style-type-selector">
        <el-radio-group v-model="styleType" @change="handleStyleTypeChange">
          <el-radio-button label="node">节点样式</el-radio-button>
          <el-radio-button label="edge">连线样式</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 节点样式配置 -->
      <div v-if="styleType === 'node'" class="node-style-config">
        <el-form :model="nodeStyle" label-width="100px" label-position="left">
          <!-- 背景颜色 -->
          <el-form-item label="背景颜色">
            <el-color-picker
              v-model="nodeStyle.backgroundColor"
              :predefine="predefineColors"
              show-alpha
            />
            <span class="color-value">{{ nodeStyle.backgroundColor }}</span>
          </el-form-item>

          <!-- 边框颜色 -->
          <el-form-item label="边框颜色">
            <el-color-picker
              v-model="nodeStyle.borderColor"
              :predefine="predefineColors"
              show-alpha
            />
            <span class="color-value">{{ nodeStyle.borderColor }}</span>
          </el-form-item>

          <!-- 文字颜色 -->
          <el-form-item label="文字颜色">
            <el-color-picker
              v-model="nodeStyle.textColor"
              :predefine="predefineColors"
              show-alpha
            />
            <span class="color-value">{{ nodeStyle.textColor }}</span>
          </el-form-item>

          <!-- 节点尺寸 -->
          <el-form-item label="节点尺寸">
            <el-select v-model="nodeStyle.sizePreset" @change="handleSizePresetChange">
              <el-option label="小型 (80x80)" value="small" />
              <el-option label="中型 (100x100)" value="medium" />
              <el-option label="大型 (120x120)" value="large" />
              <el-option label="自定义" value="custom" />
            </el-select>
          </el-form-item>

          <!-- 自定义尺寸 -->
          <div v-if="nodeStyle.sizePreset === 'custom'" class="custom-size">
            <el-form-item label="宽度">
              <el-input-number
                v-model="nodeStyle.width"
                :min="50"
                :max="200"
                :step="10"
                controls-position="right"
              />
              <span class="unit">px</span>
            </el-form-item>
            <el-form-item label="高度">
              <el-input-number
                v-model="nodeStyle.height"
                :min="50"
                :max="200"
                :step="10"
                controls-position="right"
              />
              <span class="unit">px</span>
            </el-form-item>
          </div>

          <!-- 边框圆角 -->
          <el-form-item label="边框圆角">
            <el-slider
              v-model="nodeStyle.borderRadius"
              :min="0"
              :max="50"
              :step="1"
              show-input
            />
            <span class="unit">%</span>
          </el-form-item>

          <!-- 字体大小 -->
          <el-form-item label="字体大小">
            <el-input-number
              v-model="nodeStyle.fontSize"
              :min="10"
              :max="24"
              :step="1"
              controls-position="right"
            />
            <span class="unit">px</span>
          </el-form-item>

          <!-- 字体粗细 -->
          <el-form-item label="字体粗细">
            <el-select v-model="nodeStyle.fontWeight">
              <el-option label="正常" value="normal" />
              <el-option label="粗体" value="bold" />
              <el-option label="更粗" value="bolder" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 连线样式配置 -->
      <div v-if="styleType === 'edge'" class="edge-style-config">
        <el-form :model="edgeStyle" label-width="100px" label-position="left">
          <!-- 连线颜色 -->
          <el-form-item label="连线颜色">
            <el-color-picker
              v-model="edgeStyle.stroke"
              :predefine="predefineColors"
              show-alpha
            />
            <span class="color-value">{{ edgeStyle.stroke }}</span>
          </el-form-item>

          <!-- 连线宽度 -->
          <el-form-item label="连线宽度">
            <el-input-number
              v-model="edgeStyle.strokeWidth"
              :min="1"
              :max="10"
              :step="1"
              controls-position="right"
            />
            <span class="unit">px</span>
          </el-form-item>

          <!-- 连线样式 -->
          <el-form-item label="连线样式">
            <el-select v-model="edgeStyle.strokeDasharray">
              <el-option label="实线" value="none" />
              <el-option label="虚线" value="5,5" />
              <el-option label="点线" value="2,2" />
              <el-option label="点划线" value="10,5,2,5" />
            </el-select>
          </el-form-item>

          <!-- 箭头颜色 -->
          <el-form-item label="箭头颜色">
            <el-color-picker
              v-model="edgeStyle.markerEnd"
              :predefine="predefineColors"
              show-alpha
            />
            <span class="color-value">{{ edgeStyle.markerEnd }}</span>
          </el-form-item>
        </el-form>
      </div>

      <!-- 预览区域 -->
      <div class="style-preview">
        <h4>样式预览</h4>
        <div class="preview-container">
          <div
            v-if="styleType === 'node'"
            class="node-preview"
            :style="nodePreviewStyle"
          >
            示例节点
          </div>
          <div
            v-if="styleType === 'edge'"
            class="edge-preview"
          >
            <svg width="200" height="60">
              <line
                x1="20"
                y1="30"
                x2="180"
                y2="30"
                :stroke="edgeStyle.stroke"
                :stroke-width="edgeStyle.strokeWidth"
                :stroke-dasharray="edgeStyle.strokeDasharray"
                marker-end="url(#arrowhead)"
              />
              <defs>
                <marker
                  id="arrowhead"
                  markerWidth="10"
                  markerHeight="7"
                  refX="9"
                  refY="3.5"
                  orient="auto"
                >
                  <polygon
                    points="0 0, 10 3.5, 0 7"
                    :fill="edgeStyle.markerEnd"
                  />
                </marker>
              </defs>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" @click="handleApply">
          应用样式
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// ==================== 类型定义 ====================

interface NodeStyleConfig {
  backgroundColor: string
  borderColor: string
  textColor: string
  sizePreset: 'small' | 'medium' | 'large' | 'custom'
  width: number
  height: number
  borderRadius: number
  fontSize: number
  fontWeight: 'normal' | 'bold' | 'bolder'
}

interface EdgeStyleConfig {
  stroke: string
  strokeWidth: number
  strokeDasharray: string
  markerEnd: string
}

// ==================== Props & Emits ====================

interface Props {
  modelValue: boolean
  selectedNodes?: any[]
  selectedEdges?: any[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'apply-node-style', style: NodeStyleConfig): void
  (e: 'apply-edge-style', style: EdgeStyleConfig): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

const styleType = ref<'node' | 'edge'>('node')

// 预定义颜色
const predefineColors = [
  '#1890ff', '#52c41a', '#fa8c16', '#ff4d4f', '#722ed1', '#13c2c2',
  '#eb2f96', '#f5222d', '#fa541c', '#faad14', '#a0d911', '#52c41a',
  '#13c2c2', '#1890ff', '#2f54eb', '#722ed1', '#eb2f96', '#f5222d'
]

// 节点样式配置
const nodeStyle = ref<NodeStyleConfig>({
  backgroundColor: '#1890ff',
  borderColor: '#096dd9',
  textColor: '#ffffff',
  sizePreset: 'medium',
  width: 100,
  height: 100,
  borderRadius: 50,
  fontSize: 12,
  fontWeight: 'normal'
})

// 连线样式配置
const edgeStyle = ref<EdgeStyleConfig>({
  stroke: '#d9d9d9',
  strokeWidth: 2,
  strokeDasharray: 'none', // 修改为 'none' 以匹配实线选项
  markerEnd: '#d9d9d9'
})

// ==================== 计算属性 ====================

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 节点预览样式
const nodePreviewStyle = computed(() => ({
  backgroundColor: nodeStyle.value.backgroundColor,
  borderColor: nodeStyle.value.borderColor,
  color: nodeStyle.value.textColor,
  width: `${nodeStyle.value.width}px`,
  height: `${nodeStyle.value.height}px`,
  borderRadius: `${nodeStyle.value.borderRadius}%`,
  fontSize: `${nodeStyle.value.fontSize}px`,
  fontWeight: nodeStyle.value.fontWeight,
  border: `2px solid ${nodeStyle.value.borderColor}`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
}))

// ==================== 方法 ====================

/**
 * 处理样式类型变化
 */
const handleStyleTypeChange = () => {
  // 可以在这里重置样式配置
}

/**
 * 处理尺寸预设变化
 */
const handleSizePresetChange = () => {
  const sizeMap = {
    small: { width: 80, height: 80 },
    medium: { width: 100, height: 100 },
    large: { width: 120, height: 120 },
    custom: { width: nodeStyle.value.width, height: nodeStyle.value.height }
  }
  
  const size = sizeMap[nodeStyle.value.sizePreset]
  if (nodeStyle.value.sizePreset !== 'custom') {
    nodeStyle.value.width = size.width
    nodeStyle.value.height = size.height
  }
}

/**
 * 应用样式
 */
const handleApply = () => {
  console.log('🎨 StyleCustomizerDialog 应用样式:', {
    styleType: styleType.value,
    selectedNodes: props.selectedNodes,
    selectedNodesLength: props.selectedNodes?.length,
    selectedEdges: props.selectedEdges,
    selectedEdgesLength: props.selectedEdges?.length,
    nodeStyle: nodeStyle.value,
    edgeStyle: edgeStyle.value
  })

  if (styleType.value === 'node') {
    if (!props.selectedNodes || props.selectedNodes.length === 0) {
      ElMessage.warning('请先选择要应用样式的节点')
      return
    }
    emit('apply-node-style', { ...nodeStyle.value })
  } else {
    if (!props.selectedEdges || props.selectedEdges.length === 0) {
      ElMessage.warning('请先选择要应用样式的连线')
      return
    }
    emit('apply-edge-style', { ...edgeStyle.value })
  }

  visible.value = false
  ElMessage.success('样式应用成功')
}

/**
 * 重置样式
 */
const handleReset = () => {
  if (styleType.value === 'node') {
    nodeStyle.value = {
      backgroundColor: '#1890ff',
      borderColor: '#096dd9',
      textColor: '#ffffff',
      sizePreset: 'medium',
      width: 100,
      height: 100,
      borderRadius: 50,
      fontSize: 12,
      fontWeight: 'normal'
    }
  } else {
    edgeStyle.value = {
      stroke: '#d9d9d9',
      strokeWidth: 2,
      strokeDasharray: '',
      markerEnd: '#d9d9d9'
    }
  }
}

/**
 * 关闭对话框
 */
const handleClose = () => {
  visible.value = false
}

// 监听选中元素变化，自动切换样式类型
watch(() => [props.selectedNodes, props.selectedEdges], ([nodes, edges]) => {
  if (nodes && nodes.length > 0 && (!edges || edges.length === 0)) {
    styleType.value = 'node'
  } else if (edges && edges.length > 0 && (!nodes || nodes.length === 0)) {
    styleType.value = 'edge'
  }
}, { immediate: true })
</script>

<style scoped lang="scss">
.style-customizer {
  .style-type-selector {
    margin-bottom: 20px;
    text-align: center;
  }

  .color-value {
    margin-left: 10px;
    font-family: monospace;
    font-size: 12px;
    color: #666;
  }

  .unit {
    margin-left: 5px;
    color: #666;
    font-size: 12px;
  }

  .custom-size {
    margin-left: 100px;
    padding: 10px;
    background: #f5f5f5;
    border-radius: 4px;
  }

  .style-preview {
    margin-top: 20px;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 6px;

    h4 {
      margin: 0 0 15px 0;
      font-size: 14px;
      color: #333;
    }

    .preview-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 80px;

      .node-preview {
        transition: all 0.3s ease;
      }

      .edge-preview {
        svg {
          border: 1px solid #e8e8e8;
          border-radius: 4px;
          background: white;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
