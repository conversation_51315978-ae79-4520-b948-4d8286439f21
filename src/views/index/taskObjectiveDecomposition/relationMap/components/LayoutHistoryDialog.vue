<template>
  <el-dialog
    v-model="visible"
    title="布局历史记录"
    width="800px"
    :before-close="handleClose"
  >
    <div class="layout-history-dialog">
      <!-- 历史记录列表 -->
      <div v-if="historyList.length > 0" class="history-list">
        <el-table
          :data="historyList"
          stripe
          :height="400"
          @row-click="handleRowClick"
        >
          <el-table-column type="index" label="序号" width="60" />
          
          <el-table-column label="时间" width="180">
            <template #default="{ row }">
              <div class="time-info">
                <el-icon><Clock /></el-icon>
                <span>{{ formatTime(row.timestamp) }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="节点数量" width="100">
            <template #default="{ row }">
              <el-tag type="info" size="small">
                {{ row.nodes.length }} 个节点
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="布局模式" width="120">
            <template #default="{ row }">
              <el-tag
                :type="getLayoutModeType(row.config)"
                size="small"
              >
                {{ getLayoutModeName(row.config) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="120">
            <template #default="{ row, $index }">
              <el-button
                type="primary"
                size="small"
                :loading="restoringIndex === $index"
                @click.stop="handleRestore($index)"
              >
                <el-icon><RefreshRight /></el-icon>
                还原
              </el-button>
            </template>
          </el-table-column>
          
          <el-table-column label="预览" min-width="200">
            <template #default="{ row }">
              <div class="layout-preview">
                <div class="preview-info">
                  <span>{{ row.nodes.length }} 个节点的布局快照</span>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="empty-state">
        <el-empty description="暂无布局历史记录">
          <el-button type="primary" @click="handleClose">
            关闭
          </el-button>
        </el-empty>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="danger" @click="handleClearHistory">
          <el-icon><Delete /></el-icon>
          清空历史
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Clock, RefreshRight, Delete } from '@element-plus/icons-vue'

// ==================== 类型定义 ====================

interface LayoutHistoryItem {
  nodes: any[]
  timestamp: number
  config?: any
}

// ==================== Props & Emits ====================

interface Props {
  modelValue: boolean
  historyData: LayoutHistoryItem[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'restore', index: number): void
  (e: 'clear'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================

const restoringIndex = ref(-1)

// ==================== 计算属性 ====================

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const historyList = computed(() => {
  return [...props.historyData].reverse() // 最新的在前面
})

// ==================== 方法 ====================

/**
 * 格式化时间
 */
const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/**
 * 获取布局模式名称
 */
const getLayoutModeName = (config?: any) => {
  const mode = config?.mode || 'manual'
  const modeMap: Record<string, string> = {
    'dagre': '智能布局',
    'hierarchical': '层次布局',
    'grid': '网格布局',
    'circular': '环形布局',
    'manual': '手动布局'
  }
  return modeMap[mode] || '未知'
}

/**
 * 获取布局模式标签类型
 */
const getLayoutModeType = (config?: any) => {
  const mode = config?.mode || 'manual'
  const typeMap: Record<string, string> = {
    'dagre': 'success',
    'hierarchical': 'warning',
    'grid': 'info',
    'circular': 'primary',
    'manual': ''
  }
  return typeMap[mode] || ''
}

/**
 * 处理行点击
 */
const handleRowClick = (row: LayoutHistoryItem, column: any, event: Event) => {
  // 如果点击的是操作列，不处理
  if (column.property === 'actions') return
  
  // 可以在这里添加预览功能
  console.log('点击历史记录:', row)
}

/**
 * 处理还原
 */
const handleRestore = async (index: number) => {
  try {
    restoringIndex.value = index
    
    // 计算实际的历史索引（因为列表是反向的）
    const actualIndex = props.historyData.length - 1 - index
    
    emit('restore', actualIndex)
  } catch (error) {
    console.error('还原失败:', error)
    ElMessage.error('还原失败')
  } finally {
    restoringIndex.value = -1
  }
}

/**
 * 处理清空历史
 */
const handleClearHistory = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有布局历史记录吗？此操作不可撤销。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    emit('clear')
    ElMessage.success('历史记录已清空')
  } catch {
    // 用户取消
  }
}

/**
 * 处理关闭
 */
const handleClose = () => {
  visible.value = false
}
</script>

<style scoped lang="scss">
.layout-history-dialog {
  .history-list {
    .time-info {
      display: flex;
      align-items: center;
      gap: 6px;
      
      .el-icon {
        color: var(--el-color-info);
      }
    }
    
    .layout-preview {
      .preview-info {
        color: var(--el-text-color-secondary);
        font-size: 12px;
      }
    }
  }
  
  .empty-state {
    padding: 40px 0;
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 表格行悬停效果
:deep(.el-table__row) {
  cursor: pointer;
  
  &:hover {
    background-color: var(--el-table-row-hover-bg-color);
  }
}
</style>
