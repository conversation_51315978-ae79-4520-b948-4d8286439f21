<!-- 图谱工具栏组件 -->
<script setup lang="ts" name="GraphToolbar">
import { ref, computed, watch } from 'vue'
import {
  Document,
  Download,
  Upload,
  Refresh,
  ZoomIn,
  ZoomOut,
  FullScreen,
  Back,
  Delete,
  CopyDocument,
  Edit
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useGraphStore } from '../stores/useGraphStore'
// import { useGraphOperations } from '../composables/useGraphOperations' // 暂时注释
import type { ExportFormat } from '@/define/taskGraph.define'

interface Props {
  /** 是否只读模式 */
  readonly?: boolean
  /** 是否显示返回按钮 */
  showBackButton?: boolean
  /** 当前缩放比例（0.1 到 4.0） */
  currentZoom?: number
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  showBackButton: true,
  currentZoom: 1.0
})

const emit = defineEmits<{
  back: []
  save: []
  export: [format: ExportFormat]
  import: [file: File]
  refresh: []
  zoomIn: []
  zoomOut: []
  fitView: []
  fullscreen: []
  zoomTo: [zoom: number]
}>()

// ==================== 状态管理 ====================

const graphStore = useGraphStore()
// const graphOperations = useGraphOperations() // 暂时注释

/** 当前缩放比例（百分比） */
const zoomPercentage = ref(100)

// 监听外部传入的缩放比例变化
watch(() => props.currentZoom, (newZoom) => {
  if (newZoom) {
    zoomPercentage.value = Math.round(newZoom * 100)
  }
}, { immediate: true })

// ==================== 响应式数据 ====================

/** 导出格式选项 */
const exportFormats = [
  { label: 'PNG图片', value: 'png' as ExportFormat, icon: 'picture' },
  { label: 'SVG矢量图', value: 'svg' as ExportFormat, icon: 'picture' },
  { label: 'JSON数据', value: 'json' as ExportFormat, icon: 'document' }
]

/** 文件上传引用 */
const fileUploadRef = ref()

/** 是否正在保存 */
const isSaving = ref(false)

/** 是否正在导出 */
const isExporting = ref(false)

// ==================== 计算属性 ====================

/** 是否有未保存的更改 */
const hasUnsavedChanges = computed(() => graphStore.isDirty)

/** 是否可以撤销 */
const canUndo = computed(() => graphStore.canUndo)

/** 是否可以重做 */
const canRedo = computed(() => graphStore.canRedo)

/** 选中元素数量 */
const selectionCount = computed(() => graphStore.selectedElements.length)

/** 图谱统计信息 */
const graphStats = computed(() => graphStore.graphStats)

// ==================== 事件处理方法 ====================

/**
 * 返回上一页
 */
const handleBack = () => {
  if (hasUnsavedChanges.value) {
    ElMessageBox.confirm(
      '当前有未保存的更改，确定要离开吗？',
      '确认离开',
      {
        confirmButtonText: '离开',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      emit('back')
    }).catch(() => {
      // 用户取消
    })
  } else {
    emit('back')
  }
}

/**
 * 保存图谱
 */
const handleSave = async () => {
  if (props.readonly) return
  
  isSaving.value = true
  
  try {
    const success = graphStore.saveGraph()
    if (success) {
      ElMessage.success('图谱保存成功')
      emit('save')
    } else {
      ElMessage.error('图谱保存失败')
    }
  } catch (error) {
    ElMessage.error('保存过程中发生错误')
  } finally {
    isSaving.value = false
  }
}

/**
 * 导出图谱
 */
const handleExport = async (format: ExportFormat) => {
  isExporting.value = true
  
  try {
    emit('export', format)
    ElMessage.success(`开始导出${format.toUpperCase()}格式`)
  } catch (error) {
    ElMessage.error('导出失败')
  } finally {
    isExporting.value = false
  }
}

/**
 * 导入图谱
 */
const handleImport = () => {
  if (props.readonly) return
  
  if (hasUnsavedChanges.value) {
    ElMessageBox.confirm(
      '导入新图谱将覆盖当前内容，确定继续吗？',
      '确认导入',
      {
        confirmButtonText: '继续',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      fileUploadRef.value?.click()
    }).catch(() => {
      // 用户取消
    })
  } else {
    fileUploadRef.value?.click()
  }
}

/**
 * 处理文件选择
 */
const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    if (file.type !== 'application/json') {
      ElMessage.error('请选择JSON格式的文件')
      return
    }
    
    emit('import', file)
    // 清空文件输入
    target.value = ''
  }
}

/**
 * 刷新图谱
 */
const handleRefresh = () => {
  if (hasUnsavedChanges.value) {
    ElMessageBox.confirm(
      '刷新将丢失未保存的更改，确定继续吗？',
      '确认刷新',
      {
        confirmButtonText: '刷新',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      emit('refresh')
      ElMessage.success('图谱已刷新')
    }).catch(() => {
      // 用户取消
    })
  } else {
    emit('refresh')
    ElMessage.success('图谱已刷新')
  }
}

/**
 * 撤销操作
 */
const handleUndo = () => {
  if (canUndo.value) {
    graphStore.undo()
    ElMessage.success('已撤销')
  }
}

/**
 * 重做操作
 */
const handleRedo = () => {
  if (canRedo.value) {
    graphStore.redo()
    ElMessage.success('已重做')
  }
}

/**
 * 删除选中元素
 */
const handleDeleteSelection = () => {
  if (selectionCount.value > 0) {
    // graphOperations.deleteSelection() // 暂时注释
    ElMessage.info('删除功能开发中')
  }
}

/**
 * 复制选中元素
 */
const handleCopySelection = () => {
  if (selectionCount.value > 0) {
    // 这里可以实现复制功能
    ElMessage.info('复制功能开发中')
  }
}

/**
 * 编辑选中元素
 */
const handleEditSelection = () => {
  const selectedNodes = graphStore.selectedNodes
  const selectedEdges = graphStore.selectedEdges

  if (selectedNodes.length === 1) {
    // graphOperations.editNode(selectedNodes[0].id) // 暂时注释
    ElMessage.info('编辑功能开发中')
  } else if (selectedEdges.length === 1) {
    // graphOperations.editEdge(selectedEdges[0].id) // 暂时注释
    ElMessage.info('编辑功能开发中')
  } else {
    ElMessage.warning('请选择单个元素进行编辑')
  }
}

/**
 * 快速导出
 */
const handleQuickExport = () => {
  handleExport('png' as ExportFormat)
}

/**
 * 处理缩放变化
 */
const handleZoomChange = (value: number | number[]) => {
  const zoomValue = Array.isArray(value) ? value[0] : value
  zoomPercentage.value = zoomValue

  // 将百分比转换为实际缩放比例（0.1 到 4.0）
  const actualZoom = zoomValue / 100

  // 发送缩放事件给父组件
  emit('zoomTo', actualZoom)
}
</script>

<template>
  <div class="graph-toolbar">
    <!-- 左侧按钮组 -->
    <div class="toolbar-section toolbar-section--left">
      <!-- 任务关系图谱绘制 -->
      <div class="toolbar-title">
        <span>任务关系图谱绘制</span>
      </div>

      <!-- 保存按钮 -->
      <el-button
        v-if="!readonly"
        type="primary"
        :loading="isSaving"
        :disabled="!hasUnsavedChanges"
        @click="handleSave"
      >
        保存
      </el-button>

      <!-- 导出按钮 -->
      <el-button
        type="success"
        @click="handleQuickExport"
      >
        导出
      </el-button>
    </div>
    
    <!-- 中间按钮组 -->
    <div class="toolbar-section toolbar-section--center">
      <!-- 缩放控制 -->
      <div class="zoom-controls">
        <span class="zoom-label">缩放:</span>
        <el-slider
          v-model="zoomPercentage"
          :min="10"
          :max="400"
          :step="1"
          class="zoom-slider"
          @change="handleZoomChange"
        />
        <span class="zoom-value">{{ zoomPercentage }}%</span>
      </div>

      <!-- 联动和制图版功能 -->
      <div class="chart-controls">
        <el-button size="small" type="primary">联动</el-button>
        <el-button size="small">制图版</el-button>
      </div>
    </div>
    
    <!-- 右侧按钮组 -->
    <div class="toolbar-section toolbar-section--right">
      <!-- 导出提示 -->
      <div class="export-hint">
        <span class="hint-text">导出提示记录</span>
        <div class="hint-badge">1</div>
      </div>

      <!-- 视图控制 -->
      <el-button-group>
        <el-button
          :icon="ZoomOut"
          @click="$emit('zoomOut')"
          title="缩小"
          size="small"
        />
        <el-button
          @click="$emit('fitView')"
          title="适应视图"
          size="small"
        >
          1x
        </el-button>
        <el-button
          :icon="ZoomIn"
          @click="$emit('zoomIn')"
          title="放大"
          size="small"
        />
      </el-button-group>
    </div>
    
    <!-- 隐藏的文件上传输入 -->
    <input
      ref="fileUploadRef"
      type="file"
      accept=".json"
      style="display: none"
      @change="handleFileChange"
    />
  </div>
</template>

<style lang="scss" scoped>
.graph-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #ffffff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  
  .toolbar-section {
    display: flex;
    align-items: center;
    gap: 12px;

    &--left {
      flex: 0 0 auto;

      .toolbar-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        margin-right: 16px;
      }
    }

    &--center {
      flex: 1 1 auto;
      justify-content: center;
      gap: 24px;

      .zoom-controls {
        display: flex;
        align-items: center;
        gap: 8px;

        .zoom-label {
          font-size: 14px;
          color: #666;
        }

        .zoom-slider {
          width: 120px;
        }

        .zoom-value {
          font-size: 14px;
          color: #666;
          min-width: 40px;
        }
      }

      .chart-controls {
        display: flex;
        gap: 8px;
      }
    }

    &--right {
      flex: 0 0 auto;

      .export-hint {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 4px 8px;
        background: #f5f5f5;
        border-radius: 4px;

        .hint-text {
          font-size: 12px;
          color: #666;
        }

        .hint-badge {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 16px;
          height: 16px;
          background: #ff4d4f;
          color: white;
          border-radius: 50%;
          font-size: 10px;
          font-weight: bold;
        }
      }
    }
  }
  
  .selection-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .selection-count {
      font-size: 14px;
      color: #1890ff;
      font-weight: 500;
    }
  }
  
  .graph-stats {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 14px;
    color: #666;
    
    span {
      padding: 4px 8px;
      background: #f5f5f5;
      border-radius: 4px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .graph-toolbar {
    flex-wrap: wrap;
    gap: 8px;
    
    .toolbar-section {
      &--center {
        order: 3;
        flex-basis: 100%;
        justify-content: center;
        margin-top: 8px;
      }
    }
    
    .graph-stats {
      gap: 8px;
      
      span {
        font-size: 12px;
        padding: 2px 6px;
      }
    }
  }
}
</style>
