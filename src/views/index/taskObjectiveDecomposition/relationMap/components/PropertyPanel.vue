<!-- 属性设置面板组件 -->
<script setup lang="ts" name="PropertyPanel">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useVueFlow } from '@vue-flow/core'
import { useGraphStore } from '../stores/useGraphStore'
import { useGraphOperations } from '../composables/useGraphOperations'
import type { TaskGraphNode } from '@/define/taskGraph.define'
import { TaskStatus, TaskPriority } from '@/define/taskGraph.define'

interface Props {
  /** 是否显示面板 */
  visible?: boolean
  /** 选中的节点 */
  selectedNode?: TaskGraphNode | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  selectedNode: null
})

const emit = defineEmits<{
  close: []
}>()

// ==================== 状态管理 ====================

const graphStore = useGraphStore()
const graphOperations = useGraphOperations()
const { updateNode } = useVueFlow()

// ==================== 响应式数据 ====================

/** 编辑中的坐标值 */
const editingPosition = ref({ x: 0, y: 0 })

/** 是否正在预览 */
const isPreviewing = ref(false)

/** 原始位置（用于取消预览） */
const originalPosition = ref({ x: 0, y: 0 })

/** 编辑中的节点属性 */
const editingProperties = ref({
  label: '',
  description: '',
  priority: 'medium',
  assignee: '',
  dueDate: '',
  status: 'not-started',
  progress: 0,
  tags: [] as string[]
})

/** 新标签输入 */
const newTag = ref('')

/** 优先级选项 */
const priorityOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

/** 状态选项 */
const statusOptions = [
  { label: '未开始', value: 'not-started' },
  { label: '进行中', value: 'in-progress' },
  { label: '已暂停', value: 'paused' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' }
]

// ==================== 计算属性 ====================

/** 当前选中的节点 */
const currentNode = computed(() => props.selectedNode)

/** 是否有选中的节点 */
const hasSelectedNode = computed(() => !!currentNode.value)

/** 节点基本信息 */
const nodeInfo = computed(() => {
  if (!currentNode.value) return null
  
  return {
    id: currentNode.value.id,
    label: currentNode.value.data.label,
    type: currentNode.value.type,
    status: currentNode.value.data.status,
    position: currentNode.value.position
  }
})

// ==================== 监听器 ====================

/** 监听选中节点变化 */
watch(currentNode, (newNode) => {
  if (newNode) {
    editingPosition.value = { ...newNode.position }
    originalPosition.value = { ...newNode.position }
    isPreviewing.value = false

    // 同步节点属性到编辑状态
    editingProperties.value = {
      label: newNode.data?.label || '',
      description: newNode.data?.description || '',
      priority: newNode.data?.priority || 'medium',
      assignee: newNode.data?.assignee || '',
      dueDate: newNode.data?.dueDate ? (typeof newNode.data.dueDate === 'string' ? newNode.data.dueDate : newNode.data.dueDate.toISOString().split('T')[0]) : '',
      status: newNode.data?.status || 'not-started',
      progress: newNode.data?.progress || 0,
      tags: newNode.data?.tags || []
    }
  }
}, { immediate: true })

// ==================== 事件处理方法 ====================

/**
 * 处理坐标输入变化
 */
const handlePositionChange = () => {
  if (!currentNode.value) return
  
  // 验证输入值
  const x = Number(editingPosition.value.x)
  const y = Number(editingPosition.value.y)
  
  if (isNaN(x) || isNaN(y)) {
    ElMessage.warning('请输入有效的数字')
    return
  }
  
  // 限制坐标范围
  editingPosition.value.x = Math.max(0, Math.min(2000, x))
  editingPosition.value.y = Math.max(0, Math.min(2000, y))
}

/**
 * 开始预览位置
 */
const startPreview = () => {
  if (!currentNode.value) return
  
  handlePositionChange()
  
  // 临时更新节点位置
  graphStore.moveNode(currentNode.value.id, editingPosition.value)
  isPreviewing.value = true
}

/**
 * 应用位置更改 - 修复版本
 */
const applyPosition = () => {
  if (!currentNode.value) return

  try {
    console.log('应用位置变化:', editingPosition.value)

    // 使用 Vue Flow 的 updateNode API 更新节点位置
    updateNode(currentNode.value.id, { position: editingPosition.value })

    // 同时更新 store 中的数据
    const success = graphStore.moveNode(currentNode.value.id, editingPosition.value)

    if (success) {
      // 更新原始位置为当前编辑位置
      originalPosition.value = { ...editingPosition.value }
      isPreviewing.value = false

      console.log('节点位置已通过 Vue Flow API 更新')
      ElMessage.success('位置更新成功')
    } else {
      ElMessage.error('位置更新失败')
    }
  } catch (error) {
    console.error('位置更新失败:', error)
    ElMessage.error('位置更新失败')
  }
}

/**
 * 取消预览
 */
const cancelPreview = () => {
  if (!currentNode.value || !isPreviewing.value) return
  
  // 恢复原始位置
  graphStore.moveNode(currentNode.value.id, originalPosition.value)
  editingPosition.value = { ...originalPosition.value }
  isPreviewing.value = false
}

/**
 * 重置位置
 */
const resetPosition = () => {
  if (!currentNode.value) return
  
  editingPosition.value = { ...originalPosition.value }
  if (isPreviewing.value) {
    graphStore.moveNode(currentNode.value.id, originalPosition.value)
  }
}

/**
 * 保存节点属性
 */
const saveProperties = () => {
  if (!currentNode.value) return

  try {
    const updates = {
      data: {
        ...currentNode.value.data,
        ...editingProperties.value,
        status: editingProperties.value.status as TaskStatus,
        priority: editingProperties.value.priority as TaskPriority
      }
    }

    graphStore.updateNode(currentNode.value.id, updates)
    ElMessage.success('属性保存成功')
  } catch (error) {
    console.error('保存属性失败:', error)
    ElMessage.error('保存属性失败')
  }
}

/**
 * 添加标签
 */
const addTag = () => {
  if (!newTag.value.trim()) return

  if (!editingProperties.value.tags.includes(newTag.value.trim())) {
    editingProperties.value.tags.push(newTag.value.trim())
    newTag.value = ''
  }
}

/**
 * 移除标签
 */
const removeTag = (index: number) => {
  editingProperties.value.tags.splice(index, 1)
}

/**
 * 重置属性
 */
const resetProperties = () => {
  if (currentNode.value) {
    editingProperties.value = {
      label: currentNode.value.data?.label || '',
      description: currentNode.value.data?.description || '',
      priority: currentNode.value.data?.priority || 'medium',
      assignee: currentNode.value.data?.assignee || '',
      dueDate: currentNode.value.data?.dueDate ? (typeof currentNode.value.data.dueDate === 'string' ? currentNode.value.data.dueDate : currentNode.value.data.dueDate.toISOString().split('T')[0]) : '',
      status: currentNode.value.data?.status || 'not-started',
      progress: currentNode.value.data?.progress || 0,
      tags: currentNode.value.data?.tags || []
    }
  }
}

/**
 * 关闭面板
 */
const closePanel = () => {
  if (isPreviewing.value) {
    cancelPreview()
  }
  emit('close')
}
</script>

<template>
  <div v-if="visible" class="property-panel">
    <div class="panel-header">
      <h4 class="panel-title">属性设置</h4>
      <el-button
        type="text"
        size="small"
        @click="closePanel"
        class="close-btn"
      >
        ×
      </el-button>
    </div>
    
    <div v-if="hasSelectedNode" class="panel-content">
      <!-- 节点基本信息 -->
      <div class="info-section">
        <h5 class="section-title">基本信息</h5>
        <div class="info-item">
          <span class="info-label">ID:</span>
          <span class="info-value">{{ nodeInfo?.id }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">标签:</span>
          <span class="info-value">{{ nodeInfo?.label }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">类型:</span>
          <span class="info-value">{{ nodeInfo?.type }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">状态:</span>
          <span class="info-value">{{ nodeInfo?.status }}</span>
        </div>
      </div>
      
      <!-- 位置设置 -->
      <div class="position-section">
        <h5 class="section-title">位置设置</h5>
        
        <div class="position-inputs">
          <div class="input-group">
            <label class="input-label">X坐标:</label>
            <el-input-number
              v-model="editingPosition.x"
              :min="0"
              :max="2000"
              :step="10"
              size="small"
              @change="handlePositionChange"
            />
          </div>
          
          <div class="input-group">
            <label class="input-label">Y坐标:</label>
            <el-input-number
              v-model="editingPosition.y"
              :min="0"
              :max="2000"
              :step="10"
              size="small"
              @change="handlePositionChange"
            />
          </div>
        </div>
        
        <div class="position-actions">
          <el-button
            type="primary"
            size="small"
            @click="startPreview"
            :disabled="isPreviewing"
          >
            预览
          </el-button>
          
          <el-button
            type="success"
            size="small"
            @click="applyPosition"
          >
            应用
          </el-button>
          
          <el-button
            v-if="isPreviewing"
            type="warning"
            size="small"
            @click="cancelPreview"
          >
            取消预览
          </el-button>
          
          <el-button
            type="default"
            size="small"
            @click="resetPosition"
          >
            重置
          </el-button>
        </div>
        
        <div v-if="isPreviewing" class="preview-hint">
          <el-alert
            title="正在预览位置变更"
            type="info"
            :closable="false"
            show-icon
          />
        </div>
      </div>
    </div>
    
    <div v-else class="no-selection">
      <p>请选择一个节点以查看其属性</p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.property-panel {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: #f5f5f5;
    border-bottom: 1px solid #e8e8e8;
    
    .panel-title {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: #262626;
    }
    
    .close-btn {
      padding: 0;
      font-size: 18px;
      color: #999;
      
      &:hover {
        color: #666;
      }
    }
  }
  
  .panel-content {
    padding: 16px;
    max-height: 400px;
    overflow-y: auto;
  }
  
  .info-section,
  .position-section {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      margin: 0 0 12px 0;
      font-size: 13px;
      font-weight: 500;
      color: #262626;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 6px;
    }
  }
  
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 12px;
    
    .info-label {
      color: #666;
      font-weight: 500;
    }
    
    .info-value {
      color: #262626;
      word-break: break-word;
    }
  }
  
  .position-inputs {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
    
    .input-group {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .input-label {
        font-size: 12px;
        color: #666;
        width: 50px;
        flex-shrink: 0;
      }
      
      :deep(.el-input-number) {
        flex: 1;
        
        .el-input__inner {
          font-size: 12px;
        }
      }
    }
  }
  
  .position-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;
    
    .el-button {
      flex: 1;
      min-width: 60px;
    }
  }
  
  .preview-hint {
    :deep(.el-alert) {
      padding: 8px 12px;
      
      .el-alert__title {
        font-size: 12px;
      }
    }
  }
  
  .no-selection {
    padding: 40px 16px;
    text-align: center;
    color: #999;
    font-size: 13px;
  }
}
</style>
