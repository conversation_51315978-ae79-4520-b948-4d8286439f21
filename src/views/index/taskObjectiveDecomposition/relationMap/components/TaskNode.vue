<!-- 任务节点组件 -->
<script setup lang="ts" name="TaskNode">
import { computed, ref, nextTick } from 'vue'
import { Handle, Position } from '@vue-flow/core'
import type { NodeProps } from '@vue-flow/core'
import {
  NODE_STYLE_MAP,
  type TaskNodeData,
  type TaskNodeType
} from '@/define/taskGraph.define'
import {
  User,
  Clock,
  Flag,
  CircleCheck,
  Loading,
  WarningFilled
} from '@element-plus/icons-vue'
import { useGraphStore } from '../stores/useGraphStore'
import { ElMessage } from 'element-plus'

interface Props extends /* @vue-ignore */ NodeProps {
  id: string
  data: TaskNodeData
  type: TaskNodeType
  selected?: boolean
}

const props = defineProps<Props>()

// ==================== Store ====================

const graphStore = useGraphStore()

// ==================== 响应式数据 ====================

/** 是否正在编辑标签 */
const isEditingLabel = ref(false)
/** 编辑中的标签文本 */
const editingLabel = ref('')

// ==================== 计算属性 ====================

/** 节点样式配置 */
const styleConfig = computed(() => NODE_STYLE_MAP[props.type])

/** 节点内联样式 */
const nodeStyle = computed(() => {
  // 获取默认样式配置
  const defaultStyle = styleConfig.value

  // 获取自定义样式（如果存在）
  const customStyle = props.data.metadata?.customStyle

  // 合并样式，自定义样式优先
  return {
    backgroundColor: customStyle?.backgroundColor || props.data.color || defaultStyle.backgroundColor,
    borderColor: customStyle?.borderColor || defaultStyle.borderColor,
    color: customStyle?.textColor || defaultStyle.textColor,
    width: customStyle?.width ? `${customStyle.width}px` : `${defaultStyle.size.width}px`,
    height: customStyle?.height ? `${customStyle.height}px` : `${defaultStyle.size.height}px`,
    borderRadius: customStyle?.borderRadius || defaultStyle.borderRadius,
    fontSize: customStyle?.fontSize || defaultStyle.fontSize,
    fontWeight: customStyle?.fontWeight || defaultStyle.fontWeight
  }
})

/** 状态图标 */
const statusIcon = computed(() => {
  switch (props.data.status) {
    case 'completed':
      return CircleCheck
    case 'in-progress':
      return Loading
    case 'not-started':
      return WarningFilled
    case 'blocked':
      return WarningFilled
    default:
      return null
  }
})

/** 优先级颜色 */
const priorityColor = computed(() => {
  switch (props.data.priority) {
    case 'urgent':
      return '#ff4d4f'
    case 'high':
      return '#fa8c16'
    case 'medium':
      return '#1890ff'
    case 'low':
      return '#52c41a'
    default:
      return '#8c8c8c'
  }
})

/** 进度百分比 */
const progressPercent = computed(() => {
  return Math.max(0, Math.min(100, props.data.progress || 0))
})

/** 是否显示进度条 */
const showProgress = computed(() => {
  return props.data.progress !== undefined && 
         props.data.progress > 0 && 
         props.data.status === 'in-progress'
})

/** 节点标题（截断长文本） */
const displayLabel = computed(() => {
  const label = props.data.label || '未命名节点'
  return label.length > 12 ? `${label.substring(0, 12)}...` : label
})

/** 是否为主要节点（主任务或子任务） */
const isPrimaryNode = computed(() => {
  return props.type === 'main-task' || props.type === 'sub-task'
})

// ==================== 事件处理 ====================

/**
 * 开始编辑标签
 */
const startEditLabel = () => {
  if (props.data.status === 'completed') return // 已完成的任务不可编辑

  isEditingLabel.value = true
  editingLabel.value = props.data.label

  // 下一帧聚焦到输入框
  nextTick(() => {
    const input = document.querySelector('.task-node__label-input') as HTMLInputElement
    if (input) {
      input.focus()
      input.select()
    }
  })
}

/**
 * 完成标签编辑
 */
const finishEditLabel = () => {
  if (editingLabel.value.trim() && editingLabel.value !== props.data.label) {
    // 通过 store 更新节点数据
    const success = graphStore.updateNode(props.id, {
      data: {
        ...props.data,
        label: editingLabel.value.trim()
      }
    })

    if (success) {
      ElMessage.success('节点标签更新成功')
    } else {
      ElMessage.error('节点标签更新失败')
    }
  }

  isEditingLabel.value = false
  editingLabel.value = ''
}

/**
 * 取消编辑
 */
const cancelEdit = () => {
  isEditingLabel.value = false
  editingLabel.value = ''
}

/**
 * 处理键盘事件
 */
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    finishEditLabel()
  } else if (event.key === 'Escape') {
    cancelEdit()
  }
}

/**
 * 处理节点点击
 */
const handleNodeClick = (event: MouseEvent) => {
  // 阻止事件冒泡到画布
  event.stopPropagation()

  // 获取节点ID
  const nodeId = props.id

  if (!nodeId) {
    console.error('无法获取节点ID:', props)
    return
  }

  // 更新store中的选择状态
  if (event.ctrlKey || event.metaKey) {
    // 多选模式
    graphStore.toggleElementSelection(nodeId)
  } else {
    // 单选模式
    graphStore.clearSelection()
    graphStore.selectElement(nodeId)
  }
}

/**
 * 处理双击编辑
 */
const handleDoubleClick = (event: MouseEvent) => {
  event.stopPropagation()
  startEditLabel()
}
</script>

<template>
  <div 
    class="task-node"
    :class="[
      `task-node--${type}`,
      `task-node--${data.status}`,
      {
        'task-node--selected': selected,
        'task-node--primary': isPrimaryNode,
        'task-node--editing': isEditingLabel
      }
    ]"
    :style="nodeStyle"
    @click="handleNodeClick"
    @dblclick="handleDoubleClick"
  >
    <!-- 连接点 -->
    <Handle
      type="target"
      :position="Position.Top"
      class="node-handle node-handle--target"
    />
    <Handle
      type="source"
      :position="Position.Bottom"
      class="node-handle node-handle--source"
    />
    
    <!-- 节点内容 -->
    <div class="task-node__content">
      <!-- 优先级指示器 -->
      <div 
        v-if="data.priority && data.priority !== 'medium'"
        class="task-node__priority"
        :style="{ backgroundColor: priorityColor }"
      >
        <el-icon><Flag /></el-icon>
      </div>
      
      <!-- 状态图标 -->
      <div 
        v-if="statusIcon"
        class="task-node__status"
      >
        <el-icon :class="{ 'rotating': data.status === 'in-progress' }">
          <component :is="statusIcon" />
        </el-icon>
      </div>
      
      <!-- 节点标签 -->
      <div class="task-node__label">
        <input
          v-if="isEditingLabel"
          v-model="editingLabel"
          class="task-node__label-input"
          @blur="finishEditLabel"
          @keydown="handleKeydown"
          maxlength="50"
        />
        <span 
          v-else
          class="task-node__label-text"
          :title="data.label"
        >
          {{ displayLabel }}
        </span>
      </div>
      
      <!-- 负责人信息 -->
      <div 
        v-if="data.assignee && isPrimaryNode"
        class="task-node__assignee"
      >
        <el-icon><User /></el-icon>
        <span>{{ data.assignee }}</span>
      </div>
      
      <!-- 进度条 -->
      <div 
        v-if="showProgress"
        class="task-node__progress"
      >
        <div 
          class="task-node__progress-bar"
          :style="{ width: `${progressPercent}%` }"
        />
        <span class="task-node__progress-text">{{ progressPercent }}%</span>
      </div>
      
      <!-- 截止时间 -->
      <div 
        v-if="data.dueDate && isPrimaryNode"
        class="task-node__due-date"
      >
        <el-icon><Clock /></el-icon>
        <span>{{ new Date(data.dueDate).toLocaleDateString() }}</span>
      </div>
      
      <!-- 标签 -->
      <div 
        v-if="data.tags && data.tags.length > 0"
        class="task-node__tags"
      >
        <span 
          v-for="tag in data.tags.slice(0, 2)" 
          :key="tag"
          class="task-node__tag"
        >
          {{ tag }}
        </span>
        <span 
          v-if="data.tags.length > 2"
          class="task-node__tag task-node__tag--more"
        >
          +{{ data.tags.length - 2 }}
        </span>
      </div>
    </div>
    
    <!-- 选中指示器 -->
    <div 
      v-if="selected"
      class="task-node__selection-indicator"
    />
  </div>
</template>

<style lang="scss" scoped>
.task-node {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
  
  &--selected {
    box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.3);
  }
  
  &--editing {
    box-shadow: 0 0 0 3px rgba(82, 196, 26, 0.3);
  }
  
  &__content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px;
    text-align: center;
  }
  
  &__priority {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    z-index: 10;
  }
  
  &__status {
    position: absolute;
    top: 4px;
    left: 4px;
    font-size: 14px;
    opacity: 0.8;
    
    .rotating {
      animation: rotate 2s linear infinite;
    }
  }
  
  &__label {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 20px;
    
    &-text {
      line-height: 1.2;
      word-break: break-word;
    }
    
    &-input {
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      padding: 2px 4px;
      font-size: inherit;
      color: #333;
      text-align: center;
      width: 100%;
      outline: none;
      
      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }
  
  &__assignee {
    display: flex;
    align-items: center;
    gap: 2px;
    font-size: 10px;
    margin-top: 2px;
    opacity: 0.8;
    
    span {
      max-width: 60px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  
  &__progress {
    position: relative;
    width: 80%;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin-top: 4px;
    
    &-bar {
      height: 100%;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 2px;
      transition: width 0.3s ease;
    }
    
    &-text {
      position: absolute;
      top: -16px;
      right: 0;
      font-size: 8px;
      opacity: 0.8;
    }
  }
  
  &__due-date {
    display: flex;
    align-items: center;
    gap: 2px;
    font-size: 8px;
    margin-top: 2px;
    opacity: 0.7;
  }
  
  &__tags {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
    margin-top: 4px;
    justify-content: center;
  }
  
  &__tag {
    background: rgba(255, 255, 255, 0.2);
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 8px;
    max-width: 30px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    
    &--more {
      background: rgba(255, 255, 255, 0.3);
      font-weight: bold;
    }
  }
  
  &__selection-indicator {
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid #1890ff;
    border-radius: inherit;
    pointer-events: none;
    animation: pulse 2s infinite;
  }
}

// 节点连接点样式
.node-handle {
  width: 8px;
  height: 8px;
  border: 2px solid #fff;
  background: #1890ff;
  
  &--target {
    top: -4px;
  }
  
  &--source {
    bottom: -4px;
  }
  
  &:hover {
    background: #40a9ff;
    transform: scale(1.2);
  }
}

// 动画
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 不同节点类型的特殊样式
.task-node--main-task {
  font-weight: bold;
  
  .task-node__label-text {
    font-size: 14px;
  }
}

.task-node--explanation {
  .task-node__content {
    padding: 4px;
  }
  
  .task-node__label-text {
    font-size: 11px;
  }
}

.task-node--completed {
  .task-node__content {
    opacity: 0.8;
  }
  
  .task-node__label-text {
    text-decoration: line-through;
  }
}
</style>
