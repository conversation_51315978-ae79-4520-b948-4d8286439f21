<!-- 图谱画布组件 -->
<script setup lang="ts" name="GraphCanvas">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { VueFlow, useVueFlow } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'
import { MiniMap } from '@vue-flow/minimap'
import type { Node, Edge, Connection, ViewportTransform } from '@vue-flow/core'
import { useGraphStore } from '../stores/useGraphStore'
import HoverTooltip from './HoverTooltip.vue'
import type { TaskGraphNode, TaskGraphEdge } from '@/define/taskGraph.define'
import { useGraphOperations } from '../composables/useGraphOperations'
import TaskNode from './TaskNode.vue'
// import CustomEdge from './CustomEdge.vue'

interface Props {
  /** 是否只读模式 */
  readonly?: boolean
  /** 是否显示网格背景 */
  showBackground?: boolean
  /** 是否显示控制面板 */
  showControls?: boolean
  /** 是否显示小地图 */
  showMiniMap?: boolean
  /** 画布高度 */
  height?: string
  /** 是否显示悬停信息 */
  showHoverInfo?: boolean
  /** 选择时是否高亮 */
  highlightOnSelect?: boolean
  /** 是否显示节点标签 */
  showNodeLabels?: boolean
  /** 是否显示边标签 */
  showEdgeLabels?: boolean
  /** 布局模式 */
  layoutMode?: string
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  showBackground: true,
  showControls: true,
  showMiniMap: true,
  height: '100%',
  showHoverInfo: true,
  highlightOnSelect: true,
  showNodeLabels: true,
  showEdgeLabels: true,
  layoutMode: 'manual'
})

const emit = defineEmits<{
  nodeClick: [node: Node]
  nodeDoubleClick: [node: Node]
  edgeClick: [edge: Edge]
  canvasClick: [event: MouseEvent]
  connect: [connection: Connection]
  nodeContextMenu: [event: MouseEvent, node: Node]
  edgeContextMenu: [event: MouseEvent, edge: Edge]
}>()

// ==================== 状态管理 ====================

const graphStore = useGraphStore()
const graphOperations = useGraphOperations()

// Vue Flow实例引用
const vueFlowRef = ref()
const { onConnect, onNodeDragStop, onViewportChange, updateNode } = useVueFlow()

// ==================== 响应式数据 ====================

/** 画布是否被锁定 - 修复版本 */
const isLocked = computed(() => {
  return props.readonly || props.layoutMode === 'locked'
})

/** 节点是否可拖拽 - 修复版本 */
const nodesDraggable = computed(() => {
  const draggable = !isLocked.value
  console.log('nodesDraggable 计算:', {
    layoutMode: props.layoutMode,
    readonly: props.readonly,
    isLocked: isLocked.value,
    draggable
  })
  return draggable
})

/** 节点是否可连接 - 修复版本 */
const nodesConnectable = computed(() => {
  return !isLocked.value
})

/** 元素是否可选择 - 修复版本 */
const elementsSelectable = computed(() => {
  return !isLocked.value
})

/** Vue Flow 强制更新 key */
const vueFlowKey = ref(0)

/** 节点数据 - 动态设置 draggable 属性 */
const nodes = computed(() => {
  return graphStore.nodes.map(node => ({
    ...node,
    draggable: nodesDraggable.value,
    selectable: elementsSelectable.value,
    connectable: nodesConnectable.value
  }))
})

/** 连线数据 */
const edges = computed(() => graphStore.edges as any[])

/** 选中的元素 */
const selectedElements = computed(() => graphStore.selectedElements)

/** 节点样式类 */
const getNodeClass = (node: any) => {
  const classes = ['custom-node']

  // 添加选中状态类
  if (props.highlightOnSelect && graphStore.selectedElements.includes(node.id)) {
    classes.push('selected')
  }

  // 添加节点类型类
  if (node.data?.status) {
    classes.push(`status-${node.data.status}`)
  }

  return classes
}

/** 边样式类 */
const getEdgeClass = (edge: any) => {
  const classes = ['custom-edge']

  // 添加选中状态类
  if (props.highlightOnSelect && graphStore.selectedElements.includes(edge.id)) {
    classes.push('selected')
  }

  return classes
}

// ==================== 悬停提示状态 ====================

/** 悬停提示是否显示 */
const showTooltip = ref(false)

/** 悬停提示位置 */
const tooltipPosition = ref({ x: 0, y: 0 })

/** 悬停的节点 */
const hoveredNode = ref<TaskGraphNode | null>(null)

/** 悬停的边 */
const hoveredEdge = ref<TaskGraphEdge | null>(null)

// ==================== 样式方法 ====================

/**
 * 获取节点颜色
 */
const getNodeColor = (node: any) => {
  const nodeType = node.data?.type || 'default'

  // 根据原型图的配色方案
  const colorMap: Record<string, string> = {
    'main-task': '#1890ff',      // 蓝色大圆圈
    'sub-task': '#1890ff',       // 蓝色大圆圈
    'milestone': '#13c2c2',      // 青色小圆圈
    'note': '#13c2c2',           // 青色小圆圈
    'decision': '#13c2c2',       // 青色小圆圈
    'default': '#13c2c2'         // 默认青色
  }

  return colorMap[nodeType] || '#13c2c2'
}

/**
 * 创建响应式的节点样式计算函数
 */
const createNodeStyleComputed = (data: any) => {
  return computed(() => getNodeStyle(data))
}

/**
 * 获取节点样式 - 确保每次返回新的对象引用以触发Vue响应式更新
 */
const getNodeStyle = (data: any) => {
  // 获取原始节点类型（用于确定默认样式）
  const originalType = data?.originalType || data?.type || 'default'
  const isMainNode = originalType === 'main-task'
  const isSubNode = originalType === 'sub-task'

  // 获取自定义样式（如果存在）
  const customStyle = data?.metadata?.customStyle

  // 调试日志 - 确认函数被调用
  console.log('🎨 getNodeStyle 被调用:', {
    nodeId: data?.id,
    nodeLabel: data?.label,
    originalType,
    isMainNode,
    isSubNode,
    customStyle,
    dataColor: data?.color,
    hasMetadata: !!data?.metadata
  })

  // 根据节点类型确定默认尺寸
  let defaultWidth = '80px'
  let defaultHeight = '80px'
  let defaultFontSize = '12px'

  if (isMainNode) {
    defaultWidth = '120px'
    defaultHeight = '120px'
    defaultFontSize = '14px'
  } else if (isSubNode) {
    defaultWidth = '100px'
    defaultHeight = '100px'
    defaultFontSize = '13px'
  }

  // 创建一个唯一的样式标识符，用于强制重新渲染
  const styleVersion = customStyle ? JSON.stringify(customStyle) : 'default'

  // 每次都返回全新的样式对象，确保Vue能检测到变化
  const computedStyle = {
    // 基础样式
    backgroundColor: customStyle?.backgroundColor || data?.color || getNodeColor({ data }),
    width: customStyle?.width ? `${customStyle.width}px` : defaultWidth,
    height: customStyle?.height ? `${customStyle.height}px` : defaultHeight,
    borderRadius: customStyle?.borderRadius || '50%',
    color: customStyle?.textColor || 'white',
    fontSize: customStyle?.fontSize || defaultFontSize,
    fontWeight: customStyle?.fontWeight || (isMainNode ? '600' : '500'),

    // 边框样式
    border: `3px solid ${customStyle?.borderColor || 'rgba(255, 255, 255, 0.8)'}`,
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',

    // 添加过渡效果
    transition: 'all 0.3s ease',

    // 确保是圆形
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',

    // 添加样式版本标识（用于调试）
    '--style-version': styleVersion
  }

  console.log('🎨 getNodeStyle 返回样式:', computedStyle)
  return computedStyle
}

// ==================== 监听器修复 ====================

// 监听节点数据变化，强制重新渲染样式
watch(() => graphStore.nodes, (newNodes, oldNodes) => {
  // 检查是否有节点的样式数据发生变化
  if (newNodes && oldNodes && newNodes.length === oldNodes.length) {
    const hasStyleChanges = newNodes.some((newNode, index) => {
      const oldNode = oldNodes[index]
      if (!oldNode || newNode.id !== oldNode.id) return true

      const newStyle = JSON.stringify(newNode.data?.metadata?.customStyle)
      const oldStyle = JSON.stringify(oldNode.data?.metadata?.customStyle)
      return newStyle !== oldStyle
    })

    if (hasStyleChanges) {
      console.log('检测到节点样式变化，强制重新渲染')
      // 强制触发重新渲染
      nextTick(() => {
        vueFlowKey.value++
      })
    }
  }
}, { deep: true })

// 监听布局模式变化 - 修复版本
watch(() => props.layoutMode, (newMode, oldMode) => {
  console.log('GraphCanvas: 布局模式从', oldMode, '切换到', newMode)

  // 根据布局模式调整画布行为
  if (newMode === 'locked') {
    // 锁定模式：清除选择状态，禁用交互
    graphStore.clearSelection()
    console.log('画布交互已锁定')

    // 强制更新 Vue Flow 组件
    vueFlowKey.value++
    console.log('强制更新 Vue Flow 组件，key:', vueFlowKey.value)
  } else if (newMode === 'auto' && oldMode !== 'auto') {
    // 切换到自动布局模式时，自动适应视图
    nextTick(() => {
      fitView()
    })
  } else if (newMode === 'manual') {
    console.log('画布交互已解锁')

    // 强制更新 Vue Flow 组件
    vueFlowKey.value++
    console.log('强制更新 Vue Flow 组件，key:', vueFlowKey.value)
  }
})

// 监听高亮设置变化 - 修复版本
watch(() => props.highlightOnSelect, (enabled) => {
  console.log('GraphCanvas: 高亮设置变更为', enabled)
  if (!enabled) {
    // 清除当前高亮效果
    graphStore.clearSelection()
  }
})

// ==================== 事件处理 ====================

/**
 * 处理节点点击
 */
const handleNodeClick = (event: any) => {
  if (isLocked.value) return

  const { node } = event
  if (!node) return

  // 如果正在连线模式
  if (graphOperations.isConnecting.value) {
    graphOperations.completeConnection(node.id)
    return
  }

  // 处理多选
  if (event.event?.ctrlKey || event.event?.metaKey) {
    graphStore.toggleElementSelection(node.id)
  } else {
    graphStore.clearSelection()
    graphStore.selectElement(node.id)
  }

  // 调试日志
  console.log('节点点击后的选择状态:', {
    selectedElements: graphStore.selectedElements,
    selectedNodes: graphStore.selectedNodes,
    selectedEdges: graphStore.selectedEdges
  })

  emit('nodeClick', node)
}

/**
 * 处理节点双击
 */
const handleNodeDoubleClick = (event: any) => {
  if (isLocked.value) return

  const { node } = event
  if (!node) return

  // 启用编辑功能
  graphOperations.editNode(node.id)
  emit('nodeDoubleClick', node)
}

/**
 * 处理连线点击
 */
const handleEdgeClick = (event: any) => {
  if (isLocked.value) return

  const { edge } = event
  if (!edge) return

  // 处理多选
  if (event.event?.ctrlKey || event.event?.metaKey) {
    graphStore.toggleElementSelection(edge.id)
  } else {
    graphStore.clearSelection()
    graphStore.selectElement(edge.id)
  }

  emit('edgeClick', edge)
}

/**
 * 处理画布点击
 */
const handleCanvasClick = (event: MouseEvent) => {
  // 取消连线模式（暂时注释）
  // if (graphOperations.isConnecting.value) {
  //   graphOperations.cancelConnection()
  //   return
  // }
  
  // 清空选择
  if (!event.ctrlKey && !event.metaKey) {
    graphStore.clearSelection()
  }
  
  emit('canvasClick', event)
}

/**
 * 处理节点拖拽结束 - 修复版本
 */
const handleNodeDragStop = (event: any) => {
  if (isLocked.value) return

  // 从事件中获取节点信息
  const { node } = event
  if (node) {
    console.log('节点拖拽结束:', node.id, node.position)

    // 使用 Vue Flow 的 updateNode API 更新节点位置
    updateNode(node.id, { position: node.position })

    // 同时更新 store 中的数据
    graphStore.moveNode(node.id, node.position)

    console.log('节点位置已更新')
  }
}

/**
 * 处理连线创建
 */
const handleConnect = (connection: Connection) => {
  if (isLocked.value) return
  
  if (connection.source && connection.target) {
    // graphOperations.completeConnection(connection.target) // 暂时注释
    console.log('连线创建:', connection)
  }
  
  emit('connect', connection)
}

/**
 * 处理视口变化
 */
const handleViewportChange = (viewport: ViewportTransform) => {
  graphStore.updateViewport({
    x: viewport.x,
    y: viewport.y,
    zoom: viewport.zoom
  })
}

/**
 * 处理节点变化
 */
const handleNodesChange = (changes: any[]) => {
  console.log('节点变化:', changes)

  // 查找选择状态变化
  const selectionChanges = changes.filter(change => change.type === 'select')

  if (selectionChanges.length > 0) {
    // 清除之前的选择
    graphStore.clearSelection()

    // 添加新选中的节点
    selectionChanges.forEach(change => {
      if (change.selected) {
        graphStore.selectElement(change.id)
        console.log('选中节点:', change.id)
      }
    })
  }

  // 如果没有选择变化，但是有节点被选中（通过点击事件）
  if (selectionChanges.length === 0) {
    // 获取当前选中的节点
    const selectedNodes = document.querySelectorAll('.vue-flow__node.selected, .vue-flow__node.active')

    if (selectedNodes.length > 0) {
      // 清除之前的选择
      graphStore.clearSelection()

      // 添加新选中的节点
      selectedNodes.forEach((node: any) => {
        const nodeId = node.getAttribute('data-id')
        if (nodeId) {
          graphStore.selectElement(nodeId)
          console.log('选中节点(DOM):', nodeId)
        }
      })
    }
  }
}

/**
 * 处理边变化
 */
const handleEdgesChange = (changes: any[]) => {
  console.log('边变化:', changes)

  // 查找选择状态变化
  const selectionChanges = changes.filter(change => change.type === 'select')

  if (selectionChanges.length > 0) {
    // 添加新选中的边
    selectionChanges.forEach(change => {
      if (change.selected) {
        graphStore.selectElement(change.id)
        console.log('选中边:', change.id)
      }
    })
  }
}

/**
 * 处理节点右键菜单
 */
const handleNodeContextMenu = (event: any) => {
  event.event?.preventDefault()
  emit('nodeContextMenu', event.event, event.node)
}

/**
 * 处理连线右键菜单
 */
const handleEdgeContextMenu = (event: any) => {
  event.event?.preventDefault()
  emit('edgeContextMenu', event.event, event.edge)
}

/**
 * 处理节点鼠标进入
 */
const handleNodeMouseEnter = (event: any) => {
  if (!props.showHoverInfo) return

  hoveredNode.value = event.node as TaskGraphNode
  hoveredEdge.value = null

  // 获取鼠标位置
  if (event.event) {
    tooltipPosition.value = {
      x: event.event.clientX,
      y: event.event.clientY
    }
  }

  showTooltip.value = true
}

/**
 * 处理节点鼠标离开
 */
const handleNodeMouseLeave = (event: any) => {
  showTooltip.value = false
  hoveredNode.value = null
}

/**
 * 处理连线鼠标进入
 */
const handleEdgeMouseEnter = (event: any) => {
  if (!props.showHoverInfo) return

  hoveredEdge.value = event.edge as TaskGraphEdge
  hoveredNode.value = null

  // 获取鼠标位置
  if (event.event) {
    tooltipPosition.value = {
      x: event.event.clientX,
      y: event.event.clientY
    }
  }

  showTooltip.value = true
}

/**
 * 处理连线鼠标离开
 */
const handleEdgeMouseLeave = (event: any) => {
  showTooltip.value = false
  hoveredEdge.value = null
}

/**
 * 处理键盘事件
 */
const handleKeyDown = (event: KeyboardEvent) => {
  if (isLocked.value) return
  
  switch (event.key) {
    case 'Delete':
    case 'Backspace':
      if (graphStore.selectedElements.length > 0) {
        // graphOperations.deleteSelection() // 暂时注释
        console.log('删除选中元素:', graphStore.selectedElements)
      }
      break
      
    case 'z':
      if (event.ctrlKey || event.metaKey) {
        if (event.shiftKey) {
          graphStore.redo()
        } else {
          graphStore.undo()
        }
        event.preventDefault()
      }
      break
      
    case 'a':
      if (event.ctrlKey || event.metaKey) {
        // 全选
        const allElementIds = [
          ...nodes.value.map(n => n.id),
          ...graphStore.edges.map((e: any) => e.id)
        ]
        graphStore.selectMultipleElements(allElementIds)
        event.preventDefault()
      }
      break
      
    case 'Escape':
      // if (graphOperations.isConnecting.value) {
      //   graphOperations.cancelConnection()
      // } else {
        graphStore.clearSelection()
      // }
      break
  }
}

// ==================== 生命周期 ====================

onMounted(() => {
  // 监听键盘事件
  document.addEventListener('keydown', handleKeyDown)
  
  // 设置Vue Flow事件监听
  onConnect(handleConnect)
  onNodeDragStop(handleNodeDragStop)
  onViewportChange(handleViewportChange)
})

// 监听节点和边的变化，更新Vue Flow
watch([nodes, edges], () => {
  if (vueFlowRef.value) {
    vueFlowRef.value.fitView({ duration: 300 })
  }
}, { deep: true })

// 重复的监听器已删除，使用上面的版本

// ==================== 公开方法 ====================

/**
 * 适应视图
 */
const fitView = () => {
  if (vueFlowRef.value) {
    vueFlowRef.value.fitView({ duration: 500 })
  }
}

/**
 * 缩放到指定比例
 */
const zoomTo = (zoom: number) => {
  if (vueFlowRef.value) {
    vueFlowRef.value.zoomTo(zoom)
  }
}

/**
 * 重置视图
 */
const resetView = () => {
  if (vueFlowRef.value) {
    vueFlowRef.value.setViewport({ x: 0, y: 0, zoom: 1 })
  }
}

// 暴露方法给父组件
defineExpose({
  fitView,
  zoomTo,
  resetView
})
</script>

<template>
  <div 
    class="graph-canvas" 
    :style="{ height }"
    :class="{
      'readonly': readonly,
      'connecting': graphOperations.isConnecting.value
    }"
  >
    <VueFlow
      ref="vueFlowRef"
      :key="vueFlowKey"
      :nodes="nodes"
      :edges="edges"
      :selected-nodes="selectedElements"
      :selected-edges="selectedElements"
      :nodes-draggable="nodesDraggable"
      :nodes-connectable="nodesConnectable"
      :elements-selectable="elementsSelectable"
      :zoom-on-scroll="true"
      :pan-on-scroll="true"
      :zoom-on-pinch="true"
      :pan-on-drag="true"
      :default-zoom="1"
      :min-zoom="0.1"
      :max-zoom="4"
      :fit-view-on-init="true"
      @node-click="handleNodeClick"
      @node-double-click="handleNodeDoubleClick"
      @edge-click="handleEdgeClick"
      @pane-click="handleCanvasClick"
      @node-context-menu="handleNodeContextMenu"
      @edge-context-menu="handleEdgeContextMenu"
      @node-mouse-enter="handleNodeMouseEnter"
      @node-mouse-leave="handleNodeMouseLeave"
      @edge-mouse-enter="handleEdgeMouseEnter"
      @edge-mouse-leave="handleEdgeMouseLeave"
      @nodes-change="handleNodesChange"
      @edges-change="handleEdgesChange"
      :class="[
        'vue-flow-container',
        {
          'hide-node-labels': !props.showNodeLabels,
          'hide-edge-labels': !props.showEdgeLabels,
          'highlight-disabled': !props.highlightOnSelect
        }
      ]"
    >
      <!-- 自定义节点和连线模板 -->

      <!-- 背景网格 -->
      <Background v-if="showBackground" pattern-color="#e4e7ed" :gap="20" />

      <!-- 控制面板 -->
      <Controls v-if="showControls" />

      <!-- 小地图 -->
      <MiniMap
        v-if="showMiniMap"
        :node-color="getNodeColor"
        :mask-color="'rgba(240, 242, 247, 0.7)'"
        position="bottom-right"
      />

      <!-- 自定义节点模板 - 使用TaskNode组件 -->
      <template #node-default="nodeProps">
        <TaskNode
          v-bind="nodeProps"
          :type="nodeProps.data.originalType || 'default'"
        />
      </template>

      <!-- 自定义边模板 - 暂时使用默认样式 -->
      <!-- <template #edge-default="edgeProps">
        <CustomEdge v-bind="edgeProps" />
      </template> -->
    </VueFlow>

    <!-- 悬停提示 -->
    <HoverTooltip
      :visible="showTooltip"
      :position="tooltipPosition"
      :node="hoveredNode"
      :edge="hoveredEdge"
    />

    <!-- 连线提示 -->
    <!-- <div
      v-if="graphOperations.isConnecting.value"
      class="connection-hint"
    >
      <span>点击目标节点完成连线，按ESC取消</span>
    </div> -->
  </div>
</template>

<style lang="scss" scoped>
/* 导入Vue Flow样式 */
@import '@vue-flow/core/dist/style.css';
@import '@vue-flow/core/dist/theme-default.css';

/* 显示控制样式 */
.vue-flow-container {
  &.hide-node-labels {
    .vue-flow__node-default .node-content {
      display: none;
    }

    .custom-node .node-content {
      display: none;
    }
  }

  &.hide-edge-labels {
    .vue-flow__edge-label,
    .edge-label {
      display: none !important;
    }
  }

  &.highlight-disabled {
    .vue-flow__node.selected {
      box-shadow: none !important;
      border: none !important;
    }

    .vue-flow__edge.selected {
      stroke: inherit !important;
      stroke-width: inherit !important;
    }
  }
}

/* 自定义节点样式 */
.custom-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2) !important;
  }

  &.node-selected {
    transform: scale(1.1);
    box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.3) !important;
  }

  .node-number {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    background: #ff4d4f;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    border: 2px solid white;
  }

  .node-content {
    text-align: center;
    padding: 8px;

    .node-title {
      font-weight: 500;
      line-height: 1.2;
      margin-bottom: 4px;
      word-break: break-word;
    }

    .node-description {
      font-size: 10px;
      opacity: 0.8;
      line-height: 1.2;
      word-break: break-word;
    }
  }

  .node-status {
    position: absolute;
    bottom: -4px;
    right: -4px;

    .status-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      border: 2px solid white;
    }

    &.status-completed .status-dot {
      background: #52c41a;
    }

    &.status-in-progress .status-dot {
      background: #faad14;
    }

    &.status-pending .status-dot {
      background: #d9d9d9;
    }

    &.status-blocked .status-dot {
      background: #ff4d4f;
    }
  }

  // 不同类型节点的特殊样式
  &.node-type-main-task {
    .node-content .node-title {
      font-size: 14px;
      font-weight: 600;
    }
  }

  &.node-type-sub-task {
    .node-content .node-title {
      font-size: 13px;
      font-weight: 500;
    }
  }

  &.node-type-milestone,
  &.node-type-note,
  &.node-type-decision {
    .node-content .node-title {
      font-size: 11px;
      font-weight: 500;
    }
  }
}

.graph-canvas {
  position: relative;
  width: 100%;
  background: #f8f9fa;
  
  &.readonly {
    .vue-flow-container {
      cursor: default;
    }
  }
  
  &.connecting {
    .vue-flow-container {
      cursor: crosshair;
    }
  }
  
  .vue-flow-container {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .connection-hint {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(24, 144, 255, 0.9);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 1000;
    pointer-events: none;
    
    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 6px solid transparent;
      border-top-color: rgba(24, 144, 255, 0.9);
    }
  }
}

/* Vue Flow自定义样式 */
:deep(.vue-flow__node) {
  cursor: pointer;
  transition: all 0.2s ease;

  /* 去掉默认的矩形边框和背景 */
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;

  &:hover {
    transform: scale(1.05);
  }

  &.selected {
    /* 选中状态也不显示边框，让自定义节点处理 */
    box-shadow: none !important;
  }
}

/* 确保默认节点样式被覆盖 */
:deep(.vue-flow__node-default) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

:deep(.vue-flow__edge) {
  cursor: pointer;
  
  &.selected {
    .vue-flow__edge-path {
      stroke: #1890ff;
      stroke-width: 3;
    }
  }
}

:deep(.vue-flow__controls) {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.vue-flow__minimap) {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
