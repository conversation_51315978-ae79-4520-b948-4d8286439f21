<!-- 图谱画布组件 -->
<script setup lang="ts" name="GraphCanvas">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { VueFlow, useVueFlow } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'
import { MiniMap } from '@vue-flow/minimap'
import type { Node, Edge, Connection, ViewportTransform } from '@vue-flow/core'
import { useGraphStore } from '../stores/useGraphStore'
import HoverTooltip from './HoverTooltip.vue'
import type { TaskGraphNode, TaskGraphEdge } from '@/define/taskGraph.define'
// import { useGraphOperations } from '../composables/useGraphOperations' // 暂时注释
// import TaskNode from './TaskNode.vue'
// import CustomEdge from './CustomEdge.vue'

interface Props {
  /** 是否只读模式 */
  readonly?: boolean
  /** 是否显示网格背景 */
  showBackground?: boolean
  /** 是否显示控制面板 */
  showControls?: boolean
  /** 是否显示小地图 */
  showMiniMap?: boolean
  /** 画布高度 */
  height?: string
  /** 是否显示悬停信息 */
  showHoverInfo?: boolean
  /** 选择时是否高亮 */
  highlightOnSelect?: boolean
  /** 是否显示节点标签 */
  showNodeLabels?: boolean
  /** 是否显示边标签 */
  showEdgeLabels?: boolean
  /** 布局模式 */
  layoutMode?: string
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  showBackground: true,
  showControls: true,
  showMiniMap: true,
  height: '100%',
  showHoverInfo: true,
  highlightOnSelect: true,
  showNodeLabels: true,
  showEdgeLabels: true,
  layoutMode: 'manual'
})

const emit = defineEmits<{
  nodeClick: [node: Node]
  nodeDoubleClick: [node: Node]
  edgeClick: [edge: Edge]
  canvasClick: [event: MouseEvent]
  connect: [connection: Connection]
  nodeContextMenu: [event: MouseEvent, node: Node]
  edgeContextMenu: [event: MouseEvent, edge: Edge]
}>()

// ==================== 状态管理 ====================

const graphStore = useGraphStore()
// const graphOperations = useGraphOperations() // 暂时注释

// Vue Flow实例引用
const vueFlowRef = ref()
const { onConnect, onNodeDragStop, onViewportChange } = useVueFlow()

// ==================== 响应式数据 ====================

/** 画布是否被锁定 */
const isLocked = computed(() => props.readonly || props.layoutMode === 'locked')

/** 节点数据 */
const nodes = computed(() => graphStore.nodes)

/** 连线数据 */
const edges = computed(() => graphStore.edges as any[])

/** 选中的元素 */
const selectedElements = computed(() => graphStore.selectedElements)

/** 节点样式类 */
const getNodeClass = (node: any) => {
  const classes = ['custom-node']

  // 添加选中状态类
  if (props.highlightOnSelect && graphStore.selectedElements.includes(node.id)) {
    classes.push('selected')
  }

  // 添加节点类型类
  if (node.data?.status) {
    classes.push(`status-${node.data.status}`)
  }

  return classes
}

/** 边样式类 */
const getEdgeClass = (edge: any) => {
  const classes = ['custom-edge']

  // 添加选中状态类
  if (props.highlightOnSelect && graphStore.selectedElements.includes(edge.id)) {
    classes.push('selected')
  }

  return classes
}

// ==================== 悬停提示状态 ====================

/** 悬停提示是否显示 */
const showTooltip = ref(false)

/** 悬停提示位置 */
const tooltipPosition = ref({ x: 0, y: 0 })

/** 悬停的节点 */
const hoveredNode = ref<TaskGraphNode | null>(null)

/** 悬停的边 */
const hoveredEdge = ref<TaskGraphEdge | null>(null)

// ==================== 样式方法 ====================

/**
 * 获取节点颜色
 */
const getNodeColor = (node: any) => {
  const nodeType = node.data?.type || 'default'

  // 根据原型图的配色方案
  const colorMap: Record<string, string> = {
    'main-task': '#1890ff',      // 蓝色大圆圈
    'sub-task': '#1890ff',       // 蓝色大圆圈
    'milestone': '#13c2c2',      // 青色小圆圈
    'note': '#13c2c2',           // 青色小圆圈
    'decision': '#13c2c2',       // 青色小圆圈
    'default': '#13c2c2'         // 默认青色
  }

  return colorMap[nodeType] || '#13c2c2'
}

/**
 * 获取节点样式
 */
const getNodeStyle = (data: any) => {
  const nodeType = data?.type || 'default'
  const isMainNode = nodeType === 'main-task' || nodeType === 'sub-task'

  return {
    backgroundColor: getNodeColor({ data }),
    width: isMainNode ? '120px' : '80px',
    height: isMainNode ? '120px' : '80px',
    borderRadius: '50%',
    border: '3px solid rgba(255, 255, 255, 0.8)',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    color: 'white',
    fontSize: isMainNode ? '14px' : '12px',
    fontWeight: '500'
  }
}

// ==================== 事件处理 ====================

/**
 * 处理节点点击
 */
const handleNodeClick = (event: any) => {
  if (isLocked.value) return

  const { node } = event
  if (!node) return

  // 如果正在连线模式（暂时注释）
  // if (graphOperations.isConnecting.value) {
  //   graphOperations.completeConnection(node.id)
  //   return
  // }

  // 处理多选
  if (event.event?.ctrlKey || event.event?.metaKey) {
    graphStore.toggleElementSelection(node.id)
  } else {
    graphStore.clearSelection()
    graphStore.selectElement(node.id)
  }

  emit('nodeClick', node)
}

/**
 * 处理节点双击
 */
const handleNodeDoubleClick = (event: any) => {
  if (isLocked.value) return

  const { node } = event
  if (!node) return

  // graphOperations.editNode(node.id) // 暂时注释
  emit('nodeDoubleClick', node)
}

/**
 * 处理连线点击
 */
const handleEdgeClick = (event: any) => {
  if (isLocked.value) return

  const { edge } = event
  if (!edge) return

  // 处理多选
  if (event.event?.ctrlKey || event.event?.metaKey) {
    graphStore.toggleElementSelection(edge.id)
  } else {
    graphStore.clearSelection()
    graphStore.selectElement(edge.id)
  }

  emit('edgeClick', edge)
}

/**
 * 处理画布点击
 */
const handleCanvasClick = (event: MouseEvent) => {
  // 取消连线模式（暂时注释）
  // if (graphOperations.isConnecting.value) {
  //   graphOperations.cancelConnection()
  //   return
  // }
  
  // 清空选择
  if (!event.ctrlKey && !event.metaKey) {
    graphStore.clearSelection()
  }
  
  emit('canvasClick', event)
}

/**
 * 处理节点拖拽结束
 */
const handleNodeDragStop = (event: any) => {
  if (isLocked.value) return

  // 从事件中获取节点信息
  const { node } = event
  if (node) {
    graphStore.moveNode(node.id, node.position)
  }
}

/**
 * 处理连线创建
 */
const handleConnect = (connection: Connection) => {
  if (isLocked.value) return
  
  if (connection.source && connection.target) {
    // graphOperations.completeConnection(connection.target) // 暂时注释
    console.log('连线创建:', connection)
  }
  
  emit('connect', connection)
}

/**
 * 处理视口变化
 */
const handleViewportChange = (viewport: ViewportTransform) => {
  graphStore.updateViewport({
    x: viewport.x,
    y: viewport.y,
    zoom: viewport.zoom
  })
}

/**
 * 处理节点右键菜单
 */
const handleNodeContextMenu = (event: any) => {
  event.event?.preventDefault()
  emit('nodeContextMenu', event.event, event.node)
}

/**
 * 处理连线右键菜单
 */
const handleEdgeContextMenu = (event: any) => {
  event.event?.preventDefault()
  emit('edgeContextMenu', event.event, event.edge)
}

/**
 * 处理节点鼠标进入
 */
const handleNodeMouseEnter = (event: any) => {
  if (!props.showHoverInfo) return

  hoveredNode.value = event.node as TaskGraphNode
  hoveredEdge.value = null

  // 获取鼠标位置
  if (event.event) {
    tooltipPosition.value = {
      x: event.event.clientX,
      y: event.event.clientY
    }
  }

  showTooltip.value = true
}

/**
 * 处理节点鼠标离开
 */
const handleNodeMouseLeave = (event: any) => {
  showTooltip.value = false
  hoveredNode.value = null
}

/**
 * 处理连线鼠标进入
 */
const handleEdgeMouseEnter = (event: any) => {
  if (!props.showHoverInfo) return

  hoveredEdge.value = event.edge as TaskGraphEdge
  hoveredNode.value = null

  // 获取鼠标位置
  if (event.event) {
    tooltipPosition.value = {
      x: event.event.clientX,
      y: event.event.clientY
    }
  }

  showTooltip.value = true
}

/**
 * 处理连线鼠标离开
 */
const handleEdgeMouseLeave = (event: any) => {
  showTooltip.value = false
  hoveredEdge.value = null
}

/**
 * 处理键盘事件
 */
const handleKeyDown = (event: KeyboardEvent) => {
  if (isLocked.value) return
  
  switch (event.key) {
    case 'Delete':
    case 'Backspace':
      if (graphStore.selectedElements.length > 0) {
        // graphOperations.deleteSelection() // 暂时注释
        console.log('删除选中元素:', graphStore.selectedElements)
      }
      break
      
    case 'z':
      if (event.ctrlKey || event.metaKey) {
        if (event.shiftKey) {
          graphStore.redo()
        } else {
          graphStore.undo()
        }
        event.preventDefault()
      }
      break
      
    case 'a':
      if (event.ctrlKey || event.metaKey) {
        // 全选
        const allElementIds = [
          ...nodes.value.map(n => n.id),
          ...graphStore.edges.map((e: any) => e.id)
        ]
        graphStore.selectMultipleElements(allElementIds)
        event.preventDefault()
      }
      break
      
    case 'Escape':
      // if (graphOperations.isConnecting.value) {
      //   graphOperations.cancelConnection()
      // } else {
        graphStore.clearSelection()
      // }
      break
  }
}

// ==================== 生命周期 ====================

onMounted(() => {
  // 监听键盘事件
  document.addEventListener('keydown', handleKeyDown)
  
  // 设置Vue Flow事件监听
  onConnect(handleConnect)
  onNodeDragStop(handleNodeDragStop)
  onViewportChange(handleViewportChange)
})

// 监听节点和边的变化，更新Vue Flow
watch([nodes, edges], () => {
  if (vueFlowRef.value) {
    vueFlowRef.value.fitView({ duration: 300 })
  }
}, { deep: true })

// 监听布局模式变化
watch(() => props.layoutMode, (newMode, oldMode) => {
  console.log('GraphCanvas: 布局模式从', oldMode, '切换到', newMode)

  // 根据布局模式调整画布行为
  if (newMode === 'locked') {
    // 锁定模式：清除选择状态
    graphStore.clearSelection()
  } else if (newMode === 'auto' && oldMode !== 'auto') {
    // 切换到自动布局模式时，自动适应视图
    nextTick(() => {
      fitView()
    })
  }
})

// ==================== 公开方法 ====================

/**
 * 适应视图
 */
const fitView = () => {
  if (vueFlowRef.value) {
    vueFlowRef.value.fitView({ duration: 500 })
  }
}

/**
 * 缩放到指定比例
 */
const zoomTo = (zoom: number) => {
  if (vueFlowRef.value) {
    vueFlowRef.value.zoomTo(zoom)
  }
}

/**
 * 重置视图
 */
const resetView = () => {
  if (vueFlowRef.value) {
    vueFlowRef.value.setViewport({ x: 0, y: 0, zoom: 1 })
  }
}

// 暴露方法给父组件
defineExpose({
  fitView,
  zoomTo,
  resetView
})
</script>

<template>
  <div 
    class="graph-canvas" 
    :style="{ height }"
    :class="{ 
      'readonly': readonly,
      'connecting': false // graphOperations.isConnecting.value // 暂时注释
    }"
  >
    <VueFlow
      ref="vueFlowRef"
      :nodes="nodes"
      :edges="edges"
      :nodes-draggable="!isLocked"
      :nodes-connectable="!isLocked"
      :elements-selectable="!isLocked"
      :zoom-on-scroll="true"
      :pan-on-scroll="true"
      :zoom-on-pinch="true"
      :pan-on-drag="true"
      :default-zoom="1"
      :min-zoom="0.1"
      :max-zoom="4"
      :fit-view-on-init="true"
      @node-click="handleNodeClick"
      @node-double-click="handleNodeDoubleClick"
      @edge-click="handleEdgeClick"
      @pane-click="handleCanvasClick"
      @node-context-menu="handleNodeContextMenu"
      @edge-context-menu="handleEdgeContextMenu"
      @node-mouse-enter="handleNodeMouseEnter"
      @node-mouse-leave="handleNodeMouseLeave"
      @edge-mouse-enter="handleEdgeMouseEnter"
      @edge-mouse-leave="handleEdgeMouseLeave"
      :class="[
        'vue-flow-container',
        {
          'hide-node-labels': !showNodeLabels,
          'hide-edge-labels': !showEdgeLabels,
          'highlight-disabled': !highlightOnSelect
        }
      ]"
    >
      <!-- 自定义节点和连线模板暂时注释，使用默认样式 -->
      
      <!-- 背景网格 -->
      <Background v-if="showBackground" pattern-color="#e4e7ed" :gap="20" />
      
      <!-- 控制面板 -->
      <Controls v-if="showControls" />
      
      <!-- 小地图 -->
      <MiniMap
        v-if="showMiniMap"
        :node-color="getNodeColor"
        :mask-color="'rgba(240, 242, 247, 0.7)'"
        position="bottom-right"
      />

      <!-- 自定义节点模板 -->
      <template #node-default="{ data, id }">
        <div
          class="custom-node"
          :class="getNodeClass({ data, id })"
          :style="getNodeStyle(data)"
        >
          <!-- 节点编号 -->
          <div v-if="data.nodeNumber" class="node-number">
            {{ data.nodeNumber }}
          </div>

          <!-- 节点内容 -->
          <div v-if="showNodeLabels" class="node-content">
            <div class="node-title">{{ data.label || '未命名节点' }}</div>
            <div v-if="data.description" class="node-description">
              {{ data.description }}
            </div>
          </div>

          <!-- 节点状态指示器 -->
          <div v-if="data.status" class="node-status" :class="`status-${data.status}`">
            <div class="status-dot"></div>
          </div>
        </div>
      </template>
    </VueFlow>

    <!-- 悬停提示 -->
    <HoverTooltip
      :visible="showTooltip"
      :position="tooltipPosition"
      :node="hoveredNode"
      :edge="hoveredEdge"
    />

    <!-- 连线提示 -->
    <!-- <div
      v-if="graphOperations.isConnecting.value"
      class="connection-hint"
    >
      <span>点击目标节点完成连线，按ESC取消</span>
    </div> -->
  </div>
</template>

<style lang="scss" scoped>
/* 导入Vue Flow样式 */
@import '@vue-flow/core/dist/style.css';
@import '@vue-flow/core/dist/theme-default.css';

/* 显示控制样式 */
.vue-flow-container {
  &.hide-node-labels {
    .vue-flow__node-default .node-content {
      display: none;
    }

    .custom-node .node-content {
      display: none;
    }
  }

  &.hide-edge-labels {
    .vue-flow__edge-label,
    .edge-label {
      display: none !important;
    }
  }

  &.highlight-disabled {
    .vue-flow__node.selected {
      box-shadow: none !important;
      border: none !important;
    }

    .vue-flow__edge.selected {
      stroke: inherit !important;
      stroke-width: inherit !important;
    }
  }
}

/* 自定义节点样式 */
.custom-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2) !important;
  }

  &.node-selected {
    transform: scale(1.1);
    box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.3) !important;
  }

  .node-number {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    background: #ff4d4f;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    border: 2px solid white;
  }

  .node-content {
    text-align: center;
    padding: 8px;

    .node-title {
      font-weight: 500;
      line-height: 1.2;
      margin-bottom: 4px;
      word-break: break-word;
    }

    .node-description {
      font-size: 10px;
      opacity: 0.8;
      line-height: 1.2;
      word-break: break-word;
    }
  }

  .node-status {
    position: absolute;
    bottom: -4px;
    right: -4px;

    .status-dot {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      border: 2px solid white;
    }

    &.status-completed .status-dot {
      background: #52c41a;
    }

    &.status-in-progress .status-dot {
      background: #faad14;
    }

    &.status-pending .status-dot {
      background: #d9d9d9;
    }

    &.status-blocked .status-dot {
      background: #ff4d4f;
    }
  }

  // 不同类型节点的特殊样式
  &.node-type-main-task {
    .node-content .node-title {
      font-size: 14px;
      font-weight: 600;
    }
  }

  &.node-type-sub-task {
    .node-content .node-title {
      font-size: 13px;
      font-weight: 500;
    }
  }

  &.node-type-milestone,
  &.node-type-note,
  &.node-type-decision {
    .node-content .node-title {
      font-size: 11px;
      font-weight: 500;
    }
  }
}

.graph-canvas {
  position: relative;
  width: 100%;
  background: #f8f9fa;
  
  &.readonly {
    .vue-flow-container {
      cursor: default;
    }
  }
  
  &.connecting {
    .vue-flow-container {
      cursor: crosshair;
    }
  }
  
  .vue-flow-container {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .connection-hint {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(24, 144, 255, 0.9);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 1000;
    pointer-events: none;
    
    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 6px solid transparent;
      border-top-color: rgba(24, 144, 255, 0.9);
    }
  }
}

/* Vue Flow自定义样式 */
:deep(.vue-flow__node) {
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
  
  &.selected {
    box-shadow: 0 0 0 2px #1890ff;
  }
}

:deep(.vue-flow__edge) {
  cursor: pointer;
  
  &.selected {
    .vue-flow__edge-path {
      stroke: #1890ff;
      stroke-width: 3;
    }
  }
}

:deep(.vue-flow__controls) {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.vue-flow__minimap) {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
