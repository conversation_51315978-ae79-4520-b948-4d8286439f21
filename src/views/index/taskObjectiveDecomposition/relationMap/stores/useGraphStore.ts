/**
 * 任务关系图谱状态管理Store
 * 基于Pinia实现图谱数据的状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  GraphData,
  TaskGraphNode,
  TaskGraphEdge,
  Viewport,
  OperationRecord,
  GraphMetadata
} from '@/define/taskGraph.define'
import {
  OperationType,
  TaskNodeType,
  EdgeType
} from '@/define/taskGraph.define'
import { useTaskObjectiveStore } from '@/stores/taskObjectiveStore'

/**
 * 图谱状态管理Store
 */
export const useGraphStore = defineStore('taskGraphStore', () => {
  // ==================== 状态定义 ====================
  
  /** 当前图谱数据 */
  const currentGraph = ref<GraphData | null>(null)
  
  /** 节点列表 */
  const nodes = ref<TaskGraphNode[]>([])
  
  /** 连线列表 */
  const edges = ref<TaskGraphEdge[]>([])
  
  /** 选中的元素ID列表 */
  const selectedElements = ref<string[]>([])
  
  /** 视口状态 */
  const viewport = ref<Viewport>({
    x: 0,
    y: 0,
    zoom: 1
  })
  
  /** 操作历史记录 */
  const operationHistory = ref<OperationRecord[]>([])
  
  /** 当前历史位置索引 */
  const historyIndex = ref(-1)
  
  /** 是否已修改 */
  const isDirty = ref(false)
  
  /** 加载状态 */
  const loading = ref(false)
  
  /** 错误信息 */
  const error = ref<string | null>(null)

  // ==================== 计算属性 ====================
  
  /** 选中的节点 */
  const selectedNodes = computed(() => 
    nodes.value.filter(node => selectedElements.value.includes(node.id))
  )
  
  /** 选中的连线 */
  const selectedEdges = computed(() => 
    edges.value.filter(edge => selectedElements.value.includes(edge.id))
  )
  
  /** 是否可以撤销 */
  const canUndo = computed(() => historyIndex.value >= 0)
  
  /** 是否可以重做 */
  const canRedo = computed(() => historyIndex.value < operationHistory.value.length - 1)
  
  /** 图谱统计信息 */
  const graphStats = computed(() => ({
    nodeCount: nodes.value.length,
    edgeCount: edges.value.length,
    selectedCount: selectedElements.value.length,
    nodesByType: nodes.value.reduce((acc, node) => {
      acc[node.type] = (acc[node.type] || 0) + 1
      return acc
    }, {} as Record<TaskNodeType, number>)
  }))

  // ==================== 基础操作方法 ====================
  
  /**
   * 初始化图谱数据
   */
  const initializeGraph = (taskId: string) => {
    loading.value = true
    error.value = null
    
    try {
      // 基于任务ID创建新的图谱（使用固定ID以便数据持久化）
      const graphId = taskId
      const now = new Date()

      currentGraph.value = {
        id: graphId,
        name: `任务关系图谱 - ${taskId}`,
        description: `任务ID ${taskId} 的关系图谱`,
        nodes: [],
        edges: [],
        viewport: { x: 0, y: 0, zoom: 1 },
        metadata: {
          version: 1,
          creator: 'current-user', // 实际应用中从用户状态获取
          description: '任务关系图谱'
        },
        createdAt: now,
        updatedAt: now
      }
      
      nodes.value = []
      edges.value = []
      selectedElements.value = []
      viewport.value = { x: 0, y: 0, zoom: 1 }
      operationHistory.value = []
      historyIndex.value = -1
      isDirty.value = false

      // 生成演示数据
      generateDemoData(taskId)
      
    } catch (err) {
      error.value = err instanceof Error ? err.message : '初始化图谱失败'
    } finally {
      loading.value = false
    }
  }
  
  /**
   * 生成演示数据
   */
  const generateDemoData = (taskId: string) => {
    // 创建主任务节点
    const mainTask: TaskGraphNode = {
      id: 'main-task-1',
      type: 'default' as any, // 使用 default 类型以匹配 Vue Flow 模板
      position: { x: 400, y: 200 },
      data: {
        label: '政务服务数字化转型项目',
        description: '推进政务服务全流程数字化改造',
        status: 'in-progress' as any,
        priority: 'high' as any,
        assignee: '张主任',
        participants: ['李工程师', '王分析师', '赵设计师'],
        progress: 65,
        dueDate: '2024-12-31',
        tags: ['数字化', '政务服务', '重点项目'],
        color: '#1890ff',
        originalType: TaskNodeType.MAIN_TASK // 保留原始类型信息
      },
      draggable: true,
      selectable: true,
      deletable: false,
      connectable: true
    }

    // 创建子任务节点
    const subTasks: TaskGraphNode[] = [
      {
        id: 'sub-task-1',
        type: 'default' as any, // 使用 default 类型以匹配 Vue Flow 模板
        position: { x: 200, y: 350 },
        data: {
          label: '需求分析',
          description: '收集和分析用户需求',
          status: 'completed' as any,
          priority: 'high' as any,
          assignee: '李工程师',
          progress: 100,
          dueDate: '2024-03-15',
          tags: ['需求', '分析'],
          color: '#52c41a',
          originalType: TaskNodeType.SUB_TASK // 保留原始类型信息
        },
        draggable: true,
        selectable: true,
        deletable: true,
        connectable: true
      },
      {
        id: 'sub-task-2',
        type: 'default' as any, // 使用 default 类型以匹配 Vue Flow 模板
        position: { x: 400, y: 350 },
        data: {
          label: '系统设计',
          description: '设计系统架构和界面',
          status: 'in-progress' as any,
          priority: 'high' as any,
          assignee: '赵设计师',
          progress: 80,
          dueDate: '2024-06-30',
          tags: ['设计', '架构'],
          color: '#fa8c16',
          originalType: TaskNodeType.SUB_TASK // 保留原始类型信息
        },
        draggable: true,
        selectable: true,
        deletable: true,
        connectable: true
      },
      {
        id: 'sub-task-3',
        type: 'default' as any, // 使用 default 类型以匹配 Vue Flow 模板
        position: { x: 600, y: 350 },
        data: {
          label: '开发实现',
          description: '编码实现系统功能',
          status: 'in-progress' as any,
          priority: 'medium' as any,
          assignee: '王分析师',
          progress: 45,
          dueDate: '2024-09-30',
          tags: ['开发', '编码'],
          color: '#13c2c2',
          originalType: TaskNodeType.SUB_TASK // 保留原始类型信息
        },
        draggable: true,
        selectable: true,
        deletable: true,
        connectable: true
      },
      {
        id: 'sub-task-4',
        type: 'default' as any, // 使用 default 类型以匹配 Vue Flow 模板
        position: { x: 300, y: 500 },
        data: {
          label: '测试验收',
          description: '系统测试和用户验收',
          status: 'not-started' as any,
          priority: 'medium' as any,
          assignee: '测试团队',
          progress: 0,
          dueDate: '2024-11-30',
          tags: ['测试', '验收'],
          color: '#f5222d',
          originalType: TaskNodeType.SUB_TASK // 保留原始类型信息
        },
        draggable: true,
        selectable: true,
        deletable: true,
        connectable: true
      },
      {
        id: 'sub-task-5',
        type: 'default' as any, // 使用 default 类型以匹配 Vue Flow 模板
        position: { x: 500, y: 500 },
        data: {
          label: '部署上线',
          description: '系统部署和正式上线',
          status: 'not-started' as any,
          priority: 'high' as any,
          assignee: '运维团队',
          progress: 0,
          dueDate: '2024-12-31',
          tags: ['部署', '上线'],
          color: '#722ed1',
          originalType: TaskNodeType.SUB_TASK // 保留原始类型信息
        },
        draggable: true,
        selectable: true,
        deletable: true,
        connectable: true
      }
    ]

    // 添加节点到图谱
    nodes.value = [mainTask, ...subTasks]

    // 创建连线
    const demoEdges: any[] = [
      {
        id: 'edge-1',
        type: EdgeType.DEPENDENCY,
        source: 'main-task-1',
        target: 'sub-task-1',
        data: {
          label: '项目启动',
          description: '主任务到需求分析的依赖关系',
          animated: false
        },
        animated: false
      },
      {
        id: 'edge-2',
        type: EdgeType.SEQUENCE,
        source: 'sub-task-1',
        target: 'sub-task-2',
        data: {
          label: '顺序执行',
          description: '需求分析完成后开始系统设计',
          animated: true
        },
        animated: true
      },
      {
        id: 'edge-3',
        type: EdgeType.SEQUENCE,
        source: 'sub-task-2',
        target: 'sub-task-3',
        data: {
          label: '设计完成',
          description: '系统设计完成后开始开发',
          animated: true
        },
        animated: true
      },
      {
        id: 'edge-4',
        type: EdgeType.DEPENDENCY,
        source: 'sub-task-3',
        target: 'sub-task-4',
        data: {
          label: '开发完成',
          description: '开发完成后进行测试',
          animated: false
        },
        animated: false
      },
      {
        id: 'edge-5',
        type: EdgeType.SEQUENCE,
        source: 'sub-task-4',
        target: 'sub-task-5',
        data: {
          label: '测试通过',
          description: '测试通过后部署上线',
          animated: true
        },
        animated: true
      }
    ]

    // 添加连线到图谱
    edges.value = demoEdges

    // 更新图谱数据
    if (currentGraph.value) {
      currentGraph.value.nodes = [...nodes.value]
      currentGraph.value.edges = [...edges.value]
      currentGraph.value.updatedAt = new Date()
    }
  }

  /**
   * 基于任务数据生成初始节点
   */
  const generateInitialNodes = (taskId: string) => {
    const taskStore = useTaskObjectiveStore()
    const task = taskStore.getTaskById(taskId)
    const subTasks = taskStore.getSubTasksByTaskId(taskId)

    if (!task) {
      console.warn(`未找到任务ID为 ${taskId} 的任务`)
      return
    }

    // 创建主任务节点
    const mainNode: TaskGraphNode = {
      id: `main-${task.id}`,
      type: TaskNodeType.MAIN_TASK,
      position: { x: 400, y: 200 },
      data: {
        label: task.taskName,
        description: `${task.taskType} - ${task.createDepartment}`,
        status: task.taskStatus === '执行中' ? 'in-progress' :
               task.taskStatus === '已暂停' ? 'not-started' : 'completed',
        assignee: extractAssigneeFromDepartment(task.createDepartment),
        dueDate: task.startTime,
        tags: [task.taskType, task.executionCycle],
        metadata: {
          originalTaskId: task.id,
          executionCycle: task.executionCycle,
          taskSwitch: task.taskSwitch
        }
      },
      draggable: true,
      selectable: true,
      deletable: false,
      connectable: true
    }

    nodes.value.push(mainNode)

    // 创建子任务节点
    if (subTasks.length > 0) {
      subTasks.forEach((subTask, index) => {
        // 计算节点位置 - 使用圆形布局
        const angle = (index * (360 / subTasks.length)) * (Math.PI / 180)
        const radius = Math.max(200, subTasks.length * 30) // 根据子任务数量调整半径
        const x = 400 + Math.cos(angle) * radius
        const y = 200 + Math.sin(angle) * radius

        // 根据子任务状态确定节点类型
        let nodeType: TaskNodeType
        switch (subTask.taskStatus) {
          case '已完成':
            nodeType = TaskNodeType.COMPLETED
            break
          case '执行中':
          case '待审核':
          case '已提交':
            nodeType = TaskNodeType.IN_PROGRESS
            break
          case '未提交':
          case '已退回':
          case '已驳回':
          default:
            nodeType = TaskNodeType.NOT_STARTED
            break
        }

        // 转换任务状态
        let taskStatus: 'not-started' | 'in-progress' | 'completed' | 'blocked'
        switch (subTask.taskStatus) {
          case '已完成':
            taskStatus = 'completed'
            break
          case '执行中':
          case '待审核':
          case '已提交':
            taskStatus = 'in-progress'
            break
          case '已退回':
          case '已驳回':
            taskStatus = 'blocked'
            break
          case '未提交':
          default:
            taskStatus = 'not-started'
            break
        }

        // 转换优先级
        let priority: 'low' | 'medium' | 'high' | 'urgent'
        switch (subTask.urgencyLevel) {
          case 'P1':
            priority = 'urgent'
            break
          case 'P2':
            priority = 'high'
            break
          case 'P3':
            priority = 'medium'
            break
          case 'P4':
          default:
            priority = 'low'
            break
        }

        const subNode: TaskGraphNode = {
          id: `sub-${subTask.id}`,
          type: nodeType,
          position: { x, y },
          data: {
            label: subTask.taskName,
            description: `${subTask.taskType} - ${subTask.taskCategory}`,
            status: taskStatus,
            priority: priority,
            assignee: subTask.responsiblePerson,
            participants: subTask.participants ? [subTask.participants] : [],
            progress: subTask.progress,
            tags: [subTask.taskType, subTask.taskCategory, subTask.urgencyLevel, subTask.importanceLevel],
            dueDate: subTask.updateTime,
            metadata: {
              originalSubTaskId: subTask.id,
              sequence: subTask.sequence,
              urgencyLevel: subTask.urgencyLevel,
              importanceLevel: subTask.importanceLevel,
              createTime: subTask.createTime,
              updateTime: subTask.updateTime
            }
          },
          draggable: true,
          selectable: true,
          deletable: true,
          connectable: true
        }

        nodes.value.push(subNode)

        // 创建主任务到子任务的连线
        const edge: TaskGraphEdge = {
          id: `edge-main-${subTask.id}`,
          type: EdgeType.DEPENDENCY,
          source: mainNode.id,
          target: subNode.id,
          data: {
            label: '包含',
            description: '主任务包含子任务',
            weight: subTask.sequence
          },
          animated: subTask.taskStatus === '执行中'
        }

        edges.value.push(edge)
      })

      // 为相邻的子任务创建顺序连线（可选）
      if (subTasks.length > 1) {
        const sortedSubTasks = [...subTasks].sort((a, b) => a.sequence - b.sequence)
        for (let i = 0; i < sortedSubTasks.length - 1; i++) {
          const currentSubTask = sortedSubTasks[i]
          const nextSubTask = sortedSubTasks[i + 1]

          // 只为相邻序号的任务创建顺序连线
          if (nextSubTask.sequence - currentSubTask.sequence === 1) {
            const sequenceEdge: TaskGraphEdge = {
              id: `edge-seq-${currentSubTask.id}-${nextSubTask.id}`,
              type: EdgeType.SEQUENCE,
              source: `sub-${currentSubTask.id}`,
              target: `sub-${nextSubTask.id}`,
              data: {
                label: '顺序',
                description: `序号 ${currentSubTask.sequence} → ${nextSubTask.sequence}`,
                weight: 1
              },
              animated: true
            }

            edges.value.push(sequenceEdge)
          }
        }
      }
    } else {
      // 如果没有子任务，创建一个说明节点
      const explanationNode: TaskGraphNode = {
        id: `explanation-${task.id}`,
        type: TaskNodeType.EXPLANATION,
        position: { x: 600, y: 200 },
        data: {
          label: '暂无子任务',
          description: '该任务尚未分解为子任务',
          status: 'not-started',
          metadata: { isPlaceholder: true }
        },
        draggable: true,
        selectable: true,
        deletable: true,
        connectable: true
      }

      nodes.value.push(explanationNode)

      // 创建连线
      const explanationEdge: TaskGraphEdge = {
        id: `edge-explanation-${task.id}`,
        type: EdgeType.CUSTOM,
        source: mainNode.id,
        target: explanationNode.id,
        data: {
          label: '说明',
          description: '任务说明'
        }
      }

      edges.value.push(explanationEdge)
    }

    // 更新图谱数据
    if (currentGraph.value) {
      currentGraph.value.nodes = [...nodes.value]
      currentGraph.value.edges = [...edges.value]
      currentGraph.value.updatedAt = new Date()
    }
  }

  /**
   * 从部门信息中提取负责人姓名
   */
  const extractAssigneeFromDepartment = (department: string): string => {
    // 从 "永川区民政局-安全科-程飞" 格式中提取最后的姓名
    const parts = department.split('-')
    return parts.length > 0 ? parts[parts.length - 1] : '未指定'
  }
  
  /**
   * 保存操作到历史记录
   */
  const saveToHistory = (operation: Omit<OperationRecord, 'id' | 'timestamp'>) => {
    // 清除当前位置之后的历史记录
    operationHistory.value = operationHistory.value.slice(0, historyIndex.value + 1)
    
    // 添加新的操作记录
    const record: OperationRecord = {
      id: `op-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      ...operation
    }
    
    operationHistory.value.push(record)
    historyIndex.value = operationHistory.value.length - 1
    
    // 限制历史记录数量
    if (operationHistory.value.length > 50) {
      operationHistory.value = operationHistory.value.slice(-50)
      historyIndex.value = operationHistory.value.length - 1
    }
    
    isDirty.value = true
  }
  
  /**
   * 更新视口状态
   */
  const updateViewport = (newViewport: Partial<Viewport>) => {
    viewport.value = { ...viewport.value, ...newViewport }
    
    if (currentGraph.value) {
      currentGraph.value.viewport = viewport.value
      currentGraph.value.updatedAt = new Date()
    }
  }
  
  /**
   * 清空选择
   */
  const clearSelection = () => {
    selectedElements.value = []
  }
  
  /**
   * 选择元素
   */
  const selectElement = (elementId: string) => {
    if (!selectedElements.value.includes(elementId)) {
      selectedElements.value.push(elementId)
    }
  }
  
  /**
   * 取消选择元素
   */
  const deselectElement = (elementId: string) => {
    const index = selectedElements.value.indexOf(elementId)
    if (index > -1) {
      selectedElements.value.splice(index, 1)
    }
  }
  
  /**
   * 切换元素选择状态
   */
  const toggleElementSelection = (elementId: string) => {
    if (selectedElements.value.includes(elementId)) {
      deselectElement(elementId)
    } else {
      selectElement(elementId)
    }
  }
  
  /**
   * 批量选择元素
   */
  const selectMultipleElements = (elementIds: string[]) => {
    selectedElements.value = [...new Set([...selectedElements.value, ...elementIds])]
  }

  // ==================== 节点操作方法 ====================

  /**
   * 添加节点
   */
  const addNode = (nodeData: Omit<TaskGraphNode, 'id'>) => {
    const nodeId = `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const newNode: TaskGraphNode = {
      id: nodeId,
      ...nodeData
    }

    const beforeState = [...nodes.value]
    nodes.value.push(newNode)

    // 更新图谱数据
    if (currentGraph.value) {
      currentGraph.value.nodes = [...nodes.value]
      currentGraph.value.updatedAt = new Date()
    }

    // 保存到历史记录
    saveToHistory({
      type: OperationType.ADD_NODE,
      data: newNode,
      beforeState,
      afterState: [...nodes.value],
      description: `添加节点: ${newNode.data.label}`
    })

    return newNode
  }

  /**
   * 更新节点
   */
  const updateNode = (nodeId: string, updates: Partial<TaskGraphNode>) => {
    const nodeIndex = nodes.value.findIndex(node => node.id === nodeId)
    if (nodeIndex === -1) return null

    const beforeState = [...nodes.value]
    const oldNode = { ...nodes.value[nodeIndex] }

    // 深度合并更新数据
    const updatedNode = {
      ...nodes.value[nodeIndex],
      ...updates,
      id: nodeId, // 确保ID不被覆盖
      data: {
        ...nodes.value[nodeIndex].data,
        ...updates.data,
        metadata: {
          ...nodes.value[nodeIndex].data.metadata,
          ...updates.data?.metadata
        }
      }
    }

    // 使用新的对象引用替换节点
    nodes.value[nodeIndex] = updatedNode

    // 更新图谱数据
    if (currentGraph.value) {
      currentGraph.value.nodes = [...nodes.value]
      currentGraph.value.updatedAt = new Date()
    }

    // 保存到历史记录
    saveToHistory({
      type: OperationType.UPDATE_NODE,
      data: { nodeId, updates },
      beforeState,
      afterState: [...nodes.value],
      description: `更新节点: ${nodes.value[nodeIndex].data.label}`
    })

    return nodes.value[nodeIndex]
  }

  /**
   * 删除节点
   */
  const deleteNode = (nodeId: string) => {
    const nodeIndex = nodes.value.findIndex(node => node.id === nodeId)
    if (nodeIndex === -1) return false

    const beforeNodesState = [...nodes.value]
    const beforeEdgesState = [...edges.value]
    const deletedNode = nodes.value[nodeIndex]

    // 删除节点
    nodes.value.splice(nodeIndex, 1)

    // 删除相关连线
    const relatedEdges = edges.value.filter(edge =>
      edge.source === nodeId || edge.target === nodeId
    )
    edges.value = edges.value.filter(edge =>
      edge.source !== nodeId && edge.target !== nodeId
    )

    // 从选择中移除
    deselectElement(nodeId)
    relatedEdges.forEach(edge => deselectElement(edge.id))

    // 更新图谱数据
    if (currentGraph.value) {
      currentGraph.value.nodes = [...nodes.value]
      currentGraph.value.edges = [...edges.value]
      currentGraph.value.updatedAt = new Date()
    }

    // 保存到历史记录
    saveToHistory({
      type: OperationType.DELETE_NODE,
      data: { deletedNode, relatedEdges },
      beforeState: { nodes: beforeNodesState, edges: beforeEdgesState },
      afterState: { nodes: [...nodes.value], edges: [...edges.value] },
      description: `删除节点: ${deletedNode.data.label}`
    })

    return true
  }

  /**
   * 移动节点
   */
  const moveNode = (nodeId: string, position: { x: number; y: number }) => {
    const nodeIndex = nodes.value.findIndex(node => node.id === nodeId)
    if (nodeIndex === -1) return false

    const beforeState = [...nodes.value]
    const oldPosition = { ...nodes.value[nodeIndex].position }

    nodes.value[nodeIndex].position = position

    // 更新图谱数据
    if (currentGraph.value) {
      currentGraph.value.nodes = [...nodes.value]
      currentGraph.value.updatedAt = new Date()
    }

    // 保存到历史记录
    saveToHistory({
      type: OperationType.MOVE_NODE,
      data: { nodeId, oldPosition, newPosition: position },
      beforeState,
      afterState: [...nodes.value],
      description: `移动节点: ${nodes.value[nodeIndex].data.label}`
    })

    return true
  }

  // ==================== 返回Store接口 ====================

  return {
    // 状态
    currentGraph,
    nodes,
    edges,
    selectedElements,
    viewport,
    operationHistory,
    historyIndex,
    isDirty,
    loading,
    error,

    // 计算属性
    selectedNodes,
    selectedEdges,
    canUndo,
    canRedo,
    graphStats,

    // 基础方法
    initializeGraph,
    generateInitialNodes,
    saveToHistory,
    updateViewport,
    clearSelection,
    selectElement,
    deselectElement,
    toggleElementSelection,
    selectMultipleElements,

    // 节点操作方法
    addNode,
    updateNode,
    deleteNode,
    moveNode,

    // 边操作方法
    addEdge,
    updateEdge,
    deleteEdge,

    // 历史操作方法
    undo,
    redo,

    // 数据持久化方法
    saveGraph,
    loadGraph,

    // 数据同步方法
    syncWithTaskData,
    refreshFromTaskData
  }

  // ==================== 边操作方法 ====================

  /**
   * 添加连线
   */
  function addEdge(edgeData: Omit<TaskGraphEdge, 'id'>) {
    // 检查连线是否已存在
    const existingEdge = edges.value.find(edge =>
      edge.source === edgeData.source && edge.target === edgeData.target
    )
    if (existingEdge) return null

    const edgeId = `edge-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const newEdge: TaskGraphEdge = {
      id: edgeId,
      ...edgeData
    }

    const beforeState = [...edges.value]
    edges.value.push(newEdge)

    // 更新图谱数据
    if (currentGraph.value) {
      currentGraph.value.edges = [...edges.value]
      currentGraph.value.updatedAt = new Date()
    }

    // 保存到历史记录
    saveToHistory({
      type: OperationType.ADD_EDGE,
      data: newEdge,
      beforeState,
      afterState: [...edges.value],
      description: `添加连线: ${newEdge.source} -> ${newEdge.target}`
    })

    return newEdge
  }

  /**
   * 更新连线
   */
  function updateEdge(edgeId: string, updates: Partial<TaskGraphEdge>) {
    const edgeIndex = edges.value.findIndex(edge => edge.id === edgeId)
    if (edgeIndex === -1) return null

    const beforeState = [...edges.value]

    // 合并更新数据
    edges.value[edgeIndex] = {
      ...edges.value[edgeIndex],
      ...updates,
      id: edgeId // 确保ID不被覆盖
    }

    // 更新图谱数据
    if (currentGraph.value) {
      currentGraph.value.edges = [...edges.value]
      currentGraph.value.updatedAt = new Date()
    }

    // 保存到历史记录
    saveToHistory({
      type: OperationType.UPDATE_EDGE,
      data: { edgeId, updates },
      beforeState,
      afterState: [...edges.value],
      description: `更新连线: ${edges.value[edgeIndex].source} -> ${edges.value[edgeIndex].target}`
    })

    return edges.value[edgeIndex]
  }

  /**
   * 删除连线
   */
  function deleteEdge(edgeId: string) {
    const edgeIndex = edges.value.findIndex(edge => edge.id === edgeId)
    if (edgeIndex === -1) return false

    const beforeState = [...edges.value]
    const deletedEdge = edges.value[edgeIndex]

    // 删除连线
    edges.value.splice(edgeIndex, 1)

    // 从选择中移除
    deselectElement(edgeId)

    // 更新图谱数据
    if (currentGraph.value) {
      currentGraph.value.edges = [...edges.value]
      currentGraph.value.updatedAt = new Date()
    }

    // 保存到历史记录
    saveToHistory({
      type: OperationType.DELETE_EDGE,
      data: deletedEdge,
      beforeState,
      afterState: [...edges.value],
      description: `删除连线: ${deletedEdge.source} -> ${deletedEdge.target}`
    })

    return true
  }

  // ==================== 历史操作方法 ====================

  /**
   * 撤销操作
   */
  function undo() {
    if (!canUndo.value) return false

    const operation = operationHistory.value[historyIndex.value]
    if (!operation) return false

    // 根据操作类型执行撤销
    switch (operation.type) {
      case OperationType.ADD_NODE:
        // 撤销添加节点：删除节点
        const addedNode = operation.data as TaskGraphNode
        const nodeIndex = nodes.value.findIndex(node => node.id === addedNode.id)
        if (nodeIndex > -1) {
          nodes.value.splice(nodeIndex, 1)
        }
        break

      case OperationType.DELETE_NODE:
        // 撤销删除节点：恢复节点和相关连线
        const { deletedNode, relatedEdges } = operation.data
        nodes.value.push(deletedNode)
        edges.value.push(...relatedEdges)
        break

      case OperationType.UPDATE_NODE:
        // 撤销更新节点：恢复原始状态
        if (operation.beforeState) {
          nodes.value = [...operation.beforeState]
        }
        break

      case OperationType.MOVE_NODE:
        // 撤销移动节点：恢复原始位置
        const { nodeId, oldPosition } = operation.data
        const moveNodeIndex = nodes.value.findIndex(node => node.id === nodeId)
        if (moveNodeIndex > -1) {
          nodes.value[moveNodeIndex].position = oldPosition
        }
        break

      case OperationType.ADD_EDGE:
        // 撤销添加连线：删除连线
        const addedEdge = operation.data as TaskGraphEdge
        const edgeIndex = edges.value.findIndex(edge => edge.id === addedEdge.id)
        if (edgeIndex > -1) {
          edges.value.splice(edgeIndex, 1)
        }
        break

      case OperationType.DELETE_EDGE:
        // 撤销删除连线：恢复连线
        const deletedEdge = operation.data as TaskGraphEdge
        edges.value.push(deletedEdge)
        break

      case OperationType.UPDATE_EDGE:
        // 撤销更新连线：恢复原始状态
        if (operation.beforeState) {
          edges.value = [...operation.beforeState]
        }
        break
    }

    historyIndex.value--

    // 更新图谱数据
    if (currentGraph.value) {
      currentGraph.value.nodes = [...nodes.value]
      currentGraph.value.edges = [...edges.value]
      currentGraph.value.updatedAt = new Date()
    }

    return true
  }

  /**
   * 重做操作
   */
  function redo() {
    if (!canRedo.value) return false

    historyIndex.value++
    const operation = operationHistory.value[historyIndex.value]
    if (!operation) return false

    // 根据操作类型执行重做
    switch (operation.type) {
      case OperationType.ADD_NODE:
        // 重做添加节点
        const nodeToAdd = operation.data as TaskGraphNode
        if (!nodes.value.find(node => node.id === nodeToAdd.id)) {
          nodes.value.push(nodeToAdd)
        }
        break

      case OperationType.DELETE_NODE:
        // 重做删除节点
        const { deletedNode, relatedEdges } = operation.data
        nodes.value = nodes.value.filter(node => node.id !== deletedNode.id)
        edges.value = edges.value.filter(edge =>
          !relatedEdges.some((re: TaskGraphEdge) => re.id === edge.id)
        )
        break

      case OperationType.UPDATE_NODE:
      case OperationType.MOVE_NODE:
      case OperationType.UPDATE_EDGE:
        // 重做更新操作：应用后状态
        if (operation.afterState) {
          if (operation.type === OperationType.UPDATE_EDGE) {
            edges.value = [...operation.afterState]
          } else {
            nodes.value = [...operation.afterState]
          }
        }
        break

      case OperationType.ADD_EDGE:
        // 重做添加连线
        const edgeToAdd = operation.data as TaskGraphEdge
        if (!edges.value.find(edge => edge.id === edgeToAdd.id)) {
          edges.value.push(edgeToAdd)
        }
        break

      case OperationType.DELETE_EDGE:
        // 重做删除连线
        const edgeToDelete = operation.data as TaskGraphEdge
        edges.value = edges.value.filter(edge => edge.id !== edgeToDelete.id)
        break
    }

    // 更新图谱数据
    if (currentGraph.value) {
      currentGraph.value.nodes = [...nodes.value]
      currentGraph.value.edges = [...edges.value]
      currentGraph.value.updatedAt = new Date()
    }

    return true
  }

  // ==================== 数据持久化方法 ====================

  /**
   * 保存图谱到本地存储
   */
  function saveGraph() {
    if (!currentGraph.value) return false

    try {
      const graphData = {
        ...currentGraph.value,
        nodes: [...nodes.value],
        edges: [...edges.value],
        viewport: viewport.value,
        updatedAt: new Date()
      }

      localStorage.setItem(`graph-${currentGraph.value.id}`, JSON.stringify(graphData))
      isDirty.value = false
      return true
    } catch (err) {
      error.value = '保存图谱失败'
      return false
    }
  }

  /**
   * 从本地存储加载图谱
   */
  function loadGraph(graphId: string) {
    try {
      const stored = localStorage.getItem(`graph-${graphId}`)
      if (!stored) return false

      const graphData = JSON.parse(stored) as GraphData

      currentGraph.value = graphData
      nodes.value = [...graphData.nodes]
      edges.value = [...graphData.edges]
      viewport.value = graphData.viewport
      selectedElements.value = []
      operationHistory.value = []
      historyIndex.value = -1
      isDirty.value = false

      return true
    } catch (err) {
      error.value = '加载图谱失败'
      return false
    }
  }

  /**
   * 同步图谱数据与原始任务数据
   * 当原始任务数据发生变更时调用此方法
   */
  function syncWithTaskData(taskId: string) {
    const taskStore = useTaskObjectiveStore()
    const task = taskStore.getTaskById(taskId)
    const subTasks = taskStore.getSubTasksByTaskId(taskId)

    if (!task) return false

    // 更新主任务节点
    const mainNodeId = `main-${task.id}`
    const mainNodeIndex = nodes.value.findIndex(node => node.id === mainNodeId)

    if (mainNodeIndex > -1) {
      const updatedMainNode = {
        ...nodes.value[mainNodeIndex],
        data: {
          ...nodes.value[mainNodeIndex].data,
          label: task.taskName,
          description: `${task.taskType} - ${task.createDepartment}`,
          status: task.taskStatus === '执行中' ? 'in-progress' :
                 task.taskStatus === '已暂停' ? 'not-started' : 'completed',
          assignee: extractAssigneeFromDepartment(task.createDepartment),
          tags: [task.taskType, task.executionCycle],
          metadata: {
            ...nodes.value[mainNodeIndex].data.metadata,
            executionCycle: task.executionCycle,
            taskSwitch: task.taskSwitch
          }
        }
      }

      nodes.value[mainNodeIndex] = updatedMainNode
    }

    // 更新子任务节点
    subTasks.forEach(subTask => {
      const subNodeId = `sub-${subTask.id}`
      const subNodeIndex = nodes.value.findIndex(node => node.id === subNodeId)

      if (subNodeIndex > -1) {
        // 确定节点类型
        let nodeType: TaskNodeType
        switch (subTask.taskStatus) {
          case '已完成':
            nodeType = TaskNodeType.COMPLETED
            break
          case '执行中':
          case '待审核':
          case '已提交':
            nodeType = TaskNodeType.IN_PROGRESS
            break
          case '未提交':
          case '已退回':
          case '已驳回':
          default:
            nodeType = TaskNodeType.NOT_STARTED
            break
        }

        // 转换任务状态
        let taskStatus: 'not-started' | 'in-progress' | 'completed' | 'blocked'
        switch (subTask.taskStatus) {
          case '已完成':
            taskStatus = 'completed'
            break
          case '执行中':
          case '待审核':
          case '已提交':
            taskStatus = 'in-progress'
            break
          case '已退回':
          case '已驳回':
            taskStatus = 'blocked'
            break
          case '未提交':
          default:
            taskStatus = 'not-started'
            break
        }

        // 转换优先级
        let priority: 'low' | 'medium' | 'high' | 'urgent'
        switch (subTask.urgencyLevel) {
          case 'P1':
            priority = 'urgent'
            break
          case 'P2':
            priority = 'high'
            break
          case 'P3':
            priority = 'medium'
            break
          case 'P4':
          default:
            priority = 'low'
            break
        }

        const updatedSubNode = {
          ...nodes.value[subNodeIndex],
          type: nodeType,
          data: {
            ...nodes.value[subNodeIndex].data,
            label: subTask.taskName,
            description: `${subTask.taskType} - ${subTask.taskCategory}`,
            status: taskStatus,
            priority: priority,
            assignee: subTask.responsiblePerson,
            participants: subTask.participants ? [subTask.participants] : [],
            progress: subTask.progress,
            tags: [subTask.taskType, subTask.taskCategory, subTask.urgencyLevel, subTask.importanceLevel],
            dueDate: subTask.updateTime,
            metadata: {
              ...nodes.value[subNodeIndex].data.metadata,
              sequence: subTask.sequence,
              urgencyLevel: subTask.urgencyLevel,
              importanceLevel: subTask.importanceLevel,
              updateTime: subTask.updateTime
            }
          }
        }

        nodes.value[subNodeIndex] = updatedSubNode
      }
    })

    // 更新图谱数据
    if (currentGraph.value) {
      currentGraph.value.nodes = [...nodes.value]
      currentGraph.value.updatedAt = new Date()
      isDirty.value = true
    }

    return true
  }

  /**
   * 从任务数据重新生成图谱
   * 当需要完全重新构建图谱时使用
   */
  function refreshFromTaskData(taskId: string) {
    // 清空现有数据
    nodes.value = []
    edges.value = []
    selectedElements.value = []

    // 重新生成节点
    generateInitialNodes(taskId)

    // 更新图谱数据
    if (currentGraph.value) {
      currentGraph.value.nodes = [...nodes.value]
      currentGraph.value.edges = [...edges.value]
      currentGraph.value.updatedAt = new Date()
      isDirty.value = true
    }

    return true
  }
})
