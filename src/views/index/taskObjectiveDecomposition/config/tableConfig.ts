/**
 * 表格配置文件
 */

// 子任务列表配置
export const subTaskColumns = [
  { field: 'taskName', title: '子任务名称', minWidth: 150 },
  { field: 'taskType', title: '子任务类型', width: 120 },
  { field: 'taskCategory', title: '子任务分类', width: 120 },
  { field: 'responsiblePerson', title: '责任人', width: 100 },
  { field: 'participants', title: '参与人', width: 150 },
  { field: 'taskStatus', title: '任务状态', width: 100 },
  { field: 'urgencyLevel', title: '紧急程度', width: 100 },
  { field: 'progress', title: '任务进度', width: 120 }
]

// 子任务操作按钮 - 按照【详情、关联、提交、删除、编辑、审核、更多】顺序排列
export const subTaskButtons = [
  // 主要操作按钮（显示在表格中）
  { type: 'info' as const, code: 'detail', title: '详情', icon: '', verify: 'true', more: false, showBtn: 'true' },
  { type: 'default' as const, code: 'associate', title: '关联', icon: '', verify: 'true', more: false, showBtn: 'true' },
  { type: 'success' as const, code: 'submit', title: '提交', icon: '', verify: 'true', more: false, showBtn: 'true' },
  { type: 'danger' as const, code: 'delete', title: '删除', icon: '', verify: 'true', more: false, showBtn: 'true' },
  { type: 'primary' as const, code: 'edit', title: '编辑', icon: '', verify: 'true', more: false, showBtn: 'true' },
  { type: 'warning' as const, code: 'audit', title: '审核', icon: '', verify: 'true', more: false, showBtn: 'true' },
  // 更多操作选项 - 子任务级别
  { type: 'primary' as const, code: 'split', title: '任务拆分', icon: '', verify: 'true', more: true, showBtn: 'true' },
  { type: 'info' as const, code: 'copy', title: '复制', icon: '', verify: 'true', more: true, showBtn: 'true' },
  { type: 'default' as const, code: 'moveUp', title: '上移', icon: '', verify: 'window.canMoveUp && window.canMoveUp(row)', more: true, showBtn: 'true' },
  { type: 'default' as const, code: 'moveDown', title: '下移', icon: '', verify: 'window.canMoveDown && window.canMoveDown(row)', more: true, showBtn: 'true' },
  { type: 'default' as const, code: 'setReminder', title: '设置提醒', icon: '', verify: 'true', more: true, showBtn: 'true' },
  { type: 'default' as const, code: 'progressTracking', title: '进度追踪', icon: '', verify: 'true', more: true, showBtn: 'true' },
  { type: 'default' as const, code: 'riskSetting', title: '风险设定', icon: '', verify: 'true', more: true, showBtn: 'true' },
  { type: 'default' as const, code: 'propertyManagement', title: '属性管理', icon: '', verify: 'true', more: true, showBtn: 'true' },
  // 更多操作选项 - 任务级别（从详情栏移过来的）
  { type: 'default' as const, code: 'taskDifficultyAnalysis', title: '任务难度分析', icon: '', verify: 'true', more: true, showBtn: 'true' },
  { type: 'default' as const, code: 'taskTimeAnalysis', title: '任务时长分析', icon: '', verify: 'true', more: true, showBtn: 'true' },
  { type: 'default' as const, code: 'taskReminderHistory', title: '任务提醒历史', icon: '', verify: 'true', more: true, showBtn: 'true' },
  { type: 'default' as const, code: 'editTask', title: '编辑任务', icon: '', verify: 'true', more: true, showBtn: 'true' }
]

// 状态选项 - 根据用户需求更新
export const statusOptions = [
  { label: '未提交', value: '未提交' },
  { label: '待审核', value: '待审核' },
  { label: '已提交', value: '已提交' },
  { label: '已退回', value: '已退回' },
  { label: '已驳回', value: '已驳回' },
  { label: '已完成', value: '已完成' }
]

// 状态颜色映射
export const statusColorMap = {
  '未提交': 'info',
  '待审核': 'warning',
  '已提交': 'success',
  '已退回': 'danger',
  '已驳回': 'danger',
  '已完成': 'success'
} as const

// 子任务类型选项
export const taskTypeOptions = [
  { label: '业务报表子任务', value: '业务报表' },
  { label: '临时报表子任务', value: '临时报表' }
]

// 子任务分类选项
export const taskCategoryOptions = [
  { label: '党的建设', value: '党的建设' },
  { label: '民生服务', value: '民生服务' },
  { label: '经济发展', value: '经济发展' },
  { label: '社会治理', value: '社会治理' }
]

// 优先级选项
export const priorityOptions = [
  { label: '低', value: '低' },
  { label: '中', value: '中' },
  { label: '高', value: '高' }
]

// 紧急程度选项
export const urgencyLevelOptions = [
  { label: 'P1 - 特急', value: 'P1' },
  { label: 'P2 - 加急', value: 'P2' },
  { label: 'P3 - 平急', value: 'P3' },
  { label: 'P4 - 不重要', value: 'P4' }
]

// 紧急程度颜色映射
export const urgencyColorMap = {
  'P1': 'danger',
  'P2': 'warning',
  'P3': 'primary',
  'P4': 'info'
} as const

// 责任人选项（模拟数据）
export const responsiblePersonOptions = [
  { label: '张三 - 技术部 - 开发工程师', value: '张三' },
  { label: '李四 - 产品部 - 产品经理', value: '李四' },
  { label: '王五 - 设计部 - UI设计师', value: '王五' },
  { label: '赵六 - 测试部 - 测试工程师', value: '赵六' },
  { label: '钱七 - 运维部 - 运维工程师', value: '钱七' },
  { label: '孙八 - 技术部 - 高级开发工程师', value: '孙八' },
  { label: '周九 - 产品部 - 高级产品经理', value: '周九' },
  { label: '吴十 - 管理部 - 项目经理', value: '吴十' }
]

// 参与人选项（模拟数据）
export const participantOptions = [
  { label: '张三 - 技术部 - 开发工程师', value: '张三' },
  { label: '李四 - 产品部 - 产品经理', value: '李四' },
  { label: '王五 - 设计部 - UI设计师', value: '王五' },
  { label: '赵六 - 测试部 - 测试工程师', value: '赵六' },
  { label: '钱七 - 运维部 - 运维工程师', value: '钱七' },
  { label: '孙八 - 技术部 - 高级开发工程师', value: '孙八' },
  { label: '周九 - 产品部 - 高级产品经理', value: '周九' },
  { label: '吴十 - 管理部 - 项目经理', value: '吴十' },
  { label: '刘一 - 财务部 - 财务专员', value: '刘一' },
  { label: '陈二 - 人事部 - 人事专员', value: '陈二' },
  { label: '杨三 - 市场部 - 市场专员', value: '杨三' },
  { label: '黄四 - 客服部 - 客服专员', value: '黄四' }
]

// 子任务处理选项
export const subTaskProcessingOptions = [
  { label: '批量提醒', value: 'batchReminder' },
  { label: '批量催办', value: 'batchUrge' },
  { label: '跳过审核环节', value: 'skipAudit' }
]
