<!-- 子任务详情页面 -->
<script setup lang="ts" name="SubTaskDetail">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useTaskObjectiveStore, type SubTask } from '@/stores/taskObjectiveStore'

// Router和Store
const router = useRouter()
const route = useRoute()
const taskStore = useTaskObjectiveStore()

// 响应式数据
const loading = ref(true)
const subTaskId = computed(() => route.params.id as string)

// 获取子任务数据
const subTaskData = computed(() => {
  const subTask = taskStore.getSubTaskById(subTaskId.value)
  return subTask
})

// 获取所属任务数据
const parentTaskData = computed(() => {
  if (!subTaskData.value) return null
  return taskStore.getTaskById(subTaskData.value.taskId)
})

// 格式化紧急程度显示
const formatUrgencyLevel = (level: string) => {
  const urgencyMap = {
    'P1': { text: 'P1 - 特急', type: 'danger' },
    'P2': { text: 'P2 - 加急', type: 'warning' },
    'P3': { text: 'P3 - 平急', type: 'primary' },
    'P4': { text: 'P4 - 不重要', type: 'info' }
  }
  return urgencyMap[level as keyof typeof urgencyMap] || { text: level, type: 'info' }
}

// 格式化任务状态显示
const formatTaskStatus = (status: string) => {
  const statusMap = {
    '未提交': { type: 'info' },
    '待审核': { type: 'warning' },
    '已提交': { type: 'success' },
    '已退回': { type: 'danger' },
    '已驳回': { type: 'danger' },
    '已完成': { type: 'success' }
  }
  return statusMap[status as keyof typeof statusMap] || { type: 'info' }
}

// 返回上一页
const handleBack = () => {
  router.back()
}

// 页面初始化
onMounted(async () => {
  try {
    // 确保Store数据已初始化
    taskStore.initializeData()
    
    // 模拟加载延迟
    await new Promise(resolve => setTimeout(resolve, 100))
    
    // 检查子任务是否存在
    if (!subTaskData.value) {
      ElMessage.error('子任务不存在')
      router.back()
      return
    }
    
    loading.value = false
  } catch (error) {
    console.error('加载子任务详情失败:', error)
    ElMessage.error('加载子任务详情失败')
    loading.value = false
  }
})
</script>

<template>
  <div class="subtask-detail-page" v-loading="loading">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">子任务详情</h2>
      </div>
      <div class="header-actions">
        <el-button @click="handleBack" class="return-button">返回</el-button>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="page-content" v-if="subTaskData">
      <!-- 子任务基本信息卡片 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <h3>子任务基本信息</h3>
        </template>
        
        <div class="info-grid">
          <div class="info-item">
            <label>子任务名称：</label>
            <span>{{ subTaskData.taskName }}</span>
          </div>
          
          <div class="info-item">
            <label>子任务类型：</label>
            <span>{{ subTaskData.taskType }}</span>
          </div>
          
          <div class="info-item">
            <label>子任务分类：</label>
            <span>{{ subTaskData.taskCategory }}</span>
          </div>
          
          <div class="info-item">
            <label>责任人：</label>
            <span>{{ subTaskData.responsiblePerson }}</span>
          </div>
          
          <div class="info-item">
            <label>参与人：</label>
            <span>{{ subTaskData.participants }}</span>
          </div>
          
          <div class="info-item">
            <label>任务状态：</label>
            <el-tag :type="formatTaskStatus(subTaskData.taskStatus).type">
              {{ subTaskData.taskStatus }}
            </el-tag>
          </div>
          
          <div class="info-item">
            <label>紧急程度：</label>
            <el-tag :type="formatUrgencyLevel(subTaskData.urgencyLevel).type">
              {{ formatUrgencyLevel(subTaskData.urgencyLevel).text }}
            </el-tag>
          </div>
          
          <div class="info-item">
            <label>序号：</label>
            <span>{{ subTaskData.sequence }}</span>
          </div>
        </div>
      </el-card>

      <!-- 进度信息卡片 -->
      <el-card class="progress-card" shadow="never">
        <template #header>
          <h3>进度信息</h3>
        </template>
        
        <div class="progress-content">
          <div class="progress-item">
            <label>任务进度：</label>
            <div class="progress-display">
              <el-progress 
                :percentage="subTaskData.progress" 
                :stroke-width="20"
                :text-inside="true"
              />
            </div>
          </div>
          
          <div class="info-grid">
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ subTaskData.createTime }}</span>
            </div>
            
            <div class="info-item">
              <label>更新时间：</label>
              <span>{{ subTaskData.updateTime }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 所属任务信息卡片 -->
      <el-card class="parent-task-card" shadow="never" v-if="parentTaskData">
        <template #header>
          <h3>所属任务信息</h3>
        </template>
        
        <div class="info-grid">
          <div class="info-item">
            <label>任务名称：</label>
            <span>{{ parentTaskData.taskName }}</span>
          </div>
          
          <div class="info-item">
            <label>任务类型：</label>
            <span>{{ parentTaskData.taskType }}</span>
          </div>
          
          <div class="info-item">
            <label>创建部门：</label>
            <span>{{ parentTaskData.createDepartment }}</span>
          </div>
          
          <div class="info-item">
            <label>开始时间：</label>
            <span>{{ parentTaskData.startTime }}</span>
          </div>
          
          <div class="info-item">
            <label>执行周期：</label>
            <span>{{ parentTaskData.executionCycle }}</span>
          </div>
          
          <div class="info-item">
            <label>任务状态：</label>
            <el-tag :type="parentTaskData.taskStatus === '执行中' ? 'success' : 'warning'">
              {{ parentTaskData.taskStatus }}
            </el-tag>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.subtask-detail-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 4px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #303133;
}

.return-button {
  background-color: #409EFF;
  border-color: #409EFF;
  color: white;
}

.page-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-card,
.progress-card,
.parent-task-card {
  background: white;
  border-radius: 4px;
  border: 1px solid #EBEEF5;
}

.info-card :deep(.el-card__header),
.progress-card :deep(.el-card__header),
.parent-task-card :deep(.el-card__header) {
  padding: 18px 20px;
  border-bottom: 1px solid #EBEEF5;
  background-color: #fafafa;
}

.info-card :deep(.el-card__header) h3,
.progress-card :deep(.el-card__header) h3,
.parent-task-card :deep(.el-card__header) h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 22px 40px;
  padding: 20px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item label {
  width: 140px;
  text-align: right;
  color: #606266;
  font-size: 14px;
  margin-right: 12px;
  flex-shrink: 0;
}

.info-item span {
  color: #303133;
  font-size: 14px;
  flex: 1;
}

.progress-content {
  padding: 20px;
}

.progress-item {
  margin-bottom: 22px;
}

.progress-item label {
  display: block;
  width: 140px;
  text-align: right;
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
}

.progress-display {
  max-width: 400px;
}

.progress-display :deep(.el-progress-bar__outer) {
  background-color: #f0f2f5;
}

.progress-display :deep(.el-progress-bar__inner) {
  background-color: #409EFF;
}
</style>
