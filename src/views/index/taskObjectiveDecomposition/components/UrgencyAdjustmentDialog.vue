<!-- 紧急程度调整弹窗 -->
<template>
  <DialogComp
    v-model="visible"
    title="子任务紧急程度"
    width="800px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="urgency-adjustment-content" v-loading="loading" element-loading-text="处理中...">
      <!-- 子任务列表表格 -->
      <div class="subtask-table-section">
        <el-table
          :data="subTaskList"
          style="width: 100%"
          height="400"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="taskName" label="子任务名称" min-width="150" />
          <el-table-column prop="taskType" label="子任务类型" width="120" />
          <el-table-column prop="responsiblePerson" label="责任人" width="100" />
          <el-table-column prop="urgencyLevel" label="当前紧急程度" width="120">
            <template #default="{ row }">
              <el-tag :type="getUrgencyType(row.urgencyLevel)">
                {{ getUrgencyLabel(row.urgencyLevel) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 紧急程度调整表单 -->
      <div class="urgency-form-section">
        <el-form :model="formData" :rules="formRules" ref="formRef" label-width="140px">
          <el-form-item label="调整紧急程度" prop="newUrgencyLevel" required>
            <el-select
              v-model="formData.newUrgencyLevel"
              placeholder="请选择新的紧急程度"
              style="width: 300px"
            >
              <el-option
                v-for="option in urgencyOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              >
                <span style="float: left">{{ option.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  {{ option.description }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="调整原因" prop="adjustReason">
            <el-input
              v-model="formData.adjustReason"
              type="textarea"
              :rows="3"
              placeholder="请输入调整原因（可选）"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 选中任务提示 -->
      <div class="selected-tasks-info" v-if="selectedTasks.length > 0">
        <p class="info-text">已选择 {{ selectedTasks.length }} 个子任务进行紧急程度调整：</p>
        <ul class="task-list">
          <li v-for="task in selectedTasks" :key="task.id" class="task-item">
            {{ task.taskName }} - 当前：{{ getUrgencyLabel(task.urgencyLevel) }}
          </li>
        </ul>
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm" 
          :loading="loading"
          :disabled="selectedTasks.length === 0"
        >
          确认调整
        </el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'
import { urgencyColorMap } from '../data/mockData'

interface Props {
  modelValue: boolean
  subTaskData: any[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: any): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  subTaskData: () => []
})

const emit = defineEmits<Emits>()

// 响应式状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const formRef = ref<FormInstance>()
const selectedTasks = ref<any[]>([])

// 表单数据
const formData = reactive({
  newUrgencyLevel: '',
  adjustReason: ''
})

// 表单验证规则
const formRules: FormRules = {
  newUrgencyLevel: [
    { required: true, message: '请选择新的紧急程度', trigger: 'change' }
  ],
  adjustReason: [
    { max: 200, message: '调整原因不能超过200个字符', trigger: 'blur' }
  ]
}

// 紧急程度选项
const urgencyOptions = [
  { label: 'P1 - 特急', value: 'P1', description: '需要立即处理' },
  { label: 'P2 - 加急', value: 'P2', description: '需要优先处理' },
  { label: 'P3 - 平急', value: 'P3', description: '正常处理' },
  { label: 'P4 - 不重要', value: 'P4', description: '可延后处理' }
]

// 子任务列表
const subTaskList = computed(() => props.subTaskData || [])

// 获取紧急程度标签
const getUrgencyLabel = (urgencyLevel: string) => {
  const option = urgencyOptions.find(opt => opt.value === urgencyLevel)
  return option ? option.label : urgencyLevel
}

// 获取紧急程度标签类型
const getUrgencyType = (urgencyLevel: string) => {
  return urgencyColorMap[urgencyLevel as keyof typeof urgencyColorMap] || 'info'
}

// 处理表格选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedTasks.value = selection
}

// 重置表单
const resetForm = () => {
  formData.newUrgencyLevel = ''
  formData.adjustReason = ''
  selectedTasks.value = []
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 处理取消
const handleCancel = () => {
  visible.value = false
}

// 处理关闭
const handleClose = () => {
  resetForm()
}

// 处理确认
const handleConfirm = async () => {
  if (!formRef.value) return

  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要调整的子任务')
    return
  }

  try {
    await formRef.value.validate()

    // 检查是否有任务的紧急程度与新设置的相同
    const sameUrgencyTasks = selectedTasks.value.filter(
      task => task.urgencyLevel === formData.newUrgencyLevel
    )

    if (sameUrgencyTasks.length === selectedTasks.value.length) {
      ElMessage.warning('所选任务的紧急程度与新设置相同，无需调整')
      return
    }

    if (sameUrgencyTasks.length > 0) {
      const message = `有 ${sameUrgencyTasks.length} 个任务的紧急程度已经是 ${getUrgencyLabel(formData.newUrgencyLevel)}，是否继续？`
      try {
        await ElMessageBox.confirm(message, '确认调整', {
          confirmButtonText: '继续调整',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch {
        return
      }
    }

    loading.value = true

    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 1000))

    // 模拟可能的失败情况
    const success = Math.random() > 0.1 // 90% 成功率

    if (!success) {
      throw new Error('调整失败，请重试')
    }

    const adjustmentData = {
      selectedTasks: selectedTasks.value.map(task => ({
        ...task,
        oldUrgencyLevel: task.urgencyLevel,
        newUrgencyLevel: formData.newUrgencyLevel
      })),
      newUrgencyLevel: formData.newUrgencyLevel,
      adjustReason: formData.adjustReason,
      adjustTime: new Date().toISOString(),
      adjustedCount: selectedTasks.value.length
    }

    emit('confirm', adjustmentData)
    visible.value = false

    ElMessage.success(`成功调整 ${selectedTasks.value.length} 个子任务的紧急程度`)
  } catch (error) {
    if (error instanceof Error) {
      ElMessage.error(error.message)
    } else {
      console.error('表单验证失败:', error)
    }
  } finally {
    loading.value = false
  }
}

// 监听弹窗显示状态
watch(visible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})
</script>

<style lang="scss" scoped>
.urgency-adjustment-content {
  .subtask-table-section {
    margin-bottom: 24px;

    :deep(.el-table) {
      border: 1px solid #e4e7ed;
      border-radius: 6px;

      .el-table__header {
        background-color: #fafbfc;
      }

      .el-table__row {
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }

  .urgency-form-section {
    margin-bottom: 24px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;

    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #303133;
    }

    :deep(.el-select) {
      .el-input__inner {
        border-radius: 6px;
      }
    }

    :deep(.el-textarea) {
      .el-textarea__inner {
        border-radius: 6px;
        resize: none;
      }
    }
  }

  .selected-tasks-info {
    padding: 16px 20px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 8px;
    border-left: 4px solid #409eff;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);

    .info-text {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #1f2937;
    }

    .task-list {
      margin: 0;
      padding: 0;
      list-style: none;
      max-height: 120px;
      overflow-y: auto;

      .task-item {
        padding: 6px 0;
        font-size: 13px;
        color: #4b5563;
        position: relative;
        padding-left: 20px;
        line-height: 1.4;
        border-bottom: 1px solid rgba(64, 158, 255, 0.1);

        &:last-child {
          border-bottom: none;
        }

        &:before {
          content: '▸';
          position: absolute;
          left: 0;
          color: #409eff;
          font-weight: bold;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
  margin-top: 24px;
}

// 紧急程度标签样式优化
:deep(.el-tag) {
  font-weight: 500;
  border-radius: 4px;

  &.el-tag--danger {
    background-color: #fef0f0;
    border-color: #fbc4c4;
    color: #f56c6c;
  }

  &.el-tag--warning {
    background-color: #fdf6ec;
    border-color: #f5dab1;
    color: #e6a23c;
  }

  &.el-tag--primary {
    background-color: #ecf5ff;
    border-color: #b3d8ff;
    color: #409eff;
  }

  &.el-tag--info {
    background-color: #f4f4f5;
    border-color: #d3d4d6;
    color: #909399;
  }
}

// 选择框选项样式优化
:deep(.el-select-dropdown__item) {
  padding: 12px 20px;

  &:hover {
    background-color: #f5f7fa;
  }

  &.selected {
    background-color: #ecf5ff;
    color: #409eff;
    font-weight: 500;
  }
}
</style>
