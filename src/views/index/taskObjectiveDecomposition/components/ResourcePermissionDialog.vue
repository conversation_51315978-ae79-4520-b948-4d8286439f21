<!-- 子任务资源分配权限设置弹窗 -->
<template>
  <DialogComp
    v-model="visible"
    title="子任务资源分配权限设置"
    width="700px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="resource-permission-content">
      <!-- 权限设置表格 -->
      <div class="permission-table-section">
        <el-table
          :data="permissionData"
          style="width: 100%"
          height="400"
        >
          <el-table-column prop="roleName" label="角色名称" min-width="150" />
          <el-table-column label="操作权限" min-width="300">
            <template #default="{ row }">
              <div class="permission-actions">
                <el-tag 
                  v-for="permission in row.permissions" 
                  :key="permission"
                  :type="getPermissionType(permission)"
                  size="small"
                  class="permission-tag"
                >
                  {{ permission }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import DialogComp from '@/components/common/dialog-comp.vue'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 模拟权限数据
const permissionData = ref([
  {
    id: 1,
    roleName: '管理员',
    permissions: ['新增', '编辑', '删除']
  },
  {
    id: 2,
    roleName: '台账运维员',
    permissions: ['新增', '编辑', '删除']
  },
  {
    id: 3,
    roleName: '区县台账运维员',
    permissions: ['新增', '编辑', '删除']
  },
  {
    id: 4,
    roleName: '数据领导',
    permissions: ['新增', '编辑', '删除']
  }
])

// 获取权限标签类型
const getPermissionType = (permission: string) => {
  const typeMap: Record<string, string> = {
    '新增': 'success',
    '编辑': 'warning',
    '删除': 'danger'
  }
  return typeMap[permission] || 'info'
}

// 处理取消
const handleCancel = () => {
  visible.value = false
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 处理确认
const handleConfirm = () => {
  emit('confirm', {
    permissionData: permissionData.value
  })
  visible.value = false
}
</script>

<style lang="scss" scoped>
.resource-permission-content {
  padding: 20px;

  .permission-table-section {
    .permission-actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;

      .permission-tag {
        margin: 2px 0;
      }
    }
  }
}

.dialog-footer {
  margin-top: 24px;
  text-align: right;
  
  .el-button {
    margin-left: 12px;
  }
}
</style>
