<!-- 子任务编辑弹窗 -->
<template>
  <DialogComp v-model="visible" title="业务表子任务编辑" width="600px" @clickConfirm="handleConfirm"
    @clickCancel="handleCancel">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="140px" class="subtask-edit-form">
      <el-form-item label="子任务名称" prop="taskName" required>
        <el-input v-model="formData.taskName" placeholder="请输入子任务名称" maxlength="50" show-word-limit />
      </el-form-item>

      <el-form-item label="子任务类型" prop="taskType" required>
        <el-select v-model="formData.taskType" placeholder="请选择子任务类型" style="width: 100%">
          <el-option label="业务报表" value="业务报表" />
          <el-option label="临时报表" value="临时报表" />
        </el-select>
      </el-form-item>

      <el-form-item label="子任务优先级设置" prop="priority" required>
        <el-select v-model="formData.priority" placeholder="请选择优先级" style="width: 100%">
          <el-option label="高" value="高" />
          <el-option label="中" value="中" />
          <el-option label="低" value="低" />
        </el-select>
      </el-form-item>

      <el-form-item label="责任人" prop="responsiblePersonId" required>
        <el-select v-model="formData.responsiblePersonId" placeholder="请选择责任人" style="width: 100%" filterable>
          <el-option v-for="person in personnelOptions" :key="person.id"
            :label="`${person.name} - ${person.department} - ${person.role}`" :value="person.id" />
        </el-select>
      </el-form-item>

      <el-form-item label="参与人" prop="participants" required>
        <div class="participants-container">
          <el-input v-model="formData.participants" placeholder="请输入参与人，多人用逗号分隔" maxlength="100" show-word-limit />
          <el-button type="primary" class="permission-config-btn" @click="handleOpenPermissionConfig">
            参与人权限配置
          </el-button>
        </div>
      </el-form-item>


    </el-form>

    <!-- 参与人权限配置弹窗 -->
    <ParticipantPermissionDialog v-model="permissionDialogVisible" :personnel-data="personnelData"
      @save="handlePermissionSave" />
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'
import ParticipantPermissionDialog from './ParticipantPermissionDialog.vue'
import { useTaskObjectiveStore } from '@/stores/taskObjectiveStore'

interface Props {
  modelValue: boolean
  editData?: any
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: any): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  editData: null
})

const emit = defineEmits<Emits>()

// Store
const taskStore = useTaskObjectiveStore()

// 响应式数据
const visible = ref(false)
const formRef = ref<FormInstance>()
const isEdit = ref(false)
const permissionDialogVisible = ref(false)

// 人员选项计算属性
const personnelOptions = computed(() => taskStore.personnelData)

// 表单数据
const formData = reactive({
  taskName: '',
  taskType: '',
  taskCategory: '',
  priority: '',
  responsiblePerson: '',
  responsiblePersonId: '',
  participants: '',
  taskStatus: '未提交',
  progress: 0
})

// 使用Store中的人员数据，确保数据一致性
const personnelData = computed(() => taskStore.personnelData)

// 表单验证规则
const rules: FormRules = {
  taskName: [
    { required: true, message: '请输入子任务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  taskType: [
    { required: true, message: '请选择子任务类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择子任务优先级', trigger: 'change' }
  ],
  responsiblePersonId: [
    { required: true, message: '请选择责任人', trigger: 'change' }
  ],
  participants: [
    { required: true, message: '请输入参与人', trigger: 'blur' },
    { max: 100, message: '长度不能超过 100 个字符', trigger: 'blur' }
  ],
  taskStatus: [
    { required: true, message: '请选择任务状态', trigger: 'change' }
  ]
}

// 监听弹窗显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    initForm()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 初始化表单
const initForm = () => {
  isEdit.value = !!props.editData

  if (props.editData) {
    // 编辑模式，填充数据
    // 根据责任人姓名查找对应的ID
    const responsiblePersonId = findPersonIdByName(props.editData.responsiblePerson || '')

    Object.assign(formData, {
      taskName: props.editData.taskName || '',
      taskType: props.editData.taskType || '',
      taskCategory: props.editData.taskCategory || '',
      priority: props.editData.priority || '',
      responsiblePerson: props.editData.responsiblePerson || '',
      responsiblePersonId: responsiblePersonId,
      participants: props.editData.participants || '',
      taskStatus: props.editData.taskStatus || '未开始',
      progress: props.editData.progress || 0
    })
  } else {
    // 新增模式，重置表单
    Object.assign(formData, {
      taskName: '',
      taskType: '',
      taskCategory: '',
      priority: '',
      responsiblePerson: '',
      responsiblePersonId: '',
      participants: '',
      taskStatus: '未开始',
      progress: 0
    })
  }

  // 清除验证状态
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 根据姓名查找人员ID - 改进匹配逻辑
const findPersonIdByName = (name: string): string => {
  if (!name || !name.trim()) {
    console.warn('findPersonIdByName: 姓名为空')
    return ''
  }

  const trimmedName = name.trim()
  const person = taskStore.personnelData.find(p => p.name === trimmedName)

  if (!person) {
    console.warn(`findPersonIdByName: 未找到姓名为 "${trimmedName}" 的人员`, {
      searchName: trimmedName,
      availablePersons: taskStore.personnelData.map(p => ({ id: p.id, name: p.name }))
    })
    return ''
  }

  console.log(`findPersonIdByName: 找到人员 ${trimmedName} -> ID: ${person.id}`)
  return person.id
}

// 根据ID获取人员姓名 - 改进匹配逻辑
const getPersonNameById = (id: string): string => {
  if (!id || !id.trim()) {
    console.warn('getPersonNameById: ID为空')
    return ''
  }

  const trimmedId = id.trim()
  const person = taskStore.personnelData.find(p => p.id === trimmedId)

  if (!person) {
    console.warn(`getPersonNameById: 未找到ID为 "${trimmedId}" 的人员`, {
      searchId: trimmedId,
      availablePersons: taskStore.personnelData.map(p => ({ id: p.id, name: p.name }))
    })
    return ''
  }

  console.log(`getPersonNameById: 找到人员 ID: ${trimmedId} -> ${person.name}`)
  return person.name
}

// 打开参与人权限配置弹窗
const handleOpenPermissionConfig = () => {
  permissionDialogVisible.value = true
}

// 处理权限配置保存
const handlePermissionSave = (participants: any[]) => {
  // 将参与人信息更新到表单中
  const participantNames = participants.map(p => p.name).join('、')
  formData.participants = participantNames
  permissionDialogVisible.value = false
  ElMessage.success('参与人权限配置成功')
}

// 确认操作
const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 获取选中的责任人姓名
    const responsiblePersonName = getPersonNameById(formData.responsiblePersonId)

    const submitData = {
      ...formData,
      responsiblePerson: responsiblePersonName, // 保存姓名到数据库
      id: props.editData?.id || undefined
    }

    emit('confirm', submitData)
    visible.value = false

    ElMessage.success(isEdit.value ? '编辑成功' : '新增成功')
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 取消操作
const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped lang="scss">
.subtask-edit-form {
  padding: 20px 0;

  :deep(.el-form-item__label) {
    font-weight: 500;
  }

  :deep(.el-slider) {
    margin-right: 20px;
  }

  :deep(.el-slider__input) {
    width: 80px;
  }



  .participants-container {
    display: flex;
    gap: 10px;
    align-items: flex-start;

    .el-input {
      flex: 1;
    }

    .permission-config-btn {
      flex-shrink: 0;
      white-space: nowrap;
    }
  }
}
</style>
