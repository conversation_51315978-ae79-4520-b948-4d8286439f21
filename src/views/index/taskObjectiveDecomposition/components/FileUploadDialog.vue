<!-- 导入业务表信息模板弹窗 -->
<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'upload', file: File): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 弹窗状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 上传状态
const uploading = ref(false)
const selectedFile = ref<File | null>(null)

// 方法
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    // 验证文件类型
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv', // .csv
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
      'application/msword' // .doc
    ]
    
    if (!allowedTypes.includes(file.type)) {
      ElMessage.error('只支持 Excel、Word、CSV 格式的文件')
      return
    }
    
    // 验证文件大小 (10MB)
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      ElMessage.error('文件大小不能超过 10MB')
      return
    }
    
    selectedFile.value = file
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  const files = event.dataTransfer?.files
  
  if (files && files.length > 0) {
    const file = files[0]
    
    // 验证文件类型
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword'
    ]
    
    if (!allowedTypes.includes(file.type)) {
      ElMessage.error('只支持 Excel、Word、CSV 格式的文件')
      return
    }
    
    // 验证文件大小
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      ElMessage.error('文件大小不能超过 10MB')
      return
    }
    
    selectedFile.value = file
  }
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
}

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault()
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
}

const triggerFileSelect = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.xlsx,.xls,.csv,.docx,.doc'
  input.onchange = handleFileSelect
  input.click()
}

const handleCancel = () => {
  visible.value = false
  selectedFile.value = null
}

const handleUpload = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请选择要上传的文件')
    return
  }
  
  uploading.value = true
  
  try {
    // 模拟上传过程
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('upload', selectedFile.value)
    selectedFile.value = null
  } catch (error) {
    ElMessage.error('文件上传失败')
  } finally {
    uploading.value = false
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<template>
  <DialogComp
    v-model="visible"
    title="导入业务表信息模板"
    width="600px"
    :close-on-click-modal="false"
    :visible-close-button="false"
    :visible-confirm-button="false"
    @close="handleCancel"
  >
    <!-- 提示信息 -->
    <div class="info-banner">
      <el-icon class="info-icon"><InfoFilled /></el-icon>
      <span>业务报表信息模板导入需要实现能导入一个文件。</span>
    </div>

    <!-- 上传区域 -->
    <div class="upload-section">
      <div
        class="upload-area"
        :class="{ 'is-dragover': false }"
        @drop="handleDrop"
        @dragover="handleDragOver"
        @dragenter="handleDragEnter"
        @dragleave="handleDragLeave"
        @click="triggerFileSelect"
      >
        <div class="upload-content">
          <el-icon class="upload-icon" :size="48">
            <UploadFilled />
          </el-icon>
          <div class="upload-text">
            <p class="primary-text">点击或将文件拖拽到这里上传</p>
            <p class="secondary-text">支持 Excel、Word、CSV 格式，文件大小不超过 10MB</p>
          </div>
        </div>
      </div>
      
      <!-- 已选择的文件 -->
      <div v-if="selectedFile" class="selected-file">
        <div class="file-info">
          <el-icon class="file-icon"><Document /></el-icon>
          <div class="file-details">
            <div class="file-name">{{ selectedFile.name }}</div>
            <div class="file-size">{{ formatFileSize(selectedFile.size) }}</div>
          </div>
          <el-button
            type="text"
            class="remove-btn"
            @click.stop="selectedFile = null"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          :loading="uploading"
          :disabled="!selectedFile"
          @click="handleUpload"
        >
          {{ uploading ? '上传中...' : '上传' }}
        </el-button>
      </div>
    </template>
  </DialogComp>
</template>

<style scoped lang="scss">
.info-banner {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 20px;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  color: #1890ff;
  
  .info-icon {
    margin-right: 8px;
    font-size: 16px;
  }
}

.upload-section {
  .upload-area {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background-color: #fafafa;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      border-color: #1890ff;
      background-color: #f0f8ff;
    }
    
    &.is-dragover {
      border-color: #1890ff;
      background-color: #f0f8ff;
    }
    
    .upload-content {
      .upload-icon {
        color: #8c8c8c;
        margin-bottom: 16px;
      }
      
      .upload-text {
        .primary-text {
          margin: 0 0 8px 0;
          color: #262626;
          font-size: 16px;
        }
        
        .secondary-text {
          margin: 0;
          color: #8c8c8c;
          font-size: 14px;
        }
      }
    }
  }
  
  .selected-file {
    margin-top: 16px;
    
    .file-info {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      background-color: #f5f5f5;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      
      .file-icon {
        color: #1890ff;
        font-size: 20px;
        margin-right: 12px;
      }
      
      .file-details {
        flex: 1;
        
        .file-name {
          font-size: 14px;
          color: #262626;
          margin-bottom: 4px;
        }
        
        .file-size {
          font-size: 12px;
          color: #8c8c8c;
        }
      }
      
      .remove-btn {
        color: #8c8c8c;
        
        &:hover {
          color: #ff4d4f;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
