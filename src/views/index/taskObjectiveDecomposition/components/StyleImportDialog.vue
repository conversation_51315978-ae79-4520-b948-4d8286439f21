<template>
  <DialogComp
    v-model="visible"
    title="样式模板导入"
    width="500px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="style-import-content">
      <!-- 文件上传区域 -->
      <div class="upload-area">
        <el-upload
          ref="uploadRef"
          class="upload-dragger"
          drag
          :auto-upload="false"
          :show-file-list="false"
          accept=".xlsx,.xls,.csv"
          :on-change="handleFileChange"
          :before-upload="beforeUpload"
        >
          <div class="upload-content">
            <el-icon class="upload-icon">
              <UploadFilled />
            </el-icon>
            <div class="upload-text">点击或将文件拖拽到这里上传</div>
            <div class="upload-hint">支持 .xlsx, .xls, .csv 格式文件</div>
          </div>
        </el-upload>
      </div>

      <!-- 文件信息显示 -->
      <div v-if="selectedFile" class="file-info">
        <div class="file-item">
          <el-icon><Document /></el-icon>
          <span class="file-name">{{ selectedFile.name }}</span>
          <span class="file-size">({{ formatFileSize(selectedFile.size) }})</span>
          <el-button type="text" @click="removeFile">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 导入预览 -->
      <div v-if="previewData.length > 0" class="preview-section">
        <h4>导入预览</h4>
        <el-table :data="previewData" style="width: 100%" max-height="200">
          <el-table-column prop="templateName" label="样式模板名称" min-width="150" />
          <el-table-column prop="inProgressColor" label="执行中配色" width="120" align="center">
            <template #default="{ row }">
              <div class="color-preview" :style="{ backgroundColor: row.inProgressColor }"></div>
              <span class="color-text">{{ row.inProgressColor }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="completedColor" label="已完成配色" width="120" align="center">
            <template #default="{ row }">
              <div class="color-preview" :style="{ backgroundColor: row.completedColor }"></div>
              <span class="color-text">{{ row.completedColor }}</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="preview-info">
          <span>共 {{ previewData.length }} 条数据，点击上传按钮导入</span>
        </div>
      </div>

      <!-- 导入说明 -->
      <div class="import-instructions">
        <h4>导入说明</h4>
        <ul>
          <li>文件格式：支持 Excel (.xlsx, .xls) 和 CSV (.csv) 格式</li>
          <li>文件结构：第一列为样式模板名称，第二列为执行中配色，第三列为已完成配色</li>
          <li>颜色格式：请使用十六进制颜色值，如 #409EFF</li>
          <li>文件大小：不超过 5MB</li>
        </ul>
      </div>

      <!-- 底部按钮 -->
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleUpload"
          :disabled="!selectedFile || previewData.length === 0"
          :loading="uploading"
        >
          上传
        </el-button>
      </div>
    </div>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, type UploadInstance, type UploadRawFile } from 'element-plus'
import { UploadFilled, Document, Close } from '@element-plus/icons-vue'
import DialogComp from '@/components/common/dialog-comp.vue'

// 接口定义
interface StyleTemplate {
  id: string
  templateName: string
  inProgressColor: string
  completedColor: string
  createTime: string
}

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: StyleTemplate[]]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const uploadRef = ref<UploadInstance>()
const selectedFile = ref<File | null>(null)
const previewData = ref<StyleTemplate[]>([])
const uploading = ref(false)

// 方法
const handleClose = () => {
  visible.value = false
  resetData()
}

const handleCancel = () => {
  visible.value = false
  resetData()
}

const handleFileChange = (file: any) => {
  selectedFile.value = file.raw
  parseFile(file.raw)
}

const beforeUpload = (file: UploadRawFile) => {
  const isValidType = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
                      'application/vnd.ms-excel', 
                      'text/csv'].includes(file.type)
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isValidType) {
    ElMessage.error('只能上传 Excel 或 CSV 格式的文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('文件大小不能超过 5MB!')
    return false
  }
  return false // 阻止自动上传
}

const parseFile = async (file: File) => {
  try {
    const text = await file.text()
    const lines = text.split('\n').filter(line => line.trim())
    
    if (lines.length < 2) {
      ElMessage.error('文件内容格式不正确，至少需要包含标题行和一行数据')
      return
    }

    const data: StyleTemplate[] = []
    
    // 跳过标题行，从第二行开始解析
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim()
      if (!line) continue
      
      const columns = line.split(',').map(col => col.trim().replace(/"/g, ''))
      
      if (columns.length >= 3) {
        const templateName = columns[0]
        const inProgressColor = columns[1]
        const completedColor = columns[2]
        
        // 验证颜色格式
        const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
        if (!colorRegex.test(inProgressColor) || !colorRegex.test(completedColor)) {
          ElMessage.warning(`第 ${i + 1} 行颜色格式不正确，已跳过`)
          continue
        }
        
        data.push({
          id: Date.now().toString() + i,
          templateName,
          inProgressColor,
          completedColor,
          createTime: new Date().toISOString().split('T')[0]
        })
      }
    }
    
    if (data.length === 0) {
      ElMessage.error('未找到有效的数据行')
      return
    }
    
    previewData.value = data
    ElMessage.success(`成功解析 ${data.length} 条数据`)
  } catch (error) {
    console.error('文件解析失败:', error)
    ElMessage.error('文件解析失败，请检查文件格式')
  }
}

const handleUpload = () => {
  if (!selectedFile.value || previewData.value.length === 0) {
    ElMessage.warning('请先选择文件并确保有有效数据')
    return
  }

  uploading.value = true
  
  // 添加100毫秒延迟效果
  setTimeout(() => {
    emit('confirm', previewData.value)
    visible.value = false
    resetData()
    uploading.value = false
    
    ElMessage.success(`成功导入 ${previewData.value.length} 个样式模板`)
  }, 100)
}

const removeFile = () => {
  selectedFile.value = null
  previewData.value = []
  uploadRef.value?.clearFiles()
}

const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(1) + ' KB'
  } else {
    return (size / (1024 * 1024)).toFixed(1) + ' MB'
  }
}

const resetData = () => {
  selectedFile.value = null
  previewData.value = []
  uploading.value = false
  uploadRef.value?.clearFiles()
}
</script>

<style lang="scss" scoped>
.style-import-content {
  padding: 20px;

  .upload-area {
    margin-bottom: 20px;

    .upload-dragger {
      :deep(.el-upload-dragger) {
        border: 2px dashed #d9d9d9;
        border-radius: 6px;
        padding: 40px;
        text-align: center;
        background-color: #fafafa;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409EFF;
          background-color: #f0f9ff;
        }
      }

      .upload-content {
        .upload-icon {
          font-size: 48px;
          color: #409EFF;
          margin-bottom: 16px;
        }

        .upload-text {
          font-size: 16px;
          color: #606266;
          margin-bottom: 8px;
        }

        .upload-hint {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }

  .file-info {
    margin-bottom: 20px;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 6px;

    .file-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .file-name {
        flex: 1;
        font-size: 14px;
        color: #303133;
      }

      .file-size {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .preview-section {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .color-preview {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      border: 1px solid #dcdfe6;
      margin: 0 auto 4px;
    }

    .color-text {
      font-size: 12px;
      color: #606266;
    }

    .preview-info {
      margin-top: 12px;
      font-size: 14px;
      color: #606266;
      text-align: center;
    }
  }

  .import-instructions {
    margin-bottom: 20px;
    padding: 16px;
    background: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 6px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #0066cc;
    }

    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        font-size: 14px;
        color: #606266;
        line-height: 1.6;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .dialog-footer {
    margin-top: 24px;
    text-align: right;
    
    .el-button {
      margin-left: 12px;
    }
  }
}
</style>
