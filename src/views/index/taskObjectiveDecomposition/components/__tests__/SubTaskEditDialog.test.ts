import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElMessage } from 'element-plus'
import SubTaskEditDialog from '../SubTaskEditDialog.vue'
import { useTaskObjectiveStore } from '@/stores/taskObjectiveStore'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn()
  }
}))

// Mock store
vi.mock('@/stores/taskObjectiveStore', () => ({
  useTaskObjectiveStore: vi.fn(() => ({
    personnelData: [
      {
        id: 'p1',
        name: '张三',
        department: '永川区民政局-安全科',
        role: '科员',
        permissions: ['新增', '编辑', '删除']
      },
      {
        id: 'p2',
        name: '李四',
        department: '永川区民政局-安全科',
        role: '副科长',
        permissions: ['新增', '编辑']
      }
    ]
  }))
}))

describe('SubTaskEditDialog', () => {
  let wrapper: any

  const mockProps = {
    modelValue: true,
    editData: null
  }

  beforeEach(() => {
    wrapper = mount(SubTaskEditDialog, {
      props: mockProps,
      global: {
        stubs: {
          'DialogComp': {
            template: '<div><slot /></div>',
            emits: ['clickConfirm', 'clickCancel']
          },
          'ParticipantPermissionDialog': true
        }
      }
    })
  })

  it('应该正确渲染组件', () => {
    expect(wrapper.exists()).toBe(true)
  })

  it('应该显示人员下拉选择框', () => {
    const selectElement = wrapper.find('el-select')
    expect(selectElement.exists()).toBe(true)
  })

  it('应该正确显示人员选项格式', () => {
    const options = wrapper.findAll('el-option')
    expect(options.length).toBeGreaterThan(0)
    
    // 检查第一个选项的标签格式
    const firstOption = options[0]
    const label = firstOption.attributes('label')
    expect(label).toMatch(/^.+ - .+ - .+$/) // 格式：姓名 - 部门 - 职位
  })

  it('新增模式下应该清空表单', () => {
    const component = wrapper.vm
    component.initForm()
    
    expect(component.formData.taskName).toBe('')
    expect(component.formData.responsiblePersonId).toBe('')
  })

  it('编辑模式下应该正确填充数据', async () => {
    const editData = {
      id: 'st1',
      taskName: '测试任务',
      taskType: '业务报表',
      responsiblePerson: '张三',
      participants: '李四'
    }

    await wrapper.setProps({ editData })
    const component = wrapper.vm
    component.initForm()

    expect(component.formData.taskName).toBe('测试任务')
    expect(component.formData.responsiblePersonId).toBe('p1') // 张三的ID
  })

  it('应该正确处理确认操作', async () => {
    const component = wrapper.vm
    component.formData.responsiblePersonId = 'p1'
    component.formData.taskName = '测试任务'
    component.formData.taskType = '业务报表'
    component.formData.priority = '高'
    component.formData.participants = '李四'

    // Mock form validation
    component.formRef = {
      validate: vi.fn().mockResolvedValue(true)
    }

    const confirmSpy = vi.fn()
    wrapper.vm.$emit = confirmSpy

    await component.handleConfirm()

    expect(confirmSpy).toHaveBeenCalledWith('confirm', expect.objectContaining({
      responsiblePerson: '张三' // 应该转换为姓名
    }))
  })

  it('应该正确处理取消操作', () => {
    const component = wrapper.vm
    const cancelSpy = vi.fn()
    component.$emit = cancelSpy

    component.handleCancel()

    expect(component.visible).toBe(false)
  })

  it('应该正确查找人员ID', () => {
    const component = wrapper.vm
    const personId = component.findPersonIdByName('张三')
    expect(personId).toBe('p1')

    const unknownPersonId = component.findPersonIdByName('不存在的人')
    expect(unknownPersonId).toBe('')
  })

  it('应该正确获取人员姓名', () => {
    const component = wrapper.vm
    const personName = component.getPersonNameById('p1')
    expect(personName).toBe('张三')

    const unknownPersonName = component.getPersonNameById('unknown')
    expect(unknownPersonName).toBe('')
  })

  it('表单验证失败时应该不提交', async () => {
    const component = wrapper.vm
    
    // Mock form validation failure
    component.formRef = {
      validate: vi.fn().mockRejectedValue(new Error('验证失败'))
    }

    const confirmSpy = vi.fn()
    component.$emit = confirmSpy

    await component.handleConfirm()

    expect(confirmSpy).not.toHaveBeenCalled()
  })
})
