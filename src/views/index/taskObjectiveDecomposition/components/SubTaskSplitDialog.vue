<!-- 子任务拆分弹窗组件 -->
<template>
  <el-dialog
    v-model="visible"
    title="子任务拆分"
    width="600px"
    :close-on-click-modal="false"
    @close="handleCancel"
  >
    <div class="subtask-split-content" v-loading="loading" element-loading-text="拆分处理中...">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="140px"
        v-if="!loading"
      >
        <!-- 原子任务信息显示 -->
        <div class="original-task-info">
          <h4>原子任务信息</h4>
          <p><strong>任务名称：</strong>{{ originalTask?.taskName }}</p>
          <p><strong>任务类型：</strong>{{ originalTask?.taskType }}</p>
          <p><strong>责任人：</strong>{{ originalTask?.responsiblePerson }}</p>
        </div>

        <!-- 拆分配置 -->
        <el-form-item label="拆分数量" prop="splitCount" required>
          <span class="required-mark">*</span>
          <el-input-number
            v-model="formData.splitCount"
            :min="2"
            :max="10"
            placeholder="请输入拆分数量"
            style="width: 100%"
          />
          <div class="form-tip">将原任务拆分为多个子任务，最少2个，最多10个</div>
        </el-form-item>

        <!-- 子任务类型 -->
        <el-form-item label="子任务类型" prop="taskType" required>
          <span class="required-mark">*</span>
          <el-select
            v-model="formData.taskType"
            placeholder="下拉单选：业务报表子任务、临时报表子任务"
            style="width: 100%"
          >
            <el-option
              v-for="option in taskTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 子任务分类 -->
        <el-form-item label="子任务分类" prop="taskCategory" required>
          <span class="required-mark">*</span>
          <el-select
            v-model="formData.taskCategory"
            placeholder="下拉单选：党的建设"
            style="width: 100%"
          >
            <el-option
              v-for="option in taskCategoryOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 子任务描述 -->
        <el-form-item label="子任务描述" prop="taskDescription">
          <el-input
            v-model="formData.taskDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入子任务描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <!-- 子任务优先级设置 -->
        <el-form-item label="子任务优先级设置" prop="priorityLevel" required>
          <span class="required-mark">*</span>
          <el-select
            v-model="formData.priorityLevel"
            placeholder="下拉单选：低中高"
            style="width: 100%"
          >
            <el-option
              v-for="option in priorityOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 子任务数值 -->
        <el-form-item label="子任务数值">
          <el-input-number
            v-model="formData.taskValue"
            :min="0"
            :max="100"
            placeholder="请输入数值"
            style="width: 100%"
          />
        </el-form-item>

        <!-- 责任人 -->
        <el-form-item label="责任人" prop="responsiblePerson" required>
          <span class="required-mark">*</span>
          <el-select
            v-model="formData.responsiblePerson"
            placeholder="请选择人员（范围为本部门）"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="option in responsiblePersonOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 参与人 -->
        <el-form-item label="参与人" prop="participants" required>
          <span class="required-mark">*</span>
          <el-select
            v-model="formData.participants"
            placeholder="请选择人员（范围为本部门）"
            style="width: 100%"
            multiple
            filterable
          >
            <el-option
              v-for="option in participantOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <!-- 拆分预览 -->
      <div class="split-preview" v-if="formData.splitCount > 1">
        <h4>拆分预览</h4>
        <p class="preview-tip">将生成 {{ formData.splitCount }} 个子任务，每个子任务将继承上述配置</p>
        <ul class="preview-list">
          <li v-for="i in formData.splitCount" :key="i" class="preview-item">
            {{ originalTask?.taskName }}_拆分{{ i }}
          </li>
        </ul>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">确认拆分</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import {
  taskTypeOptions,
  taskCategoryOptions,
  priorityOptions,
  responsiblePersonOptions,
  participantOptions
} from '../config/tableConfig'

// Props
interface Props {
  modelValue: boolean
  taskData?: any
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  taskData: null
})

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: any): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 原任务数据
const originalTask = computed(() => props.taskData)

// 表单数据
const formData = reactive({
  splitCount: 2,
  taskType: '',
  taskCategory: '',
  taskDescription: '',
  priorityLevel: '',
  taskValue: 0,
  responsiblePerson: '',
  participants: [] as string[]
})

// 表单验证规则
const rules: FormRules = {
  splitCount: [
    { required: true, message: '请输入拆分数量', trigger: 'blur' },
    { type: 'number', min: 2, max: 10, message: '拆分数量必须在2-10之间', trigger: 'blur' }
  ],
  taskType: [
    { required: true, message: '请选择子任务类型', trigger: 'change' }
  ],
  taskCategory: [
    { required: true, message: '请选择子任务分类', trigger: 'change' }
  ],
  priorityLevel: [
    { required: true, message: '请选择子任务优先级', trigger: 'change' }
  ],
  responsiblePerson: [
    { required: true, message: '请选择责任人', trigger: 'change' }
  ],
  participants: [
    { required: true, message: '请选择参与人', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一个参与人', trigger: 'change' }
  ]
}

// 监听弹窗打开，初始化表单数据
watch(() => props.modelValue, (newVal) => {
  if (newVal && props.taskData) {
    // 使用原任务数据初始化表单
    formData.taskType = props.taskData.taskType || ''
    formData.taskCategory = props.taskData.taskCategory || ''
    formData.priorityLevel = props.taskData.priorityLevel || ''
    formData.responsiblePerson = props.taskData.responsiblePerson || ''
    formData.participants = []
    formData.taskDescription = ''
    formData.splitCount = 2
    formData.taskValue = 0
  }
})

// 取消操作
const handleCancel = () => {
  visible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    splitCount: 2,
    taskType: '',
    taskCategory: '',
    taskDescription: '',
    priorityLevel: '',
    taskValue: 0,
    responsiblePerson: '',
    participants: []
  })
}

// 确认拆分
const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    loading.value = true

    // 模拟拆分处理延迟
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 构造拆分数据
    const splitData = {
      originalTaskId: props.taskData?.id,
      splitCount: formData.splitCount,
      taskType: formData.taskType,
      taskCategory: formData.taskCategory,
      taskDescription: formData.taskDescription,
      priorityLevel: formData.priorityLevel,
      taskValue: formData.taskValue,
      responsiblePerson: formData.responsiblePerson,
      participants: formData.participants
    }

    emit('confirm', splitData)
    visible.value = false
    resetForm()
    
    ElMessage.success(`成功将任务拆分为 ${formData.splitCount} 个子任务`)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.subtask-split-content {
  .required-mark {
    color: #f56c6c;
    margin-right: 4px;
  }

  .original-task-info {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #409eff;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }

    p {
      margin: 8px 0;
      font-size: 13px;
      color: #606266;
    }
  }

  .form-tip {
    margin-top: 4px;
    font-size: 12px;
    color: #909399;
  }

  .split-preview {
    margin-top: 20px;
    padding: 16px;
    background: #f0f9ff;
    border-radius: 6px;
    border-left: 4px solid #67c23a;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }

    .preview-tip {
      margin: 0 0 12px 0;
      font-size: 13px;
      color: #606266;
    }

    .preview-list {
      margin: 0;
      padding: 0;
      list-style: none;

      .preview-item {
        padding: 4px 0;
        font-size: 13px;
        color: #67c23a;
        position: relative;
        padding-left: 16px;

        &:before {
          content: '•';
          position: absolute;
          left: 0;
          color: #67c23a;
        }
      }
    }
  }
}
</style>
