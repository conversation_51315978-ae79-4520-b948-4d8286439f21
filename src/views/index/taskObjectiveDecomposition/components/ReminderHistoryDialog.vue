<template>
  <DialogComp
    v-model="visible"
    title="任务提醒历史"
    width="700px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="reminder-history-content">
      <!-- 提醒历史表格 -->
      <div class="reminder-table">
        <el-table 
          :data="reminderData" 
          style="width: 100%" 
          :loading="loading"
          border
          stripe
          max-height="400"
        >
          <el-table-column prop="reminderTime" label="提醒时间" width="150" align="center" />
          <el-table-column prop="reminderPerson" label="提醒人" min-width="200" align="left">
            <template #default="{ row }">
              <div class="reminder-persons">
                <el-tag 
                  v-for="person in parseReminderPersons(row.reminderPerson)" 
                  :key="person"
                  size="small"
                  class="person-tag"
                >
                  {{ person }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="reminderType" label="提醒方式" width="120" align="center">
            <template #default="{ row }">
              <el-tag 
                :type="getReminderTypeColor(row.reminderType)" 
                size="small"
              >
                {{ row.reminderType }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag 
                :type="getStatusType(row.status)" 
                size="small"
              >
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 统计信息 -->
      <div class="statistics-section">
        <h4>提醒统计</h4>
        <div class="statistics-grid">
          <div class="stat-item">
            <label class="stat-label">总提醒次数：</label>
            <span class="stat-value total-reminders">{{ totalReminders }}</span>
          </div>
          <div class="stat-item">
            <label class="stat-label">已读提醒：</label>
            <span class="stat-value read-reminders">{{ readReminders }}</span>
          </div>
          <div class="stat-item">
            <label class="stat-label">未读提醒：</label>
            <span class="stat-value unread-reminders">{{ unreadReminders }}</span>
          </div>
          <div class="stat-item">
            <label class="stat-label">最近提醒：</label>
            <span class="stat-value latest-reminder">{{ latestReminderTime }}</span>
          </div>
        </div>
      </div>

      <!-- 操作说明 -->
      <div class="operation-notes" v-if="showNotes">
        <h4>说明</h4>
        <ul>
          <li><strong>提醒方式</strong>：系统消息、邮件通知、短信提醒</li>
          <li><strong>状态标识</strong>：已读（绿色）、未读（橙色）、失败（红色）</li>
          <li><strong>提醒人员</strong>：显示接收提醒的具体人员信息</li>
        </ul>
      </div>

      <!-- 底部按钮 -->
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </div>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'

// 接口定义
interface ReminderItem {
  reminderTime: string
  reminderPerson: string
  reminderType: '系统消息' | '邮件通知' | '短信提醒'
  status: '已读' | '未读' | '失败'
}

interface ReminderHistoryData {
  reminderData: ReminderItem[]
  totalReminders: number
  readReminders: number
  unreadReminders: number
  latestReminderTime: string
}

// Props
const props = defineProps<{
  modelValue: boolean
  taskData?: any
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: ReminderHistoryData]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const showNotes = ref(false)

// 提醒历史数据
const reminderData = ref<ReminderItem[]>([
  {
    reminderTime: '2025-04-23',
    reminderPerson: '朱沅镇-公共服务-王五、朱沅镇-公共服务-李四',
    reminderType: '系统消息',
    status: '已读'
  },
  {
    reminderTime: '2025-05-23',
    reminderPerson: '朱沅镇-公共服务-王五、朱沅镇-公共服务-李四',
    reminderType: '邮件通知',
    status: '已读'
  },
  {
    reminderTime: '2025-06-23',
    reminderPerson: '朱沅镇-公共服务-王五、朱沅镇-公共服务-李四',
    reminderType: '短信提醒',
    status: '未读'
  },
  {
    reminderTime: '2025-07-01',
    reminderPerson: '朱沅镇-公共服务-张三、朱沅镇-公共服务-赵六',
    reminderType: '系统消息',
    status: '未读'
  },
  {
    reminderTime: '2025-07-02',
    reminderPerson: '朱沅镇-公共服务-王五',
    reminderType: '邮件通知',
    status: '失败'
  }
])

// 计算属性
const totalReminders = computed(() => {
  return reminderData.value.length
})

const readReminders = computed(() => {
  return reminderData.value.filter(item => item.status === '已读').length
})

const unreadReminders = computed(() => {
  return reminderData.value.filter(item => item.status === '未读').length
})

const latestReminderTime = computed(() => {
  if (reminderData.value.length === 0) return '无'
  
  const sortedData = [...reminderData.value].sort((a, b) => 
    new Date(b.reminderTime).getTime() - new Date(a.reminderTime).getTime()
  )
  
  return sortedData[0].reminderTime
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = () => {
  // 添加100毫秒延迟效果
  loading.value = true
  setTimeout(() => {
    const historyData: ReminderHistoryData = {
      reminderData: reminderData.value,
      totalReminders: totalReminders.value,
      readReminders: readReminders.value,
      unreadReminders: unreadReminders.value,
      latestReminderTime: latestReminderTime.value
    }
    
    emit('confirm', historyData)
    visible.value = false
    loading.value = false
    
    ElMessage.success('提醒历史确认成功')
  }, 100)
}

const parseReminderPersons = (personStr: string) => {
  return personStr.split('、').filter(person => person.trim())
}

const getReminderTypeColor = (type: string) => {
  switch (type) {
    case '系统消息':
      return 'primary'
    case '邮件通知':
      return 'success'
    case '短信提醒':
      return 'warning'
    default:
      return 'info'
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case '已读':
      return 'success'
    case '未读':
      return 'warning'
    case '失败':
      return 'danger'
    default:
      return 'info'
  }
}

// 根据任务数据动态分析提醒历史
const analyzeReminderHistory = (taskData: any) => {
  if (!taskData) return

  // 模拟根据任务数据进行提醒历史分析
  const taskType = taskData.type || '报表'
  const priority = taskData.priority || '中等'

  // 根据任务优先级调整提醒频率
  if (priority === '高') {
    // 添加更多提醒记录
    reminderData.value.unshift({
      reminderTime: '2025-04-20',
      reminderPerson: '朱沅镇-公共服务-管理员',
      reminderType: '系统消息',
      status: '已读'
    })
  }

  // 根据任务创建时间调整提醒时间
  if (taskData.createTime) {
    const createDate = new Date(taskData.createTime)
    const reminderDate = new Date(createDate.getTime() + 24 * 60 * 60 * 1000) // 创建后一天
    
    reminderData.value[0].reminderTime = reminderDate.toLocaleDateString('zh-CN')
  }
}

// 监听弹窗打开，分析提醒历史数据
watch(visible, (newVal) => {
  if (newVal) {
    analyzeReminderHistory(props.taskData)
  }
})

onMounted(() => {
  if (props.taskData) {
    analyzeReminderHistory(props.taskData)
  }
})
</script>

<style lang="scss" scoped>
.reminder-history-content {
  padding: 20px;

  .reminder-table {
    margin-bottom: 24px;

    .reminder-persons {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .person-tag {
        margin: 2px 0;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;
    padding: 16px;
    background: #f5f7fa;
    border-radius: 6px;

    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .statistics-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .stat-label {
          font-weight: 500;
          color: #606266;
          flex-shrink: 0;
        }

        .stat-value {
          font-weight: 600;
          font-size: 16px;

          &.total-reminders {
            color: #409EFF;
          }

          &.read-reminders {
            color: #67C23A;
          }

          &.unread-reminders {
            color: #E6A23C;
          }

          &.latest-reminder {
            color: #F56C6C;
          }
        }
      }
    }
  }

  .operation-notes {
    margin-bottom: 24px;
    padding: 16px;
    background: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 6px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #0066cc;
    }

    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        font-size: 14px;
        color: #606266;
        line-height: 1.6;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        strong {
          color: #303133;
        }
      }
    }
  }

  .dialog-footer {
    margin-top: 24px;
    text-align: right;
    
    .el-button {
      margin-left: 12px;
    }
  }
}
</style>
