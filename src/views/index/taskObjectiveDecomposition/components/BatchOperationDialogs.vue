<template>
  <!-- 批量状态更新弹窗 -->
  <el-dialog
    v-model="batchStatusVisible"
    title="批量状态更新"
    width="400px"
    :close-on-click-modal="false"
  >
    <div class="batch-dialog-content">
      <p class="batch-instruction">请选择要更新的状态：</p>
      <el-select
        v-model="selectedBatchStatus"
        placeholder="请选择状态"
        style="width: 100%"
      >
        <el-option
          v-for="option in statusOptions"
          :key="option.value"
          :label="option.label"
          :value="option.value"
        />
      </el-select>
      <p class="selected-count">已选择 {{ selectedRowsCount }} 个子任务</p>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirmStatusUpdate">确认</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 批量分配责任人弹窗 -->
  <el-dialog
    v-model="batchAssignVisible"
    title="批量分配责任人"
    width="500px"
    :close-on-click-modal="false"
  >
    <div class="batch-dialog-content">
      <p class="batch-instruction">请选择责任人：</p>
      <el-select
        v-model="selectedResponsiblePerson"
        placeholder="请选择责任人"
        style="width: 100%"
        filterable
      >
        <el-option
          v-for="option in responsiblePersonOptions"
          :key="option.value"
          :label="option.label"
          :value="option.value"
        />
      </el-select>
      <p class="selected-count">已选择 {{ selectedRowsCount }} 个子任务</p>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirmAssign">确认</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 子任务合并弹窗 -->
  <el-dialog
    v-model="subTaskMergeVisible"
    title="子任务合并"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="merge-dialog-content" v-loading="mergeLoading">
      <el-form :model="mergeFormData" label-width="120px">
        <el-form-item label="子任务类型" required>
          <el-select
            v-model="mergeFormData.taskType"
            placeholder="请选择子任务类型"
            style="width: 100%"
          >
            <el-option
              v-for="option in taskTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="合并后子任务名称" required>
          <el-input
            v-model="mergeFormData.taskName"
            placeholder="请输入合并后子任务名称"
          />
        </el-form-item>

        <el-form-item label="子任务分类" required>
          <el-select
            v-model="mergeFormData.taskCategory"
            placeholder="请选择子任务分类"
            style="width: 100%"
          >
            <el-option
              v-for="option in taskCategoryOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="子任务描述">
          <el-input
            v-model="mergeFormData.taskDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入子任务描述"
          />
        </el-form-item>

        <el-form-item label="子任务优先级设置" required>
          <el-select
            v-model="mergeFormData.priorityLevel"
            placeholder="请选择优先级"
            style="width: 100%"
          >
            <el-option
              v-for="option in priorityOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="责任人" required>
          <el-input
            v-model="mergeFormData.responsiblePerson"
            placeholder="请选择责任人"
          />
        </el-form-item>

        <el-form-item label="参与人" required>
          <el-select
            v-model="mergeFormData.participants"
            placeholder="请选择参与人"
            style="width: 100%"
            multiple
            filterable
          >
            <el-option
              v-for="option in participantOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <p class="selected-count">将合并 {{ selectedRowsCount }} 个子任务</p>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirmMerge" :loading="mergeLoading">确认合并</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { 
  statusOptions, 
  responsiblePersonOptions, 
  taskTypeOptions, 
  taskCategoryOptions, 
  priorityOptions, 
  participantOptions 
} from '../config/tableConfig'
import { getDefaultMergeFormData } from '../data/mockData'

// Props
interface Props {
  selectedRowsCount: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  statusUpdate: [status: string]
  assignUpdate: [person: string]
  merge: [formData: any]
}>()

// 弹窗状态
const batchStatusVisible = ref(false)
const batchAssignVisible = ref(false)
const subTaskMergeVisible = ref(false)
const mergeLoading = ref(false)

// 表单数据
const selectedBatchStatus = ref('')
const selectedResponsiblePerson = ref('')
const mergeFormData = reactive(getDefaultMergeFormData())

// 打开批量状态更新弹窗
const openBatchStatusDialog = () => {
  selectedBatchStatus.value = ''
  batchStatusVisible.value = true
}

// 打开批量分配责任人弹窗
const openBatchAssignDialog = () => {
  selectedResponsiblePerson.value = ''
  batchAssignVisible.value = true
}

// 打开子任务合并弹窗
const openSubTaskMergeDialog = () => {
  // 重置表单数据
  Object.assign(mergeFormData, getDefaultMergeFormData())
  subTaskMergeVisible.value = true
}

// 取消操作
const handleCancel = () => {
  batchStatusVisible.value = false
  batchAssignVisible.value = false
  subTaskMergeVisible.value = false
  mergeLoading.value = false
}

// 确认状态更新
const handleConfirmStatusUpdate = () => {
  emit('statusUpdate', selectedBatchStatus.value)
  batchStatusVisible.value = false
}

// 确认分配责任人
const handleConfirmAssign = () => {
  emit('assignUpdate', selectedResponsiblePerson.value)
  batchAssignVisible.value = false
}

// 确认合并
const handleConfirmMerge = () => {
  mergeLoading.value = true
  emit('merge', { ...mergeFormData })
}

// 合并完成后关闭弹窗
const closeMergeDialog = () => {
  subTaskMergeVisible.value = false
  mergeLoading.value = false
}

// 暴露方法给父组件
defineExpose({
  openBatchStatusDialog,
  openBatchAssignDialog,
  openSubTaskMergeDialog,
  closeMergeDialog
})
</script>

<style scoped>
.batch-dialog-content {
  padding: 20px 0;
}

.batch-instruction {
  margin-bottom: 16px;
  font-weight: 500;
  color: #333;
}

.selected-count {
  margin-top: 16px;
  color: #666;
  font-size: 14px;
}

.merge-dialog-content {
  max-height: 500px;
  overflow-y: auto;
}

.dialog-footer {
  text-align: right;
}
</style>
