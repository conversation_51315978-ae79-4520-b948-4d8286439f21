<!-- 参与人权限设置弹窗 -->
<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { PersonnelData } from '@/stores/taskObjectiveStore'

interface Props {
  modelValue: boolean
  personnelData: PersonnelData[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'save', participants: ParticipantPermission[]): void
}

interface ParticipantPermission {
  id: string
  name: string
  permissions: string[]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 弹窗状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 参与人权限数据
const participants = ref<ParticipantPermission[]>([])

// 权限选项
const permissionOptions = [
  { label: '新增', value: '新增' },
  { label: '编辑', value: '编辑' },
  { label: '删除', value: '删除' }
]

// 监听弹窗打开
watch(visible, (newVal) => {
  if (newVal) {
    initializeParticipants()
  }
})

// 方法
const initializeParticipants = () => {
  // 初始化参与人数据，默认包含前4个人员
  participants.value = props.personnelData.slice(0, 4).map(person => ({
    id: person.id,
    name: person.name,
    permissions: [...person.permissions] // 复制默认权限
  }))
}

const handlePermissionChange = (participantId: string, permissions: (string | number)[]) => {
  const participant = participants.value.find(p => p.id === participantId)
  if (participant) {
    participant.permissions = permissions.map(p => String(p))
  }
}

// 切换权限状态
const togglePermission = (participantId: string, permission: string) => {
  const participant = participants.value.find(p => p.id === participantId)
  if (participant) {
    const index = participant.permissions.indexOf(permission)
    if (index > -1) {
      participant.permissions.splice(index, 1)
    } else {
      participant.permissions.push(permission)
    }
  }
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = () => {
  // 验证每个参与人至少有一个权限
  const invalidParticipants = participants.value.filter(p => p.permissions.length === 0)

  if (invalidParticipants.length > 0) {
    ElMessage.warning('每个参与人至少需要选择一个权限')
    return
  }

  emit('save', [...participants.value])
  visible.value = false
}

// 获取权限标签类型
const getPermissionTagType = (permission: string): 'success' | 'warning' | 'danger' | 'info' => {
  switch (permission) {
    case '新增':
      return 'success'
    case '编辑':
      return 'warning'
    case '删除':
      return 'danger'
    default:
      return 'info'
  }
}
</script>

<template>
  <DialogComp
    v-model="visible"
    title="参与人权限设置"
    width="700px"
    :close-on-click-modal="false"
    @clickConfirm="handleConfirm"
    @clickCancel="handleCancel"
  >
    <!-- 参与人权限表格 -->
    <div class="permission-table">
      <div class="table-header">
        <div class="header-cell sequence">序号</div>
        <div class="header-cell participant">参与人</div>
        <div class="header-cell permission">授予权限</div>
      </div>
      
      <div class="table-body">
        <div
          v-for="(participant, index) in participants"
          :key="participant.id"
          class="table-row"
        >
          <div class="body-cell sequence">
            {{ String(index + 1).padStart(2, '0') }}
          </div>
          <div class="body-cell participant">
            {{ participant.name }}
          </div>
          <div class="body-cell permission">
            <div class="permission-buttons">
              <el-button
                v-for="option in permissionOptions"
                :key="option.value"
                :type="participant.permissions.includes(option.value) ? 'primary' : 'default'"
                size="small"
                @click="togglePermission(participant.id, option.value)"
              >
                {{ option.label }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>


  </DialogComp>
</template>

<style scoped lang="scss">
.info-banner {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 20px;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  color: #1890ff;
  
  .info-icon {
    margin-right: 8px;
    font-size: 16px;
  }
}

.permission-table {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 20px;
  
  .table-header {
    display: flex;
    background-color: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    
    .header-cell {
      padding: 12px 16px;
      font-weight: 600;
      color: #262626;
      border-right: 1px solid #e8e8e8;
      
      &:last-child {
        border-right: none;
      }
      
      &.sequence {
        width: 80px;
        text-align: center;
      }
      
      &.participant {
        width: 120px;
      }
      
      &.permission {
        flex: 1;
      }
    }
  }
  
  .table-body {
    .table-row {
      display: flex;
      border-bottom: 1px solid #e8e8e8;
      
      &:last-child {
        border-bottom: none;
      }
      
      .body-cell {
        padding: 12px 16px;
        border-right: 1px solid #e8e8e8;
        display: flex;
        align-items: center;
        
        &:last-child {
          border-right: none;
        }
        
        &.sequence {
          width: 80px;
          justify-content: center;
          font-family: monospace;
        }
        
        &.participant {
          width: 120px;
          font-weight: 500;
        }
        
        &.permission {
          flex: 1;

          .permission-buttons {
            display: flex;
            gap: 8px;

            .el-button {
              min-width: 60px;
            }
          }
        }
      }
    }
  }
}

.permission-preview {
  h4 {
    margin: 0 0 12px 0;
    color: #262626;
    font-size: 14px;
  }
  
  .preview-list {
    .preview-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .participant-name {
        font-weight: 500;
        color: #262626;
        min-width: 60px;
      }
      
      .permission-tags {
        display: flex;
        align-items: center;
        gap: 6px;
        
        .permission-tag {
          margin: 0;
        }
        
        .no-permission {
          color: #8c8c8c;
          font-style: italic;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
