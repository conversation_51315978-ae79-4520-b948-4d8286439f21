<template>
  <DialogComp
    v-model="visible"
    title="子任务提醒设置"
    width="600px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="reminder-setting-content">
      <!-- 任务信息 -->
      <div class="task-info">
        <h4>任务信息</h4>
        <div class="info-row">
          <span class="label">任务名称：</span>
          <span class="value">{{ taskData?.taskName || '未知任务' }}</span>
        </div>
        <div class="info-row">
          <span class="label">责任人：</span>
          <span class="value">{{ taskData?.responsiblePerson || '未分配' }}</span>
        </div>
      </div>

      <!-- 提醒设置表单 -->
      <div class="reminder-form">
        <h4>提醒设置</h4>
        <el-form :model="reminderForm" :rules="formRules" ref="formRef" label-width="120px">
          <el-form-item label="提醒时间" prop="reminderTime" required>
            <el-date-picker
              v-model="reminderForm.reminderTime"
              type="datetime"
              placeholder="请选择提醒时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="提醒方式" prop="reminderMethods" required>
            <el-checkbox-group v-model="reminderForm.reminderMethods">
              <el-checkbox label="系统内通知">系统内通知</el-checkbox>
              <el-checkbox label="邮件通知">邮件通知</el-checkbox>
              <el-checkbox label="短信提醒">短信提醒</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="提醒内容" prop="reminderContent">
            <el-input
              v-model="reminderForm.reminderContent"
              type="textarea"
              :rows="3"
              placeholder="请输入提醒内容（可选）"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="重复提醒" prop="repeatReminder">
            <el-switch
              v-model="reminderForm.repeatReminder"
              active-text="启用"
              inactive-text="关闭"
            />
          </el-form-item>

          <el-form-item 
            v-if="reminderForm.repeatReminder" 
            label="重复间隔" 
            prop="repeatInterval"
          >
            <el-select v-model="reminderForm.repeatInterval" placeholder="请选择重复间隔">
              <el-option label="每天" value="daily" />
              <el-option label="每周" value="weekly" />
              <el-option label="每月" value="monthly" />
            </el-select>
          </el-form-item>

          <el-form-item label="提醒对象" prop="reminderTargets">
            <el-select
              v-model="reminderForm.reminderTargets"
              multiple
              placeholder="请选择提醒对象"
              style="width: 100%"
            >
              <el-option
                v-for="person in availablePersons"
                :key="person.id"
                :label="`${person.name} (${person.department})`"
                :value="person.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮 -->
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">确认</el-button>
      </div>
    </div>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'
import { useTaskObjectiveStore } from '@/stores/taskObjectiveStore'

// Props
interface Props {
  modelValue: boolean
  taskData?: any
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  taskData: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirm: [data: any]
}>()

// Store
const taskStore = useTaskObjectiveStore()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const reminderForm = reactive({
  reminderTime: '',
  reminderMethods: ['系统内通知'],
  reminderContent: '',
  repeatReminder: false,
  repeatInterval: 'daily',
  reminderTargets: [] as string[]
})

// 表单验证规则
const formRules: FormRules = {
  reminderTime: [
    { required: true, message: '请选择提醒时间', trigger: 'change' }
  ],
  reminderMethods: [
    { required: true, message: '请选择至少一种提醒方式', trigger: 'change' }
  ],
  reminderTargets: [
    { required: true, message: '请选择提醒对象', trigger: 'change' }
  ]
}

// 可选择的人员列表
const availablePersons = computed(() => {
  return taskStore.personnelData
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const reminderData = {
      taskId: props.taskData?.id,
      taskName: props.taskData?.taskName,
      ...reminderForm,
      createTime: new Date().toISOString(),
      status: 'active'
    }
    
    emit('confirm', reminderData)
    visible.value = false
    
    ElMessage.success('提醒设置成功')
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  reminderForm.reminderTime = ''
  reminderForm.reminderMethods = ['系统内通知']
  reminderForm.reminderContent = ''
  reminderForm.repeatReminder = false
  reminderForm.repeatInterval = 'daily'
  reminderForm.reminderTargets = []
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 监听弹窗打开，重置表单
watch(visible, (newVal) => {
  if (newVal) {
    resetForm()
    // 默认选择任务责任人
    if (props.taskData?.responsiblePerson) {
      const responsiblePerson = availablePersons.value.find(
        person => person.name === props.taskData.responsiblePerson
      )
      if (responsiblePerson) {
        reminderForm.reminderTargets = [responsiblePerson.id]
      }
    }
  }
})
</script>

<style lang="scss" scoped>
.reminder-setting-content {
  padding: 20px;

  .task-info {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 80px;
        color: #606266;
        font-size: 13px;
      }

      .value {
        color: #303133;
        font-size: 13px;
        font-weight: 500;
      }
    }
  }

  .reminder-form {
    h4 {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }
  }

  .dialog-footer {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
