<!-- 子任务资源主弹窗 -->
<template>
  <DialogComp
    v-model="visible"
    title="子任务资源"
    width="900px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="subtask-resource-content">
      <!-- 资源需求模型选择 -->
      <div class="resource-model-section">
        <div class="section-header">
          <span class="required-mark">*</span>
          <span class="section-title">子任务资源需求模型选择</span>
        </div>
        <div class="model-options">
          <el-checkbox v-model="formData.personnelEvaluationModel">
            人员投入评估模型
          </el-checkbox>
          <el-checkbox v-model="formData.costEvaluationModel">
            成本评估模型
          </el-checkbox>
          <el-button type="primary" @click="openDemandAnalysis">
            需求分析
          </el-button>
        </div>
      </div>

      <!-- 资源分配 -->
      <div class="resource-allocation-section">
        <div class="section-header">
          <span class="required-mark">*</span>
          <span class="section-title">子任务资源分配</span>
          <el-button type="primary" @click="openPermissionSetting">
            权限设置
          </el-button>
        </div>
        
        <!-- 资源分配表格 -->
        <div class="allocation-table">
          <el-table
            :data="allocationData"
            style="width: 100%"
            height="200"
          >
            <el-table-column prop="sequence" label="序号" width="80" align="center" />
            <el-table-column prop="participant" label="参与人" min-width="120" />
            <el-table-column prop="allocationStatus" label="资源分配情况" min-width="150" />
          </el-table>
        </div>
      </div>

      <!-- 成本估算 -->
      <div class="cost-estimation-section">
        <div class="section-header">
          <span class="required-mark">*</span>
          <span class="section-title">子任务成本估算（应用XXXX评估模型）</span>
          <el-button type="primary" @click="openCostEstimation">
            成本估算
          </el-button>
        </div>
      </div>

      <!-- 质量标准设置 -->
      <div class="quality-standard-section">
        <div class="section-header">
          <span class="required-mark">*</span>
          <span class="section-title">子任务质量标准设置</span>
        </div>
        <div class="quality-options">
          <span class="quality-description">
            请选择下拉多选，按时完成数据录入、确保字段完整性、配合审核修改
          </span>
          <div class="quality-buttons">
            <el-button @click="openQualityCheck">检查</el-button>
            <el-button @click="openCheckHistory">检查历史</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">确认</el-button>
      </div>
    </template>

    <!-- 子弹窗组件 -->
    <ResourceDemandAnalysisDialog
      v-model="demandAnalysisVisible"
      @confirm="handleDemandAnalysisConfirm"
    />

    <ResourcePermissionDialog
      v-model="permissionSettingVisible"
      @confirm="handlePermissionSettingConfirm"
    />

    <CostEstimationDialog
      v-model="costEstimationVisible"
      @confirm="handleCostEstimationConfirm"
    />

    <QualityCheckDialog
      v-model="qualityCheckVisible"
      @confirm="handleQualityCheckConfirm"
    />

    <CheckHistoryDialog
      v-model="checkHistoryVisible"
      @confirm="handleCheckHistoryConfirm"
    />
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'
import ResourceDemandAnalysisDialog from './ResourceDemandAnalysisDialog.vue'
import ResourcePermissionDialog from './ResourcePermissionDialog.vue'
import CostEstimationDialog from './CostEstimationDialog.vue'
import QualityCheckDialog from './QualityCheckDialog.vue'
import CheckHistoryDialog from './CheckHistoryDialog.vue'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const formData = reactive({
  personnelEvaluationModel: false,
  costEvaluationModel: false
})

// 子弹窗状态
const demandAnalysisVisible = ref(false)
const permissionSettingVisible = ref(false)
const costEstimationVisible = ref(false)
const qualityCheckVisible = ref(false)
const checkHistoryVisible = ref(false)

// 资源分配数据
const allocationData = ref([
  {
    sequence: '01',
    participant: '张三',
    allocationStatus: '43%'
  },
  {
    sequence: '02',
    participant: '李四',
    allocationStatus: '23%'
  },
  {
    sequence: '03',
    participant: '王五',
    allocationStatus: '23%'
  },
  {
    sequence: '04',
    participant: 'XXX',
    allocationStatus: '11%'
  }
])

// 打开子弹窗方法
const openDemandAnalysis = () => {
  demandAnalysisVisible.value = true
}

const openPermissionSetting = () => {
  permissionSettingVisible.value = true
}

const openCostEstimation = () => {
  costEstimationVisible.value = true
}

const openQualityCheck = () => {
  qualityCheckVisible.value = true
}

const openCheckHistory = () => {
  checkHistoryVisible.value = true
}

// 加载状态
const loading = ref(false)

// 子弹窗确认处理
const handleDemandAnalysisConfirm = (data: any) => {
  console.log('需求分析结果:', data)
  // 可以在这里更新相关数据
}

const handlePermissionSettingConfirm = (data: any) => {
  console.log('权限设置结果:', data)
  // 可以在这里更新权限相关数据
}

const handleCostEstimationConfirm = (data: any) => {
  console.log('成本估算结果:', data)
  // 可以在这里更新成本估算数据
}

const handleQualityCheckConfirm = (data: any) => {
  console.log('质量检查结果:', data)
  // 可以在这里更新质量检查数据
}

const handleCheckHistoryConfirm = (data: any) => {
  console.log('检查历史结果:', data)
  // 历史数据仅供查看，无需更新
}

// 表单验证
const validateForm = () => {
  if (!formData.personnelEvaluationModel && !formData.costEvaluationModel) {
    ElMessage.warning('请至少选择一个评估模型')
    return false
  }
  return true
}

// 主弹窗处理
const handleCancel = () => {
  // 重置表单数据
  formData.personnelEvaluationModel = false
  formData.costEvaluationModel = false
  visible.value = false
}

const handleClose = () => {
  visible.value = false
}

const handleConfirm = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true

  try {
    // 模拟处理延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    emit('confirm', {
      formData: formData,
      allocationData: allocationData.value
    })

    ElMessage.success('子任务资源配置成功')
    visible.value = false
  } catch (error) {
    console.error('配置失败:', error)
    ElMessage.error('配置失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.subtask-resource-content {
  padding: 20px;

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    gap: 8px;

    .required-mark {
      color: #f56c6c;
      font-weight: bold;
    }

    .section-title {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      flex: 1;
    }
  }

  .resource-model-section {
    margin-bottom: 24px;

    .model-options {
      display: flex;
      align-items: center;
      gap: 24px;
    }
  }

  .resource-allocation-section {
    margin-bottom: 24px;

    .allocation-table {
      margin-top: 12px;
    }
  }

  .cost-estimation-section {
    margin-bottom: 24px;
  }

  .quality-standard-section {
    .quality-options {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .quality-description {
        font-size: 14px;
        color: #606266;
        flex: 1;
      }

      .quality-buttons {
        display: flex;
        gap: 12px;
      }
    }
  }
}

.dialog-footer {
  margin-top: 24px;
  text-align: right;
  
  .el-button {
    margin-left: 12px;
  }
}
</style>
