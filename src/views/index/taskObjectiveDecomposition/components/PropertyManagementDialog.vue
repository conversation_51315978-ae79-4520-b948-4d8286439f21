<template>
  <DialogComp
    v-model="visible"
    title="子任务属性设置"
    width="800px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="property-management-content">
      <!-- 任务信息 -->
      <div class="task-info">
        <h4>任务信息</h4>
        <div class="info-row">
          <span class="label">任务名称：</span>
          <span class="value">{{ taskData?.taskName || '未知任务' }}</span>
        </div>
        <div class="info-row">
          <span class="label">任务类型：</span>
          <span class="value">{{ taskData?.taskType || '未设置' }}</span>
        </div>
        <div class="info-row">
          <span class="label">责任人：</span>
          <span class="value">{{ taskData?.responsiblePerson || '未分配' }}</span>
        </div>
      </div>

      <!-- 属性设置表单 -->
      <div class="property-form">
        <h4>属性设置</h4>
        <el-form :model="propertyForm" :rules="formRules" ref="formRef" label-width="140px">
          <el-form-item label="子任务属性名称设定" prop="propertyName" required>
            <el-input
              v-model="propertyForm.propertyName"
              placeholder="请输入"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="子任务属性类型" prop="propertyType" required>
            <div class="property-type-section">
              <el-select 
                v-model="propertyForm.propertyType" 
                placeholder="请选择，选项需要为创建类型保存的内容"
                style="width: 300px;"
              >
                <el-option
                  v-for="type in availablePropertyTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
              <el-button type="primary" @click="showCreateTypeDialog">创建类型</el-button>
            </div>
          </el-form-item>

          <el-form-item label="风险等级设定依据" prop="riskBasis" required>
            <el-input
              v-model="propertyForm.riskBasis"
              placeholder="请输入"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="子任务数值" prop="taskValue">
            <el-input-number
              v-model="propertyForm.taskValue"
              :min="0"
              :max="999999"
              placeholder="请输入"
              style="width: 200px;"
            />
          </el-form-item>

          <el-form-item label="子任务风险预警" prop="riskWarning">
            <el-switch
              v-model="propertyForm.riskWarning"
              active-text="启用"
              inactive-text="关闭"
            />
          </el-form-item>

          <el-form-item 
            v-if="propertyForm.riskWarning" 
            label="风险预警值" 
            prop="warningThreshold"
          >
            <div class="warning-threshold">
              <span>超出</span>
              <el-input-number
                v-model="propertyForm.warningThreshold"
                :min="1"
                :max="100"
                placeholder="请输入"
                style="width: 120px; margin: 0 8px;"
              />
              <el-select v-model="propertyForm.warningUnit" style="width: 100px;">
                <el-option label="天" value="days" />
                <el-option label="小时" value="hours" />
              </el-select>
              <span style="margin-left: 8px;">未响应</span>
            </div>
          </el-form-item>

          <el-form-item label="属性描述" prop="propertyDescription">
            <el-input
              v-model="propertyForm.propertyDescription"
              type="textarea"
              :rows="3"
              placeholder="请输入属性的详细描述"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮 -->
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">确认</el-button>
      </div>
    </div>

    <!-- 创建类型弹窗 -->
    <DialogComp
      v-model="createTypeVisible"
      title="子任务属性类型创建"
      width="500px"
      :visible-close-button="false"
      :visible-confirm-button="false"
      :close-on-click-modal="false"
    >
      <div class="create-type-content">
        <div class="type-input-section">
          <el-input
            v-model="newPropertyType"
            placeholder="请输入子任务属性类型，多个类型逗号隔开"
            maxlength="200"
            show-word-limit
          />
          <el-button 
            type="danger" 
            @click="clearNewPropertyType"
            style="margin-left: 10px;"
          >
            删除
          </el-button>
        </div>
        
        <el-button 
          type="primary" 
          @click="addPropertyType"
          style="margin-top: 16px; width: 100%;"
        >
          添加选项
        </el-button>

        <div class="dialog-footer">
          <el-button @click="createTypeVisible = false">取消</el-button>
          <el-button type="primary" @click="savePropertyTypes">确认</el-button>
        </div>
      </div>
    </DialogComp>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'

// Props
interface Props {
  modelValue: boolean
  taskData?: any
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  taskData: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirm: [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const formRef = ref<FormInstance>()
const createTypeVisible = ref(false)
const newPropertyType = ref('')

// 可用的属性类型
const availablePropertyTypes = ref([
  { label: '文本类型', value: 'text' },
  { label: '数值类型', value: 'number' },
  { label: '日期类型', value: 'date' },
  { label: '选择类型', value: 'select' },
  { label: '布尔类型', value: 'boolean' }
])

// 表单数据
const propertyForm = reactive({
  propertyName: '',
  propertyType: '',
  riskBasis: '',
  taskValue: 0,
  riskWarning: false,
  warningThreshold: 1,
  warningUnit: 'days',
  propertyDescription: ''
})

// 表单验证规则
const formRules: FormRules = {
  propertyName: [
    { required: true, message: '请输入属性名称', trigger: 'blur' }
  ],
  propertyType: [
    { required: true, message: '请选择属性类型', trigger: 'change' }
  ],
  riskBasis: [
    { required: true, message: '请输入风险等级设定依据', trigger: 'blur' }
  ]
}

// 方法
const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const propertyData = {
      taskId: props.taskData?.id,
      taskName: props.taskData?.taskName,
      ...propertyForm,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }
    
    emit('confirm', propertyData)
    visible.value = false
    
    ElMessage.success('属性设置保存成功')
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

const showCreateTypeDialog = () => {
  createTypeVisible.value = true
}

const clearNewPropertyType = () => {
  newPropertyType.value = ''
}

const addPropertyType = () => {
  if (!newPropertyType.value.trim()) {
    ElMessage.warning('请输入属性类型')
    return
  }
  
  const types = newPropertyType.value.split(',').map(type => type.trim()).filter(type => type)
  types.forEach(type => {
    if (!availablePropertyTypes.value.find(item => item.label === type)) {
      availablePropertyTypes.value.push({
        label: type,
        value: type.toLowerCase().replace(/\s+/g, '_')
      })
    }
  })
  
  newPropertyType.value = ''
  ElMessage.success(`成功添加 ${types.length} 个属性类型`)
}

const savePropertyTypes = () => {
  createTypeVisible.value = false
  ElMessage.success('属性类型保存成功')
}

// 重置表单
const resetForm = () => {
  propertyForm.propertyName = ''
  propertyForm.propertyType = ''
  propertyForm.riskBasis = ''
  propertyForm.taskValue = 0
  propertyForm.riskWarning = false
  propertyForm.warningThreshold = 1
  propertyForm.warningUnit = 'days'
  propertyForm.propertyDescription = ''
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 监听弹窗打开，重置表单
watch(visible, (newVal) => {
  if (newVal) {
    resetForm()
    // 根据任务信息设置默认值
    if (props.taskData?.taskName) {
      propertyForm.propertyName = `${props.taskData.taskName}_属性`
    }
  }
})
</script>

<style lang="scss" scoped>
.property-management-content {
  padding: 20px;

  .task-info {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 80px;
        color: #606266;
        font-size: 13px;
      }

      .value {
        color: #303133;
        font-size: 13px;
        font-weight: 500;
      }
    }
  }

  .property-form {
    h4 {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .property-type-section {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .warning-threshold {
      display: flex;
      align-items: center;
    }
  }

  .dialog-footer {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

.create-type-content {
  padding: 20px;

  .type-input-section {
    display: flex;
    align-items: center;
  }

  .dialog-footer {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
