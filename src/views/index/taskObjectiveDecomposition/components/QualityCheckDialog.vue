<!-- 子任务质量检查弹窗 -->
<template>
  <DialogComp
    v-model="visible"
    title="子任务质量检查"
    width="600px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="quality-check-content">
      <!-- 质量检查项目列表 -->
      <div class="check-items-section">
        <div 
          v-for="item in checkItems" 
          :key="item.id"
          class="check-item"
          :class="item.status"
        >
          <div class="check-icon">
            <el-icon v-if="item.status === 'success'">
              <Check />
            </el-icon>
            <el-icon v-else-if="item.status === 'warning'">
              <Warning />
            </el-icon>
            <el-icon v-else-if="item.status === 'error'">
              <Close />
            </el-icon>
          </div>
          <div class="check-text">
            {{ item.text }}
          </div>
        </div>
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Check, Warning, Close } from '@element-plus/icons-vue'
import DialogComp from '@/components/common/dialog-comp.vue'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 质量检查项目数据
const checkItems = ref([
  {
    id: 1,
    text: '按时完成数据录入',
    status: 'success'
  },
  {
    id: 2,
    text: '确保字段完整性',
    status: 'warning'
  },
  {
    id: 3,
    text: '配合审核修改',
    status: 'error'
  }
])

// 处理取消
const handleCancel = () => {
  visible.value = false
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 处理确认
const handleConfirm = () => {
  emit('confirm', {
    checkItems: checkItems.value
  })
  visible.value = false
}
</script>

<style lang="scss" scoped>
.quality-check-content {
  padding: 20px;

  .check-items-section {
    .check-item {
      display: flex;
      align-items: center;
      padding: 16px;
      margin-bottom: 12px;
      border-radius: 6px;
      gap: 12px;

      &.success {
        background: #f0f9ff;
        border: 1px solid #d1fae5;
        
        .check-icon {
          color: #10b981;
        }
      }

      &.warning {
        background: #fffbeb;
        border: 1px solid #fed7aa;
        
        .check-icon {
          color: #f59e0b;
        }
      }

      &.error {
        background: #fef2f2;
        border: 1px solid #fecaca;
        
        .check-icon {
          color: #ef4444;
        }
      }

      .check-icon {
        font-size: 18px;
        flex-shrink: 0;
      }

      .check-text {
        font-size: 14px;
        color: #303133;
        flex: 1;
      }
    }
  }
}

.dialog-footer {
  margin-top: 24px;
  text-align: right;
  
  .el-button {
    margin-left: 12px;
  }
}
</style>
