<template>
  <DialogComp
    v-model="visible"
    title="时长分析"
    width="700px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="duration-analysis-content">
      <!-- 时长分析表格 -->
      <div class="duration-table">
        <el-table 
          :data="durationData" 
          style="width: 100%" 
          :loading="loading"
          border
          stripe
        >
          <el-table-column prop="stage" label="环节" width="150" align="center" />
          <el-table-column prop="startTime" label="开始时间" width="160" align="center" />
          <el-table-column prop="endTime" label="结束时间" width="160" align="center" />
          <el-table-column prop="duration" label="耗时" width="120" align="center">
            <template #default="{ row }">
              <span class="duration-text" :class="getDurationClass(row.durationHours)">
                {{ row.duration }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag 
                :type="getStatusType(row.status)" 
                size="small"
              >
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 统计信息 -->
      <div class="statistics-section">
        <h4>统计信息</h4>
        <div class="statistics-grid">
          <div class="stat-item">
            <label class="stat-label">总计时长：</label>
            <span class="stat-value total-duration">{{ totalDuration }}</span>
          </div>
          <div class="stat-item">
            <label class="stat-label">平均时长：</label>
            <span class="stat-value average-duration">{{ averageDuration }}</span>
          </div>
          <div class="stat-item">
            <label class="stat-label">已完成环节：</label>
            <span class="stat-value completed-count">{{ completedStages }}/{{ totalStages }}</span>
          </div>
          <div class="stat-item">
            <label class="stat-label">预计剩余时长：</label>
            <span class="stat-value remaining-duration">{{ estimatedRemaining }}</span>
          </div>
        </div>
      </div>

      <!-- 分析说明 -->
      <div class="analysis-notes" v-if="showNotes">
        <h4>分析说明</h4>
        <ul>
          <li><strong>耗时统计</strong>：基于实际开始和结束时间计算各环节耗时</li>
          <li><strong>状态标识</strong>：已完成（绿色）、进行中（蓝色）、未开始（灰色）</li>
          <li><strong>预计剩余</strong>：基于已完成环节的平均耗时估算剩余时长</li>
        </ul>
      </div>

      <!-- 底部按钮 -->
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </div>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'

// 接口定义
interface DurationItem {
  stage: string
  startTime: string
  endTime: string
  duration: string
  durationHours: number
  status: '已完成' | '进行中' | '未开始'
}

interface DurationAnalysisData {
  durationData: DurationItem[]
  totalDuration: string
  averageDuration: string
  completedStages: number
  totalStages: number
  estimatedRemaining: string
}

// Props
const props = defineProps<{
  modelValue: boolean
  taskData?: any
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: DurationAnalysisData]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const showNotes = ref(false)

// 时长分析数据
const durationData = ref<DurationItem[]>([
  {
    stage: '任务创建',
    startTime: '2024-01-01 09:00',
    endTime: '2024-01-01 09:30',
    duration: '0.5小时',
    durationHours: 0.5,
    status: '已完成'
  },
  {
    stage: '需求分析',
    startTime: '2024-01-01 10:00',
    endTime: '2024-01-01 12:00',
    duration: '2小时',
    durationHours: 2,
    status: '已完成'
  },
  {
    stage: '方案设计',
    startTime: '2024-01-01 14:00',
    endTime: '2024-01-01 17:00',
    duration: '3小时',
    durationHours: 3,
    status: '已完成'
  },
  {
    stage: '开发实施',
    startTime: '2024-01-02 09:00',
    endTime: '2024-01-02 18:00',
    duration: '8小时',
    durationHours: 8,
    status: '已完成'
  },
  {
    stage: '测试验证',
    startTime: '2024-01-03 09:00',
    endTime: '-',
    duration: '进行中',
    durationHours: 0,
    status: '进行中'
  },
  {
    stage: '部署上线',
    startTime: '-',
    endTime: '-',
    duration: '未开始',
    durationHours: 0,
    status: '未开始'
  },
  {
    stage: '验收确认',
    startTime: '-',
    endTime: '-',
    duration: '未开始',
    durationHours: 0,
    status: '未开始'
  }
])

// 计算属性
const totalDuration = computed(() => {
  const total = durationData.value
    .filter(item => item.status === '已完成')
    .reduce((sum, item) => sum + item.durationHours, 0)
  return `${total}小时`
})

const averageDuration = computed(() => {
  const completedItems = durationData.value.filter(item => item.status === '已完成')
  if (completedItems.length === 0) return '0小时'
  
  const average = completedItems.reduce((sum, item) => sum + item.durationHours, 0) / completedItems.length
  return `${average.toFixed(1)}小时`
})

const completedStages = computed(() => {
  return durationData.value.filter(item => item.status === '已完成').length
})

const totalStages = computed(() => {
  return durationData.value.length
})

const estimatedRemaining = computed(() => {
  const completedItems = durationData.value.filter(item => item.status === '已完成')
  const remainingItems = durationData.value.filter(item => item.status !== '已完成')
  
  if (completedItems.length === 0 || remainingItems.length === 0) return '0小时'
  
  const averageHours = completedItems.reduce((sum, item) => sum + item.durationHours, 0) / completedItems.length
  const estimated = averageHours * remainingItems.length
  
  return `${estimated.toFixed(1)}小时`
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = () => {
  // 添加100毫秒延迟效果
  loading.value = true
  setTimeout(() => {
    const analysisData: DurationAnalysisData = {
      durationData: durationData.value,
      totalDuration: totalDuration.value,
      averageDuration: averageDuration.value,
      completedStages: completedStages.value,
      totalStages: totalStages.value,
      estimatedRemaining: estimatedRemaining.value
    }
    
    emit('confirm', analysisData)
    visible.value = false
    loading.value = false
    
    ElMessage.success('时长分析确认成功')
  }, 100)
}

const getDurationClass = (hours: number) => {
  if (hours === 0) return 'pending'
  if (hours <= 2) return 'short'
  if (hours <= 6) return 'medium'
  return 'long'
}

const getStatusType = (status: string) => {
  switch (status) {
    case '已完成':
      return 'success'
    case '进行中':
      return 'primary'
    case '未开始':
      return 'info'
    default:
      return 'info'
  }
}

// 根据任务数据动态分析时长
const analyzeDuration = (taskData: any) => {
  if (!taskData) return

  // 模拟根据任务数据进行时长分析
  const taskType = taskData.type || '报表'
  const complexity = taskData.complexity || '中等'

  // 根据任务类型和复杂度调整时长估算
  if (taskType === '报表' && complexity === '高') {
    // 调整开发实施时长
    const devIndex = durationData.value.findIndex(item => item.stage === '开发实施')
    if (devIndex !== -1) {
      durationData.value[devIndex].duration = '12小时'
      durationData.value[devIndex].durationHours = 12
    }
  }

  // 根据任务创建时间调整开始时间
  if (taskData.createTime) {
    const createDate = new Date(taskData.createTime)
    durationData.value[0].startTime = createDate.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/\//g, '-')
  }
}

// 监听弹窗打开，分析时长数据
watch(visible, (newVal) => {
  if (newVal) {
    analyzeDuration(props.taskData)
  }
})

onMounted(() => {
  if (props.taskData) {
    analyzeDuration(props.taskData)
  }
})
</script>

<style lang="scss" scoped>
.duration-analysis-content {
  padding: 20px;

  .duration-table {
    margin-bottom: 24px;

    .duration-text {
      font-weight: 500;

      &.pending {
        color: #909399;
      }

      &.short {
        color: #67C23A;
      }

      &.medium {
        color: #E6A23C;
      }

      &.long {
        color: #F56C6C;
      }
    }
  }

  .statistics-section {
    margin-bottom: 24px;
    padding: 16px;
    background: #f5f7fa;
    border-radius: 6px;

    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .statistics-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .stat-label {
          font-weight: 500;
          color: #606266;
          flex-shrink: 0;
        }

        .stat-value {
          font-weight: 600;
          font-size: 16px;

          &.total-duration {
            color: #409EFF;
          }

          &.average-duration {
            color: #67C23A;
          }

          &.completed-count {
            color: #E6A23C;
          }

          &.remaining-duration {
            color: #F56C6C;
          }
        }
      }
    }
  }

  .analysis-notes {
    margin-bottom: 24px;
    padding: 16px;
    background: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 6px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #0066cc;
    }

    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        font-size: 14px;
        color: #606266;
        line-height: 1.6;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        strong {
          color: #303133;
        }
      }
    }
  }

  .dialog-footer {
    margin-top: 24px;
    text-align: right;
    
    .el-button {
      margin-left: 12px;
    }
  }
}
</style>
