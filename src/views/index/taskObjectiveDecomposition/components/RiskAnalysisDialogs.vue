<template>
  <!-- 子任务风险分析弹窗 -->
  <el-dialog
    v-model="riskAnalysisVisible"
    title="子任务风险分析"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="risk-analysis-content">
      <!-- 操作按钮 -->
      <div class="risk-analysis-header">
        <el-button type="primary" @click="handleExport">导出</el-button>
        <el-button @click="openRiskProcessingDialog">风险处理</el-button>
      </div>

      <!-- 风险分析表格 -->
      <div class="risk-analysis-table" v-loading="analysisLoading" element-loading-text="风险分析中...">
        <el-table :data="riskData" style="width: 100%" v-if="!analysisLoading">
          <el-table-column prop="riskItem" label="风险项" min-width="200" />
          <el-table-column prop="probability" label="发生概率 (P)" width="120" align="center" />
          <el-table-column prop="impact" label="影响程度 (I)" width="120" align="center" />
          <el-table-column prop="riskLevel" label="风险等级" width="100" align="center">
            <template #default="{ row }">
              <el-tag
                :type="row.riskLevel === '高' ? 'danger' : row.riskLevel === '中高' ? 'warning' : 'info'"
              >
                {{ row.riskLevel }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="80" align="center">
            <template #default="{ row }">
              <el-tag type="info" size="small" round>{{ row.priority }}</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="riskAnalysisVisible = false">取消</el-button>
        <el-button type="primary" @click="riskAnalysisVisible = false">确认</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 子任务风险处理弹窗 -->
  <el-dialog
    v-model="riskProcessingVisible"
    title="子任务风险处理"
    width="900px"
    :close-on-click-modal="false"
  >
    <div class="risk-processing-content">
      <!-- 风险分析表格 -->
      <div class="risk-processing-table">
        <el-table :data="riskData" style="width: 100%">
          <el-table-column prop="riskItem" label="风险项" min-width="200" />
          <el-table-column prop="probability" label="发生概率 (P)" width="120" align="center" />
          <el-table-column prop="impact" label="影响程度 (I)" width="120" align="center" />
          <el-table-column prop="riskLevel" label="风险等级" width="100" align="center">
            <template #default="{ row }">
              <el-tag
                :type="row.riskLevel === '高' ? 'danger' : row.riskLevel === '中高' ? 'warning' : 'info'"
              >
                {{ row.riskLevel }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="80" align="center">
            <template #default="{ row }">
              <el-tag type="info" size="small" round>{{ row.priority }}</el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 子任务处理 -->
      <div class="subtask-processing">
        <div class="processing-title">
          <span class="required-mark">*</span>子任务处理
        </div>
        <div class="processing-options">
          <span class="options-label">下拉单选：</span>
          <el-select
            v-model="selectedProcessingOption"
            placeholder="请选择处理方式"
            style="width: 200px"
          >
            <el-option
              v-for="option in processingOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>
      </div>

      <!-- 子任务处理知识库搜索 -->
      <div class="knowledge-search">
        <div class="search-title">子任务处理知识库搜索</div>
        <div class="search-input-group">
          <el-input
            v-model="searchInput"
            placeholder="请输入风险项"
            style="width: 300px; margin-right: 12px"
          />
          <el-button type="primary" @click="handleKnowledgeSearch" :loading="searchLoading">
            搜索
          </el-button>
        </div>
        <div class="search-result" v-if="searchResult" v-loading="searchLoading">
          <div class="result-title">搜索结果：</div>
          <div class="result-content">{{ searchResult }}</div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="riskProcessingVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmProcessing">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { riskAnalysisData } from '../data/mockData'
import { subTaskProcessingOptions } from '../config/tableConfig'
import { exportRiskAnalysis } from '../utils/exportUtils'
import { getKnowledgeSearchResult } from '../data/mockData'

// Props
interface Props {
  taskName?: string
}

const props = defineProps<Props>()

// 弹窗状态
const riskAnalysisVisible = ref(false)
const riskProcessingVisible = ref(false)

// 加载状态
const analysisLoading = ref(false)
const searchLoading = ref(false)

// 数据
const riskData = ref(riskAnalysisData)
const selectedProcessingOption = ref('')
const searchInput = ref('')
const searchResult = ref('')

// 处理选项
const processingOptions = subTaskProcessingOptions

// 打开风险分析弹窗
const openRiskAnalysisDialog = () => {
  riskAnalysisVisible.value = true
  analysisLoading.value = true

  // 模拟风险分析过程，3-5秒后显示结果
  setTimeout(() => {
    analysisLoading.value = false
  }, Math.random() * 2000 + 3000) // 3-5秒随机延迟
}

// 打开风险处理弹窗
const openRiskProcessingDialog = () => {
  riskProcessingVisible.value = true
  selectedProcessingOption.value = ''
  searchInput.value = ''
  searchResult.value = ''
}

// 导出风险分析
const handleExport = () => {
  exportRiskAnalysis(riskData.value, props.taskName)
}

// 知识搜索
const handleKnowledgeSearch = () => {
  if (!searchInput.value.trim()) {
    ElMessage.warning('请输入风险项')
    return
  }

  searchLoading.value = true
  searchResult.value = ''

  // 模拟知识搜索过程，3-6秒后显示结果
  setTimeout(() => {
    searchLoading.value = false
    searchResult.value = getKnowledgeSearchResult(searchInput.value)
  }, Math.random() * 3000 + 3000) // 3-6秒随机延迟
}

// 确认处理
const handleConfirmProcessing = () => {
  if (!selectedProcessingOption.value) {
    ElMessage.warning('请选择处理方式')
    return
  }

  ElMessage.success('风险处理设置成功')
  riskProcessingVisible.value = false
}

// 暴露方法给父组件
defineExpose({
  openRiskAnalysisDialog,
  openRiskProcessingDialog
})
</script>

<style scoped>
.risk-analysis-content {
  padding: 20px 0;
}

.risk-analysis-header {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
}

.risk-analysis-table {
  min-height: 200px;
}

.risk-processing-content {
  padding: 20px 0;
}

.risk-processing-table {
  margin-bottom: 30px;
}

.subtask-processing {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.processing-title {
  margin-bottom: 16px;
  font-weight: 500;
  color: #333;
}

.required-mark {
  color: #f56c6c;
  margin-right: 4px;
}

.processing-options {
  display: flex;
  align-items: center;
  gap: 12px;
}

.options-label {
  color: #666;
  font-size: 14px;
}

.knowledge-search {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.search-title {
  margin-bottom: 16px;
  font-weight: 500;
  color: #333;
}

.search-input-group {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.search-result {
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.result-title {
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.result-content {
  color: #666;
  line-height: 1.6;
}

.dialog-footer {
  text-align: right;
}
</style>
