<template>
  <DialogComp
    v-model="visible"
    title="样式模板设置"
    width="500px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="style-template-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="140px"
        label-position="right"
      >
        <el-form-item label="样式模板名称" prop="templateName">
          <el-input
            v-model="formData.templateName"
            placeholder="请输入名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="执行中配色" prop="inProgressColor">
          <div class="color-input-group">
            <el-input
              v-model="formData.inProgressColor"
              placeholder="请输入颜色的十六进制值"
              clearable
            />
            <el-color-picker
              v-model="formData.inProgressColor"
              show-alpha
              :predefine="predefineColors"
            />
          </div>
        </el-form-item>

        <el-form-item label="已完成配色" prop="completedColor">
          <div class="color-input-group">
            <el-input
              v-model="formData.completedColor"
              placeholder="请输入颜色的十六进制值"
              clearable
            />
            <el-color-picker
              v-model="formData.completedColor"
              show-alpha
              :predefine="predefineColors"
            />
          </div>
        </el-form-item>
      </el-form>

      <!-- 预览区域 -->
      <div class="preview-section">
        <h4>预览效果</h4>
        <div class="progress-preview">
          <div class="progress-item">
            <span class="progress-label">执行中：</span>
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ backgroundColor: formData.inProgressColor, width: '60%' }"
              ></div>
            </div>
            <span class="progress-text">60%</span>
          </div>
          <div class="progress-item">
            <span class="progress-label">已完成：</span>
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ backgroundColor: formData.completedColor, width: '100%' }"
              ></div>
            </div>
            <span class="progress-text">100%</span>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </div>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'

// 接口定义
interface StyleTemplate {
  id?: string
  templateName: string
  inProgressColor: string
  completedColor: string
  createTime?: string
  index?: number
}

// Props
const props = defineProps<{
  modelValue: boolean
  templateData?: StyleTemplate | null
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: StyleTemplate]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()

// 表单数据
const formData = ref<StyleTemplate>({
  templateName: '',
  inProgressColor: '#409EFF',
  completedColor: '#67C23A'
})

// 预定义颜色
const predefineColors = ref([
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  '#409EFF',
  '#67C23A',
  '#E6A23C',
  '#F56C6C',
  '#909399'
])

// 颜色验证函数 - 支持更多颜色格式
const validateColor = (_rule: any, value: string, callback: any) => {
  if (!value) {
    callback(new Error('请输入颜色值'))
    return
  }

  // 支持的颜色格式：
  // #fff (3位)
  // #ffffff (6位)
  // #ffffff80 (8位，带透明度)
  // 大小写不敏感
  const colorPattern = /^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/

  if (!colorPattern.test(value)) {
    callback(new Error('请输入正确的十六进制颜色值（如：#fff、#ffffff、#ffffff80）'))
    return
  }

  callback()
}

// 表单验证规则
const formRules: FormRules = {
  templateName: [
    { required: true, message: '请输入样式模板名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  inProgressColor: [
    { required: true, message: '请输入执行中配色', trigger: 'blur' },
    { validator: validateColor, trigger: 'blur' }
  ],
  completedColor: [
    { required: true, message: '请输入已完成配色', trigger: 'blur' },
    { validator: validateColor, trigger: 'blur' }
  ]
}

// 方法
const handleClose = () => {
  visible.value = false
  resetForm()
}

const handleCancel = () => {
  visible.value = false
  resetForm()
}

const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    // 添加100毫秒延迟效果
    setTimeout(() => {
      const templateData: StyleTemplate = {
        ...formData.value,
        id: props.templateData?.id || Date.now().toString(),
        createTime: props.templateData?.createTime || new Date().toISOString().split('T')[0]
      }

      // 如果是编辑模式，保留index
      if (props.templateData?.index !== undefined) {
        templateData.index = props.templateData.index
      }

      emit('confirm', templateData)
      visible.value = false
      resetForm()
      
      ElMessage.success('样式模板保存成功')
    }, 100)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const resetForm = () => {
  formData.value = {
    templateName: '',
    inProgressColor: '#409EFF',
    completedColor: '#67C23A'
  }
  formRef.value?.clearValidate()
}

// 监听弹窗打开，加载数据
watch(visible, (newVal) => {
  if (newVal && props.templateData) {
    formData.value = {
      templateName: props.templateData.templateName,
      inProgressColor: props.templateData.inProgressColor,
      completedColor: props.templateData.completedColor
    }
  } else if (newVal) {
    resetForm()
  }
})
</script>

<style lang="scss" scoped>
.style-template-content {
  padding: 20px;

  .color-input-group {
    display: flex;
    align-items: center;
    gap: 12px;

    .el-input {
      flex: 1;
    }
  }

  .preview-section {
    margin: 24px 0;
    padding: 16px;
    background: #f5f7fa;
    border-radius: 6px;

    h4 {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .progress-preview {
      .progress-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        gap: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .progress-label {
          width: 80px;
          font-size: 14px;
          color: #606266;
        }

        .progress-bar {
          flex: 1;
          height: 20px;
          background: #f0f2f5;
          border-radius: 10px;
          overflow: hidden;
          position: relative;

          .progress-fill {
            height: 100%;
            border-radius: 10px;
            transition: all 0.3s ease;
          }
        }

        .progress-text {
          width: 40px;
          font-size: 12px;
          color: #606266;
          text-align: right;
        }
      }
    }
  }

  .dialog-footer {
    margin-top: 24px;
    text-align: right;
    
    .el-button {
      margin-left: 12px;
    }
  }
}
</style>
