<!-- 子任务成本估算弹窗 -->
<template>
  <DialogComp
    v-model="visible"
    title="子任务成本估算"
    width="500px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="cost-estimation-content">
      <!-- 成本估算结果 -->
      <div class="estimation-result">
        <p class="estimation-text">
          预计设计7个部门，14位工作人员或数据领导参与任务，平均每人投入10分钟/天，预计需要5天完成。
        </p>
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import DialogComp from '@/components/common/dialog-comp.vue'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 处理确认
const handleConfirm = () => {
  emit('confirm', {
    estimationResult: '预计设计7个部门，14位工作人员或数据领导参与任务，平均每人投入10分钟/天，预计需要5天完成。'
  })
  visible.value = false
}
</script>

<style lang="scss" scoped>
.cost-estimation-content {
  padding: 20px;

  .estimation-result {
    .estimation-text {
      font-size: 14px;
      line-height: 1.6;
      color: #303133;
      margin: 0;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 6px;
      border-left: 4px solid #409eff;
    }
  }
}

.dialog-footer {
  margin-top: 24px;
  text-align: right;
  
  .el-button {
    margin-left: 12px;
  }
}
</style>
