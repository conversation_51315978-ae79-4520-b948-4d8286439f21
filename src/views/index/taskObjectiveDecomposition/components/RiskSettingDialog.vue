<template>
  <DialogComp
    v-model="visible"
    title="子任务风险设定"
    width="700px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="risk-setting-content">
      <!-- 任务信息 -->
      <div class="task-info">
        <h4>任务信息</h4>
        <div class="info-row">
          <span class="label">任务名称：</span>
          <span class="value">{{ taskData?.taskName || '未知任务' }}</span>
        </div>
        <div class="info-row">
          <span class="label">责任人：</span>
          <span class="value">{{ taskData?.responsiblePerson || '未分配' }}</span>
        </div>
        <div class="info-row">
          <span class="label">当前进度：</span>
          <span class="value">{{ taskData?.progress || 0 }}%</span>
        </div>
      </div>

      <!-- 风险设定表单 -->
      <div class="risk-form">
        <h4>风险设定</h4>
        <el-form :model="riskForm" :rules="formRules" ref="formRef" label-width="140px">
          <el-form-item label="子任务风险等级" prop="riskLevel" required>
            <el-select v-model="riskForm.riskLevel" placeholder="下拉单选，低风险、中风险、高风险">
              <el-option label="低风险" value="low" />
              <el-option label="中风险" value="medium" />
              <el-option label="高风险" value="high" />
            </el-select>
          </el-form-item>

          <el-form-item label="风险等级设定依据" prop="riskBasis" required>
            <el-input
              v-model="riskForm.riskBasis"
              placeholder="请输入"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="子任务风险预警" prop="riskWarning">
            <el-switch
              v-model="riskForm.riskWarning"
              active-text="启用"
              inactive-text="关闭"
            />
          </el-form-item>

          <el-form-item 
            v-if="riskForm.riskWarning" 
            label="风险预警值" 
            prop="warningThreshold"
          >
            <div class="warning-threshold">
              <span>超出</span>
              <el-input-number
                v-model="riskForm.warningThreshold"
                :min="1"
                :max="100"
                placeholder="请输入"
                style="width: 120px; margin: 0 8px;"
              />
              <el-select v-model="riskForm.warningUnit" style="width: 100px;">
                <el-option label="天" value="days" />
                <el-option label="小时" value="hours" />
              </el-select>
              <span style="margin-left: 8px;">未响应</span>
            </div>
          </el-form-item>

          <el-form-item label="风险描述" prop="riskDescription">
            <el-input
              v-model="riskForm.riskDescription"
              type="textarea"
              :rows="3"
              placeholder="请描述可能的风险点和影响"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="风险应对措施" prop="riskMitigation">
            <el-input
              v-model="riskForm.riskMitigation"
              type="textarea"
              :rows="3"
              placeholder="请输入风险应对和缓解措施"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="风险负责人" prop="riskOwner">
            <el-select
              v-model="riskForm.riskOwner"
              placeholder="请选择风险负责人"
              style="width: 100%"
            >
              <el-option
                v-for="person in availablePersons"
                :key="person.id"
                :label="`${person.name} (${person.department})`"
                :value="person.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮 -->
      <div class="dialog-footer">
        <el-button @click="handleCancel" :disabled="loading">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">确认</el-button>
      </div>
    </div>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'
import { useTaskObjectiveStore } from '@/stores/taskObjectiveStore'

// Props
interface Props {
  modelValue: boolean
  taskData?: any
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  taskData: null
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirm: [data: any]
}>()

// Store
const taskStore = useTaskObjectiveStore()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const riskForm = reactive({
  riskLevel: '',
  riskBasis: '',
  riskWarning: false,
  warningThreshold: 1,
  warningUnit: 'days',
  riskDescription: '',
  riskMitigation: '',
  riskOwner: ''
})

// 表单验证规则
const formRules: FormRules = {
  riskLevel: [
    { required: true, message: '请选择风险等级', trigger: 'change' }
  ],
  riskBasis: [
    { required: true, message: '请输入风险等级设定依据', trigger: 'blur' }
  ],
  warningThreshold: [
    { required: true, message: '请输入风险预警值', trigger: 'blur' }
  ]
}

// 可选择的人员列表
const availablePersons = computed(() => {
  return taskStore.personnelData
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    loading.value = true
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const riskData = {
      taskId: props.taskData?.id,
      taskName: props.taskData?.taskName,
      ...riskForm,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }
    
    emit('confirm', riskData)
    visible.value = false
    
    ElMessage.success('风险设定保存成功')
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  riskForm.riskLevel = ''
  riskForm.riskBasis = ''
  riskForm.riskWarning = false
  riskForm.warningThreshold = 1
  riskForm.warningUnit = 'days'
  riskForm.riskDescription = ''
  riskForm.riskMitigation = ''
  riskForm.riskOwner = ''
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 监听弹窗打开，重置表单并设置默认值
watch(visible, (newVal) => {
  if (newVal) {
    resetForm()
    // 根据任务进度设置默认风险等级
    if (props.taskData?.progress) {
      const progress = props.taskData.progress
      if (progress < 30) {
        riskForm.riskLevel = 'high'
      } else if (progress < 70) {
        riskForm.riskLevel = 'medium'
      } else {
        riskForm.riskLevel = 'low'
      }
    }
    
    // 默认选择任务责任人作为风险负责人
    if (props.taskData?.responsiblePerson) {
      const responsiblePerson = availablePersons.value.find(
        person => person.name === props.taskData.responsiblePerson
      )
      if (responsiblePerson) {
        riskForm.riskOwner = responsiblePerson.id
      }
    }
  }
})
</script>

<style lang="scss" scoped>
.risk-setting-content {
  padding: 20px;

  .task-info {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 80px;
        color: #606266;
        font-size: 13px;
      }

      .value {
        color: #303133;
        font-size: 13px;
        font-weight: 500;
      }
    }
  }

  .risk-form {
    h4 {
      margin: 0 0 16px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .warning-threshold {
      display: flex;
      align-items: center;
    }
  }

  .dialog-footer {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
