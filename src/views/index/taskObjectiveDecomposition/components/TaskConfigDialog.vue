<!-- 子任务属性明细配置弹窗 -->
<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useTaskObjectiveStore } from '@/stores/taskObjectiveStore'
import type { TaskConfig } from '@/stores/taskObjectiveStore'
import FileUploadDialog from './FileUploadDialog.vue'
import ParticipantPermissionDialog from './ParticipantPermissionDialog.vue'

interface Props {
  modelValue: boolean
  task: any
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'save', formData: TaskConfig): void
  (e: 'open-business-report-select'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const taskStore = useTaskObjectiveStore()

// 弹窗状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 子弹窗状态
const fileUploadVisible = ref(false)
const participantPermissionVisible = ref(false)

// 表单数据
const formData = reactive<TaskConfig>({
  taskType: '业务报表子任务',
  taskName: '',
  taskCategory: '党的建设',
  relatedBusinessReport: '',
  businessReportTemplate: null,
  taskDescription: '',
  taskPriority: 'medium',
  responsiblePerson: '',
  participants: [],
  dateFormat: '周',
  timeFormat: '时分秒',
  taskTimezone: 'UTC+08:00_Beijing',
  startTime: '',
  endTime: '',
  reminderTime: ''
})

// 表单引用
const formRef = ref()

// 选项数据
const taskTypeOptions = [
  { label: '业务报表子任务', value: '业务报表子任务' },
  { label: '临时报表子任务', value: '临时报表子任务' }
]

const taskCategoryOptions = [
  { label: '党的建设', value: '党的建设' },
  { label: '经济发展', value: '经济发展' },
  { label: '民生服务', value: '民生服务' },
  { label: '平安法治', value: '平安法治' }
]

const priorityOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' }
]

const dateFormatOptions = [
  { label: '周', value: '周' },
  { label: '月', value: '月' },
  { label: '季度', value: '季度' },
  { label: '年', value: '年' }
]

const timeFormatOptions = [
  { label: '时分秒', value: '时分秒' },
  { label: '时分', value: '时分' }
]

// 表单验证规则
const rules = {
  taskType: [{ required: true, message: '请选择子任务类型', trigger: 'change' }],
  taskName: [{ required: true, message: '请输入子任务名称', trigger: 'blur' }],
  taskCategory: [{ required: true, message: '请选择子任务分类', trigger: 'change' }],
  relatedBusinessReport: [{ required: true, message: '请选择关联业务报表', trigger: 'change' }],
  taskPriority: [{ required: true, message: '请选择子任务优先级', trigger: 'change' }],
  responsiblePerson: [{ required: true, message: '请选择责任人', trigger: 'change' }],
  taskTimezone: [{ required: true, message: '请选择任务时区', trigger: 'change' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  endTime: [{ required: true, message: '请选择结束时间', trigger: 'change' }],
  reminderTime: [{ required: true, message: '请选择提醒时间', trigger: 'change' }]
}

// 监听弹窗打开
watch(visible, (newVal) => {
  if (newVal && props.task) {
    // 加载已保存的配置
    const savedConfig = taskStore.getTaskConfig(props.task.id)
    if (savedConfig) {
      Object.assign(formData, savedConfig)
    } else {
      // 重置表单
      resetForm()
    }
  }
})

// 方法
const resetForm = () => {
  Object.assign(formData, {
    taskType: '业务报表子任务',
    taskName: '',
    taskCategory: '党的建设',
    relatedBusinessReport: '',
    businessReportTemplate: null,
    taskDescription: '',
    taskPriority: 'medium',
    responsiblePerson: '',
    participants: [],
    dateFormat: '周',
    timeFormat: '时分秒',
    taskTimezone: 'UTC+08:00_Beijing',
    startTime: '',
    endTime: '',
    reminderTime: ''
  })
  formRef.value?.clearValidate()
}

const handleSelectBusinessReport = () => {
  emit('open-business-report-select')
}

const handleImportTemplate = () => {
  fileUploadVisible.value = true
}

const handleConfigParticipantPermission = () => {
  participantPermissionVisible.value = true
}

const handleFileUpload = (file: File) => {
  formData.businessReportTemplate = file
  fileUploadVisible.value = false
  ElMessage.success('业务报表信息模板导入成功')
}

const handleParticipantPermissionSave = (participants: any[]) => {
  formData.participants = participants.map(p => p.name)
  participantPermissionVisible.value = false
  ElMessage.success('参与人权限配置保存成功')
}

const handleBusinessReportConfirm = (selectedReports: any[]) => {
  if (selectedReports.length > 0) {
    // 将所有选中的业务报表名称用逗号连接
    formData.relatedBusinessReport = selectedReports.map(report => report.reportName).join('、')
    ElMessage.success(`已选择 ${selectedReports.length} 个业务报表`)
  }
}

const handleCancel = () => {
  visible.value = false
}

const handleSaveAsDraft = () => {
  // 保存为草稿，不进行表单验证
  emit('save', { ...formData })
  ElMessage.success('已保存为草稿')
}

const handleSave = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      emit('save', { ...formData })
    } else {
      ElMessage.error('请完善必填信息')
    }
  })
}

// 获取人员选项
const personnelOptions = computed(() => {
  return taskStore.personnelData.map(person => ({
    label: person.name,
    value: person.id
  }))
})

// 动态生成第三个配置项的标题
const relatedReportLabel = computed(() => {
  return formData.taskType === '业务报表子任务' ? '关联业务报表' : '关联临时报表'
})

// 动态生成第三个配置项的placeholder文本
const relatedReportPlaceholder = computed(() => {
  return formData.taskType === '业务报表子任务' ? '请选择业务报表' : '请选择临时报表'
})

// 暴露方法给父组件
defineExpose({
  handleBusinessReportConfirm
})
</script>

<template>
  <DialogComp
    v-model="visible"
    title="子任务属性明细配置"
    width="500px"
    :close-on-click-modal="false"
    :visible-footer-button="false"
    @close="handleCancel"
  >
    <div class="modal-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="140px"
        label-position="right"
        class="task-config-form"
      >
        <!-- 子任务类型 -->
        <el-form-item label="子任务类型" prop="taskType" required>
          <el-select v-model="formData.taskType" placeholder="下拉单选：业务报表子任务、临时报表子任务" style="width: 100%">
            <el-option
              v-for="option in taskTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 子任务名称 -->
        <el-form-item label="子任务名称" prop="taskName" required>
          <el-input v-model="formData.taskName" placeholder="请输入当前子任务名称" />
        </el-form-item>

        <!-- 子任务分类 -->
        <el-form-item label="子任务分类" prop="taskCategory" required>
          <el-select v-model="formData.taskCategory" placeholder="下拉单选：党的建设" style="width: 100%">
            <el-option
              v-for="option in taskCategoryOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 关联业务报表 -->
        <el-form-item :label="relatedReportLabel" prop="relatedBusinessReport" required>
          <div class="flex-row">
            <el-input v-model="formData.relatedBusinessReport" :placeholder="relatedReportPlaceholder" readonly />
            <el-button type="primary" @click="handleSelectBusinessReport">选择</el-button>
          </div>
        </el-form-item>

        <!-- 业务报表信息模板 -->
        <el-form-item label="业务报表信息模板">
          <div class="flex-row">
            <span v-if="formData.businessReportTemplate">
              {{ formData.businessReportTemplate.name }}
            </span>
            <span v-else class="placeholder-text">请选择文件</span>
            <el-button type="primary" @click="handleImportTemplate">导入</el-button>
          </div>
        </el-form-item>

        <!-- 子任务描述 -->
        <el-form-item label="子任务描述">
          <el-input
            v-model="formData.taskDescription"
            type="textarea"
            :rows="3"
          placeholder="请输入"
          />
        </el-form-item>

        <!-- 子任务优先级设置 -->
        <el-form-item label="子任务优先级设置" prop="taskPriority" required>
          <el-select v-model="formData.taskPriority" placeholder="下拉单选：低中高" style="width: 100%">
            <el-option
              v-for="option in priorityOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 责任人 -->
        <el-form-item label="责任人" prop="responsiblePerson" required>
          <el-select v-model="formData.responsiblePerson" placeholder="请选择人员（范围为本部门）" style="width: 100%">
            <el-option
              v-for="option in personnelOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 参与人 -->
        <el-form-item label="参与人">
          <div class="flex-row">
            <span v-if="formData.participants.length > 0">
              {{ formData.participants.join(', ') }}
            </span>
            <span v-else class="placeholder-text">请选择人员（范围为本部门）</span>
            <el-button type="primary" @click="handleConfigParticipantPermission">
              参与人权限配置
            </el-button>
          </div>
        </el-form-item>

        <!-- 日期格式设置 -->
        <el-form-item label="日期格式设置">
          <el-select v-model="formData.dateFormat" placeholder="下拉单选：周、月、季度、年" style="width: 100%">
            <el-option
              v-for="option in dateFormatOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 时间格式设置 -->
        <el-form-item label="时间格式设置">
          <el-select v-model="formData.timeFormat" placeholder="下拉单选：时分秒、时分" style="width: 100%">
            <el-option
              v-for="option in timeFormatOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 任务时区设置 -->
        <el-form-item label="任务时区设置" prop="taskTimezone" required>
          <el-select v-model="formData.taskTimezone" placeholder="下拉单选" style="width: 100%">
            <el-option
              v-for="option in taskStore.timezoneOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 时间设置 -->
        <el-form-item label="开始时间" prop="startTime" required>
          <el-date-picker
            v-model="formData.startTime"
            type="datetime"
            placeholder="请选择日期"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="结束时间" prop="endTime" required>
          <el-date-picker
            v-model="formData.endTime"
            type="datetime"
            placeholder="请选择时间"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="提醒时间" prop="reminderTime" required>
          <el-date-picker
            v-model="formData.reminderTime"
            type="datetime"
            placeholder="请选择时间"
            style="width: 100%"
          />
        </el-form-item>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleSaveAsDraft">存为草稿</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
      </el-form>
    </div>



    <!-- 文件上传弹窗 -->
    <FileUploadDialog
      v-model="fileUploadVisible"
      @upload="handleFileUpload"
    />

    <!-- 参与人权限配置弹窗 -->
    <ParticipantPermissionDialog
      v-model="participantPermissionVisible"
      :personnel-data="taskStore.personnelData"
      @save="handleParticipantPermissionSave"
    />
  </DialogComp>
</template>

<style scoped lang="scss">
.modal-content {
  padding: 0;
}

.task-config-form {
  :deep(.el-form-item) {
    margin-bottom: 16px;

    .el-form-item__label {
      font-weight: 500;
      color: #303133;
      line-height: 32px;
      font-size: 14px;
    }

    .el-form-item__content {
      line-height: 32px;
    }
  }

  :deep(.el-select),
  :deep(.el-input),
  :deep(.el-date-picker) {
    width: 100%;
  }

  :deep(.el-textarea .el-textarea__inner) {
    resize: vertical;
    min-height: 60px;
  }
}

.flex-row {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;

  .el-input {
    flex: 1;
  }

  .el-button {
    flex-shrink: 0;
  }
}

.placeholder-text {
  color: #c0c4cc;
  flex: 1;
  font-size: 14px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  margin-top: 20px;
}

// 优化时间选择器布局
:deep(.el-row) {
  .el-col {
    .el-form-item {
      margin-bottom: 0;
    }
  }
}

// 优化弹窗整体样式
:deep(.el-dialog) {
  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #ebeef5;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .el-dialog__body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: none;
  }
}
</style>
