<!-- 关联业务报表选择弹窗 -->
<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useTaskObjectiveStore } from '@/stores/taskObjectiveStore'

interface Props {
  modelValue: boolean
  selectedReportNames?: string[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', selectedReports: any[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const taskStore = useTaskObjectiveStore()

// 弹窗状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 搜索条件
const searchForm = reactive({
  reportName: '',
  publishDepartment: '',
  belongingModule: ''
})

// 表格数据
const selectedReports = ref<string[]>([])
const loading = ref(false)

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 表格列配置
const colData = [
  { field: 'sequence', title: '序号', width: 80, align: 'center' },
  { field: 'reportName', title: '业务表名称', minWidth: 150 },
  { field: 'publishDepartment', title: '发布部门', width: 120 },
  { field: 'belongingModule', title: '所属板块', width: 120, slot: true },
  { field: 'dataSource', title: '所属跑道', width: 120 },
  { field: 'updateFrequency', title: '数据更新周期', width: 120 },
  { field: 'stopTime', title: '截止时间', width: 120 }
]

// 计算属性
const filteredData = computed(() => {
  return taskStore.filteredBusinessReports({
    name: searchForm.reportName,
    department: searchForm.publishDepartment,
    module: searchForm.belongingModule
  })
})

const paginatedData = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  const data = filteredData.value.slice(start, end)
  pagination.total = filteredData.value.length
  return data
})

// 监听弹窗打开
watch(visible, (newVal) => {
  if (newVal) {
    // 确保业务报表数据已初始化
    if (taskStore.businessReports.length === 0) {
      taskStore.initializeMockData()
    }

    // 根据传入的已选择报表名称设置选中状态
    if (props.selectedReportNames && props.selectedReportNames.length > 0) {
      const reportNames = props.selectedReportNames
      const selectedIds = taskStore.businessReports
        .filter(report => reportNames.includes(report.reportName))
        .map(report => report.id)
      selectedReports.value = [...selectedIds]
      console.log('设置选中状态:', selectedIds, reportNames)
    } else {
      selectedReports.value = []
    }
  }
})

// 生命周期
onMounted(() => {
  // 确保业务报表数据已初始化
  if (taskStore.businessReports.length === 0) {
    taskStore.initializeMockData()
  }
})

// 方法
const handleSearch = async () => {
  loading.value = true
  // 模拟接口请求延迟
  await new Promise(resolve => setTimeout(resolve, 100))
  pagination.currentPage = 1
  loading.value = false
}

const handleReset = () => {
  Object.assign(searchForm, {
    reportName: '',
    publishDepartment: '',
    belongingModule: ''
  })
  handleSearch()
}

const handleSelectionChange = (selection: any[]) => {
  selectedReports.value = selection.map(item => item.id)
}

const handleCurrentChange = async (page: number) => {
  loading.value = true
  // 模拟接口请求延迟
  await new Promise(resolve => setTimeout(resolve, 100))
  pagination.currentPage = page
  loading.value = false
}

const handleSizeChange = async (size: number) => {
  loading.value = true
  // 模拟接口请求延迟
  await new Promise(resolve => setTimeout(resolve, 100))
  pagination.pageSize = size
  pagination.currentPage = 1
  loading.value = false
}

const handleCancel = () => {
  visible.value = false
  selectedReports.value = []
}

const handleConfirm = () => {
  if (selectedReports.value.length === 0) {
    ElMessage.warning('请至少选择一个业务报表')
    return
  }
  
  const selected = taskStore.businessReports.filter(report => 
    selectedReports.value.includes(report.id)
  )
  
  emit('confirm', selected)
  selectedReports.value = []
}

// 部门选项
const departmentOptions = computed(() => {
  const departments = [...new Set(taskStore.businessReports.map(r => r.publishDepartment))]
  return departments.map(dept => ({ label: dept, value: dept }))
})

// 板块选项
const moduleOptions = computed(() => {
  const modules = [...new Set(taskStore.businessReports.map(r => r.belongingModule))]
  return modules.map(module => ({ label: module, value: module }))
})
</script>

<template>
  <el-dialog
    v-model="visible"
    title="关联业务报表"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleCancel"
    draggable
    destroy-on-close
    :append-to-body="true"
    class="business-report-dialog"
  >
    <!-- 提示信息 -->
    <div class="info-banner">
      <el-icon class="info-icon"><InfoFilled /></el-icon>
      <span>目标任务系统将根据所选择的行数，需要实现所选择的业务报表数据，创建子任务类型并生成时间表数据。</span>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item>
          <el-input
            v-model="searchForm.reportName"
            placeholder="请输入业务表名称"
            clearable
            style="width: 220px"
          />
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="searchForm.publishDepartment"
            placeholder="请选择发布部门"
            clearable
            style="width: 220px"
          >
            <el-option
              v-for="option in departmentOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="searchForm.belongingModule"
            placeholder="请选择所属板块"
            clearable
            style="width: 220px"
          >
            <el-option
              v-for="option in moduleOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-section" style="height: 550px;">
      <BaseTableComp
        :data="paginatedData"
        :colData="colData"
        :loading="loading"
        :checkbox="true"
        :visibleHeader="false"
        :visibleSetting="false"
        :currentPage="pagination.currentPage"
        :pageSize="pagination.pageSize"
        :total="pagination.total"
        :auto-height="true"
        @selection-change="handleSelectionChange"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
        <template #belongingModule="{ rowData }">
          <el-tag>{{ rowData.belongingModule }}</el-tag>
        </template>
      </BaseTableComp>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
:deep(.business-report-dialog) {
  height: 800px;

  .el-dialog__body {
    height: calc(100% - 120px);
    overflow-y: auto;
  }
}

.info-banner {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 20px;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  color: #1890ff;

  .info-icon {
    margin-right: 8px;
    font-size: 16px;
  }
}

.search-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
}

.table-section {
  background: white;
  border-radius: 4px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
