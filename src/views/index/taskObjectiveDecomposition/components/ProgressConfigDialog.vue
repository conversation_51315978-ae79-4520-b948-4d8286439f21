<template>
  <DialogComp
    v-model="visible"
    title="进度配置"
    width="800px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="progress-config-content">
      <!-- 进度计算开关 -->
      <div class="progress-settings">
        <div class="setting-item">
          <label class="setting-label">任务进度完成百分比计算</label>
          <el-switch v-model="progressCalculationEnabled" />
          <span class="setting-status">{{ progressCalculationEnabled ? '启用' : '禁用' }}</span>
        </div>

        <!-- 进度条样式列表 -->
        <div class="progress-styles">
          <h4>进度条样式列表</h4>
          <div class="style-actions">
            <el-button type="primary" @click="openStyleTemplateDialog">样式模板设置</el-button>
            <el-button @click="openStyleImportDialog">样式模板导入</el-button>
          </div>

          <!-- 搜索区域 -->
          <div class="template-search">
            <el-input 
              v-model="searchForm.templateName"
              placeholder="请输入模板名称" 
              style="width: 200px;" 
              clearable
              @input="handleSearch"
            />
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </div>

          <!-- 样式模板表格 -->
          <div class="style-table">
            <el-table 
              :data="filteredTemplates" 
              style="width: 100%" 
              height="300"
              :loading="loading"
            >
              <el-table-column prop="templateName" label="进度条样式模板名称" min-width="200" />
              <el-table-column prop="inProgressColor" label="执行中配色" width="150" align="center">
                <template #default="{ row }">
                  <div class="color-display" :style="{ backgroundColor: row.inProgressColor }"></div>
                  <span class="color-text">{{ row.inProgressColor }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="completedColor" label="已完成配色" width="150" align="center">
                <template #default="{ row }">
                  <div class="color-display" :style="{ backgroundColor: row.completedColor }"></div>
                  <span class="color-text">{{ row.completedColor }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center">
                <template #default="{ row, $index }">
                  <el-button type="text" size="small" @click="editTemplate(row, $index)">编辑</el-button>
                  <el-button type="text" size="small" @click="deleteTemplate($index)" style="color: #f56c6c;">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </div>

    <!-- 样式模板设置弹窗 -->
    <StyleTemplateDialog
      v-model="styleTemplateVisible"
      :template-data="currentTemplate"
      @confirm="handleTemplateConfirm"
    />

    <!-- 样式模板导入弹窗 -->
    <StyleImportDialog
      v-model="styleImportVisible"
      @confirm="handleImportConfirm"
    />
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import DialogComp from '@/components/common/dialog-comp.vue'
import StyleTemplateDialog from '@/views/index/taskObjectiveDecomposition/components/StyleTemplateDialog.vue'
import StyleImportDialog from '@/views/index/taskObjectiveDecomposition/components/StyleImportDialog.vue'

// 接口定义
interface StyleTemplate {
  id?: string
  templateName: string
  inProgressColor: string
  completedColor: string
  createTime?: string
  index?: number // 可选属性，用于编辑时传递索引
}

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const progressCalculationEnabled = ref(true)
const loading = ref(false)
const styleTemplateVisible = ref(false)
const styleImportVisible = ref(false)
const currentTemplate = ref<StyleTemplate | null>(null)

// 搜索表单
const searchForm = ref({
  templateName: ''
})

// 样式模板数据（根据原型设计更新）
const styleTemplates = ref<StyleTemplate[]>([
  {
    id: '1',
    templateName: '样式模板1',
    inProgressColor: '#155BBC',
    completedColor: '#156BBC',
    createTime: '2024-01-01'
  },
  {
    id: '2',
    templateName: '样式模板2',
    inProgressColor: '#156BBC',
    completedColor: '#157BBC',
    createTime: '2024-01-02'
  },
  {
    id: '3',
    templateName: '样式模板3',
    inProgressColor: '#157BBC',
    completedColor: '#158BBC',
    createTime: '2024-01-03'
  },
  {
    id: '4',
    templateName: '样式模板4',
    inProgressColor: '#158BBC',
    completedColor: '#159BBC',
    createTime: '2024-01-04'
  },
  {
    id: '5',
    templateName: '样式模板5',
    inProgressColor: '#159BBC',
    completedColor: '#160BBC',
    createTime: '2024-01-05'
  },
  {
    id: '6',
    templateName: '样式模板6',
    inProgressColor: '#160BBC',
    completedColor: '#161BBC',
    createTime: '2024-01-06'
  },
  {
    id: '7',
    templateName: '样式模板7',
    inProgressColor: '#161BBC',
    completedColor: '#162BBC',
    createTime: '2024-01-07'
  }
])

// 过滤后的模板数据
const filteredTemplates = computed(() => {
  if (!searchForm.value.templateName) {
    return styleTemplates.value
  }
  return styleTemplates.value.filter(template => 
    template.templateName.includes(searchForm.value.templateName)
  )
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = () => {
  // 保存配置到localStorage
  const config = {
    progressCalculationEnabled: progressCalculationEnabled.value,
    styleTemplates: styleTemplates.value
  }
  localStorage.setItem('progressConfig', JSON.stringify(config))
  
  emit('confirm', config)
  visible.value = false
}

const handleSearch = () => {
  // 搜索功能已通过computed实现
}

const handleReset = () => {
  searchForm.value.templateName = ''
}

const openStyleTemplateDialog = () => {
  currentTemplate.value = null
  styleTemplateVisible.value = true
}

const openStyleImportDialog = () => {
  styleImportVisible.value = true
}

const editTemplate = (template: StyleTemplate, index: number) => {
  currentTemplate.value = { ...template, index }
  styleTemplateVisible.value = true
}

const deleteTemplate = (index: number) => {
  styleTemplates.value.splice(index, 1)
}

const handleTemplateConfirm = (templateData: StyleTemplate & { index?: number }) => {
  if (typeof templateData.index === 'number') {
    // 编辑模式
    styleTemplates.value[templateData.index] = {
      id: templateData.id,
      templateName: templateData.templateName,
      inProgressColor: templateData.inProgressColor,
      completedColor: templateData.completedColor,
      createTime: templateData.createTime
    }
  } else {
    // 新增模式
    const newTemplate: StyleTemplate = {
      id: Date.now().toString(),
      templateName: templateData.templateName,
      inProgressColor: templateData.inProgressColor,
      completedColor: templateData.completedColor,
      createTime: new Date().toISOString().split('T')[0]
    }
    styleTemplates.value.push(newTemplate)
  }
}

const handleImportConfirm = (importedData: StyleTemplate[]) => {
  styleTemplates.value.push(...importedData)
}

// 加载保存的配置
const loadConfig = () => {
  const savedConfig = localStorage.getItem('progressConfig')
  if (savedConfig) {
    const config = JSON.parse(savedConfig)
    progressCalculationEnabled.value = config.progressCalculationEnabled ?? true
    if (config.styleTemplates && Array.isArray(config.styleTemplates)) {
      styleTemplates.value = config.styleTemplates
    }
  }
}

// 监听弹窗打开
watch(visible, (newVal) => {
  if (newVal) {
    loadConfig()
  }
})

onMounted(() => {
  loadConfig()
})
</script>

<style lang="scss" scoped>
.progress-config-content {
  padding: 20px;

  .progress-settings {
    .setting-item {
      display: flex;
      align-items: center;
      margin-bottom: 24px;
      gap: 12px;

      .setting-label {
        width: 200px;
        text-align: left;
        font-weight: 500;
        color: #606266;
        flex-shrink: 0;
      }

      .setting-status {
        color: #409EFF;
        font-size: 14px;
        margin-left: 8px;
      }
    }

    .progress-styles {
      h4 {
        margin: 24px 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .style-actions {
        margin-bottom: 16px;
        display: flex;
        gap: 12px;
      }

      .template-search {
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .style-table {
        .color-display {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          border: 1px solid #dcdfe6;
          margin: 0 auto 4px;
        }

        .color-text {
          font-size: 12px;
          color: #606266;
        }
      }
    }
  }

  .dialog-footer {
    margin-top: 24px;
    text-align: right;
    
    .el-button {
      margin-left: 12px;
    }
  }
}
</style>
