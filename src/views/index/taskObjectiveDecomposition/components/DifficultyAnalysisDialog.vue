<template>
  <DialogComp
    v-model="visible"
    title="难度分析"
    width="600px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="difficulty-analysis-content">
      <!-- 分析项目列表 -->
      <div class="analysis-items">
        <div 
          v-for="(item, index) in analysisItems" 
          :key="index"
          class="analysis-item"
        >
          <div class="analysis-row">
            <label class="analysis-label">{{ item.label }}：</label>
            <span class="analysis-content">{{ item.content }}</span>
            <span 
              class="difficulty-level" 
              :class="getDifficultyClass(item.difficulty)"
            >
              难度：{{ item.difficulty }}
            </span>
          </div>
        </div>
      </div>

      <!-- 综合分析结果 -->
      <div class="comprehensive-analysis">
        <strong>{{ comprehensiveAnalysis }}</strong>
      </div>

      <!-- 详细说明 -->
      <div class="analysis-details" v-if="showDetails">
        <h4>分析说明</h4>
        <ul>
          <li><strong>时间压力</strong>：基于任务截止时间与当前时间的差值进行评估</li>
          <li><strong>填报内容</strong>：根据需要填报的字段数量和复杂程度评估</li>
          <li><strong>步骤复杂度</strong>：考虑是否包含审核流程、多级审批等因素</li>
        </ul>
      </div>

      <!-- 底部按钮 -->
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </div>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'

// 接口定义
interface AnalysisItem {
  label: string
  content: string
  difficulty: '低' | '中等' | '高'
  difficultyColor: string
}

interface DifficultyAnalysisData {
  analysisItems: AnalysisItem[]
  overallDifficulty: '低' | '中等' | '高'
  comprehensiveAnalysis: string
}

// Props
const props = defineProps<{
  modelValue: boolean
  taskData?: any
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: DifficultyAnalysisData]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const showDetails = ref(false)

// 分析项目数据
const analysisItems = ref<AnalysisItem[]>([
  {
    label: '时间压力',
    content: '可以天数10天',
    difficulty: '低',
    difficultyColor: '#67C23A'
  },
  {
    label: '填报内容',
    content: '需填报字段30项',
    difficulty: '中等',
    difficultyColor: '#E6A23C'
  },
  {
    label: '步骤复杂度',
    content: '不包含审核流程',
    difficulty: '低',
    difficultyColor: '#67C23A'
  }
])

// 综合分析结果
const comprehensiveAnalysis = computed(() => {
  const difficulties = analysisItems.value.map(item => item.difficulty)
  const highCount = difficulties.filter(d => d === '高').length
  const mediumCount = difficulties.filter(d => d === '中等').length
  const lowCount = difficulties.filter(d => d === '低').length

  let overallDifficulty: '低' | '中等' | '高'
  
  if (highCount > 0) {
    overallDifficulty = '高'
  } else if (mediumCount >= 2) {
    overallDifficulty = '高'
  } else if (mediumCount === 1) {
    overallDifficulty = '中等'
  } else {
    overallDifficulty = '低'
  }

  return `综合分析本次任务难度为：${overallDifficulty}。`
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = () => {
  // 添加100毫秒延迟效果
  loading.value = true
  setTimeout(() => {
    const analysisData: DifficultyAnalysisData = {
      analysisItems: analysisItems.value,
      overallDifficulty: getOverallDifficulty(),
      comprehensiveAnalysis: comprehensiveAnalysis.value
    }
    
    emit('confirm', analysisData)
    visible.value = false
    loading.value = false
    
    ElMessage.success('难度分析确认成功')
  }, 100)
}

const getDifficultyClass = (difficulty: string) => {
  switch (difficulty) {
    case '低':
      return 'low'
    case '中等':
      return 'medium'
    case '高':
      return 'high'
    default:
      return 'low'
  }
}

const getOverallDifficulty = (): '低' | '中等' | '高' => {
  const difficulties = analysisItems.value.map(item => item.difficulty)
  const highCount = difficulties.filter(d => d === '高').length
  const mediumCount = difficulties.filter(d => d === '中等').length

  if (highCount > 0) {
    return '高'
  } else if (mediumCount >= 2) {
    return '高'
  } else if (mediumCount === 1) {
    return '中等'
  } else {
    return '低'
  }
}

// 根据任务数据动态分析难度
const analyzeTaskDifficulty = (taskData: any) => {
  if (!taskData) return

  // 模拟根据任务数据进行难度分析
  const currentDate = new Date()
  const taskEndDate = new Date(taskData.endTime || '2024-12-31')
  const daysDiff = Math.ceil((taskEndDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24))

  // 更新时间压力分析
  if (daysDiff <= 3) {
    analysisItems.value[0] = {
      label: '时间压力',
      content: `可用天数${daysDiff}天`,
      difficulty: '高',
      difficultyColor: '#F56C6C'
    }
  } else if (daysDiff <= 7) {
    analysisItems.value[0] = {
      label: '时间压力',
      content: `可用天数${daysDiff}天`,
      difficulty: '中等',
      difficultyColor: '#E6A23C'
    }
  } else {
    analysisItems.value[0] = {
      label: '时间压力',
      content: `可用天数${daysDiff}天`,
      difficulty: '低',
      difficultyColor: '#67C23A'
    }
  }

  // 根据任务类型分析填报内容复杂度
  const fieldCount = taskData.fieldCount || 30
  if (fieldCount > 50) {
    analysisItems.value[1] = {
      label: '填报内容',
      content: `需填报字段${fieldCount}项`,
      difficulty: '高',
      difficultyColor: '#F56C6C'
    }
  } else if (fieldCount > 20) {
    analysisItems.value[1] = {
      label: '填报内容',
      content: `需填报字段${fieldCount}项`,
      difficulty: '中等',
      difficultyColor: '#E6A23C'
    }
  } else {
    analysisItems.value[1] = {
      label: '填报内容',
      content: `需填报字段${fieldCount}项`,
      difficulty: '低',
      difficultyColor: '#67C23A'
    }
  }

  // 根据是否包含审核流程分析步骤复杂度
  const hasApproval = taskData.hasApproval || false
  if (hasApproval) {
    analysisItems.value[2] = {
      label: '步骤复杂度',
      content: '包含多级审核流程',
      difficulty: '高',
      difficultyColor: '#F56C6C'
    }
  } else {
    analysisItems.value[2] = {
      label: '步骤复杂度',
      content: '不包含审核流程',
      difficulty: '低',
      difficultyColor: '#67C23A'
    }
  }
}

// 监听弹窗打开，分析任务难度
watch(visible, (newVal) => {
  if (newVal) {
    analyzeTaskDifficulty(props.taskData)
  }
})

onMounted(() => {
  if (props.taskData) {
    analyzeTaskDifficulty(props.taskData)
  }
})
</script>

<style lang="scss" scoped>
.difficulty-analysis-content {
  padding: 20px;

  .analysis-items {
    .analysis-item {
      margin-bottom: 20px;

      .analysis-row {
        display: flex;
        align-items: center;
        gap: 16px;

        .analysis-label {
          width: 140px;
          text-align: right;
          font-weight: 500;
          color: #606266;
          flex-shrink: 0;
        }

        .analysis-content {
          flex: 1;
          color: #303133;
          font-size: 14px;
        }

        .difficulty-level {
          font-weight: 500;
          font-size: 14px;
          flex-shrink: 0;
          padding: 2px 8px;
          border-radius: 4px;
          background-color: #f5f7fa;

          &.low {
            color: #67C23A;
            background-color: #f0f9ff;
            border: 1px solid #b3d8ff;
          }

          &.medium {
            color: #E6A23C;
            background-color: #fdf6ec;
            border: 1px solid #f5dab1;
          }

          &.high {
            color: #F56C6C;
            background-color: #fef0f0;
            border: 1px solid #fbc4c4;
          }
        }
      }
    }
  }

  .comprehensive-analysis {
    margin: 24px 0;
    padding: 16px;
    background: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 6px;
    text-align: center;
    color: #0066cc;
    font-weight: 500;
    font-size: 16px;
  }

  .analysis-details {
    margin: 20px 0;
    padding: 16px;
    background: #fafbfc;
    border-radius: 6px;
    border: 1px solid #e4e7ed;

    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        font-size: 14px;
        color: #606266;
        line-height: 1.6;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        strong {
          color: #303133;
        }
      }
    }
  }

  .dialog-footer {
    margin-top: 24px;
    text-align: right;
    
    .el-button {
      margin-left: 12px;
    }
  }
}
</style>
