<!-- 重要程度调整弹窗 -->
<template>
  <DialogComp
    v-model="visible"
    title="子任务关系重要程度"
    width="800px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="importance-adjustment-content" v-loading="loading" element-loading-text="处理中...">
      <!-- 重要程度调整规则 -->
      <div class="adjustment-rules-section">
        <h4 class="section-title">重要程度调整规则</h4>
        <el-table :data="adjustmentRules" style="width: 100%" size="small">
          <el-table-column prop="adjustmentType" label="调整类型" width="120" />
          <el-table-column label="调整规则" width="180">
            <template #default="{ row }">
              <el-checkbox
                v-model="row.adjustmentRule.selected"
                @change="handleRuleItemChange(row.id, 'adjustmentRule', row.adjustmentRule.selected)"
              >
                {{ row.adjustmentRule.text }}
              </el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="任务未开始" width="120">
            <template #default="{ row }">
              <el-checkbox
                v-model="row.taskNotStarted.selected"
                @change="handleRuleItemChange(row.id, 'taskNotStarted', row.taskNotStarted.selected)"
              >
                {{ row.taskNotStarted.text }}
              </el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="任务未到期" width="120">
            <template #default="{ row }">
              <el-checkbox
                v-model="row.taskNotCompleted.selected"
                @change="handleRuleItemChange(row.id, 'taskNotCompleted', row.taskNotCompleted.selected)"
              >
                {{ row.taskNotCompleted.text }}
              </el-checkbox>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 重要程度调整原因分类 -->
      <div class="adjustment-reason-section">
        <h4 class="section-title">重要程度调整原因分类</h4>
        <el-checkbox-group v-model="selectedReasons" class="reason-checkboxes">
          <el-checkbox label="问题提级" />
          <el-checkbox label="问题已解决" />
          <el-checkbox label="时间临期" />
        </el-checkbox-group>
      </div>

      <!-- 子任务列表表格 -->
      <div class="subtask-table-section">
        <h4 class="section-title">选择要调整的子任务</h4>
        <el-table
          :data="subTaskList"
          style="width: 100%"
          height="300"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="taskName" label="子任务名称" min-width="150" />
          <el-table-column prop="taskType" label="子任务类型" width="120" />
          <el-table-column prop="responsiblePerson" label="责任人" width="100" />
          <el-table-column prop="importanceLevel" label="当前重要程度" width="120">
            <template #default="{ row }">
              <el-tag :type="getImportanceType(row.importanceLevel)">
                {{ getImportanceLevelLabel(row.importanceLevel) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 重要程度调整表单 -->
      <div class="importance-form-section" v-if="selectedTasks.length > 0">
        <h4 class="section-title">调整设置</h4>
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          size="default"
        >
          <el-form-item label="新重要程度" prop="newImportanceLevel">
            <el-select
              v-model="formData.newImportanceLevel"
              placeholder="请选择新的重要程度"
              style="width: 200px"
            >
              <el-option
                v-for="option in importanceLevelOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              >
                <div class="importance-option">
                  <el-tag :type="getImportanceType(option.value)" size="small">
                    {{ option.label }}
                  </el-tag>
                  <span class="option-description">{{ option.description }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          
          <el-form-item label="调整原因" prop="adjustReason">
            <el-input
              v-model="formData.adjustReason"
              type="textarea"
              :rows="3"
              placeholder="请输入调整原因（可选）"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm" 
          :loading="loading"
          :disabled="selectedTasks.length === 0"
        >
          确认调整
        </el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import DialogComp from '@/components/common/dialog-comp.vue'
import { importanceLevelOptions, importanceColorMap } from '../data/mockData'

interface Props {
  modelValue: boolean
  subTaskData: any[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: any): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  subTaskData: () => []
})

const emit = defineEmits<Emits>()

// 响应式状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const formRef = ref<FormInstance>()
const selectedTasks = ref<any[]>([])
const selectedReasons = ref<string[]>([])

// 调整规则数据
const adjustmentRules = ref([
  {
    id: 'rule1',
    adjustmentType: '调整为重要',
    adjustmentRule: { text: '操作人角色为管理员', selected: false },
    taskNotStarted: { text: '任务未开始', selected: false },
    taskNotCompleted: { text: '任务未到期', selected: false }
  },
  {
    id: 'rule2',
    adjustmentType: '调整为不重要',
    adjustmentRule: { text: '操作人角色为管理员', selected: false },
    taskNotStarted: { text: '任务未开始', selected: false },
    taskNotCompleted: { text: '任务未到期', selected: false }
  }
])

// 选中的调整规则项
const selectedRuleItems = ref<string[]>([])

// 表单数据
const formData = reactive({
  newImportanceLevel: '',
  adjustReason: ''
})

// 表单验证规则
const formRules: FormRules = {
  newImportanceLevel: [
    { required: true, message: '请选择新的重要程度', trigger: 'change' }
  ],
  adjustReason: [
    { max: 200, message: '调整原因不能超过200个字符', trigger: 'blur' }
  ]
}

// 计算属性
const subTaskList = computed(() => {
  return props.subTaskData.map(task => ({
    ...task,
    importanceLevel: task.importanceLevel || 'I3' // 默认为一般重要
  }))
})

// 获取重要程度标签类型
const getImportanceType = (importanceLevel: string) => {
  return importanceColorMap[importanceLevel as keyof typeof importanceColorMap] || 'info'
}

// 获取重要程度标签文本
const getImportanceLevelLabel = (importanceLevel: string) => {
  const option = importanceLevelOptions.find(opt => opt.value === importanceLevel)
  return option ? option.label : importanceLevel
}

// 处理调整规则项选择变化
const handleRuleItemChange = (ruleId: string, itemType: string, selected: boolean) => {
  const itemKey = `${ruleId}_${itemType}`

  if (selected) {
    if (!selectedRuleItems.value.includes(itemKey)) {
      selectedRuleItems.value.push(itemKey)
    }
  } else {
    const index = selectedRuleItems.value.indexOf(itemKey)
    if (index > -1) {
      selectedRuleItems.value.splice(index, 1)
    }
  }

  console.log('选中的调整规则项:', selectedRuleItems.value)
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedTasks.value = selection
  console.log('选中的子任务:', selectedTasks.value)
}

// 重置表单
const resetForm = () => {
  formData.newImportanceLevel = ''
  formData.adjustReason = ''
  selectedTasks.value = []
  selectedReasons.value = []
  selectedRuleItems.value = []
  // 重置调整规则项选中状态
  adjustmentRules.value.forEach(rule => {
    rule.adjustmentRule.selected = false
    rule.taskNotStarted.selected = false
    rule.taskNotCompleted.selected = false
  })
  formRef.value?.resetFields()
}

// 处理取消
const handleCancel = () => {
  resetForm()
  visible.value = false
}

// 处理关闭
const handleClose = () => {
  resetForm()
}

// 处理确认
const handleConfirm = async () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要调整的子任务')
    return
  }

  if (!formRef.value) return

  try {
    await formRef.value.validate()
  } catch (error) {
    console.error('表单验证失败:', error)
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要将选中的 ${selectedTasks.value.length} 个子任务的重要程度调整为 ${getImportanceLevelLabel(formData.newImportanceLevel)} 吗？`,
      '确认调整',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    loading.value = true

    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 1000))

    // 模拟可能的失败情况
    const success = Math.random() > 0.1 // 90% 成功率

    if (!success) {
      throw new Error('调整失败，请重试')
    }

    const adjustmentData = {
      selectedTasks: selectedTasks.value.map(task => ({
        ...task,
        oldImportanceLevel: task.importanceLevel,
        newImportanceLevel: formData.newImportanceLevel
      })),
      newImportanceLevel: formData.newImportanceLevel,
      adjustReason: formData.adjustReason,
      selectedReasons: selectedReasons.value,
      selectedRuleItems: selectedRuleItems.value,
      adjustTime: new Date().toISOString(),
      adjustedCount: selectedTasks.value.length
    }

    emit('confirm', adjustmentData)
    resetForm()
    
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('重要程度调整失败:', error)
      ElMessage.error(error.message || '重要程度调整失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 监听弹窗显示状态
watch(visible, (newVal) => {
  if (newVal) {
    resetForm()
  }
})
</script>

<style lang="scss" scoped>
.importance-adjustment-content {
  .section-title {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
  }

  .adjustment-rules-section {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;

    :deep(.el-table) {
      border: 1px solid #e4e7ed;
      border-radius: 6px;

      .el-table__header {
        background-color: #fafbfc;
      }
    }
  }

  .adjustment-reason-section {
    margin-bottom: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;

    .reason-checkboxes {
      display: flex;
      gap: 24px;
      
      :deep(.el-checkbox) {
        margin-right: 0;
      }
    }
  }

  .subtask-table-section {
    margin-bottom: 24px;

    :deep(.el-table) {
      border: 1px solid #e4e7ed;
      border-radius: 6px;

      .el-table__header {
        background-color: #fafbfc;
      }

      .el-table__row {
        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }

  .importance-form-section {
    margin-bottom: 24px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e4e7ed;

    :deep(.el-form-item__label) {
      font-weight: 500;
      color: #303133;
    }

    :deep(.el-select) {
      .el-input__inner {
        border-radius: 6px;
      }
    }

    :deep(.el-textarea) {
      .el-textarea__inner {
        border-radius: 6px;
        resize: none;
      }
    }

    .importance-option {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .option-description {
        font-size: 12px;
        color: #909399;
        margin-left: 8px;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
