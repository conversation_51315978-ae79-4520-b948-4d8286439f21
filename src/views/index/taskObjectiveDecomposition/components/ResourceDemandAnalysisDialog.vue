<!-- 子任务资源需求分析弹窗 -->
<template>
  <DialogComp
    v-model="visible"
    title="子任务资源需求分析"
    width="600px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="resource-demand-content">
      <!-- 需求分析表格 -->
      <div class="demand-table-section">
        <el-table
          :data="demandData"
          style="width: 100%"
          height="400"
        >
          <el-table-column prop="taskName" label="子任务名称" min-width="200" />
          <el-table-column prop="resourceDemand" label="资源需求" width="120" align="center">
            <template #default="{ row }">
              <span class="demand-percentage">{{ row.resourceDemand }}%</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import DialogComp from '@/components/common/dialog-comp.vue'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 模拟需求分析数据
const demandData = ref([
  {
    id: 1,
    taskName: '永川区民政局填报流程',
    resourceDemand: 23
  },
  {
    id: 2,
    taskName: '系统填报流程',
    resourceDemand: 21
  },
  {
    id: 3,
    taskName: '永川区民政局填报流程',
    resourceDemand: 32
  },
  {
    id: 4,
    taskName: '系统填报流程',
    resourceDemand: 24
  }
])

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 处理确认
const handleConfirm = () => {
  emit('confirm', {
    demandData: demandData.value
  })
  visible.value = false
}
</script>

<style lang="scss" scoped>
.resource-demand-content {
  padding: 20px;

  .demand-table-section {
    .demand-percentage {
      font-weight: 600;
      color: #409eff;
    }
  }
}

.dialog-footer {
  margin-top: 24px;
  text-align: right;
  
  .el-button {
    margin-left: 12px;
  }
}
</style>
