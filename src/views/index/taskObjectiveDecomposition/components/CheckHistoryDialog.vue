<!-- 子任务质量检查历史查看弹窗 -->
<template>
  <DialogComp
    v-model="visible"
    title="子任务质量检查历史查看"
    width="600px"
    :visible-close-button="false"
    :visible-confirm-button="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="check-history-content">
      <!-- 检查历史表格 -->
      <div class="history-table-section">
        <el-table
          :data="historyData"
          style="width: 100%"
          height="400"
        >
          <el-table-column prop="operator" label="操作人" min-width="120" />
          <el-table-column prop="operationTime" label="操作时间" min-width="150" />
        </el-table>
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </DialogComp>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import DialogComp from '@/components/common/dialog-comp.vue'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 模拟历史数据
const historyData = ref([
  {
    id: 1,
    operator: '张三',
    operationTime: '2025-2-20'
  },
  {
    id: 2,
    operator: '李四',
    operationTime: '2025-2-20'
  },
  {
    id: 3,
    operator: '王五',
    operationTime: '2025-2-20'
  },
  {
    id: 4,
    operator: 'XXX',
    operationTime: '2025-2-20'
  }
])

// 处理取消
const handleCancel = () => {
  visible.value = false
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 处理确认
const handleConfirm = () => {
  emit('confirm', {
    historyData: historyData.value
  })
  visible.value = false
}
</script>

<style lang="scss" scoped>
.check-history-content {
  padding: 20px;

  .history-table-section {
    // 表格样式继承Element Plus默认样式
  }
}

.dialog-footer {
  margin-top: 24px;
  text-align: right;
  
  .el-button {
    margin-left: 12px;
  }
}
</style>
