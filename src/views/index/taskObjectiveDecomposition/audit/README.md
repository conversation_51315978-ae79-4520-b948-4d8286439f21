# 子任务审核功能

## 功能概述

子任务审核功能允许审核人员对提交的子任务进行审核，包括通过、驳回或退回操作。

## 页面结构

### 左侧流程说明
- 显示任务处理的4个步骤：任务接收 → 数据填报 → 任务审核 → 任务完成
- 当前步骤（任务审核）会高亮显示

### 右侧任务信息
1. **任务信息卡片**：显示子任务的基本信息
   - 任务名称、类型、分类
   - 责任人、参与人
   - 当前状态、任务进度
   - 优先级、任务描述

2. **审核操作表单**：
   - 审核结果选择（通过/驳回/退回）
   - 审核意见输入（必填，5-500字符）
   - 审核人和审核时间（自动填充）
   - 提交审核和重置按钮

3. **审核历史记录**：显示该子任务的历史审核记录

4. **相关文档**：显示与该子任务相关的文档文件

## 使用方法

### 访问审核页面
1. 从任务详情页面点击子任务的"审核"按钮
2. 或直接访问：`/taskObjectiveDecomposition/audit/{subTaskId}?free=true`

### 进行审核操作
1. 选择审核结果（通过/驳回/退回）
2. 填写审核意见（必填）
3. 点击"提交审核"按钮
4. 确认审核操作
5. 系统会自动更新子任务状态并记录审核历史

### 其他操作
- **刷新**：重新加载页面数据
- **重置**：清空表单内容
- **返回**：返回到任务详情页面
- **文档下载**：点击文档名称可下载相关文件

## 技术实现

### 文件结构
```
audit/
├── [id].vue           # 审核页面主文件
├── __tests__/         # 测试文件目录
│   └── basic-audit-test.spec.ts
└── README.md          # 说明文档
```

### 主要功能
- **数据加载**：3-5秒模拟加载延迟
- **表单验证**：审核结果和审核意见的必填验证
- **状态管理**：使用 useTaskObjectiveStore 管理数据
- **响应式布局**：支持桌面端和移动端显示
- **错误处理**：完整的错误处理和用户反馈

### Mock数据
- 使用 `generateAuditHistoryData()` 生成审核历史
- 使用 `generateRelatedDocumentsData()` 生成相关文档
- 数据存储在浏览器本地存储中，页面刷新后保持

## 测试

### 运行测试
```bash
# 运行基础功能测试
npx playwright test tests/e2e/taskObjectiveDecomposition/basic-audit-test.spec.ts

# 运行带界面的测试
npx playwright test tests/e2e/taskObjectiveDecomposition/basic-audit-test.spec.ts --headed
```

### 测试覆盖
- 页面基本加载和显示
- 审核表单交互
- 页面导航功能
- 数据表格显示
- 响应式布局

## 注意事项

1. **演示功能**：这是纯前端演示功能，无真实后端API
2. **数据持久化**：数据存储在浏览器本地存储中
3. **权限控制**：使用 `?free=true` 参数跳过认证
4. **浏览器兼容**：支持现代浏览器（Chrome、Firefox、Safari）

## 样式说明

- 使用Element Plus组件库保持界面一致性
- 响应式设计，支持不同屏幕尺寸
- 左侧面板固定宽度300px（桌面端）
- 移动端自动切换为垂直布局
