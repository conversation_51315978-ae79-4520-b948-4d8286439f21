<!-- 子任务审核页面 -->
<script setup lang="ts" name="SubTaskAudit">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import BaseTableComp from '@/components/common/basetable-comp.vue'
import { useTaskObjectiveStore } from '@/stores/taskObjectiveStore'

// 路由和参数
const router = useRouter()
const route = useRoute()
const taskStore = useTaskObjectiveStore()

// 页面状态
const loading = ref(true)
const subTaskId = ref(route.params.id as string)

// 从store获取真实的任务和子任务数据
const currentSubTask = computed(() => {
  // 路由参数可能是任务ID或子任务ID，先尝试作为子任务ID查找
  const routeId = route.params.id as string
  let subTask = taskStore.getSubTaskById(routeId)

  if (subTask) {
    return subTask
  }

  // 如果没找到，可能是任务ID，查找该任务下的第一个待审核子任务
  const subTasks = taskStore.getSubTasksByTaskId(routeId)
  return subTasks.find(st => st.taskStatus === '待审核') || subTasks[0]
})

const currentTask = computed(() => {
  const subTask = currentSubTask.value
  if (subTask) {
    return taskStore.getTaskById(subTask.taskId)
  }
  // 如果没有子任务，直接用路由参数作为任务ID
  return taskStore.getTaskById(route.params.id as string)
})

// 任务基本信息 - 基于真实数据
const taskInfo = computed(() => {
  const task = currentTask.value
  const subTask = currentSubTask.value

  if (!task || !subTask) {
    return {
      businessTableName: '数据填报任务',
      createDepartment: '办公室',
      creator: '张三丰',
      reportDeadline: '2023-10-01 12:00:12',
      belongingArea: '202305批次',
      reportInstructions: '请在规定时间内报...',
      reportDepartment: '安全部',
      reporter: '张三丰',
      submitTime: '2025-06-01 12:00:12',
      operationType: ''
    }
  }

  return {
    businessTableName: task.taskName,
    createDepartment: task.createDepartment.split('-')[0] || '办公室',
    creator: task.createDepartment.split('-')[2] || '张三丰',
    reportDeadline: task.startTime + ' 12:00:12',
    belongingArea: '202305批次',
    reportInstructions: `${subTask.taskName}相关填报说明，请在规定时间内完成数据填报工作...`,
    reportDepartment: subTask.participants ? subTask.participants.split('-')[0] || '安全部' : '安全部',
    reporter: subTask.responsiblePerson,
    submitTime: subTask.updateTime + ' 12:00:12',
    operationType: subTask.taskType
  }
})

// 流程步骤数据
const processSteps = ref([
  {
    title: '项目数据',
    person: '朱沅镇-公共服务-张三',
    time: '2024-01-21 12:00:01',
    status: 'completed'
  },
  {
    title: '填报部门填报审核',
    person: '朱沅镇-公共服务-李四',
    time: '2024-02-21 12:00:01',
    status: 'completed',
    note: '已审核'
  },
  {
    title: '区级数据审核(当前步骤点击审核)',
    person: '永川区生态环境局工作人员A',
    time: '',
    status: 'current'
  },
  {
    title: '市级部门填报审核',
    person: '重庆市生态环境局工作人员A',
    time: '',
    status: 'pending'
  },
  {
    title: '重庆市生态环境局工作人员B',
    person: '任意一人审核通过',
    time: '',
    status: 'pending'
  },
  {
    title: '台账数据更新',
    person: '',
    time: '',
    status: 'pending'
  }
])

// 数据表格列配置
const tableColumns = [
  { field: 'department', title: '所属部门', width: 150 },
  { field: 'city', title: '所属城市', width: 120 },
  { field: 'district', title: '所属区县', width: 120 },
  { field: 'name', title: '姓名', width: 100 },
  { field: 'gender', title: '性别', width: 80 },
  { field: 'age', title: '年龄', width: 80 },
  { field: 'dataItem', title: '数据项N', width: 100 },
  { field: 'operationType', title: '操作类型', width: 120 }
]

// 表格操作按钮配置
const tableButtons = [
  {
    type: 'primary' as const,
    code: 'view',
    title: '查看详情',
    icon: '',
    verify: 'true',
    more: false,
    showBtn: 'true'
  },
  {
    type: 'warning' as const,
    code: 'edit',
    title: '编辑',
    icon: '',
    verify: 'true',
    more: false,
    showBtn: 'true'
  },
  {
    type: 'danger' as const,
    code: 'delete',
    title: '删除',
    icon: '',
    verify: 'true',
    more: false,
    showBtn: 'true'
  }
]

// 表格数据 - 基于真实的子任务数据生成
const tableData = computed(() => {
  const subTask = currentSubTask.value
  if (!subTask) {
    return [
      {
        department: '永川区民政局',
        city: '重庆市',
        district: '永川区',
        name: '张三',
        gender: '男性',
        age: 12,
        dataItem: 12,
        operationType: '新增'
      }
    ]
  }

  // 根据子任务信息生成相关的操作数据
  const baseData = {
    department: subTask.participants ? subTask.participants.split('-')[0] || '永川区民政局' : '永川区民政局',
    city: '重庆市',
    district: '永川区',
    name: subTask.responsiblePerson || '张三',
    gender: '男性',
    age: 12,
    dataItem: 12
  }

  // 根据任务状态生成不同的操作类型
  const operationTypes = ['新增', '编辑修改', '删除']
  if (subTask.taskStatus === '待审核') {
    operationTypes.push('审核中')
  }

  return operationTypes.map((type, index) => ({
    id: index + 1,
    ...baseData,
    operationType: type
  }))
})

// 初始化页面数据
const initPageData = async () => {
  loading.value = true

  try {
    // 确保store数据已初始化
    taskStore.initializeData()

    // 模拟3-5秒加载延迟
    await new Promise(resolve => setTimeout(resolve, 3000 + Math.random() * 2000))

    // 验证数据是否存在
    const task = currentTask.value
    const subTask = currentSubTask.value

    if (!task) {
      throw new Error(`任务不存在: ${route.params.id}`)
    }

    console.log('审核页面数据加载完成', {
      task: task.taskName,
      subTask: subTask?.taskName || '未找到待审核子任务'
    })

  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败，请重试')
  } finally {
    loading.value = false
  }
}

// 审核操作处理
const handleAuditAction = async (action: string) => {
  try {
    await ElMessageBox.confirm(
      `确定要${action}该子任务吗？`,
      '审核确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 模拟提交延迟
    loading.value = true
    await new Promise(resolve => setTimeout(resolve, 1500))

    ElMessage.success(`子任务${action}成功`)

    // 延迟返回上一页
    setTimeout(() => {
      handleBack()
    }, 1500)

  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核操作失败:', error)
      ElMessage.error('审核操作失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 返回上一页
const handleBack = () => {
  const currentQuery = route.query
  router.push({
    path: `/taskObjectiveDecomposition/detail/1`,
    query: currentQuery
  })
}

// 表格按钮点击处理
const handleTableButton = (data: any) => {
  const { btn, scope } = data
  const row = scope

  switch (btn.code) {
    case 'view':
      ElMessage.info(`查看详情：${row.name} (${row.operationType})`)
      break
    case 'edit':
      ElMessage.info(`编辑记录：${row.name} (${row.operationType})`)
      break
    case 'delete':
      handleDeleteRecord(row)
      break
    default:
      console.log('未知操作:', btn.code)
  }
}

const handleDeleteRecord = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 ${row.name} 的${row.operationType}记录吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}



// 生命周期
onMounted(() => {
  initPageData()
})
</script>

<template>
  <div class="subtask-audit-page" v-loading="loading">
    <!-- 左右分栏布局 -->
    <div class="audit-layout">
      <!-- 左侧流程说明面板 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3 class="panel-title">流程说明</h3>
        </div>
        <div class="panel-content">
          <div class="process-steps">
            <div
              v-for="(step, index) in processSteps"
              :key="index"
              class="step-item"
              :class="{
                'completed': step.status === 'completed',
                'current': step.status === 'current',
                'pending': step.status === 'pending'
              }"
            >
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-content">
                <div class="step-title">{{ step.title }}</div>
                <div class="step-person" v-if="step.person">{{ step.person }}</div>
                <div class="step-time" v-if="step.time">{{ step.time }}</div>
                <div class="step-note" v-if="step.note">{{ step.note }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-panel">
        <!-- 页面头部导航 -->
        <div class="page-header">
          <div class="header-left">
            <h2 class="page-title">子任务审核</h2>
          </div>
          <div class="header-actions">
            <el-button @click="handleBack" class="return-button">返回</el-button>
            <el-button @click="handleAuditAction('驳回')" type="danger" class="action-button">驳回</el-button>
            <el-button @click="handleAuditAction('退回')" type="warning" class="action-button">退回</el-button>
            <el-button @click="handleAuditAction('通过')" type="success" class="action-button">通过</el-button>
          </div>
        </div>

        <!-- 右侧主要内容 -->
        <div class="right-content">
          <!-- 任务信息卡片 -->
          <div class="info-card">
            <div class="card-header">
              <h3>任务信息</h3>
            </div>
            <div class="card-content">
              <div class="info-grid">
                <div class="info-item">
                  <label class="info-label">业务表名称：</label>
                  <span class="info-value">{{ taskInfo.businessTableName }}</span>
                </div>
                <div class="info-item">
                  <label class="info-label">创建部门：</label>
                  <span class="info-value">{{ taskInfo.createDepartment }}</span>
                </div>
                <div class="info-item">
                  <label class="info-label">创建人：</label>
                  <span class="info-value">{{ taskInfo.creator }}</span>
                </div>
                <div class="info-item">
                  <label class="info-label">填报截止时间：</label>
                  <span class="info-value">{{ taskInfo.reportDeadline }}</span>
                </div>
                <div class="info-item">
                  <label class="info-label">所属地区：</label>
                  <span class="info-value">{{ taskInfo.belongingArea }}</span>
                </div>
                <div class="info-item">
                  <label class="info-label">填报说明：</label>
                  <span class="info-value">{{ taskInfo.reportInstructions }}</span>
                </div>
                <div class="info-item">
                  <label class="info-label">填报部门：</label>
                  <span class="info-value">{{ taskInfo.reportDepartment }}</span>
                </div>
                <div class="info-item">
                  <label class="info-label">填报人：</label>
                  <span class="info-value">{{ taskInfo.reporter }}</span>
                </div>
                <div class="info-item">
                  <label class="info-label">提交时间：</label>
                  <span class="info-value">{{ taskInfo.submitTime }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 数据表格卡片 -->
          <div class="data-table-card">
            <div class="card-header">
              <h3>操作类型</h3>
            </div>
            <div class="card-content">
              <BaseTableComp
                :data="tableData"
                :colData="tableColumns"
                :buttons="tableButtons"
                :visiblePage="false"
                :checkbox="false"
                :height="400"
                :visibleHeader="false"
                :visibleSetting="false"
                @clickButton="handleTableButton"
              >
              </BaseTableComp>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.subtask-audit-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;

  // 左右分栏布局
  .audit-layout {
    display: flex;
    gap: 20px;
    height: calc(100vh - 40px);
  }

  // 左侧流程说明面板
  .left-panel {
    width: 300px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    flex-shrink: 0;

    .panel-header {
      padding: 16px 24px;
      background: #fafbfc;
      border-bottom: 1px solid #e4e7ed;

      .panel-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .panel-content {
      padding: 20px;
      height: calc(100% - 57px);
      overflow-y: auto;
    }
  }

  // 右侧内容区域
  .right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;

    // 页面头部导航
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding: 16px 24px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      flex-shrink: 0;

      .header-left {
        .page-title {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #303133;
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;

        .return-button {
          padding: 8px 16px;
          font-size: 14px;
        }

        .action-button {
          min-width: 80px;
          padding: 8px 16px;
          font-size: 14px;
        }
      }
    }

    // 右侧主要内容
    .right-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 20px;
      overflow-y: auto;
    }
  }

  // 信息卡片样式
  .info-card, .data-table-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    flex-shrink: 0;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 24px;
      background: #fafbfc;
      border-bottom: 1px solid #e4e7ed;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }

    .card-content {
      padding: 24px;
    }
  }

  // 流程步骤时间线样式
  .process-steps {
    position: relative;

    .step-item {
      display: flex;
      margin-bottom: 24px;
      position: relative;

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 15px;
        top: 32px;
        width: 2px;
        height: calc(100% + 8px);
        background: #e4e7ed;
      }

      &.completed {
        .step-number {
          background: #67c23a;
          color: white;
        }

        &:not(:last-child)::after {
          background: #67c23a;
        }

        .step-title {
          color: #67c23a;
          font-weight: 600;
        }
      }

      &.current {
        .step-number {
          background: #409eff;
          color: white;
        }

        .step-title {
          color: #409eff;
          font-weight: 600;
        }
      }

      &.pending {
        .step-number {
          background: #dcdfe6;
          color: #909399;
        }

        .step-title {
          color: #909399;
        }
      }

      .step-number {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 600;
        flex-shrink: 0;
        margin-right: 16px;
        z-index: 1;
        position: relative;
      }

      .step-content {
        flex: 1;
        padding-top: 2px;

        .step-title {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 6px;
          line-height: 1.4;
        }

        .step-person {
          font-size: 12px;
          color: #606266;
          margin-bottom: 4px;
          line-height: 1.3;
        }

        .step-time {
          font-size: 12px;
          color: #909399;
          margin-bottom: 4px;
          line-height: 1.3;
        }

        .step-note {
          font-size: 12px;
          color: #67c23a;
          font-weight: 500;
          line-height: 1.3;
        }
      }
    }
  }

  // 信息网格布局
  .info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px 40px;

    .info-item {
      display: flex;
      align-items: center;

      .info-label {
        width: 140px;
        text-align: right;
        font-weight: 500;
        color: #606266;
        margin-right: 12px;
        flex-shrink: 0;
      }

      .info-value {
        color: #303133;
        flex: 1;
      }
    }
  }

  // 表格操作按钮样式
  .table-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;

    .table-action-btn {
      padding: 4px 8px;
      font-size: 12px;
      min-width: 60px;
      height: 28px;
      border-radius: 4px;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .subtask-audit-page {
    .audit-layout {
      flex-direction: column;
      height: auto;
    }

    .left-panel {
      width: 100%;
      height: auto;
      margin-bottom: 20px;

      .panel-content {
        height: auto;
        max-height: 400px;
      }
    }

    .right-panel {
      .page-header {
        padding: 12px 16px;

        .header-actions {
          flex-wrap: wrap;
          gap: 8px;

          .action-button, .return-button {
            min-width: 70px;
            padding: 6px 12px;
            font-size: 13px;
          }
        }
      }
    }

    .info-card, .data-table-card {
      .card-header {
        padding: 12px 16px;
      }

      .card-content {
        padding: 16px;
      }
    }

    .info-grid {
      grid-template-columns: 1fr;
      gap: 16px;

      .info-item {
        .info-label {
          width: 120px;
          text-align: left;
        }
      }
    }

    .process-steps {
      .step-item {
        margin-bottom: 20px;

        .step-number {
          width: 24px;
          height: 24px;
          font-size: 12px;
          margin-right: 12px;
        }

        &:not(:last-child)::after {
          left: 11px;
          top: 28px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .subtask-audit-page {
    padding: 16px;

    .audit-layout {
      gap: 16px;
    }

    .left-panel {
      .panel-header {
        padding: 12px 16px;
      }

      .panel-content {
        padding: 16px;
        max-height: 300px;
      }
    }

    .right-panel {
      .page-header {
        flex-direction: column;
        gap: 12px;
        padding: 16px;

        .header-actions {
          width: 100%;
          justify-content: center;
        }
      }
    }

    .info-grid {
      grid-template-columns: 1fr;
      gap: 12px;

      .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;

        .info-label {
          width: auto;
          text-align: left;
          font-size: 13px;
        }

        .info-value {
          font-size: 14px;
        }
      }
    }

    .process-steps {
      .step-item {
        margin-bottom: 16px;

        .step-number {
          width: 20px;
          height: 20px;
          font-size: 11px;
          margin-right: 10px;
        }

        &:not(:last-child)::after {
          left: 9px;
          top: 24px;
        }

        .step-content {
          .step-title {
            font-size: 13px;
          }

          .step-person, .step-time, .step-note {
            font-size: 11px;
          }
        }
      }
    }
  }
}
</style>
