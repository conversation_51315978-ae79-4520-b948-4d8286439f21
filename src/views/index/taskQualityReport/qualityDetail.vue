<route>
{
  meta: {
    childTitle: '任务质量报告详情',
  },
}
</route>
<script setup lang="ts" name="taskQualityReportDetail">
import { ref, onMounted, computed, inject } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()
const axios = inject('#axios') as Request

// 获取路由参数
const taskId = route.query.id as string
const type = route.query.type as string

// 页面数据
const taskDetail = ref<any>({})
const loading = ref(false)

// 表单数据
const formData = ref({
  issues: '', // 问题及原因
  improvement: '', // 改进计划
  riskWarning: '' // 风险预警
})

// 计算剩余天数
const calculateRemainingDays = (endDate: string) => {
  if (!endDate) return 0
  const end = dayjs(endDate)
  const now = dayjs()
  return end.diff(now, 'day')
}

// 根据进度获取数据产出描述
const getDataOutputDescription = (completionRate: number) => {
  if (completionRate === 0) {
    return '无有效数据产出'
  } else if (completionRate < 50) {
    return '数据产出较低'
  } else if (completionRate >= 50 && completionRate < 100) {
    return '数据产出待加强'
  } else {
    return '数据产出完成'
  }
}

// 生成填报任务描述
const getFillingTaskDescription = (completed: number, total: number) => {
  if (total === 0) return ''

  const uncompletedCount = total - completed
  if (uncompletedCount === 0) {
    return `（${total} 项填报任务均已完成填报）`
  } else if (completed === 0) {
    return `（${total} 项填报任务均未完成填报）`
  } else {
    return `（${uncompletedCount} 项填报任务未完成填报）`
  }
}

// 根据填报进度获取人员到位状态
const getPersonnelStatus = (completed: number, total: number) => {
  if (completed === 0) {
    return '未提交'
  } else if (completed === total) {
    return '已提交'
  } else {
    return '进行中'
  }
}

// 根据进度和时间获取人员到位问题解读
const getPersonnelProblemDescription = (completed: number, total: number, remainingDays: number) => {
  const isCompleted = completed === total
  const hasSubmission = completed > 0

  if (remainingDays < 0) {
    // 已超期
    if (isCompleted) {
      return '填报完成无填报风险'
    } else {
      return '填报已超期并且未完成'
    }
  } else {
    // 未超期
    if (isCompleted) {
      return '填报完成无填报风险'
    } else if (hasSubmission) {
      return '响应进度正常'
    } else if (remainingDays <= 5) {
      return '填报有延误风险'
    } else {
      return '响应进度正常'
    }
  }
}

// 根据任务状态获取审核情况
const getAuditStatus = (taskStatus: number | string) => {
  // 处理字符串状态
  if (taskStatus === '已完成' || taskStatus === 5) {
    return '已审核'  // 已完成状态特殊处理
  } else if (taskStatus === '已终止' || taskStatus === 8) {
    return '已审核'  // 已终止
  } else {
    return '待审核'  // 其他状态
  }
}

// 根据任务状态和填报进度获取审核问题解读
const getAuditProblemDescription = (taskStatus: number | string, completed: number, total: number) => {
  const isFillingCompleted = completed === total

  // 特殊处理：如果任务状态是已完成，直接返回审核完成
  if (taskStatus === '已完成' || taskStatus === 5) {
    return '填报完成，审核完成'
  }

  // 其他状态的处理逻辑
  if (!isFillingCompleted) {
    return '未填报完成未到审核流程'
  } else if ((taskStatus === '已终止' || taskStatus === 8) && isFillingCompleted) {
    return '填报完成，审核完成'  // 已终止且填报完成
  } else {
    return '填报完成，待审核'  // 其他状态且填报完成
  }
}

// 进度数据 - 使用计算属性动态生成
const progressData = computed(() => {
  const task = taskDetail.value

  // 从reportTaskStatus解析完成情况，格式如 "0/1"
  const reportStatus = task.reportTaskStatus || '0/0'
  const [completed, total] = reportStatus.split('/').map(Number)
  const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0

  // 计算剩余天数
  const remainingDays = calculateRemainingDays(task.deadline)

  // 获取数据产出描述
  const dataOutputDesc = getDataOutputDescription(completionRate)

  // 获取人员到位状态和问题解读
  const personnelStatus = getPersonnelStatus(completed, total)
  const personnelProblemDesc = getPersonnelProblemDescription(completed, total, remainingDays)

  // 获取审核状态和问题解读
  console.log('任务状态调试信息:', {
    taskStatus: task.status,
    statusType: typeof task.status,
    completed: completed,
    total: total,
    taskName: task.name
  })

  const auditStatus = getAuditStatus(task.status)
  const auditProblemDesc = getAuditProblemDescription(task.status, completed, total)

  console.log('审核状态结果:', {
    auditStatus: auditStatus,
    auditProblemDesc: auditProblemDesc
  })

  // 获取填报范围信息
  const fillingRange = task.fillingRange || '填报范围'

  // 生成填报任务描述
  const fillingTaskDesc = getFillingTaskDescription(completed, total)

  // 生成问题解读文本
  let problemDescription = ''
  if (remainingDays < 0) {
    problemDescription = `任务已经结束，${dataOutputDesc}。`
  } else {
    problemDescription = `离截止期剩余 ${remainingDays} 天，${dataOutputDesc}。`
  }

  return [
    {
      category: '填报进度',
      actual: `${completed}/${total}${fillingTaskDesc}`,
      expected: problemDescription
    },
    {
      category: '人员到位',
      actual: `${fillingRange}（${personnelStatus}）`,
      expected: personnelProblemDesc
    },
    {
      category: '审核情况',
      actual: auditStatus,
      expected: auditProblemDesc
    }
  ]
})

// 保存表单数据 - 保存所有5个板块的数据
const saveFormData = () => {
  const saveData = {
    taskId: taskId,
    // 1. 任务信息
    taskInfo: {
      id: taskDetail.value.id,
      name: taskDetail.value.name,
      creator: taskDetail.value.creator,
      period: taskDetail.value.period,
      createDepartment: taskDetail.value.createDepartment,
      deadline: taskDetail.value.deadline,
      status: taskDetail.value.status,
      createTime: taskDetail.value.createTime
    },
    // 2. 进度与完成情况
    progressData: progressData.value,
    // 3. 问题及原因 (可编辑)
    issues: formData.value.issues,
    // 4. 改进计划 (可编辑)
    improvement: formData.value.improvement,
    // 5. 风险预警 (可编辑)
    riskWarning: formData.value.riskWarning,
    // 保存时间
    updateTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
  }

  // 保存到localStorage - 使用多集合存储
  const existingData = JSON.parse(localStorage.getItem('qualityReportDetails') || '[]')
  const existingIndex = existingData.findIndex((item: any) => item.taskId === taskId)

  if (existingIndex !== -1) {
    existingData[existingIndex] = saveData
  } else {
    existingData.push(saveData)
  }

  localStorage.setItem('qualityReportDetails', JSON.stringify(existingData))

  // 同步更新表格页面的localStorage数据
  updateTablePageReportStatus(taskId, saveData.updateTime)

  ElMessage.success('保存成功')
}

// 更新表格页面的报告状态
const updateTablePageReportStatus = (taskId: string, updateTime: string) => {
  // 获取表格页面的localStorage数据
  const qualityReports = JSON.parse(localStorage.getItem('qualityReports') || '[]')

  // 查找对应的报告数据
  let reportData = qualityReports.find((report: any) => report.id === taskId)

  if (reportData) {
    // 更新现有数据：状态改为生成完成
    reportData.reportStatus = 1 // 生成完成
    reportData.reportGenerateTime = updateTime
  } else {
    // 创建新的报告数据：状态为生成完成
    qualityReports.push({
      id: taskId,
      reportStatus: 1, // 生成完成
      reportGenerateTime: updateTime
    })
  }

  // 更新localStorage
  localStorage.setItem('qualityReports', JSON.stringify(qualityReports))
}

// 加载表单数据 - 支持3个可编辑板块的回显
const loadFormData = () => {
  const existingData = JSON.parse(localStorage.getItem('qualityReportDetails') || '[]')
  const savedData = existingData.find((item: any) => item.taskId === taskId)

  if (savedData) {
    // 回显可编辑的3个板块数据
    formData.value = {
      issues: savedData.issues || '',
      improvement: savedData.improvement || '',
      riskWarning: savedData.riskWarning || ''
    }

    console.log('已加载保存的表单数据:', formData.value)
    console.log('完整保存数据:', savedData)
  } else {
    console.log('未找到保存的表单数据')
  }
}

// 获取任务详情数据
const getTaskDetail = async () => {
  if (!taskId) {
    ElMessage.error('缺少任务ID参数')
    return
  }

  loading.value = true
  try {
    // 调用API获取任务详情
    const response = await axios?.get(`/api/filling/plan-task/${taskId}`)
    const currentTask = response.data

    console.log('API返回的任务数据:', currentTask)

    if (currentTask) {
      // 根据API返回的数据结构映射字段
      taskDetail.value = {
        id: currentTask.id,
        name: currentTask.name || '任务名称',
        creator: currentTask.creatorName || '创建人',
        period: getPeriodText(currentTask.fillingPeriodType) || '填报期间',
        createDepartment: currentTask.createdDepartment || '创建部门',
        deadline: currentTask.endDate ? dayjs(currentTask.endDate).format('YYYY-MM-DD HH:mm:ss') : '截止时间',
        status: getTaskStatusText(currentTask.status) || '进行中',
        createTime: currentTask.creationTime ? dayjs(currentTask.creationTime).format('YYYY-MM-DD HH:mm:ss') : '创建时间',
        fillingRange: currentTask.fillingRange || '填报范围',
        fillingMode: currentTask.fillingMode,
        staffs: currentTask.staffs || [],
        departments: currentTask.departments || [],
        planTaskAreaOrganizationUnits: currentTask.planTaskAreaOrganizationUnits || [],
        reportTaskStatus: currentTask.reportTaskStatus || '0/0'
      }

      console.log('映射后的任务详情:', taskDetail.value)

      // 加载表单数据
      loadFormData()
    } else {
      ElMessage.error('未找到任务详情')
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    ElMessage.error('获取任务详情失败')
  } finally {
    loading.value = false
  }
}

// 获取任务状态文本
const getTaskStatusText = (status: number) => {
  switch (status) {
    case 14: return '待执行'
    case 3: return '进行中'
    case 13: return '待审核'
    case 5: return '已完成'
    case 8: return '已终止'
    default: return '未知状态'
  }
}

// 获取填报周期文本
const getPeriodText = (fillingPeriodType: number) => {
  switch (fillingPeriodType) {
    case 1: return '一次性'
    case 2: return '每日'
    case 3: return '每周'
    case 4: return '每月'
    case 5: return '每季度'
    case 6: return '每年'
    default: return '一次性'
  }
}

// 返回列表
const goBack = () => {
  router.back()
}

onMounted(() => {
  getTaskDetail()
})
</script>

<template>
  <div class="task-quality-report-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">任务质量报告</h2>
      </div>
      <div class="header-right">
        <el-button @click="saveFormData" type="primary">保存</el-button>
        <el-button @click="goBack">返回</el-button>
      </div>
    </div>

    <!-- 内容区域 - 平铺式布局 -->
    <div class="content-container" v-loading="loading">

      <!-- 1. 任务信息 -->
      <div class="content-section">
        <div class="section-header">
          <div class="section-icon">1</div>
          <div class="section-title">任务信息</div>
        </div>
        <div class="task-info-grid">
          <div class="info-row">
            <div class="info-item">
              <label>任务名称：</label>
              <span>{{ taskDetail.name }}</span>
            </div>
            <div class="info-item">
              <label>创建人：</label>
              <span>{{ taskDetail.creator }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <label>填报期间：</label>
              <span>{{ taskDetail.period }}</span>
            </div>
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ taskDetail.createTime }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <label>填报截止时间：</label>
              <span>{{ taskDetail.deadline }}</span>
            </div>
            <div class="info-item">
              <label>创建部门：</label>
              <span>{{ taskDetail.createDepartment }}</span>
            </div>
          </div>
          <div class="info-row">
            <div class="info-item">
              <label>任务状态：</label>
              <span>{{ taskDetail.status }}</span>
            </div>
            <div class="info-item">
              <!-- 预留位置 -->
            </div>
          </div>
        </div>
      </div>

      <!-- 2. 进度与完成情况 -->
      <div class="content-section">
        <div class="section-header">
          <div class="section-icon">2</div>
          <div class="section-title">进度与完成情况</div>
        </div>
        <div class="progress-table">
          <table class="custom-table">
            <thead>
              <tr>
                <th>指标</th>
                <th>实际值</th>
                <th>问题解读</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in progressData" :key="index">
                <td>{{ item.category }}</td>
                <td>{{ item.actual }}</td>
                <td>{{ item.expected }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 3. 问题及原因 -->
      <div class="content-section">
        <div class="section-header">
          <div class="section-icon">3</div>
          <div class="section-title">问题及原因</div>
        </div>
        <div class="form-content">
          <el-input
            v-model="formData.issues"
            type="textarea"
            :rows="6"
            placeholder="请输入问题及原因分析..."
            class="form-textarea"
          />
        </div>
      </div>

      <!-- 4. 改进计划 -->
      <div class="content-section">
        <div class="section-header">
          <div class="section-icon">4</div>
          <div class="section-title">改进计划</div>
        </div>
        <div class="form-content">
          <el-input
            v-model="formData.improvement"
            type="textarea"
            :rows="4"
            placeholder="请输入改进计划..."
            class="form-textarea"
          />
        </div>
      </div>

      <!-- 5. 风险预警 -->
      <div class="content-section">
        <div class="section-header">
          <div class="section-icon">5</div>
          <div class="section-title">风险预警</div>
        </div>
        <div class="form-content">
          <el-input
            v-model="formData.riskWarning"
            type="textarea"
            :rows="4"
            placeholder="请输入风险预警信息..."
            class="form-textarea"
          />
        </div>
      </div>

    </div>
  </div>
</template>

<style scoped lang="scss">
.task-quality-report-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 20px;

  .page-title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }
}

// 移除步骤导航样式

.content-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.content-section {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;

    .section-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: #409eff;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      font-weight: 600;
      margin-right: 12px;
    }

    .section-title {
      font-size: 20px;
      font-weight: 600;
      color: #303133;
    }
  }
}

.task-info-grid {
  .info-row {
    display: flex;
    margin-bottom: 20px;

    .info-item {
      flex: 1;
      display: flex;
      align-items: center;

      label {
        min-width: 120px;
        font-weight: 500;
        color: #606266;
        margin-right: 10px;
      }

      span {
        color: #303133;
      }
    }
  }
}

.progress-table {
  .custom-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #ebeef5;

    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #ebeef5;
    }

    th {
      background-color: #f5f7fa;
      font-weight: 600;
      color: #909399;
      font-size: 14px;
    }

    td {
      color: #606266;
      font-size: 14px;
    }

    tr:hover {
      background-color: #f5f7fa;
    }
  }
}

.form-content {
  .form-textarea {
    width: 100%;

    :deep(.el-textarea__inner) {
      border-radius: 6px;
      border: 1px solid #dcdfe6;
      font-size: 14px;
      line-height: 1.6;

      &:focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }
  }
}
</style>
