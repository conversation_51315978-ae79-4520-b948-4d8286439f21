<script setup lang="ts" name="batchurge">
const emits = defineEmits(['update:modelValue'])
const urgeList = ref([0,1])
const onClickConfirm = () => {
	ElMessage.success('催办成功')
	urgeList.value = []	
	onClose()
}

const onClose = () => {
	emits('update:modelValue', false)
}
</script>
<template>
	<Dialog v-bind="$attrs" @click-confirm="onClickConfirm" @click-close="onClose" @close="onClose">
		<div class="df aic">
			<Icons name="Warning" class="mg-right-5" color="#f00"</Icons>
			将批量通知选择的这些任务正在进行环节的处理人。只催办待审核、进行中的任务
		</div>
		<el-checkbox-group v-model="urgeList" class="mg-top-10">
			<el-checkbox label="系统通知" :value="0" />
			<el-checkbox label="渝快政工作通知" :value="1" />
			<el-checkbox label="渝快政Ding通知" :value="2" />
		</el-checkbox-group>
	</Dialog>
</template>
<style lang="scss" scoped></style>
