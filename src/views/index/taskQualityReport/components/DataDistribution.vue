<script setup lang="ts">
import {nextTick, ref} from 'vue'
import {ElMessage} from 'element-plus'
import departmentFavoriteComp from '@/components/common/department-favorite-comp.vue'

const emits = defineEmits(['confirm', 'changeColumn'])
const props = defineProps({
	departmentData: {
		type: Array,
		default: () => [],
	},
	columnData: {
		type: Array,
		default: () => [],
	},
	xlsxIndex: {
		type: Number,
		default: 0,
	},
})

const formRef = ref()
const formProps = ref([
	{
		label: '分发方式',
		prop: 'distribution',
		type: 'select',
		options: [
			{label: '指定行分发给指定部门', value: 1},
			{label: '指定内容分发给指定部门', value: 2},
		],
	},
	{
		label: '选择数据列',
		prop: 'column',
		type: 'select',
		options: [
			...Array.from({length: 26}, (_, i) => ({label: String.fromCharCode(65 + i), value: i})),
		],
		formShow: false,
	},
	{
		label: '分发规则',
		prop: '_rule',
	},
])

const formRules = {
	distribution: [{required: true, message: ''}],
	column: [{required: true, message: ''}],
	_rule: [{required: true, message: ''}],
}

const form: any = ref({distribution: 1, column: -1})
const rules = ref([{lv: '', rv: ''}])

const onFormChange = (val: any, item: any) => {
	if (item.prop === 'distribution') {
		formProps.value[1].formShow = val === 2
		if (form.value.hasOwnProperty('column')) {
			delete form.value.column
		}
		rules.value = [{lv: '', rv: ''}]
	} else if (item.prop === 'column') {
		rules.value.length = 0
		emits('changeColumn', val, item)
		nextTick(() => {
			rules.value = [{lv: '', rv: ''}]
		})
	}
}

const onClickAddRule = () => {
	rules.value.push({lv: '', rv: ''})
}

const removeRule = (index: number) => {
	rules.value.splice(index, 1)
}

const onConfirm = () => {
	if (form.value.distribution === undefined) {
		ElMessage.warning('请选择分发方式')
		return
	}
	if (
		(form.value.column === -1 || form.value.column === undefined) &&
		form.value.distribution === 2
	) {
		ElMessage.warning('请选择分发列')
		return
	}

	if (rules.value.some((v: any) => v.lv === '' || v.rv === '')) {
		ElMessage.warning('请填写分发规则')
		return
	}

	const ruleStore = localStorage.getItem('ruleStore')
	if (!ruleStore) {
		localStorage.setItem('ruleStore', JSON.stringify([{form: form.value, rules: rules.value}]))
	} else {
		const ruleStoreData = JSON.parse(ruleStore)
		ruleStoreData[props.xlsxIndex] = {form: form.value, rules: rules.value}
		localStorage.setItem('ruleStore', JSON.stringify(ruleStoreData))
	}
	emits('confirm', JSON.parse(localStorage.getItem('ruleStore') as string))
}
</script>
<template>
	<Dialog v-bind="$attrs" @click-confirm="onConfirm">
		<Form
			ref="formRef"
			v-model="form"
			:props="formProps"
			:rules="formRules"
			:enable-button="false"
			@change="onFormChange"
		>
			<template #form-_rule="scoped">
				<div class="items choose-row">
					<div class="item" v-for="(item, index) of rules as any">
						<template v-if="form.distribution === 2">
							<span>该列内容为</span>
							<el-select v-model="item.lv" :multiple="true" style="max-width: 100px">
								<el-option
									v-for="(col, index) of columnData as any"
									:key="index"
									:label="col.label"
									:value="index"
								/>
							</el-select>
						</template>
						<template v-else>
							<span>行</span>
							<el-input v-model.number="item.lv" style="width: 100px" />
						</template>
						<span class="mg-left-20">接收部门</span>
						<departmentFavoriteComp
							placeholder="请选择"
							:multiple="false"
							:data="props.departmentData"
							:type="'modal'"
							@change="(vals) => (item.rv = vals[0].id)"
						></departmentFavoriteComp>
						<el-button type="danger" class="mg-left-5 cancel" @click="removeRule(index)"
							>移除</el-button
						>
					</div>
				</div>
				<div class="add-rule" @click="onClickAddRule">+ 添加分发</div>
			</template>
		</Form>
	</Dialog>
</template>
<style scoped lang="scss">
.items {
	width: 100%;
	.item {
		align-items: center;
		display: flex;
		margin-bottom: 10px;
		white-space: nowrap;
		span {
			margin-right: 5px;
		}
	}
}
.add-rule {
	border: 1px dashed var(--z-line);
	cursor: pointer;
	margin: 10px 0;
	text-align: center;
	width: 100%;
}
</style>
