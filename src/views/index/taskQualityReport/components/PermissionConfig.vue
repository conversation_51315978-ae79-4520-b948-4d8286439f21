<script setup lang="ts" name="permissionconfig">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import {USER_ROLES_ENUM} from '@/define/organization.define'
import {ACTION_KEY, useLocalStorage} from '@/hooks/useLocalStorage'
import {useGuid} from '@/hooks/useGuid'
import {useSleep} from '@/hooks/useSleep'
import * as DialogComp from '@/components/common/dialog-comp.vue'
import * as FormComp from '@/components/common/form-comp.vue'
import * as BaseTableComp from '@/components/common/basetable-comp.vue'
// 使用 Element Plus 的 el-pagination 组件

const storage: any = useLocalStorage()
const departments = [{label: '生态环境局', value: '生态环境局'}]
const roles = Object.values(USER_ROLES_ENUM)
	.filter(
		(item) =>
			![
				USER_ROLES_ENUM.YKZ,
				USER_ROLES_ENUM.USER_ADMIN,
				USER_ROLES_ENUM.DATA_EXPORT,
			].includes(item as any)
	)
	.map((item) => ({label: item, value: item}))

const permissions = [
	{label: '编辑', value: '编辑'},
	{label: '催办', value: '催办'},
	{label: '终止', value: '终止'},
	{label: '复制', value: '复制'},
	{label: '绑定任务', value: '绑定任务'},
	{label: '绑定流程说明', value: '绑定流程说明'},
	{label: '重置', value: '重置'},
	{label: '删除', value: '删除'},
	{label: '撤回', value: '撤回'},
]

const formProps = [
	{label: '部门', prop: 'department', type: 'select', options: departments},
	{label: '角色', prop: 'role', type: 'select', options: roles},
]
const columns = [
	{label: '部门', prop: 'department'},
	{label: '角色', prop: 'role'},
	{label: '角色数量', prop: 'roleCount'},
]
const tableButtons = [
	{type: 'primary', code: 'edit', label: '编辑'},
	{type: 'danger', code: 'delete', label: '删除'},
]

const dialogFormProps = [
	{label: '权限部门', prop: 'department', type: 'select', options: departments},
	{label: '权限角色', prop: 'roles', type: 'select', options: roles, attrs: {multiple: true}},
	{
		label: '权限选择',
		prop: 'permissions',
		type: 'select',
		options: permissions,
		attrs: {multiple: true},
	},
]

const loading = ref(false)
const searchForm = ref({department: '', role: ''})
const currentPage = ref(1)
const pageSize = ref(10)
const tableRef = ref()
const tableData: any = ref([])
const tableCurrentData: any = ref([])
const filterTableData: any = ref([])
const currentRow: any = ref(null)
const dialogForm: any = ref({roles: [], permissions: []})
const dialogFormRef = ref()
const dialogFormRules = {
	roles: [{required: true, message: '请选择角色', trigger: 'change'}],
	permissions: [{required: true, message: '请选择权限', trigger: 'change'}],
	department: [{required: true, message: '请选择部门', trigger: 'change'}],
}
const show = ref(false)

const handleClickButtons = ({btn, row}: any) => {
	if (btn.code === 'edit') {
		currentRow.value = row
		dialogForm.value = row
		show.value = true
	} else if (btn.code === 'delete') {
		tableData.value = tableData.value.filter((item: any) => item !== row)
		tableCurrentData.value.length = 0
		useSleep().then(() => {
			tableCurrentData.value = tableData.value.slice(
				(currentPage.value - 1) * pageSize.value,
				currentPage.value * pageSize.value
			)
			ElMessage.success('删除成功')
			storage.save(ACTION_KEY.TempReportPermission, tableData.value)
		})
	}
}

const onOpened = () => {
	tableData.value = storage.get(ACTION_KEY.TempReportPermission) || []
	tableCurrentData.value = tableData.value.slice(
		(currentPage.value - 1) * pageSize.value,
		currentPage.value * pageSize.value
	)
}

const onClickPermissionConfirm = () => {
	dialogFormRef.value.validate((valid: boolean) => {
		if (!valid) {
			return
		}
		loading.value = true
		tableCurrentData.value.length = 0
		useSleep().then(() => {
			loading.value = false
			if (!currentRow.value) {
				const newData = {
					...dialogForm.value,
					roleCount: dialogForm.value?.roles.length,
					id: useGuid(),
				}
				tableData.value.unshift(newData)
			} else {
				tableData.value.some((item: any) => {
					if (item.id === dialogForm.value.id) {
						Object.assign(item, dialogForm.value, {
							roleCount: dialogForm.value.roles.length,
						})
						return true
					}
				})
			}

			tableCurrentData.value = tableData.value.slice(
				(currentPage.value - 1) * pageSize.value,
				currentPage.value * pageSize.value
			)

			ElMessage.success('保存成功')
			storage.save(ACTION_KEY.TempReportPermission, tableData.value)
			show.value = false
		})
	})
}

const onPaginationChange = (value: number, type: string) => {
	if (type === 'page') {
		currentPage.value = value
	} else {
		pageSize.value = value
	}
	tableCurrentData.value = tableData.value.slice(
		(currentPage.value - 1) * pageSize.value,
		currentPage.value * pageSize.value
	)
}

const onSearch = () => {
	loading.value = true
	tableCurrentData.value.length = 0
	currentPage.value = 1
	useSleep().then(() => {
		loading.value = false
		if (!searchForm.value.department && !searchForm.value.role) {
			tableCurrentData.value = tableData.value.slice(
				(currentPage.value - 1) * pageSize.value,
				currentPage.value * pageSize.value
			)
			filterTableData.value.length = 0
			return
		}
		filterTableData.value = tableData.value.filter((item: any) => {
			return (
				item.department === searchForm.value.department ||
				item.roles.includes(searchForm.value.role)
			)
		})
		tableCurrentData.value = filterTableData.value.slice(
			(currentPage.value - 1) * pageSize.value,
			currentPage.value * pageSize.value
		)
	})
}
</script>
<template>
	<DialogComp v-bind="$attrs" :enable-confirm="false" @opened="onOpened">
		<FormComp
			v-model="searchForm"
			:props="formProps"
			:column-count="3"
			:enable-reset="false"
			:loading="loading"
			button-vertical="flowing"
			label-width="40"
			confirm-text="搜索"
			class="mg-bottom-20"
			@submit="onSearch"
		></FormComp>
		<BaseTableComp
			ref="tableRef"
			v-model="tableCurrentData"
			:auto-height="true"
			:columns="columns"
			:buttons="tableButtons"
			:enable-toolbar="false"
			:enable-own-button="false"
			:loading="loading"
			@click-button="handleClickButtons"
		>
			<template #role="scoped">
				{{ scoped.row.roles.join() }}
			</template>
		</BaseTableComp>
		<el-pagination
			:total="filterTableData.length ? filterTableData.length : tableData.length"
			:current-page="currentPage"
			:page-size="pageSize"
			@current-change="onPaginationChange($event, 'page')"
			@size-change="onPaginationChange($event, 'size')"
			layout="total, sizes, prev, pager, next, jumper"
			:page-sizes="[10, 20, 30, 50]"
			background
		/>
		<template #footer-button>
			<el-button
				type="primary"
				@click=";(show = true), (currentRow = null), (dialogForm = {})"
				>新增</el-button
			>
		</template>
	</DialogComp>

	<DialogComp
		v-model="show"
		:title="currentRow ? '编辑权限' : '新增权限'"
		:destroy-on-close="true"
		:loading="loading"
		loading-text="保存中"
		width="600"
		@click-confirm="onClickPermissionConfirm"
	>
		<FormComp
			ref="dialogFormRef"
			v-model="dialogForm"
			:props="dialogFormProps"
			:rules="dialogFormRules"
			:enable-button="false"
		></FormComp>
	</DialogComp>
</template>
<style scoped lang="scss"></style>
