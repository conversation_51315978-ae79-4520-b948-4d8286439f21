<script setup lang="ts" name="statementtask">
import queryString from 'querystring'
import {ref, inject, computed, onActivated, h, reactive, onMounted} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import {Axios} from 'axios'
import {useTaskManageStore} from '@/stores/taskManageStore'
import {ElNotification, ElMessageBox, ElCheckbox, CheckboxValueType, ElMessage, dayjs} from 'element-plus'
import {
	periodList,
	statusTrans,
	StatusSelect,
	ReportsFlowStatus,
	ReportsFlowStatusType,
	ReportsFlowStatusEnum,
	reportStatusTrans,
	GetBatch,
	surplusDate,
	PlanTaskStatus,
} from '@/define/statement.define'
import {useUserStore} from '@/stores/useUserStore'
import util from '@/plugin/util'
import {STAFFROLEARRAY} from '@/define/organization.define'
import {CirclePlusFilled, Bell, ElementPlus} from '@element-plus/icons-vue'
import {
	ExecutionReportTask,
	ReportRevokeInnerWrite,
	DeleteReportFlowTask,
	PushReportNotice,
	GetPlanTaskProgress,
	urging,
} from '@/api/ReportApi'

import * as CopyReport from './components/CopyReport.vue'
import * as PermissionConfig from './components/PermissionConfig.vue'
import * as BatchUrge from './components/BatchUrge.vue'

enum LTABS {
	ICREATE = '我的创建',
	ITURN = '我的转发',
}

const route = useRoute()
const router = useRouter()
const UserStore = useUserStore()
const taskManageStore = useTaskManageStore()
const axios = inject<Axios>('#axios')
const cureerntText = ref('任务创建')
const pageParams = ref({
	MaxResultCount: 10,
	SkipCount: 0,
})
const searchParams = ref<any>({
	Name: null,
	FillingPeriodType: undefined,
	PlanTaskStatus: undefined,
	isSelf: true,
})
const loading = ref(false)
// 移除tab切换，默认使用"我创建的"内容
const currentIndex = ref(1)
// 初始化数据和表格配置
const initData = () => {
	taskManageStore.taskList = []
	pageParams.value = {
		MaxResultCount: 10,
		SkipCount: 0,
	}

	searchParams.value = {
		Name: null,
		FillingPeriodType: undefined,
		PlanTaskStatus: undefined,
		isSelf: true,
	}

	// 请求计划任务列表
	getTaskList(11)
	// 设置button列表显示
	buttons.value = [
		{
			type: 'primary',
			code: 'urge',
			title: '催办',
			more: true,
			icon: '<i i-majesticons-clock-line></i>',
			verify: 'row.status === 2 || row.status === 3 || row.status === 6 || row.status === 7',
		},
		{
			type: 'primary',
			code: 'submitAudit',
			title: '执行',
			more: false,
			icon: '<i i-majesticons-send-line></i>',
			verify: `row.status === ${ReportsFlowStatusEnum.PendingExecution} || row.status === ${ReportsFlowStatusEnum.Rejected} || row.status === ${ReportsFlowStatusEnum.Revoke}`,
		},
		{
			type: 'primary',
			code: 'detail',
			title: '详情',
			more: false,
			icon: '<i i-majesticons-eye-line></i>',
			verify: 'true',
		},
		{
			type: 'primary',
			code: 'edit',
			title: '编辑',
			more: true,
			icon: '<i i-majesticons-pencil-alt-line></i>',
			verify: `"${UserStore.userInfo.id}" ==  row.creatorId && row.status === 1 || "${UserStore.userInfo.id}" ==  row.creatorId && row.status === 4`,
		},
		{
			type: 'primary',
			code: 'terminate',
			title: '终止',
			more: true,
			icon: '<i i-majesticons-stop-circle-line></i>',
			verify: `"${UserStore.userInfo.id}" == row.creatorId && (row.status === 2 || row.status === 3 || row.status === 6 || row.status === 7)`,
		},
		{
			type: 'danger',
			code: 'del',
			more: true,
			title: '删除',
			icon: '<i i-majesticons-minus-circle-line></i>',
			verify: 'true',
		},
	]
	// 表格数据展示
	colData.value = [
		{
			title: '任务名称',
			field: 'name',
		},
		{
			title: '填报范围',
			field: 'fillingRange',
		},
		// {
		// 	title: '填报周期',
		// 	field: 'fillingPeriodType',
		// 	width: '100',
		// },
		{
			title: '填报方式',
			field: 'fillingMode',
			width: '100',
		},
		{
			title: '创建人',
			field: 'creatorName',
			width: '80',
		},
		{
			title: '创建人科室',
			field: 'createdDepartment',
			width: '110',
		},
		{
			title: '创建时间',
			field: 'creationTime',
			width: '150',
		},
		{
			title: '截止时间',
			field: 'endTime',
			width: '150',
		},
		{
			title: '状态',
			field: 'status',
			width: '100',
		},
	]
}
const tableButtons = {
	[LTABS.ICREATE]: [
		// {
		// 	type: 'primary',
		// 	code: 'urge',
		// 	more: true,
		// 	title: '催办',
		// 	icon: '<i i-majesticons-clock-line></i>',
		// 	verify: 'row.status === 2 || row.status === 3 || row.status === 6 || row.status === 7',
		// },
		{
			type: 'primary',
			code: 'submitAudit',
			more: false,
			title: '执行',
			icon: '<i i-majesticons-send-line></i>',
			verify: `row.status === ${ReportsFlowStatusEnum.PendingExecution} || row.status === ${ReportsFlowStatusEnum.Rejected} || row.status === ${ReportsFlowStatusEnum.Revoke}`,
		},
		{
			type: 'primary',
			code: 'detail',
			more: false,
			title: '详情',
			verify: 'true',
		},
		{
			type: 'primary',
			more: true,
			code: 'edit',
			title: '编辑',
			verify: `"${UserStore.userInfo.id}" ==  row.creatorId && row.status === 1 || row.status === 11 || "${UserStore.userInfo.id}" ==  row.creatorId && (row.status === 14 || row.status === 4)`,
		},

		{
			type: 'danger',
			code: 'terminate',
			more: true,
			title: '终止',
			verify: `"${UserStore.userInfo.id}" == row.creatorId && (row.status === 2 || row.status === 3 || row.status === 6 || row.status === 7)`,
		},
		{
			type: 'primary',
			code: 'revoke',
			more: true,
			title: '撤回',
			verify: `row.status === ${ReportsFlowStatusEnum.PendingReview}`,
		},
		// {
		// 	type: 'danger',
		// 	more: true,
		// 	code: 'urging',
		// 	title: '催办',
		// 	icon: '<i i-majesticons-minus-circle-line></i>',
		// 	verify: 'row.status == 3 || row.status == 13',
		// },
		{type: 'primary', code: 'copy', more: true, title: '复制', verify: 'true'},
		{
			type: 'danger',
			more: true,
			code: 'del',
			title: '删除',
			verify: 'row.status !== 3',
		},
	],
	[LTABS.ITURN]: [
		{
			type: 'primary',
			code: 'turnDetail',
			title: '详情',
			verify: 'true',
		},
		// {
		// 	type: 'warning',
		// 	code: 'turnExpediting',
		// 	title: '催办',
		// 	verify: 'true',
		// },
		{
			type: 'danger',
			code: 'turnDelete',
			title: '删除',
			verify: 'row._raw?.stop',
		},
		{
			type: 'primary',
			code: 'turnRevocation',
			title: '撤回',
			more: true,
			verify: 'true',
		},
		// {
		// 	type: 'danger',
		// 	more: true,
		// 	code: 'urging',
		// 	title: '催办',
		// 	icon: '<i i-majesticons-minus-circle-line></i>',
		// 	// verify: 'row.status == 4 && !row.endTime',
		// },
	],
}

// TableV2 列定义
const columns = ref<any>([
	{
		prop: 'name',
		label: '任务名称',
		slot: 'name',
	},
	{
		prop: 'fillingRange',
		label: '填报范围',
	},
	{
		prop: 'fillingStatus',
		label: '填报情况',
		width: 100,
		slot: 'fillingStatus',
	},
	{
		prop: 'endTime',
		label: '截止时间',
		width: 150,
		slot: 'endTime',
	},
	{
		prop: 'fillingPeriodType',
		label: '填报周期',
		width: 100,
		slot: 'fillingPeriodType',
	},
	{
		prop: 'status',
		label: '任务状态',
		width: 100,
		slot: 'status',
	},
	{
		prop: 'reportGenerateTime',
		label: '报告生成时间',
		width: 150,
		slot: 'reportGenerateTime',
	},
	{
		prop: 'reportStatus',
		label: '报告状态',
		width: 100,
		slot: 'reportStatus',
	},
	// {
	// 	prop: 'operation',
	// 	label: '操作',
	// 	width: 180,
	// 	slot: 'operation',
	// },
])

// 保留原有的colData用于tab切换时的动态配置
const colData = ref<any>([
	{
		title: '序号',
		field: 'index',
		width: '60',
	},
	{
		title: '任务名称',
		field: 'name',
	},
	{
		title: '填报范围',
		field: 'fillingRange',
	},
	{
		title: '报告生成时间',
		field: 'reportGenerateTime',
		width: '150',
	},
	{
		title: '报告状态',
		field: 'reportStatus',
		width: '100',
	},
	{
		title: '填报情况',
		field: 'fillingStatus',
		width: '100',
	},
	{
		title: '截止时间',
		field: 'endTime',
		width: '150',
	},
	{
		title: '填报周期',
		field: 'fillingPeriodType',
		width: '100',
	},
	{
		title: '任务状态',
		field: 'status',
		width: '100',
	},
	{
		title: '操作',
		field: 'operation',
		width: '180',
	},
])
// TableV2 按钮配置 - 使用函数动态生成
const getTableV2Buttons = (row: any) => {
	const buttons = [
		{
			label: '添加水印',
			type: 'primary',
			code: 'addWatermark',
			disabled: row.reportStatus !== 1, // 只有报告生成完成时才启用
		},
		{
			label: '详情',
			type: 'primary',
			code: 'detail',
		},
		{
			label: '删除',
			type: 'danger',
			code: 'del',
			disabled: row.reportStatus !== 1,
		}
	]
	return buttons
}

// 保留原有的静态配置用于兼容
const tableV2Buttons = ref<any>([
	{
		label: '添加水印',
		type: 'primary',
		code: 'addWatermark',
		disabled: 'row.reportStatus !== 1', // 只有报告生成完成时才启用
	},
	{
		label: '详情',
		type: 'primary',
		code: 'detail',
	},
	{
		label: '删除',
		type: 'danger',
		code: 'del',
		disabled: 'row.reportStatus !== 1',
	},
	// 屏蔽所有 more: true 的按钮
	// {
	// 	label: '编辑',
	// 	type: 'primary',
	// 	code: 'edit',
	// 	more: true, // 折叠到更多菜单
	// 	show: (row: any) => row && UserStore.userInfo?.id == row.creatorId && (row.status === 1 || row.status === 4),
	// },
	// {
	// 	label: '催办',
	// 	type: 'primary',
	// 	code: 'urge',
	// 	more: true, // 折叠到更多菜单
	// 	show: (row: any) => row && (row.status === 2 || row.status === 3 || row.status === 6 || row.status === 7),
	// },
	// {
	// 	label: '终止',
	// 	type: 'primary',
	// 	code: 'terminate',
	// 	more: true, // 折叠到更多菜单
	// 	show: (row: any) => row && UserStore.userInfo?.id == row.creatorId && (row.status === 2 || row.status === 3 || row.status === 6 || row.status === 7),
	// },
	// {
	// 	label: '复制',
	// 	type: 'primary',
	// 	code: 'copy',
	// 	more: true, // 折叠到更多菜单
	// },
	// {
	// 	label: '撤回',
	// 	type: 'primary',
	// 	code: 'revoke',
	// 	more: true, // 折叠到更多菜单
	// 	show: (row: any) => row && UserStore.userInfo?.id == row.creatorId,
	// },
])

// 保留原有的buttons用于兼容
const buttons = ref<any>([
	{
		type: 'primary',
		code: 'urge',
		title: '催办',
		more: true,
		icon: '<i i-majesticons-clock-line></i>',
		verify: 'row.status === 2 || row.status === 3 || row.status === 6 || row.status === 7',
	},
	{
		type: 'primary',
		code: 'submitAudit',
		title: '执行',
		more: false,
		icon: '<i i-majesticons-send-line></i>',
		verify: `row.status === ${ReportsFlowStatusEnum.PendingExecution} || row.status === ${ReportsFlowStatusEnum.Rejected} || row.status === ${ReportsFlowStatusEnum.Revoke}`,
	},
	{
		type: 'primary',
		code: 'detail',
		title: '详情',
		more: false,
		icon: '<i i-majesticons-eye-line></i>',
		verify: 'true',
	},
	{
		type: 'primary',
		code: 'edit',
		title: '编辑',
		more: true,
		icon: '<i i-majesticons-pencil-alt-line></i>',
		verify: `"${UserStore.userInfo.id}" ==  row.creatorId && row.status === 1 || "${UserStore.userInfo.id}" ==  row.creatorId && row.status === 4`,
	},
	{
		type: 'primary',
		code: 'terminate',
		title: '终止',
		more: true,
		icon: '<i i-majesticons-stop-circle-line></i>',
		verify: `"${UserStore.userInfo.id}" == row.creatorId && (row.status === 2 || row.status === 3 || row.status === 6 || row.status === 7)`,
	},
	{
		type: 'danger',
		code: 'del',
		more: true,
		title: '删除',
		icon: '<i i-majesticons-minus-circle-line></i>',
		verify: 'true',
	},
])

const tableData = computed(() => taskManageStore.taskList || [])

// 计算是否可以批量删除：选中的任务都必须是生成中或生成完成
const canBatchDelete = computed(() => {
	return selectionList.value.length > 0 &&
		   selectionList.value.every((task: any) => task.reportStatus === 1 || task.reportStatus === 2)
})

// 计算是否可以批量添加水印：选中的任务都必须报告生成完成
const canBatchAddWatermark = computed(() => {
	return selectionList.value.length > 0 &&
		   selectionList.value.every((task: any) => task.reportStatus === 1)
})

// 计算是否可以生成质量报告：选中的任务都必须是未生成状态
const canGenerateQualityReport = computed(() => {
	return selectionList.value.length > 0 &&
		   selectionList.value.every((task: any) => !task.reportStatus || task.reportStatus === undefined || task.reportStatus === null)
})

// 计算是否可以导出质量报告：选中的任务都必须是生成完成状态
const canExportQualityReport = computed(() => {
	return selectionList.value.length > 0 &&
		   selectionList.value.every((task: any) => task.reportStatus === 1)
})
const totalCount = computed(() => taskManageStore.totalCount)

const messageModalNotify = ref({
	audit: false,
	terminate: false,
	messageDing: false,
	NotifyDing: false,
	phoneDing: false,
})
const notifyType = ref<number>()

const currentActiveName = ref(LTABS.ICREATE)
const btnCode = ref()
// 表格按钮点击行为
const clickButton = (btn: {
	btn: {
		code: any
		title?: string
		verify?: string
	}
	scope: any
}) => {
	let urgingMessageTypes: any = []
	urgingList.value.forEach((item: any) => {
		if (item == '0') {
			urgingMessageTypes.push(0)
		} else {
			urgingMessageTypes.push(Number(item))
		}
	})
	switch (btn.btn.code) {
		case 'addWatermark':
			// 添加水印功能 - 检查报告状态
			if (btn.scope.reportStatus !== 1) {
				ElMessage.warning('只能为报告生成完成的任务添加水印')
				return
			}
			ElMessage.success('水印添加成功')
			break
		case 'copy':
			isCopyReport.value = true
			break
		case 'submitAudit':
			// 执行
			ExecutionReportTask({planTaskId: btn.scope.id}).then((res: any) => {
				ElMessage.success('已执行任务')
				getTaskList(11)
			})
			// ElMessageBox({
			// 	title: '执行',
			// 	message: () =>
			// 		h(ElCheckbox, {
			// 			modelValue: messageModalNotify.value.audit,
			// 			'onUpdate:modelValue': (val: any) => {
			// 				messageModalNotify.value.audit = val
			// 			},
			// 			label: '通知审核人！',
			// 		}),
			// })
			// 	.then(() => {

			// 		axios
			// 			?.post(
			// 				`/api/filling/plan-task/set-active?id=${btn.scope.id}&enabled=true&notice=${messageModalNotify.value.audit}`
			// 			)
			// 			.then((res) => {
			// 				getTaskList()
			// 				ElNotification.success({
			// 					title: '通知',
			// 					message: '已提交领导审核',
			// 				})
			// 			})
			// 	})
			// 	.catch((action) => {
			// 		messageModalNotify.value.audit = false
			// 		// console.log(action)
			// 	})

			break
		case 'detail':
			router.push({
				path: '/taskQualityReport/qualityDetail',
				query: {id: btn.scope.id, type: 'detail'},
			})

			// router.push({
			// 	path: '/taskQualityReport/task-detail',
			// 	query: {
			// 		reportTaskId: btn.scope.id,
			// 	},
			// })
			break
		case 'edit':
			router.push({path: '/taskQualityReport/create', query: {id: btn.scope.id}})
			break
		case 'export':
			break
		case 'del':
			ElMessageBox.confirm(
				'你正在删除质量任务，是否确定删除质量任务。',
				'删除确认',
				{
					confirmButtonText: '确认删除',
					cancelButtonText: '取消',
					type: 'warning',
				}
			)
				.then(() => {
					// 获取当前行的ID
					const taskId = btn.scope.id

					// 获取现有的localStorage数据
					const existingReports = JSON.parse(localStorage.getItem('qualityReports') || '[]')

					// 过滤掉当前任务对应的报告数据
					const filteredReports = existingReports.filter((report: any) =>
						report.id !== taskId
					)

					// 更新localStorage
					localStorage.setItem('qualityReports', JSON.stringify(filteredReports))

					ElMessage.success('质量任务删除成功')

					// 刷新列表以显示更新后的状态
					refreshTableData()
				})
				.catch(() => {
					ElMessage.info('已取消删除')
				})
			break
		case 'urge':
			// 催办

			ElMessageBox({
				title: '任务催办',
				showCancelButton: true,
				message: `将发送消息提示${btn.scope.status === 3 ? '填报人' : '审核人'}进行${
					btn.scope.status === 3 ? '填报' : '审核'
				}操作`,
				// () =>
				// 	h('div', {style: 'margin-left:10px'}, [
				// 		h(
				// 			'p',
				// 			{},
				// 			`将发送消息提示${btn.scope.status === 3 ? '填报人' : '审核人'}进行${
				// 				btn.scope.status === 3 ? '填报' : '审核'
				// 			}操作`
				// 		),
				// 		h(
				// 			'div',
				// 			{
				// 				style:
				// 					'display:flex;align-items:center;padding-right:60px;flex:1;justify-content: space-between;',
				// 			},
				// 			[
				// 				h('div', {}, [
				// 					h(ElCheckbox, {
				// 						modelValue: messageModalNotify.value.NotifyDing,
				// 						'onUpdate:modelValue': (val: any) => {
				// 							messageModalNotify.value.NotifyDing = val
				// 							messageModalNotify.value.messageDing = false
				// 							messageModalNotify.value.phoneDing = false
				// 							notifyType.value = val ? 1 : undefined
				// 						},
				// 					}),
				// 					h('span', {style: 'display:inline-block;margin-left:5px'}, '消息通知DING'),
				// 				]),
				// 				h('div', {}, [
				// 					h(ElCheckbox, {
				// 						modelValue: messageModalNotify.value.messageDing,
				// 						'onUpdate:modelValue': (val: any) => {
				// 							messageModalNotify.value.messageDing = val
				// 							messageModalNotify.value.NotifyDing = false
				// 							messageModalNotify.value.phoneDing = false
				// 							notifyType.value = val ? 2 : undefined
				// 						},
				// 					}),
				// 					h('span', {style: 'display:inline-block;margin-left:5px'}, '短信DING'),
				// 				]),
				// 				h('div', {}, [
				// 					h(ElCheckbox, {
				// 						modelValue: messageModalNotify.value.phoneDing,
				// 						'onUpdate:modelValue': (val: any) => {
				// 							messageModalNotify.value.phoneDing = val
				// 							messageModalNotify.value.messageDing = false
				// 							messageModalNotify.value.NotifyDing = false
				// 							notifyType.value = val ? 3 : undefined
				// 						},
				// 					}),
				// 					h('span', {style: 'display:inline-block;margin-left:5px'}, '电话DING'),
				// 				]),
				// 			]
				// 		),
				// 	]),

				// ,
			})
				.then(() => {
					axios
						?.post(
							`/api/filling/message-notice/urge?planTaskId=${btn.scope.id}${
								notifyType.value ? `&dingMessageType=${notifyType.value}` : ''
							}`
						)
						.then((res) => {
							// if (res.status === 200 || res.status === 204) {
							ElMessage.success('已通知相关人员')
							// }
						})
				})
				.catch((action) => {
					// console.log(action)
				})
			break
		case 'terminate':
			// 终止
			ElMessageBox({
				title: '确认要终止该项任务吗？',
				showCancelButton: true,
				message: () =>
					h('div', {style: 'margin-left:10px'}, [
						h('p', {style: 'color:red'}, '终止后，填报人将无法填报。'),
						h('div', {style: 'display:flex;align-items:center'}, [
							h(ElCheckbox, {
								modelValue: messageModalNotify.value.terminate,
								'onUpdate:modelValue': (val: any) => {
									messageModalNotify.value.terminate = val
								},
							}),
							h(
								'span',
								{style: 'display:inline-block;margin-left:5px'},
								'通知填报人及审核人！'
							),
						]),
					]),
			})
				.then(() => {
					axios
						?.put(
							`/api/filling/plan-task/stope?id=${btn.scope.id}&notice=${messageModalNotify.value.terminate}`
						)
						.then((res) => {
							ElMessage.success('已终止该任务')
							getTaskList(11)
							messageModalNotify.value.terminate = false
						})
				})
				.catch((action) => {
					messageModalNotify.value.audit = false
					// console.log(action)
				})
			break
		case 'revoke':
			// 撤回
			let arrId = urgingMessageTypes.map((item: any) => {
				return `types=${item}`
			})
			ElMessageBox({
				title: '提示',
				showCancelButton: true,
				message: () =>
					h('div', {style: 'margin-left:10px'}, [
						h('p', {style: 'font-size:16px'}, '您正在撤回您的创建任务。'),
						h(
							'p',
							{style: 'margin-top:10px;color:#9b9b9b;font-size:14px;'},
							'你正在执行任务撤回操作，原任务的审批记录将被清除，请确认该操作。'
						),
					]),
			}).then(() => {
				axios
					?.get(
						`/api/filling/plan-task/revoke-planTask/${btn.scope.id}?${arrId.join('&')}`
					)
					.then((res) => {
						ElMessage.success('已撤回该任务')
						getTaskList(11)
					})
			})
			break
		// ====================转发任务====================
		case 'turnRevocation':
			// 撤回
			let arr = urgingMessageTypes.map((item: any) => {
				return item
			})
			ElMessageBox({
				title: '提示',
				showCancelButton: true,
				message: () =>
					h('div', {style: 'margin-left:10px'}, [
						h('p', {style: 'font-size:16px'}, '您正在撤回您的转发任务。'),
						h(
							'p',
							{style: 'margin-top:10px;color:#9b9b9b;font-size:14px;'},
							'你正在进行任务撤回操作，原任务已收集数据将被清除，请确认该操作。'
						),
					]),
			}).then(() => {
				let parmas = {
					id: btn.scope._raw.id,
					// reportTaskId: btn.scope._raw.reportTask.id,
					// areaOrganizationUnitId: btn.scope._raw.areaOrganizationUnitId,
					types: arr,
				}
				ReportRevokeInnerWrite(parmas)
					.then(() => {
						ElMessage.success('已撤回任务')
						getReportTask(16)
					})
					.catch((err) => {
						window.errMsg(err, '撤回')
					})
			})

			break
		case 'turnDetail':
			router.push({
				path: '/taskQualityReport/report-task-detail',
				query: {
					reportTaskId: btn.scope.id,
					areaOrganizationUnitId: btn.scope._raw.areaOrganizationUnitId,
					id: btn.scope._raw.id,
					type: 'detail',
					from: 'myturn',
				},
			})
			break
		case 'turnDelete':
			DeleteReportFlowTask(btn.scope.id, btn.scope._raw.areaOrganizationUnitId)
				.then(() => {
					ElMessage.success('已删除任务')
					getReportTask(16)
				})
				.catch((err) => {
					window.errMsg(err, '删除')
				})
			break
		case 'turnExpediting':
			PushReportNotice({
				reportTaskId: btn.scope.id,
				areaOrganizationUnitId: btn.scope._raw.areaOrganizationUnitId,
				dingMessageType: 1,
			})
				.then(() => {
					ElMessage.success('已催办')
				})
				.catch((err) => {
					window.errMsg(err, '催办')
				})
			break
		case 'urging':
			isUrging.value = true
			urgingId.value = btn.scope.id
			break
		default:
			break
	}
}
const isUrging = ref(false)
const urgingList = ref(['0', '1'])
const urgingId = ref('')
const urgeingLoading = ref(false)
// 催办确定
const onUrging = () => {
	if (!urgingList.value.length) return ElMessage.error('请选择通知方式')
	urgeingLoading.value = true
	let urgingMessageTypes: any = []
	urgingList.value.forEach((item: any) => {
		if (item == '0') {
			urgingMessageTypes.push(0)
		} else {
			urgingMessageTypes.push(Number(item))
		}
	})
	if ((btnCode.value = 'revoke')) {
		let arr = urgingMessageTypes.map((item: any) => {
			return `types=${item}`
		})
		axios
			?.get(`/api/filling/plan-task/revoke-planTask/${urgingId.value}?${arr.join('&')}`)
			.then((res) => {
				ElMessage.success('已撤回该任务')
				urgeingLoading.value = false
				isUrging.value = false
				getTaskList(11)
			})
	}

	if (!btnCode.value) {
		urging(urgingId.value, {
			urgingMessageTypes: urgingMessageTypes,
			reportTaskType: currentIndex.value == 1 ? 0 : 1,
		}).then((res) => {
			console.log(res)
			ElMessage.success('催办成功')
			urgeingLoading.value = false
			isUrging.value = false
		})
	}
}
// 催办取消
const closeUrging = () => {
	urgingList.value = ['0', '1']
	btnCode.value = ''
	isUrging.value = false
	urgeingLoading.value = false
}
// 是否启用
const statusChange = (events: boolean, id: string) => {
	axios?.post(`/api/filling/plan-task/set-active?id=${id}&enabled=${events}`).then((res) => {
		getTaskList(11)
		ElMessage.success(`${events ? '已启用' : '已禁用'}`)
	})
}
const sizeChange = (val: number) => {
	pageSize.value = val
	pageParams.value.MaxResultCount = val
	pageParams.value.SkipCount = 0
	getTaskList(11)
}
const currentPage = ref(1)
const pageSize = ref(10)

// TableV2 相关变量
const tableHeight = ref(400)
const pagination = reactive({
	page: 1,
	size: 10,
	total: 0,
})
const currentChange = (val: number) => {
	currentPage.value = val
	pageParams.value.SkipCount = pageParams.value.MaxResultCount * (val - 1)
	getTaskList(11)
}

// 跳转到创建任务页面
const createTask = () => {
	router.push('/taskQualityReport/create')
}

const getPlanTaskProgress = () => {
	loading.value = true
	GetPlanTaskProgress(taskManageStore.taskList.map((item: any) => item.id))
		.then((res: any) => {
			res.data.forEach((item: any) => {
				const task = taskManageStore.taskList.find((x: any) => x.id === item.planTaskId)
				if (task) {
					// 将reportTaskStatus赋值给填报情况字段
					task.reportTaskStatus = item.reportTaskStatus
					// 保留原有的departmentReportingStatus字段
					task.departmentReportingStatus = item.reportTaskStatus
				}
			})
		})
		.catch((err: any) => {
			window.errMsg(err, '获取进度')
		})
		.finally(() => {
			loading.value = false
		})
}

// 获取计划任务列表, 我创建的
const getTaskList = async (filterMode: number = 10) => {
	const params = util.extractNonEmptyValuesFromSearchParams({
		FilterMode: filterMode,
		...searchParams.value,
	})
	loading.value = true
	axios
		?.get(
			`/api/filling/plan-task?${queryString.stringify(
				pageParams.value
			)}&${queryString.stringify(params)}`
		)
		.then((res: any) => {
			const {data} = res
			// 先进行基础数据映射
			const mappedItems = data.items.map((item: any) => ({
				...item,
				departmentReportingStatus: '',
				remindDays: surplusDate(item.fillingPeriodType, item.newToDays, item.endDate),
				// 确保endTime字段正确映射
				endTime: item.endDate ? dayjs(item.endDate).format('YYYY-MM-DD HH:mm:ss') : '-',
				// 添加报告生成时间和报告状态字段
				reportGenerateTime: item.reportGenerateTime
					? dayjs(item.reportGenerateTime).format('YYYY-MM-DD HH:mm:ss')
					: '-',
				reportStatus: item.reportStatus || null, // 只使用实际的报告状态，不使用任务状态
			}))

			// 合并localStorage中的报告数据
			taskManageStore.taskList = mergeReportData(mappedItems)

			taskManageStore.$patch({totalCount: data.totalCount})
			// 同步TableV2分页状态
			pagination.total = data.totalCount
			getPlanTaskProgress()
			loading.value = false
		})
		.catch((error: any) => {
			if (error.response?.status === 500) {
				ElNotification.error('当前操作“报表创建”人员较多，请5分钟后再试')
			}
		})
}

// 获取报表任务列表
const getReportTask = async (FilterMode: number) => {
	const params = {
		Name: searchParams.value.Name,
		FillingPeriodType: searchParams.value.FillingPeriodType,
		Status: searchParams.value.PlanTaskStatus,
	}
	loading.value = true
	await axios
		?.get(
			`/api/filling/report-task-ou?Sorting=submitTime Desc&FilterMode=${FilterMode}&${queryString.stringify(
				pageParams.value
			)}&${queryString.stringify(params)}`
		)
		.then((res: any) => {
			const {data} = res
			// 先进行基础数据映射
			const mappedItems = data.items.map((x: any) => ({
				id: x.reportTask.id,
				reportTaskName: x.reportTask?.name,
				reportTaskCreatorName: x.reportTask?.creatorName,
				creationTime: x.creationTime,
				status: x.status,
				createdBigDepartment: x.createdBigDepartment,
				createdDepartment: x.createdDepartment,
				fillerSubmitTime: x.fillerSubmitTime,
				endTime: x?.reportTask.endDate,
				currentServiceTime: x?.reportTask.currentServiceTime,
				// 添加报告生成时间和报告状态字段
				reportGenerateTime: x.reportGenerateTime
					? dayjs(x.reportGenerateTime).format('YYYY-MM-DD HH:mm:ss')
					: '-',
				reportStatus: x.reportStatus || null, // 只使用实际的报告状态
				_raw: JSON.parse(JSON.stringify(x)),
			}))

			// 合并localStorage中的报告数据
			taskManageStore.taskList = mergeReportData(mappedItems)

			taskManageStore.$patch({totalCount: data.totalCount})
			// 同步TableV2分页状态
			pagination.total = data.totalCount
			loading.value = false
		})
		.catch((error: any) => {
			if (error.response?.status === 500) {
				ElNotification.error('当前操作人员较多，请5分钟后再试')
			}
		})
}

async function checkBoxChange(val: CheckboxValueType) {
	searchParams.value.isSelf = val
	await getTaskList(11)
}

const selectedCount = ref(0)
const checked = ref(true)

const tableOffsetHeight = ref(-165)
const tableRef = ref()
const expendSearch = (height: number, expand: boolean) => {
	tableOffsetHeight.value = -(height + 165)
	tableRef.value.resize()
}
const resetSearch = () => {
	// searchParams.value = {
	// 	Name: null,
	// 	FillingPeriodType: undefined,
	// 	PlanTaskStatus: undefined,
	// 	isSelf: true,
	// }

	Object.keys(searchParams.value).forEach((key) => {
		searchParams.value[key] = null
		searchParams.value.isSelf = true
	})

	getTaskList(11)
}
const selectionList = ref([])
const selectionChange = (selection: any) => {
	selectionList.value = selection
}
const onClickTabs = (val: LTABS) => {
	if (loading.value) {
		return
	}
	taskManageStore.taskList = []
	searchParams.value = {
		Name: null,
		FillingPeriodType: undefined,
		PlanTaskStatus: undefined,
		isSelf: true,
	}

	currentPage.value = 1
	pageSize.value = 10

	if (val === LTABS.ICREATE) {
		currentIndex.value = 1
		buttons.value = tableButtons[LTABS.ICREATE]
		// 更新TableV2列配置
		columns.value = [
			{
				prop: 'name',
				label: '任务名称',
				slot: 'name',
			},
			{
				prop: 'fillingRange',
				label: '填报范围',
			},
			{
				prop: 'fillingStatus',
				label: '填报情况',
				width: 100,
				slot: 'fillingStatus',
			},
			{
				prop: 'endTime',
				label: '截止时间',
				width: 150,
				slot: 'endTime',
			},
			{
				prop: 'fillingPeriodType',
				label: '填报周期',
				width: 100,
				slot: 'fillingPeriodType',
			},
			{
				prop: 'status',
				label: '任务状态',
				width: 100,
				slot: 'status',
			},
			{
				prop: 'reportGenerateTime',
				label: '报告生成时间',
				width: 150,
				slot: 'reportGenerateTime',
			},
			{
				prop: 'reportStatus',
				label: '报告状态',
				width: 100,
				slot: 'reportStatus',
			},
		]
		// 保留原有colData用于兼容
		colData.value = [
			{
				title: '任务名称',
				field: 'name',
			},
			{
				title: '填报范围',
				field: 'fillingRange',
			},
			{
				title: '填报方式',
				field: 'fillingMode',
				width: '100',
			},
			{
				title: '创建人',
				field: 'creatorName',
				width: '80',
			},
			{
				title: '创建人科室',
				field: 'createdDepartment',
				width: '110',
			},
			{
				title: '创建时间',
				field: 'creationTime',
				width: '150',
			},
			{
				title: '截止时间',
				field: 'endTime',
				width: '150',
			},
			{
				title: '状态',
				field: 'status',
				width: '100',
			},
		]
		getTaskList(11)
	} else if (val === LTABS.ITURN) {
		currentIndex.value = 2
		buttons.value = tableButtons[LTABS.ITURN]
		// 更新TableV2列配置
		columns.value = [
			{
				prop: 'reportTaskName',
				label: '任务名称',
			},
			{
				prop: 'reportTaskCreatorName',
				label: '创建人',
				slot: 'reportTaskCreatorName',
			},
			{
				prop: 'createdBigDepartment',
				label: '创建人部门',
			},
			{
				prop: 'createdDepartment',
				label: '创建人科室',
			},
			{
				prop: 'creationTime',
				label: '创建时间',
			},
			{
				prop: 'fillerSubmitTime',
				label: '填报时间',
			},
			{
				prop: 'endTime',
				label: '截止时间',
			},
			{
				prop: 'status',
				label: '状态',
			},
		]
		// 保留原有colData用于兼容
		colData.value = [
			{
				title: '任务名称',
				field: 'reportTaskName',
			},
			{
				title: '创建人',
				field: 'reportTaskCreatorName',
			},
			{
				title: '创建人部门',
				field: 'createdBigDepartment',
			},
			{
				title: '创建人科室',
				field: 'createdDepartment',
			},
			{
				title: '创建时间',
				field: 'creationTime',
			},
			{
				title: '填报时间',
				field: 'fillerSubmitTime',
			},
			{
				title: '截止时间',
				field: 'endTime',
			},
			{
				title: '状态',
				field: 'status',
			},
		]
		getReportTask(16)
	}
	cureerntText.value = currentIndex.value == 1 ? '任务创建' : ''
	selectionList.value = []
}

const handleClickbell = () => {
	if (selectionList.value.length === 0) {
		return ElMessage.warning('请选择要催办的任务')
	}
	isBatchUrge.value = true
}
const getTimeLeft = (scoped: any) => {
	if (!scoped.rowData.endTime || !scoped.rowData.currentServiceTime) return ''
	const start: any = new Date(scoped.rowData.currentServiceTime)
	const end: any = new Date(scoped.rowData.endTime)
	const diffTime = Math.abs(end - start)
	const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

	let type = ''
	if (diffDays <= 1) {
		type = 'danger'
	} else if (diffDays <= 3) {
		type = 'warning'
	} else {
		type = 'primary'
	}

	return {
		type,
		diffDays,
	}
}

const isCopyReport = ref(false)
const isPermissionConfig = ref(false)
const isBatchUrge = ref(false)

// 获取报告状态类型
const getReportStatusType = (rowData: any) => {
	if (rowData?.reportStatus === 1) {
		return 'success'
	} else if (rowData?.reportStatus === 2) {
		return 'warning'
	} else if (rowData?.reportStatus === 3) {
		return 'danger'
	} else {
		return 'info'
	}
}

// 获取报告状态文本
const getReportStatusText = (rowData: any) => {
	if (rowData?.reportStatus === 1) {
		return '生成完成'
	} else if (rowData?.reportStatus === 2) {
		return '生成中'
	} else if (rowData?.reportStatus === 3) {
		return '已终止'
	} else {
		return '未生成'
	}
}

// TableV2 事件处理函数
const onTableV2ClickButton = ({row, btn}: any) => {
	// 检查删除按钮的条件
	if (btn.code === 'del') {
		if (!row || (row.reportStatus !== 1 && row.reportStatus !== 2)) {
			ElMessage.warning('只能删除生成中或生成完成的报告任务')
			return
		}
	}

	// 转换为原有的clickButton格式
	clickButton({
		btn: {code: btn.code},
		scope: row
	})
}

// TableV2 分页事件
const onTableV2PaginationChange = (val: any, type: any) => {
	if (type === 'page') {
		pagination.page = val
		currentPage.value = val
		pageParams.value.SkipCount = pageParams.value.MaxResultCount * (val - 1)
	} else {
		pagination.size = val
		pageSize.value = val
		pageParams.value.MaxResultCount = val
		pageParams.value.SkipCount = 0
	}
	getTaskList(11)
}

// Block高度变化事件
const onBlockHeightChanged = (height: any) => {
	tableHeight.value = height - 75
}

// 功能按钮处理函数 - 生成任务质量报告（检查状态）
const generateQualityReport = () => {
	if (selectionList.value.length === 0) {
		ElMessage.warning('请选择要生成报告的任务')
		return
	}

	// 获取已选中的数据行
	const selectedTasks = selectionList.value

	// 检查详情页localStorage中是否存在对应数据
	const qualityReportDetails = JSON.parse(localStorage.getItem('qualityReportDetails') || '[]')

	// 获取表格页localStorage数据
	const qualityReports = JSON.parse(localStorage.getItem('qualityReports') || '[]')

	selectedTasks.forEach((task: any) => {
		// 检查详情页是否有对应数据
		const detailData = qualityReportDetails.find((detail: any) => detail.taskId === task.id)

		// 查找或创建表格页对应的报告数据
		let reportData = qualityReports.find((report: any) => report.id === task.id)

		if (detailData) {
			// 详情页存在数据，状态为生成完成
			if (reportData) {
				reportData.reportStatus = 1 // 生成完成
				reportData.reportGenerateTime = detailData.updateTime || dayjs().format('YYYY-MM-DD HH:mm:ss')
			} else {
				// 创建新的报告数据
				qualityReports.push({
					id: task.id,
					reportStatus: 1, // 生成完成
					reportGenerateTime: detailData.updateTime || dayjs().format('YYYY-MM-DD HH:mm:ss')
				})
			}
		} else {
			// 详情页不存在数据，状态为生成中
			if (reportData) {
				reportData.reportStatus = 2 // 生成中
				reportData.reportGenerateTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
			} else {
				// 创建新的报告数据
				qualityReports.push({
					id: task.id,
					reportStatus: 2, // 生成中
					reportGenerateTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
				})
			}
		}
	})

	// 更新localStorage
	localStorage.setItem('qualityReports', JSON.stringify(qualityReports))

	ElMessage.success(`已为 ${selectedTasks.length} 个任务更新质量报告状态`)

	// 刷新表格数据以显示更新后的状态
	refreshTableData()
}

// 生成Excel文件的函数
const generateExcelFile = async (data: any[], fileName?: string) => {
	if (data.length === 0) {
		ElMessage.warning('没有数据可以导出')
		return
	}

	try {
		// 动态导入exceljs和file-saver库
		const [ExcelJS, FileSaver] = await Promise.all([
			import('exceljs'),
			import('file-saver')
		])

		// 创建工作簿
		const workbook = new ExcelJS.Workbook()
		const worksheet = workbook.addWorksheet('任务质量报告')

		// 定义列标题和宽度
		const columns = [
			{ header: '任务名称', key: 'taskName', width: 25 },
			{ header: '创建人', key: 'creator', width: 12 },
			{ header: '创建部门', key: 'createDepartment', width: 20 },
			{ header: '填报期间', key: 'period', width: 12 },
			{ header: '填报截止时间', key: 'deadline', width: 20 },
			{ header: '任务状态', key: 'status', width: 12 },
			{ header: '填报进度', key: 'fillingProgress', width: 20 },
			{ header: '填报问题解读', key: 'fillingProblem', width: 35 },
			{ header: '人员到位情况', key: 'personnelStatus', width: 25 },
			{ header: '响应问题解读', key: 'responseProblem', width: 35 },
			{ header: '审核情况', key: 'auditStatus', width: 12 },
			{ header: '问题解读', key: 'auditProblem', width: 35 },
			{ header: '问题及原因', key: 'issues', width: 45 },
			{ header: '改进计划', key: 'improvement', width: 45 },
			{ header: '风险预警', key: 'riskWarning', width: 45 }
		]

		worksheet.columns = columns

		// 设置表头样式
		worksheet.getRow(1).font = { bold: true }
		worksheet.getRow(1).fill = {
			type: 'pattern',
			pattern: 'solid',
			fgColor: { argb: 'FFE6F3FF' }
		}

		// 添加数据行
		data.forEach((item) => {
			worksheet.addRow({
				taskName: item['任务名称'],
				creator: item['创建人'],
				createDepartment: item['创建部门'],
				period: item['填报期间'],
				deadline: item['填报截止时间'],
				status: item['任务状态'],
				fillingProgress: item['填报进度'],
				fillingProblem: item['填报问题解读'],
				personnelStatus: item['人员到位情况'],
				responseProblem: item['响应问题解读'],
				auditStatus: item['审核情况'],
				auditProblem: item['问题解读'],
				issues: item['问题及原因'],
				improvement: item['改进计划'],
				riskWarning: item['风险预警']
			})
		})

		// 设置所有单元格的边框和对齐方式
		worksheet.eachRow((row, rowNumber) => {
			row.eachCell((cell) => {
				cell.border = {
					top: { style: 'thin' },
					left: { style: 'thin' },
					bottom: { style: 'thin' },
					right: { style: 'thin' }
				}
				cell.alignment = {
					vertical: 'middle',
					horizontal: 'left',
					wrapText: true
				}
			})
		})

		// 生成文件
		const buffer = await workbook.xlsx.writeBuffer()
		const finalFileName = fileName || `任务质量报告_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`

		// 下载文件
		const blob = new Blob([buffer], {
			type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
		})
		FileSaver.saveAs(blob, finalFileName)

		ElMessage.success('Excel文件生成成功！')
	} catch (error) {
		console.error('生成Excel文件失败:', error)
		ElMessage.error('Excel文件生成失败，请检查网络连接')
	}
}

// 生成Excel Buffer的函数（用于ZIP打包）
const generateExcelBuffer = async (data: any[]) => {
	if (data.length === 0) {
		throw new Error('没有数据可以导出')
	}

	try {
		// 动态导入exceljs库
		const ExcelJS = (await import('exceljs')).default

		// 创建工作簿
		const workbook = new ExcelJS.Workbook()
		const worksheet = workbook.addWorksheet('任务质量报告')

		// 定义列标题和宽度
		const columns = [
			{ header: '任务名称', key: 'taskName', width: 25 },
			{ header: '创建人', key: 'creator', width: 12 },
			{ header: '创建部门', key: 'createDepartment', width: 20 },
			{ header: '填报期间', key: 'period', width: 12 },
			{ header: '填报截止时间', key: 'deadline', width: 20 },
			{ header: '任务状态', key: 'status', width: 12 },
			{ header: '填报进度', key: 'fillingProgress', width: 20 },
			{ header: '填报问题解读', key: 'fillingProblem', width: 35 },
			{ header: '人员到位情况', key: 'personnelStatus', width: 25 },
			{ header: '响应问题解读', key: 'responseProblem', width: 35 },
			{ header: '审核情况', key: 'auditStatus', width: 12 },
			{ header: '问题解读', key: 'auditProblem', width: 35 },
			{ header: '问题及原因', key: 'issues', width: 45 },
			{ header: '改进计划', key: 'improvement', width: 45 },
			{ header: '风险预警', key: 'riskWarning', width: 45 }
		]

		worksheet.columns = columns

		// 设置表头样式
		worksheet.getRow(1).font = { bold: true }
		worksheet.getRow(1).fill = {
			type: 'pattern',
			pattern: 'solid',
			fgColor: { argb: 'FFE6F3FF' }
		}

		// 添加数据行
		data.forEach((item) => {
			worksheet.addRow({
				taskName: item['任务名称'],
				creator: item['创建人'],
				createDepartment: item['创建部门'],
				period: item['填报期间'],
				deadline: item['填报截止时间'],
				status: item['任务状态'],
				fillingProgress: item['填报进度'],
				fillingProblem: item['填报问题解读'],
				personnelStatus: item['人员到位情况'],
				responseProblem: item['响应问题解读'],
				auditStatus: item['审核情况'],
				auditProblem: item['问题解读'],
				issues: item['问题及原因'],
				improvement: item['改进计划'],
				riskWarning: item['风险预警']
			})
		})

		// 设置所有单元格的边框和对齐方式
		worksheet.eachRow((row, rowNumber) => {
			row.eachCell((cell) => {
				cell.border = {
					top: { style: 'thin' },
					left: { style: 'thin' },
					bottom: { style: 'thin' },
					right: { style: 'thin' }
				}
				cell.alignment = {
					vertical: 'middle',
					horizontal: 'left',
					wrapText: true
				}
			})
		})

		// 返回buffer
		return await workbook.xlsx.writeBuffer()
	} catch (error) {
		console.error('生成Excel Buffer失败:', error)
		throw error
	}
}

// 刷新表格数据
const refreshTableData = () => {
	if (currentIndex.value === 1) {
		getTaskList(11)
	} else {
		getReportTask(16)
	}
}

// 合并localStorage中的报告数据
const mergeReportData = (items: any[]) => {
	const qualityReports = JSON.parse(localStorage.getItem('qualityReports') || '[]')

	return items.map((item: any) => {
		// 查找localStorage中对应id的报告数据
		const reportData = qualityReports.find((report: any) => report.id === item.id)

		if (reportData) {
			// 如果找到了，合并数据，优先使用localStorage中的数据
			return {
				...item,
				reportStatus: reportData.reportStatus,
				reportGenerateTime: reportData.reportGenerateTime,
			}
		}

		// 如果没找到，保持原数据
		return item
	})
}

const exportQualityReport = async () => {
	if (selectionList.value.length === 0) {
		ElMessage.warning('请选择要导出的任务')
		return
	}

	// 获取已选中的数据行
	const selectedTasks = selectionList.value

	if (selectedTasks.length === 1) {
		// 单个任务，直接生成Excel文件
		ElMessage.success(`正在为任务生成Excel质量报告...`)
		await generateSingleTaskExcel(selectedTasks[0])
	} else {
		// 多个任务，生成ZIP文件
		ElMessage.success(`正在为 ${selectedTasks.length} 个任务生成ZIP质量报告...`)
		await generateMultipleTasksZip(selectedTasks)
	}
}

// 生成单个任务的Excel文件
const generateSingleTaskExcel = async (task: any) => {
	// 从localStorage获取详情数据
	const qualityReportDetails = JSON.parse(localStorage.getItem('qualityReportDetails') || '[]')
	const detailData = qualityReportDetails.find((detail: any) => detail.taskId === task.id)

	// 准备Excel数据
	const excelData = buildTaskExcelData(task, detailData)

	// 生成文件名：任务名称-质量报告-时间戳（报告生成时间）
	const reportTime = detailData?.updateTime || dayjs().format('YYYY-MM-DD_HH-mm-ss')
	const fileName = `${task.name || '未命名任务'}-质量报告-${reportTime}.xlsx`

	// 生成Excel文件
	await generateExcelFile([excelData], fileName)
}

// 生成多个任务的文件（尝试ZIP，失败则逐个下载）
const generateMultipleTasksZip = async (tasks: any[]) => {
	try {
		// 尝试动态导入JSZip
		const JSZip = (await import('jszip')).default
		const zip = new JSZip()

		// 从localStorage获取详情数据
		const qualityReportDetails = JSON.parse(localStorage.getItem('qualityReportDetails') || '[]')

		// 为每个任务生成Excel数据
		for (const task of tasks) {
			const detailData = qualityReportDetails.find((detail: any) => detail.taskId === task.id)
			const excelData = buildTaskExcelData(task, detailData)

			// 生成Excel buffer
			const buffer = await generateExcelBuffer([excelData])

			// 生成文件名：任务名称-质量报告-时间戳（报告生成时间）
			const reportTime = detailData?.updateTime || dayjs().format('YYYY-MM-DD_HH-mm-ss')
			const fileName = `${task.name || '未命名任务'}-质量报告-${reportTime}.xlsx`

			// 添加到ZIP
			zip.file(fileName, buffer)
		}

		// 生成ZIP文件
		const zipBlob = await zip.generateAsync({ type: 'blob' })

		// 下载ZIP文件：时间戳（导出时间）-质量报告
		const exportTime = dayjs().format('YYYY-MM-DD_HH-mm-ss')
		const zipFileName = `${exportTime}-质量报告.zip`

		const link = document.createElement('a')
		const url = URL.createObjectURL(zipBlob)
		link.setAttribute('href', url)
		link.setAttribute('download', zipFileName)
		link.style.visibility = 'hidden'
		document.body.appendChild(link)
		link.click()
		document.body.removeChild(link)
		URL.revokeObjectURL(url)

		ElMessage.success('ZIP质量报告生成成功！')
	} catch (error) {
		console.error('JSZip不可用，改为逐个下载:', error)
		ElMessage.warning('ZIP功能不可用，将逐个下载Excel文件')

		// 降级方案：逐个下载Excel文件
		await generateMultipleTasksIndividually(tasks)
	}
}

// 逐个下载多个任务的Excel文件
const generateMultipleTasksIndividually = async (tasks: any[]) => {
	try {
		// 从localStorage获取详情数据
		const qualityReportDetails = JSON.parse(localStorage.getItem('qualityReportDetails') || '[]')

		for (let i = 0; i < tasks.length; i++) {
			const task = tasks[i]
			const detailData = qualityReportDetails.find((detail: any) => detail.taskId === task.id)
			const excelData = buildTaskExcelData(task, detailData)

			// 生成文件名：任务名称-质量报告-时间戳（报告生成时间）
			const reportTime = detailData?.updateTime || dayjs().format('YYYY-MM-DD_HH-mm-ss')
			const fileName = `${task.name || '未命名任务'}-质量报告-${reportTime}.xlsx`

			// 生成并下载Excel文件
			await generateExcelFile([excelData], fileName)

			// 添加延迟避免浏览器阻止多个下载
			if (i < tasks.length - 1) {
				await new Promise(resolve => setTimeout(resolve, 500))
			}
		}

		ElMessage.success(`已成功生成 ${tasks.length} 个Excel质量报告文件！`)
	} catch (error) {
		console.error('逐个下载失败:', error)
		ElMessage.error('文件生成失败，请重试')
	}
}

// 构建任务Excel数据
const buildTaskExcelData = (task: any, detailData: any) => {
	if (detailData) {
		// 有详情数据，使用完整数据
		return {
			'任务名称': detailData.taskInfo?.name || task.name || '',
			'创建人': detailData.taskInfo?.creator || '',
			'创建部门': detailData.taskInfo?.createDepartment || '',
			'填报期间': detailData.taskInfo?.period || '',
			'填报截止时间': detailData.taskInfo?.deadline || '',
			'任务状态': detailData.taskInfo?.status || '',
			'填报进度': detailData.progressData?.[0]?.actual || '',
			'填报问题解读': detailData.progressData?.[0]?.expected || '',
			'人员到位情况': detailData.progressData?.[1]?.actual || '',
			'响应问题解读': detailData.progressData?.[1]?.expected || '',
			'审核情况': detailData.progressData?.[2]?.actual || '',
			'问题解读': detailData.progressData?.[2]?.expected || '',
			'问题及原因': detailData.issues || '',
			'改进计划': detailData.improvement || '',
			'风险预警': detailData.riskWarning || ''
		}
	} else {
		// 没有详情数据，使用基础任务数据
		return {
			'任务名称': task.name || '',
			'创建人': '',
			'创建部门': '',
			'填报期间': '',
			'填报截止时间': task.endTime || '',
			'任务状态': task.status === 14 ? '待执行' : (task.status === 3 ? '进行中' : (task.status === 13 ? '待审核' : (task.status === 5 ? '已完成' : (task.status === 8 ? '已终止' : '')))),
			'填报进度': task.reportTaskStatus || '',
			'填报问题解读': '',
			'人员到位情况': '',
			'响应问题解读': '',
			'审核情况': '',
			'问题解读': '',
			'问题及原因': '',
			'改进计划': '',
			'风险预警': ''
		}
	}
}

const batchDelete = () => {
	if (selectionList.value.length === 0) {
		ElMessage.warning('请选择要删除的任务')
		return
	}

	ElMessageBox.confirm(
		'你正在删除质量任务，是否确定删除质量任务。',
		'删除确认',
		{
			confirmButtonText: '确认删除',
			cancelButtonText: '取消',
			type: 'warning',
		}
	).then(() => {
		// 获取选中任务的ID列表
		const selectedIds = selectionList.value.map((task: any) => task.id)

		// 获取现有的localStorage数据
		const existingReports = JSON.parse(localStorage.getItem('qualityReports') || '[]')

		// 过滤掉选中任务对应的报告数据
		const filteredReports = existingReports.filter((report: any) =>
			!selectedIds.includes(report.id)
		)

		// 更新localStorage
		localStorage.setItem('qualityReports', JSON.stringify(filteredReports))

		ElMessage.success(`成功删除 ${selectionList.value.length} 个任务的质量报告`)

		// 清空选择
		selectionList.value = []

		// 刷新列表以显示更新后的状态
		refreshTableData()
	}).catch(() => {
		ElMessage.info('已取消删除')
	})
}

const batchAddWatermark = () => {
	if (selectionList.value.length === 0) {
		ElMessage.warning('请选择要添加水印的任务')
		return
	}

	// 检查所有选中的任务是否都是报告生成完成状态
	const invalidTasks = selectionList.value.filter((task: any) => task.reportStatus !== 1)
	if (invalidTasks.length > 0) {
		ElMessage.warning('只能为报告生成完成的任务添加水印')
		return
	}

	const selectedTasks = selectionList.value
	ElMessage.success(`正在为 ${selectedTasks.length} 个任务添加水印...`)

	// 模拟添加水印过程
	const randomDelay = Math.floor(Math.random() * 2000) + 2000 // 2-4秒

	setTimeout(() => {
		ElMessage.success(`成功为 ${selectedTasks.length} 个任务添加水印！`)
		// 清空选择
		selectionList.value = []
	}, randomDelay)
}

onActivated(async () => {
	initData()
})

onMounted(() => {
	initData()
})
</script>
<template>
	<div class="statement">
		<Block title="任务质量报告" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
			<template #header>
				<div class="df jcsb">
					<div class="df aic">
						<span class="title-tag df aic jcc">任务管理</span>
					</div>
				</div>
			</template>
			<!-- 顶部按钮已移除 -->
			<template #expand>
				<div
					class="search-box"
					v-action:enter="
						() => (currentIndex === 1 ? getTaskList(11) : getReportTask(16))
					"
				>
					<el-input
						size="default"
						v-model="searchParams.Name"
						placeholder="请输入任务名称"
						class="value"
					/>

					<!-- <el-select
						clearable
						v-model="searchParams.FillingPeriodType"
						placeholder="请选择填报周期"
						size="default"
						class="value"
					>
						<el-option
							v-for="item in periodList"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select> -->

					<el-select
						v-if="currentIndex === 1"
						clearable
						v-model="searchParams.PlanTaskStatus"
						placeholder="请选择任务状态"
						size="default"
						@change="() => (currentIndex === 1 ? getTaskList(11) : getReportTask(16))"
						class="value"
					>
						<el-option
							v-for="item in ReportsFlowStatus.filter(
								(x) => x.type === 'plan' && x.enable
							)"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
					<el-select
						v-if="currentIndex === 2"
						clearable
						v-model="searchParams.PlanTaskStatus"
						placeholder="请选择任务状态"
						size="default"
						@change="() => (currentIndex === 1 ? getTaskList(11) : getReportTask(16))"
						class="value"
					>
						<el-option
							v-for="item in reportStatusTrans.filter((x: any) => x.enable)"
							:key="item.value"
							:label="item.name"
							:value="item.value"
						/>
					</el-select>

					<el-button size="default" type="default" @click="resetSearch">
						<i class="i-ic-outline-cleaning-services"></i>
						清空
					</el-button>
					<el-button
						:loading="loading"
						size="default"
						type="primary"
						@click="currentIndex === 1 ? getTaskList(11) : getReportTask(16)"
					>查询</el-button>
				</div>
			</template>

			<!-- 功能按钮区域 -->
			<div class="function-buttons" style="margin: 16px 0; display: flex; gap: 12px;">
				<el-button
					type="primary"
					@click="generateQualityReport"
					:loading="loading"
					:disabled="!canGenerateQualityReport"
				>
					<i class="i-majesticons-file-plus-line"></i>
					生成任务质量报告
				</el-button>
				<el-button
					type="success"
					@click="exportQualityReport"
					:loading="loading"
					:disabled="!canExportQualityReport"
				>
					<i class="i-majesticons-inbox-out-line"></i>
					导出质量报告
				</el-button>
				<el-button
					type="danger"
					@click="batchDelete"
					:disabled="!canBatchDelete"
				>
					<i class="i-majesticons-trash-line"></i>
					批量删除
				</el-button>
				<el-button
					type="warning"
					@click="batchAddWatermark"
					:disabled="!canBatchAddWatermark"
				>
					<i class="i-majesticons-image-line"></i>
					批量添加水印
				</el-button>
			</div>

			<TableV2
				v-if="columns && columns.length > 0"
				ref="tableRef"
				:key="`table-${currentIndex}`"
				:columns="columns"
				:defaultTableData="tableData"
				:enable-toolbar="false"
				:enable-own-button="false"
				:enable-selection="true"
				:enable-index="true"
				:height="tableHeight"
				:buttons="tableV2Buttons"
				:loading="loading"
				@click-button="onTableV2ClickButton"
				@selection-change="selectionChange"
			>
				<template #name="{ row }">
					<span
						class="link-click"
						@click="clickButton({btn: {code: 'detail'}, scope: row})"
						>{{ row.name }}</span
					>
				</template>
				<template #departments="{ row }">
					<!-- {{ row?.departments }} -->
					{{
						row && row?.fillingMode === 2
							? row?.staffs !== null
								? row?.staffs.map((v: any) => v.name).toString()
								: '-'
							: row?.departments !== null
							? row?.departments
									.map((v: any) => v?.parent?.name + '-' + v.name)
									.toString()
							: '-'
					}}
				</template>
				<template #areaOrganizationUnit="{ row }">
					{{ row.areaOrganizationUnit }}
				</template>




				<!-- <template #enabled="scope">
				<el-switch
					v-model="scope.rowData.enabled"
					:disabled="scope.rowData.enabled"
					size="small"
					@change="statusChange($event, scope.rowData.id)"
				/>
			</template> -->
				<template #reportTaskStatus="{ row }">
					<span
						class="el-button el-button--small is-round"
						:class="reportStatusTrans[row.reportTaskStatus]?.class"
					>
						{{ reportStatusTrans[row?.reportTaskStatus]?.name }}
					</span>
				</template>
				<template #reportTaskCreatorName="{ row }">
					{{ row._raw.reportTask?.creator?.name || '-' }}
				</template>
				<template #departmentReportingStatus="{ row }">
					<LoadingTransition v-if="loading"></LoadingTransition>
					<template v-else>
						{{ row.departmentReportingStatus || '-' }}
					</template>
				</template>
				<template #fillingStatus="{ row }">
					<div class="df aic">
						<span v-if="row.reportTaskStatus">
							{{ row.reportTaskStatus }}
						</span>
						<span v-else>0/0</span>
					</div>
				</template>
				<template #endTime="{ row }">
					<div class="df aic">
						{{ row.endTime || '-' }}
						<el-tag
							v-if="row.remindDays && row.remindDays > 0"
							:type="row.remindDays <= 3 ? 'danger' : (row.remindDays <= 7 ? 'warning' : 'success')"
							class="mg-left-10"
						>
							剩余{{ row.remindDays }}天
						</el-tag>
						<el-tag v-else-if="row.endTime" type="danger" class="mg-left-10">
							已截止
						</el-tag>
					</div>
				</template>
				<template #fillingPeriodType="{ row }">
					{{ periodList.find(item => item.value === row.fillingPeriodType)?.label || '-' }}
				</template>
				<template #status="{ row }">
					<el-tag
						:type="
							row.status === 14 ? 'warning' :
							(row.status === 3 ? 'primary' :
							(row.status === 13 ? 'info' :
							(row.status === 5 ? 'success' :
							(row.status === 8 ? 'danger' : 'info'))))
						"
					>
						{{
							row.status === 14 ? '待执行' :
							(row.status === 3 ? '进行中' :
							(row.status === 13 ? '待审核' :
							(row.status === 5 ? '已完成' :
							(row.status === 8 ? '已终止' : '-'))))
						}}
					</el-tag>
				</template>
				<template #reportGenerateTime="{ row }">
					{{ row?.reportGenerateTime || '-' }}
				</template>
				<template #reportStatus="{ row }">
					<el-tag
						:type="getReportStatusType(row)"
					>
						{{ getReportStatusText(row) }}
					</el-tag>
				</template>
			</TableV2>
			<!-- 分页 -->
			<Pagination
				:total="pagination.total"
				:current-page="pagination.page"
				:page-size="pagination.size"
				@current-change="onTableV2PaginationChange($event, 'page')"
				@size-change="onTableV2PaginationChange($event, 'size')"
			></Pagination>
			<Dialog
				v-model="isUrging"
				:title="btnCode ? '报表撤回' : '报表催办'"
				width="600"
				@clickConfirm="onUrging"
				@close="closeUrging"
				:loading="urgeingLoading"
			>
				<div>
					<p v-show="!btnCode">将批量通知各未完成填报部门尽快完成填报</p>
					<el-checkbox-group v-model="urgingList" style="margin-top: 10px">
						<el-checkbox label="发送系统内消息" value="0" />
						<el-checkbox label="发送渝快政工作通知" value="1" />
						<el-checkbox label="发送渝快政Ding消息" value="2" />
					</el-checkbox-group>
				</div>
			</Dialog>
		</Block>

		<CopyReport.default v-model="isCopyReport" title="报表复制" />
		<PermissionConfig.default v-model="isPermissionConfig" title="权限配置" />
		<BatchUrge.default
			v-model="isBatchUrge"
			title="批量催办"
			@close="
				() => {
					tableRef.clearSelection()
				}
			"
		/>
	</div>
</template>
<route>
	{
		meta: {
			title: '临时报表创建',
		},
	}
</route>
<style scoped lang="scss">
:deep(.el-checkbox) {
	margin-right: 10px !important;
}
.active {
	border-bottom: 3px solid #2185c5;
	font-weight: bold;
	color: #000;
}

.statement {
	.title-tag {
		width: 110px;
		height: 26px;
		background: #e3ecfa;
		font-size: 14px;
		border-radius: 4px;
		font-family: Microsoft YaHei-Bold, Microsoft YaHei, sans-serif;
		font-weight: bold;
		color: #3d7fff;
	}
}
.link {
	width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: no-wrap;
	color: var(--z-main);
}
.search-box {
	display: flex;
	padding: 10px 15px;
	.value {
		margin-right: 10px;
		width: 250px;
	}
}
</style>
