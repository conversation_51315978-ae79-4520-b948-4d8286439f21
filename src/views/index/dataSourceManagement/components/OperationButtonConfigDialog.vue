<!-- 操作按钮隐藏配置弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="操作按钮隐藏配置"
    width="400px"
    :destroy-on-close="true"
    @click-confirm="onConfirm"
    @click-cancel="handleClose"
  >
    <div class="operation-button-config-content" style="padding: 20px; min-height: 200px;">
      <!-- 按钮配置选项 -->
      <div class="button-config-section">
        <div class="checkbox-group" style="display: flex; flex-wrap: wrap; gap: 20px;">
          <el-checkbox v-model="config.showQueryButton">查询按钮</el-checkbox>
          <el-checkbox v-model="config.showResetButton">重置按钮</el-checkbox>
          <el-checkbox v-model="config.showDetailButton">详情按钮</el-checkbox>
          <el-checkbox v-model="config.showEditButton">编辑按钮</el-checkbox>
          <el-checkbox v-model="config.showDeleteButton">删除按钮</el-checkbox>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 配置数据
const config = ref({
  showQueryButton: true,
  showResetButton: true,
  showDetailButton: true,
  showEditButton: true,
  showDeleteButton: true
})

// 缓存键
const STORAGE_KEY = 'operationButtonConfig'

// 加载配置数据
const loadConfigFromCache = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      const savedConfig = JSON.parse(cached)
      config.value = {
        showQueryButton: savedConfig.showQueryButton !== undefined ? savedConfig.showQueryButton : true,
        showResetButton: savedConfig.showResetButton !== undefined ? savedConfig.showResetButton : true,
        showDetailButton: savedConfig.showDetailButton !== undefined ? savedConfig.showDetailButton : true,
        showEditButton: savedConfig.showEditButton !== undefined ? savedConfig.showEditButton : true,
        showDeleteButton: savedConfig.showDeleteButton !== undefined ? savedConfig.showDeleteButton : true
      }
    } else {
      resetConfig()
    }
  } catch (error) {
    console.error('加载操作按钮配置失败:', error)
    resetConfig()
  }
}

// 保存配置数据
const saveConfigToCache = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(config.value))
  } catch (error) {
    console.error('保存操作按钮配置失败:', error)
  }
}

// 重置配置
const resetConfig = () => {
  config.value = {
    showQueryButton: true,
    showResetButton: true,
    showDetailButton: true,
    showEditButton: true,
    showDeleteButton: true
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 确认保存
const onConfirm = () => {
  saveConfigToCache()
  ElMessage.success('操作按钮配置保存成功')
  visible.value = false
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadConfigFromCache()
  }
})
</script>

<style scoped>
.operation-button-config-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.button-config-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.checkbox-group .el-checkbox {
  margin-right: 0;
}
</style>
