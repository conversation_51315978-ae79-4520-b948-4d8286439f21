<template>
  <Dialog
    v-model="visible"
    title="数据源健康监控"
    width="1200px"
    :destroy-on-close="true"
    :visible-confirm-button="false"
    cancel-text="关闭"
    @click-cancel="handleClose"
  >
    <div class="health-monitor" style="padding: 20px; min-height: 600px;">
      <!-- 监控卡片 -->
      <div class="monitor-cards">
        <!-- 数据源安全性评估 -->
        <div class="monitor-card">
          <div class="card-header">
            <h4>数据源安全性评估</h4>
          </div>
          <div class="card-content">
            <!-- 安全等级 -->
            <div class="security-grade">
              <div class="grade-label">数据源安全等级：</div>
              <div class="grade-value">A+</div>
            </div>

            <!-- 安全指标 -->
            <div class="security-indicators">
              <div class="indicator-item">
                <el-icon class="indicator-icon success"><Check /></el-icon>
                <span class="indicator-text">访问控制：合规</span>
              </div>
              <div class="indicator-item">
                <el-icon class="indicator-icon success"><Check /></el-icon>
                <span class="indicator-text">数据加密：已启用</span>
              </div>
            </div>

            <!-- 安全统计 -->
            <div class="security-stats">
              <div class="stat-item warning">
                <el-icon><Warning /></el-icon>
                <span>漏洞数量：2 (个)</span>
              </div>
              <div class="stat-item error">
                <el-icon><Close /></el-icon>
                <span>异常数量：0</span>
              </div>
            </div>

            <!-- 安全饼图 -->
            <div class="security-chart">
              <div ref="securityPieChart" style="width: 100%; height: 200px;"></div>
            </div>
          </div>
        </div>

        <!-- 数据源安全性监控 -->
        <div class="monitor-card">
          <div class="card-header">
            <h4>数据源安全性监控</h4>
          </div>
          <div class="card-content">
            <!-- 安全监控项 -->
            <div class="security-monitor-items">
              <div class="monitor-item">
                <el-icon class="monitor-icon"><User /></el-icon>
                <div class="monitor-info">
                  <div class="monitor-name">异常登录验证</div>
                  <div class="monitor-count">5</div>
                </div>
              </div>
              <div class="monitor-item">
                <el-icon class="monitor-icon"><User /></el-icon>
                <div class="monitor-info">
                  <div class="monitor-name">敏感数据访问</div>
                  <div class="monitor-count">8</div>
                </div>
              </div>
              <div class="monitor-item">
                <el-icon class="monitor-icon"><User /></el-icon>
                <div class="monitor-info">
                  <div class="monitor-name">最新安全告警</div>
                  <div class="monitor-count">12</div>
                </div>
              </div>
            </div>

            <!-- 安全监控柱状图 -->
            <div class="security-bar-chart">
              <div ref="securityBarChart" style="width: 100%; height: 200px;"></div>
            </div>
          </div>
        </div>

        <!-- 数据源性能监控 -->
        <div class="monitor-card">
          <div class="card-header">
            <h4>数据源性能监控</h4>
          </div>
          <div class="card-content">
            <!-- 性能指标 -->
            <div class="performance-metrics">
              <div class="metric-item">
                <div class="metric-label">平均响应时间</div>
                <div class="metric-value">50 ms</div>
              </div>
              <div class="metric-item">
                <div class="metric-label">CPU 使用率</div>
                <div class="metric-value">35%</div>
              </div>
              <div class="metric-item">
                <div class="metric-label">内存使用率</div>
                <div class="metric-value">60%</div>
              </div>
            </div>

            <!-- 性能详细信息 -->
            <div class="performance-details">
              <div class="detail-row">
                <span class="detail-label">读IOPS</span>
                <span class="detail-value">3200</span>
                <span class="detail-label">连接数</span>
                <span class="detail-value">657</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">写IOPS</span>
                <span class="detail-value">800</span>
                <span class="detail-label">网络延时</span>
                <span class="detail-value">6</span>
              </div>
            </div>

            <!-- 性能趋势图 -->
            <div class="performance-trend-chart">
              <div ref="performanceTrendChart" style="width: 100%; height: 200px;"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 性能趋势图 -->
      <div class="trend-chart-container">
        <div class="chart-card">
          <div class="card-header">
            <h4>性能趋势</h4>
          </div>
          <div class="card-content">
            <div ref="trendChart" style="width: 100%; height: 300px;"></div>
          </div>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import { Check, Warning, Close, User } from '@element-plus/icons-vue'
import * as echarts from 'echarts'


// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 图表引用
const securityPieChart = ref()
const securityBarChart = ref()
const performanceTrendChart = ref()
const trendChart = ref()

// 安全性数据
const securityData = ref({
  grade: 'A+',
  accessControl: '合规',
  dataEncryption: '已启用',
  vulnerabilities: 2,
  anomalies: 0
})

// 安全监控数据
const securityMonitorData = ref([
  { name: '异常登录验证', count: 5 },
  { name: '敏感数据访问', count: 8 },
  { name: '最新安全告警', count: 12 }
])

// 性能指标
const performanceMetrics = ref({
  avgResponseTime: 50,
  cpuUsage: 35,
  memoryUsage: 60,
  readIOPS: 3200,
  writeIOPS: 800,
  connections: 657,
  networkLatency: 6
})

// Mock数据
const trendData = [
  { time: '00:00', cpu: 45, memory: 60, connections: 120 },
  { time: '04:00', cpu: 42, memory: 58, connections: 115 },
  { time: '08:00', cpu: 65, memory: 72, connections: 180 },
  { time: '12:00', cpu: 70, memory: 75, connections: 200 },
  { time: '16:00', cpu: 68, memory: 73, connections: 195 },
  { time: '20:00', cpu: 55, memory: 65, connections: 150 }
]

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '正常':
      return 'success'
    case '警告':
      return 'warning'
    case '异常':
      return 'danger'
    default:
      return 'info'
  }
}

// 初始化安全性饼图
const initSecurityPieChart = () => {
  const chart = echarts.init(securityPieChart.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}'
    },
    series: [{
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '50%'],
      data: [
        { name: '安全', value: 90, itemStyle: { color: '#67c23a' } },
        { name: '合规', value: 80, itemStyle: { color: '#409eff' } },
        { name: '异常', value: 0, itemStyle: { color: '#f56c6c' } }
      ]
    }]
  }
  chart.setOption(option)
}

// 初始化安全监控柱状图
const initSecurityBarChart = () => {
  const chart = echarts.init(securityBarChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['异常登录验证', '敏感数据访问', '最新安全告警']
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: [
        { value: 5, itemStyle: { color: '#5470c6' } },
        { value: 8, itemStyle: { color: '#91cc75' } },
        { value: 12, itemStyle: { color: '#fac858' } }
      ],
      type: 'bar'
    }]
  }
  chart.setOption(option)
}

// 初始化性能趋势图
const initPerformanceTrendChart = () => {
  const chart = echarts.init(performanceTrendChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['CPU使用率', '内存使用率']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
    },
    yAxis: {
      type: 'value',
      max: 100
    },
    series: [
      {
        name: 'CPU使用率',
        type: 'line',
        data: [40, 35, 45, 50, 48, 42],
        smooth: true,
        lineStyle: { color: '#5470c6' },
        areaStyle: { color: 'rgba(84, 112, 198, 0.3)' }
      },
      {
        name: '内存使用率',
        type: 'line',
        data: [60, 58, 65, 70, 68, 62],
        smooth: true,
        lineStyle: { color: '#91cc75' },
        areaStyle: { color: 'rgba(145, 204, 117, 0.3)' }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化趋势图
const initTrendChart = () => {
  const chart = echarts.init(trendChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['CPU使用率', '内存使用率', '连接数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: trendData.map(item => item.time)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: 'CPU使用率',
        type: 'line',
        data: trendData.map(item => item.cpu),
        smooth: true,
        lineStyle: { color: '#5470c6' }
      },
      {
        name: '内存使用率',
        type: 'line',
        data: trendData.map(item => item.memory),
        smooth: true,
        lineStyle: { color: '#91cc75' }
      },
      {
        name: '连接数',
        type: 'line',
        data: trendData.map(item => item.connections),
        smooth: true,
        lineStyle: { color: '#fac858' }
      }
    ]
  }
  chart.setOption(option)
}

// 监听弹窗显示状态
const handleDialogOpen = () => {
  nextTick(() => {
    initSecurityPieChart()
    initSecurityBarChart()
    initPerformanceTrendChart()
    initTrendChart()
  })
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 监听visible变化
watch(() => visible.value, (newVal) => {
  if (newVal) {
    handleDialogOpen()
  }
})
</script>

<style scoped lang="scss">
.health-monitor {
  .monitor-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 30px;

    .monitor-card {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .card-header {
        background: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #ebeef5;

        h4 {
          margin: 0;
          font-size: 14px;
          color: #303133;
          font-weight: 600;
        }
      }

      .card-content {
        padding: 20px;
      }
    }
  }

  // 安全性评估样式
  .security-grade {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .grade-label {
      font-size: 14px;
      color: #303133;
      margin-right: 10px;
    }

    .grade-value {
      background: #67c23a;
      color: white;
      padding: 4px 12px;
      border-radius: 4px;
      font-weight: bold;
      font-size: 14px;
    }
  }

  .security-indicators {
    margin-bottom: 15px;

    .indicator-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .indicator-icon {
        margin-right: 8px;

        &.success {
          color: #67c23a;
        }
      }

      .indicator-text {
        font-size: 14px;
        color: #303133;
      }
    }
  }

  .security-stats {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;

    .stat-item {
      display: flex;
      align-items: center;
      font-size: 12px;

      .el-icon {
        margin-right: 5px;
      }

      &.warning {
        color: #e6a23c;
      }

      &.error {
        color: #f56c6c;
      }
    }
  }

  // 安全监控样式
  .security-monitor-items {
    margin-bottom: 20px;

    .monitor-item {
      display: flex;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .monitor-icon {
        color: #409eff;
        font-size: 20px;
        margin-right: 15px;
      }

      .monitor-info {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .monitor-name {
          font-size: 14px;
          color: #303133;
        }

        .monitor-count {
          font-size: 16px;
          font-weight: bold;
          color: #409eff;
        }
      }
    }
  }

  .performance-metrics {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;

    .metric-item {
      text-align: center;

      .metric-label {
        font-size: 12px;
        color: #909399;
        margin-bottom: 5px;
      }

      .metric-value {
        font-size: 18px;
        font-weight: bold;
        color: #303133;
      }
    }
  }

  .performance-details {
    margin-bottom: 20px;

    .detail-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .detail-label {
        font-size: 12px;
        color: #909399;
        flex: 1;
      }

      .detail-value {
        font-size: 14px;
        font-weight: bold;
        color: #303133;
        flex: 1;
        text-align: center;
      }
    }
  }

  .trend-chart-container {
    .chart-card {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      overflow: hidden;

      .card-header {
        background: #f8f9fa;
        padding: 15px 20px;
        border-bottom: 1px solid #ebeef5;

        h4 {
          margin: 0;
          font-size: 16px;
          color: #303133;
          font-weight: 600;
        }
      }

      .card-content {
        padding: 20px;
      }
    }
  }
}
</style>
