<!-- 格式文件配置弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="数据源格式文件配置"
    width="700px"
    :destroy-on-close="true"
    :loading="loading"
    loading-text="保存中"
    @closed="resetForm"
    @click-confirm="onConfirm"
  >
    <div class="format-file-config-content" style="min-height: 600px; padding: 20px;">
      <!-- 数据源CSV格式文件配置 -->
      <div class="config-section">
        <h4 class="section-title">数据源CSV格式文件配置</h4>
        <Form
          ref="csvFormRef"
          v-model="csvForm"
          :props="[
            { label: '分隔符', prop: 'separator', type: 'text', required: true, placeholder: '请输入' },
            { label: '编码', prop: 'charset', type: 'text', required: true, placeholder: '请输入' }
          ]"
          :rules="csvFormRules"
          :enable-button="false"
        />

        <!-- 文件路径选择 -->
        <div style="margin-top: 20px;">
          <label style="display: block; margin-bottom: 10px;">
            <span style="color: red;">*</span> 文件路径:
          </label>
          <el-input
            v-model="csvForm.filePath"
            placeholder="请选择文件：csv"
            readonly
            style="margin-bottom: 10px;"
          >
            <template #append>
              <el-button @click="selectFile('csv')">选择文件</el-button>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 数据源Excel格式文件配置 -->
      <div class="config-section">
        <h4 class="section-title">数据源Excel格式文件配置</h4>
        <Form
          ref="excelFormRef"
          v-model="excelForm"
          :props="[
            { label: '工作表名称/索引', prop: 'sheetName', type: 'text', required: true, placeholder: '请输入工作表名称或索引' },
            { label: '数据起始行/列', prop: 'startPosition', type: 'text', required: true, placeholder: '从第几行、第几列开始，中间用逗号分开（例如1，A）' }
          ]"
          :rules="excelFormRules"
          :enable-button="false"
        />

        <!-- 文件路径选择 -->
        <div style="margin-top: 20px;">
          <label style="display: block; margin-bottom: 10px;">
            <span style="color: red;">*</span> 文件路径:
          </label>
          <el-input
            v-model="excelForm.filePath"
            placeholder="请选择文件"
            readonly
            style="margin-bottom: 10px;"
          >
            <template #append>
              <el-button @click="selectFile('excel')">选择文件</el-button>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 数据源JSON格式文件配置 -->
      <div class="config-section">
        <h4 class="section-title">数据源JSON格式文件配置</h4>
        <Form
          ref="jsonFormRef"
          v-model="jsonForm"
          :props="[
            { label: '编码格式', prop: 'format', type: 'text', required: true, placeholder: '请输入' }
          ]"
          :rules="jsonFormRules"
          :enable-button="false"
        />

        <!-- 文件路径选择 -->
        <div style="margin-top: 20px;">
          <label style="display: block; margin-bottom: 10px;">
            <span style="color: red;">*</span> 文件路径:
          </label>
          <el-input
            v-model="jsonForm.filePath"
            placeholder="请选择文件:json"
            readonly
            style="margin-bottom: 10px;"
          >
            <template #append>
              <el-button @click="selectFile('json')">选择文件</el-button>
            </template>
          </el-input>
        </div>

        <div style="margin-top: 20px;">
          <label style="display: block; margin-bottom: 10px;">
            <span style="color: #f56c6c;">*</span> 数据提取规则区:
          </label>
          <el-radio-group v-model="jsonForm.extractRule" style="margin-bottom: 10px;">
            <el-radio value="jsonpath">JSONPath输入框（手动输入）</el-radio>
          </el-radio-group>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)

// 表单引用
const csvFormRef = ref()
const excelFormRef = ref()
const jsonFormRef = ref()

// 表单数据
const csvForm = ref({
  filePath: '',
  separator: '',
  charset: ''
})

const excelForm = ref({
  filePath: '',
  sheetName: '',
  startPosition: ''
})

const jsonForm = ref({
  filePath: '',
  format: '',
  extractRule: 'jsonpath'
})

// 缓存键
const STORAGE_KEY = 'formatFileConfig'

// 选项数据（暂无）



// 表单验证规则
const csvFormRules = {
  filePath: [{ required: true, message: '请选择文件路径', trigger: 'change' }],
  separator: [{ required: true, message: '请输入分隔符', trigger: 'blur' }],
  charset: [{ required: true, message: '请输入编码', trigger: 'blur' }]
}

const excelFormRules = {
  filePath: [{ required: true, message: '请选择文件路径', trigger: 'change' }],
  sheetName: [{ required: true, message: '请选择工作表', trigger: 'change' }],
  startPosition: [{ required: true, message: '请输入数据起始行/列', trigger: 'blur' }]
}

const jsonFormRules = {
  filePath: [{ required: true, message: '请选择文件路径', trigger: 'change' }],
  format: [{ required: true, message: '请输入编码格式', trigger: 'blur' }]
}

// 加载配置数据
const loadConfigFromCache = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      const config = JSON.parse(cached)
      csvForm.value = config.csv || {
        filePath: '',
        separator: '',
        charset: ''
      }
      excelForm.value = config.excel || {
        filePath: '',
        sheetName: '',
        startPosition: ''
      }
      jsonForm.value = config.json || {
        filePath: '',
        format: '',
        extractRule: 'jsonpath'
      }
    } else {
      resetForm()
    }
  } catch (error) {
    console.error('加载格式文件配置失败:', error)
    resetForm()
  }
}

// 保存配置数据
const saveConfigToCache = () => {
  try {
    const config = {
      csv: csvForm.value,
      excel: excelForm.value,
      json: jsonForm.value
    }
    localStorage.setItem(STORAGE_KEY, JSON.stringify(config))
  } catch (error) {
    console.error('保存格式文件配置失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  csvForm.value = {
    filePath: '',
    separator: '',
    charset: ''
  }
  excelForm.value = {
    filePath: '',
    sheetName: '',
    startPosition: ''
  }
  jsonForm.value = {
    filePath: '',
    format: '',
    extractRule: 'jsonpath'
  }
}

// 文件选择
const selectFile = (type: string) => {
  // 创建文件输入元素
  const input = document.createElement('input')
  input.type = 'file'

  // 根据类型设置文件过滤器
  switch (type) {
    case 'csv':
      input.accept = '.csv'
      break
    case 'excel':
      input.accept = '.xlsx,.xls'
      break
    case 'json':
      input.accept = '.json'
      break
  }

  input.onchange = (event: any) => {
    const file = event.target.files[0]
    if (file) {
      const filePath = file.name // 在实际应用中，这里应该是文件的完整路径
      switch (type) {
        case 'csv':
          csvForm.value.filePath = filePath
          break
        case 'excel':
          excelForm.value.filePath = filePath
          break
        case 'json':
          jsonForm.value.filePath = filePath
          break
      }
    }
  }

  input.click()
}

// 验证必填项
const validateRequiredFields = () => {
  const errors = []

  // 验证CSV配置
  if (!csvForm.value.filePath) {
    errors.push('CSV文件路径不能为空')
  }
  if (!csvForm.value.separator) {
    errors.push('CSV分隔符不能为空')
  }
  if (!csvForm.value.charset) {
    errors.push('CSV编码不能为空')
  }

  // 验证Excel配置
  if (!excelForm.value.filePath) {
    errors.push('Excel文件路径不能为空')
  }
  if (!excelForm.value.sheetName) {
    errors.push('Excel工作表名称/索引不能为空')
  }
  if (!excelForm.value.startPosition) {
    errors.push('Excel数据起始行/列不能为空')
  }

  // 验证JSON配置
  if (!jsonForm.value.filePath) {
    errors.push('JSON文件路径不能为空')
  }
  if (!jsonForm.value.format) {
    errors.push('JSON编码格式不能为空')
  }

  return errors
}

// 确认保存
const onConfirm = () => {
  console.log('onConfirm 被调用')
  console.log('表单数据:', { csvForm: csvForm.value, excelForm: excelForm.value, jsonForm: jsonForm.value })

  // 验证必填项
  const errors = validateRequiredFields()
  if (errors.length > 0) {
    ElMessage.error(`请完善以下配置信息：\n${errors.join('\n')}`)
    return
  }

  // 保存配置
  loading.value = true
  setTimeout(() => {
    saveConfigToCache()
    ElMessage.success('格式文件配置保存成功')
    visible.value = false
    loading.value = false
  }, 1000)
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadConfigFromCache()
  }
})
</script>

<style lang="scss" scoped>
.format-file-config-content {
  .config-section {
    margin-bottom: 32px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
    }
  }
}
</style>
