<!-- 数据集类型配置弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="数据集类型"
    width="600px"
    :destroy-on-close="true"
    :visible-confirm-button="false"
    cancel-text="取消"
    @click-cancel="handleClose"
  >
    <div class="data-set-type-config-content" style="padding: 20px; min-height: 400px;">
      <!-- 顶部按钮 -->
      <div style="margin-bottom: 20px;">
        <el-button size="small" type="primary" @click="onClickAddType">新增数据集类型</el-button>
      </div>

      <!-- 数据集类型列表 -->
      <el-table :data="typeList" border style="width: 100%">
        <el-table-column prop="sequence" label="序号" width="80" align="center" />
        <el-table-column prop="typeName" label="类型名称" />
        <el-table-column prop="description" label="描述" />
        <el-table-column label="操作" width="120" align="center">
          <template #default="{ row }">
            <el-button size="small" type="primary" link @click="onClickEditType(row)">修改</el-button>
            <el-button size="small" type="danger" link @click="onClickDeleteType(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新增/编辑数据集类型弹窗 -->
    <Dialog
      v-model="showAddTypeDialog"
      title="数据集类型"
      width="400px"
      :destroy-on-close="true"
      @click-confirm="onConfirmAddType"
      @click-cancel="handleCloseAddType"
    >
      <div style="padding: 20px;">
        <el-form :model="typeForm" label-width="120px">
          <el-form-item label="数据集类型名:" required>
            <el-input v-model="typeForm.typeName" placeholder="请输入" />
          </el-form-item>
          
          <el-form-item label="数据集类型描述:">
            <el-input
              v-model="typeForm.description"
              type="textarea"
              :rows="4"
              placeholder="请输入"
            />
          </el-form-item>
          
          <el-form-item label="是否启用:">
            <el-switch v-model="typeForm.isEnabled" />
          </el-form-item>
        </el-form>
      </div>
    </Dialog>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 数据集类型接口
interface DataSetType {
  id: string
  sequence: number
  typeName: string
  description: string
  isEnabled: boolean
  createTime: string
}

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const showAddTypeDialog = ref(false)
const currentEditType = ref<DataSetType | null>(null)

// 数据集类型列表
const typeList = ref<DataSetType[]>([])

// 表单数据
const typeForm = ref({
  typeName: '',
  description: '',
  isEnabled: true
})

// 缓存键
const STORAGE_KEY = 'dataSetTypeConfig'

// 初始化模拟数据
const initMockData = () => {
  const mockData: DataSetType[] = [
    {
      id: '1',
      sequence: 1,
      typeName: '结构化数据',
      description: '结构化数据类型',
      isEnabled: true,
      createTime: '2024-01-15 10:30:00'
    },
    {
      id: '2',
      sequence: 2,
      typeName: '半结构化数据',
      description: '半结构化数据类型',
      isEnabled: true,
      createTime: '2024-01-14 09:15:00'
    },
    {
      id: '3',
      sequence: 3,
      typeName: '非结构化数据',
      description: '非结构化数据类型',
      isEnabled: true,
      createTime: '2024-01-13 15:20:00'
    },
    {
      id: '4',
      sequence: 4,
      typeName: '流式数据',
      description: '流式数据类型',
      isEnabled: false,
      createTime: '2024-01-12 11:45:00'
    }
  ]
  
  typeList.value = mockData
  saveDataToCache()
}

// 加载数据
const loadDataFromCache = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      typeList.value = JSON.parse(cached)
    } else {
      initMockData()
    }
  } catch (error) {
    console.error('加载数据集类型配置失败:', error)
    initMockData()
  }
}

// 保存数据
const saveDataToCache = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(typeList.value))
  } catch (error) {
    console.error('保存数据集类型配置失败:', error)
  }
}

// 重置表单
const resetTypeForm = () => {
  typeForm.value = {
    typeName: '',
    description: '',
    isEnabled: true
  }
  currentEditType.value = null
}

// 关闭主弹窗
const handleClose = () => {
  visible.value = false
}

// 新增数据集类型
const onClickAddType = () => {
  resetTypeForm()
  showAddTypeDialog.value = true
}

// 编辑数据集类型
const onClickEditType = (row: DataSetType) => {
  currentEditType.value = row
  typeForm.value = {
    typeName: row.typeName,
    description: row.description,
    isEnabled: row.isEnabled
  }
  showAddTypeDialog.value = true
}

// 删除数据集类型
const onClickDeleteType = (row: DataSetType) => {
  ElMessageBox.confirm(
    `确定要删除数据集类型"${row.typeName}"吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = typeList.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      typeList.value.splice(index, 1)
      // 重新排序
      typeList.value.forEach((item, idx) => {
        item.sequence = idx + 1
      })
      saveDataToCache()
      ElMessage.success('删除成功')
    }
  })
}

// 关闭新增/编辑弹窗
const handleCloseAddType = () => {
  showAddTypeDialog.value = false
  resetTypeForm()
}

// 确认新增/编辑
const onConfirmAddType = () => {
  if (!typeForm.value.typeName.trim()) {
    ElMessage.error('请输入数据集类型名称')
    return
  }

  if (currentEditType.value) {
    // 编辑模式
    const index = typeList.value.findIndex(item => item.id === currentEditType.value!.id)
    if (index !== -1) {
      typeList.value[index] = {
        ...typeList.value[index],
        typeName: typeForm.value.typeName,
        description: typeForm.value.description,
        isEnabled: typeForm.value.isEnabled
      }
      ElMessage.success('修改成功')
    }
  } else {
    // 新增模式
    const newType: DataSetType = {
      id: Date.now().toString(),
      sequence: typeList.value.length + 1,
      typeName: typeForm.value.typeName,
      description: typeForm.value.description,
      isEnabled: typeForm.value.isEnabled,
      createTime: new Date().toLocaleString()
    }
    typeList.value.push(newType)
    ElMessage.success('新增成功')
  }

  saveDataToCache()
  showAddTypeDialog.value = false
  resetTypeForm()
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadDataFromCache()
  }
})
</script>

<style scoped>
.data-set-type-config-content {
  display: flex;
  flex-direction: column;
}
</style>
