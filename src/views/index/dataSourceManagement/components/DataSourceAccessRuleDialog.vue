<!-- 数据源接入规则弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="数据源接入规则"
    width="500px"
    :destroy-on-close="true"
    :loading="loading"
    loading-text="保存中"
    @closed="resetForm"
    @click-confirm="onConfirm"
  >
    <div class="data-source-access-rule-content" style="padding: 20px; min-height: 270px;">
      <!-- 连接控制规则 -->
      <div class="rule-section">
        <h4 style="margin-bottom: 15px; color: #333;">连接控制规则：</h4>

        <div class="form-item">
          <label style="display: block; margin-bottom: 8px;">超时设置</label>
          <el-input
            v-model="form.timeout"
            placeholder="请输入（s）"
            style="width: 100%;"
          />
        </div>

        <div class="form-item" style="margin-top: 15px;">
          <label style="display: block; margin-bottom: 8px;">最大重试次数：</label>
          <el-input
            v-model="form.maxRetries"
            placeholder="请输入"
            style="width: 100%;"
          />
        </div>

        <div class="form-item" style="margin-top: 15px; display: flex; align-items: center;">
          <span style="color: red; margin-right: 5px;">*</span>
          <label style="margin-right: 10px;">是否启动：</label>
          <el-switch v-model="form.connectionEnabled" />
        </div>
      </div>

    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const formRef = ref()

// 表单数据
const form = ref({
  timeout: '',
  maxRetries: '',
  connectionEnabled: true
})

// 缓存键
const STORAGE_KEY = 'dataSourceAccessRule'

// 表单验证规则
const formRules = {
  description: [{ required: true, message: '请输入接入规则描述', trigger: 'blur' }]
}

// 加载配置数据
const loadConfigFromCache = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      const config = JSON.parse(cached)
      form.value = {
        timeout: config.timeout || '',
        maxRetries: config.maxRetries || '',
        connectionEnabled: config.connectionEnabled !== undefined ? config.connectionEnabled : true
      }
    } else {
      resetForm()
    }
  } catch (error) {
    console.error('加载数据源接入规则失败:', error)
    resetForm()
  }
}

// 保存配置数据
const saveConfigToCache = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(form.value))
  } catch (error) {
    console.error('保存数据源接入规则失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    timeout: '',
    maxRetries: '',
    connectionEnabled: true
  }
}


// 确认保存
const onConfirm = () => {
  loading.value = true
  setTimeout(() => {
    saveConfigToCache()
    ElMessage.success('数据源接入规则保存成功')
    visible.value = false
    loading.value = false
  }, 1000)
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadConfigFromCache()
  }
})
</script>

<style lang="scss" scoped>
.data-source-access-rule-content {
  .rule-section {
    margin-bottom: 20px;

    h4 {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 15px;
    }
  }

  .form-item {
    margin-bottom: 15px;

    label {
      font-size: 14px;
      color: #333;
      font-weight: 400;
    }
  }
}
</style>
