<template>
  <Dialog
    v-model="visible"
    :title="getDialogTitle()"
    width="600px"
    :destroy-on-close="true"
    :loading="loading"
    loading-text="保存中"
    @closed="onDialogClosed"
    @click-confirm="onConfirm"
  >
    <div class="database-config-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="left"
      >
        <!-- 数据库类型（只读显示） -->
        <el-form-item label="数据库:" prop="database">
          <el-select v-model="formData.database" disabled style="width: 100%">
            <el-option :label="databaseType" :value="databaseType" />
          </el-select>
        </el-form-item>

        <!-- 数据库地址/服务器地址/主机名 -->
        <el-form-item :label="getAddressLabel()" prop="address">
          <el-input v-model="formData.address" :placeholder="getAddressPlaceholder()" />
        </el-form-item>

        <!-- 端口 -->
        <el-form-item label="端口:" prop="port">
          <el-input v-model="formData.port" placeholder="请输入端口" />
        </el-form-item>

        <!-- 数据库名称 -->
        <el-form-item label="数据库名称:" prop="databaseName">
          <el-input v-model="formData.databaseName" placeholder="请输入数据库名称" />
        </el-form-item>

        <!-- SQL Server 和 达梦 特有的身份认证字段 -->
        <el-form-item 
          v-if="databaseType === 'SQL Server' || databaseType === '达梦'" 
          label="身份认证:" 
          prop="authentication"
        >
          <el-select v-model="formData.authentication" placeholder="请选择身份认证方式" style="width: 100%">
            <el-option 
              v-if="databaseType === 'SQL Server'"
              label="SQL Server 认证" 
              value="sqlserver" 
            />
            <el-option 
              v-if="databaseType === 'SQL Server'"
              label="Windows 认证" 
              value="windows" 
            />
            <el-option 
              v-if="databaseType === '达梦'"
              label="用户名密码认证" 
              value="password" 
            />
            <el-option 
              v-if="databaseType === '达梦'"
              label="集成认证" 
              value="integrated" 
            />
          </el-select>
        </el-form-item>

        <!-- 用户名 -->
        <el-form-item label="用户名:" prop="username">
          <el-input v-model="formData.username" placeholder="请输入用户名" />
        </el-form-item>

        <!-- 密码 -->
        <el-form-item label="密码:" prop="password">
          <el-input 
            v-model="formData.password" 
            type="password" 
            placeholder="请输入密码" 
            show-password 
          />
        </el-form-item>

        <!-- SQL Server 特有的提示信息 -->
        <div v-if="databaseType === 'SQL Server'" class="auth-tips">
          <p>若选择"SQL Server 认证"：需填写用户名（如sa）和密码。</p>
          <p>若选择"Windows 认证"：自动使用当前Windows登录账户（无需填写用户名/密码）</p>
        </div>
      </el-form>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

interface DatabaseConfig {
  database: string
  address: string
  port: string
  databaseName: string
  username: string
  password: string
  authentication?: string // SQL Server 和 达梦 特有
}

interface Props {
  modelValue: boolean
  databaseType: string
  configData?: DatabaseConfig
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: DatabaseConfig): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  databaseType: '',
  configData: undefined
})

const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const formRef = ref()

// 表单数据
const formData = ref<DatabaseConfig>({
  database: '',
  address: '',
  port: '',
  databaseName: '',
  username: '',
  password: '',
  authentication: ''
})

// 表单验证规则
const formRules = {
  address: [
    { required: true, message: '请输入地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口', trigger: 'blur' },
    { pattern: /^\d+$/, message: '端口必须为数字', trigger: 'blur' }
  ],
  databaseName: [
    { required: true, message: '请输入数据库名称', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  authentication: [
    { required: true, message: '请选择身份认证方式', trigger: 'change' }
  ]
}

// 获取弹窗标题
const getDialogTitle = () => {
  return `${props.databaseType}数据库配置`
}

// 获取地址标签
const getAddressLabel = () => {
  switch (props.databaseType) {
    case 'MySQL':
    case 'Oracle':
      return '数据库地址:'
    case 'SQL Server':
    case '达梦':
      return '服务器地址:'
    case 'Hive':
    case 'MongoDB':
    case 'Huawei GaussDB':
      return '主机名:'
    default:
      return '地址:'
  }
}

// 获取地址占位符
const getAddressPlaceholder = () => {
  switch (props.databaseType) {
    case 'MySQL':
    case 'Oracle':
      return '请输入数据库地址'
    case 'SQL Server':
    case '达梦':
      return '请输入服务器地址'
    case 'Hive':
    case 'MongoDB':
    case 'Huawei GaussDB':
      return '请输入主机名'
    default:
      return '请输入地址'
  }
}

// 监听数据库类型变化，重置表单
watch(() => props.databaseType, (newType) => {
  if (newType) {
    formData.value.database = newType
    // 设置默认端口
    switch (newType) {
      case 'MySQL':
        formData.value.port = '3306'
        break
      case 'Oracle':
        formData.value.port = '1521'
        break
      case 'SQL Server':
        formData.value.port = '1433'
        break
      case '达梦':
        formData.value.port = '5236'
        break
      case 'Hive':
        formData.value.port = '10000'
        break
      case 'MongoDB':
        formData.value.port = '27017'
        break
      case 'Huawei GaussDB':
        formData.value.port = '8000'
        break
    }
  }
}, { immediate: true })

// 监听配置数据变化，用于回显
watch(() => props.configData, (newData) => {
  if (newData) {
    Object.assign(formData.value, newData)
  }
}, { immediate: true, deep: true })

// 弹窗关闭
const onDialogClosed = () => {
  // 重置表单
  formData.value = {
    database: props.databaseType,
    address: '',
    port: '',
    databaseName: '',
    username: '',
    password: '',
    authentication: ''
  }
  formRef.value?.resetFields()
}

// 确认保存
const onConfirm = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      loading.value = true
      
      // 模拟保存过程
      setTimeout(() => {
        loading.value = false
        ElMessage.success('数据库配置保存成功')
        emit('confirm', { ...formData.value })
        visible.value = false
      }, 1000)
    }
  })
}
</script>

<style scoped>
.database-config-content {
  padding: 20px 0;
}

.auth-tips {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
  line-height: 1.5;
}

.auth-tips p {
  margin: 0 0 5px 0;
}

.auth-tips p:last-child {
  margin-bottom: 0;
}
</style>
