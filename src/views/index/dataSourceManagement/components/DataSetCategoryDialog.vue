<!-- 数据集分类弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="数据集分类"
    width="600px"
    :destroy-on-close="true"
    :visible-confirm-button="false"
    cancel-text="取消"
    @click-cancel="handleClose"
  >
    <div class="data-set-category-content" style="padding: 20px; min-height: 400px;">
      <!-- 顶部按钮 -->
      <div style="margin-bottom: 20px;">
        <el-button size="small" type="primary" @click="onClickAddCategory">新增分类</el-button>
      </div>

      <!-- 数据集分类列表 -->
      <el-table :data="categoryList" border style="width: 100%">
        <el-table-column prop="sequence" label="序号" width="80" align="center" />
        <el-table-column prop="categoryName" label="分类名称" />
        <el-table-column prop="description" label="描述" />
        <el-table-column label="操作" width="120" align="center">
          <template #default="{ row }">
            <el-button size="small" type="primary" link @click="onClickEditCategory(row)">修改</el-button>
            <el-button size="small" type="danger" link @click="onClickDeleteCategory(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新增/编辑数据集分类弹窗 -->
    <Dialog
      v-model="showAddCategoryDialog"
      title="数据集分类"
      width="400px"
      :destroy-on-close="true"
      @click-confirm="onConfirmAddCategory"
      @click-cancel="handleCloseAddCategory"
    >
      <div style="padding: 20px;">
        <el-form :model="categoryForm" label-width="120px">
          <el-form-item label="数据集分类名:" required>
            <el-input v-model="categoryForm.categoryName" placeholder="请输入" />
          </el-form-item>
          
          <el-form-item label="数据集分类描述:">
            <el-input
              v-model="categoryForm.description"
              type="textarea"
              :rows="4"
              placeholder="请输入"
            />
          </el-form-item>
          
          <el-form-item label="是否启用:">
            <el-switch v-model="categoryForm.isEnabled" />
          </el-form-item>
        </el-form>
      </div>
    </Dialog>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 数据集分类接口
interface DataSetCategory {
  id: string
  sequence: number
  categoryName: string
  description: string
  isEnabled: boolean
  createTime: string
}

// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const showAddCategoryDialog = ref(false)
const currentEditCategory = ref<DataSetCategory | null>(null)

// 数据集分类列表
const categoryList = ref<DataSetCategory[]>([])

// 表单数据
const categoryForm = ref({
  categoryName: '',
  description: '',
  isEnabled: true
})

// 缓存键
const STORAGE_KEY = 'dataSetCategoryConfig'

// 初始化模拟数据
const initMockData = () => {
  const mockData: DataSetCategory[] = [
    {
      id: '1',
      sequence: 1,
      categoryName: '业务数据',
      description: '业务相关数据',
      isEnabled: true,
      createTime: '2024-01-15 10:30:00'
    },
    {
      id: '2',
      sequence: 2,
      categoryName: '日志数据',
      description: '系统日志数据',
      isEnabled: true,
      createTime: '2024-01-14 09:15:00'
    },
    {
      id: '3',
      sequence: 3,
      categoryName: '监控数据',
      description: '监控指标数据',
      isEnabled: true,
      createTime: '2024-01-13 15:20:00'
    },
    {
      id: '4',
      sequence: 4,
      categoryName: '分析数据',
      description: '数据分析结果',
      isEnabled: false,
      createTime: '2024-01-12 11:45:00'
    }
  ]
  
  categoryList.value = mockData
  saveDataToCache()
}

// 加载数据
const loadDataFromCache = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      categoryList.value = JSON.parse(cached)
    } else {
      initMockData()
    }
  } catch (error) {
    console.error('加载数据集分类配置失败:', error)
    initMockData()
  }
}

// 保存数据
const saveDataToCache = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(categoryList.value))
  } catch (error) {
    console.error('保存数据集分类配置失败:', error)
  }
}

// 重置表单
const resetCategoryForm = () => {
  categoryForm.value = {
    categoryName: '',
    description: '',
    isEnabled: true
  }
  currentEditCategory.value = null
}

// 关闭主弹窗
const handleClose = () => {
  visible.value = false
}

// 新增数据集分类
const onClickAddCategory = () => {
  resetCategoryForm()
  showAddCategoryDialog.value = true
}

// 编辑数据集分类
const onClickEditCategory = (row: DataSetCategory) => {
  currentEditCategory.value = row
  categoryForm.value = {
    categoryName: row.categoryName,
    description: row.description,
    isEnabled: row.isEnabled
  }
  showAddCategoryDialog.value = true
}

// 删除数据集分类
const onClickDeleteCategory = (row: DataSetCategory) => {
  ElMessageBox.confirm(
    `确定要删除数据集分类"${row.categoryName}"吗？`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    const index = categoryList.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      categoryList.value.splice(index, 1)
      // 重新排序
      categoryList.value.forEach((item, idx) => {
        item.sequence = idx + 1
      })
      saveDataToCache()
      ElMessage.success('删除成功')
    }
  })
}

// 关闭新增/编辑弹窗
const handleCloseAddCategory = () => {
  showAddCategoryDialog.value = false
  resetCategoryForm()
}

// 确认新增/编辑
const onConfirmAddCategory = () => {
  if (!categoryForm.value.categoryName.trim()) {
    ElMessage.error('请输入数据集分类名称')
    return
  }

  if (currentEditCategory.value) {
    // 编辑模式
    const index = categoryList.value.findIndex(item => item.id === currentEditCategory.value!.id)
    if (index !== -1) {
      categoryList.value[index] = {
        ...categoryList.value[index],
        categoryName: categoryForm.value.categoryName,
        description: categoryForm.value.description,
        isEnabled: categoryForm.value.isEnabled
      }
      ElMessage.success('修改成功')
    }
  } else {
    // 新增模式
    const newCategory: DataSetCategory = {
      id: Date.now().toString(),
      sequence: categoryList.value.length + 1,
      categoryName: categoryForm.value.categoryName,
      description: categoryForm.value.description,
      isEnabled: categoryForm.value.isEnabled,
      createTime: new Date().toLocaleString()
    }
    categoryList.value.push(newCategory)
    ElMessage.success('新增成功')
  }

  saveDataToCache()
  showAddCategoryDialog.value = false
  resetCategoryForm()
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadDataFromCache()
  }
})
</script>

<style scoped>
.data-set-category-content {
  display: flex;
  flex-direction: column;
}
</style>
