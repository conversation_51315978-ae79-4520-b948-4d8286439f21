<template>
  <Dialog
    v-model="visible"
    title="数据源归档"
    width="1200px"
    :destroy-on-close="true"
    :visible-confirm-button="false"
    cancel-text="关闭"
    @click-cancel="visible = false"
  >
    <div class="archive-management" style="padding: 20px; min-height: 380px;">
      <!-- 操作按钮 -->
      <div class="operation-buttons" style="margin-bottom: 20px;">
        <el-button type="primary" @click="showAddDialog = true">新增</el-button>
        <el-button @click="handleBatchDelete">批量删除</el-button>
      </div>

      <!-- 数据表格 -->
      <el-table 
        :data="tableData" 
        style="width: 100%" 
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="archiveId" label="归档ID" width="120" />
        <el-table-column prop="dataSourceName" label="数据源名称" width="150" />
        <el-table-column prop="archiveTime" label="归档时间" width="180" />
        <el-table-column prop="dataSize" label="数据大小" width="120" />
        <el-table-column prop="storageLocation" label="存储位置" min-width="200" />
        <el-table-column label="操作" width="150" align="center">
          <template #default="{ row }">
            <el-button size="small" @click="handleEdit(row)">修改</el-button>
            <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination" style="margin-top: 20px; text-align: center;">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <Dialog
      v-model="showAddDialog"
      :title="currentRow ? '修改数据源归档' : '新增数据源归档'"
      width="600px"
      :destroy-on-close="true"
      :loading="formLoading"
      loading-text="保存中"
      @closed="resetForm"
      @click-confirm="onConfirm"
    >
      <div style="padding: 20px; min-height: 350px;">
        <Form
          ref="formRef"
          v-model="form"
          :props="[
            { label: '数据源名称', prop: 'dataSourceName', type: 'select', required: true, options: dataSourceOptions, placeholder: '请选择数据源' },
            { label: '归档时间', prop: 'archiveTime', type: 'datetime', required: true, placeholder: '请选择归档时间' },
            { label: '存储位置', prop: 'storageLocation', type: 'text', required: true, placeholder: '请输入存储位置' },
            { label: '备注', prop: 'remark', type: 'textarea', placeholder: '请输入备注信息' }
          ]"
          :rules="formRules"
          :enable-button="false"
        />
      </div>
    </Dialog>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const showAddDialog = ref(false)
const formLoading = ref(false)
const currentRow = ref(null)
const formRef = ref()
const selectedRows = ref([])

// 表格数据
const tableData = ref([
  {
    id: 1,
    archiveId: 'ARC20240115001',
    dataSourceName: '用户数据库',
    archiveTime: '2024-01-15 10:30:00',
    dataSize: '2.5GB',
    storageLocation: '/archive/user_db/2024/01/15'
  },
  {
    id: 2,
    archiveId: 'ARC20240114001',
    dataSourceName: '订单数据库',
    archiveTime: '2024-01-14 14:20:00',
    dataSize: '1.8GB',
    storageLocation: '/archive/order_db/2024/01/14'
  },
  {
    id: 3,
    archiveId: 'ARC20240113001',
    dataSourceName: '日志数据库',
    archiveTime: '2024-01-13 09:15:00',
    dataSize: '5.2GB',
    storageLocation: '/archive/log_db/2024/01/13'
  }
])

// 分页数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(3)

// 表单数据
const form = ref({
  dataSourceName: '',
  archiveTime: '',
  storageLocation: '',
  remark: ''
})

// 表单验证规则
const formRules = ref({
  dataSourceName: [
    { required: true, message: '请选择数据源', trigger: 'change' }
  ],
  archiveTime: [
    { required: true, message: '请选择归档时间', trigger: 'change' }
  ],
  storageLocation: [
    { required: true, message: '请输入存储位置', trigger: 'blur' }
  ]
})

// 数据源选项 - 从数据源管理中获取
const dataSourceOptions = ref([])

// 从localStorage获取数据源管理中的数据
const loadDataSourceOptions = () => {
  try {
    const cached = localStorage.getItem('dataSourceManagement_data')
    if (cached) {
      const dataSourceList = JSON.parse(cached)
      dataSourceOptions.value = dataSourceList.map((item: any) => ({
        label: item.name,
        value: item.name
      }))
    } else {
      // 如果没有缓存数据，使用默认数据
      dataSourceOptions.value = [
        { label: 'MySQL数据库1', value: 'MySQL数据库1' },
        { label: 'Oracle数据库1', value: 'Oracle数据库1' },
        { label: 'SQL Server数据库1', value: 'SQL Server数据库1' },
        { label: '达梦数据库1', value: '达梦数据库1' }
      ]
    }
  } catch (error) {
    console.error('加载数据源选项失败:', error)
    // 使用默认数据
    dataSourceOptions.value = [
      { label: 'MySQL数据库1', value: 'MySQL数据库1' },
      { label: 'Oracle数据库1', value: 'Oracle数据库1' },
      { label: 'SQL Server数据库1', value: 'SQL Server数据库1' },
      { label: '达梦数据库1', value: '达梦数据库1' }
    ]
  }
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 处理编辑
const handleEdit = (row: any) => {
  currentRow.value = row
  form.value = { ...row }
  showAddDialog.value = true
}

// 处理删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这条归档记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 模拟删除操作
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      tableData.value.splice(index, 1)
      total.value--
      ElMessage.success('删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 条记录吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 模拟批量删除操作
    const selectedIds = selectedRows.value.map((row: any) => row.id)
    tableData.value = tableData.value.filter(item => !selectedIds.includes(item.id))
    total.value = tableData.value.length
    selectedRows.value = []
    ElMessage.success('批量删除成功')
  } catch {
    // 用户取消删除
  }
}

// 重置表单
const resetForm = () => {
  currentRow.value = null
  form.value = {
    dataSourceName: '',
    archiveTime: '',
    storageLocation: '',
    remark: ''
  }
  formRef.value?.resetFields()
}

// 确认保存
const onConfirm = async () => {
  try {
    await formRef.value?.validate()
    formLoading.value = true
    
    // 模拟保存操作
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (currentRow.value) {
      // 编辑
      const index = tableData.value.findIndex(item => item.id === currentRow.value.id)
      if (index > -1) {
        tableData.value[index] = {
          ...tableData.value[index],
          ...form.value
        }
      }
      ElMessage.success('修改成功')
    } else {
      // 新增
      const newItem = {
        id: Date.now(),
        archiveId: `ARC${new Date().toISOString().slice(0, 10).replace(/-/g, '')}${String(Date.now()).slice(-3)}`,
        ...form.value,
        dataSize: '计算中...'
      }
      tableData.value.unshift(newItem)
      total.value++
      ElMessage.success('新增成功')
    }
    
    showAddDialog.value = false
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    formLoading.value = false
  }
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  // 重新加载数据
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  // 重新加载数据
}

onMounted(() => {
  // 初始化数据
  loadDataSourceOptions()
})
</script>

<style scoped lang="scss">
.archive-management {
  .operation-buttons {
    display: flex;
    justify-content: flex-start;
    gap: 10px;
  }

  .pagination {
    display: flex;
    justify-content: center;
  }
}
</style>
