<!-- 创建表连接数据集三步骤向导弹窗 -->
<template>
  <Dialog
    v-model="visible"
    :title="dialogTitle"
    width="1200px"
    :destroy-on-close="true"
    :visible-confirm-button="false"
    @click-confirm="handleNext"
    @click-cancel="handleCancel"
    @closed="handleClose"
  >
    <!-- 自定义底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ isReadonly ? '关闭' : '取消' }}</el-button>
        <el-button
          v-if="!isReadonly && currentStep > 1 && currentStep < 3"
          @click="handlePrevious"
        >
          上一步
        </el-button>
        <el-button
          v-if="!isReadonly && currentStep < 3"
          type="primary"
          @click="handleNext"
          :loading="loading"
        >
          {{ currentStep === 2 ? (isEditMode ? '更新' : '保存') : '下一步' }}
        </el-button>
      </div>
    </template>
    <!-- 步骤指示器 -->
    <div class="wizard-steps">
      <div class="steps-container">
        <div 
          v-for="(step, index) in steps" 
          :key="index"
          class="step-item"
          :class="{ 
            'active': currentStep === index + 1,
            'completed': currentStep > index + 1 
          }"
        >
          <div class="step-number">{{ index + 1 }}</div>
          <div class="step-title">{{ step.title }}</div>
        </div>
      </div>
    </div>

    <!-- 步骤内容 -->
    <div class="wizard-content">
      <!-- 第一步：选择数据源 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="form-section">
          <h3 class="section-title">基础信息</h3>
          <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="数据集名称" prop="name">
                  <el-input
                    v-model="formData.name"
                    placeholder="请输入数据集名称"
                    :readonly="isReadonly"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="数据集类型" prop="type">
                  <el-select
                    v-model="formData.type"
                    placeholder="请选择数据集类型"
                    style="width: 100%"
                    :disabled="isReadonly"
                  >
                    <el-option
                      v-for="option in datasetTypeOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="数据集分类" prop="category">
                  <el-select
                    v-model="formData.category"
                    placeholder="请选择数据集分类"
                    style="width: 100%"
                    :disabled="isReadonly"
                  >
                    <el-option
                      v-for="option in datasetCategoryOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="数据源" prop="dataSource">
                  <el-select
                    v-model="formData.dataSource"
                    placeholder="请选择数据源"
                    style="width: 100%"
                    :disabled="isReadonly"
                    @change="handleDataSourceChange"
                  >
                    <el-option
                      v-for="option in dataSourceOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="数据集标签" prop="tags">
                  <el-select
                    v-model="formData.tags"
                    multiple
                    placeholder="请选择数据集标签"
                    style="width: 100%"
                    :disabled="isReadonly"
                  >
                    <el-option
                      v-for="option in datasetTagOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="数据集描述" prop="description">
                  <el-input
                    v-model="formData.description"
                    placeholder="请输入数据集描述"
                    type="textarea"
                    :rows="2"
                    :readonly="isReadonly"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <!-- 表关联配置区域 -->
        <div class="table-relation-section" v-if="formData.dataSource">
          <h3 class="section-title">表关联配置</h3>
          <div class="relation-container">
            <!-- 左侧：数据库表列表 -->
            <div class="table-list-container">
              <div class="table-list-section">
                <h4>主表区域</h4>
                <div class="table-list main-tables">
                  <div 
                    v-for="table in mainTables" 
                    :key="table.id"
                    class="table-item"
                    :class="{ 'selected': selectedTables.includes(table.id) }"
                    draggable="true"
                    @dragstart="handleDragStart($event, table)"
                    @click="toggleTableSelection(table.id)"
                  >
                    <div class="table-name">{{ table.displayName }}</div>
                    <div class="table-info">{{ table.name }}</div>
                  </div>
                </div>
              </div>
              
              <div class="table-list-section">
                <h4>关联表区域</h4>
                <div class="table-list related-tables">
                  <div 
                    v-for="table in relatedTables" 
                    :key="table.id"
                    class="table-item"
                    :class="{ 'selected': selectedTables.includes(table.id) }"
                    draggable="true"
                    @dragstart="handleDragStart($event, table)"
                    @click="toggleTableSelection(table.id)"
                  >
                    <div class="table-name">{{ table.displayName }}</div>
                    <div class="table-info">{{ table.name }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧：关联视图画布 -->
            <div class="canvas-container">
              <h4>关联视图</h4>
              <div class="canvas-instructions" v-if="canvasTables.length > 0">
                <el-alert
                  title="连接提示：悬停表格显示锚点，点击两个不同表的锚点进行精确连接"
                  type="info"
                  :closable="false"
                  show-icon
                />
              </div>
              <div
                class="relation-canvas"
                @drop="handleDrop"
                @dragover="handleDragOver"
              >
                <div class="canvas-hint" v-if="canvasTables.length === 0">
                  请从左侧拖拽数据库表到此区域
                </div>
                
                <!-- 画布中的表 -->
                <div
                  v-for="(table, index) in canvasTables"
                  :key="table.id"
                  class="canvas-table"
                  :style="{
                    left: table.x + 'px',
                    top: table.y + 'px'
                  }"
                  :class="{ 'selected': selectedTableForConnection === table.id }"
                >
                  <!-- 锚点 -->
                  <div
                    class="anchor-point anchor-top"
                    @click.stop="handleAnchorClick(table.id, 'top')"
                  ></div>
                  <div
                    class="anchor-point anchor-right"
                    @click.stop="handleAnchorClick(table.id, 'right')"
                  ></div>
                  <div
                    class="anchor-point anchor-bottom"
                    @click.stop="handleAnchorClick(table.id, 'bottom')"
                  ></div>
                  <div
                    class="anchor-point anchor-left"
                    @click.stop="handleAnchorClick(table.id, 'left')"
                  ></div>

                  <div class="table-header">
                    <span>{{ table.displayName }}</span>
                    <el-button
                      type="danger"
                      size="small"
                      circle
                      @click.stop="removeTableFromCanvas(table.id)"
                    >
                      <el-icon><Close /></el-icon>
                    </el-button>
                  </div>
                  <div class="table-info">
                    <div class="table-name">{{ table.name }}</div>
                    <div class="field-count">{{ table.fields.length }} 个字段</div>
                  </div>
                </div>

                <!-- 连接线 -->
                <svg class="connection-lines" v-if="connections.length > 0">
                  <line 
                    v-for="(connection, index) in connections"
                    :key="index"
                    :x1="connection.x1"
                    :y1="connection.y1"
                    :x2="connection.x2"
                    :y2="connection.y2"
                    stroke="#409EFF"
                    stroke-width="2"
                  />
                  <circle 
                    v-for="(connection, index) in connections"
                    :key="'icon-' + index"
                    :cx="(connection.x1 + connection.x2) / 2"
                    :cy="(connection.y1 + connection.y2) / 2"
                    r="10"
                    fill="#409EFF"
                    class="connection-icon"
                    @click="showRelationConfig(connection)"
                  />
                </svg>
              </div>
            </div>
          </div>

          <!-- 关联配置区域 -->
          <div class="relation-config-section" v-if="canvasTables.length >= 2">
            <h4>关联配置</h4>
            <div class="relation-config">
              <div class="join-type-section">
                <div class="join-type-row">
                  <label>关联方式：</label>
                  <el-radio-group v-model="relationConfig.joinType">
                    <el-radio label="left">左关联</el-radio>
                    <el-radio label="right">右关联</el-radio>
                    <el-radio label="outer">外关联</el-radio>
                    <el-radio label="inner">内关联</el-radio>
                  </el-radio-group>
                  <el-button type="primary" @click="addRelationField" style="margin-left: 20px;">
                    添加关联字段
                  </el-button>
                </div>
              </div>

              <div class="relation-fields-section">
                <div class="relation-tables-info">
                  <div class="table-info-item">
                    <strong>{{ canvasTables[0]?.displayName || '表1' }}</strong>
                  </div>
                  <div class="relation-symbol">⟷</div>
                  <div class="table-info-item">
                    <strong>{{ canvasTables[1]?.displayName || '表2' }}</strong>
                  </div>
                </div>

                <div class="relation-fields-list">
                  <div
                    v-for="(relation, index) in relationConfig.fields"
                    :key="index"
                    class="relation-field-item"
                  >
                    <el-select
                      v-model="relation.leftField"
                      placeholder="选择左表字段"
                      style="width: 200px"
                    >
                      <el-option
                        v-for="field in canvasTables[0]?.fields || []"
                        :key="field.name"
                        :label="field.name"
                        :value="field.name"
                      />
                    </el-select>

                    <span class="relation-operator">=</span>

                    <el-select
                      v-model="relation.rightField"
                      placeholder="选择右表字段"
                      style="width: 200px"
                    >
                      <el-option
                        v-for="field in canvasTables[1]?.fields || []"
                        :key="field.name"
                        :label="field.name"
                        :value="field.name"
                      />
                    </el-select>

                    <el-button
                      type="danger"
                      size="small"
                      @click="removeRelationField(index)"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第二步：配置数据项 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="data-config-section">
          <h3 class="section-title">字段配置</h3>
          <div class="field-config-table">
            <el-table :data="fieldConfigData" border style="width: 100%" max-height="400">
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column prop="physicalName" label="物理表字段名称" min-width="150" />
              <el-table-column prop="fieldType" label="字段类型" width="100" />
              <el-table-column prop="length" label="长度" width="80" />
              <el-table-column label="不为空" width="80" align="center">
                <template #default="{ row }">
                  <el-checkbox v-model="row.notNull" />
                </template>
              </el-table-column>
              <el-table-column label="数据项名称" min-width="200">
                <template #default="{ row }">
                  <el-input
                    v-model="row.dataItemName"
                    placeholder="请输入数据项名称"
                    @blur="validateDataItemName(row)"
                  />
                </template>
              </el-table-column>
              <el-table-column label="加密" width="80" align="center">
                <template #default="{ row }">
                  <el-checkbox v-model="row.encrypted" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150" align="center">
                <template #default="{ row, $index }">
                  <el-button
                    :type="row.visible ? 'success' : 'info'"
                    size="small"
                    @click="toggleFieldVisibility(row)"
                  >
                    {{ row.visible ? '显示' : '隐藏' }}
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="deleteField(row, $index)"
                    style="margin-left: 5px;"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 脱敏配置区域 -->
          <div class="desensitization-section">
            <h3 class="section-title">脱敏配置</h3>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="名称脱敏">
                  <el-select 
                    v-model="desensitizationConfig.nameFields" 
                    multiple 
                    placeholder="请选择字段"
                    style="width: 100%"
                  >
                    <el-option 
                      v-for="field in nameRelatedFields"
                      :key="field.physicalName"
                      :label="field.physicalName"
                      :value="field.physicalName"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="证件号脱敏">
                  <el-select 
                    v-model="desensitizationConfig.idFields" 
                    multiple 
                    placeholder="请选择字段"
                    style="width: 100%"
                  >
                    <el-option 
                      v-for="field in idRelatedFields"
                      :key="field.physicalName"
                      :label="field.physicalName"
                      :value="field.physicalName"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系电话脱敏">
                  <el-select 
                    v-model="desensitizationConfig.phoneFields" 
                    multiple 
                    placeholder="请选择字段"
                    style="width: 100%"
                  >
                    <el-option 
                      v-for="field in phoneRelatedFields"
                      :key="field.physicalName"
                      :label="field.physicalName"
                      :value="field.physicalName"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>

      <!-- 第三步：保存设计 -->
      <div v-if="currentStep === 3" class="step-content">
        <div class="save-design-section">
          <div class="save-animation">
            <el-icon class="loading-icon"><Loading /></el-icon>
            <h3>正在保存数据集设计...</h3>
            <p>请稍候，系统正在处理您的数据集配置</p>
          </div>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Close, Loading } from '@element-plus/icons-vue'
// Dialog组件已全局注册，无需导入

// Props
interface Props {
  visible: boolean
  mode?: 'add' | 'edit' | 'detail'
  editData?: any
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  mode: 'add',
  editData: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'success': []
}>()

// 响应式数据
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const loading = ref(false)
const currentStep = ref(1)
const formRef = ref()

// 步骤配置
const steps = [
  { title: '选择数据源' },
  { title: '配置数据项' },
  { title: '保存设计' }
]

// 计算属性
const dialogTitle = computed(() => {
  const stepTitle = steps[currentStep.value - 1]?.title || ''
  switch (props.mode) {
    case 'detail':
      return `表连接数据集详情 - ${stepTitle}`
    case 'edit':
      return `编辑表连接数据集 - ${stepTitle}`
    default:
      return `创建表连接数据集 - ${stepTitle}`
  }
})

const isReadonly = computed(() => props.mode === 'detail')
const isEditMode = computed(() => props.mode === 'edit')

// 表单数据
const formData = reactive({
  name: '',
  type: '',
  category: '',
  dataSource: '',
  tags: [],
  description: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入数据集名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择数据集类型', trigger: 'change' }
  ],
  category: [
    { required: true, message: '请选择数据集分类', trigger: 'change' }
  ],
  dataSource: [
    { required: true, message: '请选择数据源', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入数据集描述', trigger: 'blur' }
  ]
}

// 选项数据
const datasetTypeOptions = ref([])
const datasetCategoryOptions = ref([])
const datasetTagOptions = ref([])
const dataSourceOptions = ref([])

// 数据库表数据
const mainTables = ref([])
const relatedTables = ref([])
const selectedTables = ref([])
const canvasTables = ref([])
const connections = ref([])
const selectedTableForConnection = ref('')
const selectedAnchor = ref(null) // 存储选中的锚点信息 { tableId, position }

// 关联配置
const relationConfig = reactive({
  joinType: 'inner',
  fields: []
})

// 字段配置数据
const fieldConfigData = ref([])

// 脱敏配置
const desensitizationConfig = reactive({
  nameFields: [],
  idFields: [],
  phoneFields: []
})

// 计算属性：筛选相关字段
const nameRelatedFields = computed(() => {
  return fieldConfigData.value.filter(field => 
    field.physicalName.includes('name') || 
    field.physicalName.includes('姓名') ||
    field.physicalName.includes('名称')
  )
})

const idRelatedFields = computed(() => {
  return fieldConfigData.value.filter(field => 
    field.physicalName.includes('id') || 
    field.physicalName.includes('身份证') ||
    field.physicalName.includes('证件')
  )
})

const phoneRelatedFields = computed(() => {
  return fieldConfigData.value.filter(field => 
    field.physicalName.includes('phone') || 
    field.physicalName.includes('电话') ||
    field.physicalName.includes('手机')
  )
})

// 方法
const handleNext = async () => {
  if (currentStep.value === 1) {
    // 验证第一步表单
    const valid = await formRef.value?.validate()
    if (!valid) return

    if (canvasTables.value.length < 2) {
      ElMessage.warning('请至少选择两个表进行关联')
      return
    }

    // 生成字段配置数据（编辑模式下会保留现有配置）
    generateFieldConfigData()
    currentStep.value = 2
  } else if (currentStep.value === 2) {
    // 验证数据项名称唯一性
    if (!validateAllDataItemNames()) {
      return
    }

    currentStep.value = 3
    // 开始保存流程
    await saveDataset()
  }
}

const handlePrevious = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

const handleCancel = () => {
  visible.value = false
}

const handleClose = () => {
  // 重置数据
  resetFormData()
}

// 填充表单数据（编辑/详情模式）
const fillFormData = (data: any) => {
  console.log('填充表单数据:', data)
  Object.assign(formData, {
    name: data.name || '',
    type: data.type || '',
    category: data.category || '',
    dataSource: data.dataSource || data.dataSourceName || '',
    tags: data.tags || [],
    description: data.description || ''
  })

  console.log('填充后的表单数据:', formData)

  // 如果有表关联数据，也需要填充
  if (data.tables && data.tables.length > 0) {
    canvasTables.value = data.tables
    // 如果有两个或更多表，重新创建连接线
    if (canvasTables.value.length >= 2) {
      nextTick(() => {
        createConnection()
      })
    }
  }
  if (data.relationConfig) {
    Object.assign(relationConfig, data.relationConfig)
  }
  if (data.fieldConfig && data.fieldConfig.length > 0) {
    fieldConfigData.value = data.fieldConfig
    console.log('回显字段配置数据:', data.fieldConfig.length, '个字段')
  }
  if (data.desensitizationConfig) {
    Object.assign(desensitizationConfig, data.desensitizationConfig)
  }
}

const resetFormData = () => {
  currentStep.value = 1
  Object.assign(formData, {
    name: '',
    type: '',
    category: '',
    dataSource: '',
    tags: [],
    description: ''
  })
  selectedTables.value = []
  canvasTables.value = []
  connections.value = []
  selectedTableForConnection.value = ''
  selectedAnchor.value = null
  relationConfig.joinType = 'inner'
  relationConfig.fields = []
  fieldConfigData.value = []
  Object.assign(desensitizationConfig, {
    nameFields: [],
    idFields: [],
    phoneFields: []
  })
}

// 监听弹窗打开，重新加载数据
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadDataSourceOptions()
    loadDatasetOptions()
    loadMockTableData()

    // 如果是编辑或详情模式，填充数据
    if ((props.mode === 'edit' || props.mode === 'detail') && props.editData) {
      fillFormData(props.editData)
    }
  }
})

// 初始化
onMounted(() => {
  loadDataSourceOptions()
  loadDatasetOptions()
  loadMockTableData()
})

// 加载数据源选项
const loadDataSourceOptions = () => {
  try {
    const cached = localStorage.getItem('dataSourceManagement_data')
    if (cached) {
      const dataSourceList = JSON.parse(cached)
      dataSourceOptions.value = dataSourceList.map((item: any) => ({
        label: item.name,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('加载数据源选项失败:', error)
  }
}

// 加载数据集选项（类型、分类、标签）
const loadDatasetOptions = () => {
  try {
    // 加载数据集类型选项（从数据集类型配置弹窗）
    const typesCached = localStorage.getItem('dataSetTypeConfig')
    if (typesCached) {
      const typesList = JSON.parse(typesCached)
      datasetTypeOptions.value = typesList
        .filter((type: any) => type.isEnabled)
        .map((type: any) => ({
          label: type.typeName,
          value: type.typeName
        }))
    }

    // 加载数据集分类选项（从数据集分类配置弹窗）
    const categoriesCached = localStorage.getItem('dataSetCategoryConfig')
    if (categoriesCached) {
      const categoriesList = JSON.parse(categoriesCached)
      datasetCategoryOptions.value = categoriesList
        .filter((category: any) => category.isEnabled)
        .map((category: any) => ({
          label: category.categoryName,
          value: category.categoryName
        }))
    }

    // 加载数据集标签选项（从数据集标签管理）
    // 尝试多个可能的localStorage key
    let tagsList = []
    const possibleKeys = ['dataSetTagsList', 'dataSetTags', 'dataSetTagsConfig']

    for (const key of possibleKeys) {
      const tagsCached = localStorage.getItem(key)
      if (tagsCached) {
        try {
          tagsList = JSON.parse(tagsCached)
          if (Array.isArray(tagsList) && tagsList.length > 0) {
            break
          }
        } catch (error) {
          console.warn(`解析${key}失败:`, error)
        }
      }
    }

    if (tagsList.length > 0) {
      datasetTagOptions.value = tagsList
        .filter((tag: any) => tag.isActive !== false) // 兼容不同的字段名
        .map((tag: any) => ({
          label: tag.name,
          value: tag.name
        }))
    }

    // 如果没有缓存数据，初始化默认数据
    if (datasetTypeOptions.value.length === 0) {
      initDefaultTypeData()
    }

    if (datasetCategoryOptions.value.length === 0) {
      initDefaultCategoryData()
    }

    if (datasetTagOptions.value.length === 0) {
      initDefaultTagData()
    }
  } catch (error) {
    console.error('加载数据集选项失败:', error)
    // 出错时初始化默认数据
    initDefaultTypeData()
    initDefaultCategoryData()
    initDefaultTagData()
  }
}

// 初始化默认类型数据
const initDefaultTypeData = () => {
  const defaultTypes = [
    {
      id: '1',
      sequence: 1,
      typeName: '结构化数据',
      description: '结构化数据类型',
      isEnabled: true,
      createTime: new Date().toLocaleString()
    },
    {
      id: '2',
      sequence: 2,
      typeName: '半结构化数据',
      description: '半结构化数据类型',
      isEnabled: true,
      createTime: new Date().toLocaleString()
    },
    {
      id: '3',
      sequence: 3,
      typeName: '非结构化数据',
      description: '非结构化数据类型',
      isEnabled: true,
      createTime: new Date().toLocaleString()
    }
  ]

  localStorage.setItem('dataSetTypeConfig', JSON.stringify(defaultTypes))
  datasetTypeOptions.value = defaultTypes.map(type => ({
    label: type.typeName,
    value: type.typeName
  }))
}

// 初始化默认分类数据
const initDefaultCategoryData = () => {
  const defaultCategories = [
    {
      id: '1',
      sequence: 1,
      categoryName: '业务数据',
      description: '业务相关数据分类',
      isEnabled: true,
      createTime: new Date().toLocaleString()
    },
    {
      id: '2',
      sequence: 2,
      categoryName: '日志数据',
      description: '系统日志数据分类',
      isEnabled: true,
      createTime: new Date().toLocaleString()
    },
    {
      id: '3',
      sequence: 3,
      categoryName: '监控数据',
      description: '系统监控数据分类',
      isEnabled: true,
      createTime: new Date().toLocaleString()
    }
  ]

  localStorage.setItem('dataSetCategoryConfig', JSON.stringify(defaultCategories))
  datasetCategoryOptions.value = defaultCategories.map(category => ({
    label: category.categoryName,
    value: category.categoryName
  }))
}

// 初始化默认标签数据
const initDefaultTagData = () => {
  const defaultTags = [
    {
      id: 1,
      name: '重要',
      description: '重要数据标签',
      isActive: true,
      createTime: new Date().toLocaleString()
    },
    {
      id: 2,
      name: '常用',
      description: '常用数据标签',
      isActive: true,
      createTime: new Date().toLocaleString()
    },
    {
      id: 3,
      name: '临时',
      description: '临时数据标签',
      isActive: true,
      createTime: new Date().toLocaleString()
    },
    {
      id: 4,
      name: '核心',
      description: '核心业务数据标签',
      isActive: true,
      createTime: new Date().toLocaleString()
    },
    {
      id: 5,
      name: '敏感',
      description: '敏感数据标签',
      isActive: true,
      createTime: new Date().toLocaleString()
    }
  ]

  // 保存到DataSetTab.vue使用的key
  localStorage.setItem('dataSetTagsList', JSON.stringify(defaultTags))
  datasetTagOptions.value = defaultTags.map(tag => ({
    label: tag.name,
    value: tag.name
  }))
}

// 加载Mock表数据
const loadMockTableData = () => {
  // Mock数据：模拟数据库表和字段
  const mockTables = [
    {
      id: 'user_info',
      name: 'yxq_user_info',
      displayName: '用户基本信息表',
      fields: [
        { name: 'user_id', type: 'varchar', length: 32, comment: '用户ID' },
        { name: 'user_name', type: 'varchar', length: 50, comment: '用户姓名' },
        { name: 'id_card', type: 'varchar', length: 18, comment: '身份证号' },
        { name: 'phone', type: 'varchar', length: 11, comment: '手机号码' },
        { name: 'email', type: 'varchar', length: 100, comment: '邮箱' },
        { name: 'create_time', type: 'timestamp', length: 0, comment: '创建时间' }
      ],
      relatedTables: ['user_address', 'user_order']
    },
    {
      id: 'user_address',
      name: 'yxq_user_address',
      displayName: '用户地址信息表',
      fields: [
        { name: 'address_id', type: 'varchar', length: 32, comment: '地址ID' },
        { name: 'user_id', type: 'varchar', length: 32, comment: '用户ID' },
        { name: 'province', type: 'varchar', length: 50, comment: '省份' },
        { name: 'city', type: 'varchar', length: 50, comment: '城市' },
        { name: 'district', type: 'varchar', length: 50, comment: '区县' },
        { name: 'detail_address', type: 'varchar', length: 200, comment: '详细地址' }
      ],
      relatedTables: ['user_info']
    },
    {
      id: 'user_order',
      name: 'yxq_user_order',
      displayName: '用户订单表',
      fields: [
        { name: 'order_id', type: 'varchar', length: 32, comment: '订单ID' },
        { name: 'user_id', type: 'varchar', length: 32, comment: '用户ID' },
        { name: 'order_amount', type: 'decimal', length: 10, comment: '订单金额' },
        { name: 'order_status', type: 'int', length: 4, comment: '订单状态' },
        { name: 'create_time', type: 'timestamp', length: 0, comment: '创建时间' }
      ],
      relatedTables: ['user_info', 'order_detail']
    },
    {
      id: 'order_detail',
      name: 'yxq_order_detail',
      displayName: '订单详情表',
      fields: [
        { name: 'detail_id', type: 'varchar', length: 32, comment: '详情ID' },
        { name: 'order_id', type: 'varchar', length: 32, comment: '订单ID' },
        { name: 'product_name', type: 'varchar', length: 100, comment: '产品名称' },
        { name: 'quantity', type: 'int', length: 4, comment: '数量' },
        { name: 'unit_price', type: 'decimal', length: 10, comment: '单价' }
      ],
      relatedTables: ['user_order']
    }
  ]

  // 分配主表和关联表
  mainTables.value = mockTables.slice(0, 2)
  relatedTables.value = mockTables.slice(2)
}

// 数据源变化处理
const handleDataSourceChange = (value: string) => {
  // 在编辑模式下，如果数据源没有真正改变，不要重置配置
  if (isEditMode.value && props.editData &&
      (value === props.editData.dataSource || value === props.editData.dataSourceName)) {
    return
  }

  // 重置表选择（仅在新增模式或数据源真正改变时）
  selectedTables.value = []
  canvasTables.value = []
  connections.value = []
  selectedTableForConnection.value = ''
  selectedAnchor.value = null
  relationConfig.joinType = 'inner'
  relationConfig.fields = []
  fieldConfigData.value = []

  // 这里可以根据不同数据源加载不同的表数据
  loadMockTableData()
}

// 表选择切换
const toggleTableSelection = (tableId: string) => {
  const index = selectedTables.value.indexOf(tableId)
  if (index > -1) {
    selectedTables.value.splice(index, 1)
  } else {
    selectedTables.value.push(tableId)
  }
}

// 拖拽开始
const handleDragStart = (event: DragEvent, table: any) => {
  event.dataTransfer?.setData('text/plain', JSON.stringify(table))
}

// 拖拽悬停
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
}

// 拖拽放置
const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  const tableData = JSON.parse(event.dataTransfer?.getData('text/plain') || '{}')

  if (tableData.id && !canvasTables.value.find(t => t.id === tableData.id)) {
    const rect = (event.target as HTMLElement).getBoundingClientRect()
    const x = event.clientX - rect.left - 75 // 表格宽度的一半
    const y = event.clientY - rect.top - 50  // 表格高度的一半

    canvasTables.value.push({
      ...tableData,
      x: Math.max(0, Math.min(x, rect.width - 150)),
      y: Math.max(0, Math.min(y, rect.height - 100))
    })
  }
}

// 从画布移除表
const removeTableFromCanvas = (tableId: string) => {
  const index = canvasTables.value.findIndex(t => t.id === tableId)
  if (index > -1) {
    canvasTables.value.splice(index, 1)
    connections.value = []
    relationConfig.fields = []
    selectedTableForConnection.value = ''
  }
}

// 处理锚点点击
const handleAnchorClick = (tableId: string, position: string) => {
  if (!selectedAnchor.value) {
    // 第一次选择锚点
    selectedAnchor.value = { tableId, position }
    ElMessage.info('请选择第二个锚点进行连接')
  } else if (selectedAnchor.value.tableId === tableId) {
    // 同一个表的锚点，取消选择
    selectedAnchor.value = null
  } else {
    // 不同表的锚点，创建连接
    createConnectionByAnchors(selectedAnchor.value, { tableId, position })
    selectedAnchor.value = null
  }
}

// 选择表进行连接（保留原有功能）
const selectTable = (tableId: string) => {
  if (selectedTableForConnection.value === '') {
    // 第一次选择表
    selectedTableForConnection.value = tableId
    ElMessage.info('请选择第二个表进行连接')
  } else if (selectedTableForConnection.value === tableId) {
    // 取消选择
    selectedTableForConnection.value = ''
  } else {
    // 第二次选择表，创建连接
    createConnection(selectedTableForConnection.value, tableId)
    selectedTableForConnection.value = ''
  }
}

// 通过锚点创建连接
const createConnectionByAnchors = (anchor1: any, anchor2: any) => {
  const table1 = canvasTables.value.find(t => t.id === anchor1.tableId)
  const table2 = canvasTables.value.find(t => t.id === anchor2.tableId)

  if (table1 && table2) {
    const pos1 = getAnchorPosition(table1, anchor1.position)
    const pos2 = getAnchorPosition(table2, anchor2.position)

    connections.value = [{
      x1: pos1.x,
      y1: pos1.y,
      x2: pos2.x,
      y2: pos2.y,
      table1Id: table1.id,
      table2Id: table2.id,
      anchor1: anchor1.position,
      anchor2: anchor2.position
    }]

    ElMessage.success(`已连接 ${table1.displayName} 和 ${table2.displayName}`)
  }
}

// 获取锚点位置
const getAnchorPosition = (table: any, position: string) => {
  const tableWidth = 150
  const tableHeight = 80

  switch (position) {
    case 'top':
      return { x: table.x + tableWidth / 2, y: table.y }
    case 'right':
      return { x: table.x + tableWidth, y: table.y + tableHeight / 2 }
    case 'bottom':
      return { x: table.x + tableWidth / 2, y: table.y + tableHeight }
    case 'left':
      return { x: table.x, y: table.y + tableHeight / 2 }
    default:
      return { x: table.x + tableWidth / 2, y: table.y + tableHeight / 2 }
  }
}

// 创建连接线（保留原有功能）
const createConnection = (table1Id?: string, table2Id?: string) => {
  let table1, table2

  if (table1Id && table2Id) {
    table1 = canvasTables.value.find(t => t.id === table1Id)
    table2 = canvasTables.value.find(t => t.id === table2Id)
  } else if (canvasTables.value.length >= 2) {
    table1 = canvasTables.value[0]
    table2 = canvasTables.value[1]
  }

  if (table1 && table2) {
    connections.value = [{
      x1: table1.x + 75,
      y1: table1.y + 50,
      x2: table2.x + 75,
      y2: table2.y + 50,
      table1Id: table1.id,
      table2Id: table2.id
    }]

    ElMessage.success(`已连接 ${table1.displayName} 和 ${table2.displayName}`)
  }
}

// 显示关联配置
const showRelationConfig = (connection: any) => {
  // 这里可以显示关联配置的详细弹窗
  ElMessage.info('点击连接线配置关联关系')
}

// 添加关联字段
const addRelationField = () => {
  relationConfig.fields.push({
    leftField: '',
    rightField: ''
  })
}

// 移除关联字段
const removeRelationField = (index: number) => {
  relationConfig.fields.splice(index, 1)
}

// 生成字段配置数据
const generateFieldConfigData = () => {
  // 在编辑模式下，如果已经有字段配置数据，不重新生成
  if (isEditMode.value && fieldConfigData.value.length > 0) {
    console.log('编辑模式：保留现有字段配置，不重新生成')
    return
  }

  const allFields = []

  canvasTables.value.forEach(table => {
    table.fields.forEach(field => {
      allFields.push({
        physicalName: field.name,
        fieldType: field.type,
        length: field.length,
        notNull: false,
        dataItemName: field.comment || field.name,
        encrypted: false,
        visible: true,
        tableName: table.name
      })
    })
  })

  // 在编辑模式下，智能合并现有配置和新字段
  if (isEditMode.value && fieldConfigData.value.length > 0) {
    console.log('编辑模式：智能合并字段配置')

    // 创建现有配置的映射（以 tableName + physicalName 为key）
    const existingConfigMap = new Map()
    fieldConfigData.value.forEach(config => {
      const key = `${config.tableName}.${config.physicalName}`
      existingConfigMap.set(key, config)
    })

    // 合并配置：保留现有配置，添加新字段
    const mergedFields = allFields.map(newField => {
      const key = `${newField.tableName}.${newField.physicalName}`
      const existingConfig = existingConfigMap.get(key)

      if (existingConfig) {
        // 保留用户的配置，但更新基础字段信息
        return {
          ...existingConfig,
          fieldType: newField.fieldType,
          length: newField.length
        }
      } else {
        // 新字段，使用默认配置
        return newField
      }
    })

    fieldConfigData.value = mergedFields
    console.log('合并后的字段配置:', mergedFields.length, '个字段')
  } else {
    // 新增模式或没有现有配置，直接使用新生成的配置
    fieldConfigData.value = allFields
    console.log('生成新的字段配置:', allFields.length, '个字段')
  }
}

// 切换字段可见性
const toggleFieldVisibility = (row: any) => {
  row.visible = !row.visible
}

// 验证数据项名称
const validateDataItemName = (row: any) => {
  const duplicates = fieldConfigData.value.filter(item =>
    item.dataItemName === row.dataItemName && item !== row
  )

  if (duplicates.length > 0) {
    ElMessage.warning('数据项名称不能重复')
    row.dataItemName = ''
  }
}

// 删除字段
const deleteField = (row: any, index: number) => {
  ElMessageBox.confirm(
    `确定要删除字段"${row.physicalName}"吗？删除后将无法恢复。`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    fieldConfigData.value.splice(index, 1)
    ElMessage.success('字段删除成功')
    console.log('删除字段:', row.physicalName, '剩余字段数量:', fieldConfigData.value.length)
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 验证所有数据项名称
const validateAllDataItemNames = () => {
  const names = fieldConfigData.value.map(item => item.dataItemName).filter(name => name)
  const uniqueNames = new Set(names)

  if (names.length !== uniqueNames.size) {
    ElMessage.error('存在重复的数据项名称，请检查')
    return false
  }

  const emptyNames = fieldConfigData.value.filter(item => !item.dataItemName)
  if (emptyNames.length > 0) {
    ElMessage.error('请为所有字段设置数据项名称')
    return false
  }

  return true
}

// 保存数据集
const saveDataset = async () => {
  loading.value = true

  try {
    // 模拟保存过程
    await new Promise(resolve => setTimeout(resolve, 3000))

    // 构建数据集数据（确保字段名与DataSet接口一致）
    const datasetData = {
      id: isEditMode.value ? props.editData.id : Date.now().toString(),
      name: formData.name,
      type: formData.type,
      category: formData.category,
      description: formData.description,
      status: isEditMode.value ? props.editData.status : true,
      createTime: isEditMode.value ? props.editData.createTime : new Date().toLocaleString(),
      createUser: isEditMode.value ? props.editData.createUser : '当前用户',
      dataSize: isEditMode.value ? props.editData.dataSize : '0 MB',
      recordCount: isEditMode.value ? props.editData.recordCount : 0,
      lastUpdateTime: new Date().toLocaleString(),
      dataSourceName: formData.dataSource,
      dataSourceType: '数据库',
      // 额外的字段用于向导功能
      dataSource: formData.dataSource, // 保留用于向导内部使用
      tags: formData.tags,
      tables: canvasTables.value,
      relationConfig: relationConfig,
      fieldConfig: fieldConfigData.value,
      desensitizationConfig: desensitizationConfig,
      updateTime: isEditMode.value ? new Date().toLocaleString() : undefined
    }

    console.log('保存数据集数据:', datasetData)
    console.log('表单数据:', formData)

    // 保存到localStorage（使用与DataSetTab.vue一致的key）
    const existingDatasets = JSON.parse(localStorage.getItem('dataSetManagement_data') || '[]')

    if (isEditMode.value) {
      // 编辑模式：更新现有数据
      const index = existingDatasets.findIndex((item: any) => item.id === props.editData.id)
      if (index !== -1) {
        existingDatasets[index] = datasetData
      }
    } else {
      // 新增模式：添加新数据
      existingDatasets.push(datasetData)
    }

    localStorage.setItem('dataSetManagement_data', JSON.stringify(existingDatasets))

    ElMessage.success(isEditMode.value ? '数据集更新成功' : '数据集创建成功')
    emit('success')
    visible.value = false
  } catch (error) {
    ElMessage.error('保存失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.wizard-steps {
  margin-bottom: 30px;
  
  .steps-container {
    display: flex;
    justify-content: center;
    align-items: center;
    
    .step-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 0 40px;
      
      .step-number {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: #e4e7ed;
        color: #909399;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-bottom: 8px;
        transition: all 0.3s;
      }
      
      .step-title {
        font-size: 14px;
        color: #909399;
        transition: all 0.3s;
      }
      
      &.active {
        .step-number {
          background-color: #409eff;
          color: white;
        }
        
        .step-title {
          color: #409eff;
          font-weight: bold;
        }
      }
      
      &.completed {
        .step-number {
          background-color: #67c23a;
          color: white;
        }
        
        .step-title {
          color: #67c23a;
        }
      }
    }
  }
}

.wizard-content {
  min-height: 500px;
  
  .step-content {
    .section-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 20px;
      color: #303133;
      border-left: 4px solid #409eff;
      padding-left: 10px;
    }
    
    .form-section {
      margin-bottom: 30px;
    }
    
    .table-relation-section {
      .relation-container {
        display: flex;
        gap: 20px;
        margin-bottom: 30px;
        
        .table-list-container {
          width: 300px;
          
          .table-list-section {
            margin-bottom: 20px;
            
            h4 {
              margin-bottom: 10px;
              color: #606266;
            }
            
            .table-list {
              border: 1px solid #dcdfe6;
              border-radius: 4px;
              max-height: 200px;
              overflow-y: auto;
              
              .table-item {
                padding: 10px;
                border-bottom: 1px solid #ebeef5;
                cursor: pointer;
                transition: all 0.3s;
                
                &:hover {
                  background-color: #f5f7fa;
                }
                
                &.selected {
                  background-color: #ecf5ff;
                  border-color: #409eff;
                }
                
                .table-name {
                  font-weight: bold;
                  color: #303133;
                }
                
                .table-info {
                  font-size: 12px;
                  color: #909399;
                  margin-top: 4px;
                }
              }
            }
          }
        }
        
        .canvas-container {
          flex: 1;
          
          h4 {
            margin-bottom: 10px;
            color: #606266;
          }

          .canvas-instructions {
            margin-bottom: 10px;
          }

          .relation-canvas {
            position: relative;
            border: 2px dashed #dcdfe6;
            border-radius: 4px;
            height: 400px;
            background-color: #fafafa;
            
            .canvas-hint {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              color: #909399;
              font-size: 14px;
            }
            
            .canvas-table {
              position: absolute;
              width: 150px;
              height: 80px;
              border: 1px solid #409eff;
              border-radius: 4px;
              background-color: white;
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
              cursor: pointer;
              transition: all 0.3s;

              &:hover {
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                transform: translateY(-2px);

                .anchor-point {
                  opacity: 1;
                }
              }

              &.selected {
                border-color: #67c23a;
                box-shadow: 0 0 10px rgba(103, 194, 58, 0.3);

                .table-header {
                  background-color: #67c23a;
                }
              }

              .anchor-point {
                position: absolute;
                width: 8px;
                height: 8px;
                background-color: #409eff;
                border: 2px solid white;
                border-radius: 50%;
                cursor: pointer;
                opacity: 0;
                transition: all 0.3s;
                z-index: 10;

                &:hover {
                  background-color: #67c23a;
                  transform: scale(1.2);
                }

                &.anchor-top {
                  top: -6px;
                  left: 50%;
                  transform: translateX(-50%);
                }

                &.anchor-right {
                  top: 50%;
                  right: -6px;
                  transform: translateY(-50%);
                }

                &.anchor-bottom {
                  bottom: -6px;
                  left: 50%;
                  transform: translateX(-50%);
                }

                &.anchor-left {
                  top: 50%;
                  left: -6px;
                  transform: translateY(-50%);
                }
              }

              .table-header {
                background-color: #409eff;
                color: white;
                padding: 8px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 12px;
                font-weight: bold;
                height: 32px;
                box-sizing: border-box;
              }

              .table-info {
                padding: 8px;
                height: 48px;
                box-sizing: border-box;

                .table-name {
                  font-size: 11px;
                  color: #606266;
                  margin-bottom: 4px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }

                .field-count {
                  font-size: 10px;
                  color: #909399;
                }
              }
            }
            
            .connection-lines {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              pointer-events: none;
              
              .connection-icon {
                pointer-events: all;
                cursor: pointer;
              }
            }
          }
        }
      }
      
      .relation-config-section {
        .relation-config {
          .join-type-section {
            margin-bottom: 20px;

            .join-type-row {
              display: flex;
              align-items: center;

              label {
                font-weight: bold;
                margin-right: 10px;
              }
            }
          }

          .relation-fields-section {
            .relation-tables-info {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              margin-bottom: 15px;

              .table-info-item {
                padding: 10px 20px;
                background-color: #f5f7fa;
                border-radius: 4px;
              }

              .relation-symbol {
                margin: 0 20px;
                font-size: 18px;
                color: #409eff;
              }
            }

            .relation-fields-list {
              .relation-field-item {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 10px;
                justify-content: flex-start;

                .relation-operator {
                  font-weight: bold;
                  color: #409eff;
                }
              }
            }
          }
        }
      }
    }
    
    .data-config-section {
      .field-config-table {
        margin-bottom: 30px;
      }
      
      .desensitization-section {
        .section-title {
          margin-top: 30px;
        }
      }
    }
    
    .save-design-section {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 300px;
      
      .save-animation {
        text-align: center;
        
        .loading-icon {
          font-size: 48px;
          color: #409eff;
          animation: rotate 2s linear infinite;
          margin-bottom: 20px;
        }
        
        h3 {
          color: #303133;
          margin-bottom: 10px;
        }
        
        p {
          color: #909399;
        }
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px 0 0 0;
}
</style>
