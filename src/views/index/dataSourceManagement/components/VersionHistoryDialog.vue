<template>
  <Dialog
    v-model="visible"
    title="版本历史记录"
    width="1000px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div style="padding: 20px;">
      <!-- 查询条件 -->
      <div style="margin-bottom: 20px;">
        <el-form :model="searchForm" inline>
          <el-form-item label="版本名称">
            <el-input
              v-model="searchForm.versionName"
              placeholder="请输入版本名称"
              style="width: 200px;"
              clearable
            />
          </el-form-item>
          <el-form-item label="操作时间">
            <el-date-picker
              v-model="searchForm.operationTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 350px;"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 版本历史列表 -->
      <div>
        <TableV2
          :defaultTableData="getFilteredVersionHistory()"
          :columns="[
            { prop: 'versionNumber', label: '版本号', width: 120 },
            { prop: 'updateTime', label: '更新时间', width: 180 },
            { prop: 'updateUser', label: '更新人', width: 120 },
            { prop: 'updateDescription', label: '更新描述' }
          ]"
          :enable-toolbar="false"
          :enable-own-button="false"
          :enable-selection="false"
          height="400"
        />
      </div>
    </div>

    <template #footer>
      <div style="text-align: right;">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 定义组件属性
interface Props {
  modelValue: boolean
  dataSourceList?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  dataSourceList: () => []
})

// 定义事件
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 搜索表单
const searchForm = ref({
  versionName: '',
  operationTime: null as [string, string] | null
})

// 版本历史数据
const versionHistoryData = ref([
  {
    sequence: 1,
    versionNumber: 'v1.0.0',
    updateTime: '2024-01-15 09:30:00',
    updateUser: '张三',
    updateDescription: '创建了数据源：MySQL客户数据库'
  },
  {
    sequence: 2,
    versionNumber: 'v1.0.1',
    updateTime: '2024-01-16 14:20:00',
    updateUser: '张三',
    updateDescription: '更新了数据源描述'
  },
  {
    sequence: 3,
    versionNumber: 'v1.1.0',
    updateTime: '2024-02-20 14:15:00',
    updateUser: '李四',
    updateDescription: '创建了数据源：Oracle财务数据仓库'
  },
  {
    sequence: 4,
    versionNumber: 'v1.1.1',
    updateTime: '2024-02-21 10:30:00',
    updateUser: '李四',
    updateDescription: '更新了数据库连接配置'
  },
  {
    sequence: 5,
    versionNumber: 'v1.2.0',
    updateTime: '2024-03-10 16:20:00',
    updateUser: '王五',
    updateDescription: '创建了数据源：SQL Server业务分析库'
  },
  {
    sequence: 6,
    versionNumber: 'v1.2.1',
    updateTime: '2024-03-11 09:45:00',
    updateUser: '王五',
    updateDescription: '更新了数据源版本信息'
  },
  {
    sequence: 7,
    versionNumber: 'v1.3.0',
    updateTime: '2024-04-15 10:45:00',
    updateUser: '赵六',
    updateDescription: '创建了数据源：达梦政务数据库'
  },
  {
    sequence: 8,
    versionNumber: 'v1.3.1',
    updateTime: '2024-04-16 15:20:00',
    updateUser: '赵六',
    updateDescription: '更新了数据源描述和认证类型'
  },
  {
    sequence: 9,
    versionNumber: 'v1.4.0',
    updateTime: '2024-05-20 13:30:00',
    updateUser: '孙七',
    updateDescription: '创建了数据源：外部API数据源'
  },
  {
    sequence: 10,
    versionNumber: 'v1.5.0',
    updateTime: '2024-06-01 11:15:00',
    updateUser: '周八',
    updateDescription: '创建了数据源：MongoDB文档数据库'
  },
  {
    sequence: 11,
    versionNumber: 'v1.6.0',
    updateTime: '2024-06-15 15:45:00',
    updateUser: '吴九',
    updateDescription: '创建了数据源：Redis缓存数据库'
  },
  {
    sequence: 12,
    versionNumber: 'v1.7.0',
    updateTime: '2024-07-01 09:20:00',
    updateUser: '郑十',
    updateDescription: '创建了数据源：Elasticsearch搜索引擎'
  },
  {
    sequence: 13,
    versionNumber: 'v1.7.1',
    updateTime: '2024-07-15 14:30:00',
    updateUser: '张三',
    updateDescription: '更新了MySQL客户数据库的端口配置'
  },
  {
    sequence: 14,
    versionNumber: 'v1.7.2',
    updateTime: '2024-07-20 16:45:00',
    updateUser: '李四',
    updateDescription: '更新了Oracle财务数据仓库的数据库名称'
  },
  {
    sequence: 15,
    versionNumber: 'v1.7.3',
    updateTime: '2024-07-25 11:20:00',
    updateUser: '王五',
    updateDescription: '更新了SQL Server业务分析库的用户名和密码'
  }
])

// 获取过滤后的版本历史数据
const getFilteredVersionHistory = () => {
  let filteredData = [...versionHistoryData.value]

  // 按版本名称过滤
  if (searchForm.value.versionName) {
    filteredData = filteredData.filter(item =>
      item.versionNumber.toLowerCase().includes(searchForm.value.versionName.toLowerCase())
    )
  }

  // 按操作时间过滤
  if (searchForm.value.operationTime && searchForm.value.operationTime.length === 2) {
    const [startTime, endTime] = searchForm.value.operationTime
    filteredData = filteredData.filter(item => {
      const itemTime = new Date(item.updateTime).getTime()
      const start = new Date(startTime).getTime()
      const end = new Date(endTime).getTime()
      return itemTime >= start && itemTime <= end
    })
  }

  return filteredData
}

// 查询
const handleSearch = () => {
  // 触发表格重新渲染
  console.log('查询版本历史', searchForm.value)
}

// 重置
const handleReset = () => {
  searchForm.value = {
    versionName: '',
    operationTime: null
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 添加版本历史记录的方法（供外部调用）
const addVersionHistory = (description: string, user: string = '当前用户') => {
  const newVersion = {
    sequence: versionHistoryData.value.length + 1,
    versionNumber: `v1.7.${versionHistoryData.value.length - 11}`,
    updateTime: new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).replace(/\//g, '-'),
    updateUser: user,
    updateDescription: description
  }
  versionHistoryData.value.push(newVersion)
}

// 暴露方法给父组件
defineExpose({
  addVersionHistory
})
</script>

<style scoped>
.el-form-item {
  margin-bottom: 10px;
}
</style>
