<!-- 清洗任务表单弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="清洗任务"
    width="500px"
    :destroy-on-close="true"
    :loading="loading"
    loading-text="保存中"
    @closed="resetForm"
    @click-confirm="onConfirm"
    @click-cancel="handleClose"
  >
    <div class="cleaning-task-form-content" style="padding: 20px; min-height: 400px;">
      <div class="form-item">
        <label style="display: block; margin-bottom: 8px;">任务名称</label>
        <el-input
          v-model="form.taskName"
          placeholder="请输入"
          style="width: 100%;"
        />
      </div>
      
      <div class="form-item" style="margin-top: 15px;">
        <label style="display: block; margin-bottom: 8px;">任务描述</label>
        <el-input
          v-model="form.taskDescription"
          placeholder="请输入"
          style="width: 100%;"
        />
      </div>
      
      <div class="form-item" style="margin-top: 15px;">
        <label style="display: block; margin-bottom: 8px;">关联规则</label>
        <el-select
          v-model="form.associatedRule"
          placeholder="请选择 (从清洗规则配置中选择)"
          style="width: 100%;"
        >
          <el-option
            v-for="rule in cleaningRules"
            :key="rule.index"
            :label="rule.ruleName"
            :value="rule.ruleName"
          />
        </el-select>
      </div>
      
      <div class="form-item" style="margin-top: 15px;">
        <label style="display: block; margin-bottom: 8px;">执行时间</label>
        <el-date-picker
          v-model="form.executionTime"
          type="datetime"
          placeholder="请选择 (年月日时分秒)"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%;"
        />
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'


// Props
const props = defineProps<{
  modelValue: boolean
  editData?: any
  cleaningRules: any[]
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirm: [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)

// 表单数据
const form = ref({
  taskName: '',
  taskDescription: '',
  associatedRule: '',
  executionTime: ''
})

// 验证必填项
const validateRequiredFields = () => {
  const errors = []
  
  if (!form.value.taskName.trim()) {
    errors.push('任务名称不能为空')
  }
  
  if (!form.value.associatedRule) {
    errors.push('关联规则不能为空')
  }
  
  if (!form.value.executionTime) {
    errors.push('执行时间不能为空')
  }
  
  return errors
}

// 重置表单
const resetForm = () => {
  form.value = {
    taskName: '',
    taskDescription: '',
    associatedRule: '',
    executionTime: ''
  }
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 确认保存
const onConfirm = () => {
  // 验证必填项
  const errors = validateRequiredFields()
  if (errors.length > 0) {
    ElMessage.error(`请完善以下信息：\n${errors.join('\n')}`)
    return
  }

  loading.value = true
  setTimeout(() => {
    const taskData = {
      taskName: form.value.taskName,
      taskDescription: form.value.taskDescription,
      associatedRule: form.value.associatedRule,
      executionTime: form.value.executionTime,
      lastExecutionTime: '暂无'
    }

    emit('confirm', taskData)
    ElMessage.success(props.editData ? '编辑清洗任务成功' : '新增清洗任务成功')
    visible.value = false
    loading.value = false
  }, 1000)
}

// 监听编辑数据变化
watch(() => props.editData, (newVal) => {
  if (newVal) {
    form.value = {
      taskName: newVal.taskName || '',
      taskDescription: newVal.taskDescription || '',
      associatedRule: newVal.associatedRule || '',
      executionTime: newVal.executionTime || ''
    }
  } else {
    resetForm()
  }
}, { immediate: true })

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal && !props.editData) {
    resetForm()
  }
})
</script>

<style lang="scss" scoped>
.cleaning-task-form-content {
  .form-item {
    margin-bottom: 15px;
    
    label {
      font-size: 14px;
      color: #333;
      font-weight: 400;
    }
  }
}
</style>
