<template>
  <Dialog
    v-model="visible"
    title="数据源血缘分析"
    width="1200px"
    :destroy-on-close="true"
    :visible-confirm-button="false"
    cancel-text="关闭"
    @click-cancel="visible = false"
  >
    <div style="padding: 20px; min-height: 600px;">
      <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 20px;">
        <div style="display: flex; align-items: center; gap: 8px;">
          <span>数据源名称</span>
          <el-select v-model="bloodlineAnalysisForm.dataSourceName" placeholder="请选择" style="width: 200px;">
            <el-option
              v-for="item in dataSourceList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </div>
      </div>

      <div style="margin-bottom: 20px;">
        <div style="font-weight: bold; margin-bottom: 10px;">分析范围设置</div>
        <div style="display: flex; gap: 20px;">
          <div>
            <span style="margin-right: 8px;">血缘关系深度</span>
            <el-slider v-model="bloodlineAnalysisForm.depth" :min="1" :max="5" style="width: 200px;" />
          </div>
          <div style="display: flex; gap: 20px;">
            <div>
              <span>上游</span>
              <el-select v-model="bloodlineAnalysisForm.upstream" placeholder="请选择 (1/2/3)" style="width: 120px;">
                <el-option label="1级" value="1" />
                <el-option label="2级" value="2" />
                <el-option label="3级" value="3" />
              </el-select>
            </div>
            <div>
              <span>下游</span>
              <el-select v-model="bloodlineAnalysisForm.downstream" placeholder="请选择 (1/2/3)" style="width: 120px;">
                <el-option label="1级" value="1" />
                <el-option label="2级" value="2" />
                <el-option label="3级" value="3" />
              </el-select>
            </div>
          </div>
        </div>
      </div>

      <div style="margin-bottom: 20px;">
        <div style="font-weight: bold; margin-bottom: 10px;">关系图可视化</div>
        <div style="position: relative; border: 1px solid #ddd; height: 400px; background: #f9f9f9;">
          <canvas
            ref="lineageCanvas"
            width="800"
            height="400"
            style="width: 100%; height: 100%; cursor: grab;"
            @mousedown="handleCanvasMouseDown"
            @mousemove="handleCanvasMouseMove"
            @mouseup="handleCanvasMouseUp"
            @wheel="handleCanvasWheel"
          ></canvas>

          <div style="position: absolute; top: 10px; right: 10px; display: flex; gap: 10px;">
            <el-button size="small" @click="zoomIn">放大</el-button>
            <el-button size="small" @click="zoomOut">缩小</el-button>
            <el-button size="small" @click="resetView">重置</el-button>
          </div>
        </div>
      </div>

      <div style="margin-bottom: 20px;">
        <div style="font-weight: bold; margin-bottom: 10px;">影响分析结果</div>
        <div style="display: flex; gap: 30px;">
          <div style="text-align: center; padding: 20px; background: #f0f9ff; border-radius: 8px; min-width: 120px;">
            <div style="font-size: 24px; font-weight: bold; color: #409EFF;">{{ analysisResults.directImpact }}</div>
            <div style="color: #666; margin-top: 5px;">直接影响的对象</div>
            <div style="color: #67C23A; font-size: 12px; margin-top: 2px;">↑ 较上次分析增加 {{ analysisResults.directImpactChange }} 个</div>
          </div>
          <div style="text-align: center; padding: 20px; background: #f0f9ff; border-radius: 8px; min-width: 120px;">
            <div style="font-size: 24px; font-weight: bold; color: #409EFF;">{{ analysisResults.indirectImpact }}</div>
            <div style="color: #666; margin-top: 5px;">间接影响的对象</div>
            <div style="color: #67C23A; font-size: 12px; margin-top: 2px;">↑ 较上次分析增加 {{ analysisResults.indirectImpactChange }} 个</div>
          </div>
          <div style="text-align: center; padding: 20px; background: #fff2f0; border-radius: 8px; min-width: 120px;">
            <div style="font-size: 24px; font-weight: bold; color: #F56C6C;">{{ analysisResults.securityImpact }}</div>
            <div style="color: #666; margin-top: 5px;">安全影响业务流程</div>
            <div style="color: #F56C6C; font-size: 12px; margin-top: 2px;">● 包含 {{ analysisResults.coreProcesses }} 个核心流程</div>
          </div>
        </div>
      </div>


    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'

// Props
interface Props {
  modelValue: boolean
  dataSourceList?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  dataSourceList: () => []
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = ref(false)
const lineageCanvas = ref<HTMLCanvasElement>()

// 数据源列表 - 使用父组件传递的数据或默认数据
const dataSourceList = computed(() => {
  if (props.dataSourceList && props.dataSourceList.length > 0) {
    return props.dataSourceList.map(ds => ({
      id: parseInt(ds.id),
      name: ds.name,
      type: ds.sourceType || ds.type || 'Unknown'
    }))
  }

  // 默认数据源列表（如果父组件没有传递数据）
  return [
    { id: 1, name: 'MySQL客户数据库', type: 'MySQL' },
    { id: 2, name: 'Oracle财务数据仓库', type: 'Oracle' },
    { id: 3, name: 'SQL Server业务分析库', type: 'SQL Server' },
    { id: 4, name: '达梦政务数据库', type: '达梦' },
    { id: 5, name: '外部API数据源', type: 'API' },
    { id: 6, name: 'MongoDB文档数据库', type: 'MongoDB' },
    { id: 7, name: 'Redis缓存数据库', type: 'Redis' },
    { id: 8, name: 'Elasticsearch搜索引擎', type: 'Elasticsearch' }
  ]
})

// 血缘分析表单
const bloodlineAnalysisForm = ref({
  dataSourceName: 1, // 默认选中第一个数据源
  depth: 3,
  upstream: '1',
  downstream: '1'
})

// Canvas状态
const canvasState = ref({
  scale: 1,
  offsetX: 0,
  offsetY: 0,
  isDragging: false,
  lastMouseX: 0,
  lastMouseY: 0
})

// 影响分析结果数据
const analysisResults = ref({
  directImpact: 15,
  directImpactChange: 3,
  indirectImpact: 42,
  indirectImpactChange: 6,
  securityImpact: 10,
  coreProcesses: 3
})

// 模拟不同数据源的分析结果
const getAnalysisResultsByDataSource = (dataSourceId) => {
  const results = {
    1: { directImpact: 15, directImpactChange: 3, indirectImpact: 42, indirectImpactChange: 6, securityImpact: 10, coreProcesses: 3 },
    2: { directImpact: 22, directImpactChange: 5, indirectImpact: 58, indirectImpactChange: 8, securityImpact: 15, coreProcesses: 5 },
    3: { directImpact: 18, directImpactChange: 2, indirectImpact: 35, indirectImpactChange: 4, securityImpact: 8, coreProcesses: 2 },
    4: { directImpact: 12, directImpactChange: 1, indirectImpact: 28, indirectImpactChange: 3, securityImpact: 12, coreProcesses: 4 },
    5: { directImpact: 8, directImpactChange: 2, indirectImpact: 20, indirectImpactChange: 5, securityImpact: 6, coreProcesses: 1 },
    6: { directImpact: 14, directImpactChange: 4, indirectImpact: 32, indirectImpactChange: 7, securityImpact: 7, coreProcesses: 2 },
    7: { directImpact: 25, directImpactChange: 6, indirectImpact: 48, indirectImpactChange: 9, securityImpact: 5, coreProcesses: 1 },
    8: { directImpact: 20, directImpactChange: 3, indirectImpact: 45, indirectImpactChange: 6, securityImpact: 9, coreProcesses: 3 }
  }
  return results[dataSourceId] || results[1]
}



// 获取不同数据源的节点数据
const getNodesByDataSource = (dataSourceId) => {
  const dataSource = dataSourceList.value.find(ds => ds.id === dataSourceId)
  const dataSourceName = dataSource?.name || 'MySQL客户数据库'

  const nodeConfigs = {
    1: { // MySQL客户数据库
      current: { label: 'MySQL客户数据库', detail: '主要业务数据库' },
      upstream: ['外部API数据源', '文件系统数据源'],
      downstream: ['数据仓库系统', '业务报表系统', '实时分析平台']
    },
    2: { // Oracle财务数据仓库
      current: { label: 'Oracle财务数据仓库', detail: '企业财务核心数据库' },
      upstream: ['ERP系统', '财务管理系统'],
      downstream: ['财务报表系统', '成本分析平台', '预算管理系统']
    },
    3: { // SQL Server业务分析库
      current: { label: 'SQL Server业务分析库', detail: '业务智能分析数据库' },
      upstream: ['业务系统数据库', '日志收集系统'],
      downstream: ['BI报表平台', '数据挖掘系统', '决策支持系统']
    },
    4: { // 达梦政务数据库
      current: { label: '达梦政务数据库', detail: '政务系统专用数据库' },
      upstream: ['政务办公系统', '公共服务平台'],
      downstream: ['政务报表系统', '数据开放平台', '监管分析系统']
    },
    5: { // 外部API数据源
      current: { label: '外部API数据源', detail: '第三方API接口数据源' },
      upstream: ['合作伙伴API', '公共数据API'],
      downstream: ['数据集成平台', '实时数据处理', '业务应用系统']
    },
    6: { // MongoDB文档数据库
      current: { label: 'MongoDB文档数据库', detail: '非关系型文档数据库' },
      upstream: ['用户行为系统', '内容管理系统'],
      downstream: ['搜索引擎', '推荐系统', '内容分析平台']
    },
    7: { // Redis缓存数据库
      current: { label: 'Redis缓存数据库', detail: '高性能内存数据库' },
      upstream: ['应用服务器', '数据库系统'],
      downstream: ['Web应用', '移动应用', '实时计算平台']
    },
    8: { // Elasticsearch搜索引擎
      current: { label: 'Elasticsearch搜索引擎', detail: '分布式搜索和分析引擎' },
      upstream: ['日志系统', '数据采集器'],
      downstream: ['搜索服务', '日志分析平台', '监控告警系统']
    }
  }

  const config = nodeConfigs[dataSourceId] || nodeConfigs[1]

  return [
    { id: 'upstream1', x: 50, y: 100, width: 120, height: 60, label: config.upstream[0], sublabel: '上游数据源', color: '#E6F7FF', borderColor: '#1890FF' },
    { id: 'upstream2', x: 50, y: 200, width: 120, height: 60, label: config.upstream[1], sublabel: '上游数据源', color: '#E6F7FF', borderColor: '#1890FF' },
    { id: 'current', x: 300, y: 150, width: 140, height: 80, label: config.current.label, sublabel: '当前分析对象', detail: config.current.detail, color: '#FFF2E8', borderColor: '#FA8C16' },
    { id: 'downstream1', x: 550, y: 80, width: 120, height: 60, label: config.downstream[0], sublabel: '下游系统', color: '#F6FFED', borderColor: '#52C41A' },
    { id: 'downstream2', x: 550, y: 170, width: 120, height: 60, label: config.downstream[1], sublabel: '下游应用', color: '#F6FFED', borderColor: '#52C41A' },
    { id: 'downstream3', x: 550, y: 260, width: 120, height: 60, label: config.downstream[2], sublabel: '下游应用', color: '#F6FFED', borderColor: '#52C41A' },
    { id: 'business', x: 750, y: 170, width: 120, height: 60, label: '机器学习平台', sublabel: '核心业务', color: '#FFF1F0', borderColor: '#FF4D4F' }
  ]
}

// 节点数据 - 响应式
const nodes = ref(getNodesByDataSource(bloodlineAnalysisForm.value.dataSourceName))

// 连接线数据
const connections = [
  { from: 'upstream1', to: 'current' },
  { from: 'upstream2', to: 'current' },
  { from: 'current', to: 'downstream1' },
  { from: 'current', to: 'downstream2' },
  { from: 'current', to: 'downstream3' },
  { from: 'downstream1', to: 'business' },
  { from: 'downstream2', to: 'business' }
]

// 监听数据源切换
watch(() => bloodlineAnalysisForm.value.dataSourceName, (newDataSourceId) => {
  // 更新分析结果
  analysisResults.value = getAnalysisResultsByDataSource(newDataSourceId)
  // 更新节点数据
  nodes.value = getNodesByDataSource(newDataSourceId)
  // 重新绘制Canvas
  nextTick(() => {
    if (visible.value) {
      initLineageCanvas()
    }
  })
  console.log(`切换到数据源: ${dataSourceList.value.find(ds => ds.id === newDataSourceId)?.name}`)
  console.log('更新分析结果:', analysisResults.value)
})

// 监听visible变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    nextTick(() => {
      initLineageCanvas()
    })
  }
})

watch(() => visible.value, (newVal) => {
  emit('update:modelValue', newVal)
})

// 初始化时设置默认分析结果
analysisResults.value = getAnalysisResultsByDataSource(bloodlineAnalysisForm.value.dataSourceName)

// 初始化Canvas
const initLineageCanvas = () => {
  if (!lineageCanvas.value) return

  const canvas = lineageCanvas.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return

  // 设置高DPI支持
  const dpr = window.devicePixelRatio || 1
  const rect = canvas.getBoundingClientRect()
  canvas.width = rect.width * dpr
  canvas.height = rect.height * dpr
  ctx.scale(dpr, dpr)

  drawLineageChart(ctx)
}

// 绘制血缘关系图
const drawLineageChart = (ctx: CanvasRenderingContext2D) => {
  const { scale, offsetX, offsetY } = canvasState.value

  // 清空画布
  ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height)

  // 应用变换
  ctx.save()
  ctx.translate(offsetX, offsetY)
  ctx.scale(scale, scale)

  // 绘制连接线
  ctx.strokeStyle = '#d9d9d9'
  ctx.lineWidth = 2
  connections.forEach(conn => {
    const fromNode = nodes.value.find(n => n.id === conn.from)
    const toNode = nodes.value.find(n => n.id === conn.to)
    if (fromNode && toNode) {
      ctx.beginPath()
      ctx.moveTo(fromNode.x + fromNode.width, fromNode.y + fromNode.height / 2)
      ctx.lineTo(toNode.x, toNode.y + toNode.height / 2)
      ctx.stroke()

      // 绘制箭头
      const arrowX = toNode.x
      const arrowY = toNode.y + toNode.height / 2
      ctx.beginPath()
      ctx.moveTo(arrowX, arrowY)
      ctx.lineTo(arrowX - 10, arrowY - 5)
      ctx.lineTo(arrowX - 10, arrowY + 5)
      ctx.closePath()
      ctx.fillStyle = '#d9d9d9'
      ctx.fill()
    }
  })

  // 绘制节点
  nodes.value.forEach(node => {
    // 绘制节点背景
    ctx.fillStyle = node.color
    ctx.strokeStyle = node.borderColor
    ctx.lineWidth = 2
    ctx.fillRect(node.x, node.y, node.width, node.height)
    ctx.strokeRect(node.x, node.y, node.width, node.height)

    // 绘制节点文本
    ctx.fillStyle = '#333'
    ctx.font = '12px Arial'
    ctx.textAlign = 'center'
    
    // 主标签
    ctx.fillText(node.label, node.x + node.width / 2, node.y + node.height / 2 - 5)
    
    // 副标签
    ctx.fillStyle = '#666'
    ctx.font = '10px Arial'
    ctx.fillText(node.sublabel, node.x + node.width / 2, node.y + node.height / 2 + 10)
    
    // 详情信息（如果有）
    if (node.detail) {
      ctx.fillStyle = '#999'
      ctx.font = '9px Arial'
      ctx.fillText(node.detail, node.x + node.width / 2, node.y + node.height / 2 + 22)
    }
  })

  ctx.restore()
}

// 鼠标事件处理
const handleCanvasMouseDown = (e: MouseEvent) => {
  canvasState.value.isDragging = true
  canvasState.value.lastMouseX = e.clientX
  canvasState.value.lastMouseY = e.clientY
  if (lineageCanvas.value) {
    lineageCanvas.value.style.cursor = 'grabbing'
  }
}

const handleCanvasMouseMove = (e: MouseEvent) => {
  if (!canvasState.value.isDragging) return

  const deltaX = e.clientX - canvasState.value.lastMouseX
  const deltaY = e.clientY - canvasState.value.lastMouseY

  canvasState.value.offsetX += deltaX
  canvasState.value.offsetY += deltaY
  canvasState.value.lastMouseX = e.clientX
  canvasState.value.lastMouseY = e.clientY

  if (lineageCanvas.value) {
    const ctx = lineageCanvas.value.getContext('2d')
    if (ctx) drawLineageChart(ctx)
  }
}

const handleCanvasMouseUp = () => {
  canvasState.value.isDragging = false
  if (lineageCanvas.value) {
    lineageCanvas.value.style.cursor = 'grab'
  }
}

const handleCanvasWheel = (e: WheelEvent) => {
  e.preventDefault()
  const delta = e.deltaY > 0 ? 0.9 : 1.1
  canvasState.value.scale = Math.max(0.5, Math.min(3, canvasState.value.scale * delta))

  if (lineageCanvas.value) {
    const ctx = lineageCanvas.value.getContext('2d')
    if (ctx) drawLineageChart(ctx)
  }
}

// 缩放控制
const zoomIn = () => {
  canvasState.value.scale = Math.min(3, canvasState.value.scale * 1.2)
  if (lineageCanvas.value) {
    const ctx = lineageCanvas.value.getContext('2d')
    if (ctx) drawLineageChart(ctx)
  }
}

const zoomOut = () => {
  canvasState.value.scale = Math.max(0.5, canvasState.value.scale * 0.8)
  if (lineageCanvas.value) {
    const ctx = lineageCanvas.value.getContext('2d')
    if (ctx) drawLineageChart(ctx)
  }
}

const resetView = () => {
  canvasState.value.scale = 1
  canvasState.value.offsetX = 0
  canvasState.value.offsetY = 0
  if (lineageCanvas.value) {
    const ctx = lineageCanvas.value.getContext('2d')
    if (ctx) drawLineageChart(ctx)
  }
}
</script>
