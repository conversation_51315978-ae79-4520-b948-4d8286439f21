<!-- 接口类型管理Tab组件 -->
<template>
  <div class="interface-type-tab">
    <div class="interface-layout">
      <!-- 左侧垂直Tab导航 -->
      <div class="left-nav">
        <div class="nav-header">
          <h3>接口类型</h3>
        </div>
        <div class="nav-list">
          <div
            v-for="item in navItems"
            :key="item.key"
            :class="['nav-item', { active: activeTab === item.key }]"
            @click="handleTabClick(item.key)"
          >
            <span class="nav-icon">{{ item.icon }}</span>
            <span class="nav-text">{{ item.label }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-content">
        <div class="content-wrapper">
          <!-- HTTP接口设置 -->
          <div v-if="activeTab === 'http'" class="tab-content">
            <HttpSettingsPage />
          </div>

          <!-- HTTPS接口设置 -->
          <div v-if="activeTab === 'https'" class="tab-content">
            <HttpsSettingsPage />
          </div>

          <!-- JDBC配置 -->
          <div v-if="activeTab === 'jdbc'" class="tab-content">
            <JdbcConfigPage />
          </div>

          <!-- FTP配置 -->
          <div v-if="activeTab === 'ftp'" class="tab-content">
            <FtpConfigPage />
          </div>

          <!-- SFTP配置 -->
          <div v-if="activeTab === 'sftp'" class="tab-content">
            <SftpConfigPage />
          </div>

          <!-- 本地文件设置 -->
          <div v-if="activeTab === 'localFile'" class="tab-content">
            <LocalFileSettingsPage />
          </div>

          <!-- 接口优先级排序 -->
          <div v-if="activeTab === 'priority'" class="tab-content">
            <InterfacePriorityPage />
          </div>

          <!-- 接口类型配置模板 -->
          <div v-if="activeTab === 'template'" class="tab-content">
            <InterfaceTemplateConfigPage />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="InterfaceTypeTab">
// 导入子页面组件
import HttpSettingsPage from './interfaceType/HttpSettingsPage.vue'
import HttpsSettingsPage from './interfaceType/HttpsSettingsPage.vue'
import JdbcConfigPage from './interfaceType/JdbcConfigPage.vue'
import FtpConfigPage from './interfaceType/FtpConfigPage.vue'
import SftpConfigPage from './interfaceType/SftpConfigPage.vue'
import LocalFileSettingsPage from './interfaceType/LocalFileSettingsPage.vue'
import InterfacePriorityPage from './interfaceType/InterfacePriorityPage.vue'
import InterfaceTemplateConfigPage from './interfaceType/InterfaceTemplateConfigPage.vue'

// 当前激活的Tab
const activeTab = ref('http')

// 导航项配置
const navItems = ref([
  {
    key: 'http',
    label: 'HTTP接口设置',
    icon: '🌐'
  },
  {
    key: 'https',
    label: 'HTTPS接口设置',
    icon: '🔒'
  },
  {
    key: 'jdbc',
    label: 'JDBC配置',
    icon: '🗄️'
  },
  {
    key: 'ftp',
    label: 'FTP配置',
    icon: '📁'
  },
  {
    key: 'sftp',
    label: 'SFTP配置',
    icon: '🔐'
  },
  {
    key: 'localFile',
    label: '本地文件设置',
    icon: '📄'
  },
  {
    key: 'priority',
    label: '接口优先级排序',
    icon: '📊'
  },
  {
    key: 'template',
    label: '接口类型配置模板',
    icon: '📋'
  }
])

// Tab切换处理
const handleTabClick = (tabKey: string) => {
  activeTab.value = tabKey
  console.log('切换到Tab:', tabKey)
}

// 组件挂载时的初始化
onMounted(() => {
  console.log('接口类型Tab组件已挂载')
})
</script>

<style scoped lang="scss">
.interface-type-tab {
  height: 100%;
  
  .interface-layout {
    display: flex;
    height: 100%;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
  }

  .left-nav {
    width: 200px;
    background: #f8f9fa;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;

    .nav-header {
      padding: 16px;
      border-bottom: 1px solid #e4e7ed;
      background: #fff;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .nav-list {
      flex: 1;
      padding: 8px 0;
    }

    .nav-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      border-left: 3px solid transparent;

      &:hover {
        background: #ecf5ff;
        color: #409eff;
      }

      &.active {
        background: #ecf5ff;
        color: #409eff;
        border-left-color: #409eff;
        font-weight: 600;
      }

      .nav-icon {
        margin-right: 8px;
        font-size: 16px;
      }

      .nav-text {
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .right-content {
    flex: 1;
    background: #fff;
    overflow: hidden;

    .content-wrapper {
      height: 100%;
      overflow-y: auto;
    }

    .tab-content {
      height: 100%;
      padding: 20px;
    }
  }
}
</style>
