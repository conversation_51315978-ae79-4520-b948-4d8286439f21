<!-- 添加清洗规则弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="添加清洗规则"
    width="600px"
    :destroy-on-close="true"
    :loading="loading"
    loading-text="保存中"
    @closed="resetForm"
    @click-confirm="onConfirm"
    @click-cancel="handleClose"
  >
    <div class="add-cleaning-rule-content" style="padding: 20px; min-height: 500px;">
      <div class="form-item">
        <label style="display: block; margin-bottom: 8px;">规则名称</label>
        <el-input
          v-model="form.ruleName"
          placeholder="请输入"
          style="width: 100%;"
        />
      </div>

      <div class="form-item" style="margin-top: 15px;">
        <label style="display: block; margin-bottom: 8px;">规则描述</label>
        <el-input
          v-model="form.ruleDescription"
          placeholder="请输入"
          style="width: 100%;"
        />
      </div>

      <div class="form-item" style="margin-top: 15px;">
        <label style="display: block; margin-bottom: 8px;">规则类型</label>
        <div class="rule-type-section">
          <div class="radio-group" style="margin-bottom: 15px;">
            <el-radio v-model="form.ruleType" label="removeBlank">去除空白</el-radio>
          </div>

          <div class="radio-group" style="margin-bottom: 15px;">
            <el-radio v-model="form.ruleType" label="dateFormat">日期格式化</el-radio>
            <el-input
              v-model="form.dateFormatPattern"
              placeholder="请输入目标日期格式（例如：YYYY-MM-DD）"
              style="width: 100%; margin-top: 10px;"
              :disabled="form.ruleType !== 'dateFormat'"
            />
          </div>

          <div class="radio-group" style="margin-bottom: 15px;">
            <el-radio v-model="form.ruleType" label="replaceChar">替换字符</el-radio>
            <div style="display: flex; gap: 15px; margin-top: 10px;">
              <el-input
                v-model="form.sourceChar"
                placeholder="请输入需要替换的字符"
                style="flex: 1;"
                :disabled="form.ruleType !== 'replaceChar'"
              />
              <el-input
                v-model="form.targetChar"
                placeholder="请输入需要替换的字符"
                style="flex: 1;"
                :disabled="form.ruleType !== 'replaceChar'"
              />
            </div>
          </div>

          <div class="radio-group" style="margin-bottom: 15px;">
            <el-radio v-model="form.ruleType" label="regex">正则表达式</el-radio>
            <el-input
              v-model="form.regexPattern"
              placeholder="请输入正则"
              style="width: 100%; margin-top: 10px;"
              :disabled="form.ruleType !== 'regex'"
            />
          </div>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'


// Props
const props = defineProps<{
  modelValue: boolean
  editData?: any
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirm: [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)

// 表单数据
const form = ref({
  ruleName: '',
  ruleDescription: '',
  ruleType: 'removeBlank',
  dateFormatPattern: '',
  sourceChar: '',
  targetChar: '',
  regexPattern: ''
})

// 验证必填项
const validateRequiredFields = () => {
  const errors = []

  if (!form.value.ruleName.trim()) {
    errors.push('规则名称不能为空')
  }

  if (!form.value.ruleDescription.trim()) {
    errors.push('规则描述不能为空')
  }

  if (!form.value.ruleType) {
    errors.push('规则类型不能为空')
  }

  // 根据规则类型验证特定字段
  if (form.value.ruleType === 'dateFormat' && !form.value.dateFormatPattern.trim()) {
    errors.push('日期格式不能为空')
  }

  if (form.value.ruleType === 'replaceChar') {
    if (!form.value.sourceChar.trim()) {
      errors.push('源字符不能为空')
    }
    if (!form.value.targetChar.trim()) {
      errors.push('目标字符不能为空')
    }
  }

  if (form.value.ruleType === 'regex' && !form.value.regexPattern.trim()) {
    errors.push('正则表达式不能为空')
  }

  return errors
}

// 重置表单
const resetForm = () => {
  form.value = {
    ruleName: '',
    ruleDescription: '',
    ruleType: 'removeBlank',
    dateFormatPattern: '',
    sourceChar: '',
    targetChar: '',
    regexPattern: ''
  }
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 确认保存
const onConfirm = () => {
  // 验证必填项
  const errors = validateRequiredFields()
  if (errors.length > 0) {
    ElMessage.error(`请完善以下信息：\n${errors.join('\n')}`)
    return
  }

  loading.value = true
  setTimeout(() => {
    // 根据规则类型生成规则类型显示名称
    const ruleTypeNames = {
      removeBlank: '去除空白',
      dateFormat: '日期格式化',
      replaceChar: '替换字符',
      regex: '正则表达式'
    }

    const ruleData = {
      ruleName: form.value.ruleName,
      ruleType: ruleTypeNames[form.value.ruleType] || form.value.ruleType,
      ruleDescription: form.value.ruleDescription
    }

    emit('confirm', ruleData)
    ElMessage.success(props.editData ? '编辑清洗规则成功' : '添加清洗规则成功')
    visible.value = false
    loading.value = false
  }, 1000)
}

// 监听编辑数据变化
watch(() => props.editData, (newVal) => {
  if (newVal) {
    // 根据规则类型反向映射
    const ruleTypeMap = {
      '去除空白': 'removeBlank',
      '日期格式化': 'dateFormat',
      '替换字符': 'replaceChar',
      '正则表达式': 'regex'
    }

    form.value = {
      ruleName: newVal.ruleName || '',
      ruleDescription: newVal.ruleDescription || '',
      ruleType: ruleTypeMap[newVal.ruleType] || 'removeBlank',
      dateFormatPattern: '',
      sourceChar: '',
      targetChar: '',
      regexPattern: ''
    }
  } else {
    resetForm()
  }
}, { immediate: true })

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal && !props.editData) {
    resetForm()
  }
})
</script>

<style lang="scss" scoped>
.add-cleaning-rule-content {
  .form-item {
    margin-bottom: 15px;
    
    label {
      font-size: 14px;
      color: #333;
      font-weight: 400;
    }
  }
  
  .rule-description-section {
    .checkbox-group {
      display: flex;
      flex-direction: column;
      gap: 10px;
      
      .el-checkbox {
        margin-right: 0;
      }
    }
    
    .input-group {
      .el-input {
        margin-bottom: 10px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
