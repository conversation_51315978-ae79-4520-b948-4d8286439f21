<!-- 数据集业务表绑定页面 -->
<template>
  <div class="data-set-business-table-binding">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>数据集业务表绑定</h2>
    </div>

    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :model="searchForm" inline>
        <el-form-item label="名称">
          <el-input v-model="searchForm.name" placeholder="请输入名称" style="width: 200px;" />
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="searchForm.type" placeholder="请选择类型" style="width: 150px;">
            <el-option label="全部" value="" />
            <el-option label="业务表" value="业务表" />
            <el-option label="维度表" value="维度表" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 顶部按钮群 -->
    <div class="top-buttons">
      <el-button type="primary" @click="onClickAdd">新建分区</el-button>
      <el-button type="primary" @click="onClickBatchImport">批量导入</el-button>
      <el-button type="primary" @click="onClickBatchExport">批量导出</el-button>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table :data="tableData" style="width: 100%" border>
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="name" label="名称" width="120" />
        <el-table-column prop="type" label="类型" width="100" />
        <el-table-column prop="businessTable" label="业务表名称" width="150" />
        <el-table-column prop="businessField" label="业务表字段" width="150" />
        <el-table-column prop="dataType" label="数据类型" width="100" />
        <el-table-column prop="length" label="长度" width="80" />
        <el-table-column prop="description" label="描述" min-width="120" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="onClickDetail(scope.row)">详情</el-button>
            <el-button type="primary" link @click="onClickEdit(scope.row)">修改</el-button>
            <el-button type="danger" link @click="onClickDelete(scope.row)">删除</el-button>
            
            <el-dropdown @command="(command) => handleRowMoreCommand(command, scope.row)">
              <el-button type="primary" link>
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="sync">同步</el-dropdown-item>
                  <el-dropdown-item command="validate">验证</el-dropdown-item>
                  <el-dropdown-item command="backup">备份</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新建分区弹窗 -->
    <Dialog
      v-model="showAddPartitionDialog"
      title="新建业务表分区管理"
      width="600px"
      confirm-text="确认"
      cancel-text="取消"
      @click-confirm="savePartition"
      @click-cancel="showAddPartitionDialog = false"
    >
      <div class="dialog-content">
        <el-form :model="partitionForm" label-width="120px">
          <el-form-item label="分区名称：">
            <el-input v-model="partitionForm.name" placeholder="请输入人员入库名称" />
          </el-form-item>

          <el-form-item label="数据库名称：">
            <el-input v-model="partitionForm.database" placeholder="请输入（以数据库表名称显示）" />
          </el-form-item>

          <el-form-item label="绑定业务表：">
            <el-input v-model="partitionForm.businessTable" placeholder="请输入（以业务表数据显示，可选择多个）" />
          </el-form-item>

          <el-form-item label="是否启用">
            <el-switch v-model="partitionForm.enabled" />
          </el-form-item>
        </el-form>

        <div style="margin-top: 20px; text-align: right;">
          <el-button type="primary" @click="onBusinessTableDeduplication">业务表去重</el-button>
          <el-button type="primary" @click="onBusinessTableValidation">业务表数据校验</el-button>
          <el-button type="primary">保存</el-button>
        </div>
      </div>
    </Dialog>
  </div>
</template>

<script setup lang="ts" name="DataSetBusinessTableBinding">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import Dialog from '@/components/Dialog/index.vue'

// 搜索表单
const searchForm = ref({
  name: '',
  type: ''
})

// 表格数据
const tableData = ref([
  {
    id: 1,
    name: 'user',
    type: '业务表',
    businessTable: '用户表',
    businessField: 'user_id',
    dataType: 'varchar',
    length: '50',
    description: '用户标识'
  },
  {
    id: 2,
    name: 'order',
    type: '业务表',
    businessTable: '订单表',
    businessField: 'order_id',
    dataType: 'varchar',
    length: '32',
    description: '订单编号'
  },
  {
    id: 3,
    name: 'product',
    type: '维度表',
    businessTable: '产品表',
    businessField: 'product_id',
    dataType: 'int',
    length: '11',
    description: '产品标识'
  }
])

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 3
})

// 新建分区弹窗
const showAddPartitionDialog = ref(false)
const partitionForm = ref({
  name: '',
  database: '',
  businessTable: '',
  enabled: true
})

// 搜索
const onSearch = () => {
  ElMessage.success('搜索完成')
}

// 重置
const onReset = () => {
  searchForm.value = {
    name: '',
    type: ''
  }
}

// 新建分区
const onClickAdd = () => {
  showAddPartitionDialog.value = true
}

// 批量导入
const onClickBatchImport = () => {
  ElMessage.info('批量导入功能开发中...')
}

// 批量导出
const onClickBatchExport = () => {
  ElMessage.info('批量导出功能开发中...')
}

// 详情
const onClickDetail = (row: any) => {
  ElMessage.info(`查看详情：${row.name}`)
}

// 修改
const onClickEdit = (row: any) => {
  ElMessage.info(`编辑：${row.name}`)
}

// 删除
const onClickDelete = (row: any) => {
  ElMessage.info(`删除：${row.name}`)
}

// 更多操作
const handleRowMoreCommand = (command: string, row: any) => {
  switch (command) {
    case 'sync':
      ElMessage.info(`同步：${row.name}`)
      break
    case 'validate':
      ElMessage.info(`验证：${row.name}`)
      break
    case 'backup':
      ElMessage.info(`备份：${row.name}`)
      break
  }
}

// 业务表去重
const onBusinessTableDeduplication = () => {
  ElMessage.success('去重成功')
}

// 业务表数据校验
const onBusinessTableValidation = () => {
  ElMessage.success('校验成功')
}

// 保存分区
const savePartition = () => {
  ElMessage.success('分区保存成功')
  showAddPartitionDialog.value = false
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
}

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.data-set-business-table-binding {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.search-area {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.top-buttons {
  margin-bottom: 20px;
}

.top-buttons .el-button {
  margin-right: 10px;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-content {
  padding: 20px;
}
</style>
