<template>
  <Dialog
    v-model="visible"
    title="数据来源分析"
    width="1000px"
    :destroy-on-close="true"
    :visible-confirm-button="false"
    cancel-text="关闭"
    @click-cancel="visible = false"
  >
    <div class="source-analysis" style="padding: 20px; min-height: 500px;">
      <!-- 操作按钮 -->
      <div class="operation-buttons" style="margin-bottom: 20px;">
        <el-button type="primary" @click="showAddDialog = true">新增来源分析</el-button>
      </div>

      <!-- 数据表格 -->
      <el-table :data="tableData" style="width: 100%" border>
        <el-table-column prop="id" label="序号" width="80" align="center" />
        <el-table-column prop="sourceName" label="来源名称" width="120" />
        <el-table-column prop="dataSource" label="数据源" width="120" />
        <el-table-column prop="analysisType" label="分析类型" width="120" />
        <el-table-column prop="analysisResult" label="分析结果" min-width="200" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template #default="{ row }">
            <el-button size="small" @click="handleEdit(row)">修改</el-button>
            <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination" style="margin-top: 20px; text-align: center;">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <Dialog
      v-model="showAddDialog"
      :title="currentRow ? '修改来源分析' : '新增来源分析'"
      width="600px"
      :destroy-on-close="true"
      :loading="formLoading"
      loading-text="保存中"
      @closed="resetForm"
      @click-confirm="onConfirm"
    >
      <div style="padding: 20px; min-height: 400px;">
        <Form
          ref="formRef"
          v-model="form"
          :props="[
            { label: '来源名称', prop: 'sourceName', type: 'text', required: true, placeholder: '请输入来源名称' },
            { label: '数据源', prop: 'dataSource', type: 'select', required: true, options: dataSourceOptions, placeholder: '请选择数据源' },
            { label: '分析类型', prop: 'analysisType', type: 'select', required: true, options: analysisTypeOptions, placeholder: '请选择分析类型' },
            { label: '分析描述', prop: 'description', type: 'textarea', placeholder: '请输入分析描述' }
          ]"
          :rules="formRules"
          :enable-button="false"
        />
      </div>
    </Dialog>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const showAddDialog = ref(false)
const formLoading = ref(false)
const currentRow = ref(null)
const formRef = ref()

// 表格数据
const tableData = ref([
  {
    id: 1,
    sourceName: '用户行为',
    dataSource: '用户数据库',
    analysisType: '行为分析',
    analysisResult: '用户活跃度较高，主要集中在工作日',
    createTime: '2024-01-15 10:30:00',
    status: '正常'
  },
  {
    id: 2,
    sourceName: '订单流水',
    dataSource: '订单数据库',
    analysisType: '趋势分析',
    analysisResult: '订单量呈上升趋势，周末订单量较高',
    createTime: '2024-01-14 14:20:00',
    status: '正常'
  },
  {
    id: 3,
    sourceName: '系统日志',
    dataSource: '日志数据库',
    analysisType: '异常分析',
    analysisResult: '发现部分接口响应时间过长',
    createTime: '2024-01-13 09:15:00',
    status: '异常'
  }
])

// 分页数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(3)

// 表单数据
const form = ref({
  sourceName: '',
  dataSource: '',
  analysisType: '',
  description: ''
})

// 表单验证规则
const formRules = ref({
  sourceName: [
    { required: true, message: '请输入来源名称', trigger: 'blur' }
  ],
  dataSource: [
    { required: true, message: '请选择数据源', trigger: 'change' }
  ],
  analysisType: [
    { required: true, message: '请选择分析类型', trigger: 'change' }
  ]
})

// 数据源选项（从数据源管理中获取）
const dataSourceOptions = ref([])

// 从localStorage获取数据源管理中的数据
const loadDataSourceOptions = () => {
  try {
    const cached = localStorage.getItem('dataSourceManagement_data')
    if (cached) {
      const dataSourceList = JSON.parse(cached)
      dataSourceOptions.value = dataSourceList.map((item: any) => ({
        label: item.name,
        value: item.name
      }))
    } else {
      // 如果没有缓存数据，使用默认数据
      dataSourceOptions.value = [
        { label: 'MySQL数据库1', value: 'MySQL数据库1' },
        { label: 'Oracle数据库1', value: 'Oracle数据库1' },
        { label: 'SQL Server数据库1', value: 'SQL Server数据库1' },
        { label: '达梦数据库1', value: '达梦数据库1' }
      ]
    }
  } catch (error) {
    console.error('加载数据源选项失败:', error)
    // 使用默认数据
    dataSourceOptions.value = [
      { label: 'MySQL数据库1', value: 'MySQL数据库1' },
      { label: 'Oracle数据库1', value: 'Oracle数据库1' },
      { label: 'SQL Server数据库1', value: 'SQL Server数据库1' },
      { label: '达梦数据库1', value: '达梦数据库1' }
    ]
  }
}

// 分析类型选项
const analysisTypeOptions = ref([
  { label: '行为分析', value: '行为分析' },
  { label: '趋势分析', value: '趋势分析' },
  { label: '异常分析', value: '异常分析' },
  { label: '关联分析', value: '关联分析' }
])

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case '正常':
      return 'success'
    case '异常':
      return 'danger'
    case '警告':
      return 'warning'
    default:
      return 'info'
  }
}

// 处理编辑
const handleEdit = (row: any) => {
  currentRow.value = row
  form.value = { ...row }
  showAddDialog.value = true
}

// 处理删除
const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这条来源分析记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 模拟删除操作
    const index = tableData.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      tableData.value.splice(index, 1)
      total.value--
      ElMessage.success('删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

// 重置表单
const resetForm = () => {
  currentRow.value = null
  form.value = {
    sourceName: '',
    dataSource: '',
    analysisType: '',
    description: ''
  }
  formRef.value?.resetFields()
}

// 确认保存
const onConfirm = async () => {
  try {
    await formRef.value?.validate()
    formLoading.value = true
    
    // 模拟保存操作
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (currentRow.value) {
      // 编辑
      const index = tableData.value.findIndex(item => item.id === currentRow.value.id)
      if (index > -1) {
        tableData.value[index] = {
          ...tableData.value[index],
          ...form.value,
          analysisResult: '分析完成，等待查看结果'
        }
      }
      ElMessage.success('修改成功')
    } else {
      // 新增
      const newItem = {
        id: Date.now(),
        ...form.value,
        analysisResult: '分析完成，等待查看结果',
        createTime: new Date().toLocaleString(),
        status: '正常'
      }
      tableData.value.unshift(newItem)
      total.value++
      ElMessage.success('新增成功')
    }
    
    showAddDialog.value = false
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    formLoading.value = false
  }
}

// 分页处理
const handleSizeChange = (val: number) => {
  pageSize.value = val
  // 重新加载数据
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  // 重新加载数据
}

onMounted(() => {
  // 初始化数据
  loadDataSourceOptions()
})
</script>

<style scoped lang="scss">
.source-analysis {
  .operation-buttons {
    display: flex;
    justify-content: flex-start;
  }

  .pagination {
    display: flex;
    justify-content: center;
  }
}
</style>
