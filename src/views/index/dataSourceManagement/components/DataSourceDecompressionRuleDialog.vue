<!-- 数据源解压规则配置弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="数据集压缩解压配置"
    width="700px"
    :destroy-on-close="true"
    :loading="loading"
    loading-text="保存中"
    @closed="resetForm"
    @click-confirm="onConfirm"
    @click-cancel="handleClose"
  >
    <div class="decompression-rule-content" style="padding: 20px; min-height: 400px;">
      <!-- 数据集压缩规则 -->
      <div class="form-section">
        <h4 style="margin-bottom: 15px; color: #333;">数据集压缩规则</h4>

        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px;">压缩范围配置</label>
          <div class="checkbox-row" style="display: flex; gap: 20px; flex-wrap: wrap;">
            <el-checkbox v-model="form.compressionRules.historyData">历史数据表（>3个月）</el-checkbox>
            <el-checkbox v-model="form.compressionRules.attachmentFiles">附件文件</el-checkbox>
            <el-checkbox v-model="form.compressionRules.realtimeData">实时数据</el-checkbox>
          </div>
        </div>

        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px;">压缩格式</label>
          <div class="checkbox-row" style="display: flex; gap: 20px; flex-wrap: wrap;">
            <el-checkbox v-model="form.compressionRules.zipFormat">ZIP</el-checkbox>
            <el-checkbox v-model="form.compressionRules.gzipFormat">GZIP</el-checkbox>
            <el-checkbox v-model="form.compressionRules.sevenZipFormat">7Z</el-checkbox>
            <el-checkbox v-model="form.compressionRules.rarFormat">RAR</el-checkbox>
          </div>
        </div>

        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px;">文件类型过滤</label>
          <div class="checkbox-row" style="display: flex; gap: 20px; flex-wrap: wrap;">
            <el-checkbox v-model="form.compressionRules.textLogOnly">仅压缩.txt/.log</el-checkbox>
            <el-checkbox v-model="form.compressionRules.allUnstructured">所有非结构化文件</el-checkbox>
          </div>
        </div>

        <div class="form-item" style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px;">压缩后处理</label>
          <div class="checkbox-row" style="display: flex; gap: 20px; flex-wrap: wrap;">
            <el-checkbox v-model="form.compressionRules.keepOriginal">保留原文件</el-checkbox>
            <el-checkbox v-model="form.compressionRules.autoDelete">自动删除</el-checkbox>
            <el-checkbox v-model="form.compressionRules.moveToArchive">移动至归档目录</el-checkbox>
          </div>
        </div>

        <div class="form-item">
          <label style="display: block; margin-bottom: 8px;">解压路径</label>
          <el-input
            v-model="form.decompressionRules.unzipPath"
            placeholder="请输入解压路径"
            style="width: 100%;"
          />
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'


// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)

// 表单数据
const form = ref({
  compressionRules: {
    // 压缩范围配置
    historyData: false,
    attachmentFiles: false,
    realtimeData: false,
    // 压缩格式
    zipFormat: false,
    gzipFormat: false,
    sevenZipFormat: false,
    rarFormat: false,
    // 文件类型过滤
    textLogOnly: false,
    allUnstructured: false,
    // 压缩后处理
    keepOriginal: false,
    autoDelete: false,
    moveToArchive: false
  },
  decompressionRules: {
    unzipPath: '../data/dataSet/unzip'
  }
})

// 缓存键
const STORAGE_KEY = 'dataSourceDecompressionRule'

// 加载配置数据
const loadConfigFromCache = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      const config = JSON.parse(cached)
      form.value = {
        compressionRules: {
          // 压缩范围配置
          historyData: config.compressionRules?.historyData || false,
          attachmentFiles: config.compressionRules?.attachmentFiles || false,
          realtimeData: config.compressionRules?.realtimeData || false,
          // 压缩格式
          zipFormat: config.compressionRules?.zipFormat || false,
          gzipFormat: config.compressionRules?.gzipFormat || false,
          sevenZipFormat: config.compressionRules?.sevenZipFormat || false,
          rarFormat: config.compressionRules?.rarFormat || false,
          // 文件类型过滤
          textLogOnly: config.compressionRules?.textLogOnly || false,
          allUnstructured: config.compressionRules?.allUnstructured || false,
          // 压缩后处理
          keepOriginal: config.compressionRules?.keepOriginal || false,
          autoDelete: config.compressionRules?.autoDelete || false,
          moveToArchive: config.compressionRules?.moveToArchive || false
        },
        decompressionRules: {
          unzipPath: config.decompressionRules?.unzipPath || '../data/dataSet/unzip'
        }
      }
    } else {
      resetForm()
    }
  } catch (error) {
    console.error('加载数据集压缩解压配置失败:', error)
    resetForm()
  }
}

// 保存配置数据
const saveConfigToCache = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(form.value))
  } catch (error) {
    console.error('保存数据源解压规则配置失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    compressionRules: {
      // 压缩范围配置
      historyData: false,
      attachmentFiles: false,
      realtimeData: false,
      // 压缩格式
      zipFormat: false,
      gzipFormat: false,
      sevenZipFormat: false,
      rarFormat: false,
      // 文件类型过滤
      textLogOnly: false,
      allUnstructured: false,
      // 压缩后处理
      keepOriginal: false,
      autoDelete: false,
      moveToArchive: false
    },
    decompressionRules: {
      unzipPath: '../data/dataSet/unzip'
    }
  }
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 确认保存
const onConfirm = () => {
  loading.value = true
  setTimeout(() => {
    saveConfigToCache()
    ElMessage.success('数据集压缩解压配置保存成功')
    visible.value = false
    loading.value = false
  }, 1000)
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadConfigFromCache()
  }
})
</script>

<style lang="scss" scoped>
.decompression-rule-content {
  .form-section {
    margin-bottom: 20px;
    
    h4 {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 15px;
    }
  }
  
  .form-item {
    margin-bottom: 15px;
    
    label {
      font-size: 14px;
      color: #333;
      font-weight: 400;
    }
  }
  
  .checkbox-row {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    
    .el-checkbox {
      margin-right: 0;
      white-space: nowrap;
    }
  }
  
  .radio-row {
    display: flex;
    flex-direction: column;
    gap: 10px;
    
    .el-radio {
      margin-right: 0;
    }
  }
}
</style>
