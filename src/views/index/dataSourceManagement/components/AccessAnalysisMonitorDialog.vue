<template>
  <Dialog
    v-model="visible"
    title="访问与分析监控"
    width="1200px"
    :destroy-on-close="true"
    :visible-confirm-button="false"
    cancel-text="关闭"
    @click-cancel="visible = false"
  >
    <div class="access-analysis-monitor" style="padding: 20px; min-height: 600px;">
      <!-- 统计卡片 -->
      <div class="stats-cards">
        <div class="stats-card">
          <div class="stats-icon success">
            <el-icon><Check /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ stats.totalAccess }}</div>
            <div class="stats-label">总访问次数</div>
          </div>
        </div>
        <div class="stats-card">
          <div class="stats-icon warning">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ stats.avgResponseTime }}ms</div>
            <div class="stats-label">平均响应时间</div>
          </div>
        </div>
        <div class="stats-card">
          <div class="stats-icon danger">
            <el-icon><Close /></el-icon>
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ stats.errorCount }}</div>
            <div class="stats-label">错误次数</div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="charts-container">
        <!-- 访问趋势图 -->
        <div class="chart-item">
          <h4>访问趋势</h4>
          <div ref="accessTrendChart" style="width: 100%; height: 300px;"></div>
        </div>

        <!-- 底部图表 -->
        <div class="bottom-charts">
          <!-- 访问量统计 -->
          <div class="chart-item half">
            <h4>访问量统计</h4>
            <div ref="accessVolumeChart" style="width: 100%; height: 250px;"></div>
          </div>
          
          <!-- 数据源分布 -->
          <div class="chart-item half">
            <h4>数据源分布</h4>
            <div ref="dataSourceChart" style="width: 100%; height: 250px;"></div>
          </div>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { Check, Clock, Close } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 图表引用
const accessTrendChart = ref()
const accessVolumeChart = ref()
const dataSourceChart = ref()

// 统计数据
const stats = ref({
  totalAccess: 12580,
  avgResponseTime: 245,
  errorCount: 23
})

// Mock数据
const accessTrendData = [
  { time: '00:00', value: 120 },
  { time: '02:00', value: 132 },
  { time: '04:00', value: 101 },
  { time: '06:00', value: 134 },
  { time: '08:00', value: 290 },
  { time: '10:00', value: 330 },
  { time: '12:00', value: 320 },
  { time: '14:00', value: 340 },
  { time: '16:00', value: 250 },
  { time: '18:00', value: 180 },
  { time: '20:00', value: 160 },
  { time: '22:00', value: 140 }
]

const accessVolumeData = [
  { name: '周一', value: 820 },
  { name: '周二', value: 932 },
  { name: '周三', value: 901 },
  { name: '周四', value: 934 },
  { name: '周五', value: 1290 },
  { name: '周六', value: 1330 },
  { name: '周日', value: 1320 }
]

const dataSourceDistribution = [
  { name: 'MySQL', value: 35, color: '#5470c6' },
  { name: 'Oracle', value: 25, color: '#91cc75' },
  { name: 'PostgreSQL', value: 20, color: '#fac858' },
  { name: 'MongoDB', value: 15, color: '#ee6666' },
  { name: '其他', value: 5, color: '#73c0de' }
]

// 初始化访问趋势图
const initAccessTrendChart = () => {
  const chart = echarts.init(accessTrendChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: accessTrendData.map(item => item.time)
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: accessTrendData.map(item => item.value),
      type: 'line',
      smooth: true,
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(84, 112, 198, 0.3)' },
          { offset: 1, color: 'rgba(84, 112, 198, 0.1)' }
        ])
      },
      lineStyle: {
        color: '#5470c6'
      }
    }]
  }
  chart.setOption(option)
}

// 初始化访问量统计图
const initAccessVolumeChart = () => {
  const chart = echarts.init(accessVolumeChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: accessVolumeData.map(item => item.name)
    },
    yAxis: {
      type: 'value'
    },
    series: [{
      data: accessVolumeData.map(item => item.value),
      type: 'bar',
      itemStyle: {
        color: '#5470c6'
      }
    }]
  }
  chart.setOption(option)
}

// 初始化数据源分布图
const initDataSourceChart = () => {
  const chart = echarts.init(dataSourceChart.value)
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center'
    },
    series: [{
      name: '数据源分布',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['40%', '50%'],
      data: dataSourceDistribution.map(item => ({
        name: item.name,
        value: item.value,
        itemStyle: { color: item.color }
      }))
    }]
  }
  chart.setOption(option)
}

// 监听弹窗显示状态
const handleDialogOpen = () => {
  nextTick(() => {
    initAccessTrendChart()
    initAccessVolumeChart()
    initDataSourceChart()
  })
}

// 监听visible变化
watch(() => visible.value, (newVal) => {
  if (newVal) {
    handleDialogOpen()
  }
})
</script>

<style scoped lang="scss">
.access-analysis-monitor {
  .stats-cards {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;

    .stats-card {
      flex: 1;
      display: flex;
      align-items: center;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      border-left: 4px solid #5470c6;

      .stats-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 24px;
        color: white;

        &.success {
          background: #67c23a;
        }

        &.warning {
          background: #e6a23c;
        }

        &.danger {
          background: #f56c6c;
        }
      }

      .stats-content {
        .stats-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 5px;
        }

        .stats-label {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }

  .charts-container {
    .chart-item {
      background: white;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      h4 {
        margin: 0 0 20px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
      }

      &.half {
        width: calc(50% - 10px);
      }
    }

    .bottom-charts {
      display: flex;
      gap: 20px;
    }
  }
}
</style>
