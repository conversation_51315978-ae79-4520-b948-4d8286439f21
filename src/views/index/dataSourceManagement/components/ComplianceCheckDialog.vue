<template>
  <Dialog
    v-model="visible"
    title="数据源合规性检查"
    width="1100px"
    :destroy-on-close="true"
    :visible-confirm-button="false"
    cancel-text="关闭"
    @click-cancel="visible = false"
  >
    <div class="compliance-check" style="padding: 20px; min-height: 400px;">
      <!-- 合规性检查规则管理 -->
      <div class="section">
        <div class="section-title">合规性检查规则管理</div>
        
        <!-- 操作按钮 -->
        <div class="operation-buttons" style="margin-bottom: 20px;">
          <el-button type="primary" @click="showAddRuleDialog = true">添加规则</el-button>
        </div>

        <!-- 规则列表表格 -->
        <el-table :data="ruleList" style="width: 100%" border>
          <el-table-column prop="id" label="序号" width="80" align="center" />
          <el-table-column prop="ruleName" label="规则名称" width="150" />
          <el-table-column prop="applicableField" label="适用字段" width="120" />
          <el-table-column prop="ruleType" label="规则类型" width="120" />
          <el-table-column prop="violationHandling" label="违规处理方式" width="120" />
          <el-table-column prop="ruleStatus" label="规则状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.ruleStatus === '启用' ? 'success' : 'danger'">
                {{ row.ruleStatus }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180" />
          <el-table-column label="操作" width="150" align="center">
            <template #default="{ row }">
              <el-button size="small" @click="handleEditRule(row)">修改</el-button>
              <el-button size="small" type="danger" @click="handleDeleteRule(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 检查执行配置 -->
      <div class="section" style="margin-top: 30px;">
        <div class="section-title">检查执行配置</div>
        <div class="form-section">
          <el-form :model="checkConfigForm" label-width="120px">
            <el-form-item label="检查频率：">
              <el-select v-model="checkConfigForm.frequency" style="width: 200px;">
                <el-option label="每日" value="每日" />
                <el-option label="每周" value="每周" />
                <el-option label="每月" value="每月" />
                <el-option label="自动触发" value="自动触发" />
              </el-select>
            </el-form-item>

            <el-form-item label="执行时间：">
              <el-time-picker
                v-model="checkConfigForm.executionTime"
                format="HH:mm:ss"
                value-format="HH:mm:ss"
                placeholder="选择时间"
                style="width: 200px;"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveConfig">确定</el-button>
      </div>
    </template>

    <!-- 添加/编辑规则弹窗 -->
    <Dialog
      v-model="showAddRuleDialog"
      :title="currentRule ? '修改规则' : '新增规则'"
      width="600px"
      :destroy-on-close="true"
      :loading="ruleFormLoading"
      loading-text="保存中"
      @closed="resetRuleForm"
      @click-confirm="onConfirmRule"
    >
      <div style="padding: 20px; min-height: 400px;">
        <Form
          ref="ruleFormRef"
          v-model="ruleForm"
          :props="ruleFormProps"
          :rules="ruleFormRules"
          :enable-button="false"
        />
      </div>
    </Dialog>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 规则列表
const ruleList = ref([
  {
    id: 1,
    ruleName: '城市格式校验',
    applicableField: '所属城市',
    ruleType: '格式校验',
    violationHandling: '仅告警',
    ruleStatus: '启用',
    createTime: '2024-01-15 10:30:00'
  },
  {
    id: 2,
    ruleName: '更新时间必填',
    applicableField: '更新时间',
    ruleType: '必填校验',
    violationHandling: '阻止记录',
    ruleStatus: '启用',
    createTime: '2024-01-16 14:20:00'
  }
])

// 检查配置表单
const checkConfigForm = ref({
  frequency: '每日',
  executionTime: '02:00:00'
})

// 添加规则弹窗状态
const showAddRuleDialog = ref(false)
const ruleFormLoading = ref(false)
const currentRule = ref(null)
const ruleFormRef = ref()

// 规则表单
const ruleForm = ref({
  ruleName: '',
  applicableField: '',
  ruleType: '',
  violationHandling: '',
  ruleStatus: '启用',
  ruleDescription: ''
})

// 规则表单配置
const ruleFormProps = ref([
  { 
    label: '规则名称', 
    prop: 'ruleName', 
    type: 'text', 
    required: true, 
    placeholder: '请输入规则名称（年月日）' 
  },
  { 
    label: '适用字段', 
    prop: 'applicableField', 
    type: 'select', 
    required: true, 
    options: [
      { label: '所属城市', value: '所属城市' },
      { label: '所属区县', value: '所属区县' },
      { label: '所属街道', value: '所属街道' },
      { label: '所属社区', value: '所属社区' },
      { label: '更新时间', value: '更新时间' },
      { label: '填报人', value: '填报人' },
      { label: '编辑人', value: '编辑人' },
      { label: '数据来源', value: '数据来源' }
    ],
    placeholder: '请选择适用字段' 
  },
  { 
    label: '规则类型', 
    prop: 'ruleType', 
    type: 'select', 
    required: true, 
    options: [
      { label: '格式校验', value: '格式校验' },
      { label: '范围校验', value: '范围校验' },
      { label: '必填校验', value: '必填校验' },
      { label: '自定义校验', value: '自定义校验' }
    ],
    placeholder: '请选择规则类型' 
  },
  { 
    label: '违规处理方式', 
    prop: 'violationHandling', 
    type: 'select', 
    required: true, 
    options: [
      { label: '仅告警', value: '仅告警' },
      { label: '阻止记录', value: '阻止记录' },
      { label: '自动修复', value: '自动修复' }
    ],
    placeholder: '请选择违规处理方式' 
  },
  { 
    label: '规则状态', 
    prop: 'ruleStatus', 
    type: 'select', 
    required: true, 
    options: [
      { label: '启用', value: '启用' },
      { label: '禁用', value: '禁用' }
    ],
    placeholder: '请选择规则状态' 
  },
  { 
    label: '规则描述', 
    prop: 'ruleDescription', 
    type: 'textarea', 
    placeholder: '请输入规则描述' 
  }
])

// 规则表单验证规则
const ruleFormRules = ref({
  ruleName: [
    { required: true, message: '请输入规则名称', trigger: 'blur' }
  ],
  applicableField: [
    { required: true, message: '请选择适用字段', trigger: 'change' }
  ],
  ruleType: [
    { required: true, message: '请选择规则类型', trigger: 'change' }
  ],
  violationHandling: [
    { required: true, message: '请选择违规处理方式', trigger: 'change' }
  ],
  ruleStatus: [
    { required: true, message: '请选择规则状态', trigger: 'change' }
  ]
})

// 编辑规则
const handleEditRule = (row: any) => {
  currentRule.value = row
  ruleForm.value = { ...row }
  showAddRuleDialog.value = true
}

// 删除规则
const handleDeleteRule = async (row: any) => {
  try {
    await ElMessageBox.confirm('确认删除该规则吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const index = ruleList.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      ruleList.value.splice(index, 1)
      saveRulesToCache()
      ElMessage.success('删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

// 确认添加/编辑规则
const onConfirmRule = async () => {
  try {
    // 表单验证
    const valid = await new Promise((resolve) => {
      ruleFormRef.value?.validate((valid: boolean) => {
        resolve(valid)
      })
    })

    if (!valid) {
      return
    }

    ruleFormLoading.value = true

    // 模拟保存延迟
    setTimeout(() => {
      if (currentRule.value) {
        // 编辑
        const index = ruleList.value.findIndex(item => item.id === currentRule.value.id)
        if (index > -1) {
          ruleList.value[index] = { ...ruleForm.value, id: currentRule.value.id }
        }
      } else {
        // 新增
        const newRule = {
          ...ruleForm.value,
          id: Date.now(),
          createTime: new Date().toLocaleString()
        }
        ruleList.value.push(newRule)
      }

      saveRulesToCache()
      ElMessage.success(currentRule.value ? '修改成功' : '添加成功')
      showAddRuleDialog.value = false
      ruleFormLoading.value = false
    }, 1000)

  } catch (error) {
    // 表单验证失败
    console.error('表单验证失败:', error)
    ruleFormLoading.value = false
  }
}

// 重置规则表单
const resetRuleForm = () => {
  currentRule.value = null
  ruleForm.value = {
    ruleName: '',
    applicableField: '',
    ruleType: '',
    violationHandling: '',
    ruleStatus: '启用',
    ruleDescription: ''
  }
}

// 保存检查配置
const handleSaveConfig = () => {
  try {
    const config = {
      checkConfig: checkConfigForm.value,
      rules: ruleList.value,
      updateTime: new Date().toISOString()
    }
    localStorage.setItem('complianceCheck_data', JSON.stringify(config))
    
    ElMessage.success('合规性检查配置保存成功')
    visible.value = false
  } catch (error) {
    console.error('保存合规性检查配置失败:', error)
    ElMessage.error('保存失败')
  }
}

// 保存规则到缓存
const saveRulesToCache = () => {
  try {
    const config = {
      checkConfig: checkConfigForm.value,
      rules: ruleList.value,
      updateTime: new Date().toISOString()
    }
    localStorage.setItem('complianceCheck_data', JSON.stringify(config))
  } catch (error) {
    console.error('保存规则到缓存失败:', error)
  }
}

// 加载配置
const loadConfig = () => {
  try {
    const cached = localStorage.getItem('complianceCheck_data')
    if (cached) {
      const config = JSON.parse(cached)
      if (config.checkConfig) {
        checkConfigForm.value = { ...checkConfigForm.value, ...config.checkConfig }
      }
      if (config.rules) {
        ruleList.value = config.rules
      }
    }
  } catch (error) {
    console.error('加载合规性检查配置失败:', error)
  }
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadConfig()
  }
})
</script>

<style scoped lang="scss">
.compliance-check {
  .section {
    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 2px solid #409eff;
    }
    
    .form-section {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 6px;
    }
  }
  
  .operation-buttons {
    display: flex;
    justify-content: flex-start;
  }
}

.dialog-footer {
  text-align: center;
}
</style>
