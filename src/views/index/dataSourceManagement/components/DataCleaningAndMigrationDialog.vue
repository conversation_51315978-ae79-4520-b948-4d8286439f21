<!-- 数据清理与迁移配置弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="数据清理与迁移"
    width="600px"
    :destroy-on-close="true"
    :loading="loading"
    loading-text="保存中"
    @closed="resetForm"
    @click-confirm="onConfirm"
    @click-cancel="handleClose"
  >
    <div class="data-cleaning-migration-content" style="padding: 20px; min-height: 400px;">
      <!-- 数据清理 -->
      <div class="form-section">
        <h4 style="margin-bottom: 15px; color: #333;">数据清理</h4>

        <div class="form-item">
          <label style="display: block; margin-bottom: 8px;">范围</label>
          <div class="checkbox-row" style="display: flex; gap: 20px;">
            <el-checkbox v-model="form.dataCleaning.timeBasedCleanup">临时表（>30天）</el-checkbox>
            <el-checkbox v-model="form.dataCleaning.logCleanup">日志表（>90天）</el-checkbox>
            <el-checkbox v-model="form.dataCleaning.duplicateDataCleanup">冗余备份文件</el-checkbox>
          </div>
        </div>

        <div class="form-item" style="margin-top: 20px;">
          <label style="display: block; margin-bottom: 8px;">执行频率</label>
          <div style="display: flex; align-items: center; gap: 15px;">
            <el-select
              v-model="form.dataCleaning.frequency"
              placeholder="请选择"
              style="width: 150px;"
            >
              <el-option label="每日" value="daily" />
              <el-option label="每周" value="weekly" />
              <el-option label="每月" value="monthly" />
            </el-select>

            <span>时分秒</span>
            <el-time-picker
              v-model="form.dataCleaning.executionTime"
              format="HH:mm:ss"
              placeholder="选择时间"
              style="width: 150px;"
            />
          </div>
        </div>

        <div class="form-item" style="margin-top: 15px;">
          <label style="display: block; margin-bottom: 8px;">清理方式</label>
          <el-select
            v-model="form.dataCleaning.cleaningMethod"
            placeholder="请选择（物理删除/逻辑删除）"
            style="width: 100%;"
          >
            <el-option label="物理删除" value="physical" />
            <el-option label="逻辑删除" value="logical" />
          </el-select>
        </div>
      </div>

      <!-- 数据迁移 -->
      <div class="form-section" style="margin-top: 30px;">
        <h4 style="margin-bottom: 15px; color: #333;">数据迁移</h4>

        <div class="form-item">
          <label style="display: block; margin-bottom: 8px;">目标数据源</label>
          <el-select
            v-model="form.dataMigration.targetDataSource"
            placeholder="请选择"
            style="width: 100%;"
          >
            <el-option
              v-for="dataSource in dataSourceList"
              :key="dataSource.id"
              :label="dataSource.name"
              :value="dataSource.id"
            />
          </el-select>
        </div>

        <div class="form-item" style="margin-top: 15px;">
          <label style="display: block; margin-bottom: 8px;">迁移方式</label>
          <div class="checkbox-row" style="display: flex; gap: 20px;">
            <el-checkbox v-model="form.dataMigration.fullIncremental">全量+增量同步</el-checkbox>
            <el-checkbox v-model="form.dataMigration.fullOnly">仅全量迁移</el-checkbox>
          </div>
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'


// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)

// 数据源列表
const dataSourceList = ref([])

// 表单数据
const form = ref({
  dataCleaning: {
    timeBasedCleanup: false,
    logCleanup: false,
    duplicateDataCleanup: false,
    frequency: '',
    executionTime: null,
    cleaningMethod: ''
  },
  dataMigration: {
    targetDataSource: '',
    fullIncremental: false,
    fullOnly: false
  }
})

// 缓存键
const STORAGE_KEY = 'dataCleaningAndMigration'
const DATA_SOURCE_STORAGE_KEY = 'dataSourceManagement_data'

// 加载数据源列表
const loadDataSourceList = () => {
  try {
    const cached = localStorage.getItem(DATA_SOURCE_STORAGE_KEY)
    if (cached) {
      dataSourceList.value = JSON.parse(cached)
    }
  } catch (error) {
    console.error('加载数据源列表失败:', error)
    dataSourceList.value = []
  }
}

// 加载配置数据
const loadConfigFromCache = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      const config = JSON.parse(cached)
      form.value = {
        dataCleaning: {
          timeBasedCleanup: config.dataCleaning?.timeBasedCleanup || false,
          logCleanup: config.dataCleaning?.logCleanup || false,
          duplicateDataCleanup: config.dataCleaning?.duplicateDataCleanup || false,
          frequency: config.dataCleaning?.frequency || '',
          executionTime: config.dataCleaning?.executionTime || null,
          cleaningMethod: config.dataCleaning?.cleaningMethod || ''
        },
        dataMigration: {
          targetDataSource: config.dataMigration?.targetDataSource || '',
          fullIncremental: config.dataMigration?.fullIncremental || false,
          fullOnly: config.dataMigration?.fullOnly || false
        }
      }
    } else {
      resetForm()
    }
  } catch (error) {
    console.error('加载数据清理与迁移配置失败:', error)
    resetForm()
  }
}

// 保存配置数据
const saveConfigToCache = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(form.value))
  } catch (error) {
    console.error('保存数据清理与迁移配置失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    dataCleaning: {
      timeBasedCleanup: false,
      logCleanup: false,
      duplicateDataCleanup: false,
      frequency: '',
      executionTime: null,
      cleaningMethod: ''
    },
    dataMigration: {
      targetDataSource: '',
      fullIncremental: false,
      fullOnly: false
    }
  }
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 确认保存
const onConfirm = () => {
  loading.value = true
  setTimeout(() => {
    saveConfigToCache()
    ElMessage.success('数据清理与迁移配置保存成功')
    visible.value = false
    loading.value = false
  }, 1000)
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadDataSourceList()
    loadConfigFromCache()
  }
})
</script>

<style lang="scss" scoped>
.data-cleaning-migration-content {
  .form-section {
    margin-bottom: 20px;
    
    h4 {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 15px;
    }
  }
  
  .form-item {
    margin-bottom: 15px;
    
    label {
      font-size: 14px;
      color: #333;
      font-weight: 400;
    }
  }
  
  .checkbox-row {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    
    .el-checkbox {
      margin-right: 0;
      white-space: nowrap;
    }
  }
  
  .radio-row {
    display: flex;
    flex-direction: column;
    gap: 10px;
    
    .el-radio {
      margin-right: 0;
    }
  }
}
</style>
