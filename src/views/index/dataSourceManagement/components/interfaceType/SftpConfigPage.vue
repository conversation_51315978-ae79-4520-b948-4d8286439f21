<!-- SFTP配置页面 -->
<template>
  <div class="sftp-config-page">
    <div class="page-header">
      <h2>SFTP设置</h2>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>

    <div class="settings-content">
      <!-- SFTP接口基础信息设置 -->
      <div class="settings-section">
        <h3>SFTP接口基础信息设置</h3>
        <el-form :model="form" label-width="120px" class="settings-form">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="SFTP服务器地址" required>
                <el-input v-model="form.serverHost" placeholder="请输入" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="端口号" required>
                <el-input v-model="form.port" placeholder="请输入（默认22）" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="用户名" required>
            <el-input v-model="form.username" placeholder="请输入" />
          </el-form-item>
        </el-form>
      </div>

      <!-- SFTP文件信息 -->
      <div class="settings-section">
        <h3>SFTP文件信息</h3>
        <el-form :model="form" label-width="120px" class="settings-form">
          <el-form-item label="私钥文件路径" required>
            <div class="file-path-input">
              <el-input v-model="form.privateKeyPath" placeholder="请输入" />
              <el-button type="default" icon="Folder">浏览</el-button>
            </div>
          </el-form-item>
          <el-form-item label="私钥密码">
            <el-input v-model="form.privateKeyPassword" type="password" placeholder="请输入" />
          </el-form-item>
        </el-form>
      </div>

      <!-- SFTP加密算法 -->
      <div class="settings-section">
        <h3>SFTP加密算法</h3>
        <el-form :model="form" label-width="120px" class="settings-form">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="加密算法">
                <el-select v-model="form.encryptionAlgorithm" placeholder="请选择" style="width: 100%;">
                  <el-option label="请选择" value="" />
                  <el-option label="AES-128" value="aes128" />
                  <el-option label="AES-192" value="aes192" />
                  <el-option label="AES-256" value="aes256" />
                  <el-option label="3DES" value="3des" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密钥交换算法">
                <el-select v-model="form.keyExchangeAlgorithm" placeholder="请选择" style="width: 100%;">
                  <el-option label="请选择" value="" />
                  <el-option label="Diffie-Hellman" value="dh" />
                  <el-option label="ECDH" value="ecdh" />
                  <el-option label="RSA" value="rsa" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="SftpConfigPage">
// 表单数据
const form = ref({
  // 基础信息
  serverHost: '',
  port: '',
  username: '',
  
  // 文件信息
  privateKeyPath: '',
  privateKeyPassword: '',
  
  // 加密算法
  encryptionAlgorithm: '',
  keyExchangeAlgorithm: ''
})

// 缓存键
const STORAGE_KEY = 'interfaceType_sftpConfig'

// 加载保存的数据
const loadData = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      const data = JSON.parse(cached)
      Object.assign(form.value, data)
    }
  } catch (error) {
    console.error('加载SFTP配置数据失败:', error)
  }
}

// 保存数据
const handleSave = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(form.value))
    ElMessage.success('SFTP配置保存成功')
  } catch (error) {
    console.error('保存SFTP配置数据失败:', error)
    ElMessage.error('保存失败')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.sftp-config-page {
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .settings-content {
    flex: 1;
    overflow-y: auto;
  }

  .settings-section {
    margin-bottom: 32px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;

    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .settings-form {
      .el-form-item {
        margin-bottom: 16px;
      }
    }

    .file-path-input {
      display: flex;
      gap: 8px;

      .el-input {
        flex: 1;
      }
    }
  }
}
</style>
