<!-- JDBC配置页面 -->
<template>
  <div class="jdbc-config-page">
    <div class="page-header">
      <h2>JDBC配置</h2>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>

    <div class="settings-content">
      <!-- JDBC自动提交模式设置 -->
      <div class="settings-section">
        <h3>JDBC自动提交模式设置</h3>
        <div class="switch-setting">
          <el-switch v-model="form.autoCommit" />
          <span class="switch-label">控制JDBC连接的自动提交行为，决定事务是否需要在每次操作后自动提交。</span>
        </div>
        <div class="radio-group" style="margin-top: 16px;">
          <el-radio-group v-model="form.commitMode">
            <el-radio label="auto">启用自动提交（默认）</el-radio>
            <el-radio label="manual">禁用自动提交</el-radio>
          </el-radio-group>
        </div>
      </div>

      <!-- JDBC事务隔离级别设置 -->
      <div class="settings-section">
        <h3>JDBC事务隔离级别设置</h3>
        <div class="switch-setting">
          <el-switch v-model="form.enableIsolationLevel" />
          <span class="switch-label">设置事务隔离级别，控制事务之间的可见性和并发控制。</span>
        </div>
        <div class="isolation-levels" style="margin-top: 16px;">
          <el-radio-group v-model="form.isolationLevel">
            <el-radio label="read_uncommitted">读未提交 (READ UNCOMMITTED)</el-radio>
            <el-radio label="read_committed">读已提交 (READ COMMITTED)</el-radio>
            <el-radio label="repeatable_read">可重复读 (REPEATABLE READ)</el-radio>
            <el-radio label="serializable">串行化 (SERIALIZABLE)</el-radio>
          </el-radio-group>
        </div>
      </div>

      <!-- JDBC连接字符集设置 -->
      <div class="settings-section">
        <h3>JDBC连接字符集设置</h3>
        <div class="switch-setting">
          <el-switch v-model="form.enableCharset" />
          <span class="switch-label">配置JDBC连接使用的字符集编码，确保数据库数据与应用之间的字符编码一致。</span>
        </div>
        <el-form :model="form" label-width="120px" class="settings-form" style="margin-top: 16px;">
          <el-form-item label="字符集编码">
            <el-select v-model="form.charset" placeholder="请选择" style="width: 100%;">
              <el-option label="请选择" value="" />
              <el-option label="UTF-8" value="utf8" />
              <el-option label="GBK" value="gbk" />
              <el-option label="ISO-8859-1" value="iso88591" />
              <el-option label="UTF-16" value="utf16" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- JDBC连接属性高级设置 -->
      <div class="settings-section">
        <h3>JDBC连接属性高级设置</h3>
        <div class="switch-setting">
          <el-switch v-model="form.enableAdvancedSettings" />
          <span class="switch-label">配置JDBC连接的高级属性，优化连接性能和行为。</span>
        </div>
        <el-form :model="form" label-width="150px" class="settings-form" style="margin-top: 16px;">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="连接超时时间（秒）">
                <el-input v-model="form.connectionTimeout" placeholder="请输入" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="Socket超时时间（秒）">
                <el-input v-model="form.socketTimeout" placeholder="请输入（设置为0表示永不超时）" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="最大连接数">
                <el-input v-model="form.maxConnections" placeholder="请输入" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="空闲连接超时（毫秒）">
                <el-input v-model="form.idleTimeout" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item>
            <el-checkbox v-model="form.maintainPreloadedQueries">维护预编译语句</el-checkbox>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="JdbcConfigPage">
// 表单数据
const form = ref({
  // 自动提交设置
  autoCommit: false,
  commitMode: 'auto',
  
  // 事务隔离级别
  enableIsolationLevel: false,
  isolationLevel: 'read_committed',
  
  // 字符集设置
  enableCharset: false,
  charset: '',
  
  // 高级设置
  enableAdvancedSettings: false,
  connectionTimeout: '',
  socketTimeout: '',
  maxConnections: '',
  idleTimeout: '',
  maintainPreloadedQueries: false
})

// 缓存键
const STORAGE_KEY = 'interfaceType_jdbcConfig'

// 加载保存的数据
const loadData = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      const data = JSON.parse(cached)
      Object.assign(form.value, data)
    }
  } catch (error) {
    console.error('加载JDBC配置数据失败:', error)
  }
}

// 保存数据
const handleSave = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(form.value))
    ElMessage.success('JDBC配置保存成功')
  } catch (error) {
    console.error('保存JDBC配置数据失败:', error)
    ElMessage.error('保存失败')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.jdbc-config-page {
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .settings-content {
    flex: 1;
    overflow-y: auto;
  }

  .settings-section {
    margin-bottom: 32px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;

    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .switch-setting {
      display: flex;
      align-items: flex-start;
      gap: 12px;

      .switch-label {
        font-size: 14px;
        color: #606266;
        line-height: 1.5;
        flex: 1;
      }
    }

    .radio-group {
      .el-radio-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
    }

    .isolation-levels {
      .el-radio-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
    }

    .settings-form {
      .el-form-item {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
