<!-- 接口优先级排序页面 -->
<template>
  <div class="interface-priority-page">
    <div class="page-header">
      <h2>接口优先级排序</h2>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>

    <div class="settings-content">
      <!-- 优先级说明 -->
      <div class="priority-info">
        <div class="info-icon">
          <el-icon><i class="el-icon-info"></i></el-icon>
        </div>
        <div class="info-content">
          <h4>优先级说明</h4>
          <p>接口类型优先级决定了系统在处理多种接口类型时的处理顺序。优先级高的接口类型将优先获得系统资源分配。</p>
        </div>
      </div>

      <!-- 拖拽排序列表 -->
      <div class="priority-list">
        <div
          v-for="(item, index) in priorityList"
          :key="item.id"
          :class="['priority-item', { dragging: dragIndex === index }]"
          draggable="true"
          @dragstart="handleDragStart(index, $event)"
          @dragover="handleDragOver($event)"
          @drop="handleDrop(index, $event)"
          @dragend="handleDragEnd"
        >
          <div class="drag-handle">
            <el-icon><i class="el-icon-rank"></i></el-icon>
          </div>
          <div class="priority-number">{{ index + 1 }}</div>
          <div class="interface-name">{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="InterfacePriorityPage">

// 接口类型列表
const priorityList = ref([
  { id: 1, name: 'REST API' },
  { id: 2, name: 'GraphQL' },
  { id: 3, name: 'WebSocket' },
  { id: 4, name: 'gRPC' },
  { id: 5, name: 'SOAP' }
])

// 拖拽状态
const dragIndex = ref(-1)

// 缓存键
const STORAGE_KEY = 'interfaceType_priorityOrder'

// 拖拽开始
const handleDragStart = (index: number, event: DragEvent) => {
  dragIndex.value = index
  if (event.dataTransfer) {
    event.dataTransfer.effectAllowed = 'move'
    event.dataTransfer.setData('text/html', index.toString())
  }
}

// 拖拽悬停
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }
}

// 拖拽放置
const handleDrop = (dropIndex: number, event: DragEvent) => {
  event.preventDefault()
  
  if (event.dataTransfer) {
    const draggedIndex = parseInt(event.dataTransfer.getData('text/html'))
    
    if (draggedIndex !== dropIndex) {
      // 移动元素
      const draggedItem = priorityList.value[draggedIndex]
      priorityList.value.splice(draggedIndex, 1)
      priorityList.value.splice(dropIndex, 0, draggedItem)
    }
  }
  
  dragIndex.value = -1
}

// 拖拽结束
const handleDragEnd = () => {
  dragIndex.value = -1
}

// 加载保存的数据
const loadData = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      const data = JSON.parse(cached)
      if (Array.isArray(data) && data.length > 0) {
        priorityList.value = data
      }
    }
  } catch (error) {
    console.error('加载接口优先级数据失败:', error)
  }
}

// 保存数据
const handleSave = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(priorityList.value))
    ElMessage.success('接口优先级排序保存成功')
  } catch (error) {
    console.error('保存接口优先级数据失败:', error)
    ElMessage.error('保存失败')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.interface-priority-page {
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .settings-content {
    flex: 1;
    overflow-y: auto;
  }

  .priority-info {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: #e6f7ff;
    border: 1px solid #91d5ff;
    border-radius: 6px;
    margin-bottom: 24px;

    .info-icon {
      color: #1890ff;
      font-size: 20px;
      margin-top: 2px;
    }

    .info-content {
      flex: 1;

      h4 {
        margin: 0 0 8px 0;
        font-size: 14px;
        font-weight: 600;
        color: #1890ff;
      }

      p {
        margin: 0;
        font-size: 14px;
        color: #595959;
        line-height: 1.5;
      }
    }
  }

  .priority-list {
    background: #fff;
    border-radius: 6px;
    overflow: hidden;
  }

  .priority-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    cursor: move;
    transition: all 0.3s ease;
    user-select: none;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background: #f5f5f5;
    }

    &.dragging {
      opacity: 0.5;
      background: #e6f7ff;
    }

    .drag-handle {
      margin-right: 12px;
      color: #8c8c8c;
      font-size: 16px;
      cursor: grab;

      &:active {
        cursor: grabbing;
      }
    }

    .priority-number {
      width: 32px;
      height: 32px;
      background: #1890ff;
      color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      margin-right: 16px;
    }

    .interface-name {
      flex: 1;
      font-size: 16px;
      font-weight: 500;
      color: #262626;
    }
  }
}
</style>
