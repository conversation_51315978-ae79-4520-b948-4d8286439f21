<!-- 接口类型配置模板页面 -->
<template>
  <div class="interface-template-config-page">
    <div class="page-header">
      <h2>接口类型配置模板</h2>
      <el-button type="primary" @click="handleAdd">新增</el-button>
    </div>

    <div class="template-content">
      <!-- 模板列表表格 -->
      <el-table :data="templateList" style="width: 100%" border>
        <el-table-column prop="templateName" label="模板名称" width="200" />
        <el-table-column prop="interfaceType" label="接口类型" width="150" />
        <el-table-column prop="dataSource" label="数据源" width="150" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="createUser" label="创建人" width="120" />
        <el-table-column label="操作" width="150">
          <template #default="{ row, $index }">
            <el-button type="primary" link @click="handleEdit(row, $index)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete($index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
        <el-form-item label="模板名称" prop="templateName">
          <el-input v-model="form.templateName" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="接口类型" prop="interfaceType">
          <el-select v-model="form.interfaceType" placeholder="请选择接口类型" style="width: 100%;">
            <el-option label="REST" value="REST" />
            <el-option label="GraphQL" value="GraphQL" />
            <el-option label="WebSocket" value="WebSocket" />
            <el-option label="gRPC" value="gRPC" />
            <el-option label="SOAP" value="SOAP" />
          </el-select>
        </el-form-item>
        <el-form-item label="数据源" prop="dataSource">
          <el-input v-model="form.dataSource" placeholder="请输入数据源" />
        </el-form-item>
        <el-form-item label="创建人" prop="createUser">
          <el-input v-model="form.createUser" placeholder="请输入创建人" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="InterfaceTemplateConfigPage">
// 表单引用
const formRef = ref()

// 模板列表数据
const templateList = ref([
  {
    templateName: '用户信息查询接口',
    interfaceType: 'REST',
    dataSource: 'MySQL 数据库',
    createTime: '2023-05-15 10:30:00',
    createUser: '张三'
  },
  {
    templateName: '用户信息查询接口',
    interfaceType: 'REST',
    dataSource: 'MySQL 数据库',
    createTime: '2023-05-15 10:30:00',
    createUser: '张三'
  },
  {
    templateName: '用户信息查询接口',
    interfaceType: 'REST',
    dataSource: 'MySQL 数据库',
    createTime: '2023-05-15 10:30:00',
    createUser: '张三'
  },
  {
    templateName: '用户信息查询接口',
    interfaceType: 'REST',
    dataSource: 'MySQL 数据库',
    createTime: '2023-05-15 10:30:00',
    createUser: '张三'
  },
  {
    templateName: '用户信息查询接口',
    interfaceType: 'REST',
    dataSource: 'MySQL 数据库',
    createTime: '2023-05-15 10:30:00',
    createUser: '张三'
  },
  {
    templateName: '用户信息查询接口',
    interfaceType: 'REST',
    dataSource: 'MySQL 数据库',
    createTime: '2023-05-15 10:30:00',
    createUser: '张三'
  },
  {
    templateName: '用户信息查询接口',
    interfaceType: 'REST',
    dataSource: 'MySQL 数据库',
    createTime: '2023-05-15 10:30:00',
    createUser: '张三'
  },
  {
    templateName: '用户信息查询接口',
    interfaceType: 'REST',
    dataSource: 'MySQL 数据库',
    createTime: '2023-05-15 10:30:00',
    createUser: '张三'
  },
  {
    templateName: '用户信息查询接口',
    interfaceType: 'REST',
    dataSource: 'MySQL 数据库',
    createTime: '2023-05-15 10:30:00',
    createUser: '张三'
  }
])

// 对话框状态
const dialogVisible = ref(false)
const dialogTitle = ref('新增模板')
const editIndex = ref(-1)

// 表单数据
const form = ref({
  templateName: '',
  interfaceType: '',
  dataSource: '',
  createUser: ''
})

// 表单验证规则
const rules = {
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' }
  ],
  interfaceType: [
    { required: true, message: '请选择接口类型', trigger: 'change' }
  ],
  dataSource: [
    { required: true, message: '请输入数据源', trigger: 'blur' }
  ],
  createUser: [
    { required: true, message: '请输入创建人', trigger: 'blur' }
  ]
}

// 缓存键
const STORAGE_KEY = 'interfaceType_templateConfig'

// 新增模板
const handleAdd = () => {
  dialogTitle.value = '新增模板'
  editIndex.value = -1
  resetForm()
  dialogVisible.value = true
}

// 编辑模板
const handleEdit = (row: any, index: number) => {
  dialogTitle.value = '编辑模板'
  editIndex.value = index
  form.value = { ...row }
  dialogVisible.value = true
}

// 删除模板
const handleDelete = (index: number) => {
  ElMessageBox.confirm('确定要删除这个模板吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    templateList.value.splice(index, 1)
    saveData()
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      const currentTime = new Date().toLocaleString('zh-CN')
      
      if (editIndex.value >= 0) {
        // 编辑模式
        templateList.value[editIndex.value] = {
          ...form.value,
          createTime: templateList.value[editIndex.value].createTime // 保持原创建时间
        }
        ElMessage.success('编辑成功')
      } else {
        // 新增模式
        templateList.value.push({
          ...form.value,
          createTime: currentTime
        })
        ElMessage.success('新增成功')
      }
      
      saveData()
      dialogVisible.value = false
    }
  })
}

// 关闭对话框
const handleDialogClose = () => {
  resetForm()
}

// 重置表单
const resetForm = () => {
  form.value = {
    templateName: '',
    interfaceType: '',
    dataSource: '',
    createUser: ''
  }
  formRef.value?.clearValidate()
}

// 加载保存的数据
const loadData = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      const data = JSON.parse(cached)
      if (Array.isArray(data) && data.length > 0) {
        templateList.value = data
      }
    }
  } catch (error) {
    console.error('加载模板配置数据失败:', error)
  }
}

// 保存数据
const saveData = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(templateList.value))
  } catch (error) {
    console.error('保存模板配置数据失败:', error)
    ElMessage.error('保存失败')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.interface-template-config-page {
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .template-content {
    flex: 1;
    overflow-y: auto;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
