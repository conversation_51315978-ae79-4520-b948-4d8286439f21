<!-- HTTP接口设置页面 -->
<template>
  <div class="http-settings-page">
    <div class="page-header">
      <h2>HTTP设置</h2>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>

    <div class="settings-content">
      <!-- 接口基础信息设置 -->
      <div class="settings-section">
        <h3>接口基础信息设置</h3>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="120px" class="settings-form">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="名称" prop="name" required>
                <el-input v-model="form.name" placeholder="请输入" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="url" prop="url" required>
                <el-input v-model="form.url" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="接口描述">
            <el-input v-model="form.description" type="textarea" placeholder="请输入" />
          </el-form-item>
        </el-form>
      </div>

      <!-- HTTP请求方法类型设置 -->
      <div class="settings-section">
        <h3>HTTP请求方法类型设置</h3>
        <div class="method-checkboxes">
          <el-checkbox v-model="form.methods.get">GET</el-checkbox>
          <el-checkbox v-model="form.methods.post">POST</el-checkbox>
          <el-checkbox v-model="form.methods.put">PUT</el-checkbox>
          <el-checkbox v-model="form.methods.delete">delete</el-checkbox>
          <el-checkbox v-model="form.methods.patch">PATCH</el-checkbox>
        </div>
      </div>

      <!-- HTTP响应状态码配置设置 -->
      <div class="settings-section">
        <h3>HTTP响应状态码配置设置</h3>
        <el-form :model="form" label-width="150px" class="settings-form">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="成功响应状态码范围">
                <el-input v-model="form.successStatusStart" placeholder="请输入（默认为200）" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="错误状态码范围">
                <el-input v-model="form.errorStatusStart" placeholder="请输入（默认为400）" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="重定向状态码范围">
                <el-input v-model="form.redirectStatusStart" placeholder="请输入（默认为300）" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="请输入（默认为999）">
                <el-input v-model="form.statusEnd" placeholder="请输入（默认为999）" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- HTTP响应内容编码设置 -->
      <div class="settings-section">
        <h3>HTTP响应内容编码设置</h3>
        <div class="encoding-options">
          <el-radio-group v-model="form.encoding">
            <el-radio label="UTF-8">UTF-8</el-radio>
            <el-radio label="GBK">GBK</el-radio>
            <el-radio label="ISO-8859-1">ISO-8859-1</el-radio>
          </el-radio-group>
        </div>
      </div>

      <!-- HTTP响应超时时间配置 -->
      <div class="settings-section">
        <h3>HTTP响应超时时间配置</h3>
        <el-form :model="form" label-width="120px" class="settings-form">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="连接超时时间 (秒)">
                <el-input v-model="form.connectionTimeout" placeholder="请输入" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="读取超时时间 (秒)">
                <el-input v-model="form.readTimeout" placeholder="请输入" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="写入超时时间 (秒)">
                <el-input v-model="form.writeTimeout" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- HTTP重定向自动跟随设置 -->
      <div class="settings-section">
        <h3>HTTP重定向自动跟随设置</h3>
        <div class="redirect-settings">
          <el-switch v-model="form.autoRedirect" />
          <span class="switch-label">启用重定向自动跟随</span>
        </div>
        <el-form :model="form" label-width="120px" class="settings-form" style="margin-top: 16px;">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="最大重定向次数">
                <el-input v-model="form.maxRedirects" placeholder="请输入" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item>
                <el-checkbox v-model="form.allowHttpsRedirect">
                  允许跳转协议定向 (HTTP -> HTTPS 或反之)
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- HTTP代理服务器设置 -->
      <div class="settings-section">
        <h3>HTTP代理服务器设置</h3>
        <div class="proxy-settings">
          <el-switch v-model="form.enableProxy" />
          <span class="switch-label">启用代理服务器</span>
        </div>
        <el-form :model="form" label-width="120px" class="settings-form" style="margin-top: 16px;">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="代理服务器地址">
                <el-input v-model="form.proxyHost" placeholder="请输入地址，如：***********" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="代理服务器端口">
                <el-input v-model="form.proxyPort" placeholder="请输入端口，如：8080" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="代理服务器用户名">
                <el-input v-model="form.proxyUsername" placeholder="请输入" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="代理服务器密码">
                <el-input v-model="form.proxyPassword" type="password" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="HttpSettingsPage">
// 表单引用
const formRef = ref()

// 表单数据
const form = ref({
  // 基础信息
  name: '',
  url: '',
  description: '',
  
  // 请求方法
  methods: {
    get: false,
    post: false,
    put: false,
    delete: false,
    patch: false
  },
  
  // 状态码配置
  successStatusStart: '',
  errorStatusStart: '',
  redirectStatusStart: '',
  statusEnd: '',
  
  // 编码设置
  encoding: 'UTF-8',
  
  // 超时配置
  connectionTimeout: '',
  readTimeout: '',
  writeTimeout: '',
  
  // 重定向设置
  autoRedirect: false,
  maxRedirects: '',
  allowHttpsRedirect: false,
  
  // 代理设置
  enableProxy: false,
  proxyHost: '',
  proxyPort: '',
  proxyUsername: '',
  proxyPassword: ''
})

// 表单验证规则
const rules = ref({
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入URL', trigger: 'blur' }
  ]
})

// 缓存键
const STORAGE_KEY = 'interfaceType_httpSettings'

// 加载保存的数据
const loadData = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      const data = JSON.parse(cached)
      Object.assign(form.value, data)
    }
  } catch (error) {
    console.error('加载HTTP设置数据失败:', error)
  }
}

// 保存数据
const handleSave = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(form.value))
        ElMessage.success('HTTP设置保存成功')
      } catch (error) {
        console.error('保存HTTP设置数据失败:', error)
        ElMessage.error('保存失败')
      }
    } else {
      ElMessage.warning('请填写必填项')
    }
  })
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.http-settings-page {
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e4e7ed;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .settings-content {
    flex: 1;
    overflow-y: auto;
  }

  .settings-section {
    margin-bottom: 32px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;

    h3 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .settings-form {
      .el-form-item {
        margin-bottom: 16px;
      }
    }

    .method-checkboxes {
      display: flex;
      gap: 20px;
      flex-wrap: wrap;

      .el-checkbox {
        margin-right: 0;
      }
    }

    .encoding-options {
      .el-radio-group {
        display: flex;
        gap: 20px;
      }
    }

    .redirect-settings,
    .proxy-settings {
      display: flex;
      align-items: center;
      gap: 12px;

      .switch-label {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}
</style>
