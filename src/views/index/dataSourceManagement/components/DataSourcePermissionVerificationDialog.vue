<!-- 数据源权限验证弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="数据源权限验证"
    width="600px"
    :destroy-on-close="true"
    :loading="loading"
    loading-text="保存中"
    @closed="resetForm"
    @click-confirm="onConfirm"
    @click-cancel="handleClose"
  >
    <div class="permission-verification-content" style="padding: 20px; min-height: 500px;">
      <!-- 权限验证方式 -->
      <div class="form-section">
        <h4 style="margin-bottom: 15px; color: #333;">权限验证方式</h4>

        <div class="checkbox-group" style="display: flex; gap: 20px;">
          <el-checkbox v-model="form.userPermissionVerification">用户名密码验证</el-checkbox>
          <el-checkbox v-model="form.ipWhitelistVerification">IP白名单验证</el-checkbox>
          <el-checkbox v-model="form.tokenVerification">token验证</el-checkbox>
        </div>
      </div>

      <!-- 验证提示内容 -->
      <div class="form-section" style="margin-top: 25px;">
        <h4 style="margin-bottom: 15px; color: #333;">验证提示内容</h4>

        <div class="form-item">
          <label style="display: block; margin-bottom: 8px;">
            <span style="color: red;">*</span> 提示类型：
          </label>
          <el-select
            v-model="form.promptType"
            placeholder="权限不足"
            style="width: 100%;"
          >
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failure" />
            <el-option label="权限不足" value="insufficient" />
            <el-option label="Token过期/无效" value="token_invalid" />
            <el-option label="用户名/密码错误" value="credential_error" />
          </el-select>
        </div>

        <div class="form-item" style="margin-top: 15px;">
          <label style="display: block; margin-bottom: 8px;">
            <span style="color: red;">*</span> 提示内容：
          </label>
          <el-input
            v-model="form.promptContent"
            type="textarea"
            :rows="4"
            placeholder="请输入内容"
            style="width: 100%;"
          />
        </div>
      </div>

      <!-- 验证提示规则 -->
      <div class="form-section" style="margin-top: 25px;">
        <h4 style="margin-bottom: 15px; color: #333;">验证提示规则</h4>

        <div class="form-item">
          <label style="display: block; margin-bottom: 8px;">
            <span style="color: red;">*</span> 提示方式：
          </label>
          <div class="checkbox-group" style="display: flex; gap: 20px;">
            <el-checkbox v-model="form.popupPrompt">弹窗提示</el-checkbox>
            <el-checkbox v-model="form.pageTopPrompt">页面顶部横幅</el-checkbox>
          </div>
        </div>

        <div class="form-item" style="margin-top: 15px;">
          <label style="display: block; margin-bottom: 8px;">
            <span style="color: red;">*</span> 提示时长：
          </label>
          <el-input
            v-model="form.promptDuration"
            placeholder="请输入（s）"
            style="width: 100%;"
          />
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'



// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)

// 表单数据
const form = ref({
  userPermissionVerification: false,
  ipWhitelistVerification: false,
  tokenVerification: false,
  promptType: 'insufficient',
  promptContent: '',
  popupPrompt: false,
  pageTopPrompt: false,
  promptDuration: ''
})

// 缓存键
const STORAGE_KEY = 'dataSourcePermissionVerification'

// 验证必填项
const validateRequiredFields = () => {
  const errors = []

  if (!form.value.promptType) {
    errors.push('提示类型不能为空')
  }

  if (!form.value.promptContent.trim()) {
    errors.push('提示内容不能为空')
  }

  if (!form.value.popupPrompt && !form.value.pageTopPrompt) {
    errors.push('请至少选择一种提示方式')
  }

  if (!form.value.promptDuration) {
    errors.push('提示时长不能为空')
  }

  return errors
}

// 加载配置数据
const loadConfigFromCache = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      const config = JSON.parse(cached)
      form.value = {
        userPermissionVerification: config.userPermissionVerification || false,
        ipWhitelistVerification: config.ipWhitelistVerification || false,
        tokenVerification: config.tokenVerification || false,
        promptType: config.promptType || 'insufficient',
        promptContent: config.promptContent || '',
        popupPrompt: config.popupPrompt || false,
        pageTopPrompt: config.pageTopPrompt || false,
        promptDuration: config.promptDuration || ''
      }
    } else {
      resetForm()
    }
  } catch (error) {
    console.error('加载数据源权限验证配置失败:', error)
    resetForm()
  }
}

// 保存配置数据
const saveConfigToCache = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(form.value))
  } catch (error) {
    console.error('保存数据源权限验证配置失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    userPermissionVerification: false,
    ipWhitelistVerification: false,
    tokenVerification: false,
    promptType: 'insufficient',
    promptContent: '',
    popupPrompt: false,
    pageTopPrompt: false,
    promptDuration: ''
  }
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 确认保存
const onConfirm = () => {
  // 验证必填项
  const errors = validateRequiredFields()
  if (errors.length > 0) {
    ElMessage.error(`请完善以下信息：\n${errors.join('\n')}`)
    return
  }

  loading.value = true
  setTimeout(() => {
    saveConfigToCache()
    ElMessage.success('数据源权限验证配置保存成功')
    visible.value = false
    loading.value = false
  }, 1000)
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadConfigFromCache()
  }
})
</script>

<style lang="scss" scoped>
.permission-verification-content {
  .form-section {
    margin-bottom: 20px;
    
    h4 {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 15px;
    }
  }
  
  .form-item {
    margin-bottom: 15px;
    
    label {
      font-size: 14px;
      color: #333;
      font-weight: 400;
    }
  }
  
  .checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
    
    .el-checkbox {
      margin-right: 0;
    }
  }
  
  .radio-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
    
    .el-radio {
      margin-right: 0;
      display: flex;
      align-items: center;
      
      .el-icon {
        margin-right: 5px;
      }
    }
  }
}
</style>
