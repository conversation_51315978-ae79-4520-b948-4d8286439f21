<!-- 数据源容量配置弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="数据源容量配置"
    width="500px"
    :destroy-on-close="true"
    :loading="loading"
    loading-text="保存中"
    @closed="resetForm"
    @click-confirm="onConfirm"
    @click-cancel="handleClose"
  >
    <div class="capacity-config-content" style="padding: 20px; min-height: 400px;">
      <!-- 容量监控配置 -->
      <div class="form-section">
        <h4 style="margin-bottom: 15px; color: #333;">容量监控配置</h4>
        
        <div class="form-item">
          <label style="display: block; margin-bottom: 8px;">存储容量（GB）</label>
          <el-input
            v-model="form.storageCapacity"
            placeholder="请输入"
            style="width: 100%;"
          />
        </div>
        
        <div class="form-item" style="margin-top: 15px;">
          <label style="display: block; margin-bottom: 8px;">手动容量（%）</label>
          <el-input
            v-model="form.manualCapacity"
            placeholder="请输入"
            style="width: 100%;"
          />
        </div>
        
        <div class="form-item" style="margin-top: 15px;">
          <label style="display: block; margin-bottom: 8px;">手动容量（GB）</label>
          <el-input
            v-model="form.manualCapacityGB"
            placeholder="请输入"
            style="width: 100%;"
          />
        </div>
      </div>
      
      <!-- 容量控制 -->
      <div class="form-section" style="margin-top: 25px;">
        <h4 style="margin-bottom: 15px; color: #333;">容量控制</h4>
        
        <div class="form-item">
          <label style="display: block; margin-bottom: 8px;">初始数据容量</label>
          <el-input
            v-model="form.initialDataCapacity"
            placeholder="请输入"
            style="width: 100%;"
          />
        </div>
        
        <div class="form-item" style="margin-top: 15px;">
          <label style="display: block; margin-bottom: 8px;">最大数据容量</label>
          <el-input
            v-model="form.maxDataCapacity"
            placeholder="请输入"
            style="width: 100%;"
          />
        </div>
      </div>
    </div>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'


// Props
const props = defineProps<{
  modelValue: boolean
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)

// 表单数据
const form = ref({
  storageCapacity: '',
  manualCapacity: '',
  manualCapacityGB: '',
  initialDataCapacity: '',
  maxDataCapacity: ''
})

// 缓存键
const STORAGE_KEY = 'dataSourceCapacityConfig'

// 加载配置数据
const loadConfigFromCache = () => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY)
    if (cached) {
      const config = JSON.parse(cached)
      form.value = {
        storageCapacity: config.storageCapacity || '',
        manualCapacity: config.manualCapacity || '',
        manualCapacityGB: config.manualCapacityGB || '',
        initialDataCapacity: config.initialDataCapacity || '',
        maxDataCapacity: config.maxDataCapacity || ''
      }
    } else {
      resetForm()
    }
  } catch (error) {
    console.error('加载数据源容量配置失败:', error)
    resetForm()
  }
}

// 保存配置数据
const saveConfigToCache = () => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(form.value))
  } catch (error) {
    console.error('保存数据源容量配置失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    storageCapacity: '',
    manualCapacity: '',
    manualCapacityGB: '',
    initialDataCapacity: '',
    maxDataCapacity: ''
  }
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 确认保存
const onConfirm = () => {
  loading.value = true
  setTimeout(() => {
    saveConfigToCache()
    ElMessage.success('数据源容量配置保存成功')
    visible.value = false
    loading.value = false
  }, 1000)
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    loadConfigFromCache()
  }
})
</script>

<style lang="scss" scoped>
.capacity-config-content {
  .form-section {
    margin-bottom: 20px;
    
    h4 {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 15px;
    }
  }
  
  .form-item {
    margin-bottom: 15px;
    
    label {
      font-size: 14px;
      color: #333;
      font-weight: 400;
    }
  }
}
</style>
