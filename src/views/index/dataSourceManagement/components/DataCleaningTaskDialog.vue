<!-- 数据集清洗任务与配置弹窗 -->
<template>
  <Dialog
    v-model="visible"
    title="数据集清洗任务与配置"
    width="800px"
    :destroy-on-close="true"
    :loading="loading"
    loading-text="保存中"
    :visible-confirm-button="activeTab === 'rules'"
    @closed="resetForm"
    @click-confirm="onConfirm"
    @click-cancel="handleClose"
  >
    <div class="data-cleaning-task-content" style="padding: 20px; min-height: 400px;">
      <!-- Tab切换 -->
      <el-tabs v-model="activeTab" class="cleaning-tabs">
        <el-tab-pane label="清洗规则配置" name="rules">
          <div class="rules-config-content">
            <!-- 操作按钮 -->
            <div class="action-buttons" style="margin-bottom: 20px;">
              <el-button type="primary" @click="showAddRuleDialog">添加清洗规则</el-button>
            </div>
            
            <!-- 清洗规则列表 -->
            <el-table :data="paginatedCleaningRules" border style="width: 100%;">
              <el-table-column prop="index" label="序号" width="80" align="center" />
              <el-table-column prop="ruleName" label="规则名称" />
              <el-table-column prop="ruleType" label="规则类型" />
              <el-table-column prop="ruleDescription" label="规则描述" />
              <el-table-column label="操作" width="120" align="center">
                <template #default="{ row, $index }">
                  <el-button type="text" size="small" @click="editRule(row, $index)">编辑</el-button>
                  <el-button type="text" size="small" @click="deleteRule(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container" style="margin-top: 20px; text-align: right;">
              <el-pagination
                v-model:current-page="rulesPagination.currentPage"
                v-model:page-size="rulesPagination.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="cleaningRules.length"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleRulesSizeChange"
                @current-change="handleRulesCurrentChange"
              />
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="清洗任务管理" name="tasks">
          <div class="tasks-management-content">
            <!-- 查询条件 -->
            <div class="search-form" style="margin-bottom: 20px;">
              <el-form :model="searchForm" inline>
                <el-form-item label="任务名称">
                  <el-input v-model="searchForm.taskName" placeholder="请输入任务名称" style="width: 200px;" />
                </el-form-item>
                <el-form-item label="执行状态">
                  <el-select v-model="searchForm.status" placeholder="请选择" style="width: 150px;">
                    <el-option label="全部" value="" />
                    <el-option label="待执行" value="pending" />
                    <el-option label="执行中" value="running" />
                    <el-option label="已完成" value="completed" />
                    <el-option label="失败" value="failed" />
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="searchTasks">查询</el-button>
                  <el-button @click="resetSearch">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons" style="margin-bottom: 20px;">
              <el-button type="primary" @click="showAddTaskDialog">新增</el-button>
              <el-button @click="goBack">返回</el-button>
            </div>
            
            <!-- 任务列表 -->
            <el-table :data="cleaningTasks" border style="width: 100%;" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55" />
              <el-table-column prop="taskName" label="任务名称" />
              <el-table-column prop="associatedRule" label="关联规则" />
              <el-table-column prop="executionTime" label="执行时间" />
              <el-table-column prop="lastExecutionTime" label="上一次执行时间" />
              <el-table-column label="操作" width="120" align="center">
                <template #default="{ row, $index }">
                  <el-button type="text" size="small" @click="editTask(row, $index)">编辑</el-button>
                  <el-button type="text" size="small" @click="deleteTask(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <!-- 添加清洗规则弹窗 -->
    <AddCleaningRuleDialog
      v-model="showAddRuleDialogVisible"
      :edit-data="editRuleData"
      @confirm="handleAddRule"
    />
    
    <!-- 新增清洗任务弹窗 -->
    <CleaningTaskFormDialog
      v-model="showAddTaskDialogVisible"
      :edit-data="editTaskData"
      :cleaning-rules="cleaningRules"
      @confirm="handleAddTask"
    />
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

import AddCleaningRuleDialog from './AddCleaningRuleDialog.vue'
import CleaningTaskFormDialog from './CleaningTaskFormDialog.vue'

// Props
const props = defineProps<{
  modelValue: boolean
  editData?: any
  dataSourceType?: string // 'datasource' 或 'dataset'
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const activeTab = ref('rules')

// 分页数据
const rulesPagination = ref({
  currentPage: 1,
  pageSize: 10
})

// 清洗规则数据
const cleaningRules = ref([
 
])

// 清洗任务数据
const cleaningTasks = ref([])

// 搜索表单
const searchForm = ref({
  taskName: '',
  status: ''
})

// 弹窗控制
const showAddRuleDialogVisible = ref(false)
const showAddTaskDialogVisible = ref(false)
const editRuleData = ref(null)
const editTaskData = ref(null)
const selectedTasks = ref([])

// 缓存键 - 根据数据源类型区分
const getStorageKey = () => {
  const type = props.dataSourceType || 'datasource'
  return `${type}CleaningTaskConfig`
}

const getRowConfigStorageKey = () => {
  const type = props.dataSourceType || 'datasource'
  return `${type}CleaningRowConfigs`
}

// 分页计算属性
const paginatedCleaningRules = computed(() => {
  const start = (rulesPagination.value.currentPage - 1) * rulesPagination.value.pageSize
  const end = start + rulesPagination.value.pageSize
  return cleaningRules.value.slice(start, end)
})

// 分页方法
const handleRulesSizeChange = (val: number) => {
  rulesPagination.value.pageSize = val
  rulesPagination.value.currentPage = 1
}

const handleRulesCurrentChange = (val: number) => {
  rulesPagination.value.currentPage = val
}

// 显示添加规则弹窗
const showAddRuleDialog = () => {
  editRuleData.value = null
  showAddRuleDialogVisible.value = true
}

// 编辑规则
const editRule = (row: any, index: number) => {
  editRuleData.value = { ...row, index }
  showAddRuleDialogVisible.value = true
}

// 删除规则
const deleteRule = (row: any) => {
  ElMessageBox.confirm('确定要删除这条清洗规则吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 根据行数据找到在原始数组中的索引
    const index = cleaningRules.value.findIndex(item =>
      item.ruleName === row.ruleName && item.ruleType === row.ruleType
    )
    if (index > -1) {
      cleaningRules.value.splice(index, 1)
      // 重新编号
      cleaningRules.value.forEach((item, idx) => {
        item.index = idx + 1
      })
      // 保存配置
      saveConfigToCache()
      ElMessage.success('删除成功')
    }
  })
}

// 处理添加规则
const handleAddRule = (ruleData: any) => {
  if (editRuleData.value) {
    // 编辑
    const index = editRuleData.value.index - 1
    cleaningRules.value[index] = { ...ruleData, index: editRuleData.value.index }
  } else {
    // 新增
    cleaningRules.value.push({
      ...ruleData,
      index: cleaningRules.value.length + 1
    })
  }
  saveConfigToCache()
}

// 显示添加任务弹窗
const showAddTaskDialog = () => {
  editTaskData.value = null
  showAddTaskDialogVisible.value = true
}

// 编辑任务
const editTask = (row: any, index: number) => {
  editTaskData.value = { ...row, index }
  showAddTaskDialogVisible.value = true
}

// 删除任务
const deleteTask = (row: any) => {
  ElMessageBox.confirm('确定要删除这个清洗任务吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 根据行数据找到在原始数组中的索引
    const index = cleaningTasks.value.findIndex(item =>
      item.taskName === row.taskName && item.associatedRule === row.associatedRule
    )
    if (index > -1) {
      cleaningTasks.value.splice(index, 1)
      // 保存配置
      saveConfigToCache()
      ElMessage.success('删除成功')
    }
  })
}

// 批量删除任务
const deleteTasks = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请选择要删除的任务')
    return
  }
  
  ElMessageBox.confirm(`确定要删除选中的${selectedTasks.value.length}个任务吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 删除选中的任务
    selectedTasks.value.forEach(task => {
      const index = cleaningTasks.value.findIndex(item => item === task)
      if (index > -1) {
        cleaningTasks.value.splice(index, 1)
      }
    })
    selectedTasks.value = []
    ElMessage.success('删除成功')
  })
}

// 处理添加任务
const handleAddTask = (taskData: any) => {
  if (editTaskData.value) {
    // 编辑
    const index = editTaskData.value.index
    cleaningTasks.value[index] = taskData
  } else {
    // 新增
    cleaningTasks.value.push(taskData)
  }
  saveConfigToCache()
}

// 原始任务数据（空）
const originalCleaningTasks = ref([])

// 搜索任务
const searchTasks = () => {
  let filteredTasks = [...originalCleaningTasks.value]

  // 按任务名称搜索
  if (searchForm.value.taskName.trim()) {
    filteredTasks = filteredTasks.filter(task =>
      task.taskName.includes(searchForm.value.taskName.trim())
    )
  }

  // 按状态搜索
  if (searchForm.value.status) {
    // 这里可以根据实际状态字段进行过滤
    // 暂时保持所有数据，因为示例数据没有状态字段
  }

  cleaningTasks.value = filteredTasks
}

// 重置搜索
const resetSearch = () => {
  searchForm.value = {
    taskName: '',
    status: ''
  }
  // 重置为原始数据
  cleaningTasks.value = [...originalCleaningTasks.value]
}

// 返回
const goBack = () => {
  activeTab.value = 'rules'
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedTasks.value = selection
}

// 加载配置数据
const loadConfigFromCache = () => {
  try {
    const cached = localStorage.getItem(getStorageKey())
    if (cached) {
      const config = JSON.parse(cached)
      if (config.cleaningRules) {
        cleaningRules.value = config.cleaningRules
      }
      if (config.cleaningTasks) {
        cleaningTasks.value = config.cleaningTasks
      }
    }
  } catch (error) {
    console.error('加载清洗任务配置失败:', error)
  }
}

// 基于行ID查找已保存的配置
const findRowConfigByRowId = (rowId: number) => {
  try {
    const cached = localStorage.getItem(getRowConfigStorageKey())
    if (!cached) return null

    const configs = JSON.parse(cached)
    return configs.find((config: any) => config.rowId === rowId)
  } catch (error) {
    console.error('查找清洗规则行配置失败:', error)
    return null
  }
}

// 保存行配置数据
const saveRowConfigToCache = (rowData: any) => {
  if (!rowData?.id) {
    console.error('缺少行ID，无法保存清洗规则配置')
    return
  }

  try {
    const cached = localStorage.getItem(getRowConfigStorageKey())
    let configs = []

    if (cached) {
      configs = JSON.parse(cached)
    }

    const rowId = rowData.id
    const existingIndex = configs.findIndex((config: any) => config.rowId === rowId)

    const configData = {
      rowId: rowId,
      partitionName: rowData.partitionName,
      datasetName: rowData.datasetName,
      businessTable: rowData.businessTable,
      bindingStatus: rowData.bindingStatus,
      cleaningRules: cleaningRules.value,
      cleaningTasks: cleaningTasks.value,
      createTime: existingIndex >= 0 ? configs[existingIndex].createTime : new Date().toLocaleString(),
      updateTime: new Date().toLocaleString()
    }

    if (existingIndex >= 0) {
      // 更新现有配置
      configs[existingIndex] = configData
      console.log('更新清洗规则配置:', configData)
    } else {
      // 新增配置
      configs.push(configData)
      console.log('新增清洗规则配置:', configData)
    }

    localStorage.setItem(getRowConfigStorageKey(), JSON.stringify(configs))
  } catch (error) {
    console.error('保存清洗规则行配置失败:', error)
  }
}

// 保存配置数据
const saveConfigToCache = () => {
  try {
    const config = {
      cleaningRules: cleaningRules.value,
      cleaningTasks: cleaningTasks.value
    }
    localStorage.setItem(getStorageKey(), JSON.stringify(config))

    // 同时保存行配置
    if (props.editData) {
      saveRowConfigToCache(props.editData)
    }
  } catch (error) {
    console.error('保存清洗任务配置失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  activeTab.value = 'rules'
  searchForm.value = {
    taskName: '',
    status: ''
  }
  selectedTasks.value = []
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 确认保存（仅在清洗规则配置tab时显示）
const onConfirm = () => {
  loading.value = true
  setTimeout(() => {
    saveConfigToCache()
    ElMessage.success('清洗规则配置保存成功')
    visible.value = false
    loading.value = false
  }, 1000)
}

// 处理编辑数据的函数
const handleEditData = (editData: any) => {
  if (!editData) {
    // 如果没有编辑数据，重置为默认状态
    resetToDefaultState()
    return
  }

  console.log('数据清洗规则 - 处理编辑数据:', editData)

  // 首先尝试基于行ID查找已保存的配置
  if (editData.id) {
    const savedConfig = findRowConfigByRowId(editData.id)
    if (savedConfig) {
      console.log('找到已保存的清洗规则配置:', savedConfig)
      // 使用已保存的配置数据
      if (savedConfig.cleaningRules) {
        cleaningRules.value = [...savedConfig.cleaningRules] // 使用深拷贝
      } else {
        cleaningRules.value = []
      }
      if (savedConfig.cleaningTasks) {
        cleaningTasks.value = [...savedConfig.cleaningTasks] // 使用深拷贝
      } else {
        cleaningTasks.value = []
      }
      console.log('回显已保存的清洗规则配置:', cleaningRules.value)
      return
    }
  }

  // 如果没有找到已保存的配置，重置并生成默认配置
  console.log('未找到已保存配置，生成默认清洗规则配置')

  // 重置为默认状态
  resetToDefaultState()

  // 根据数据源类型和表格行数据生成智能描述和配置
  const isDataset = props.dataSourceType === 'dataset'

  if (isDataset) {
    // 数据集清洗规则
    const datasetName = editData.name || ''
    const datasetType = editData.type || ''
    const status = editData.status ? '启用' : '禁用'

    if (datasetName) {
      console.log(`数据集清洗规则 - 为数据集"${datasetName}"配置清洗规则`)

      // 根据状态设置不同的清洗策略
      if (status === '启用') {
        const enabledRules = [
          {
            index: 1,
            ruleName: `${datasetName}_数据集严格清洗`,
            ruleType: '数据验证',
            ruleDescription: `为启用状态的数据集"${datasetName}"配置严格的数据清洗规则`
          }
        ]
        cleaningRules.value = enabledRules
      } else {
        const basicRules = [
          {
            index: 1,
            ruleName: `${datasetName}_数据集基础清洗`,
            ruleType: '去除空白',
            ruleDescription: `为禁用状态的数据集"${datasetName}"配置基础的数据清洗规则`
          }
        ]
        cleaningRules.value = basicRules
      }
    }
  } else {
    // 数据源清洗规则（原有逻辑）
    const partitionName = editData.partitionName || ''
    const datasetName = editData.datasetName || ''
    const bindingStatus = editData.bindingStatus || ''

    if (partitionName && datasetName) {
      console.log(`数据清洗规则 - 为分区"${partitionName}"的数据集"${datasetName}"配置清洗规则`)

      // 根据绑定状态设置不同的清洗策略
      if (bindingStatus === '启用') {
        const enabledRules = [
          {
            index: 1,
            ruleName: `${partitionName}_严格清洗`,
            ruleType: '数据验证',
            ruleDescription: `为启用状态的${datasetName}配置严格的数据清洗规则`
          }
        ]
        cleaningRules.value = enabledRules
      } else {
        const basicRules = [
          {
            index: 1,
            ruleName: `${partitionName}_基础清洗`,
            ruleType: '去除空白',
            ruleDescription: `为禁用状态的${datasetName}配置基础的数据清洗规则`
          }
        ]
        cleaningRules.value = basicRules
      }
    }
  }

  console.log('设置清洗规则数据:', cleaningRules.value)
}

// 重置到默认状态
const resetToDefaultState = () => {
  // 重置清洗规则为空
  cleaningRules.value = []
  // 重置清洗任务为空
  cleaningTasks.value = []
  console.log('重置到默认状态')
}

// 监听弹窗打开
watch(() => props.modelValue, (newVal) => {
  console.log('数据清洗规则弹窗状态变化:', newVal, '编辑数据:', props.editData)
  if (newVal) {
    // 每次打开弹窗时先重置到默认状态
    resetToDefaultState()

    // 加载全局配置（如果有的话）
    loadConfigFromCache()

    // 使用nextTick确保DOM更新后再处理数据回显
    nextTick(() => {
      if (props.editData) {
        console.log('开始处理清洗规则数据回显:', props.editData)
        handleEditData(props.editData)
      } else {
        // 如果没有编辑数据，确保使用默认状态
        resetToDefaultState()
      }
    })
  }
})

// 监听编辑数据变化
watch(() => props.editData, (newEditData) => {
  console.log('清洗规则编辑数据变化:', newEditData)
  if (props.modelValue && newEditData) {
    handleEditData(newEditData)
  }
}, { deep: true })
</script>

<style lang="scss" scoped>
.data-cleaning-task-content {
  .cleaning-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 20px;
    }
  }
  
  .action-buttons {
    display: flex;
    gap: 10px;
  }
  
  .search-form {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    
    .el-form {
      margin-bottom: 0;
    }
  }
}
</style>
