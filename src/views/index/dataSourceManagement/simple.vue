<script setup lang="ts" name="dataSourceManagementSimple">
// 简化版本的数据源管理页面，用于调试

// 当前激活的Tab
const activeTab = ref('dataSource')

// Tab切换
const handleTabClick = (tab: any) => {
  activeTab.value = tab.name
}

// 新增按钮
const onClickAdd = () => {
  ElMessage.success('点击了新增按钮')
}
</script>

<template>
  <div class="data-source-management">
    <h1>数据源管理页面</h1>
    
    <!-- Tab切换 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="main-tabs">
      <el-tab-pane label="数据源" name="dataSource"></el-tab-pane>
      <el-tab-pane label="数据集" name="dataSet"></el-tab-pane>
      <el-tab-pane label="用户反馈" name="userFeedback"></el-tab-pane>
      <el-tab-pane label="接口类型" name="interfaceType"></el-tab-pane>
      <el-tab-pane label="数据接入" name="dataAccess"></el-tab-pane>
    </el-tabs>

    <!-- 数据源Tab内容 -->
    <div v-show="activeTab === 'dataSource'">
      <div class="content-area">
        <h2>数据源管理</h2>
        <el-button type="primary" @click="onClickAdd">新增</el-button>
        <p>这是数据源管理的内容区域</p>
      </div>
    </div>

    <!-- 其他Tab的占位内容 -->
    <div v-show="activeTab !== 'dataSource'" class="tab-placeholder">
      <el-empty description="该功能正在开发中..." />
    </div>
  </div>
</template>

<route>
{
  meta: {
    title: '数据源管理（简化版）',
  },
}
</route>

<style scoped lang="scss">
.data-source-management {
  padding: 20px;
  
  .main-tabs {
    margin-bottom: 20px;
  }
  
  .content-area {
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 4px;
  }

  .tab-placeholder {
    padding: 40px;
    text-align: center;
  }
}
</style>
