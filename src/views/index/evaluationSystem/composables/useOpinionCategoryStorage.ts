import { ref, reactive } from 'vue'

// 意见分类数据类型
export interface OpinionCategoryData {
  id: number
  categoryName: string
  subCategoryName: string
  priority: string
  operation: string
  createTime?: string
  updateTime?: string
}

// 本地存储key
const STORAGE_KEY = 'evaluation_opinion_categories'

export function useOpinionCategoryStorage() {
  const loading = ref(false)
  
  // 意见分类数据
  const opinionCategoryData = ref<OpinionCategoryData[]>([])
  
  // 从本地存储加载数据
  const loadData = () => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const parsed = JSON.parse(stored)
        opinionCategoryData.value = Array.isArray(parsed) ? parsed : []
      } else {
        // 初始化默认数据
        opinionCategoryData.value = [
          {
            id: 1,
            categoryName: '工作态度',
            subCategoryName: '积极主动',
            priority: '重要优先',
            operation: '编辑 删除',
            createTime: new Date().toISOString(),
            updateTime: new Date().toISOString()
          },
          {
            id: 2,
            categoryName: '工作态度',
            subCategoryName: '责任心强',
            priority: '常见',
            operation: '编辑 删除',
            createTime: new Date().toISOString(),
            updateTime: new Date().toISOString()
          },
          {
            id: 3,
            categoryName: '工作能力',
            subCategoryName: '专业技能',
            priority: '重要优先',
            operation: '编辑 删除',
            createTime: new Date().toISOString(),
            updateTime: new Date().toISOString()
          },
          {
            id: 4,
            categoryName: '工作能力',
            subCategoryName: '沟通协调',
            priority: '常见',
            operation: '编辑 删除',
            createTime: new Date().toISOString(),
            updateTime: new Date().toISOString()
          },
          {
            id: 5,
            categoryName: '工作效率',
            subCategoryName: '按时完成',
            priority: '重要优先',
            operation: '编辑 删除',
            createTime: new Date().toISOString(),
            updateTime: new Date().toISOString()
          },
          {
            id: 6,
            categoryName: '工作效率',
            subCategoryName: '质量保证',
            priority: '常见',
            operation: '编辑 删除',
            createTime: new Date().toISOString(),
            updateTime: new Date().toISOString()
          },
          {
            id: 7,
            categoryName: '团队合作',
            subCategoryName: '协作配合',
            priority: '常见',
            operation: '编辑 删除',
            createTime: new Date().toISOString(),
            updateTime: new Date().toISOString()
          },
          {
            id: 8,
            categoryName: '创新能力',
            subCategoryName: '改进建议',
            priority: '其他',
            operation: '编辑 删除',
            createTime: new Date().toISOString(),
            updateTime: new Date().toISOString()
          }
        ]
        saveData()
      }
    } catch (error) {
      console.error('加载意见分类数据失败:', error)
      opinionCategoryData.value = []
    }
  }
  
  // 保存数据到本地存储
  const saveData = () => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(opinionCategoryData.value))
    } catch (error) {
      console.error('保存意见分类数据失败:', error)
    }
  }
  
  // 新增意见分类
  const addOpinionCategory = (data: Omit<OpinionCategoryData, 'id' | 'createTime' | 'updateTime'>) => {
    const now = new Date().toISOString()
    const maxId = opinionCategoryData.value.length > 0 
      ? Math.max(...opinionCategoryData.value.map(item => item.id)) 
      : 0
    
    const newCategory: OpinionCategoryData = {
      ...data,
      id: maxId + 1,
      createTime: now,
      updateTime: now
    }
    
    opinionCategoryData.value.push(newCategory)
    saveData()
    return newCategory
  }
  
  // 更新意见分类
  const updateOpinionCategory = (id: number, data: Partial<Omit<OpinionCategoryData, 'id' | 'createTime'>>) => {
    const index = opinionCategoryData.value.findIndex(item => item.id === id)
    if (index !== -1) {
      opinionCategoryData.value[index] = {
        ...opinionCategoryData.value[index],
        ...data,
        updateTime: new Date().toISOString()
      }
      saveData()
      return opinionCategoryData.value[index]
    }
    return null
  }
  
  // 删除意见分类
  const deleteOpinionCategory = (id: number) => {
    const index = opinionCategoryData.value.findIndex(item => item.id === id)
    if (index !== -1) {
      const deleted = opinionCategoryData.value.splice(index, 1)[0]
      saveData()
      return deleted
    }
    return null
  }
  
  // 批量删除
  const batchDeleteOpinionCategories = (ids: number[]) => {
    const deleted = opinionCategoryData.value.filter(item => ids.includes(item.id))
    opinionCategoryData.value = opinionCategoryData.value.filter(item => !ids.includes(item.id))
    saveData()
    return deleted
  }
  
  // 搜索意见分类
  const searchOpinionCategories = (keyword: string) => {
    if (!keyword.trim()) {
      return opinionCategoryData.value
    }
    
    return opinionCategoryData.value.filter(item => 
      item.categoryName.toLowerCase().includes(keyword.toLowerCase()) ||
      item.subCategoryName.toLowerCase().includes(keyword.toLowerCase()) ||
      item.priority.toLowerCase().includes(keyword.toLowerCase())
    )
  }
  
  // 初始化加载数据
  loadData()
  
  return {
    opinionCategoryData,
    loading,
    addOpinionCategory,
    updateOpinionCategory,
    deleteOpinionCategory,
    batchDeleteOpinionCategories,
    searchOpinionCategories,
    loadData,
    saveData
  }
}
