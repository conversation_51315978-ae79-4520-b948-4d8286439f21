import { ref, reactive, computed, onMounted } from 'vue'

// 评价体系数据接口
export interface EvaluationData {
  id: string
  department: string // 审批部门
  role: string // 审批角色
  category: '常用' | '其他' // 分类
  content: string // 评价语句
  createTime: string
  updateTime: string
}

// 本地存储key
const STORAGE_KEY = 'evaluation_system_data'

// 生成模拟数据
const generateMockData = (): EvaluationData[] => {
  const departments = ['财政部', '人事部', '审计部', '法务部', '行政部']
  const roles = ['主任', '副主任', '科长', '副科长', '主办']
  const categories: ('常用' | '其他')[] = ['常用', '其他']
  const contents = [
    '审批流程规范，处理及时高效',
    '材料审核仔细，意见建议中肯',
    '业务熟练，专业水平较高',
    '工作态度认真负责，服务意识强',
    '审批意见明确，指导性强',
    '处理问题思路清晰，解决方案合理',
    '沟通协调能力强，配合度高',
    '严格按照规定执行，程序合规',
    '响应速度快，办事效率高',
    '专业知识扎实，判断准确',
    '服务态度良好，耐心细致',
    '创新意识强，工作方法灵活',
    '团队协作精神好，配合默契',
    '质量控制严格，标准执行到位',
    '风险意识强，预防措施得当',
    '学习能力强，业务水平不断提升',
    '工作计划性强，时间安排合理',
    '问题分析透彻，解决方案可行',
    '执行力强，落实措施有力',
    '廉洁自律，作风正派'
  ]

  const data: EvaluationData[] = []
  const now = new Date()

  for (let i = 0; i < 25; i++) {
    const department = departments[Math.floor(Math.random() * departments.length)]
    const role = roles[Math.floor(Math.random() * roles.length)]
    const category = categories[Math.floor(Math.random() * categories.length)]
    const content = contents[Math.floor(Math.random() * contents.length)]
    
    data.push({
      id: `eval_${Date.now()}_${i}`,
      department,
      role,
      category,
      content,
      createTime: new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      updateTime: new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
    })
  }

  return data
}

export const useEvaluationSystemStorage = () => {
  // 响应式数据
  const evaluationData = ref<EvaluationData[]>([])
  const loading = ref(false)

  // 从localStorage加载数据
  const loadData = () => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        evaluationData.value = JSON.parse(stored)
      } else {
        // 如果没有存储数据，生成模拟数据
        evaluationData.value = generateMockData()
        saveData()
      }
    } catch (error) {
      console.error('加载评价体系数据失败:', error)
      evaluationData.value = generateMockData()
      saveData()
    }
  }

  // 保存数据到localStorage
  const saveData = () => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(evaluationData.value))
    } catch (error) {
      console.error('保存评价体系数据失败:', error)
    }
  }

  // 新增评价数据
  const addEvaluation = (data: Omit<EvaluationData, 'id' | 'createTime' | 'updateTime'>) => {
    const now = new Date().toISOString()
    const newEvaluation: EvaluationData = {
      ...data,
      id: `eval_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createTime: now,
      updateTime: now
    }
    
    evaluationData.value.unshift(newEvaluation)
    saveData()
    return newEvaluation
  }

  // 更新评价数据
  const updateEvaluation = (id: string, data: Partial<Omit<EvaluationData, 'id' | 'createTime'>>) => {
    const index = evaluationData.value.findIndex(item => item.id === id)
    if (index !== -1) {
      evaluationData.value[index] = {
        ...evaluationData.value[index],
        ...data,
        updateTime: new Date().toISOString()
      }
      saveData()
      return evaluationData.value[index]
    }
    return null
  }

  // 删除评价数据
  const deleteEvaluation = (id: string) => {
    const index = evaluationData.value.findIndex(item => item.id === id)
    if (index !== -1) {
      const deleted = evaluationData.value.splice(index, 1)[0]
      saveData()
      return deleted
    }
    return null
  }

  // 批量删除
  const batchDeleteEvaluations = (ids: string[]) => {
    const deleted = evaluationData.value.filter(item => ids.includes(item.id))
    evaluationData.value = evaluationData.value.filter(item => !ids.includes(item.id))
    saveData()
    return deleted
  }

  // 搜索评价数据
  const searchEvaluations = (department?: string, role?: string) => {
    let filtered = evaluationData.value

    if (department && department.trim()) {
      filtered = filtered.filter(item => 
        item.department.toLowerCase().includes(department.toLowerCase())
      )
    }

    if (role && role.trim()) {
      filtered = filtered.filter(item => 
        item.role.toLowerCase().includes(role.toLowerCase())
      )
    }

    return filtered
  }

  // 根据分类筛选
  const filterByCategory = (category: '常用' | '其他') => {
    return evaluationData.value.filter(item => item.category === category)
  }

  // 清空所有数据
  const clearAllData = () => {
    evaluationData.value = []
    saveData()
  }

  // 重置为模拟数据
  const resetToMockData = () => {
    evaluationData.value = generateMockData()
    saveData()
  }

  // 导出数据
  const exportData = () => {
    return JSON.stringify(evaluationData.value, null, 2)
  }

  // 导入数据
  const importData = (jsonData: string) => {
    try {
      const imported = JSON.parse(jsonData) as EvaluationData[]
      if (Array.isArray(imported)) {
        evaluationData.value = imported
        saveData()
        return true
      }
      return false
    } catch (error) {
      console.error('导入数据失败:', error)
      return false
    }
  }

  // 统计信息
  const statistics = computed(() => {
    const total = evaluationData.value.length
    const categoryStats = evaluationData.value.reduce((acc, item) => {
      acc[item.category] = (acc[item.category] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const departmentStats = evaluationData.value.reduce((acc, item) => {
      acc[item.department] = (acc[item.department] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return {
      total,
      categoryStats,
      departmentStats
    }
  })

  // 组件挂载时加载数据
  onMounted(() => {
    loadData()
  })

  return {
    evaluationData: readonly(evaluationData),
    loading,
    statistics,
    addEvaluation,
    updateEvaluation,
    deleteEvaluation,
    batchDeleteEvaluations,
    searchEvaluations,
    filterByCategory,
    clearAllData,
    resetToMockData,
    exportData,
    importData,
    loadData,
    saveData
  }
}
