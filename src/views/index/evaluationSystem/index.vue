<script setup lang="ts" name="evaluationSystem">
import { useEvaluationSystemStorage } from './composables/useEvaluationSystemStorage'
import { useOpinionCategoryStorage } from './composables/useOpinionCategoryStorage'

// 使用本地存储 composable
const {
  evaluationData,
  loading,
  addEvaluation,
  updateEvaluation,
  deleteEvaluation,
  searchEvaluations
} = useEvaluationSystemStorage()

// 使用意见分类存储 composable
const {
  opinionCategoryData,
  addOpinionCategory,
  updateOpinionCategory,
  deleteOpinionCategory
} = useOpinionCategoryStorage()

// 表格ref
const tableRef = ref()
const tableHeight = ref(0) // 表格高度

// 当前行数据
const currentRow = ref<any>(null)

// 操作按钮配置 - BaseTableComp格式
const buttons = [
  { title: '编辑', type: 'primary', code: 'edit', verify: 'true' },
  { title: '删除', type: 'danger', code: 'delete', verify: 'true' }
]

// 表格列配置 - BaseTableComp格式
const columns = [
  { field: 'department', title: '审批部门', align: 'center', width: '140px' },
  { field: 'role', title: '审批角色', align: 'center', width: '140px' },
  { field: 'category', title: '分类', align: 'center', width: '100px' },
  { field: 'content', title: '评价语句', align: 'left', minWidth: '200px' },
]

// 分页配置
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
})



// 弹窗相关
const showDialogForm = ref(false)
const dialogFormRef = ref()

// 意见分类弹窗相关
const showOpinionCategoryDialog = ref(false)
const editingRowId = ref<number | null>(null)

// 弹窗表单数据
const dialogForm = ref({
  department: '',
  role: '',
  category: '常用' as '常用' | '其他',
  content: ''
})

// 弹窗表单属性
const dialogFormProps = ref([
  {
    label: '审批部门',
    prop: 'department',
    type: 'select',
    options: [
      { label: '财政部', value: '财政部' },
      { label: '人事部', value: '人事部' },
      { label: '审计部', value: '审计部' },
      { label: '法务部', value: '法务部' },
      { label: '行政部', value: '行政部' }
    ]
  },
  {
    label: '审批角色',
    prop: 'role',
    type: 'select',
    options: [
      { label: '主任', value: '主任' },
      { label: '副主任', value: '副主任' },
      { label: '科长', value: '科长' },
      { label: '副科长', value: '副科长' },
      { label: '主办', value: '主办' }
    ]
  },
  {
    label: '分类',
    prop: 'category',
    type: 'select',
    options: [
      { label: '常用', value: '常用' },
      { label: '其他', value: '其他' }
    ]
  },
  {
    label: '评价语句',
    prop: 'content',
    type: 'textarea',
    rows: 4
  }
])

// 弹框表单校验规则
const dialogFormRules = {
  department: [{ required: true, message: '请选择审批部门', trigger: 'change' }],
  role: [{ required: true, message: '请选择审批角色', trigger: 'change' }],
  category: [{ required: true, message: '请选择分类', trigger: 'change' }],
  content: [{ required: true, message: '请输入评价语句', trigger: 'blur' }]
}

// 计算分页数据
const paginatedData = computed(() => {
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  // 移除搜索功能，直接显示所有数据
  const filtered = evaluationData.value
  pagination.total = filtered.length
  return filtered.slice(start, end)
})



// 表格操作点击事件 - BaseTableComp格式
const onTableClickButton = ({ btn, scope }: any) => {
  const row = scope // BaseTableComp传递的是scope，实际就是row数据

  if (btn.code === 'edit') {
    currentRow.value = row
    // 确保复制所有字段，包括id
    dialogForm.value = {
      department: row.department || '',
      role: row.role || '',
      category: row.category || '常用',
      content: row.content || ''
    }
    showDialogForm.value = true
  } else if (btn.code === 'delete') {
    deleteEvaluation(row.id)
    ElMessage.success('删除成功')
  }
}

// 新增
const onClickAdd = () => {
  currentRow.value = null
  dialogForm.value = {
    department: '',
    role: '',
    category: '常用' as '常用' | '其他',
    content: ''
  }
  showDialogForm.value = true
}

// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type === 'page') {
    pagination.page = val
  } else {
    pagination.size = val
    pagination.page = 1
  }
}

// 块高度变化事件时修改表格高度
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 75
}

// 弹框表单提交
const onDialogConfirm = () => {
  dialogFormRef.value.validate((valid: boolean) => {
    if (valid) {
      if (currentRow.value && currentRow.value.id) {
        // 编辑
        updateEvaluation(currentRow.value.id, dialogForm.value)
        ElMessage.success('编辑成功')
      } else {
        // 新增
        addEvaluation(dialogForm.value)
        ElMessage.success('新增成功')
      }
      showDialogForm.value = false
    }
  })
}

// 意见分类相关函数
const onClickOpinionCategory = () => {
  showOpinionCategoryDialog.value = true
}

const onAddOpinionCategory = () => {
  const newCategory = addOpinionCategory({
    categoryName: '',
    subCategoryName: '',
    priority: '',
    operation: '编辑 删除'
  })
  editingRowId.value = newCategory.id
}

const onEditOpinionCategory = (row: any) => {
  editingRowId.value = row.id
}

const onDeleteOpinionCategory = (row: any) => {
  const deleted = deleteOpinionCategory(row.id)
  if (deleted) {
    ElMessage.success('删除成功')
  } else {
    ElMessage.error('删除失败')
  }
}

const onSaveOpinionCategory = (row: any) => {
  const updated = updateOpinionCategory(row.id, {
    categoryName: row.categoryName,
    subCategoryName: row.subCategoryName,
    priority: row.priority,
    operation: row.operation
  })

  if (updated) {
    editingRowId.value = null
    ElMessage.success('保存成功')
  } else {
    ElMessage.error('保存失败')
  }
}

const onCancelEditOpinionCategory = () => {
  editingRowId.value = null
}
</script>

<template>
  <div class="evaluation-system-container">
    <div class="evaluation-system">
    <Block title="评价体系" :enable-fixed-height="true" :enable-expand="false" @height-changed="onBlockHeightChanged">
      <template #topRight>
        <el-button size="small" type="info" @click="onClickOpinionCategory" style="margin-right: 8px;">意见分类</el-button>
        <el-button size="small" type="primary" @click="onClickAdd">新增</el-button>
      </template>

      <!-- 列表 -->
      <BaseTableComp
        ref="tableRef"
        :data="paginatedData"
        :colData="columns"
        :buttons="buttons"
        :loading="loading"
        :auto-height="true"
        :show-border="true"
        :visibleHeader="false"
        :visibleSetting="false"
        :visibleIndex="true"
        :currentPage="pagination.page"
        :pageSize="pagination.size"
        :total="pagination.total"
        @clickButton="onTableClickButton"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      />

    </Block>

    <!-- 新增/编辑弹窗 -->
    <Dialog
      v-model="showDialogForm"
      :title="currentRow ? '编辑评价体系' : '评价体系设置'"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      @closed="currentRow = null"
      @click-confirm="onDialogConfirm"
    >
      <Form
        ref="dialogFormRef"
        v-model="dialogForm"
        :props="dialogFormProps"
        :rules="dialogFormRules"
        :enable-button="false"
      />
    </Dialog>

    <!-- 意见分类弹窗 -->
    <Dialog
      v-model="showOpinionCategoryDialog"
      title="意见分类"
      width="800px"
      :enable-confirm="false"
      :destroy-on-close="true"
    >
      <div class="opinion-category-content">
        <!-- 新增按钮 -->
        <div class="category-header">
          <el-button size="small" type="primary" @click="onAddOpinionCategory">新增</el-button>
        </div>

        <!-- 表格 -->
        <el-table :data="opinionCategoryData" border style="width: 100%; margin-top: 16px;">
          <el-table-column label="分类名称" width="150">
            <template #default="{ row }">
              <el-input
                v-if="editingRowId === row.id"
                v-model="row.categoryName"
                size="small"
                placeholder="请输入分类名称"
              />
              <span v-else>{{ row.categoryName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="子类名称" width="150">
            <template #default="{ row }">
              <el-input
                v-if="editingRowId === row.id"
                v-model="row.subCategoryName"
                size="small"
                placeholder="请输入子类名称"
              />
              <span v-else>{{ row.subCategoryName }}</span>
            </template>
          </el-table-column>

          <el-table-column label="重要优先" width="200">
            <template #default="{ row }">
              <div v-if="editingRowId === row.id" class="priority-buttons">
                <el-select v-model="row.priority" size="small" style="width: 100%;">
                  <el-option label="重要优先" value="重要优先" />
                  <el-option label="常见" value="常见" />
                  <el-option label="其他" value="其他" />
                </el-select>
              </div>
              <span v-else>{{ row.priority }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200">
            <template #default="{ row }">
              <div v-if="editingRowId === row.id" class="operation-buttons">
                <el-button size="small" type="success" @click="onSaveOpinionCategory(row)">保存</el-button>
                <el-button size="small" @click="onCancelEditOpinionCategory">取消</el-button>
              </div>
              <div v-else class="operation-buttons">
                <el-button size="small" type="primary" @click="onEditOpinionCategory(row)">编辑</el-button>
                <el-button size="small" type="danger" @click="onDeleteOpinionCategory(row)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 底部按钮 -->
        <div class="category-footer">
          <el-button @click="showOpinionCategoryDialog = false">取消</el-button>
          <el-button type="primary" @click="showOpinionCategoryDialog = false">提交</el-button>
        </div>
      </div>
    </Dialog>
    </div>
  </div>
</template>

<route>
{
  meta: {
    title: '评价体系',
  },
}
</route>

<style scoped lang="scss">
.evaluation-system-container {
  width: 100%;
  height: 100vh;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.evaluation-system {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;

  // 优化表格布局，减少留白
  :deep(.base-table-comp) {
    width: 100%;
    margin: 0;

    .el-table {
      width: 100% !important;
      margin: 0;
    }

    .el-table .el-table__cell {
      padding: 6px 8px;
    }

    // 确保表格充满容器
    .el-table__body-wrapper {
      width: 100%;
    }

    // 优化分页样式
    .el-pagination {
      margin-top: 12px;
      padding: 0;
    }
  }

  // 确保Block组件充满空间
  :deep(.block-component) {
    width: 100%;
    height: 100%;
    margin: 0;

    .block-content {
      padding: 12px;
    }

    .block-header {
      padding: 8px 12px;

      .block-title {
        margin: 0;
      }
    }
  }
}

.search {
  margin-bottom: 12px;
}

// 意见分类弹窗样式
.opinion-category-content {
  .category-header {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 16px;
  }

  .priority-buttons {
    display: flex;
    gap: 8px;

    .el-button {
      padding: 4px 8px;
      font-size: 12px;
    }
  }

  .operation-buttons {
    display: flex;
    gap: 8px;

    .el-button {
      padding: 4px 8px;
      font-size: 12px;
    }
  }

  .category-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
  }

  .el-table {
    .el-input {
      width: 100%;
    }
  }
}
</style>
