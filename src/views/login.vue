<script setup lang="ts">
import {ElMessage} from 'element-plus'
import {FormInstance} from 'element-plus/es/components/form'
import {inject, onMounted, reactive, ref} from 'vue'
import {useRouter} from 'vue-router'
import {useUserStore} from '$/useUserStore'
import ParticleBackground from '@/components/common/ParticleBackground.vue'
import {UserFilled, Lock} from '@element-plus/icons-vue'
import CryptoJS from 'crypto-js'
import {ElNotification} from 'element-plus'

const router = useRouter()
const axios = inject('#axios') as any
const useUser = useUserStore()

const ruleForm = reactive({
	name: '',
	pass: '',
})

const getCurrentUser = () => {
	return new Promise((resolve, reject) => {
		axios
			?.get('/api/filling/staff/current-staff')
			.then((res: any) => {
				if (res.status === 200) {
					localStorage.setItem('currentUserInfo', JSON.stringify(res.data))
					useUser.userInfo = res.data
					useUser.casualUser = res.data
					ElMessage.success({
						message: '登录成功',
						duration: 2000,
					})
					resolve('')
				}
			})
			.catch(() => resolve(''))
	})
}
const getCurrentUserInfo = () => {
	return new Promise((resolve, reject) => {
		axios
			.request({
				method: 'get',
				url: '/api/platform/user/lite-current-user-info',
				headers: {
					Urlkey: 'iframeCode',
				},
			})
			.then((res: any) => {
				if (res.status === 200) {
					res.data.account = res.data.userName
					res.data.staffRole = res.data.roles.map((v: any) => v.name)
					localStorage.setItem('currentUserInfo', JSON.stringify(res.data))

					localStorage.setItem('currentPlatformInfo', JSON.stringify(res.data))
					useUser.platformInfo = res.data
					useUser.userInfo = res.data
					useUser.casualUser = res.data
					ElMessage.success({
						message: '登录成功',
						duration: 2000,
					})
					if (res.data.departments.length !== 0) {
						if (localStorage.getItem('currentDepartmentInfo') === null) {
							localStorage.setItem(
								'currentDepartmentInfo',
								JSON.stringify(res.data.departments[0])
							)
						}
					} else {
						ElMessage.warning('当前用户尚未绑定任何部门！')
					}
					router.push('/home/<USER>')
				}
				resolve('')
			})
			.catch(() => resolve(''))
	})
}
function stringEncipher(string: string) {
	const key = CryptoJS.enc.Base64.parse('YpBxiB+V2YfH5kh/ejho/5DgLdAcDUXTlYiweJkkGRA=')
	// var key = CryptoJS.enc.Base64.parse('MTIzNDU2Nzg5MDEyMzQ1Ng==');
	const iv = CryptoJS.enc.Base64.parse('xdORzptR6wCaugm+E+kDpw==')
	return CryptoJS.AES.encrypt(string, key, {iv: iv}).toString()
}
const login = () => {
	const bytes = CryptoJS.AES.decrypt(
		import.meta.env.VITE_CLIENT_SECRET,
		import.meta.env.VITE_SECRE_KEY
	)
	const clientSecret = bytes.toString(CryptoJS.enc.Utf8)

	const e = stringEncipher(
		JSON.stringify({
			clientId: 'vue-admin-element',
			clientSecret,
			grantType: 'password',
			userName: ruleForm.name,
			password: ruleForm.pass,
			Scope: 'inspur-abp-application inspur-ledger offline_access',
		})
	)
	// 获取用户数据
	axios
		?.login('/api/sso/auth/access-token', {e})
		.then(async (res: any) => {
			if (res.status === 200) {
				// localStorage.setItem('access_token', res.data.accessToken)
				// localStorage.setItem('refresh_token', res.data.refreshToken)
				useUser.setToken(res.data.accessToken, res.data.refreshToken)
				// useUser.token = res.data.accessToken
				ElMessage.success({
					message: '登录成功',
					duration: 2000,
				})
				router.push('/')

				// await getCurrentUser()
				// await getCurrentUserInfo()

				// router.push('/')
			}
		})
		.catch((err: any) => {
			if (err.response?.status === 500) {
				ElNotification.error('当前系统用户在线量较多，请5分钟后再试')
			}
		})
}
const submitForm = () => {
	if (ruleForm.name === '') {
		ElMessage.error({
			message: '请输入用户名',
			duration: 2000,
		})
		return
	}
	if (ruleForm.pass === '') {
		ElMessage.error({
			message: '请输入密码',
			duration: 2000,
		})
		return
	}
	login()
}

onMounted(() => {
	const access_token = window.localStorage.getItem('access_token')
	if (access_token) router.push('/ledgerConfig')
})
</script>
<template>
	<div class="login">
		<ParticleBackground />
		<img class="bg" src="../assets/image/login-bg.png" alt="" />
		<div class="login-layout">
			<div class="box">
				<div class="login-img">
					<img src="../assets/image/title.png" alt="" />
				</div>
				<div class="login-title">“一表通”智能报表</div>
				<div class="login-form">
					<el-input
						v-model.trim="ruleForm.name"
						@keyup.enter="submitForm()"
						:prefix-icon="UserFilled"
						placeholder="请输入账号"
					>
					</el-input>
					<el-input
						v-model.trim="ruleForm.pass"
						@keyup.enter="submitForm()"
						:prefix-icon="Lock"
						type="password"
						autocomplete="off"
						placeholder="请输入密码"
					>
					</el-input>
					<el-button class="btn" type="primary" size="large" @click="submitForm()"
						>登录</el-button
					>
				</div>
			</div>
		</div>
	</div>
</template>
<style lang="scss" scoped>
.login {
	height: 100%;
	width: 100%;

	.bg {
		height: 100%;
		left: 0;
		position: absolute;
		top: 0;
		width: 100%;
		z-index: 1;
	}

	.login-layout {
		align-items: center;
		display: flex;
		left: 0;
		flex-direction: column;
		justify-content: center;
		height: 100%;
		position: absolute;
		top: 0;
		width: 100%;
		z-index: 3;
	}

	.login-img {
		height: 130px;
		width: 250px;
		img {
			height: 100%;
			width: 100%;
		}
	}

	.login-title {
		color: #fff;
		font-size: 20px;
		margin-top: 10px;
	}

	.login-form {
		height: 200px;
		padding-top: 30px;
		width: 350px;
	}

	.btn {
		margin-top: 20px;
		width: 100%;
	}

	.box {
		align-items: center;
		background-color: rgba(152, 199, 255, 0.25);
		display: flex;
		flex-direction: column;
		height: 400px;
		justify-content: center;
		width: 400px;
	}

	.el-input {
		height: 40px;
		margin-bottom: 20px;
	}

	button {
		height: 40px;
	}

	.el-input__inner {
		border: none;
	}

	:deep(.el-input__inner) {
		border: 0;
		color: #fff;
	}

	:deep(.el-input__wrapper) {
		background-color: rgba(255, 255, 255, 0.247);
	}

	:deep(.el-textarea__inner) {
		box-shadow: 0 0 0 0px;
	}

	:deep(.el-textarea__inner:hover) {
		box-shadow: 0 0 0 0px;
	}

	:deep(.el-textarea__inner:focus) {
		box-shadow: 0 0 0 0px;
	}

	.box {
		border-radius: 5px;
		box-shadow: 0 0 100px 0 rgba(0, 0, 0, 0.8);
	}
}
</style>
