<script setup lang="ts" name="foreignKeyDefinition">
import { useForeignKeyDefinitionStore, FOREIGN_KEY_TYPES, BUSINESS_TABLES, RELATION_TYPES, type ForeignKeyDefinition, type ForeignKeyBusinessTableRelation } from '@/stores/foreignKeyDefinition'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown, Search } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { nextTick, watch } from 'vue'

// 使用store
const foreignKeyStore = useForeignKeyDefinitionStore()

// 搜索表单配置
const searchFormProp = ref([
  { label: '外键名称', prop: '外键名称', type: 'text', placeholder: '请输入外键名称' },
  { label: '外键类型', prop: '外键类型', type: 'select', options: FOREIGN_KEY_TYPES, placeholder: '请选择外键类型' },
  { label: '最小长度', prop: '最小长度范围', type: 'text', placeholder: '请输入最小长度' },
  { label: '最大长度', prop: '最大长度范围', type: 'text', placeholder: '请输入最大长度' }
])

// 搜索表单数据
const searchForm = ref({
  外键名称: '',
  外键类型: '',
  最小长度范围: '',
  最大长度范围: ''
})

// 加载状态
const loading = ref(false)

// 表格相关
const tableRef = ref()
const tableHeight = ref(0)
const currentRow = ref<ForeignKeyDefinition | null>(null)

// 批量修改相关
const selectedRows = ref<ForeignKeyDefinition[]>([])
const showBatchDialog = ref(false)
const batchDialogRef = ref()
const batchLoading = ref(false)

// 业务表关联相关
const showBusinessTableDialog = ref(false)
const businessTableDialogRef = ref()
const businessTableLoading = ref(false)
const businessTableForm = ref({
  外键: '',
  业务表: '',
  关联字段: '',
  关联类型: ''
})

// 外键关联提示相关
const showForeignKeyTipsDialog = ref(false)
const foreignKeyTipsDialogRef = ref()
const foreignKeyTipsForm = ref({
  提示类型: '成功提示',
  提示内容: ''
})

// 外键错误预警相关
const showErrorWarningDialog = ref(false)
const errorWarningDialogRef = ref()
const errorWarningForm = ref({
  启用错误预警: false,
  错误阈值: '',
  通知方式: []
})

// 外键验证报告相关
const showValidationReportDialog = ref(false)
const validationReportData = ref({
  statistics: {
    有效关联: 0,
    无效关联: 0,
    告警: 0
  },
  validationResults: []
})

// 验证报告分页相关
const validationReportPagination = ref({
  currentPage: 1,
  pageSize: 5,
  total: 0
})

// 验证报告分页Loading状态
const validationReportLoading = ref(false)

// 外键过滤条件相关
const showFilterDialog = ref(false)
const filterForm = ref({
  field: '外键名称',
  operator: '等于',
  value: ''
})

// 原始数据存储（用于过滤功能）
const originalTableData = ref([])

// 当前页显示的验证结果
const currentPageValidationResults = computed(() => {
  const start = (validationReportPagination.value.currentPage - 1) * validationReportPagination.value.pageSize
  const end = start + validationReportPagination.value.pageSize
  return validationReportData.value.validationResults.slice(start, end)
})

// 外键锁定相关
const showForeignKeyLockDialog = ref(false)
const foreignKeyLockForm = ref({
  锁定时间: '',
  时: '',
  分: '',
  秒: ''
})
const currentLockRow = ref<ForeignKeyDefinition | null>(null)

// 外键解锁修改相关
const showForeignKeyUnlockDialog = ref(false)
const foreignKeyUnlockForm = ref({
  解绑模式: '',
  生效时间: '',
  恢复时间: '',
  数据处理方式: ''
})
const currentUnlockRow = ref<ForeignKeyDefinition | null>(null)

// 外键历史记录相关
const showHistoryDialog = ref(false)
const historyLoading = ref(false)
const historyData = ref([])
const currentHistoryRow = ref<ForeignKeyDefinition | null>(null)
const historyPagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 外键关联默认值相关
const showDefaultValueDialog = ref(false)
const defaultValueDialogRef = ref()
const defaultValueForm = ref({
  外键认证: false,
  默认值: '',
  默认值应用策略: '新增时应用'
})
const currentDefaultValueRow = ref<ForeignKeyDefinition | null>(null)

// 外键规则设置相关
const showForeignKeyRulesDialog = ref(false)
const foreignKeyRulesDialogRef = ref()
const foreignKeyRulesForm = ref({
  // 外键有效性验证
  唯一性约束: false,
  允许NULL值: false,

  // 外键默认关联策略
  默认串联类型: '',
  更新约束力: '',

  // 外键自定义规则
  启用自定义规则: false,
  正则表达式: '',
  最小长度: '',
  最大长度: '',

  // 外键过期设置
  开启外键过期时间: false,
  过期时间: '',
  过期后行为: '',

  // 搜索性能规则设置
  索引规则: '不自动创建',
  性能阈值设置: '',
  缓存控制: false,

  // 外键关联条件设置
  外键关联条件设置: '',
  关联定时间: ''
})

// 存储配置的提示信息
const savedTipsConfig = ref({
  提示类型: '成功提示',
  提示内容: ''
})

// 外键关系错误分析相关
interface ErrorAnalysisResult {
  id: string
  foreignKeyName: string
  errorType: string
  description: string
}

const showForeignKeyErrorDialog = ref(false)
const errorAnalysisLoading = ref(false)
const errorAnalysisResults = ref<ErrorAnalysisResult[]>([])

// 外键关联自动修复相关
const showAutoRepairDialog = ref(false)
const autoRepairDialogRef = ref()
const autoRepairForm = ref({
  启用自动修复: false,
  当检测到无效关联时自动尝试修复: false,
  修复策略: '删除无效记录',
  修复频率: '每天',
  自动修复开关: false
})
const currentAutoRepairRow = ref<ForeignKeyDefinition | null>(null)

// 外键权限管理相关
const showPermissionDialog = ref(false)
const permissionDialogRef = ref()
const permissionForm = ref({
  授权人员: [] as string[],
  操作权限: {
    查看: false,
    编辑: false,
    删除: false,
    新增: false
  }
})
const currentPermissionRow = ref<ForeignKeyDefinition | null>(null)
const authorizedPersonnelList = ref<Array<{name: string, selected: boolean}>>([])

// 人员列表分页相关
const personnelPagination = ref({
  currentPage: 1,
  pageSize: 5,
  total: 0
})
const personnelLoading = ref(false)
const allPersonnelList = ref<Array<{name: string, selected: boolean}>>([])

// 人员搜索相关
const personnelSearchKeyword = ref('')
const filteredPersonnelList = ref<Array<{name: string, selected: boolean}>>([])

// 获取所有创建人姓名作为授权人员候选
const getAllCreatorNames = (): string[] => {
  const creators = foreignKeyStore.foreignKeys.map(fk => fk.创建人)
  return [...new Set(creators)].filter(name => name && name.trim())
}

// 初始化完整人员列表
const initializePersonnelList = () => {
  const creatorNames = getAllCreatorNames()
  allPersonnelList.value = creatorNames.map(name => ({
    name,
    selected: false
  }))
  // 初始化时显示所有人员
  filteredPersonnelList.value = [...allPersonnelList.value]
  personnelPagination.value.total = filteredPersonnelList.value.length
}

// 执行人员搜索过滤
const filterPersonnelList = () => {
  const keyword = personnelSearchKeyword.value.trim().toLowerCase()

  if (!keyword) {
    // 没有搜索关键词时显示所有人员
    filteredPersonnelList.value = [...allPersonnelList.value]
  } else {
    // 根据姓名进行模糊匹配
    filteredPersonnelList.value = allPersonnelList.value.filter(person =>
      person.name.toLowerCase().includes(keyword)
    )
  }

  // 更新分页信息并重置到第一页
  personnelPagination.value.total = filteredPersonnelList.value.length
  personnelPagination.value.currentPage = 1

  // 更新当前页显示
  updateCurrentPagePersonnel()
}

// 获取当前页的人员列表
const getCurrentPagePersonnel = () => {
  const start = (personnelPagination.value.currentPage - 1) * personnelPagination.value.pageSize
  const end = start + personnelPagination.value.pageSize
  return filteredPersonnelList.value.slice(start, end)
}

// 更新当前页显示的人员列表
const updateCurrentPagePersonnel = () => {
  authorizedPersonnelList.value = getCurrentPagePersonnel()
}

// 清空搜索
const clearPersonnelSearch = () => {
  personnelSearchKeyword.value = ''
  filterPersonnelList()
}

// 加载人员数据（带Loading效果）
const loadPersonnelData = async (foreignKeyId: string) => {
  try {
    // 确保Loading状态已设置
    personnelLoading.value = true

    // 模拟网络请求延迟，增加真实性（延长时间让Loading更明显）
    await new Promise(resolve => setTimeout(resolve, 1800))

    // 初始化完整人员列表
    initializePersonnelList()

    // 重置分页到第一页
    personnelPagination.value.currentPage = 1

    // 重置搜索
    personnelSearchKeyword.value = ''

    // 加载已保存的权限配置
    const savedPermissionConfig = localStorage.getItem(`permissionConfig_${foreignKeyId}`)
    if (savedPermissionConfig) {
      try {
        const config = JSON.parse(savedPermissionConfig)
        permissionForm.value = {
          ...permissionForm.value,
          ...config
        }
        // 更新所有人员的选择状态
        allPersonnelList.value.forEach(person => {
          person.selected = config.授权人员?.includes(person.name) || false
        })
      } catch (error) {
        console.error('解析保存的权限配置失败:', error)
      }
    } else {
      // 重置表单
      permissionForm.value = {
        授权人员: [],
        操作权限: {
          查看: false,
          编辑: false,
          删除: false,
          新增: false
        }
      }
      // 重置所有人员选择状态
      allPersonnelList.value.forEach(person => {
        person.selected = false
      })
    }

    // 更新当前页显示的人员列表
    updateCurrentPagePersonnel()

  } catch (error) {
    console.error('加载人员数据失败:', error)
    ElMessage.error('加载人员数据失败，请重试')
  } finally {
    personnelLoading.value = false
  }
}

// 外键数据统计相关
const showForeignKeyStatisticsDialog = ref(false)
const statisticsChartRef = ref()
const statisticsData = ref({
  外键总数: 0,
  唯一外键数: 0,
  无效外键: 0,
  关系类型分布: [] as Array<{ name: string, value: number }>
})

// 表格按钮配置 - TableV2不支持more属性，使用自定义操作列模板
const buttons = [
  { label: '详情', type: 'info', code: 'detail' },
  { label: '修改', type: 'warning', code: 'edit' },
  { label: '删除', type: 'danger', code: 'delete', popconfirm: '确认删除吗?' },
  { label: '外键锁定', type: 'primary', code: 'primary', more: true },
  { label: '外键解绑修改', type: 'primary', code: 'primary', more: true },
  { label: '历史记录', type: 'primary', code: 'primary', more: true },
  { label: '关联默认值', type: 'primary', code: 'primary', more: true },
  { label: '自动修复', type: 'primary', code: 'primary', more: true },
  { label: '权限管理', type: 'primary', code: 'primary', more: true },
]

// 表格列配置 - 添加自定义操作列
const columns = [
  { prop: '外键名称', label: '外键名称', minWidth: 120 },
  { prop: '外键类型', label: '外键类型', minWidth: 80 },
  { prop: '最小长度', label: '最小长度', minWidth: 80 },
  { prop: '最大长度', label: '最大长度', minWidth: 80 },
  { prop: '外键版本', label: '外键版本', minWidth: 80 },
  { prop: '说明', label: '说明', minWidth: 200 }, // 说明列占用更多空间
  { prop: '状态', label: '状态', minWidth: 60, slot: '状态' },
  { prop: '操作', label: '操作', minWidth: 380, slot: '操作', fixed: 'right' } // 自定义操作列，增加宽度以确保按钮完整显示
]

// 分页配置
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 列表请求参数
const reqParams = reactive({
  外键名称: '',
  外键类型: '',
  最小长度范围: '',
  最大长度范围: '',
  skipCount: 0,
  maxResultCount: 10
})

// 弹窗相关
const showDialogForm = ref(false)
const dialogFormRef = ref()

// Loading状态
const searchLoading = ref(false)

// 弹窗表单数据
const dialogForm = ref({
  外键名称: '',
  外键类型: '',
  最小长度: 1,
  最大长度: 50,
  外键版本: 'V1.0',
  说明: '',
  状态: true
})

// 批量修改表单数据
const batchForm = ref({
  最小长度: '',
  最大长度: '',
  外键版本: '',
  说明: '',
  状态: null
})

// 弹窗表单配置
const dialogFormProps = ref([
  { 
    label: '外键名称', 
    prop: '外键名称', 
    type: 'text', 
    placeholder: '请输入外键名称',
    rules: [{ required: true, message: '请输入外键名称', trigger: 'blur' }]
  },
  { 
    label: '外键类型', 
    prop: '外键类型', 
    type: 'select', 
    options: FOREIGN_KEY_TYPES,
    placeholder: '请选择外键类型',
    rules: [{ required: true, message: '请选择外键类型', trigger: 'change' }]
  },
  { 
    label: '最小长度', 
    prop: '最小长度', 
    type: 'text', 
    inputType: 'number',
    placeholder: '请输入最小长度',
    rules: [{ required: true, message: '请输入最小长度', trigger: 'blur' }]
  },
  { 
    label: '最大长度', 
    prop: '最大长度', 
    type: 'text', 
    inputType: 'number',
    placeholder: '请输入最大长度',
    rules: [{ required: true, message: '请输入最大长度', trigger: 'blur' }]
  },
  { 
    label: '外键版本', 
    prop: '外键版本', 
    type: 'text', 
    placeholder: '请输入外键版本'
  },
  { 
    label: '说明', 
    prop: '说明', 
    type: 'textarea', 
    placeholder: '请输入说明信息'
  },
  { 
    label: '状态', 
    prop: '状态', 
    type: 'switch'
  }
])

// 弹窗表单校验规则
const dialogFormRules = {
  外键名称: [{ required: true, message: '请输入外键名称', trigger: 'blur' }],
  外键类型: [{ required: true, message: '请选择外键类型', trigger: 'change' }],
  最小长度: [{ required: true, message: '请输入最小长度', trigger: 'blur' }],
  最大长度: [{ required: true, message: '请输入最大长度', trigger: 'blur' }]
}

// 批量修改表单配置
const batchFormProps = ref([
  {
    label: '最小长度',
    prop: '最小长度',
    type: 'text',
    placeholder: '请输入最小长度（留空表示不修改）'
  },
  {
    label: '最大长度',
    prop: '最大长度',
    type: 'text',
    placeholder: '请输入最大长度（留空表示不修改）'
  },
  {
    label: '外键版本',
    prop: '外键版本',
    type: 'text',
    placeholder: '请输入外键版本（留空表示不修改）'
  },
  {
    label: '说明',
    prop: '说明',
    type: 'textarea',
    placeholder: '请输入说明（留空表示不修改）'
  },
  {
    label: '状态',
    prop: '状态',
    type: 'select',
    options: [
      { label: '不修改', value: null },
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ],
    placeholder: '请选择状态'
  }
])

// 业务表关联表单配置
const businessTableFormProps = ref([
  {
    label: '业务表',
    prop: '业务表',
    type: 'select',
    options: BUSINESS_TABLES.map(table => ({
      label: table.label,
      value: table.value
    })),
    placeholder: '请选择业务表（多选）',
    multiple: true
  },
  {
    label: '外键',
    prop: '外键',
    type: 'select',
    options: [],
    placeholder: '请选择外键（多选）',
    multiple: true
  }
])

// 外键关联提示表单配置
const foreignKeyTipsFormProps = ref([
  {
    label: '提示类型',
    prop: '提示类型',
    type: 'select',
    options: [
      { label: '成功提示', value: '成功提示' },
      { label: '错误提示', value: '错误提示' },
      { label: '警告提示', value: '警告提示' },
      { label: '信息提示', value: '信息提示' }
    ],
    placeholder: '请选择提示类型'
  },
  {
    label: '提示内容',
    prop: '提示内容',
    type: 'textarea',
    placeholder: '请输入提示内容，可使用 {count} 占位符表示关联数量',
    rows: 4
  }
])

// 外键错误预警表单配置
const errorWarningFormProps = ref([
  {
    label: '错误阈值 (%)',
    prop: '错误阈值',
    type: 'text',
    placeholder: '请输入错误阈值百分比'
  }
])

// 外键关联默认值表单配置
const defaultValueFormProps = ref([
  {
    label: '默认值',
    prop: '默认值',
    type: 'text',
    placeholder: '请输入'
  },
  {
    label: '默认值应用策略',
    prop: '默认值应用策略',
    type: 'select',
    options: [
      { label: '新增时应用', value: '新增时应用' },
      { label: '修改时应用', value: '修改时应用' },
      { label: '新增和修改时应用', value: '新增和修改时应用' }
    ],
    placeholder: '请选择应用策略'
  }
])

// 外键关联自动修复表单配置
const autoRepairFormProps = ref([
  {
    label: '修复策略',
    prop: '修复策略',
    type: 'select',
    options: [
      { label: '删除无效记录', value: '删除无效记录' },
      { label: '修复关联关系', value: '修复关联关系' },
      { label: '标记为无效', value: '标记为无效' },
      { label: '发送警告通知', value: '发送警告通知' }
    ],
    placeholder: '请选择修复策略'
  },
  {
    label: '修复频率',
    prop: '修复频率',
    type: 'select',
    options: [
      { label: '每天', value: '每天' },
      { label: '每周', value: '每周' },
      { label: '每月', value: '每月' },
      { label: '手动触发', value: '手动触发' }
    ],
    placeholder: '请选择修复频率'
  }
])

// 外键规则设置表单配置
const foreignKeyRulesFormProps = ref([
  // 外键有效性验证
  {
    label: '唯一性约束',
    prop: '唯一性约束',
    type: 'switch'
  },
  {
    label: '允许NULL值',
    prop: '允许NULL值',
    type: 'switch'
  },

  // 外键默认关联策略
  {
    label: '默认串联类型',
    prop: '默认串联类型',
    type: 'select',
    options: [
      { label: '请选择', value: '' },
      { label: '级联删除', value: '级联删除' },
      { label: '级联更新', value: '级联更新' },
      { label: '设置NULL', value: '设置NULL' },
      { label: '限制操作', value: '限制操作' }
    ],
    placeholder: '请选择'
  },
  {
    label: '更新约束力',
    prop: '更新约束力',
    type: 'select',
    options: [
      { label: '请选择', value: '' },
      { label: '强制约束', value: '强制约束' },
      { label: '软约束', value: '软约束' },
      { label: '延迟约束', value: '延迟约束' }
    ],
    placeholder: '请选择'
  },

  // 外键自定义规则
  {
    label: '启用自定义规则',
    prop: '启用自定义规则',
    type: 'switch'
  },
  {
    label: '正则表达式',
    prop: '正则表达式',
    type: 'text',
    placeholder: '请输入'
  },
  {
    label: '最小长度',
    prop: '最小长度',
    type: 'text',
    placeholder: '请输入'
  },
  {
    label: '最大长度',
    prop: '最大长度',
    type: 'text',
    placeholder: '请输入'
  },

  // 外键过期设置
  {
    label: '开启外键过期时间',
    prop: '开启外键过期时间',
    type: 'switch'
  },
  {
    label: '过期时间',
    prop: '过期时间',
    type: 'select',
    options: [
      { label: '请选择年月日', value: '' },
      { label: '1个月后', value: '1个月后' },
      { label: '3个月后', value: '3个月后' },
      { label: '6个月后', value: '6个月后' },
      { label: '1年后', value: '1年后' },
      { label: '永不过期', value: '永不过期' }
    ],
    placeholder: '请选择年月日'
  },
  {
    label: '过期后行为',
    prop: '过期后行为',
    type: 'select',
    options: [
      { label: '请选择', value: '' },
      { label: '自动删除', value: '自动删除' },
      { label: '标记无效', value: '标记无效' },
      { label: '发送警告', value: '发送警告' },
      { label: '禁用访问', value: '禁用访问' }
    ],
    placeholder: '请选择'
  },

  // 搜索性能规则设置
  {
    label: '索引规则',
    prop: '索引规则',
    type: 'select',
    options: [
      { label: '不自动创建', value: '不自动创建' },
      { label: '自动创建索引', value: '自动创建索引' },
      { label: '复合索引', value: '复合索引' },
      { label: '唯一索引', value: '唯一索引' }
    ],
    placeholder: '不自动创建'
  },
  {
    label: '性能阈值设置',
    prop: '性能阈值设置',
    type: 'text',
    placeholder: '请输入 (秒)'
  },
  {
    label: '缓存控制',
    prop: '缓存控制',
    type: 'switch'
  },

  // 外键关联条件设置
  {
    label: '外键关联条件设置',
    prop: '外键关联条件设置',
    type: 'select',
    options: [
      { label: '请选择', value: '' },
      { label: '严格匹配', value: '严格匹配' },
      { label: '模糊匹配', value: '模糊匹配' },
      { label: '范围匹配', value: '范围匹配' }
    ],
    placeholder: '请选择'
  },
  {
    label: '关联定时间',
    prop: '关联定时间',
    type: 'select',
    options: [
      { label: '请选择年月日', value: '' },
      { label: '立即生效', value: '立即生效' },
      { label: '延迟1小时', value: '延迟1小时' },
      { label: '延迟1天', value: '延迟1天' },
      { label: '自定义时间', value: '自定义时间' }
    ],
    placeholder: '请选择年月日'
  }
])

// 防抖函数
const debounce = (func: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout
  return (...args: any[]) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(null, args), delay)
  }
}

// 实际的表格数据状态
const actualTableData = ref<any[]>([])
const actualTotal = ref(0)

// 显示的表格数据 - 直接使用ref而不是computed
const tableData = ref<any[]>([])

// 显示的分页总数 - 直接使用ref而不是computed
const displayTotal = ref(0)

// 更新表格数据的函数
const updateTableData = () => {
  const filters = {
    外键名称: searchForm.value.外键名称?.trim() || '',
    外键类型: searchForm.value.外键类型?.trim() || '',
    最小长度范围: searchForm.value.最小长度范围 ? Number(searchForm.value.最小长度范围) : null,
    最大长度范围: searchForm.value.最大长度范围 ? Number(searchForm.value.最大长度范围) : null
  }

  const filteredData = foreignKeyStore.getFilteredForeignKeys(filters)

  // 保存原始数据（用于过滤功能）
  if (originalTableData.value.length === 0) {
    originalTableData.value = [...filteredData]
  }

  // 更新实际数据
  actualTotal.value = filteredData.length
  pagination.total = filteredData.length
  displayTotal.value = filteredData.length

  // 分页处理
  const start = (pagination.page - 1) * pagination.size
  const end = start + pagination.size
  actualTableData.value = filteredData.slice(start, end)
  tableData.value = filteredData.slice(start, end)
}

// 实时搜索防抖函数 - 先显示loading，再执行查询逻辑
const debouncedSearch = debounce(async () => {
  try {
    // 添加500ms延迟，让loading效果更明显
    await new Promise(resolve => setTimeout(resolve, 500))
    pagination.page = 1
    // 更新表格数据
    updateTableData()
  } finally {
    searchLoading.value = false
  }
}, 300)

// 监听搜索表单变化，实现实时搜索
watch(searchForm, () => {
  // 立即显示loading状态，防止数据先更新后loading
  searchLoading.value = true
  // 立即重置所有显示数据，确保显示为0条
  pagination.total = 0
  actualTotal.value = 0
  actualTableData.value = []
  tableData.value = []
  displayTotal.value = 0

  // 使用setTimeout确保loading状态先显示，然后再执行防抖搜索
  setTimeout(() => {
    debouncedSearch()
  }, 0)
}, { deep: true })

// 查询 - 添加合理的延迟和loading效果提升用户体验
const onSearch = async () => {
  searchLoading.value = true
  try {
    // 添加800ms延迟，让loading效果更明显
    await new Promise(resolve => setTimeout(resolve, 800))
    pagination.page = 1
    // 更新表格数据
    updateTableData()
  } finally {
    searchLoading.value = false
  }
}

// 重置搜索 - 添加合理的延迟和loading效果提升用户体验
const onReset = async () => {
  searchLoading.value = true
  try {
    // 重置搜索表单
    searchForm.value = {
      外键名称: '',
      外键类型: '',
      最小长度范围: '',
      最大长度范围: ''
    }
    // 添加600ms延迟，让loading效果更明显
    await new Promise(resolve => setTimeout(resolve, 600))
    pagination.page = 1
    // 更新表格数据
    updateTableData()
  } finally {
    searchLoading.value = false
  }
}

// 清空搜索 - 添加合理的延迟提升用户体验
const onClear = async () => {
  // 清空搜索表单
  searchForm.value = {
    外键名称: '',
    外键类型: '',
    最小长度范围: '',
    最大长度范围: ''
  }
  // 添加120ms延迟，让清空过程更自然
  await new Promise(resolve => setTimeout(resolve, 120))
  pagination.page = 1
  // 触发搜索以显示所有数据
  onSearch()
}

// 表格操作点击事件（已移除，现在使用自定义操作列模板）

// 状态切换
const handleStatusChange = (row: ForeignKeyDefinition) => {
  // 记录切换前的状态，用于显示正确的消息
  const oldStatus = row.状态
  const newStatus = foreignKeyStore.toggleStatus(row.id)
  if (newStatus !== null) {
    // 根据切换后的状态显示正确的消息
    ElMessage.success(`已${newStatus ? '启用' : '禁用'}外键`)
    // 同步更新tableData中的状态，确保switch视觉状态正确
    row.状态 = newStatus
    // 同时更新actualTableData中对应的数据
    const actualRow = actualTableData.value.find(item => item.id === row.id)
    if (actualRow) {
      actualRow.状态 = newStatus
    }
  } else {
    ElMessage.error('状态切换失败')
    // 如果切换失败，保持原状态不变
  }
}

// 新增外键
const onClickAdd = () => {
  currentRow.value = null
  dialogForm.value = {
    外键名称: '',
    外键类型: '',
    最小长度: 1,
    最大长度: 50,
    外键版本: 'V1.0',
    说明: '',
    状态: true
  }
  showDialogForm.value = true
}

// 分页事件 - 添加loading效果提升用户体验
const onPaginationChange = async (val: any, type: any) => {
  searchLoading.value = true
  try {
    if (type === 'page') {
      pagination.page = val
    } else {
      pagination.size = val
      pagination.page = 1
    }
    // 添加300ms延迟，让loading效果更明显
    await new Promise(resolve => setTimeout(resolve, 300))
    // 分页变化后重新计算表格数据
    updateTableData()
  } finally {
    searchLoading.value = false
  }
}

// 块高度变化事件
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 75
}

// 弹窗表单提交
const onDialogConfirm = () => {
  dialogFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true
      
      try {
        if (currentRow.value) {
          // 编辑
          const result = foreignKeyStore.updateForeignKey(currentRow.value.id, {
            外键名称: dialogForm.value.外键名称,
            外键类型: dialogForm.value.外键类型,
            最小长度: Number(dialogForm.value.最小长度),
            最大长度: Number(dialogForm.value.最大长度),
            外键版本: dialogForm.value.外键版本,
            说明: dialogForm.value.说明,
            状态: dialogForm.value.状态
          })
          
          if (result) {
            ElMessage.success('编辑成功')
            showDialogForm.value = false
          } else {
            ElMessage.error('编辑失败')
          }
        } else {
          // 新增
          foreignKeyStore.addForeignKey({
            外键名称: dialogForm.value.外键名称,
            外键类型: dialogForm.value.外键类型,
            最小长度: Number(dialogForm.value.最小长度),
            最大长度: Number(dialogForm.value.最大长度),
            外键版本: dialogForm.value.外键版本,
            说明: dialogForm.value.说明,
            状态: dialogForm.value.状态
          })

          // 新增成功后立即更新表格数据和总数
          updateTableData()

          ElMessage.success('新增成功')
          showDialogForm.value = false
        }
      } catch (error) {
        ElMessage.error('操作失败')
        console.error(error)
      } finally {
        loading.value = false
      }
    }
  })
}

// 功能按钮处理
const handleCustomForeignKey = () => {
  ElMessage.info('自定义外键功能开发中...')
}

const handleBatchSetting = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要批量修改的外键项目')
    return
  }

  // 重置批量修改表单
  batchForm.value = {
    最小长度: '',
    最大长度: '',
    外键版本: '',
    说明: '',
    状态: null
  }

  showBatchDialog.value = true
}

const handleBusinessTableLink = () => {
  // 初始化业务表数据
  foreignKeyStore.initializeBusinessTables()
  foreignKeyStore.initializeForeignKeyBusinessRelations()

  // 重置表单
  businessTableForm.value = {
    外键: '',
    业务表: '',
    关联字段: '',
    关联类型: ''
  }

  // 更新外键选项
  const foreignKeyOptions = foreignKeyStore.foreignKeys.map(fk => ({
    label: fk.外键名称,
    value: fk.id
  }))

  // 更新表单配置中的外键选项
  const foreignKeyProp = businessTableFormProps.value.find(prop => prop.prop === '外键')
  if (foreignKeyProp) {
    foreignKeyProp.options = foreignKeyOptions
  }

  showBusinessTableDialog.value = true
}

const handleForeignKeyTips = () => {
  // 加载已保存的配置
  const savedConfig = localStorage.getItem('foreignKeyTipsConfig')
  if (savedConfig) {
    const config = JSON.parse(savedConfig)
    savedTipsConfig.value = config
    foreignKeyTipsForm.value = {
      提示类型: config.提示类型,
      提示内容: config.提示内容
    }
  } else {
    // 重置表单
    foreignKeyTipsForm.value = {
      提示类型: '成功提示',
      提示内容: ''
    }
  }

  showForeignKeyTipsDialog.value = true
}

const handleForeignKeyRules = () => {
  // 加载已保存的外键规则配置
  const savedRulesConfig = localStorage.getItem('foreignKeyRulesConfig')
  if (savedRulesConfig) {
    try {
      const config = JSON.parse(savedRulesConfig)
      foreignKeyRulesForm.value = {
        ...foreignKeyRulesForm.value,
        ...config
      }
    } catch (error) {
      console.error('解析保存的外键规则配置失败:', error)
    }
  }

  showForeignKeyRulesDialog.value = true
}

const handleForeignKeyStatistics = () => {
  // 计算统计数据
  calculateStatisticsData()
  showForeignKeyStatisticsDialog.value = true
  // 图表渲染由watch监听器处理
}

// 计算统计数据
const calculateStatisticsData = () => {
  const allForeignKeys = foreignKeyStore.foreignKeys

  // 基础统计
  statisticsData.value.外键总数 = allForeignKeys.length
  statisticsData.value.唯一外键数 = new Set(allForeignKeys.map(fk => fk.外键名称)).size
  statisticsData.value.无效外键 = allForeignKeys.filter(fk => !fk.状态).length

  // 关系类型分布统计 - 简化为一对多和一对一
  const oneToManyCount = Math.floor(allForeignKeys.length * 0.8) // 80%为一对多
  const oneToOneCount = allForeignKeys.length - oneToManyCount   // 20%为一对一

  // 转换为图表数据格式，匹配图片中的显示
  statisticsData.value.关系类型分布 = [
    { name: '一对多', value: oneToManyCount },
    { name: '一对一', value: oneToOneCount }
  ]
}

// 渲染统计图表
const renderStatisticsChart = () => {
  console.log('开始渲染图表')

  if (!statisticsChartRef.value) {
    console.error('图表容器未找到')
    return
  }

  try {
    // 销毁已存在的图表实例
    if ((statisticsChartRef.value as any)._echarts_instance_) {
      (statisticsChartRef.value as any)._echarts_instance_.dispose()
    }

    const chart = echarts.init(statisticsChartRef.value)

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {d}%'
      },
      legend: {
        orient: 'vertical',
        right: 30,
        top: 'center',
        itemWidth: 14,
        itemHeight: 14,
        textStyle: {
          fontSize: 12,
          color: '#333'
        },
        data: statisticsData.value.关系类型分布.map(item => item.name)
      },
      series: [
        {
          name: '关系类型分布',
          type: 'pie',
          radius: '65%',
          center: ['35%', '50%'],
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}: {d}%',
            fontSize: 12,
            color: '#333'
          },
          labelLine: {
            show: true,
            length: 15,
            length2: 10
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          data: statisticsData.value.关系类型分布
        }
      ],
      color: ['#5B9BD5', '#F4B942']  // 蓝色和黄色，匹配图片样式
    }

    chart.setOption(option)
    console.log('图表渲染完成，数据:', statisticsData.value.关系类型分布)

    // 窗口大小改变时重新渲染
    const resizeHandler = () => {
      chart.resize()
    }
    window.addEventListener('resize', resizeHandler)

    // 保存清理函数到图表实例
    ;(chart as any)._cleanup = () => {
      window.removeEventListener('resize', resizeHandler)
      chart.dispose()
    }

  } catch (error) {
    console.error('图表渲染失败:', error)
  }
}

const handleForeignKeyErrorAnalysis = () => {
  showForeignKeyErrorDialog.value = true
  // 模拟分析过程
  performErrorAnalysis()
}

// 执行错误分析
const performErrorAnalysis = async () => {
  errorAnalysisLoading.value = true
  errorAnalysisResults.value = []

  try {
    // 模拟分析延迟
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 基于真实数据进行错误分析
    const realErrors = generateRealErrors()
    errorAnalysisResults.value = realErrors

    if (realErrors.length === 0) {
      ElMessage.success('分析完成，未发现外键关系错误')
    } else {
      ElMessage.warning(`分析完成，发现 ${realErrors.length} 个错误`)
    }
  } catch (error) {
    ElMessage.error('分析过程中出现错误')
  } finally {
    errorAnalysisLoading.value = false
  }
}

// 基于真实数据生成错误分析结果
const generateRealErrors = (): ErrorAnalysisResult[] => {
  const allForeignKeys = foreignKeyStore.foreignKeys
  const errors: ErrorAnalysisResult[] = []
  let errorId = 1

  // 1. 检查重复的外键名称
  const nameMap = new Map<string, string[]>()
  allForeignKeys.forEach(fk => {
    if (!nameMap.has(fk.外键名称)) {
      nameMap.set(fk.外键名称, [])
    }
    nameMap.get(fk.外键名称)!.push(fk.id)
  })

  nameMap.forEach((ids, name) => {
    if (ids.length > 1) {
      errors.push({
        id: `error_${errorId++}`,
        foreignKeyName: name,
        errorType: '重复键冲突',
        description: `${name}: 存在重复的外键名称，共${ids.length}个重复项`
      })
    }
  })

  // 2. 检查长度配置不合理的外键
  allForeignKeys.forEach(fk => {
    if (fk.最小长度 > fk.最大长度) {
      errors.push({
        id: `error_${errorId++}`,
        foreignKeyName: fk.外键名称,
        errorType: '长度配置错误',
        description: `${fk.外键名称}: 最小长度(${fk.最小长度})大于最大长度(${fk.最大长度})`
      })
    }

    // 检查字符型外键长度是否过短
    if (fk.外键类型 === '字符型C' && fk.最大长度 < 6) {
      errors.push({
        id: `error_${errorId++}`,
        foreignKeyName: fk.外键名称,
        errorType: '长度限制风险',
        description: `${fk.外键名称}: 字符型外键最大长度(${fk.最大长度})过短，可能导致数据截断`
      })
    }

    // 检查数值型外键长度是否过长
    if (fk.外键类型 === '数值型N' && fk.最大长度 > 10) {
      errors.push({
        id: `error_${errorId++}`,
        foreignKeyName: fk.外键名称,
        errorType: '性能风险',
        description: `${fk.外键名称}: 数值型外键长度(${fk.最大长度})过长，可能影响查询性能`
      })
    }
  })

  // 3. 检查禁用状态的外键
  const disabledKeys = allForeignKeys.filter(fk => !fk.状态)
  disabledKeys.forEach(fk => {
    errors.push({
      id: `error_${errorId++}`,
      foreignKeyName: fk.外键名称,
      errorType: '状态异常',
      description: `${fk.外键名称}: 外键处于禁用状态，可能影响数据关联完整性`
    })
  })

  // 4. 检查类型不一致的时间字段
  const timeKeys = allForeignKeys.filter(fk =>
    fk.外键名称.includes('TIME') || fk.外键名称.includes('DATE')
  )
  timeKeys.forEach(fk => {
    if (fk.外键类型 !== '日期型T') {
      errors.push({
        id: `error_${errorId++}`,
        foreignKeyName: fk.外键名称,
        errorType: '数据类型不匹配',
        description: `${fk.外键名称}: 时间字段应使用日期型T，当前为${fk.外键类型}`
      })
    }
  })

  // 5. 检查相同类型外键的长度一致性
  const typeGroups = new Map<string, typeof allForeignKeys>()
  allForeignKeys.forEach(fk => {
    if (!typeGroups.has(fk.外键类型)) {
      typeGroups.set(fk.外键类型, [])
    }
    typeGroups.get(fk.外键类型)!.push(fk)
  })

  typeGroups.forEach((keys, type) => {
    if (keys.length > 1) {
      const lengths = keys.map(k => `${k.最小长度}-${k.最大长度}`)
      const uniqueLengths = [...new Set(lengths)]
      if (uniqueLengths.length > 1) {
        const inconsistentKeys = keys.filter((k, i) =>
          lengths[i] !== lengths[0]
        ).slice(0, 2) // 只报告前2个不一致的

        inconsistentKeys.forEach(fk => {
          errors.push({
            id: `error_${errorId++}`,
            foreignKeyName: fk.外键名称,
            errorType: '长度标准不一致',
            description: `${fk.外键名称}: ${type}类型外键长度(${fk.最小长度}-${fk.最大长度})与同类型其他外键不一致`
          })
        })
      }
    }
  })

  return errors
}

// 更多操作菜单项（页面级别）
const moreOperationItems = [
  { label: '更多操作', disabled: true, divided: false },
  { label: '外键规则设置', command: 'rules', divided: true },
  { label: '外键数据统计', command: 'statistics', divided: false },
  { label: '错误预警', command: 'warning', divided: false },
  { label: '外键验证报告', command: 'report', divided: false },
  { label: '外键过滤条件', command: 'filter', divided: false }
]

// 列表项更多操作菜单项
const rowMoreOperationItems = [
  { label: '更多操作', disabled: true, divided: false },
  { label: '外键锁定', command: 'lock', divided: true },
  { label: '外键解锁修改', command: 'unlock', divided: false },
  { label: '历史记录', command: 'history', divided: false },
  { label: '外键关联默认值', command: 'auth', divided: false },
  { label: '自动修复', command: 'repair', divided: false },
  { label: '权限管理', command: 'permission', divided: false }
]

const handleMoreOperations = () => {
  // 这个函数现在由下拉菜单的点击事件处理
}

// 处理更多操作菜单项点击（页面级别）
const handleMoreOperationCommand = (command: string) => {
  switch (command) {
    case 'rules':
      handleForeignKeyRules()
      break
    case 'statistics':
      handleForeignKeyStatistics()
      break
    case 'warning':
      handleErrorWarning()
      break
    case 'report':
      handleValidationReport()
      break
    case 'filter':
      handleFilterCondition()
      break
    default:
      ElMessage.info(`${command} 功能开发中...`)
      break
  }
}

// 处理列表项更多操作菜单项点击
const handleRowMoreOperationCommand = (command: string, row: ForeignKeyDefinition) => {
  switch (command) {
    case 'lock':
      // 显示外键锁定对话框
      currentLockRow.value = row
      foreignKeyLockForm.value = {
        锁定时间: '',
        时: '',
        分: '',
        秒: ''
      }
      showForeignKeyLockDialog.value = true
      break
    case 'unlock':
      // 显示外键解锁修改对话框
      currentUnlockRow.value = row
      foreignKeyUnlockForm.value = {
        解绑模式: '',
        生效时间: '',
        恢复时间: '',
        数据处理方式: ''
      }
      showForeignKeyUnlockDialog.value = true
      break
    case 'history':
      // 显示外键历史记录对话框
      currentHistoryRow.value = row
      showHistoryDialog.value = true
      loadHistoryData()
      break
    case 'auth':
      // 显示外键关联默认值对话框
      currentDefaultValueRow.value = row
      showDefaultValueDialog.value = true
      // 重置表单数据
      defaultValueForm.value = {
        外键认证: false,
        默认值: '',
        默认值应用策略: '新增时应用'
      }
      break
    case 'repair':
      // 显示外键关联自动修复对话框
      currentAutoRepairRow.value = row
      // 加载已保存的自动修复配置
      const savedAutoRepairConfig = localStorage.getItem('autoRepairConfig')
      if (savedAutoRepairConfig) {
        try {
          const config = JSON.parse(savedAutoRepairConfig)
          autoRepairForm.value = {
            ...autoRepairForm.value,
            ...config
          }
        } catch (error) {
          console.error('解析保存的自动修复配置失败:', error)
        }
      }
      showAutoRepairDialog.value = true
      break
    case 'permission':
      // 设置当前操作行
      currentPermissionRow.value = row

      // 立即设置Loading状态
      personnelLoading.value = true

      // 重置相关状态
      authorizedPersonnelList.value = []
      allPersonnelList.value = []
      personnelSearchKeyword.value = ''
      personnelPagination.value.currentPage = 1

      // 显示对话框
      showPermissionDialog.value = true

      // 使用nextTick确保DOM更新后再开始加载数据
      nextTick(() => {
        loadPersonnelData(row.id)
      })
      break
    default:
      ElMessage.info(`${command} 功能开发中...`)
      break
  }
}

// TableV2按钮点击事件处理
const onTableClickButton = ({ row, btn }: any) => {
  console.log('按钮点击:', btn.code, row)

  switch (btn.code) {
    case 'detail':
      handleDetail(row)
      break
    case 'edit':
      handleEdit(row)
      break
    case 'delete':
      handleDelete(row)
      break
    default:
      console.log('未知操作:', btn.code)
  }
}

// 注释：现在使用more属性实现下拉菜单，复用已有的handleRowMoreOperationCommand处理函数

// 单独的操作处理函数
const handleDetail = (row: ForeignKeyDefinition) => {
  ElMessageBox.alert(
    `外键名称：${row.外键名称}<br/>
     外键类型：${row.外键类型}<br/>
     长度范围：${row.最小长度} - ${row.最大长度}<br/>
     外键版本：${row.外键版本}<br/>
     说明：${row.说明}<br/>
     状态：${row.状态 ? '启用' : '禁用'}<br/>
     创建时间：${row.创建时间}<br/>
     创建人：${row.创建人}`,
    '外键详情',
    { dangerouslyUseHTMLString: true }
  )
}

const handleEdit = (row: ForeignKeyDefinition) => {
  currentRow.value = row
  Object.assign(dialogForm.value, {
    外键名称: row.外键名称,
    外键类型: row.外键类型,
    最小长度: row.最小长度,
    最大长度: row.最大长度,
    外键版本: row.外键版本,
    说明: row.说明,
    状态: row.状态
  })
  showDialogForm.value = true
}

const handleDelete = (row: ForeignKeyDefinition) => {
  const success = foreignKeyStore.deleteForeignKey(row.id)
  if (success) {
    ElMessage.success('删除成功')
    // 如果当前页没有数据了，回到上一页
    if (tableData.value.length === 1 && pagination.page > 1) {
      pagination.page--
    }
    // 重新加载数据
    onSearch()
  } else {
    ElMessage.error('删除失败')
  }
}

const handleReturn = () => {
  ElMessage.info('返回上级页面')
}

// 重置数据功能（临时测试用）
const handleResetData = () => {
  ElMessageBox.confirm(
    '确认要重置所有外键数据吗？这将清除当前数据并使用最新的真实姓名数据。',
    '重置数据确认',
    {
      confirmButtonText: '确认重置',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 强制重新初始化数据
    foreignKeyStore.forceReinitializeData()
    // 刷新页面数据
    updateTableData()
    ElMessage.success('数据重置成功，已使用最新的真实姓名数据')
  }).catch(() => {
    ElMessage.info('已取消重置操作')
  })
}

// 表格选择变化处理
const onSelectionChange = (selection: ForeignKeyDefinition[]) => {
  selectedRows.value = selection
}

// 批量修改确认
const onBatchConfirm = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('没有选中的项目')
    return
  }

  // 检查是否有要修改的字段
  const hasChanges = Object.values(batchForm.value).some(value =>
    value !== '' && value !== null && value !== undefined
  )

  if (!hasChanges) {
    ElMessage.warning('请至少修改一个字段')
    return
  }

  batchLoading.value = true
  try {
    // 添加延迟，让loading效果更明显
    await new Promise(resolve => setTimeout(resolve, 800))

    // 批量更新选中的外键
    for (const row of selectedRows.value) {
      const updateData: any = { ...row }

      // 只更新非空字段
      if (batchForm.value.最小长度 !== '') {
        updateData.最小长度 = Number(batchForm.value.最小长度)
      }
      if (batchForm.value.最大长度 !== '') {
        updateData.最大长度 = Number(batchForm.value.最大长度)
      }
      if (batchForm.value.外键版本 !== '') {
        updateData.外键版本 = batchForm.value.外键版本
      }
      if (batchForm.value.说明 !== '') {
        updateData.说明 = batchForm.value.说明
      }
      if (batchForm.value.状态 !== null) {
        updateData.状态 = batchForm.value.状态
      }

      // 更新数据
      foreignKeyStore.updateForeignKey(row.id, updateData)
    }

    ElMessage.success(`成功批量修改 ${selectedRows.value.length} 个外键项目`)
    showBatchDialog.value = false

    // 清空选择
    selectedRows.value = []

    // 刷新表格数据
    updateTableData()

  } catch (error) {
    console.error('批量修改失败:', error)
    ElMessage.error('批量修改失败，请重试')
  } finally {
    batchLoading.value = false
  }
}

// 业务表关联确认
const onBusinessTableConfirm = async () => {
  try {
    businessTableLoading.value = true

    // 验证表单
    if (!businessTableForm.value.业务表 || !businessTableForm.value.外键) {
      ElMessage.warning('请选择业务表和外键')
      return
    }

    // 添加500ms延迟，让loading效果更明显
    await new Promise(resolve => setTimeout(resolve, 500))

    const selectedBusinessTables = Array.isArray(businessTableForm.value.业务表)
      ? businessTableForm.value.业务表
      : [businessTableForm.value.业务表]

    const selectedForeignKeys = Array.isArray(businessTableForm.value.外键)
      ? businessTableForm.value.外键
      : [businessTableForm.value.外键]

    let createdCount = 0

    // 为每个选中的业务表和外键创建关联关系
    for (const businessTableId of selectedBusinessTables) {
      for (const foreignKeyId of selectedForeignKeys) {
        const businessTable = BUSINESS_TABLES.find(bt => bt.value === businessTableId)
        const foreignKey = foreignKeyStore.foreignKeys.find(fk => fk.id === foreignKeyId)

        if (businessTable && foreignKey) {
          // 检查是否已存在关联关系
          const existingRelation = foreignKeyStore.foreignKeyBusinessRelations.find(
            rel => rel.外键ID === foreignKeyId && rel.业务表ID === businessTableId
          )

          if (!existingRelation) {
            foreignKeyStore.createForeignKeyBusinessRelation({
              外键ID: foreignKeyId,
              外键名称: foreignKey.外键名称,
              业务表ID: businessTableId,
              业务表名称: businessTable.label,
              关联字段: 'id', // 默认关联字段
              关联类型: 'one_to_many' // 默认关联类型
            })
            createdCount++
          }
        }
      }
    }

    if (createdCount > 0) {
      // 使用配置的提示信息，如果没有配置则使用默认消息
      if (savedTipsConfig.value.提示内容) {
        const messageType = savedTipsConfig.value.提示类型
        const content = savedTipsConfig.value.提示内容.replace('{count}', createdCount.toString())

        switch (messageType) {
          case '成功提示':
            ElMessage.success(content)
            break
          case '错误提示':
            ElMessage.error(content)
            break
          case '警告提示':
            ElMessage.warning(content)
            break
          case '信息提示':
            ElMessage.info(content)
            break
          default:
            ElMessage.success(content)
        }
      } else {
        ElMessage.success(`成功创建 ${createdCount} 个外键与业务表关联关系`)
      }
    } else {
      ElMessage.warning('所选关联关系已存在，未创建新的关联')
    }

    showBusinessTableDialog.value = false

    // 重置表单
    businessTableForm.value = {
      外键: '',
      业务表: '',
      关联字段: '',
      关联类型: ''
    }

  } catch (error) {
    console.error('创建业务表关联失败:', error)
    ElMessage.error('创建业务表关联失败，请重试')
  } finally {
    businessTableLoading.value = false
  }
}

// 外键关联提示确认
const onForeignKeyTipsConfirm = () => {
  // 验证表单
  if (!foreignKeyTipsForm.value.提示内容.trim()) {
    ElMessage.warning('请输入提示内容')
    return
  }

  // 保存配置到本地存储和内存
  savedTipsConfig.value = {
    提示类型: foreignKeyTipsForm.value.提示类型,
    提示内容: foreignKeyTipsForm.value.提示内容
  }

  // 保存到本地存储
  localStorage.setItem('foreignKeyTipsConfig', JSON.stringify(savedTipsConfig.value))

  ElMessage.success('外键关联提示配置已保存')
  showForeignKeyTipsDialog.value = false

  // 重置表单
  foreignKeyTipsForm.value = {
    提示类型: '成功提示',
    提示内容: ''
  }
}

// 外键错误预警处理
const handleErrorWarning = () => {
  // 加载已保存的错误预警配置
  const savedWarningConfig = localStorage.getItem('errorWarningConfig')
  if (savedWarningConfig) {
    const config = JSON.parse(savedWarningConfig)
    errorWarningForm.value = {
      启用错误预警: config.启用错误预警 || false,
      错误阈值: config.错误阈值 || '',
      通知方式: config.通知方式 || []
    }
  } else {
    // 默认配置
    errorWarningForm.value = {
      启用错误预警: false,
      错误阈值: '',
      通知方式: []
    }
  }

  showErrorWarningDialog.value = true
}

// 外键错误预警确认
const onErrorWarningConfirm = () => {
  // 验证表单
  if (errorWarningForm.value.启用错误预警) {
    if (!errorWarningForm.value.错误阈值.trim()) {
      ElMessage.warning('请输入错误阈值')
      return
    }

    const threshold = parseFloat(errorWarningForm.value.错误阈值)
    if (isNaN(threshold) || threshold < 0 || threshold > 100) {
      ElMessage.warning('错误阈值必须是0-100之间的数字')
      return
    }

    if (errorWarningForm.value.通知方式.length === 0) {
      ElMessage.warning('请选择至少一种通知方式')
      return
    }
  }

  // 保存配置到本地存储
  const warningConfig = {
    启用错误预警: errorWarningForm.value.启用错误预警,
    错误阈值: errorWarningForm.value.错误阈值,
    通知方式: errorWarningForm.value.通知方式
  }

  localStorage.setItem('errorWarningConfig', JSON.stringify(warningConfig))

  ElMessage.success('外键错误预警配置已保存')
  showErrorWarningDialog.value = false

  // 重置表单
  errorWarningForm.value = {
    启用错误预警: false,
    错误阈值: '',
    通知方式: []
  }
}

// 外键关联默认值确认
const onDefaultValueConfirm = () => {
  // 验证表单
  if (defaultValueForm.value.外键认证 && !defaultValueForm.value.默认值.trim()) {
    ElMessage.warning('启用外键认证时，请输入默认值')
    return
  }

  // 保存配置
  const config = {
    外键名称: currentDefaultValueRow.value?.外键名称,
    外键认证: defaultValueForm.value.外键认证,
    默认值: defaultValueForm.value.默认值,
    默认值应用策略: defaultValueForm.value.默认值应用策略
  }

  // 保存到本地存储
  const existingConfigs = JSON.parse(localStorage.getItem('defaultValueConfigs') || '[]')
  const existingIndex = existingConfigs.findIndex((c: any) => c.外键名称 === config.外键名称)

  if (existingIndex >= 0) {
    existingConfigs[existingIndex] = config
  } else {
    existingConfigs.push(config)
  }

  localStorage.setItem('defaultValueConfigs', JSON.stringify(existingConfigs))

  ElMessage.success(`外键 ${currentDefaultValueRow.value?.外键名称} 关联默认值配置已保存`)
  showDefaultValueDialog.value = false
}

// 外键验证报告处理
const handleValidationReport = () => {
  // 生成验证报告数据
  generateValidationReportData()
  showValidationReportDialog.value = true
}

// 生成验证报告数据
const generateValidationReportData = () => {
  // 基于当前外键数据生成验证报告
  const currentData = tableData.value

  // 模拟验证逻辑，生成统计数据
  let validCount = 0
  let invalidCount = 0
  let warningCount = 0

  const validationResults = []

  currentData.forEach((item, index) => {
    // 模拟验证逻辑
    const isValid = Math.random() > 0.3 // 70%概率有效
    const hasWarning = Math.random() > 0.8 // 20%概率有警告

    if (isValid && !hasWarning) {
      validCount++
      validationResults.push({
        关联名称: `${item.外键名称}-字段关联`,
        状态: '有效',
        问题描述: ''
      })
    } else if (!isValid) {
      invalidCount++
      const issues = [
        '所属表中找不到对应的主键字段',
        '外键类型与主键类型不匹配',
        '外键长度超出主键字段限制',
        '外键值存在空值但主键不允许空值',
        '外键引用的主键记录不存在'
      ]
      validationResults.push({
        关联名称: `${item.外键名称}-字段关联`,
        状态: '无效',
        问题描述: issues[Math.floor(Math.random() * issues.length)]
      })
    } else {
      warningCount++
      validationResults.push({
        关联名称: `${item.外键名称}-字段关联`,
        状态: '警告',
        问题描述: '建议优化外键索引以提升查询性能'
      })
    }
  })

  // 更新验证报告数据
  validationReportData.value = {
    statistics: {
      有效关联: validCount,
      无效关联: invalidCount,
      告警: warningCount
    },
    validationResults: validationResults
  }

  // 更新分页信息
  validationReportPagination.value.total = validationResults.length
  validationReportPagination.value.currentPage = 1 // 重置到第一页
}

// 验证报告分页处理
const handleValidationReportPageChange = async (page) => {
  validationReportLoading.value = true

  // 模拟网络请求延迟，增加真实性（延长时间让Loading更明显）
  await new Promise(resolve => setTimeout(resolve, 1500))

  validationReportPagination.value.currentPage = page
  validationReportLoading.value = false
}

const handleValidationReportSizeChange = async (size) => {
  validationReportLoading.value = true

  // 模拟网络请求延迟，增加真实性（延长时间让Loading更明显）
  await new Promise(resolve => setTimeout(resolve, 1200))

  validationReportPagination.value.pageSize = size
  validationReportPagination.value.currentPage = 1 // 重置到第一页
  validationReportLoading.value = false
}

// 外键过滤条件处理
const handleFilterCondition = () => {
  showFilterDialog.value = true
}

// 确认过滤条件
const confirmFilter = () => {
  // 根据过滤条件筛选数据
  applyFilter()
  showFilterDialog.value = false
}

// 取消过滤条件
const cancelFilter = () => {
  showFilterDialog.value = false
}

// 应用过滤条件
const applyFilter = () => {
  const { field, operator, value } = filterForm.value

  if (!value.trim()) {
    ElMessage.warning('请输入过滤值')
    return
  }

  // 根据过滤条件筛选数据
  let filteredData = [...originalTableData.value]

  filteredData = filteredData.filter(item => {
    let fieldValue = ''

    // 根据字段名获取对应的值
    switch (field) {
      case '外键名称':
        fieldValue = item.外键名称
        break
      case '外键类型':
        fieldValue = item.外键类型
        break
      case '说明':
        fieldValue = item.说明
        break
      default:
        fieldValue = item.外键名称
    }

    // 根据操作符进行比较
    switch (operator) {
      case '等于':
        return fieldValue === value
      case '包含':
        return fieldValue.includes(value)
      case '不等于':
        return fieldValue !== value
      case '开始于':
        return fieldValue.startsWith(value)
      case '结束于':
        return fieldValue.endsWith(value)
      default:
        return fieldValue.includes(value)
    }
  })

  // 更新表格数据
  tableData.value = filteredData
  pagination.total = filteredData.length
  pagination.page = 1

  ElMessage.success(`已应用过滤条件，找到 ${filteredData.length} 条记录`)
}

// 重置过滤条件
const resetFilter = () => {
  filterForm.value = {
    field: '外键名称',
    operator: '等于',
    value: ''
  }

  // 恢复原始数据
  tableData.value = [...originalTableData.value]
  pagination.total = originalTableData.value.length
  pagination.page = 1

  ElMessage.success('已重置过滤条件')
}

// 外键锁定确认
const onForeignKeyLockConfirm = () => {
  if (!currentLockRow.value) return

  // 构建锁定时间字符串
  const lockTime = `${foreignKeyLockForm.value.时 || '00'}:${foreignKeyLockForm.value.分 || '00'}:${foreignKeyLockForm.value.秒 || '00'}`

  ElMessage.success(`已锁定外键：${currentLockRow.value.外键名称}，锁定时间：${lockTime}`)
  showForeignKeyLockDialog.value = false

  // 重置表单
  foreignKeyLockForm.value = {
    锁定时间: '',
    时: '',
    分: '',
    秒: ''
  }
  currentLockRow.value = null
}

// 外键解锁修改确认
const onForeignKeyUnlockConfirm = () => {
  if (!currentUnlockRow.value) return

  let message = `已解锁外键：${currentUnlockRow.value.外键名称}`

  if (foreignKeyUnlockForm.value.解绑模式 === '保持原有约束') {
    message += `，解绑模式：${foreignKeyUnlockForm.value.解绑模式}`
    if (foreignKeyUnlockForm.value.生效时间) {
      message += `，生效时间：${foreignKeyUnlockForm.value.生效时间}`
    }
    if (foreignKeyUnlockForm.value.恢复时间) {
      message += `，恢复时间：${foreignKeyUnlockForm.value.恢复时间}`
    }
  } else if (foreignKeyUnlockForm.value.解绑模式 === '永久移除外键') {
    message += `，解绑模式：${foreignKeyUnlockForm.value.解绑模式}`
    if (foreignKeyUnlockForm.value.数据处理方式) {
      message += `，数据处理：${foreignKeyUnlockForm.value.数据处理方式}`
    }
  }

  ElMessage.success(message)
  showForeignKeyUnlockDialog.value = false

  // 重置表单
  foreignKeyUnlockForm.value = {
    解绑模式: '',
    生效时间: '',
    恢复时间: '',
    数据处理方式: ''
  }
  currentUnlockRow.value = null
}

// 加载历史记录数据
const loadHistoryData = async (page = 1) => {
  if (!currentHistoryRow.value) return

  historyLoading.value = true
  historyPagination.value.currentPage = page

  try {
    // 模拟网络请求延迟，增加真实性
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 生成与当前外键相关的历史记录数据
    const mockHistoryData = generateHistoryData(currentHistoryRow.value)

    // 分页处理
    const startIndex = (page - 1) * historyPagination.value.pageSize
    const endIndex = startIndex + historyPagination.value.pageSize

    historyData.value = mockHistoryData.slice(startIndex, endIndex)
    historyPagination.value.total = mockHistoryData.length

  } catch (error) {
    ElMessage.error('加载历史记录失败')
  } finally {
    historyLoading.value = false
  }
}

// 生成历史记录数据
const generateHistoryData = (foreignKey: ForeignKeyDefinition) => {
  const operations = ['创建', '修改', '锁定', '解锁', '删除', '恢复']
  const users = ['张明华', '李晓红', '王建国', '陈美丽', '刘德华']

  const historyRecords = []

  // 生成15-25条历史记录
  const recordCount = Math.floor(Math.random() * 11) + 15

  for (let i = 0; i < recordCount; i++) {
    const operation = operations[Math.floor(Math.random() * operations.length)]
    const user = users[Math.floor(Math.random() * users.length)]

    // 生成时间（最近30天内）
    const date = new Date()
    date.setDate(date.getDate() - Math.floor(Math.random() * 30))
    const timeStr = date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).replace(/\//g, '.')

    // 生成操作内容
    let content = ''
    switch (operation) {
      case '创建':
        content = `创建了外键${foreignKey.外键名称}`
        break
      case '修改':
        content = `修改了外键${foreignKey.外键名称}的配置`
        break
      case '锁定':
        content = `锁定了外键${foreignKey.外键名称}`
        break
      case '解锁':
        content = `解锁了外键${foreignKey.外键名称}`
        break
      case '删除':
        content = `删除了外键${foreignKey.外键名称}`
        break
      case '恢复':
        content = `恢复了外键${foreignKey.外键名称}`
        break
    }

    historyRecords.push({
      序号: i + 1,
      时间: timeStr,
      操作类型: operation,
      操作内容: content,
      操作人: user
    })
  }

  // 按时间倒序排列（最新的在前面）
  return historyRecords.sort((a, b) => new Date(b.时间).getTime() - new Date(a.时间).getTime())
}

// 历史记录分页变化处理
const handleHistoryPageChange = (page: number) => {
  loadHistoryData(page)
}

// 获取操作类型标签颜色
const getOperationTagType = (operationType: string) => {
  switch (operationType) {
    case '创建':
      return 'success'
    case '修改':
      return 'warning'
    case '锁定':
      return 'danger'
    case '解锁':
      return 'primary'
    case '删除':
      return 'danger'
    case '恢复':
      return 'success'
    default:
      return 'info'
  }
}

// 外键规则设置确认
const onForeignKeyRulesConfirm = () => {
  // 保存外键规则配置到本地存储
  localStorage.setItem('foreignKeyRulesConfig', JSON.stringify(foreignKeyRulesForm.value))

  ElMessage.success('外键规则设置已保存')
  showForeignKeyRulesDialog.value = false
}

// 外键关系错误分析确认
const onForeignKeyErrorConfirm = () => {
  showForeignKeyErrorDialog.value = false
}

// 外键关联自动修复确认
const onAutoRepairConfirm = () => {
  if (!currentAutoRepairRow.value) return

  // 验证表单
  if (autoRepairForm.value.启用自动修复) {
    if (!autoRepairForm.value.修复策略) {
      ElMessage.warning('请选择修复策略')
      return
    }
    if (!autoRepairForm.value.修复频率) {
      ElMessage.warning('请选择修复频率')
      return
    }
  }

  // 保存配置到本地存储
  const autoRepairConfig = {
    启用自动修复: autoRepairForm.value.启用自动修复,
    当检测到无效关联时自动尝试修复: autoRepairForm.value.当检测到无效关联时自动尝试修复,
    修复策略: autoRepairForm.value.修复策略,
    修复频率: autoRepairForm.value.修复频率,
    自动修复开关: autoRepairForm.value.自动修复开关
  }

  localStorage.setItem('autoRepairConfig', JSON.stringify(autoRepairConfig))

  ElMessage.success(`外键 ${currentAutoRepairRow.value.外键名称} 自动修复配置已保存`)
  showAutoRepairDialog.value = false

  // 重置表单
  autoRepairForm.value = {
    启用自动修复: false,
    当检测到无效关联时自动尝试修复: false,
    修复策略: '删除无效记录',
    修复频率: '每天',
    自动修复开关: false
  }
  currentAutoRepairRow.value = null
}

// 外键权限管理确认
const onPermissionConfirm = () => {
  if (!currentPermissionRow.value) return

  // 获取所有选中的授权人员（从完整列表中获取）
  const selectedPersonnel = allPersonnelList.value
    .filter(person => person.selected)
    .map(person => person.name)

  // 验证表单
  if (selectedPersonnel.length === 0) {
    ElMessage.warning('请至少选择一个授权人员')
    return
  }

  const hasAnyPermission = Object.values(permissionForm.value.操作权限).some(permission => permission)
  if (!hasAnyPermission) {
    ElMessage.warning('请至少选择一个操作权限')
    return
  }

  // 保存配置到本地存储
  const permissionConfig = {
    授权人员: selectedPersonnel,
    操作权限: { ...permissionForm.value.操作权限 }
  }

  localStorage.setItem(`permissionConfig_${currentPermissionRow.value.id}`, JSON.stringify(permissionConfig))

  const permissionNames = Object.entries(permissionForm.value.操作权限)
    .filter(([_, value]) => value)
    .map(([key, _]) => key)
    .join('、')

  ElMessage.success(`外键 ${currentPermissionRow.value.外键名称} 权限配置已保存，授权人员：${selectedPersonnel.join('、')}，操作权限：${permissionNames}`)
  showPermissionDialog.value = false

  // 重置表单和数据
  permissionForm.value = {
    授权人员: [],
    操作权限: {
      查看: false,
      编辑: false,
      删除: false,
      新增: false
    }
  }
  authorizedPersonnelList.value = []
  allPersonnelList.value = []
  personnelPagination.value.currentPage = 1
  currentPermissionRow.value = null
}

// 处理授权人员选择变化
const handlePersonnelSelectionChange = (person: {name: string, selected: boolean}) => {
  // 同步更新完整列表中对应人员的选择状态
  const fullListPerson = allPersonnelList.value.find(p => p.name === person.name)
  if (fullListPerson) {
    fullListPerson.selected = person.selected
  }

  // 更新授权人员表单数据
  const selectedPersonnel = allPersonnelList.value
    .filter(p => p.selected)
    .map(p => p.name)
  permissionForm.value.授权人员 = selectedPersonnel
}

// 人员列表分页变化处理
const handlePersonnelPageChange = async (page: number) => {
  personnelLoading.value = true

  try {
    // 模拟网络请求延迟，增加真实性
    await new Promise(resolve => setTimeout(resolve, 800))

    personnelPagination.value.currentPage = page
    updateCurrentPagePersonnel()
  } finally {
    personnelLoading.value = false
  }
}

const handlePersonnelSizeChange = async (size: number) => {
  personnelLoading.value = true

  try {
    // 模拟网络请求延迟，增加真实性
    await new Promise(resolve => setTimeout(resolve, 600))

    personnelPagination.value.pageSize = size
    personnelPagination.value.currentPage = 1 // 重置到第一页
    updateCurrentPagePersonnel()
  } finally {
    personnelLoading.value = false
  }
}

// 监听统计弹窗的显示状态
watch(showForeignKeyStatisticsDialog, (newVal) => {
  if (newVal) {
    // 弹窗打开时，延迟渲染图表
    nextTick(() => {
      setTimeout(() => {
        renderStatisticsChart()
      }, 500) // 增加延迟时间，确保弹窗完全展开
    })
  } else {
    // 弹窗关闭时清理图表资源
    if (statisticsChartRef.value && (statisticsChartRef.value as any)._echarts_instance_) {
      const chart = (statisticsChartRef.value as any)._echarts_instance_
      if ((chart as any)._cleanup) {
        (chart as any)._cleanup()
      }
    }
  }
})

// 初始化数据
onMounted(async () => {
  foreignKeyStore.initializeData()
  foreignKeyStore.initializeBusinessTables()
  foreignKeyStore.initializeForeignKeyBusinessRelations()

  // 初始化外键关联提示配置
  const savedConfig = localStorage.getItem('foreignKeyTipsConfig')
  if (savedConfig) {
    savedTipsConfig.value = JSON.parse(savedConfig)
  }

  // 初始化loading状态
  searchLoading.value = true
  try {
    // 添加延迟，让初始loading效果更明显
    await new Promise(resolve => setTimeout(resolve, 500))
    // 加载初始数据
    updateTableData()
  } finally {
    searchLoading.value = false
  }
})
</script>

<template>
  <div class="foreign-key-definition">
    <Block title="外键定义" :enable-fixed-height="true" :enable-close-button="false" @height-changed="onBlockHeightChanged">
      <template #topRight>
        <el-button size="small" type="primary" @click="onClickAdd" style="margin-right: 12px;">自定义外键</el-button>
        <el-button size="small" type="primary" @click="handleBatchSetting" style="margin-right: 12px;">批量修改</el-button>
        <el-button size="small" type="primary" @click="handleBusinessTableLink" style="margin-right: 12px;">外键与业务表批量关联</el-button>
        <el-button size="small" type="primary" @click="handleForeignKeyTips" style="margin-right: 12px;">外键关联提示</el-button>
        <el-button size="small" type="primary" @click="handleForeignKeyErrorAnalysis" style="margin-right: 12px;">外键关系错误分析</el-button>
        <el-dropdown @command="handleMoreOperationCommand" trigger="click" style="margin-right: 12px;">
          <el-button size="small" type="primary">
            更多操作
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="item in moreOperationItems"
                :key="item.label"
                :command="item.command"
                :disabled="item.disabled"
                :divided="item.divided"
              >
                {{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button size="small" type="info" @click="handleReturn">返回</el-button>
      </template>
      <template #expand>
        <!-- 搜索 -->
        <div class="search">
          <Form
            :props="searchFormProp"
            v-model="searchForm"
            :column-count="2"
            :label-width="90"
            :enable-reset="true"
            :enable-clear="false"
            :loading="searchLoading"
            confirm-text="查询"
            reset-text="重置"
            button-vertical="flowing"
            confirm-type="primary"
            reset-type="info"
            @submit="onSearch"
            @reset="onReset"
          ></Form>
        </div>
      </template>
      
      <!-- 表格 -->
      <TableV2
        ref="tableRef"
        :columns="columns"
        :req-params="reqParams"
        :enable-toolbar="false"
        :enable-own-button="false"
        :enable-selection="true"
        :height="tableHeight"
        :defaultTableData="tableData"
        :loading="searchLoading"
        @loading="loading = $event"
        @selection-change="onSelectionChange"
      >
        <!-- 状态列模板 -->
        <template #状态="{ row }">
          <el-switch
            :model-value="row.状态"
            @update:model-value="handleStatusChange(row)"
            :disabled="false"
          />
        </template>

        <!-- 自定义操作列模板 -->
        <template #操作="{ row }">
          <div class="table-actions">
            <el-button size="small" type="info" @click="handleDetail(row)">详情</el-button>
            <el-button size="small" type="warning" @click="handleEdit(row)">修改</el-button>
            <el-popconfirm title="确认删除吗?" @confirm="handleDelete(row)">
              <template #reference>
                <el-button size="small" type="danger">删除</el-button>
              </template>
            </el-popconfirm>
            <el-dropdown @command="(command) => handleRowMoreOperationCommand(command, row)" trigger="click">
              <el-button size="small" type="primary">
                更多操作
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="item in rowMoreOperationItems.filter(item => !item.disabled)"
                    :key="item.label"
                    :command="item.command"
                    :divided="item.divided"
                  >
                    {{ item.label }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>

      </TableV2>
      
      <!-- 分页 -->
      <Pagination
        :total="displayTotal"
        :current-page="pagination.page"
        :page-size="pagination.size"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      ></Pagination>
    </Block>

    <!-- 新增/编辑弹窗 -->
    <Dialog
      v-model="showDialogForm"
      :title="currentRow ? '编辑外键' : '新增外键'"
      :destroy-on-close="true"
      :loading="loading"
      loading-text="保存中"
      confirm-type="primary"
      cancel-type="info"
      @closed="currentRow = null"
      @click-confirm="onDialogConfirm"
    >
      <Form
        ref="dialogFormRef"
        v-model="dialogForm"
        :props="dialogFormProps"
        :rules="dialogFormRules"
        :enable-button="false"
        :column-count="2"
      ></Form>
    </Dialog>

    <!-- 批量修改弹窗 -->
    <Dialog
      v-model="showBatchDialog"
      title="批量修改"
      :destroy-on-close="true"
      :loading="batchLoading"
      loading-text="批量修改中"
      confirm-type="primary"
      cancel-type="info"
      @click-confirm="onBatchConfirm"
      width="800px"
    >
      <div class="batch-dialog-content">
        <!-- 批量修改表单 -->
        <div class="batch-form">
          <h4>批量修改设置：</h4>
          <Form
            ref="batchDialogRef"
            v-model="batchForm"
            :props="batchFormProps"
            :enable-button="false"
            :column-count="2"
          ></Form>
        </div>
      </div>
    </Dialog>

    <!-- 业务表关联弹窗 -->
    <Dialog
      v-model="showBusinessTableDialog"
      title="批量关联外键"
      :destroy-on-close="true"
      :loading="businessTableLoading"
      loading-text="创建关联中"
      confirm-type="primary"
      cancel-type="info"
      @click-confirm="onBusinessTableConfirm"
      width="600px"
    >
      <div class="business-table-dialog-content">
        <!-- 业务表关联表单 -->
        <div class="business-table-form">
          <Form
            ref="businessTableDialogRef"
            v-model="businessTableForm"
            :props="businessTableFormProps"
            :enable-button="false"
            :column-count="1"
          ></Form>
        </div>
      </div>
    </Dialog>

    <!-- 外键规则设置弹窗 -->
    <Dialog
      v-model="showForeignKeyRulesDialog"
      title="外键规则设置"
      :destroy-on-close="true"
      confirm-type="primary"
      cancel-type="info"
      @click-confirm="onForeignKeyRulesConfirm"
      width="800px"
    >
      <div class="foreign-key-rules-dialog-content">
        <!-- 外键有效性验证 -->
        <div class="rules-section">
          <h4>外键有效性验证</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="唯一性约束">
                <el-switch v-model="foreignKeyRulesForm.唯一性约束" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="允许NULL值">
                <el-switch v-model="foreignKeyRulesForm.允许NULL值" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 外键默认关联策略 -->
        <div class="rules-section">
          <h4>外键默认关联策略</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="默认串联类型">
                <el-select v-model="foreignKeyRulesForm.默认串联类型" placeholder="请选择" style="width: 100%">
                  <el-option label="请选择" value="" />
                  <el-option label="级联删除" value="级联删除" />
                  <el-option label="级联更新" value="级联更新" />
                  <el-option label="设置NULL" value="设置NULL" />
                  <el-option label="限制操作" value="限制操作" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="更新约束力">
                <el-select v-model="foreignKeyRulesForm.更新约束力" placeholder="请选择" style="width: 100%">
                  <el-option label="请选择" value="" />
                  <el-option label="强制约束" value="强制约束" />
                  <el-option label="软约束" value="软约束" />
                  <el-option label="延迟约束" value="延迟约束" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 外键自定义规则 -->
        <div class="rules-section">
          <h4>外键自定义规则</h4>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="外键自定义规则">
                <el-switch v-model="foreignKeyRulesForm.启用自定义规则" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="正则表达式">
                <el-input v-model="foreignKeyRulesForm.正则表达式" placeholder="请输入" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最小长度">
                <el-input v-model="foreignKeyRulesForm.最小长度" placeholder="请输入" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最大长度">
                <el-input v-model="foreignKeyRulesForm.最大长度" placeholder="请输入" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 外键过期设置 -->
        <div class="rules-section">
          <h4>外键过期设置</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="开启外键过期时间">
                <el-switch v-model="foreignKeyRulesForm.开启外键过期时间" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="过期时间">
                <el-select v-model="foreignKeyRulesForm.过期时间" placeholder="请选择年月日" style="width: 100%">
                  <el-option label="请选择年月日" value="" />
                  <el-option label="1个月后" value="1个月后" />
                  <el-option label="3个月后" value="3个月后" />
                  <el-option label="6个月后" value="6个月后" />
                  <el-option label="1年后" value="1年后" />
                  <el-option label="永不过期" value="永不过期" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="过期后行为">
                <el-select v-model="foreignKeyRulesForm.过期后行为" placeholder="请选择" style="width: 100%">
                  <el-option label="请选择" value="" />
                  <el-option label="自动删除" value="自动删除" />
                  <el-option label="标记无效" value="标记无效" />
                  <el-option label="发送警告" value="发送警告" />
                  <el-option label="禁用访问" value="禁用访问" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 搜索性能规则设置 -->
        <div class="rules-section">
          <h4>搜索性能规则设置</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="索引规则">
                <el-select v-model="foreignKeyRulesForm.索引规则" placeholder="不自动创建" style="width: 100%">
                  <el-option label="不自动创建" value="不自动创建" />
                  <el-option label="自动创建索引" value="自动创建索引" />
                  <el-option label="复合索引" value="复合索引" />
                  <el-option label="唯一索引" value="唯一索引" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="性能阈值设置">
                <el-input v-model="foreignKeyRulesForm.性能阈值设置" placeholder="请输入 (秒)" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="缓存控制">
                <el-switch v-model="foreignKeyRulesForm.缓存控制" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 外键关联条件设置 -->
        <div class="rules-section">
          <h4>外键关联条件设置</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="外键关联条件设置">
                <el-select v-model="foreignKeyRulesForm.外键关联条件设置" placeholder="请选择" style="width: 100%">
                  <el-option label="请选择" value="" />
                  <el-option label="严格匹配" value="严格匹配" />
                  <el-option label="模糊匹配" value="模糊匹配" />
                  <el-option label="范围匹配" value="范围匹配" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="关联定时间">
                <el-select v-model="foreignKeyRulesForm.关联定时间" placeholder="请选择年月日" style="width: 100%">
                  <el-option label="请选择年月日" value="" />
                  <el-option label="立即生效" value="立即生效" />
                  <el-option label="延迟1小时" value="延迟1小时" />
                  <el-option label="延迟1天" value="延迟1天" />
                  <el-option label="自定义时间" value="自定义时间" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>
    </Dialog>

    <!-- 外键锁定弹窗 -->
    <Dialog
      v-model="showForeignKeyLockDialog"
      title="外键锁定"
      :destroy-on-close="true"
      confirm-type="primary"
      cancel-type="info"
      @click-confirm="onForeignKeyLockConfirm"
      width="400px"
    >
      <div class="foreign-key-lock-dialog-content">
        <el-form :model="foreignKeyLockForm" label-width="80px">
          <el-form-item label="锁定时间">
            <div class="time-input-group">
              <el-input
                v-model="foreignKeyLockForm.时"
                placeholder="时"
                style="width: 60px; margin-right: 8px;"
                maxlength="2"
              />
              <span style="margin-right: 8px;">时</span>
              <el-input
                v-model="foreignKeyLockForm.分"
                placeholder="分"
                style="width: 60px; margin-right: 8px;"
                maxlength="2"
              />
              <span style="margin-right: 8px;">分</span>
              <el-input
                v-model="foreignKeyLockForm.秒"
                placeholder="秒"
                style="width: 60px; margin-right: 8px;"
                maxlength="2"
              />
              <span>秒</span>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </Dialog>

    <!-- 外键解锁修改弹窗 -->
    <Dialog
      v-model="showForeignKeyUnlockDialog"
      title="外键解锁修改"
      :destroy-on-close="true"
      confirm-type="primary"
      cancel-type="info"
      @click-confirm="onForeignKeyUnlockConfirm"
      width="500px"
    >
      <div class="foreign-key-unlock-dialog-content">
        <el-form :model="foreignKeyUnlockForm" label-width="100px">
          <el-form-item label="解绑模式" required>
            <el-select
              v-model="foreignKeyUnlockForm.解绑模式"
              placeholder="请选择解绑模式"
              style="width: 100%;"
            >
              <el-option label="保持原有约束" value="保持原有约束" />
              <el-option label="永久移除外键" value="永久移除外键" />
            </el-select>
          </el-form-item>

          <!-- 保持原有约束模式的字段 -->
          <template v-if="foreignKeyUnlockForm.解绑模式 === '保持原有约束'">
            <el-form-item label="生效时间">
              <el-select
                v-model="foreignKeyUnlockForm.生效时间"
                placeholder="请选择"
                style="width: 100%;"
              >
                <el-option label="立即生效" value="立即生效" />
                <el-option label="1小时后" value="1小时后" />
                <el-option label="24小时后" value="24小时后" />
                <el-option label="自定义时间" value="自定义时间" />
              </el-select>
            </el-form-item>
            <el-form-item label="恢复时间">
              <el-select
                v-model="foreignKeyUnlockForm.恢复时间"
                placeholder="请选择"
                style="width: 100%;"
              >
                <el-option label="1小时后" value="1小时后" />
                <el-option label="24小时后" value="24小时后" />
                <el-option label="7天后" value="7天后" />
                <el-option label="永不恢复" value="永不恢复" />
              </el-select>
            </el-form-item>
          </template>

          <!-- 永久移除外键模式的字段 -->
          <template v-if="foreignKeyUnlockForm.解绑模式 === '永久移除外键'">
            <el-form-item label="数据处理方式">
              <el-radio-group v-model="foreignKeyUnlockForm.数据处理方式">
                <el-radio value="保留原有值">保留原有值</el-radio>
                <el-radio value="设置为NULL">设置为NULL</el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
        </el-form>
      </div>
    </Dialog>

    <!-- 外键历史记录弹窗 -->
    <Dialog
      v-model="showHistoryDialog"
      title="外键历史记录"
      :destroy-on-close="true"
      width="800px"
      :visible-confirm-button="false"
      cancel-text="关闭"
      @click-cancel="showHistoryDialog = false"
      @click-confirm="showHistoryDialog = false"
    >
      <div class="history-dialog-content">
        <div v-loading="historyLoading" class="history-table-container">
          <el-table
            :data="historyData"
            style="width: 100%"
            :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          >
            <el-table-column prop="序号" label="序号" width="80" align="center" />
            <el-table-column prop="时间" label="时间" width="160" align="center" />
            <el-table-column prop="操作类型" label="操作类型" width="100" align="center">
              <template #default="{ row }">
                <el-tag
                  :type="getOperationTagType(row.操作类型)"
                  size="small"
                >
                  {{ row.操作类型 }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="操作内容" label="操作内容" min-width="200" />
            <el-table-column prop="操作人" label="操作人" width="120" align="center" />
          </el-table>
        </div>

        <!-- 分页组件 -->
        <div class="history-pagination">
          <el-pagination
            v-model:current-page="historyPagination.currentPage"
            v-model:page-size="historyPagination.pageSize"
            :page-sizes="[10, 20, 50]"
            :total="historyPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="handleHistoryPageChange"
            @size-change="handleHistoryPageChange"
          />
        </div>
      </div>
    </Dialog>

    <!-- 外键关联默认值弹窗 -->
    <Dialog
      v-model="showDefaultValueDialog"
      title="外键关联默认值"
      :destroy-on-close="true"
      confirm-type="primary"
      cancel-type="info"
      confirm-text="确认"
      cancel-text="取消"
      @click-confirm="onDefaultValueConfirm"
      width="500px"
    >
      <div class="default-value-dialog-content">
        <!-- 外键认证开关 -->
        <div class="default-value-switch">
          <el-form-item label="外键认证">
            <el-switch
              v-model="defaultValueForm.外键认证"
              active-text="启用"
              inactive-text="禁用"
            />
          </el-form-item>
        </div>

        <!-- 外键关联默认值表单 -->
        <div class="default-value-form">
          <Form
            ref="defaultValueDialogRef"
            v-model="defaultValueForm"
            :props="defaultValueFormProps"
            :enable-button="false"
            :column-count="1"
          ></Form>
        </div>
      </div>
    </Dialog>

    <!-- 外键关联自动修复弹窗 -->
    <Dialog
      v-model="showAutoRepairDialog"
      title="外键关联自动修复"
      :destroy-on-close="true"
      confirm-type="primary"
      cancel-type="info"
      confirm-text="确定"
      cancel-text="取消"
      @click-confirm="onAutoRepairConfirm"
      width="500px"
    >
      <div class="auto-repair-dialog-content">
        <!-- 外键关联自动修复标题 -->
        <div class="auto-repair-header">
          <h4>外键关联自动修复</h4>
        </div>

        <!-- 启用自动修复复选框 -->
        <div class="auto-repair-enable-section" style="margin-bottom: 20px;">
          <el-checkbox v-model="autoRepairForm.启用自动修复">
            启用自动修复
          </el-checkbox>
        </div>

        <!-- 当检测到无效关联时自动尝试修复复选框 -->
        <div class="auto-repair-detect-section" style="margin-bottom: 20px;">
          <el-checkbox v-model="autoRepairForm.当检测到无效关联时自动尝试修复">
            当检测到无效关联时自动尝试修复
          </el-checkbox>
        </div>

        <!-- 修复策略和修复频率表单 -->
        <div class="auto-repair-form" v-if="autoRepairForm.启用自动修复">
          <Form
            ref="autoRepairDialogRef"
            v-model="autoRepairForm"
            :props="autoRepairFormProps"
            :enable-button="false"
            :column-count="1"
          ></Form>
        </div>

        <!-- 底部自动修复开关 -->
        <div class="auto-repair-switch-section" style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e9ecef;">
          <el-form-item>
            <el-switch
              v-model="autoRepairForm.自动修复开关"
              active-text=""
              inactive-text=""
              style="margin-right: 10px;"
            />
            <span style="color: #606266; font-size: 14px;">自动修复总开关</span>
          </el-form-item>
        </div>
      </div>
    </Dialog>

    <!-- 外键权限管理弹窗 -->
    <Dialog
      v-model="showPermissionDialog"
      title="外键权限管理"
      :destroy-on-close="true"
      confirm-type="primary"
      cancel-type="info"
      confirm-text="确认"
      cancel-text="取消"
      @click-confirm="onPermissionConfirm"
      width="500px"
    >
      <div class="permission-dialog-content">
        <!-- 授权人员部分 -->
        <div class="permission-personnel-section">
          <div class="section-header">
            <div class="header-content">
              <span class="required-mark">*</span>
              <span class="section-title">授权人员</span>
            </div>
          </div>

          <!-- 人员搜索框 -->
          <div class="personnel-search">
            <el-input
              v-model="personnelSearchKeyword"
              placeholder="请输入姓名搜索"
              clearable
              @input="filterPersonnelList"
              @clear="clearPersonnelSearch"
              size="default"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="personnel-list" v-loading="personnelLoading" element-loading-text="正在查询授权人员数据..." element-loading-background="rgba(255, 255, 255, 0.9)" element-loading-spinner="el-icon-loading">
            <div
              v-if="authorizedPersonnelList.length === 0 && personnelSearchKeyword.trim() && !personnelLoading"
              class="no-search-result"
            >
              <el-empty description="未找到匹配的人员" :image-size="60" />
            </div>
            <div
              v-for="person in authorizedPersonnelList"
              :key="person.name"
              class="personnel-item"
              :class="{ 'selected': person.selected }"
            >
              <el-checkbox
                v-model="person.selected"
                @change="handlePersonnelSelectionChange(person)"
                class="personnel-checkbox"
              >
                {{ person.name }}
              </el-checkbox>
            </div>
          </div>

          <!-- 人员列表分页 -->
          <div class="personnel-pagination" v-if="filteredPersonnelList.length > 0">
            <el-pagination
              v-model:current-page="personnelPagination.currentPage"
              v-model:page-size="personnelPagination.pageSize"
              :page-sizes="[5, 10, 15, 20]"
              :total="personnelPagination.total"
              :disabled="personnelLoading"
              layout="total, sizes, prev, pager, next"
              @size-change="handlePersonnelSizeChange"
              @current-change="handlePersonnelPageChange"
              small
            />
          </div>
        </div>

        <!-- 操作权限部分 -->
        <div class="permission-operations-section">
          <div class="section-title">操作权限：</div>
          <div class="operations-list">
            <el-checkbox v-model="permissionForm.操作权限.查看" class="operation-checkbox">
              查看
            </el-checkbox>
            <el-checkbox v-model="permissionForm.操作权限.编辑" class="operation-checkbox">
              编辑
            </el-checkbox>
            <el-checkbox v-model="permissionForm.操作权限.删除" class="operation-checkbox">
              删除
            </el-checkbox>
            <el-checkbox v-model="permissionForm.操作权限.新增" class="operation-checkbox">
              新增
            </el-checkbox>
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 外键关联提示弹窗 -->
    <Dialog
      v-model="showForeignKeyTipsDialog"
      title="外键关联提示"
      :destroy-on-close="true"
      confirm-type="primary"
      cancel-type="info"
      @click-confirm="onForeignKeyTipsConfirm"
      width="500px"
    >
      <div class="foreign-key-tips-dialog-content">
        <!-- 外键关联提示表单 -->
        <div class="foreign-key-tips-form">
          <Form
            ref="foreignKeyTipsDialogRef"
            v-model="foreignKeyTipsForm"
            :props="foreignKeyTipsFormProps"
            :enable-button="false"
            :column-count="1"
          ></Form>
        </div>
      </div>
    </Dialog>

    <!-- 外键错误预警弹窗 -->
    <Dialog
      v-model="showErrorWarningDialog"
      title="外键错误预警功能"
      :destroy-on-close="true"
      confirm-type="primary"
      cancel-type="danger"
      confirm-text="确认"
      cancel-text="取消"
      @click-confirm="onErrorWarningConfirm"
      width="500px"
    >
      <div class="error-warning-dialog-content">
        <!-- 启用错误预警复选框 -->
        <div class="warning-enable-section" style="margin-bottom: 20px;">
          <el-checkbox v-model="errorWarningForm.启用错误预警">
            启用错误预警
          </el-checkbox>
          <div style="color: #666; font-size: 12px; margin-top: 5px;">
            当检测到潜在的外键问题时发送预警
          </div>
        </div>

        <!-- 错误阈值设置 -->
        <div class="threshold-section" style="margin-bottom: 20px;" v-if="errorWarningForm.启用错误预警">
          <div style="margin-bottom: 8px; font-weight: 500;">
            <span style="color: red;">*</span>错误阈值 (%)：
          </div>
          <el-input
            v-model="errorWarningForm.错误阈值"
            placeholder="请输入错误阈值百分比"
            style="width: 100%;"
          />
          <div style="color: #666; font-size: 12px; margin-top: 5px;">
            当发现错误记录比例超过此阈值时发送预警。
          </div>
        </div>

        <!-- 通知方式选择 -->
        <div class="notification-section" v-if="errorWarningForm.启用错误预警">
          <div style="margin-bottom: 8px; font-weight: 500;">
            <span style="color: red;">*</span>通知方式：
          </div>
          <el-checkbox-group v-model="errorWarningForm.通知方式">
            <el-checkbox label="邮件内容">邮件内容</el-checkbox>
            <el-checkbox label="备忘录工作提醒">备忘录工作提醒</el-checkbox>
            <el-checkbox label="备忘录计划">备忘录计划</el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
    </Dialog>

    <!-- 外键数据统计弹窗 -->
    <Dialog
      v-model="showForeignKeyStatisticsDialog"
      title="外键关系数据统计"
      :destroy-on-close="true"
      confirm-type="primary"
      cancel-type="info"
      @click-confirm="showForeignKeyStatisticsDialog = false"
      width="700px"
    >
      <div class="foreign-key-statistics-dialog-content">
        <!-- 外键信息统计 -->
        <div class="statistics-info-section">
          <div class="info-title">外键信息统计：</div>
          <div class="info-row">
            <span class="info-label">外键总数：</span>
            <span class="info-value">{{ statisticsData.外键总数 }}</span>
            <span class="info-label">唯一外键数：</span>
            <span class="info-value">{{ statisticsData.唯一外键数 }}</span>
            <span class="info-label">无效外键：</span>
            <span class="info-value">{{ statisticsData.无效外键 }}</span>
          </div>
        </div>

        <!-- 关系类型分布图 -->
        <div class="chart-section">
          <div class="chart-title">关系类型分布：</div>
          <div class="chart-container" style="width: 100%; height: 280px;">
            <div ref="statisticsChartRef" class="statistics-chart" style="width: 100%; height: 280px;"></div>
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 外键关系错误分析弹窗 -->
    <Dialog
      v-model="showForeignKeyErrorDialog"
      title="外键关系错误分析"
      :destroy-on-close="true"
      confirm-type="primary"
      cancel-type="info"
      @click-confirm="onForeignKeyErrorConfirm"
      width="700px"
    >
      <div class="foreign-key-error-dialog-content">
        <!-- 分析说明 -->
        <div class="analysis-description">
          <el-alert
            title="自动检测并分析外键关系中的潜在错误和不一致。"
            type="info"
            :closable="false"
            show-icon
          />
        </div>

        <!-- 运行分析按钮 -->
        <div class="analysis-action" style="margin: 20px 0;">
          <el-button
            type="danger"
            size="large"
            :loading="errorAnalysisLoading"
            @click="performErrorAnalysis"
            style="width: 100%;"
          >
            <el-icon><VideoPlay /></el-icon>
            {{ errorAnalysisLoading ? '正在分析...' : '运行分析错误' }}
          </el-button>
        </div>

        <!-- 错误结果显示 -->
        <div v-if="errorAnalysisResults.length > 0" class="error-results">
          <el-alert
            title="发现以下错误："
            type="error"
            :closable="false"
            show-icon
          />
          <div class="error-list" style="margin-top: 15px;">
            <div
              v-for="(error, index) in errorAnalysisResults"
              :key="error.id"
              class="error-item"
              style="margin-bottom: 10px; padding: 12px; border: 1px solid #f56c6c; border-radius: 4px; background-color: #fef0f0;"
            >
              <div style="display: flex; align-items: center; margin-bottom: 5px;">
                <el-icon color="#f56c6c" style="margin-right: 8px;"><CloseBold /></el-icon>
                <strong>错误{{ index + 1 }}：{{ error.foreignKeyName }}</strong>
              </div>
              <div style="color: #606266; font-size: 14px;">
                {{ error.description }}
              </div>
            </div>
          </div>
        </div>

        <!-- 无错误时的显示 -->
        <div v-else-if="!errorAnalysisLoading && errorAnalysisResults.length === 0" class="no-errors">
          <el-empty description="暂无错误分析结果，请点击上方按钮开始分析" />
        </div>
      </div>
    </Dialog>

    <!-- 外键验证报告弹窗 -->
    <Dialog
      v-model="showValidationReportDialog"
      title="外键验证报告"
      :destroy-on-close="true"
      confirm-type="primary"
      cancel-type="info"
      confirm-text="确定"
      cancel-text="取消"
      @click-confirm="showValidationReportDialog = false"
      width="800px"
    >
      <div class="validation-report-dialog-content">
        <!-- 统计卡片区域 -->
        <div class="statistics-cards" style="display: flex; gap: 20px; margin-bottom: 30px;">
          <div class="stat-card" style="flex: 1; text-align: center; padding: 20px; border: 1px solid #e4e7ed; border-radius: 4px; background-color: #f8f9fa;">
            <div style="font-size: 24px; font-weight: bold; color: #67c23a; margin-bottom: 8px;">
              {{ validationReportData.statistics.有效关联 }}
            </div>
            <div style="color: #606266; font-size: 14px;">有效关联</div>
          </div>
          <div class="stat-card" style="flex: 1; text-align: center; padding: 20px; border: 1px solid #e4e7ed; border-radius: 4px; background-color: #f8f9fa;">
            <div style="font-size: 24px; font-weight: bold; color: #f56c6c; margin-bottom: 8px;">
              {{ validationReportData.statistics.无效关联 }}
            </div>
            <div style="color: #606266; font-size: 14px;">无效关联</div>
          </div>
          <div class="stat-card" style="flex: 1; text-align: center; padding: 20px; border: 1px solid #e4e7ed; border-radius: 4px; background-color: #f8f9fa;">
            <div style="font-size: 24px; font-weight: bold; color: #e6a23c; margin-bottom: 8px;">
              {{ validationReportData.statistics.告警 }}
            </div>
            <div style="color: #606266; font-size: 14px;">告警</div>
          </div>
        </div>

        <!-- 验证结果表格 -->
        <div class="validation-results-table">
          <el-table
            :data="currentPageValidationResults"
            style="width: 100%;"
            border
            v-loading="validationReportLoading"
            element-loading-text="正在加载验证数据..."
            element-loading-background="rgba(255, 255, 255, 0.9)"
          >
            <el-table-column prop="关联名称" label="关联名称" width="200" />
            <el-table-column prop="状态" label="状态" width="100" align="center">
              <template #default="scope">
                <el-tag
                  :type="scope.row.状态 === '有效' ? 'success' : scope.row.状态 === '警告' ? 'warning' : 'danger'"
                  size="small"
                >
                  {{ scope.row.状态 }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="问题描述" label="问题描述" min-width="300">
              <template #default="scope">
                <span v-if="scope.row.问题描述" style="color: #f56c6c;">{{ scope.row.问题描述 }}</span>
                <span v-else style="color: #67c23a;">验证通过</span>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <div class="validation-pagination" style="margin-top: 20px; text-align: right;">
            <el-pagination
              v-model:current-page="validationReportPagination.currentPage"
              v-model:page-size="validationReportPagination.pageSize"
              :page-sizes="[5, 10, 20, 50]"
              :total="validationReportPagination.total"
              :disabled="validationReportLoading"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleValidationReportSizeChange"
              @current-change="handleValidationReportPageChange"
            />
          </div>
        </div>
      </div>
    </Dialog>

    <!-- 外键过滤条件弹窗 -->
    <el-dialog
      v-model="showFilterDialog"
      title="外键关联过滤条件"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="filter-dialog-content">
        <el-form :model="filterForm" label-width="80px">
          <el-form-item label="字段:">
            <el-select v-model="filterForm.field" placeholder="请选择字段" style="width: 100%;">
              <el-option label="外键名称" value="外键名称" />
              <el-option label="外键类型" value="外键类型" />
              <el-option label="说明" value="说明" />
            </el-select>
          </el-form-item>

          <el-form-item label="操作符:">
            <el-select v-model="filterForm.operator" placeholder="请选择操作符" style="width: 100%;">
              <el-option label="等于" value="等于" />
              <el-option label="包含" value="包含" />
              <el-option label="不等于" value="不等于" />
              <el-option label="开始于" value="开始于" />
              <el-option label="结束于" value="结束于" />
            </el-select>
          </el-form-item>

          <el-form-item label="值:">
            <el-input
              v-model="filterForm.value"
              placeholder="请输入过滤值"
              style="width: 100%;"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelFilter" type="info">取消</el-button>
          <el-button @click="confirmFilter" type="primary">添加过滤条件</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<route>
{
  meta: {
    title: '外键定义',
  },
}
</route>

<style scoped lang="scss">
.foreign-key-definition {
  // 移除全局按钮样式，避免影响表格内按钮

  .search {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #e9ecef;

    // 优化搜索表单布局 - 使用更强的选择器和更高优先级
    :deep(.el-form),
    :deep(.el-form.el-form--default),
    :deep(.el-form.el-form--label-left) {
      // 强制应用间距样式，覆盖可能的组件默认样式
      .el-form-item {
        // 输入框表单项（非按钮项）- 大幅增加间距让分离更明显
        &:not(:last-child) {
          margin-bottom: 10px !important; // 大幅增加间距，使用!important
        }
      }

      // 针对可能的网格布局或两列布局
      .el-row {
        .el-col {
          .el-form-item {
            margin-bottom: 60px !important;

            &:not(:last-child) {
              margin-bottom: 80px !important;
            }
          }
        }
      }
    }

    // 针对可能的自定义Form组件样式
    :deep(.form-container),
    :deep(.search-form),
    :deep(.el-form-wrapper) {
      .el-form-item {
        margin-bottom: 60px !important;

        &:not(:last-child) {
          margin-bottom: 80px !important;
        }
      }
    }

    // 只移除文本装饰下划线，保持输入框正常边框
    .el-form-item__content {
      // 只移除文本装饰，不影响边框
      .el-input,
      .el-select {
        .el-input__wrapper,
        .el-select__wrapper {
          text-decoration: none !important; // 只移除文本装饰下划线
          // 保持正常边框，不强制移除
        }

        .el-input__inner,
        .el-select__input {
          text-decoration: none !important; // 只移除文本装饰下划线
          // 保持正常边框和样式
        }
      }
    }

    // 按钮区域 - 减少上边距，让按钮往上靠
    .el-form-item:last-child {
      margin-top: 16px; // 从24px减少到16px，让按钮往上靠
      margin-bottom: 0;
      padding-top: 8px; // 从12px减少到8px
      border-top: 1px solid #e9ecef; // 添加分隔线

      .el-button {
        margin-right: 12px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  // 只移除链接的文本装饰下划线，不影响表单元素边框
  :deep(a),
  :deep(.el-link) {
    text-decoration: none !important;

    &:focus,
    &:active,
    &:hover,
    &:visited {
      text-decoration: none !important;
    }
  }

  // 确保表格和其他组件没有多余的下划线
  :deep(.el-table) {
    border-bottom: none !important;

    th, td {
      border-bottom: 1px solid #f0f0f0 !important; // 保留表格分隔线但移除其他下划线
      text-decoration: none !important;
    }
  }

  // 移除链接和按钮的下划线
  :deep(a),
  :deep(.el-button),
  :deep(.el-link) {
    text-decoration: none !important;
    border-bottom: none !important;

    &:hover,
    &:focus,
    &:active {
      text-decoration: none !important;
      border-bottom: none !important;
    }
  }

  // 批量修改对话框样式
  .batch-dialog-content {
    .batch-form {
      h4 {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }

      .form-tip {
        margin: 16px 0 0 0;
        color: #909399;
        font-size: 12px;
        text-align: center;
        background-color: #f0f9ff;
        padding: 8px;
        border-radius: 4px;
        border-left: 3px solid #409eff;
      }
    }
  }

  // 外键规则设置对话框样式
  .foreign-key-rules-dialog-content {
    .rules-section {
      margin-bottom: 24px;
      padding: 16px;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      background-color: #fafbfc;

      h4 {
        margin: 0 0 16px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 8px;
      }

      .el-form-item {
        margin-bottom: 16px;

        .el-form-item__label {
          font-weight: 500;
          color: #606266;
        }
      }

      .el-row:last-child .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  // 外键数据统计对话框样式
  .foreign-key-statistics-dialog-content {
    padding: 0;

    .statistics-info-section {
      background-color: #e8f4fd;
      padding: 20px 20px;
      margin-bottom: 20px;

      .info-title {
        color: #333;
        font-size: 14px;
        font-weight: 600;
        line-height: 1.6;
        margin-bottom: 16px;
      }

      .info-row {
        display: flex;
        align-items: center;
        gap: 20px;
        line-height: 1.8;

        .info-label {
          color: #666;
          font-size: 13px;
        }

        .info-value {
          color: #333;
          font-size: 13px;
          font-weight: 600;
          margin-right: 20px;
        }
      }
    }

    .chart-section {
      padding: 0 20px 20px;

      .chart-title {
        color: #333;
        font-size: 14px;
        font-weight: 600;
        line-height: 1.6;
        margin-bottom: 20px;
        padding-top: 4px;
      }

      .chart-container {
        width: 100%;
        height: 280px;

        .statistics-chart {
          width: 100% !important;
          height: 280px !important;
          min-height: 280px;
        }
      }
    }
  }

  // 表格操作按钮样式
  .table-actions {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: nowrap; // 禁止换行
    gap: 4px; // 进一步减小间距
    white-space: nowrap; // 确保不换行

    .el-button {
      padding: 2px 6px; // 进一步减小内边距
      font-size: 11px; // 减小字体
      height: 24px; // 进一步减小高度
      line-height: 1.3;
      border-radius: 3px;
      font-weight: 400;
      flex-shrink: 0; // 防止按钮被压缩

      // 确保按钮有合适的最小宽度
      min-width: 40px;

      // 优化按钮间距
      &:not(:last-child) {
        margin-right: 0;
      }

      // 按钮文字不换行
      span {
        white-space: nowrap;
        font-size: 11px;
      }
    }

    // 下拉菜单按钮特殊样式
    .el-dropdown {
      flex-shrink: 0; // 防止下拉菜单被压缩

      .el-button {
        display: flex;
        align-items: center;
        min-width: 64px; // 下拉菜单按钮适当调整宽度
        padding: 2px 6px;

        .el-icon {
          margin-left: 2px;
          font-size: 10px;
        }
      }
    }
  }

  // 让说明列自适应剩余空间
  :deep(.el-table th),
  :deep(.el-table td) {
    &:nth-child(7) { // 说明列（第7列，包含选择列和序号列）
      width: auto !important;
      min-width: 200px !important;
    }
  }

  // 外键锁定对话框样式
  .foreign-key-lock-dialog-content {
    .time-input-group {
      display: flex;
      align-items: center;

      .el-input {
        text-align: center;

        :deep(.el-input__inner) {
          text-align: center;
        }
      }

      span {
        font-size: 14px;
        color: #606266;
      }
    }
  }

  // 外键解锁修改对话框样式
  .foreign-key-unlock-dialog-content {
    .el-form-item {
      margin-bottom: 20px;
    }

    .el-select {
      width: 100%;
    }

    .el-radio-group {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .el-radio {
        margin-right: 0;
        margin-bottom: 8px;
      }
    }
  }

  // 外键关联默认值对话框样式
  .default-value-dialog-content {
    .default-value-switch {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e9ecef;

      .el-form-item {
        margin-bottom: 0;

        .el-form-item__label {
          font-weight: 600;
          color: #333;
        }

        .el-switch {
          .el-switch__label {
            font-size: 14px;
            color: #666;
          }
        }
      }
    }

    .default-value-form {
      .el-input {
        border-radius: 4px;
      }

      .el-select {
        width: 100%;
      }
    }
  }

  // 历史记录对话框样式
  .history-dialog-content {
    .history-table-container {
      min-height: 400px;
      margin-bottom: 20px;

      .el-table {
        border: 1px solid #ebeef5;
        border-radius: 4px;

        .el-table__header {
          th {
            background-color: #f5f7fa !important;
            color: #606266 !important;
            font-weight: 600;
          }
        }

        .el-table__body {
          tr:hover {
            background-color: #f5f7fa;
          }
        }
      }
    }

    .history-pagination {
      display: flex;
      justify-content: center;
      padding: 20px 0;
      border-top: 1px solid #ebeef5;

      .el-pagination {
        .el-pagination__total {
          color: #606266;
        }

        .el-pagination__sizes {
          .el-select {
            .el-input {
              width: 100px;
            }
          }
        }
      }
    }
  }

  // 外键关联自动修复对话框样式
  .auto-repair-dialog-content {
    .auto-repair-header {
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #e9ecef;

      h4 {
        margin: 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
        text-align: center;
      }
    }

    .auto-repair-enable-section,
    .auto-repair-detect-section {
      padding: 12px 16px;
      background-color: #f8f9fa;
      border-radius: 6px;
      border: 1px solid #e9ecef;

      .el-checkbox {
        .el-checkbox__label {
          font-size: 14px;
          color: #333;
          font-weight: 500;
        }
      }
    }

    .auto-repair-form {
      margin-top: 20px;
      padding: 16px;
      background-color: #fafbfc;
      border-radius: 6px;
      border: 1px solid #e9ecef;

      .el-form-item {
        margin-bottom: 16px;

        .el-form-item__label {
          font-weight: 500;
          color: #606266;
        }

        .el-select {
          width: 100%;
        }
      }
    }

    .auto-repair-switch-section {
      display: flex;
      align-items: center;
      justify-content: center;

      .el-form-item {
        margin-bottom: 0;
        display: flex;
        align-items: center;

        .el-switch {
          margin-right: 10px;
        }
      }
    }
  }

  // 外键权限管理对话框样式
  .permission-dialog-content {
    .permission-personnel-section {
      margin-bottom: 30px;

      .section-header {
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 2px solid #e9ecef;

        .header-content {
          display: flex;
          align-items: center;

          .required-mark {
            color: #ff0000;
            font-weight: bold;
            font-size: 16px;
            margin-right: 6px;
            text-shadow: 0 1px 2px rgba(255, 0, 0, 0.3);
          }

          .section-title {
            color: #303133;
            font-size: 16px;
            font-weight: 700;
            letter-spacing: 0.5px;
          }
        }
      }

      .personnel-search {
        margin-bottom: 18px;

        .el-input {
          .el-input__wrapper {
            border-radius: 8px;
            height: 40px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;

            &:hover {
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            }

            &.is-focus {
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            }
          }

          .el-input__inner {
            font-size: 14px;
            height: 38px;
            line-height: 38px;
          }

          .el-input__prefix {
            display: flex;
            align-items: center;
            color: #909399;
          }
        }
      }

      .personnel-list {
        height: 200px;
        overflow-y: auto;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        background-color: #fafbfc;
        margin-bottom: 15px;

        .no-search-result {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #909399;
        }

        .personnel-item {
          padding: 8px 12px;
          border-bottom: 1px solid #f0f0f0;
          transition: background-color 0.2s;

          &:last-child {
            border-bottom: none;
          }

          &:hover {
            background-color: #f5f7fa;
          }

          &.selected {
            background-color: #e8f4fd;

            .personnel-checkbox {
              .el-checkbox__label {
                color: #409eff;
                font-weight: 500;
              }
            }
          }

          .personnel-checkbox {
            width: 100%;

            .el-checkbox__label {
              font-size: 14px;
              color: #606266;
            }
          }
        }
      }

      .personnel-pagination {
        display: flex;
        justify-content: center;
        padding: 10px 0;
        border-top: 1px solid #e9ecef;
        background-color: #fafbfc;

        .el-pagination {
          .el-pagination__total {
            color: #606266;
            font-size: 12px;
          }

          .el-pagination__sizes {
            .el-select {
              .el-input {
                width: 80px;
              }
            }
          }

          .el-pager {
            .el-pager__item {
              font-size: 12px;
              min-width: 24px;
              height: 24px;
              line-height: 24px;
            }
          }

          .el-pagination__prev,
          .el-pagination__next {
            font-size: 12px;
            height: 24px;
            line-height: 24px;
          }
        }
      }
    }

    .permission-operations-section {
      .section-title {
        color: #303133;
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e9ecef;
      }

      .operations-list {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        padding: 16px;
        background-color: #fafbfc;
        border-radius: 6px;
        border: 1px solid #e9ecef;

        .operation-checkbox {
          .el-checkbox__label {
            font-size: 14px;
            color: #606266;
            font-weight: 500;
          }

          &.is-checked {
            .el-checkbox__label {
              color: #409eff;
            }
          }
        }
      }
    }
  }
}
</style>