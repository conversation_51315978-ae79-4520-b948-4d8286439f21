<template>
	<div class="unauthorized-container">
		<div class="unauthorized-content">
			<img src="@/assets/image/401.png" alt="401" class="unauthorized-image" />
			<h1 class="unauthorized-title">身份认证过期</h1>
			<p class="unauthorized-message">抱歉，您的身份认证已过期，请从渝快政工作台重新登入。</p>
		</div>
	</div>
</template>

<script setup lang="ts">
// 401 页面 - 身份认证过期
</script>

<style scoped lang="scss">
.unauthorized-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 100vh;
	background-color: #f5f7fa;
}

.unauthorized-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
	padding: 20px;
}

.unauthorized-image {
	width: 120px;
	height: 120px;
	margin-bottom: 20px;
}

.unauthorized-title {
	font-size: 24px;
	color: #333;
	margin-bottom: 16px;
	font-weight: 500;
}

.unauthorized-message {
	font-size: 16px;
	color: #666;
	line-height: 1.5;
	max-width: 500px;
}
</style>
