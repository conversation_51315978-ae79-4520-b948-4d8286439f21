<!-- 子任务关系展示测试页面 -->
<script setup lang="ts">
import { ref } from 'vue'
import SubtaskRelationDialog from '@/components/SubtaskRelationDialog.vue'

// 页面标题
defineOptions({
  name: 'SubtaskRelationTest'
})

// 弹窗状态
const dialogVisible = ref(false)

// 打开弹窗
const openDialog = () => {
  dialogVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false
}
</script>

<template>
  <div class="subtask-relation-test">
    <div class="page-header">
      <h1>子任务关系展示弹窗测试</h1>
      <p>这是一个测试页面，用于展示子任务关系展示弹窗的功能。</p>
    </div>
    
    <div class="page-content">
      <div class="demo-section">
        <h2>功能演示</h2>
        <p>点击下面的按钮打开子任务关系展示弹窗：</p>
        
        <el-button type="primary" @click="openDialog" size="large">
          <i class="el-icon-view"></i>
          打开子任务关系展示弹窗
        </el-button>
      </div>
      
      <div class="feature-section">
        <h2>功能特性</h2>
        <div class="feature-grid">
          <div class="feature-item">
            <h3>业务报表子任务</h3>
            <ul>
              <li>使用 @vue-flow/core 实现流程图展示</li>
              <li>支持节点拖拽和缩放操作</li>
              <li>动画连接线效果</li>
              <li>右侧倾向关系分析面板</li>
            </ul>
          </div>
          
          <div class="feature-item">
            <h3>临时报表子任务</h3>
            <ul>
              <li>横向关系和纵向关系表格展示</li>
              <li>支持分页功能</li>
              <li>状态标签区分（已响应/待响应）</li>
              <li>右侧倾向关系分析面板</li>
            </ul>
          </div>
          
          <div class="feature-item">
            <h3>交互功能</h3>
            <ul>
              <li>Tab 切换功能</li>
              <li>弹窗拖拽和关闭</li>
              <li>响应式设计</li>
              <li>数据本地存储</li>
            </ul>
          </div>
          
          <div class="feature-item">
            <h3>技术实现</h3>
            <ul>
              <li>Vue 3 + TypeScript</li>
              <li>Element Plus UI 组件</li>
              <li>@vue-flow/core 流程图</li>
              <li>SCSS 样式预处理</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 子任务关系展示弹窗 -->
    <SubtaskRelationDialog
      v-model:visible="dialogVisible"
      title="子任务关系展示"
      @close="closeDialog"
    />
  </div>
</template>

<style scoped lang="scss">
.subtask-relation-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  
  .page-header {
    text-align: center;
    margin-bottom: 40px;
    
    h1 {
      color: #303133;
      margin-bottom: 10px;
      font-size: 28px;
      font-weight: 500;
    }
    
    p {
      color: #606266;
      font-size: 16px;
      margin: 0;
    }
  }
  
  .page-content {
    .demo-section {
      text-align: center;
      padding: 40px;
      background: #f8f9fa;
      border-radius: 8px;
      margin-bottom: 30px;
      
      h2 {
        color: #303133;
        margin-bottom: 15px;
        font-size: 20px;
        font-weight: 500;
      }
      
      p {
        color: #606266;
        margin-bottom: 25px;
        font-size: 14px;
      }
    }
    
    .feature-section {
      h2 {
        color: #303133;
        margin-bottom: 20px;
        font-size: 20px;
        font-weight: 500;
        text-align: center;
      }
      
      .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        
        .feature-item {
          background: white;
          border: 1px solid #e4e7ed;
          border-radius: 8px;
          padding: 20px;
          
          h3 {
            color: #409eff;
            margin-bottom: 15px;
            font-size: 16px;
            font-weight: 500;
          }
          
          ul {
            margin: 0;
            padding-left: 20px;
            
            li {
              color: #606266;
              margin-bottom: 8px;
              font-size: 14px;
              line-height: 1.5;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .subtask-relation-test {
    padding: 15px;
    
    .page-header {
      h1 {
        font-size: 24px;
      }
    }
    
    .page-content {
      .demo-section {
        padding: 30px 20px;
      }
      
      .feature-section {
        .feature-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
</style>
