<!-- 测试OverviewDashboard组件 -->
<template>
  <div class="test-page">
    <h1>OverviewDashboard 组件测试</h1>
    
    <div class="test-controls">
      <el-button @click="toggleLoading">{{ loading ? '停止加载' : '开始加载' }}</el-button>
      <el-button @click="refreshData">刷新数据</el-button>
      <el-button @click="exportData">导出数据</el-button>
    </div>

    <div class="dashboard-container">
      <OverviewDashboard
        :data="dashboardData"
        :config="dashboardConfig"
        :loading="loading"
        @refresh="handleRefresh"
        @row-click="handleRowClick"
        @data-point-click="handleDataPointClick"
        @export="handleExport"
        @chart-click="handleChartClick"
      />
    </div>

    <!-- 事件日志 -->
    <div class="event-log">
      <h3>事件日志</h3>
      <div class="log-content">
        <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-event">{{ log.event }}</span>
          <span class="log-data">{{ log.data }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import OverviewDashboard from '../index/taskObjectiveDecomposition/dashboard/components/OverviewDashboard.vue'
import type { DashboardData, ChartConfig, TableRow, DataPoint } from '../index/taskObjectiveDecomposition/dashboard/types/dashboard.types'

// 响应式数据
const loading = ref(false)
const eventLogs = ref<Array<{ time: string; event: string; data: string }>>([])

// 模拟仪表板数据
const dashboardData = reactive<DashboardData>({
  tableData: {
    columns: [
      { key: 'name', label: '任务名称' },
      { key: 'status', label: '状态' },
      { key: 'progress', label: '进度' },
      { key: 'assignee', label: '负责人' }
    ],
    rows: [
      { id: '1', name: '需求分析', status: '已完成', progress: '100%', assignee: '李工程师' },
      { id: '2', name: '系统设计', status: '进行中', progress: '80%', assignee: '赵设计师' },
      { id: '3', name: '开发编码', status: '进行中', progress: '45%', assignee: '王分析师' },
      { id: '4', name: '测试验收', status: '未开始', progress: '0%', assignee: '测试团队' },
      { id: '5', name: '部署上线', status: '未开始', progress: '0%', assignee: '运维团队' }
    ]
  },
  chartData: {
    barChart: {
      categories: ['需求分析', '系统设计', '开发编码', '测试验收', '部署上线'],
      series: [
        {
          name: '完成进度',
          data: [100, 80, 45, 0, 0],
          color: '#409EFF'
        }
      ]
    },
    lineChart: {
      categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
      series: [
        {
          name: '项目进度',
          data: [10, 25, 45, 60, 75, 85],
          color: '#67C23A'
        },
        {
          name: '预期进度',
          data: [15, 30, 50, 70, 85, 100],
          color: '#E6A23C'
        }
      ]
    },
    pieChart: {
      series: [
        {
          name: '任务状态分布',
          data: [
            { name: '已完成', value: 1, color: '#67C23A' },
            { name: '进行中', value: 2, color: '#409EFF' },
            { name: '未开始', value: 2, color: '#909399' }
          ]
        }
      ]
    }
  }
})

// 仪表板配置
const dashboardConfig = reactive<ChartConfig>({
  barChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    orientation: 'vertical',
    showDataLabels: true,
    barWidth: 60,
    spacing: 10
  },
  lineChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    smooth: false,
    showPoints: true,
    showArea: false,
    lineWidth: 2
  },
  pieChart: {
    theme: 'light',
    animation: true,
    responsive: true,
    showLegend: true,
    showTooltip: true,
    showPercentage: true,
    innerRadius: 0,
    outerRadius: 80,
    startAngle: 90
  }
})

// 添加事件日志
const addLog = (event: string, data: any = '') => {
  const time = new Date().toLocaleTimeString()
  eventLogs.value.unshift({
    time,
    event,
    data: typeof data === 'object' ? JSON.stringify(data) : String(data)
  })
  
  // 限制日志数量
  if (eventLogs.value.length > 50) {
    eventLogs.value = eventLogs.value.slice(0, 50)
  }
}

// 事件处理函数
const toggleLoading = () => {
  loading.value = !loading.value
  addLog('切换加载状态', loading.value)
}

const refreshData = () => {
  addLog('刷新数据')
  // 模拟数据更新
  dashboardData.chartData.barChart.series[0].data = dashboardData.chartData.barChart.series[0].data.map(
    () => Math.floor(Math.random() * 100)
  )
}

const exportData = () => {
  addLog('导出数据')
}

const handleRefresh = () => {
  addLog('Dashboard刷新事件')
}

const handleRowClick = (row: TableRow) => {
  addLog('表格行点击', row)
}

const handleDataPointClick = (point: DataPoint) => {
  addLog('数据点点击', point)
}

const handleExport = () => {
  addLog('导出事件')
}

const handleChartClick = (chartType: string, data: any) => {
  addLog('图表点击', { chartType, hasData: !!data })
}

// 初始化日志
addLog('组件初始化完成')
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;

  h1 {
    color: #303133;
    margin-bottom: 20px;
  }

  .test-controls {
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
  }

  .dashboard-container {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    background: #fafafa;
  }

  .event-log {
    h3 {
      color: #303133;
      margin-bottom: 12px;
    }

    .log-content {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 12px;
      background: white;

      .log-item {
        display: flex;
        gap: 12px;
        padding: 4px 0;
        border-bottom: 1px solid #f0f0f0;
        font-size: 12px;

        &:last-child {
          border-bottom: none;
        }

        .log-time {
          color: #909399;
          min-width: 80px;
        }

        .log-event {
          color: #409EFF;
          min-width: 120px;
          font-weight: 500;
        }

        .log-data {
          color: #606266;
          flex: 1;
          word-break: break-all;
        }
      }
    }
  }
}
</style>
