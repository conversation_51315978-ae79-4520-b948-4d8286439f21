<script setup lang="ts">
import {useViewStore} from '@/stores/useViewStore'

const store = useViewStore()
store.setBuildStats(true)
</script>
<template>
	<div class="build">
		<div class="build-info">
			<h1>系统升级中...</h1>
		</div>
	</div>
</template>
<style scoped lang="scss">
.build {
	background: url('/building.jpg') no-repeat center;
	background-size: cover;
	position: relative;
	height: 100%;
	width: 100%;

	&-info {
		position: fixed;
		left: 8%;
		top: calc(50% - 2rem);

		h1 {
			color: #2c3e50;
			font-size: 4rem;
		}
	}
}
</style>
