<script setup lang="ts">
import {computed, onMounted} from 'vue'

const info = computed(() => window.GOV_CONFIG.ForbiddenInfo)
onMounted(() => {
	localStorage.clear()
})
</script>
<template>
	<div class="no-permissions">
		<div class="box">
			您无该应用访问权限!
			<br />
			{{ info }}
			<!-- 如需开通访问权限，请联系应用管理员，电话:18829571892 -->
			<!-- 如需开通访问权限，请联系应用管理员，电话：13638383824 -->
		</div>
	</div>
</template>
<style scoped lang="scss">
.no-permissions {
	padding-top: 50px;
	height: 100%;
	width: 100%;
	.box {
		font-size: 16px;
		margin: 0 auto;
		line-height: 2;
		width: 500px;

		color: #94969a;
		background: #eef2fb;
		border-radius: 20px;
		padding: 10px 15px;
	}
}
</style>
