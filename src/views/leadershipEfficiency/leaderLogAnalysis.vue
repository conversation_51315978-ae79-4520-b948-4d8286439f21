<script setup lang="ts" name="leaderLogAnalysis">
// 领导日志分析数据接口
interface LeaderLogData {
  id: string
  sequence: number
  department: string
  leaderRole: string
  todayApprovalCount: number
  avgReviewTime: string
  rejectionCount: number
}

// 移除搜索表单相关代码

// 加载状态
const loading = ref(false)

const tableHeight = ref(0) // 表格高度

// 表头配置 - 优化列宽分配，使用百分比实现响应式
const columns = [
  { prop: 'sequence', label: '序号', align: 'center' ,width: 120},
  { prop: 'department', label: '所属部门', align: 'left' },
  { prop: 'leaderRole', label: '领导角色', align: 'center' },
  { prop: 'todayApprovalCount', label: '今日审批量', align: 'center' },
  { prop: 'avgReviewTime', label: '平均审核耗时', align: 'center' },
  { prop: 'rejectionCount', label: '驳回次数', align: 'center' }
]

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
})

// 列表请求参数
const reqParams = reactive({
  skipCount: 0,
  maxResultCount: 10,
})

// 本地存储key
const STORAGE_KEY = 'leaderLogAnalysisData'

// 生成模拟数据
const generateMockData = (): LeaderLogData[] => {
  const departments = ['市政府办公室', '发展改革委', '教育局', '科技局', '工信局', '公安局', '民政局', '司法局', '财政局', '人社局', '自然资源局', '生态环境局', '住建局', '交通运输局', '水利局', '农业农村局', '商务局', '文旅局', '卫健委', '应急管理局', '市场监管局', '统计局', '医保局', '城管局', '税务局']
  const roles = ['局长', '副局长', '主任', '副主任', '处长', '副处长', '科长', '副科长']
  
  const data: LeaderLogData[] = []
  
  for (let i = 1; i <= 25; i++) {
    const department = departments[Math.floor(Math.random() * departments.length)]
    const role = roles[Math.floor(Math.random() * roles.length)]
    const approvalCount = Math.floor(Math.random() * 50) + 1
    const reviewHours = Math.floor(Math.random() * 8) + 1
    const reviewMinutes = Math.floor(Math.random() * 60)
    const rejectionCount = Math.floor(Math.random() * 10)
    
    data.push({
      id: `leader_${i}`,
      sequence: i,
      department,
      leaderRole: role,
      todayApprovalCount: approvalCount,
      avgReviewTime: `${reviewHours}h${reviewMinutes}m`,
      rejectionCount
    })
  }
  
  return data
}

// 获取本地存储数据
const getStoredData = (): LeaderLogData[] => {
  const stored = localStorage.getItem(STORAGE_KEY)
  if (stored) {
    return JSON.parse(stored)
  }
  
  // 如果没有存储数据，生成并保存模拟数据
  const mockData = generateMockData()
  localStorage.setItem(STORAGE_KEY, JSON.stringify(mockData))
  return mockData
}

// 全部数据
const allData = ref<LeaderLogData[]>([])
// 当前页数据
const currentPageData = ref<LeaderLogData[]>([])

// 初始化数据
const initData = () => {
  allData.value = getStoredData()
  updatePagination()
  loadCurrentPageData()
}

// 更新分页信息
const updatePagination = () => {
  const filteredData = getFilteredData()
  pagination.total = filteredData.length
}

// 获取过滤后的数据（无筛选条件，返回全部数据）
const getFilteredData = (): LeaderLogData[] => {
  return allData.value
}

// 加载当前页数据
const loadCurrentPageData = () => {
  const filteredData = getFilteredData()
  const startIndex = (pagination.page - 1) * pagination.size
  const endIndex = startIndex + pagination.size
  currentPageData.value = filteredData.slice(startIndex, endIndex)
}

// 移除查询功能

// 分页事件
const onPaginationChange = (val: any, type: any) => {
  if (type == 'page') {
    pagination.page = val
    reqParams.skipCount = (val - 1) * pagination.size
  } else {
    pagination.size = val
    reqParams.maxResultCount = pagination.size
    pagination.page = 1
    reqParams.skipCount = 0
  }
  
  updatePagination()
  loadCurrentPageData()
}

// 块高度变化事件时修改表格高度
const onBlockHeightChanged = (height: any) => {
  tableHeight.value = height - 75
}

// 移除不需要的模拟接口

// 页面加载时初始化数据
onMounted(() => {
  initData()
})
</script>

<template>
  <div class="leader-log-analysis">
    <Block title="主要领导日志分析" :enable-fixed-height="true" @height-changed="onBlockHeightChanged">
      
      <!-- 列表 -->
      <div class="table-container">
        <el-table
          :data="currentPageData"
          :height="tableHeight"
          stripe
          border
          style="width: 100vw"
          v-loading="loading"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold' }"
        >
          <el-table-column
            v-for="column in columns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :align="column.align"
            :show-overflow-tooltip="true"
          >
            <template #default="{ row }">
              <span v-if="column.prop === 'todayApprovalCount'" class="approval-count">{{ row[column.prop] }}件</span>
              <span v-else-if="column.prop === 'rejectionCount'" class="rejection-count">{{ row[column.prop] }}次</span>
              <span v-else-if="column.prop === 'avgReviewTime'" class="review-time">{{ row[column.prop] }}</span>
              <span v-else>{{ row[column.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 分页 -->
      <Pagination
        :total="pagination.total"
        :current-page="pagination.page"
        :page-size="pagination.size"
        @current-change="onPaginationChange($event, 'page')"
        @size-change="onPaginationChange($event, 'size')"
      ></Pagination>
    </Block>
  </div>
</template>

<route>
{
  meta: {
    title: '主要领导日志分析',
  },
}
</route>

<style scoped lang="scss">
.leader-log-analysis {
  .table-container {
    margin-bottom: 16px;
    width: 100vw;
  }

  // 数据样式优化
  :deep(.approval-count) {
    color: #409eff;
    font-weight: 600;
  }

  :deep(.rejection-count) {
    color: #f56c6c;
    font-weight: 600;
  }

  :deep(.review-time) {
    color: #909399;
    font-weight: 500;
  }

  // 表格样式优化
  :deep(.el-table) {
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #f5f7fa !important;
          border-bottom: 2px solid #e4e7ed;
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover {
            background-color: #f5f7fa;
          }
        }
      }
    }
  }
}
</style>
