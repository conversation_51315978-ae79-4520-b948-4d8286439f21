<!-- 图谱测试页面 -->
<script setup lang="ts" name="TestGraph">
import { ref, onMounted } from 'vue'
import { VueFlow } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'
import { MiniMap } from '@vue-flow/minimap'
import type { Node, Edge } from '@vue-flow/core'

// 测试数据
const nodes = ref<Node[]>([
  {
    id: '1',
    type: 'default',
    position: { x: 250, y: 50 },
    data: { label: '主任务节点' },
    style: {
      backgroundColor: '#1890ff',
      color: 'white',
      border: '2px solid #096dd9',
      borderRadius: '50%',
      width: '120px',
      height: '120px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }
  },
  {
    id: '2',
    type: 'default',
    position: { x: 100, y: 200 },
    data: { label: '子任务1' },
    style: {
      backgroundColor: '#13c2c2',
      color: 'white',
      border: '2px solid #08979c',
      borderRadius: '50%',
      width: '100px',
      height: '100px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }
  },
  {
    id: '3',
    type: 'default',
    position: { x: 400, y: 200 },
    data: { label: '子任务2' },
    style: {
      backgroundColor: '#52c41a',
      color: 'white',
      border: '2px solid #389e0d',
      borderRadius: '50%',
      width: '100px',
      height: '100px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }
  },
  {
    id: '4',
    type: 'default',
    position: { x: 250, y: 350 },
    data: { label: '说明节点' },
    style: {
      backgroundColor: '#ff4d4f',
      color: 'white',
      border: '2px solid #cf1322',
      borderRadius: '50%',
      width: '80px',
      height: '80px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '12px'
    }
  }
])

const edges = ref<Edge[]>([
  {
    id: 'e1-2',
    source: '1',
    target: '2',
    type: 'default',
    style: { stroke: '#1890ff', strokeWidth: 2 },
    markerEnd: {
      type: 'arrowclosed',
      color: '#1890ff'
    }
  },
  {
    id: 'e1-3',
    source: '1',
    target: '3',
    type: 'default',
    style: { stroke: '#1890ff', strokeWidth: 2 },
    markerEnd: {
      type: 'arrowclosed',
      color: '#1890ff'
    }
  },
  {
    id: 'e2-4',
    source: '2',
    target: '4',
    type: 'default',
    style: { stroke: '#52c41a', strokeWidth: 2, strokeDasharray: '5,5' },
    markerEnd: {
      type: 'arrowclosed',
      color: '#52c41a'
    },
    animated: true
  }
])

onMounted(() => {
  console.log('图谱测试页面已加载')
})
</script>

<template>
  <div class="test-graph-page">
    <div class="header">
      <h1>任务关系图谱测试页面</h1>
      <p>这是一个简单的图谱测试，验证Vue Flow组件是否正常工作</p>
    </div>
    
    <div class="graph-container">
      <VueFlow
        :nodes="nodes"
        :edges="edges"
        :zoom-on-scroll="true"
        :pan-on-scroll="true"
        :zoom-on-pinch="true"
        :pan-on-drag="true"
        :default-zoom="1"
        :min-zoom="0.1"
        :max-zoom="4"
        :fit-view-on-init="true"
        class="vue-flow-container"
      >
        <!-- 背景网格 -->
        <Background pattern-color="#e4e7ed" :gap="20" />
        
        <!-- 控制面板 -->
        <Controls />
        
        <!-- 小地图 -->
        <MiniMap 
          :node-color="(node) => node.style?.backgroundColor || '#1890ff'"
          :mask-color="'rgba(240, 242, 247, 0.7)'"
          position="bottom-right"
        />
      </VueFlow>
    </div>
    
    <div class="info-panel">
      <h3>测试信息</h3>
      <ul>
        <li>节点数量: {{ nodes.length }}</li>
        <li>连线数量: {{ edges.length }}</li>
        <li>Vue Flow版本: 已安装</li>
        <li>状态: 正常运行</li>
      </ul>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '@vue-flow/core/dist/style.css';
@import '@vue-flow/core/dist/theme-default.css';

.test-graph-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
  
  .header {
    padding: 20px;
    background: white;
    border-bottom: 1px solid #e8e8e8;
    text-align: center;
    
    h1 {
      margin: 0 0 10px 0;
      color: #1890ff;
      font-size: 24px;
    }
    
    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }
  
  .graph-container {
    flex: 1;
    margin: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .vue-flow-container {
      width: 100%;
      height: 100%;
    }
  }
  
  .info-panel {
    padding: 20px;
    background: white;
    border-top: 1px solid #e8e8e8;
    
    h3 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 16px;
    }
    
    ul {
      margin: 0;
      padding: 0;
      list-style: none;
      display: flex;
      gap: 20px;
      
      li {
        padding: 5px 10px;
        background: #f5f5f5;
        border-radius: 4px;
        font-size: 12px;
        color: #666;
      }
    }
  }
}

// Vue Flow自定义样式
:deep(.vue-flow__node) {
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

:deep(.vue-flow__edge) {
  cursor: pointer;
}

:deep(.vue-flow__controls) {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.vue-flow__minimap) {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
