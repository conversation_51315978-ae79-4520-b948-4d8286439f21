<script setup lang="ts">
import {
	App,
	computed,
	inject,
	nextTick,
	onBeforeMount,
	onBeforeUnmount,
	onMounted,
	onUnmounted,
	provide,
	ref,
	watch,
} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {useViewStore} from '@/stores/useViewStore'
import {useUserStore} from '@/stores/useUserStore'
import LoadingComp from '@/components/common/loading-comp.vue'
import {Request} from '@/api/interface'
import Cookies from 'js-cookie'
import dd from 'gdt-jsapi'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

// 引入md 转 html 插件
import MarkdownIt from 'markdown-it'
// 引入 html转word插件
import {asBlob} from 'html-docx-js-typescript'
import FileSaver from 'file-saver'

import {ElMessageBox, ElMessage, dayjs} from 'element-plus'
import {usePagePermissions} from '@/hooks/usePagePermissions'
import {ArrowDown} from '@element-plus/icons-vue'
import {
	getApplicationConfiguration,
	getMyNotifilers,
	clearNotifilers,
	getNoticeBigTypes,
	getUnreadNoticeCount,
} from '@/api/OrganizeApi'
import feedBackComp from '@/components/common/feedback-comp.vue'
import popupComp from '@/components/common/popup-comp.vue'
import feedBackModal from '@/components/common/feedback-modal.vue'
import MarkdownPreview from '@/components/common/markdown-preview.vue'
import {processMarkdownWithToken, isAuthRequiredUrl, addTokenToUrl} from '@/utils/imageTokenUtils'

import Header from '@/layout/header.vue'
import Footer from '@/layout/footer.vue'
import {getBookData} from '@/api/handbookApi'
import {Notification, NotificationConfig} from '@/define/Notification'
import {useSignalr, useSignalrStop} from '@/hooks/useSignalr'
import {useGlobal} from '@/stores/useGlobal'
import {RefreshTaskFillHistory} from '@/api/CommonApi'
import {MenuIcon} from '@/define/icon.define'
import {useKeepAlive} from '@/stores/useKeepAlive'

const keepAliveStore = useKeepAlive()
const includeKeepAlive = computed(() => keepAliveStore.include)

const frame = computed(() => {
	if (route.query.isframe) {
		useGlobal().setFarmes(['default'])
	}
	return useGlobal().getFarmes
})

const complated = ref(false)
const app = inject('#app') as App<Element>
const axios = inject('#axios') as Request
const route = useRoute()
const router = useRouter()
let signalrNotifications: any = null
let signalrWorlflow: any = null
let signalrReport: any = null
const wolkflowCount = ref(0)
const reportCount = ref(0)
const store = useViewStore()
const userStore = useUserStore()
// const frame = computed(() => useViewStore().getFrame)
const currentUser = ref(JSON.parse(localStorage.getItem('currentUserInfo') as string))
const currentUserInfoData: any = ref(null)
const currentPlatformInfo = ref(JSON.parse(localStorage.getItem('currentPlatformInfo') as string))
const isYKZFrame = ref(true)
const isYTHFrame = computed(() => store.frame)
const currentDepartmentList = ref([])
const currentDepartmentInfo = ref()
const waterMarkerText = ref(
	`${userStore.getPlatformInfo?.name || ''}${
		userStore.getPlatformInfo?.phone && userStore.getPlatformInfo?.phone !== null
			? userStore.getPlatformInfo?.phone.substring(7, 11)
			: ''
	}\n${dayjs(new Date()).format('YYYY-MM-DD')}`
)
// 样式控制
let isCollapse = ref(false)
const inopenModel = ref(false)
const asideBadge: any = ref({})
// 答疑手册
const markDown = ref()
// 操作手册
const operationText = ref()

const versionLogText = ref()
// 顶部菜单下面的条条
const setNavBarStyle = () => {
	nextTick(() => {
		const link: any = document.querySelector('.top-nav a.active')
		if (link) {
			navPosition.value.width = link.offsetWidth
			navPosition.value.left = link.offsetLeft
		}
	})
}

const getIframeCode = async (item: any) => {
	store.isIframe = true
	const res = await axios.request({
		method: 'get',
		url: `/api/platform/authorization/authorization-code?MenuName=${item.name}&ClientId=${item.meta.clientId}`,
		headers: {
			Urlkey: 'iframeCode',
		},
	})
	return res
}
// 左侧导航/标签点击切换路由
const changeRouter = async (item: any) => {
	console.log('changeRouter', item)

	// outlink页面直接获取iframe地址
	if (item && item?.meta?.frameSrc) {
		if (item?.meta?.clientId === 'workbench') {
			if (isYKZFrame.value) {
				dd.openLink({
					url: `${item.meta.frameSrc}?AccessToken=${localStorage.getItem(
						'access_token'
					)}`,
				})
					.then((res) => {
						console.log(res)
					})
					.catch((err) => {})
			} else {
				const newUrl = `${item.meta.frameSrc}?AccessToken=${localStorage.getItem(
					'access_token'
				)}`
				window.open(newUrl, '_blank')
			}
		} else {
			const res: any = await getIframeCode(item)
			const frameSrc = item.meta.frameSrc.includes('?')
				? `${item.meta.frameSrc}&code=${res.data}`
				: `${item.meta.frameSrc}?code=${res.data}`
			store.saveOutlink(frameSrc)
		}
	}
	// labelRef.value.add(item)
}
/** 菜单 */
const currentNavId = ref('')
const navigation = ref([])
const navPosition = ref({
	left: 0,
	width: 0,
})

const getMenuItems = async () => {
	try {
		const res = await axios.request({
			method: 'get',
			url: `/api/platform/user/get-user-menus`,
			headers: {
				Urlkey: 'iframeCode',
			},
		})
		let items: any

		if (res.status !== 200) {
			return
		}

		const currentUserGrade = JSON.parse(localStorage.getItem('currentDepartmentInfo') as string)
			.region.grade
		const currentUserInfoRole = JSON.parse(localStorage.getItem('currentUserInfo') as string)
		// 判断街镇用户是否有跑道关联菜单权限
		// if (currentUserGrade !== 4) {
		// 	// 判断是否为区县运维员
		// 	if (currentUserInfoRole.includes(USER_ROLES_ENUM.LEDGER_PERATOR)) {
		// 		items = res.data.filter((v: any) => v.path !== '/runway')
		// 	} else {
		// 		items = res.data.filter(
		// 			(v: any) => v.path !== '/runway' && v.path !== '/runwayview'
		// 		)
		// 	}
		// } else {
		// 	items = res.data
		// }
		// 镇街
		if (currentUserGrade === 4) {
			items = res.data
		}
		// else if (
		// 	currentUserInfoRole.baseRoles.includes('管理员') ||
		// 	currentUserInfoRole.staffRole.includes('区县台账运维员')
		// ) {
		// 	items = res.data
		// }
		else if (currentUserGrade === 3 || currentUserGrade === 2) {
			items = res.data.filter((v: any) => v.path !== '/runway')
		} else {
			items = res.data
		}

		if (items.length === 0) {
			return
		}

		// 处理当前用户拥有的模块
		const modules = Array.from(new Set(items.map((m: any) => m.path.split('/')[1])))
		store.savePermissions(modules)

		// if (!usePagePermissions(route.fullPath.split('/')[1]) && route.fullPath !== '/login') {
		// 	console.log('没有页面访问权限')
		// 	location.href = '/403'
		// 	return
		// }

		const toTree = (data: any) => {
			data.forEach((item: any) => {
				item.label = item.displayName
				item.iconSvg = MenuIcon[item.displayName]
				item.children = items
					.filter((f: any) => f.parentId === item.id)
					.sort((a: any, b: any) => a.meta.sort - b.meta.sort)

				if (item.children.length > 0) {
					toTree(item.children)
				}
			})

			return data
		}

		const temp = toTree(items).filter((f: any) => f.component === '/workReport')[0]?.children

		temp.forEach((tem: any) => {
			if (tem.component === '/runwayManagement' && tem.children.length === 0) {
				temp.splice(temp.indexOf(tem), 1)
			}
		})

		navigation.value = temp

		// 配置的默认首页索引
		let index = items.findIndex((f: any) => f.displayName === '业务表创建') //f.startup
		labelsRef.value.first(items[index])
	} catch (err: any) {
		console.log(err)
		if (err.response?.status === 500) {
			ElMessage.error('当前系统用户在线量较多，请5分钟后再试')
		}
	}
}

// 退出登录
const back = () => {
	store.clear()
	userStore.clear()
	// localStorage.clear()
	removeStorage()

	// location.href = location.origin
	router.replace({path: '/401'})
}

const getCurrentUser = () => {
	return new Promise((resolve, reject) => {
		axios
			?.get('/api/filling/staff/current-staff')
			.then((res: any) => {
				if (res.status === 200) {
					// res.data.staffRole = res.data.staffRole.map((v: any) => v.name)
					localStorage.setItem('currentUserInfo', JSON.stringify(res.data))
					// userStore.userInfo = res.data
					userStore.setCurrentUserInfo(res.data)
				}
				resolve('')
			})
			.catch(() => {
				resolve('')
			})
	})
}

const getCurrentArea = (currentDepartment: any) => {
	return new Promise((resolve) => {
		// 开始组装userInfo-----市-区-街镇-村社
		let currentUserInfo: any = userStore.getUserInfo

		currentUserInfo.city = currentDepartment.city
		currentUserInfo.district = currentDepartment.district
		currentUserInfo.street = currentDepartment.street
		currentUserInfo.community = currentDepartment.community

		const currentDepartmentInfo = JSON.parse(
			localStorage.getItem('currentDepartmentInfo') as string
		)

		localStorage.setItem('currentUserInfo', JSON.stringify(currentUserInfo))
		userStore.setCurrentUserInfo(currentUserInfo)

		if (currentDepartmentInfo) {
			userStore.setCurrentDepartmentId(currentDepartmentInfo.departmentExtendId)
		}
		console.log(888, currentUserInfo, currentDepartmentInfo)
		resolve('')
		// userStore.setCurrentDepartmentId(rr.data.id)
		// getBigDepartmentId({
		// 	departmentId: currentDepartmentInfo.value.id,
		// }).then((rr: any) => {
		// 	userStore.setCurrentDepartmentId(rr.data.id)
		// 	resolve('')
		// })
	})
}

const refreshTimer = ref()
// 登陆进来过后，刷新token携带departmentId
const refreshToken = async () => {
	return new Promise((resolve) => {
		console.log(999, userStore.getUserInfo)

		axios
			?.login('/api/sso/auth/token', {
				clientId: 'vue-admin-element',
				clientSecret: '1q2w3e*',
				grantType: 'refresh_token',
				refresh_token: localStorage.getItem('refresh_token'),
				Scope: 'inspur-abp-application inspur-ledger offline_access',
				departmentId: currentDepartmentInfo.value.id,
				bigDepartmentId: currentDepartmentInfo.value.departmentExtendId,
				businessRole: userStore.getUserInfo.roles
					.filter((f: any) => f.departmentId === currentDepartmentInfo.value.id)
					.map((f: any) => f.name)
					.join(','),
			})
			.then((res: any) => {
				if (res.status === 200) {
					userStore.setToken(res.data.accessToken, res.data.refreshToken)
				}
			})
			.catch((err: any) => {
				if (err.response?.status === 500) {
					ElMessage.error('当前系统用户在线量较多，请5分钟后再试')
				}
			})
			.finally(() => {
				resolve('')
			})
	})
}

// 清除消息通知
const cleanNotice = () => {
	const time = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')
	console.log(time)
	clearNotifilers(time).then((res) => {
		noticeDataList.value = []
		// getUnreadTotal()
		// console.log(NoticeUnreadTotal.value)

		NoticeUnreadTotal.value = []
		NoticeTabList.value.forEach((v: any) => {
			v.unreadCount = 0
		})
		ElMessage.success('已清除所有消息')
	})
}

// 消息点击时跳转处理
const handelNoticeIsRead = async (value: any, index: number) => {
	console.log('Click Notice:', value, index)
	const {name, extraProperties} = value
	const id = value.id + ''
	const readState = 0
	if (
		!extraProperties.description ||
		extraProperties.description === '' ||
		extraProperties.description === null ||
		extraProperties.description === 'null'
	) {
		return
	}
	if (extraProperties.BusinessType == 'ReportRevoke') {
		return
	}

	const json = JSON.parse(extraProperties.description)
	signalrNotifications
		?.invoke('change-state', id, readState)
		.then(async () => {
			await getUnreadTotal()

			const {mainLabel, path, query} = (
				NotificationConfig[name][json.BusinessType] || NotificationConfig[name]
			)(json)
			localStorage.setItem('MAIN_LABEL', mainLabel)
			noticeDataList.value.forEach((f: any) => {
				if (f.id === id) {
					f.state = 0
				}
			})
			console.log(path, query)
			router.push({path, query})
		})
		.catch((error: any) => {
			console.error('Error sending message: ', error)
		})
}

const getCurrentUserInfo = () => {
	return new Promise((resolve, reject) => {
		// 检查URL参数中是否包含free=true，如果是则跳过API调用
		const urlParams = new URLSearchParams(window.location.search)
		const isFreeMode = urlParams.get('free') === 'true'

		if (isFreeMode) {
			// 测试模式：跳过API调用，设置模拟数据
			const mockUserData = {
				userName: 'test-user',
				account: 'test-user',
				staffRole: ['测试角色'],
				roles: [{ name: '测试角色' }],
				departments: [
					{
						id: 'test-dept-id',
						name: '测试部门',
						departmentExtendId: 'test-dept-extend-id',
						region: { grade: 4 }
					}
				],
				isActive: true
			}

			userStore.casualUser = mockUserData
			userStore.setCurrentUserInfo(mockUserData)
			userStore.setCurrentPlatformInfo(mockUserData)

			currentUserInfoData.value = mockUserData
			currentDepartmentList.value = mockUserData.departments

			console.log('测试模式：跳过用户信息API调用')
			resolve('')
			return
		}

		axios
			?.request({
				method: 'get',
				url: '/api/platform/user/lite-current-user-info',
			})
			.then(async (res: any) => {
				if (res.status === 200) {
					res.data.staffRole = res.data.roles.map((v: any) => v.name)
					res.data.account = res.data.userName

					userStore.casualUser = res.data
					userStore.setCurrentUserInfo(res.data)
					userStore.setCurrentPlatformInfo(res.data)

					currentUserInfoData.value = res.data

					if (res.data.departments.length !== 0) {
						currentDepartmentList.value = res.data.departments
					}
					if (res.data.roles.length === 0 || res.data.isActive === false) {
						router.replace('/noPermission')
					}
				}
			})
			.catch(async (err: any) => {
				if (err.response?.status === 500) {
					ElMessage.error('当前系统用户在线量较多，请5分钟后再试')
				}
			})
			.finally(() => {
				resolve('')
			})
	})
}

const transFormAuthCodeToToken = async () => {
	console.log('transFormAuthCodeToToken', router)
	// 清除localStorage
	// localStorage.clear()
	// store.$patch({isYKZFrame: false})

	isYKZFrame.value = false

	const access_token: any = route.query.access_token
	if (access_token) {
		const refresh_token = route.query.refresh_token as string
		userStore.setToken(access_token, refresh_token)
	}

	const accountId = route.query.accountid
	if (accountId) {
		store.$patch({frame: false})
		return new Promise((resolve, reject) => {
			axios
				?.login('/api/sso/auth/token', {
					clientId: 'vue-admin-element',
					clientSecret: '1q2w3e*',
					grantType: 'jczz_account_id',
					accountId: accountId,
					url: route.query.url ?? null,
					orgCode: route.query.orgCode ?? null,
					roleName: route.query.roleName ?? null,
					userType: route.query.userType ?? null,
					jczzToken: route.query.jczztoken ?? null,
				})
				.then(async (res: any) => {
					if (res.status === 200) {
						userStore.setToken(res.data.accessToken, res.data.refreshToken)
					}
					resolve('')
				})
				.catch((err: any) => {
					if (err.response?.status === 500) {
						ElMessage.error('当前系统用户在线量较多，请5分钟后再试')
					}
					resolve('')
				})
		})
	}

	const authUserId = route.query.authUserId
	if (authUserId) {
		// localStorage.clear()
		removeStorage()
		return new Promise((resolve, reject) => {
			axios
				?.login(
					'/connect/token',
					{
						client_id: 'vue-admin-element',
						client_secret: '1q2w3e*',
						grant_type: 'user',
						authUserId,
						operatorToken: route.query.currentToken,
					},
					{
						headers: {
							'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
						},
					}
				)
				.then(async (res: any) => {
					if (res.status === 200) {
						userStore.setToken(res.data.access_token, res.data.refresh_token)
					}
					resolve('')
				})
				.catch(() => {
					resolve('')
				})
		})
	}

	let AuthCode = route.query.auth_code
	if (AuthCode) {
		return new Promise((resolve, reject) => {
			const getToken = (isQuery: any) => {
				labelsRef.value?.clear()
				// localStorage.clear()
				removeStorage()

				axios
					?.login('/api/sso/auth/token', {
						clientId: 'vue-admin-element',
						clientSecret: '1q2w3e*',
						grantType: 'ykz_auth_code',
						authCode: AuthCode,
						source: isQuery ? 'query' : 'dd',
					})
					.then(async (res: any) => {
						if (res.status === 200) {
							userStore.setToken(res.data.accessToken, res.data.refreshToken)
							window.history.pushState({}, '', location.href.split('?')[0])
						}
						resolve('')
					})
					.catch((err: any) => {
						if (err.response?.status === 500) {
							ElMessage.error('当前系统用户在线量较多，请5分钟后再试')
						}
						resolve('')
					})
			}

			dd.ready(() => {
				store.$patch({isYKZFrame: true})
				dd.getAuthCode({corpId: ''})
					.then((res1: any) => {
						if (res1) {
							AuthCode = res1.auth_code
							isYKZFrame.value = true

							if (!AuthCode) {
								resolve('')
							} else {
								getToken(0)
							}
						}
					})
					.catch((error) => {
						getToken(1)
					})
			})
		})
	}

	return Promise.resolve('')
}

const showAI = ref(false)
const showAuth = ref(true)
const showDepartment = ref(false)

const onConfirmDepartment = async (val: any, item: any) => {
	const department = localStorage.getItem('currentDepartmentInfo')
	if (!department) {
		currentDepartmentInfo.value = item
		userStore.setCurrentDepartment(item)
	} else {
		currentDepartmentInfo.value = JSON.parse(department)
	}

	// 检查是否为测试环境（通过URL参数判断）
	const isTestEnvironment = window.location.search.includes('test=true') ||
	                          window.location.search.includes('free=true')

	if (!isTestEnvironment) {
		await ApplicationConfiguration() // 请求SR接口太慢233
		await getCurrentArea(currentDepartmentInfo.value)
		if (localStorage.getItem('screenJob') === null) await refreshToken()
		await getCurrentUserInfo()
		// await userStore.getTodoListCount()
		await getMenuItems()
		// await userStore.getAuditListCount()
		// await userStore.getWorkTodoCount()
	} else {
		console.log('测试环境：跳过异步初始化操作')
		// 在测试环境中设置基本的导航数据
		navigation.value = [
			{
				id: 'taskObjectiveDecomposition',
				displayName: '任务目标拆解',
				path: '/taskObjectiveDecomposition',
				component: '/taskObjectiveDecomposition',
				meta: { title: '任务目标拆解' },
				children: []
			},
			{
				id: 'dataSourceConfig',
				displayName: '数据源配置管理',
				path: '/dataSourceConfig',
				component: '/dataSourceConfig',
				meta: { title: '数据源配置管理' },
				children: []
			}
		]
	}

	RefreshTaskFillHistory()
	useSignalrStop()

	userStore.$patch((state) => {
		asideBadge.value = {
			我的待办: state.badgeObj.totalPendingCount,
			数据审核: state.auditObj.totalCount,
			// 工作待办: state.badgeObj.reportLedgerNoticeNotProcessedCount,
			工作待办: 0,
			任务待办: 0,
		}
	})

	console.log('待办消息:', asideBadge.value)

	complated.value = true
	userStore.setLogout(false)

	signalrNotifications = useSignalr('Notifications', '/signalr-hubs/notifications')
	signalrWorlflow = useSignalr('Workflow', '/signalr-hubs/workflow')
	signalrReport = useSignalr('Filling', '/signalr-hubs/platform')

	nextTick(() => {
		if (route.query?.authUserId) {
			history.replaceState({}, '', location.href.split('?')[0])
		}

		waterMarkerText.value = `${currentUserInfoData.value?.name || ''}${
			currentUserInfoData.value?.phone && currentUserInfoData.value?.phone !== null
				? currentUserInfoData.value?.phone.substring(7, 11)
				: ''
		}\n${dayjs(new Date()).format('YYYY-MM-DD')}`

		setTimeout(() => getMessages(), 1000)
	})
}

const completeAuthNext = () => {
	return new Promise(async (resolve, reject) => {
		// 大屏跳转
		if (route.query.screen_job) {
			localStorage.setItem('screenJob', route.query.screen_job as string)
		}

		showAuth.value = false

		await transFormAuthCodeToToken()
		await getCurrentUserInfo()
		let isdepartmentforStreet = JSON.parse(localStorage.getItem('departmentforStreet') as any)
		// 处理顶部切换部门
		const ihc = localStorage.getItem('IS_HEADER_CHANGE')
		if (ihc === 'true' || currentDepartmentList.value.length === 1) {
			showDepartment.value = false
			localStorage.removeItem('IS_HEADER_CHANGE')
			if (currentDepartmentList.value.length === 1) {
				onConfirmDepartment(null, currentDepartmentList.value[0])
			} else {
				onConfirmDepartment(
					null,
					JSON.parse(localStorage.getItem('currentDepartmentInfo') as string)
				)
			}
		} else {
			// 检查是否为测试模式（URL包含free=true参数）
			const urlParams = new URLSearchParams(window.location.search)
			const isFreeMode = urlParams.get('free') === 'true'

			if (isdepartmentforStreet) {
				if (userStore.userInfo.id === isdepartmentforStreet.userId) {
					showDepartment.value = false
					onConfirmDepartment(null, isdepartmentforStreet)
				} else {
					localStorage.removeItem('currentDepartmentInfo')
					showDepartment.value = !isFreeMode // 测试模式下不显示部门选择遮罩
				}
			} else {
				localStorage.removeItem('currentDepartmentInfo')
				// 测试模式下不显示部门选择遮罩，并设置默认部门信息
				if (isFreeMode) {
					showDepartment.value = false
					// 设置一个默认的部门信息用于测试
					const defaultDepartment = {
						id: 'test-dept-001',
						name: '测试部门',
						parentName: '测试科室',
						community: '永川区',
						street: '民政局',
						district: '安全科'
					}
					onConfirmDepartment(null, defaultDepartment)
				} else {
					showDepartment.value = true
				}
			}
		}
		resolve('')
	})
}
const departmentList = ref<any[]>([])
// 监听当前部门发生改变时的弹窗
const openChangeModel = ref(false)
const checkDepartment = ref(null)
const openDepartmentChangeModel = (id: string) => {
	if (currentPlatformInfo.value.departments.length !== 0) {
		departmentList.value = currentPlatformInfo.value.departments.filter((v) => v.id !== id)
	}
	openChangeModel.value = true
}
function closeCurrentPage() {
	openChangeModel.value = false
	if (isYKZFrame.value) {
		dd.closePage({})
	} else {
		// localStorage.clear()
		removeStorage()

		location.href = location.origin
	}
}
const removeStorage = () => {
	// 此处是为了保持当前人的登录状态。
	const keepFieldName = 'departmentforStreet'
	const keepFieldValue = localStorage.getItem(keepFieldName)
	localStorage.clear()
	// 重新设置之前保留的字段
	if (keepFieldValue !== null) {
		localStorage.setItem(keepFieldName, keepFieldValue)
	}
}
const submit = async () => {
	console.log(checkDepartment.value)
	if (checkDepartment.value !== null) {
		currentDepartmentInfo.value = checkDepartment.value
		localStorage.setItem('currentDepartmentInfo', JSON.stringify(checkDepartment.value))
		await getCurrentArea(currentDepartmentInfo.value)
		userStore.setCurrentDepartment(checkDepartment.value)
		window.localStorage.removeItem('nextChildren')
		await refreshToken()
		// 切换部门-刷新页面
		window.location.reload()
		openChangeModel.value = false
	} else {
		ElMessage.warning('请选择部门')
	}
}
const onAuthClick = (str: string) => {
	Cookies.set('auth', str === 'confirm' ? 'true' : 'false')
	const cur: boolean = Cookies.get('auth') === 'true' ? true : false

	if (cur) {
		completeAuthNext()
	} else {
		back()
	}
}
const changeCurrentDepartment = (item: any) => {
	ElMessageBox({
		title: '切换部门',
		showCancelButton: true,
		message: `即将切换至"${item.name}"部门？`,
	})
		.then(async () => {
			currentDepartmentInfo.value = item
			localStorage.setItem('currentDepartmentInfo', JSON.stringify(item))
			localStorage.setItem(
				'departmentforStreet',
				JSON.stringify({...item, userId: userStore.userInfo.id})
			)

			await getCurrentArea(currentDepartmentInfo.value)
			localStorage.setItem('isRefresh', 'true')
			localStorage.setItem('IS_HEADER_CHANGE', 'true')
			console.log('当前部门:', item)
			userStore.setCurrentDepartment(item)
			window.localStorage.removeItem('nextChildren')
			await refreshToken()
			// 切换部门-刷新页面
			window.location.reload()
		})
		.catch((action) => {
			// console.log(action)
		})
}

// 获取后台api
const ApplicationConfiguration = async () => {
	getApplicationConfiguration()
		.then((res) => {
			userStore.setApplication(res.data)
			getNoticeType()
		})
		.catch((err) => {
			if (err.response?.status === 500) {
				ElMessage.error('当前系统用户在线量较多，请5分钟后再试')
			}
		})
}
// 获取消息提示
const noticeDataList = ref<any>([])
const getnoticeDataList = () => {
	console.log(noticeCurrentTab.value)

	getMyNotifilers({
		skipCount: 0,
		maxResultCount: 10,
		reverse: false,
		sorting: '',
		BigType: noticeCurrentTab.value,
	}).then((res) => {
		noticeDataList.value = res.data.items
		// handleNoticeDatas()
	})
}
const NoticeTabList = ref([])
const NoticeUnreadTotal = ref<any>([])
const noticeCurrentTab = ref<any>()
// 获取未读消息total
const getUnreadTotal = async () => {
	try {
		const res = await getUnreadNoticeCount()
		NoticeUnreadTotal.value = res.data
		NoticeTabList.value.forEach((v: any) => {
			v.unreadCount = NoticeUnreadTotal.value.filter((x) => x.bigType === v.bigType)[0].count
		})
	} catch (err: any) {
		if (err.response?.status === 500) {
			ElMessage.error('当前系统用户在线量较多，请5分钟后再试')
		}
	}
}
const getNoticeType = async () => {
	try {
		const res = await getNoticeBigTypes()
		getUnreadTotal()
		NoticeTabList.value = res.data
		bigType.value = noticeCurrentTab.value = res.data[0].bigType
	} catch (err: any) {
		if (err.response?.status === 500) {
			ElMessage.error('当前系统用户在线量较多，请5分钟后再试')
		}
	}
	// getnoticeDataList()
}
const _fixLoading = () => {
	const body = document.querySelector('body') as HTMLElement
	const style = document.createElement('style')
	style.innerHTML = '.loading-mask{pointer-events:none}'
	body.appendChild(style)
	return style
}

const onHeaderNoticeScroll = (val: number, isBottom: boolean) => {
	if (isBottom) {
		console.log(bigType.value)
		console.log(noticeType.value)
		let params: any = {
			skipCount: noticeDataList.value.length,
			maxResultCount: 10,
			reverse: false,
			sorting: '',
		}
		if (noticeType.value !== 0) {
			params.NoticeType = noticeType.value
		} else {
			params.bigType = bigType.value
		}
		const style = _fixLoading()
		getMyNotifilers(params).then((res) => {
			noticeDataList.value = noticeDataList.value.concat(res.data.items)
			setTimeout(() => style.remove(), 1000)
		})
	}
}
const bigType = ref(0)
const onClickNoticeChange = (val: number) => {
	console.log(val)
	bigType.value = val
	noticeType.value = 0
	const style = _fixLoading()
	getMyNotifilers({
		skipCount: 0,
		maxResultCount: 10,
		reverse: false,
		sorting: '',
		bigType: val,
	}).then((res) => {
		noticeDataList.value = res.data.items
		setTimeout(() => style.remove(), 1000)
	})
}
const noticeType = ref(0)
const onSelectNoticeTypeChange = (val: number) => {
	noticeType.value = val
	const style = _fixLoading()
	let params
	if (val === 0) {
		params = {
			skipCount: 0,
			maxResultCount: 10,
			reverse: false,
			sorting: '',
			bigType: bigType.value,
		}
	} else {
		params = {
			skipCount: 0,
			maxResultCount: 10,
			reverse: false,
			sorting: '',
			NoticeType: val,
		}
	}
	getMyNotifilers(params).then((res) => {
		noticeDataList.value = res.data.items
		setTimeout(() => style.remove(), 1000)
	})
}

const getWorkflowTaskCount = () => {
	const invoke = () => {
		signalrWorlflow?.invoke('getUnCompletedCount').then((res: any) => {
			console.log('useSignalr getWorkflowTaskCount:', res)
			wolkflowCount.value = res
			asideBadge.value['工作待办'] = wolkflowCount.value + reportCount.value
			asideBadge.value['任务待办'] = wolkflowCount.value + reportCount.value
			userStore.setAuditkCount(res)
		})
	}
	signalrWorlflow?.on('refreshGetUnCompletedCount', (message: any) => {
		if (message) {
			invoke()
		}
	})
	invoke()
}

const getReportTaskCount = () => {
	const invoke = () => {
		signalrReport?.invoke('getUnCompletedReportFillingCount').then((res: any) => {
			console.log('useSignalr getUnCompletedReportFillingCount:', res)
			reportCount.value = res
			asideBadge.value['工作待办'] = wolkflowCount.value + reportCount.value
			asideBadge.value['任务待办'] = wolkflowCount.value + reportCount.value
			userStore.setFillCount(res)
		})
	}
	signalrReport?.on('refreshGetUnCompletedFillingCount', (message: any) => {
		if (message) {
			invoke()
		}
	})
	invoke()
}

const getMessages = () => {
	if (userStore.getToken && (signalrNotifications || signalrWorlflow || signalrReport)) {
		signalrNotifications?.on('get-notification', (message: any) => {
			console.log('Signalr notification from Index:', message)
			if (message.name === Notification.UserOffline) {
				ElMessage.error('您的账号已被禁用')
				userStore.setLogout(true)
				userStore.clear()
				// localStorage.clear()
				removeStorage()
				router.push('/401')
				return
			}

			noticeDataList.value.unshift({
				id: message.id,
				name: message.name,
				...message.data,
			})
			getUnreadTotal()
			console.log(noticeDataList.value)
		})
		getWorkflowTaskCount()
		getReportTaskCount()
	}
}

// 点击反馈
const inopenfeedBackModal = ref<any>()
const feedbackcomp = ref<any>()
const handleInopenModel = () => {
	feedbackcomp.value.opensubmit()
}
// 点击关闭
const handleClose = () => {
	feedbackcomp.value.onCancel()
}
// 子组件返回关闭弹窗
const updateInpoenModel = () => {
	inopenModel.value = false
	inopenfeedBackModal.value.getFeedbackList()
}

onBeforeMount(async () => {
	console.log('useSignalr Index beforeMount')
	const hasAuthCode = route.query['authorization-code']
	let isdepartmentforStreet = JSON.parse(localStorage.getItem('departmentforStreet') as any)
	if (isdepartmentforStreet) {
		currentDepartmentList.value = isdepartmentforStreet
	}
	if (hasAuthCode) {
		const auth = Cookies.get('auth') === 'true' ? true : false
		if (auth) {
			await completeAuthNext()
		} else {
			showAuth.value = true
		}
	} else {
		await completeAuthNext()
	}
})

onMounted(() => {
	console.log('useSignalr Index mounted')
	currentPlatformInfo.value = userStore.getPlatformInfo
})

onBeforeUnmount(() => {
	useSignalrStop()
})
onUnmounted(() => {
	complated.value = false
})

watch(
	() => userStore.userInfo,
	(val) => {
		currentUser.value = val
	}
)
watch(
	() => userStore.platformInfo,
	(val) => {
		currentPlatformInfo.value = val
	}
)
const showPopup = ref(false)
const showOperationBook = ref(false)
const openFeedbackModel = ref(false)
const showVersionLogModal = ref(false)
const openFeedbackCreateModel = ref(false)
const currentClickType = ref()
const handleClick = (type: number) => {
	currentClickType.value = type
	if (type === 0) {
		searchKey.value = null
		hasFindList.value = []
		showPopup.value = false
		showOperationBook.value = true
		openFeedbackModel.value = false
		showVersionLogModal.value = false
		setTimeout(() => {
			getMunus()
			getAllInnerHTML()
		}, 100)
	}
	if (type === 1) {
		hasFindList.value = []
		searchKey.value = null
		showPopup.value = true
		openFeedbackModel.value = false
		showOperationBook.value = false
		showVersionLogModal.value = false
		setTimeout(() => {
			getMunus()
			getAllInnerHTML()
		}, 100)
	}
	if (type === 2) {
		showPopup.value = false
		openFeedbackModel.value = true
		showOperationBook.value = false
	}
	if (type === 4) {
		searchKey.value = null
		hasFindList.value = []
		showPopup.value = false
		showOperationBook.value = false
		openFeedbackModel.value = false
		setTimeout(() => {
			getMunus()
			getAllInnerHTML()
		}, 100)
	}
}
const title = ref<any[]>()
const searchKey = ref()

const isOpeationFull = ref(true)
const isHandleBookFull = ref(true)
const previewRef = ref()
const previewRef1 = ref()
const previewRef2 = ref()
const changeFull = (val: boolean) => {
	isHandleBookFull.value = val
}
const changeOperationFull = (val: boolean) => {
	isOpeationFull.value = val
}
const isFeedbackModal = ref(false)
const changeFeedbackFull = (val: boolean) => {
	isFeedbackModal.value = val
}
function getMunus() {
	const anchors =
		currentClickType.value === 0
			? (previewRef1.value.$el.querySelectorAll('h1,h2,h3,h4,h5,h6') as any)
			: currentClickType.value === 1
			? (previewRef.value.$el.querySelectorAll('h1,h2,h3,h4,h5,h6') as any)
			: (previewRef2.value.$el.querySelectorAll('h1,h2,h3,h4,h5,h6') as any)
	console.log(previewRef2.value)

	const titles = Array.from(anchors).filter((item: any) => !!item.innerText.trim())
	if (!titles.length) {
		title.value = []
		return
	}
	const hTags = Array.from(new Set(titles.map((item: any) => item.tagName))).sort()

	title.value = titles.map((el: any) => ({
		title: el.innerText,
		lineIndex: el.getAttribute('data-v-md-line'),
		indent: hTags.indexOf(el.tagName),
	}))
}
function handleAnchorClick(anchor: any) {
	nextTick(() => {
		const {lineIndex} = anchor
		const heading =
			currentClickType.value === 0
				? (previewRef1.value.$el.querySelector(`[data-v-md-line="${lineIndex}"]`) as any)
				: currentClickType.value === 1
				? (previewRef.value.$el.querySelector(`[data-v-md-line="${lineIndex}"]`) as any)
				: (previewRef2.value.$el.querySelector(`[data-v-md-line="${lineIndex}"]`) as any)
		if (heading) {
			currentClickType.value === 0
				? previewRef1.value.scrollToTarget({
						target: heading,
						scrollContainer: document.querySelector('.md-preview'),
						top: 80,
				  })
				: currentClickType.value === 1
				? previewRef.value.scrollToTarget({
						target: heading,
						scrollContainer: document.querySelector('.md-preview'),
						top: 80,
				  })
				: previewRef2.value.scrollToTarget({
						target: heading,
						scrollContainer: document.querySelector('.md-preview'),
						top: 80,
				  })
			// previewRef.value.scrollToTarget({
			// 	target: heading,
			// 	scrollContainer: document.querySelector('.md-preview'),
			// 	top: 80,
			// })
		}
	})
}
const allText = ref<any[]>() // 所有待搜索内容
const hasFindList = ref<any[]>([]) // 已搜索到的列表
const currentNumber = ref(1)
function getAllInnerHTML() {
	const anchors =
		currentClickType.value === 0
			? (previewRef1.value.$el.querySelectorAll('h1,h2,h3,h4,h5,h6,p') as any)
			: currentClickType.value === 1
			? (previewRef.value.$el.querySelectorAll('h1,h2,h3,h4,h5,h6,p') as any)
			: (previewRef2.value.$el.querySelectorAll('h1,h2,h3,h4,h5,h6,p') as any)
	const container = Array.from(anchors).filter((item: any) => !!item.innerText.trim())
	allText.value = container
}
function handleSearch(value: string) {
	hasFindList.value = []
	currentNumber.value = 1

	// 筛选变色
	allText.value?.forEach((v) => {
		// 重置
		// v.innerHTML = v.innerHTML.replace(/<[^>]+>/g, '')
		// 移除所有带有红色背景的 <span> 标签
		v.innerHTML = v.innerHTML.replace(/<span style="background: red">(.*?)<\/span>/g, '$1')
		if (value !== '') {
			if (v.textContent?.includes(value)) {
				// 提取所有 <img> 标签
				const imgTags = v.querySelectorAll('img')
				const imgContents = Array.from(imgTags).map((img: any, index) => {
					const placeholder = `<!--IMG_TAG_${index}-->`
					img.outerHTML = placeholder
					return {placeholder, content: img.outerHTML}
				})

				// 替换文本中的 value
				const regex = new RegExp(`(${value})`, 'g')
				v.innerHTML = v.innerHTML.replace(regex, `<span style="background: red">$1</span>`)

				// 将 <img> 标签放回去
				imgContents.forEach(({placeholder, content}) => {
					v.innerHTML = v.innerHTML.replace(placeholder, content)
				})

				hasFindList.value.push(v)
			}
		}
	})
	if (hasFindList.value.length !== 0) {
		scrollToTarget(hasFindList.value[currentNumber.value - 1])
	}
}
const pre = () => {
	if (currentNumber.value > 1) {
		currentNumber.value--
	} else {
		currentNumber.value = hasFindList.value.length
	}
	scrollToTarget(hasFindList.value[currentNumber.value - 1])
}
const next = () => {
	if (currentNumber.value < hasFindList.value.length) {
		currentNumber.value++
	} else {
		currentNumber.value = 1
	}

	scrollToTarget(hasFindList.value[currentNumber.value - 1])
}
// 滚动到指定位置
const scrollToTarget = (anchor: any) => {
	const lineIndex = anchor.getAttribute('data-v-md-line')
	const heading =
		currentClickType.value === 0
			? previewRef1.value.$el.querySelector(`[data-v-md-line="${lineIndex}"]`)
			: currentClickType.value === 1
			? previewRef.value.$el.querySelector(`[data-v-md-line="${lineIndex}"]`)
			: previewRef2.value.$el.querySelector(`[data-v-md-line="${lineIndex}"]`)
	if (heading) {
		currentClickType.value === 0
			? previewRef1.value.scrollToTarget({
					target: heading,
					scrollContainer: document.querySelector('.md-preview'),
					top: 80,
			  })
			: currentClickType.value === 1
			? previewRef.value.scrollToTarget({
					target: heading,
					scrollContainer: document.querySelector('.md-preview'),
					top: 80,
			  })
			: previewRef2.value.scrollToTarget({
					target: heading,
					scrollContainer: document.querySelector('.md-preview'),
					top: 80,
			  })
	}
}

const labelsRef = ref()
const currentMenuItem = ref(null)

const onAsideBeforeRouter = (item: any) => {
	console.log('Index Aside Before Router:', item)

	if (item && item?.meta?.frameSrc) {
		if (item?.meta?.clientId === 'workbench') {
			if (isYKZFrame.value) {
				dd.openLink({
					url: `${item.meta.frameSrc}?AccessToken=${localStorage.getItem(
						'access_token'
					)}`,
				})
					.then((res) => {
						console.log(res)
					})
					.catch((err) => {})
			} else {
				const newUrl = `${item.meta.frameSrc}?AccessToken=${localStorage.getItem(
					'access_token'
				)}`
				window.open(newUrl, '_blank')
				return false
			}
		}
	}
	return true
}

const onClickAsideItem = (item: any) => {
	if (item && item?.meta?.frameSrc) {
		const getCode = async (item: any) => {
			await getIframeCode(item)
		}
		const res: any = getCode(item)
		const frameSrc = item.meta.frameSrc.includes('?')
			? `${item.meta.frameSrc}&code=${res.data}`
			: `${item.meta.frameSrc}?code=${res.data}`
		store.saveOutlink(frameSrc)
	}
	console.log('Index Aside Item Change:', item)

	currentMenuItem.value = item
}

const onClickLabel = (item: any) => {
	// if (item.meta.frameSrc) {
	// 	store.saveOutlink(item.meta.frameSrc)
	// }
	// currentMenuItem.value = item
}

const onClickAibotItem = async (val: number) => {
	if (val === 0) {
		const res = await getBookData(1)
		operationText.value = res.data

		handleClick(0)
		showOperationBook.value = true
	}

	if (val === 1) {
		const res = await getBookData(2)
		markDown.value = res.data
		handleClick(1)
		showPopup.value = true
	}

	if (val === 2) {
		openFeedbackModel.value = true
	}

	if (val === 3) {
		showAI.value = true
		// ElMessage.warning('暂未开放')
	}
	if (val === 4) {
		const res = await getBookData(3)
		versionLogText.value = res.data
		handleClick(4)
		showVersionLogModal.value = true
	}
}
const exportToDocx = (htmlContent: any, fileName = '导出文件.docx') => {
	const fullHtmlContent = `
	<!DOCTYPE html>
	<html>
	<head>
	<meta http-equiv="Content-Type" content="text/html" charset="UTF-8">
	</head>
	<body>
	${htmlContent}
	</body>
	</html>
	`
	const convertData = asBlob(fullHtmlContent).then((data: any) => {
		FileSaver.saveAs(data, fileName)
	})
	// FileSaver.saveAs(convertData, fileName)
}
const downLoadBook = (type: number) => {
	const text = type === 1 ? operationText.value : markDown.value
	// 处理Markdown文本，添加token参数
	const processedText = processMarkdownWithToken(text)
	// 初始化Markdown解析器
	const md = new MarkdownIt()
	// 将Markdown转换为HTML
	const htmlContent = md.render(processedText)
	exportToDocx(htmlContent, type === 1 ? '操作手册.docx' : '答疑手册.docx')
}

const onScroll = (val: any) => {
	useViewStore().setMScroll(val)
}
</script>
<template>
	<DepartmentSelection
		v-model="showDepartment"
		:departments="currentDepartmentList"
		@confirm="onConfirmDepartment"
	></DepartmentSelection>

	<LoadingComp v-if="useViewStore().getEnableLoading"></LoadingComp>
	<Watermark :text="waterMarkerText" :opacity="0.3"></Watermark>
	<AuthorizedComp v-if="showAuth" @auth="onAuthClick"></AuthorizedComp>
	<AI v-model="showAI" />

	<Container
		class="main-container"
		model="habf"
		:enableExpand="false"
		:enableFooter="true"
		:offsetTop="56"
		:expand="!isCollapse"
		:frame="frame"
		:class="{full: !isYTHFrame}"
		@scroll="onScroll"
	>
		<template #header>
			<Header
				:isYKZFrame="isYKZFrame"
				:current-department-info="currentDepartmentInfo"
				:current-platform-info="currentPlatformInfo"
				:notice-data-list="noticeDataList"
				:notice-type-list="NoticeTabList"
				:notice-unread-count="NoticeUnreadTotal"
				@collapse="(val:any) => (isCollapse = val)"
				@click-notice-read="handelNoticeIsRead"
				@click-clear-notice="cleanNotice"
				@click-aibot-item="onClickAibotItem"
				@change-department-item="changeCurrentDepartment"
				@notice-scroll="onHeaderNoticeScroll"
				@clickNoticeTabChange="onClickNoticeChange"
				@selectNoticeTypeChange="onSelectNoticeTypeChange"
				@get-notice-type-list="getnoticeDataList"
			></Header>
		</template>
		<template #aside>
			<div class="df dfc navigation">
				<div class="top navigation-top">
					<Navigation
						v-if="navigation.length"
						v-model="currentNavId"
						:collapse="isCollapse"
						:items="navigation"
						:keys="['id', 'displayName']"
						:badges="asideBadge"
						:before-router="onAsideBeforeRouter"
						:default-active="currentNavId"
						containerClass="navigation-top"
						@click-item="onClickAsideItem"
					/>
					<LoadingTransition v-else color="#fff" style="height: 100%" />
				</div>
				<div class="collapse-layout" @click="isCollapse = !isCollapse">
					<i class="icon collapse" :class="{active: isCollapse}">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							xmlns:xlink="http://www.w3.org/1999/xlink"
							t="1714359315213"
							class="icon"
							viewBox="0 0 1024 1024"
							version="1.1"
							p-id="4258"
							width="24"
							height="24"
						>
							<path
								d="M0 0m47.919115 0l1054.220532 0q47.919115 0 47.919115 47.919115l0 0q0 47.919115-47.919115 47.919115l-1054.220532 0q-47.919115 0-47.919115-47.919115l0 0q0-47.919115 47.919115-47.919115Z"
								fill="#fff"
								p-id="4259"
							/>
							<path
								d="M0 862.544072m47.919115 0l1054.220532 0q47.919115 0 47.919115 47.919115l0 0q0 47.919115-47.919115 47.919115l-1054.220532 0q-47.919115 0-47.919115-47.919115l0 0q0-47.919115 47.919115-47.919115Z"
								fill="#fff"
								p-id="4260"
							/>
							<path
								d="M383.352921 575.029381m47.919115 0l670.867611 0q47.919115 0 47.919115 47.919115l0 0q0 47.919115-47.919115 47.919115l-670.867611 0q-47.919115 0-47.919115-47.919115l0 0q0-47.919115 47.919115-47.919115Z"
								fill="#fff"
								p-id="4261"
							/>
							<path
								d="M383.352921 287.514691m47.919115 0l670.867611 0q47.919115 0 47.919115 47.919115l0 0q0 47.919115-47.919115 47.919115l-670.867611 0q-47.919115 0-47.919115-47.919115l0 0q0-47.919115 47.919115-47.919115Z"
								fill="#fff"
								p-id="4262"
							/>
							<path
								d="M6.9825 502.46615L262.870574 683.737317a16.566323 16.566323 0 0 0 26.150146-13.691176V307.640719a16.566323 16.566323 0 0 0-26.150146-13.691176L6.9825 475.357622a16.703234 16.703234 0 0 0 0 27.108528z"
								fill="#fff"
								p-id="4263"
							/>
						</svg>
					</i>
				</div>
			</div>
		</template>

		<template #top>
			<Labels
				ref="labelsRef"
				v-model="currentNavId"
				:max="10"
				:height="30"
				:navigator="navigation"
				@click-item="onClickLabel"
				class="labels"
			></Labels>
		</template>

		<router-view v-slot="{Component}">
			<Transition name="fade" mode="out-in" appear>
				<keep-alive :include="includeKeepAlive">
					<component :is="Component" :key="$route.fullPath" v-if="complated" />
					<LoadingTransition v-else style="margin-top: -15px; height: 100%" />
				</keep-alive>
			</Transition>
		</router-view>

		<template #footer>
			<Footer></Footer>
		</template>
	</Container>
	<Dialog
		v-model="openChangeModel"
		:key="openChangeModel"
		title="信息提示"
		width="600"
		:showClose="false"
		:visibleConfirmButton="departmentList.length !== 0"
		@close="openChangeModel = false"
		:cancelText="'直接退出'"
		:confirmText="'确定'"
		@clickConfirm="submit"
		@clickCancel="closeCurrentPage"
	>
		<div w-full h-full p="40px">
			<div>
				系统检测到您当前的渝快政部门已解绑，<span v-if="departmentList.length !== 0"
					>您可选择其他部门继续使用一表通。</span
				>
			</div>
			<div mt="20px" flex items-center v-if="departmentList.length !== 0">
				<div w="80px">选择部门：</div>
				<div flex="1">
					<el-select w-full v-model="checkDepartment">
						<el-option
							v-for="item in departmentList"
							:label="item.name"
							:value="item"
						/>
					</el-select>
				</div>
			</div>
			<div text="gray" mt="20px">
				为了不影响您的使用，请和渝快政负责人确认部门变更。
				<i v-if="departmentList.length !== 0"
					>部门变更后如有权限问题请联系部门数据管理员。</i
				>
			</div>
		</div>
	</Dialog>

	<Dialog
		v-model="inopenModel"
		title="问题反馈"
		width="600"
		@close="inopenModel = false"
		@click-close="inopenModel = false"
	>
		<feedBackComp
			:inopenModel="inopenModel"
			ref="feedbackcomp"
			@update="updateInpoenModel"
		></feedBackComp>
		<template #footer>
			<el-button mr-5px mt="-5px" type="smiall" @click="handleClose"
				><i class="icon i-ic-outline-cancel"></i>关闭</el-button
			>
			<el-button
				mr-5px
				mt="-5px"
				type="primary"
				@click="handleInopenModel"
				style="color: #fff !important"
				><i class="icon i-ic-round-task-alt"></i>反馈</el-button
			>
		</template>
	</Dialog>

	<popupComp
		title="答疑手册"
		v-if="showPopup"
		@close="showPopup = false"
		@change-full="changeFull"
		@clickDownLoad="downLoadBook(2)"
	>
		<div
			v-if="!isHandleBookFull"
			style="scale: 0.9; width: 100%; height: 100%; overflow-y: auto"
		>
			<v-md-preview ref="previewRef" id="preview" :text="markDown"></v-md-preview>
		</div>
		<div v-else style="display: flex; width: 100%; height: 100%" class="handbook-container">
			<div
				class="menus"
				style="width: 400px; padding-left: 10px; padding-top: 10px; overflow-y: auto"
			>
				<div style="display: flex; width: 100%">
					<el-input
						style="width: 100%; height: 28px"
						h="28px"
						placeholder="请输入关键字查询"
						v-model="searchKey"
						@input="handleSearch"
					>
						<template #append>{{
							hasFindList.length === 0
								? '0/0'
								: `${currentNumber}/${hasFindList.length}`
						}}</template>
					</el-input>
					<el-button style="margin-left: 5px" type="primary" @click="pre">
						<el-icon><ArrowUp /></el-icon>
					</el-button>
					<el-button type="primary" @click="next">
						<el-icon><ArrowDown /></el-icon>
					</el-button>
				</div>

				<div
					v-for="anchor in title"
					:style="{padding: `10px 0 10px ${anchor.indent * 20}px`}"
					@click="handleAnchorClick(anchor)"
				>
					<a style="cursor: pointer">{{ anchor.title }}</a>
				</div>
			</div>
			<div class="md-preview" style="flex: 1; overflow-y: auto">
				<MarkdownPreview ref="previewRef" id="preview" :text="markDown"></MarkdownPreview>
			</div>
		</div>
	</popupComp>
	<popupComp
		title="操作手册"
		v-if="showOperationBook"
		@close="showOperationBook = false"
		@change-full="changeOperationFull"
		@clickDownLoad="downLoadBook(1)"
	>
		<div style="scale: 0.9; width: 100%; height: 100%; overflow-y: auto" v-if="!isOpeationFull">
			<MarkdownPreview
				ref="previewRef1"
				id="preview1"
				:text="operationText"
			></MarkdownPreview>
		</div>
		<div v-else class="handbook-container" style="display: flex; width: 100%; height: 100%">
			<div
				class="menus"
				style="padding-top: 10px; padding-left: 10px; overflow-y: auto; width: 400px"
			>
				<div style="width: 100%; display: flex; margin-top: 20px">
					<el-input
						style="width: 100%; height: 28px"
						placeholder="请输入关键字查询"
						v-model="searchKey"
						@input="handleSearch"
					>
						<template #append>{{
							hasFindList.length === 0
								? '0/0'
								: `${currentNumber}/${hasFindList.length}`
						}}</template>
					</el-input>
					<el-button style="margin-left: 5px" type="primary" @click="pre">
						<el-icon><ArrowUp /></el-icon>
					</el-button>
					<el-button type="primary" @click="next">
						<el-icon><ArrowDown /></el-icon>
					</el-button>
				</div>

				<div
					v-for="anchor in title"
					:style="{padding: `10px 0 10px ${anchor.indent * 20}px`}"
					@click="handleAnchorClick(anchor)"
				>
					<a style="cursor: pointer">{{ anchor.title }}</a>
				</div>
			</div>
			<div class="md-preview" style="flex: 1; overflow-y: auto">
				<MarkdownPreview
					ref="previewRef1"
					id="preview1"
					:text="operationText"
				></MarkdownPreview>
			</div>
		</div>
	</popupComp>
	<popupComp
		title="版本日志"
		v-if="showVersionLogModal"
		@close="showVersionLogModal = false"
		@change-full="changeOperationFull"
		@clickDownLoad="downLoadBook(1)"
	>
		<div style="scale: 0.9; width: 100%; height: 100%; overflow-y: auto" v-if="!isOpeationFull">
			<MarkdownPreview
				ref="previewRef2"
				id="preview2"
				:text="versionLogText"
			></MarkdownPreview>
		</div>
		<div v-else class="handbook-container" style="display: flex; width: 100%; height: 100%">
			<div
				class="menus"
				style="padding-top: 10px; padding-left: 10px; overflow-y: auto; width: 400px"
			>
				<div style="width: 100%; display: flex; margin-top: 20px">
					<el-input
						style="width: 100%; height: 28px"
						placeholder="请输入关键字查询"
						v-model="searchKey"
						@input="handleSearch"
					>
						<template #append>{{
							hasFindList.length === 0
								? '0/0'
								: `${currentNumber}/${hasFindList.length}`
						}}</template>
					</el-input>
					<el-button style="margin-left: 5px" type="primary" @click="pre">
						<el-icon><ArrowUp /></el-icon>
					</el-button>
					<el-button type="primary" @click="next">
						<el-icon><ArrowDown /></el-icon>
					</el-button>
				</div>

				<div
					v-for="anchor in title"
					:style="{padding: `10px 0 10px ${anchor.indent * 20}px`}"
					@click="handleAnchorClick(anchor)"
				>
					<a style="cursor: pointer">{{ anchor.title }}</a>
				</div>
			</div>
			<div class="md-preview" style="flex: 1; overflow-y: auto">
				<MarkdownPreview
					ref="previewRef2"
					id="preview2"
					:text="versionLogText"
				></MarkdownPreview>
			</div>
		</div>
	</popupComp>
	<popupComp
		title="问题反馈"
		v-if="openFeedbackModel"
		:full="false"
		@close="openFeedbackModel = false"
		@change-full="changeFeedbackFull"
	>
		<template #handlebook>
			<el-button
				type="primary"
				size="small"
				@click="inopenModel = true"
				style="color: #fff !important; margin-right: 5px; margin-top: -5px"
				>我要反馈</el-button
			>
		</template>
		<feedBackModal :isFull="isFeedbackModal" ref="inopenfeedBackModal"></feedBackModal>
	</popupComp>
</template>
<style scoped lang="scss">
.app {
	background-color: var(--z-bg);
	flex-direction: column;
	height: 100vh;
}

.main-container {
	width: 100%;
}

.body-container {
	overflow: hidden;
	width: 100%;
}

.main-scrollbar {
	height: calc(100% - 25px);
	width: calc(100%);
}

.labels {
	width: 100%;
}

.main {
	padding-top: 20px;
	padding-bottom: 0;
	position: relative;
	width: calc(100%);
	z-index: 1;
}

.navigation {
	height: 100%;
	.top {
		height: calc(100% - 40px);
		overflow-x: hidden;
		overflow-y: auto;
	}
	.collapse-layout {
		align-items: center;
		bottom: 0;
		border-top: 1px solid rgba(var(--z-line-rgb), 0.6);
		background: var(--z-main);
		cursor: pointer;
		display: flex;
		height: 40px;
		justify-content: center;

		width: 100%;
		.collapse {
			cursor: pointer;
			transform: rotate(0deg) scale(0.6);
			position: relative;
			top: 1px;

			&.active {
				transform: rotate(180deg) scale(0.6);
			}
		}
	}
}
</style>
