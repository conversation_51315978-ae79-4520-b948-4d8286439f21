<!-- 图谱Store测试页面 -->
<script setup lang="ts" name="TestGraphStore">
import { ref, onMounted, computed } from 'vue'
import { ElButton, ElMessage } from 'element-plus'
import { useGraphStore } from './index/taskObjectiveDecomposition/relationMap/stores/useGraphStore'
import { TaskNodeType, EdgeType } from '@/define/taskGraph.define'

const graphStore = useGraphStore()

// 测试状态
const testResults = ref<string[]>([])

// 计算属性
const storeStats = computed(() => ({
  nodeCount: graphStore.nodes.length,
  edgeCount: graphStore.edges.length,
  selectedCount: graphStore.selectedElements.length,
  canUndo: graphStore.canUndo,
  canRedo: graphStore.canRedo,
  isDirty: graphStore.isDirty
}))

// 添加测试结果
const addTestResult = (message: string, success: boolean = true) => {
  const status = success ? '✅' : '❌'
  testResults.value.push(`${status} ${message}`)
  console.log(`${status} ${message}`)
}

// 测试函数
const testInitializeGraph = () => {
  try {
    graphStore.initializeGraph('test-task-123')
    addTestResult('图谱初始化成功')
  } catch (error) {
    addTestResult(`图谱初始化失败: ${error}`, false)
  }
}

const testAddNode = () => {
  try {
    const node = graphStore.addNode({
      type: TaskNodeType.MAIN_TASK,
      position: { x: 100, y: 100 },
      data: {
        label: '测试主任务',
        description: '这是一个测试节点',
        status: 'in-progress',
        assignee: '测试用户'
      },
      draggable: true,
      selectable: true,
      deletable: false,
      connectable: true
    })
    
    if (node) {
      addTestResult(`添加节点成功: ${node.data.label}`)
    } else {
      addTestResult('添加节点失败', false)
    }
  } catch (error) {
    addTestResult(`添加节点失败: ${error}`, false)
  }
}

const testAddSubNode = () => {
  try {
    const subNode = graphStore.addNode({
      type: TaskNodeType.SUB_TASK,
      position: { x: 300, y: 200 },
      data: {
        label: '测试子任务',
        description: '这是一个测试子任务',
        status: 'not-started',
        assignee: '测试用户2',
        progress: 30
      },
      draggable: true,
      selectable: true,
      deletable: true,
      connectable: true
    })
    
    if (subNode) {
      addTestResult(`添加子任务节点成功: ${subNode.data.label}`)
    } else {
      addTestResult('添加子任务节点失败', false)
    }
  } catch (error) {
    addTestResult(`添加子任务节点失败: ${error}`, false)
  }
}

const testAddEdge = () => {
  try {
    if (graphStore.nodes.length < 2) {
      addTestResult('需要至少2个节点才能创建连线', false)
      return
    }
    
    const sourceNode = graphStore.nodes[0]
    const targetNode = graphStore.nodes[1]
    
    const edge = graphStore.addEdge({
      type: EdgeType.DEPENDENCY,
      source: sourceNode.id,
      target: targetNode.id,
      data: {
        label: '依赖关系',
        description: '测试连线'
      }
    })
    
    if (edge) {
      addTestResult(`添加连线成功: ${edge.source} -> ${edge.target}`)
    } else {
      addTestResult('添加连线失败', false)
    }
  } catch (error) {
    addTestResult(`添加连线失败: ${error}`, false)
  }
}

const testSelectElement = () => {
  try {
    if (graphStore.nodes.length > 0) {
      const nodeId = graphStore.nodes[0].id
      graphStore.selectElement(nodeId)
      addTestResult(`选择元素成功: ${nodeId}`)
    } else {
      addTestResult('没有节点可选择', false)
    }
  } catch (error) {
    addTestResult(`选择元素失败: ${error}`, false)
  }
}

const testUndoRedo = () => {
  try {
    // 先执行一个操作
    testAddNode()
    
    // 测试撤销
    if (graphStore.canUndo) {
      const success = graphStore.undo()
      addTestResult(`撤销操作: ${success ? '成功' : '失败'}`, success)
    } else {
      addTestResult('无法撤销：没有可撤销的操作', false)
    }
    
    // 测试重做
    if (graphStore.canRedo) {
      const success = graphStore.redo()
      addTestResult(`重做操作: ${success ? '成功' : '失败'}`, success)
    } else {
      addTestResult('无法重做：没有可重做的操作')
    }
  } catch (error) {
    addTestResult(`撤销重做测试失败: ${error}`, false)
  }
}

const testSaveLoad = () => {
  try {
    // 测试保存
    const saveSuccess = graphStore.saveGraph()
    addTestResult(`保存图谱: ${saveSuccess ? '成功' : '失败'}`, saveSuccess)
    
    // 测试加载
    if (graphStore.currentGraph) {
      const loadSuccess = graphStore.loadGraph(graphStore.currentGraph.id)
      addTestResult(`加载图谱: ${loadSuccess ? '成功' : '失败'}`, loadSuccess)
    }
  } catch (error) {
    addTestResult(`保存加载测试失败: ${error}`, false)
  }
}

const testDataSync = () => {
  try {
    // 测试数据同步
    const syncSuccess = graphStore.syncWithTaskData('test-task-123')
    addTestResult(`数据同步: ${syncSuccess ? '成功' : '失败'}`, syncSuccess)
    
    // 测试数据刷新
    const refreshSuccess = graphStore.refreshFromTaskData('test-task-123')
    addTestResult(`数据刷新: ${refreshSuccess ? '成功' : '失败'}`, refreshSuccess)
  } catch (error) {
    addTestResult(`数据同步测试失败: ${error}`, false)
  }
}

const runAllTests = async () => {
  testResults.value = []
  addTestResult('开始运行所有测试...')
  
  // 按顺序执行测试
  testInitializeGraph()
  await new Promise(resolve => setTimeout(resolve, 100))
  
  testAddNode()
  await new Promise(resolve => setTimeout(resolve, 100))
  
  testAddSubNode()
  await new Promise(resolve => setTimeout(resolve, 100))
  
  testAddEdge()
  await new Promise(resolve => setTimeout(resolve, 100))
  
  testSelectElement()
  await new Promise(resolve => setTimeout(resolve, 100))
  
  testUndoRedo()
  await new Promise(resolve => setTimeout(resolve, 100))
  
  testSaveLoad()
  await new Promise(resolve => setTimeout(resolve, 100))
  
  testDataSync()
  
  addTestResult('所有测试完成!')
  ElMessage.success('测试完成，请查看结果')
}

const clearTests = () => {
  testResults.value = []
  // 清空store数据
  graphStore.nodes = []
  graphStore.edges = []
  graphStore.selectedElements = []
  graphStore.operationHistory = []
  graphStore.historyIndex = -1
  graphStore.isDirty = false
  ElMessage.info('测试数据已清空')
}

onMounted(() => {
  addTestResult('图谱Store测试页面已加载')
})
</script>

<template>
  <div class="test-graph-store-page">
    <div class="header">
      <h1>图谱Store功能测试</h1>
      <p>测试图谱状态管理的各项功能</p>
    </div>
    
    <div class="content">
      <!-- 控制面板 -->
      <div class="control-panel">
        <h3>测试控制</h3>
        <div class="button-group">
          <el-button type="primary" @click="runAllTests">运行所有测试</el-button>
          <el-button @click="clearTests">清空测试</el-button>
        </div>
        
        <div class="individual-tests">
          <h4>单项测试</h4>
          <div class="button-grid">
            <el-button size="small" @click="testInitializeGraph">初始化图谱</el-button>
            <el-button size="small" @click="testAddNode">添加节点</el-button>
            <el-button size="small" @click="testAddSubNode">添加子节点</el-button>
            <el-button size="small" @click="testAddEdge">添加连线</el-button>
            <el-button size="small" @click="testSelectElement">选择元素</el-button>
            <el-button size="small" @click="testUndoRedo">撤销重做</el-button>
            <el-button size="small" @click="testSaveLoad">保存加载</el-button>
            <el-button size="small" @click="testDataSync">数据同步</el-button>
          </div>
        </div>
      </div>
      
      <!-- 状态面板 -->
      <div class="status-panel">
        <h3>Store状态</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="label">节点数量:</span>
            <span class="value">{{ storeStats.nodeCount }}</span>
          </div>
          <div class="stat-item">
            <span class="label">连线数量:</span>
            <span class="value">{{ storeStats.edgeCount }}</span>
          </div>
          <div class="stat-item">
            <span class="label">选中数量:</span>
            <span class="value">{{ storeStats.selectedCount }}</span>
          </div>
          <div class="stat-item">
            <span class="label">可撤销:</span>
            <span class="value" :class="{ 'success': storeStats.canUndo }">
              {{ storeStats.canUndo ? '是' : '否' }}
            </span>
          </div>
          <div class="stat-item">
            <span class="label">可重做:</span>
            <span class="value" :class="{ 'success': storeStats.canRedo }">
              {{ storeStats.canRedo ? '是' : '否' }}
            </span>
          </div>
          <div class="stat-item">
            <span class="label">已修改:</span>
            <span class="value" :class="{ 'warning': storeStats.isDirty }">
              {{ storeStats.isDirty ? '是' : '否' }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- 测试结果面板 -->
      <div class="results-panel">
        <h3>测试结果</h3>
        <div class="results-list">
          <div
            v-for="(result, index) in testResults"
            :key="index"
            class="result-item"
            :class="{
              'success': result.includes('✅'),
              'error': result.includes('❌')
            }"
          >
            {{ result }}
          </div>
          <div v-if="testResults.length === 0" class="no-results">
            暂无测试结果，点击"运行所有测试"开始测试
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.test-graph-store-page {
  min-height: 100vh;
  background: #f0f2f5;
  padding: 20px;
  
  .header {
    text-align: center;
    margin-bottom: 30px;
    
    h1 {
      color: #1890ff;
      margin-bottom: 10px;
    }
    
    p {
      color: #666;
      margin: 0;
    }
  }
  
  .content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto 1fr;
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
    
    .control-panel {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      h3, h4 {
        margin-top: 0;
        color: #333;
      }
      
      .button-group {
        margin-bottom: 20px;
        
        .el-button {
          margin-right: 10px;
        }
      }
      
      .button-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
      }
    }
    
    .status-panel {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      h3 {
        margin-top: 0;
        color: #333;
      }
      
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        
        .stat-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px;
          background: #f8f9fa;
          border-radius: 4px;
          
          .label {
            font-weight: 500;
            color: #666;
          }
          
          .value {
            font-weight: bold;
            color: #333;
            
            &.success {
              color: #52c41a;
            }
            
            &.warning {
              color: #fa8c16;
            }
          }
        }
      }
    }
    
    .results-panel {
      grid-column: 1 / -1;
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      h3 {
        margin-top: 0;
        color: #333;
      }
      
      .results-list {
        max-height: 400px;
        overflow-y: auto;
        
        .result-item {
          padding: 8px 12px;
          margin-bottom: 5px;
          border-radius: 4px;
          font-family: 'Monaco', 'Menlo', monospace;
          font-size: 13px;
          
          &.success {
            background: #f6ffed;
            border-left: 3px solid #52c41a;
          }
          
          &.error {
            background: #fff2f0;
            border-left: 3px solid #ff4d4f;
          }
          
          &:not(.success):not(.error) {
            background: #f8f9fa;
            border-left: 3px solid #d9d9d9;
          }
        }
        
        .no-results {
          text-align: center;
          color: #999;
          font-style: italic;
          padding: 40px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .test-graph-store-page {
    .content {
      grid-template-columns: 1fr;
      
      .button-grid {
        grid-template-columns: 1fr;
      }
      
      .stats-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>
