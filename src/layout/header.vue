<script setup lang="ts">
import {computed, onMounted, ref, watch} from 'vue'
import {useRouter} from 'vue-router'
import {useViewStore} from '@/stores/useViewStore'
import {useUserStore} from '@/stores/useUserStore'
import {ElMessage} from 'element-plus'
// import {NoticeTabList} from '@/define/Notification'
import {getNoticeBigTypes} from '@/api/OrganizeApi'
import {logout} from '@/api/UserApi'
import {usePermissions} from '@/hooks/usePermissions'

const emits = defineEmits([
	'collapse',
	'clickLabelItem',
	'clickAibotItem',
	'changeDepartmentItem',
	'clickNoticeRead',
	'clickClearNotice',
	'noticeScroll',
	'clickNoticeTabChange',
	'selectNoticeTypeChange',
	'getNoticeTypeList',
])
const props = defineProps({
	menuItem: {
		type: Object,
		default: () => {},
	},
	currentDepartmentInfo: {
		type: Object,
		default: () => {},
	},
	currentPlatformInfo: {
		type: Object,
		default: () => {},
	},
	isYKZFrame: {
		type: Boolean,
		default: false,
	},
	noticeDataList: {
		type: Array,
		default: () => [],
	},
	noticeTypeList: {
		type: Array,
		default: () => [],
	},
	noticeUnreadCount: {
		type: Array,
		default: () => [],
	},
})

const router = useRouter()
const viewStore = useViewStore()
const userStore = useUserStore()
const title = import.meta.env.VITE_TITLE
const labelsRef = ref()
const NoticeTabList = computed(() => props.noticeTypeList as any)
const promptActiveName = ref('')
const selectValue = ref('0')
const unread = computed(() =>
	props.noticeDataList.filter((item: any) => item.state || !item.hasOwnProperty('state'))
)
const unreadCount = ref(0)
const read = computed(() => props.noticeDataList.filter((item: any) => !item.state))
const noticeScrollIsBottom = ref(false)
let noticeTimer: any = null

const isCollapse = ref(false)
const host = location.hostname
const isDev =
	import.meta.env.DEV || host === 'localhost' || host === '************' || host === '***********'

const tabChange = (val: any) => {
	console.log(val)
	selectValue.value = '0'
	emits('clickNoticeTabChange', val)
}
const selectChange = (val: any) => {
	emits('selectNoticeTypeChange', Number(val))
}
const mouseoverCount = ref(0)
const mouseover = (val: any) => {
	mouseoverCount.value++
	if (mouseoverCount.value === 1) {
		emits('getNoticeTypeList')
	}
}
watch(
	() => props.menuItem,
	(newVal) => {
		// labelsRef.value.add(newVal)
	},
	{
		deep: true,
	}
)
watch(
	() => props.noticeTypeList,
	(val: any) => {
		if (val) {
			promptActiveName.value = val[0].bigType
		}
	}
)
watch(
	() => props.noticeUnreadCount,
	(val: any) => {
		if (val && val.length !== 0) {
			// 所有未读消息 累加
			unreadCount.value = val.reduce((a: any, b: any) => a + b.count, 0)
		} else {
			unreadCount.value = 0
		}
	}
)
const onToggle = () => {
	isCollapse.value = !isCollapse.value
	emits('collapse', isCollapse.value)
}

const onClickAibot = (val: any) => {
	emits('clickAibotItem', val)
}

const onChangeDepartment = (item: any) => {
	emits('changeDepartmentItem', item)
}

const onNoticeScroll = (val: any) => {
	const el = document.querySelector('.notice-box')
	if (el) {
		const {clientHeight} = el
		noticeScrollIsBottom.value = false
		// 400=高度300+偏移量100
		if (val.scrollTop >= clientHeight - 400) {
			clearTimeout(noticeTimer)
			noticeScrollIsBottom.value = true
			noticeTimer = setTimeout(() => {
				emits('noticeScroll', val, noticeScrollIsBottom.value)
			}, 200)
		}
	}
}

const onBackMain = () => {
	router.push('/')
}

const onLogOut = async () => {
	userStore.setLogout(true)
	ElMessage.success('退出成功')
	// 注销token
	await logout()
	userStore.clear(() => {
		// localStorage.clear()
		// 此处是为了保持当前人的登录状态。
		const keepFieldName = 'departmentforStreet'
		const keepFieldValue = localStorage.getItem(keepFieldName)
		localStorage.clear()
		// 重新设置之前保留的字段
		if (keepFieldValue !== null) {
			localStorage.setItem(keepFieldName, keepFieldValue)
		}
		viewStore.clear()
		// router.push({path: '/login'})
		router.push({path: '/401'})
	})
}

// 定义一个函数来替换字符串中的占位符
function replacePlaceholders(template, values) {
	return template.replace(/\{(\w+)\}/g, (_, key) => {
		return values[key] || `{${key}}` // 如果找不到对应的值，则保留原占位符
	})
}
</script>
<template>
	<el-header class="header">
		<h1 class="logo" @click="onBackMain">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				width="242.658"
				height="24.48"
				viewBox="0 0 242.658 24.48"
			>
				<path
					id="路径_46"
					data-name="路径 46"
					d="M4.9-13.532H1.36l.34-7.752H5.882Zm5.882,0H7.242l.34-7.752h4.182Zm32.028-1.156L42.33-11.22H14.722l.476-3.468ZM57.7-18.36l.2-1.292h-9.69l.442-3.23h9.69l-1.088-1.292h7.752l-.2,1.292h9.69l-.442,3.23h-9.69l-.2,1.292h9.078l-.476,3.23H63.716l-.2,1.292h10.37l-.442,3.23H57.9L55.692-8.67h.68L55.59-3.2,61.438-3.5,61-.272,48.62.306l.816-5.746H45.56l5.882-5.168H46.308l.442-3.23h10.3l.2-1.292H48.212l.476-3.23Zm7.854,8.738,1.292,1.87,5.678-.918-.476,3.23-3.2.544,3.672,5.2h-6.46l-6.97-9.928ZM95.88-18.054h1.5l2.55-2.244H88.944l.442-3.23h16.8l-.544,3.876-1.836,1.6h2.244L104.516-7.14a2.685,2.685,0,0,1-.884,1.666,2.5,2.5,0,0,1-1.734.68H98.056l1.836-1.6.136-.986H97.784l-.34,2.584H92.922l.34-2.584H90.984l-.34,2.584H86.122l1.836-13.26h2.108l-.578-1.6H95.3ZM76.432-.272,78.166-3.5,79.8-15.13H78.506l.476-3.23h7.106L84.184-4.9,88.842-3.5h15.81l-.442,3.23H86.122L82.79-1.258l-.544.986Zm8.432-23.256,1.394,3.876H81.09L79.7-23.528Zm7,9.69H94.18l.17-1.292H92.072Zm-.51,3.876h2.278l.17-1.292H91.528Zm6.8,0h2.21l.2-1.292H98.328Zm.714-5.168-.17,1.292h2.21l.2-1.292Zm12.988,1.6h-3.536l.34-7.752h4.182Zm5.882,0h-3.536l.34-7.752h4.182Zm5.44,2.346h24.106L145.724-.1H121.618Zm5.3-11.73h9.044l-.51,3.468h-4.488l-.1.68h4.556l-.442,3.2H134.3l2.142,3.672h-5.814L129.2-14.314l-2.04,2.448h-6.222l3.6-3.706h-3.5l.442-3.2h5.576l.1-.68h-.986l-.1.17h-4.59l2.89-4.42-.884-.476h5.848Zm9.758-.374h12.24L148.886-12h-12.24ZM128.52-6.9H141l.2-1.36H128.724ZM140.454-3.06l.17-1.224H128.146l-.17,1.224Zm4.114-12.1.646-4.794h-2.346l-.646,4.794ZM158.95.306l1.5-1.224.442-3.23H158.27L157.658.306h-5.814l2.278-16.082h14.212L166.4-1.938a2.626,2.626,0,0,1-.918,1.615,2.614,2.614,0,0,1-1.734.629ZM181.7-19.006l-6.256,1.054-.306,2.176h4.828l1.462-1.292-.612,4.522H168.878l1.632-11.628h5.814l-.442,2.992,6.29-1.054Zm-1.8,12.92-6.256,1.054-.306,2.176h4.9l-.068-.034,1.462-1.292L178.976.306H167.042l1.632-11.56h5.814l-.408,2.992,6.256-1.054ZM167.96-22.236l.476,5.814h-4.522L163.846-17l-9.622.578.408-2.89,2.108-3.944-.782-.918h7.106l-2.448,4.488,2.992-.17-.17-2.38Zm-8.67,10.982h2.584l.238-1.632h-2.584Zm-.612,4.182H161.3l.17-1.258h-2.584ZM194.344.306l3.366-23.834h16.15l-.68,4.794a2.616,2.616,0,0,1-.884,1.649,2.535,2.535,0,0,1-1.734.663h-4.148l1.462-1.258.374-2.618h-5.814l-.748,5.168H212.67l-.714,5.168-3.842,4.59L211.14.306h-5.168l-1.02-1.9-1.564,1.9Zm-7.412-11.594.816-5.78h-2.584l.476-3.23h2.584l.408-2.89-.85-.986h6.8l-.544,3.876h2.584l-.476,3.23H193.6l-.646,4.454,2.652-.578-.476,3.57-2.686.578-.986,7.106a2.626,2.626,0,0,1-.918,1.615A2.614,2.614,0,0,1,188.8.306h-6.086l3.06-3.162.68-4.9-2.686.578.51-3.536ZM199.75-1.53l3.23-3.808-2.108-3.978Zm1.462-10.37-.068.646h3.876l1.122,2.142,1.8-2.142.1-.646Zm26.01-6.46.2-1.292h-9.69l.442-3.23h9.69l-1.088-1.292h7.752l-.2,1.292h9.69l-.442,3.23h-9.69l-.2,1.292h9.078l-.476,3.23H233.24l-.2,1.292h10.37l-.442,3.23H227.426l-2.21,1.938h.68L225.114-3.2l5.848-.306-.442,3.23L218.144.306l.816-5.746h-3.876l5.882-5.168h-5.134l.442-3.23h10.3l.2-1.292h-9.044l.476-3.23Zm7.854,8.738,1.292,1.87,5.678-.918-.476,3.23-3.2.544,3.672,5.2h-6.46l-6.97-9.928Z"
					transform="translate(-1.36 24.174)"
					fill="#000"
				/>
			</svg>
			<span>一表通智能报表</span>
		</h1>

		<!-- <i class="icon collapse" :class="{active: isCollapse}" @click="onToggle">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				xmlns:xlink="http://www.w3.org/1999/xlink"
				t="1714359315213"
				class="icon"
				viewBox="0 0 1228 1024"
				version="1.1"
				p-id="4258"
				width="239.84375"
				height="200"
			>
				<path
					d="M0 0m47.919115 0l1054.220532 0q47.919115 0 47.919115 47.919115l0 0q0 47.919115-47.919115 47.919115l-1054.220532 0q-47.919115 0-47.919115-47.919115l0 0q0-47.919115 47.919115-47.919115Z"
					fill="#333333"
					p-id="4259"
				/>
				<path
					d="M0 862.544072m47.919115 0l1054.220532 0q47.919115 0 47.919115 47.919115l0 0q0 47.919115-47.919115 47.919115l-1054.220532 0q-47.919115 0-47.919115-47.919115l0 0q0-47.919115 47.919115-47.919115Z"
					fill="#333333"
					p-id="4260"
				/>
				<path
					d="M383.352921 575.029381m47.919115 0l670.867611 0q47.919115 0 47.919115 47.919115l0 0q0 47.919115-47.919115 47.919115l-670.867611 0q-47.919115 0-47.919115-47.919115l0 0q0-47.919115 47.919115-47.919115Z"
					fill="#333333"
					p-id="4261"
				/>
				<path
					d="M383.352921 287.514691m47.919115 0l670.867611 0q47.919115 0 47.919115 47.919115l0 0q0 47.919115-47.919115 47.919115l-670.867611 0q-47.919115 0-47.919115-47.919115l0 0q0-47.919115 47.919115-47.919115Z"
					fill="#333333"
					p-id="4262"
				/>
				<path
					d="M6.9825 502.46615L262.870574 683.737317a16.566323 16.566323 0 0 0 26.150146-13.691176V307.640719a16.566323 16.566323 0 0 0-26.150146-13.691176L6.9825 475.357622a16.703234 16.703234 0 0 0 0 27.108528z"
					fill="#333333"
					p-id="4263"
				/>
			</svg>
		</i> -->

		<h1 class="title"></h1>

		<div class="middle">
			<slot name="center"></slot>
		</div>

		<el-popover placement="bottom" :width="100" trigger="hover">
			<template #reference>
				<button class="aibot"></button>
			</template>
			<template #default>
				<ul class="aibot-list">
					<li @click="onClickAibot(4)">发版日志</li>
					<li @click="onClickAibot(0)">操作手册</li>
					<li @click="onClickAibot(1)">答疑手册</li>
					<li @click="onClickAibot(2)">问题反馈</li>
					<li v-if="usePermissions('Ledger.LLM')" @click="onClickAibot(3)">智能助手</li>
				</ul>
			</template>
		</el-popover>

		<el-popover placement="bottom" :width="300" trigger="hover">
			<template #reference>
				<el-badge
					v-if="unreadCount !== 0"
					:value="unreadCount"
					class="item"
					:offset="[-40, 0]"
					@mouseover="mouseover"
				>
					<Icons name="Notifications" size="28" class="mg-right-30"></Icons>
				</el-badge>
				<Icons
					v-else
					name="Notifications"
					size="28"
					class="mg-right-30"
					@mouseover="mouseover"
				></Icons>
			</template>
			<template #default>
				<el-tabs v-model="promptActiveName" @tab-change="tabChange">
					<el-tab-pane
						v-for="notice in NoticeTabList"
						:label="notice?.typeName"
						:name="notice?.bigType"
					>
						<template #label>
							<el-badge
								:is-dot="notice.unreadCount === 0 ? false : true"
								class="item"
								>{{ notice?.typeName }}</el-badge
							>
						</template>
						<div class="mg-bottom-10 tac w-full">
							<el-select
								v-model="selectValue"
								:teleported="false"
								@change="selectChange"
								placeholder="请选择通知类型"
								style="width: 95%; text-align: start"
							>
								<el-option label="全部" value="0" :key="0"></el-option>
								<el-option
									v-for="item in notice.typeList"
									:label="item.typeName"
									:value="item.type"
									:key="item.type"
								></el-option>
							</el-select>
						</div>

						<el-scrollbar
							always
							maxHeight="300px"
							class="top-notice-scrollbar"
							@scroll="onNoticeScroll"
						>
							<div class="notice-box" v-loading="false">
								<el-empty
									v-if="noticeDataList.length == 0 ? true : false"
									:image-size="100"
									description="无数据"
								/>
								<div
									class="notice-item"
									v-for="(item, index) in noticeDataList as any"
									:key="index"
									:class="{
										'is-read': item.hasOwnProperty('state')
											? !item.state
											: false,
									}"
									@click="$emit('clickNoticeRead', item, index)"
								>
									<div class="title">
										<div class="title-badge" v-if="item.state"></div>
										<div
											class="title-txt"
											:title="item.extraProperties.title"
											v-if="!item.extraProperties.L"
										>
											{{ item.extraProperties.title }}
										</div>
										<div v-else class="title-txt">
											{{
												replacePlaceholders(
													initDataObj[
														item.extraProperties.title.Resourcelame
													][item.extraProperties.title.Name],
													item.extraProperties.title.Values
												)
											}}
										</div>
									</div>
									<div
										class="txt"
										v-if="!item.extraProperties.L"
										v-html="item.extraProperties.message"
									></div>
									<div class="txt" v-else>
										{{
											replacePlaceholders(
												initDataObj[
													item.extraProperties.message.Resourcelame
												][item.extraProperties.message.Name],
												item.extraProperties.message.Values
											)
										}}
									</div>
									<div class="date">{{ item.extraProperties.createTime }}</div>
									<div class="divider"></div>
								</div>
								<div class="interval"></div>
							</div>
						</el-scrollbar>

						<div class="prompt-btnbox mg-top-10">
							<el-button-group style="width: 100%">
								<el-button
									style="width: 100%; height: 40px"
									type="primary"
									size="large"
									text
									bg
									@click="$emit('clickClearNotice')"
									>全部已读</el-button
								>
							</el-button-group>
						</div>
					</el-tab-pane>
				</el-tabs>
			</template>
		</el-popover>

		<el-avatar class="avater" :title="userStore.getUserInfo?.name">
			{{ userStore.getUserInfo?.name ? userStore.getUserInfo.name.substring(0, 1) : '未登录' }}
		</el-avatar>

		<el-dropdown size="small" trigger="hover">
			<span cursor-pointer text="12px" style="height: 28px; line-height: 28px"
				>{{
					currentDepartmentInfo
						? (currentDepartmentInfo.community ||
								currentDepartmentInfo.street ||
								currentDepartmentInfo.district ||
								currentDepartmentInfo.city) +
						  '-' +
						  currentDepartmentInfo.parentName +
						  '-' +
						  currentDepartmentInfo.name
						: '未登录'
				}}
				<el-icon text="#000" size="12">
					<ArrowDown />
				</el-icon>
			</span>
			<template #dropdown>
				<el-dropdown-menu v-if="currentPlatformInfo?.departments">
					<el-dropdown-item
						v-for="item in currentPlatformInfo.departments"
						@click="onChangeDepartment(item)"
					>
						{{
							(item.community || item.street || item.district || item.city) +
							'-' +
							item.parentName +
							'-' +
							item.name
						}}
					</el-dropdown-item>
				</el-dropdown-menu>
			</template>
		</el-dropdown>

		<el-link type="default" @click="onLogOut" title="退出" class="logout" v-if="isDev">
			<span>退出</span>
			<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 21 21">
				<path
					fill="none"
					stroke="currentColor"
					stroke-linecap="round"
					stroke-linejoin="round"
					d="m14.595 13.5l2.905-3l-2.905-3m2.905 3h-9m6-7l-8 .002c-1.104.001-2 .896-2 2v9.995a2 2 0 0 0 2 2h8.095"
				/>
			</svg>
		</el-link>
	</el-header>
</template>
<style scoped lang="scss">
.header {
	align-items: center;
	// background-color: rgba($color: var(--z-bg-rgb), $alpha: 0.2);
	background-color: #fff;
	// backdrop-filter: blur(10px);
	// -webkit-backdrop-filter: blur(10px);
	// border-bottom: 1px solid var(--z-line);
	color: var(--z-font-color);
	display: flex;
	height: 56px;
	overflow: hidden;
	// position: fixed;
	transition: all 0.3s ease-in-out;
	width: calc(100%);
	z-index: 3;

	.logo {
		align-items: center;
		background: url('/src/assets/image/logo.png') no-repeat left center;
		background-size: 40px 30px;
		cursor: pointer;
		display: flex;
		height: 40px;
		padding-left: 50px;
		span {
			display: none;
		}
	}

	.icon {
		margin-right: 0;
	}

	.title {
		align-items: center;
		display: flex;
		font-size: torem(18px);
		white-space: nowrap;
	}

	.avater {
		background-color: var(--z-main);
		color: #fff;
		flex: none;
		height: 25px;
		margin-right: 10px;
		width: 25px;
	}

	.username,
	.language {
		align-items: center;
		display: flex;
		color: var(--z-font-color);
	}

	.middle {
		flex: 1;
	}

	.collapse {
		cursor: pointer;
		margin-right: 20px;
		transform: rotate(0deg);

		&.active {
			transform: rotate(180deg) translateY(1px);
		}
	}

	.aibot {
		background: url(/src/assets/image/aibot2.png) no-repeat;
		background-size: cover;
		cursor: pointer;
		height: 25px;
		margin-right: 30px;
		width: 23px;
	}

	.logout {
		text-decoration: none;
		margin-left: 20px;
	}
	:deep(.el-link.is-underline:hover:after) {
		border-bottom: 0 !important;
	}
}

.labels {
	align-items: center;
	display: flex;
	height: inherit;
	margin: 0 20px;
}

.aibot-list {
	margin: -12px;
	li {
		border-bottom: 1px solid var(--z-line);
		cursor: pointer;
		padding: 10px 0;
		text-align: center;

		&:last-child {
			border-bottom: none;
		}

		&:hover {
			background-color: var(--z-bg-secondary);
			font-weight: 500;
		}
	}
}
</style>
<style>
.notice-box {
	.notice-item {
		border-bottom: 1px solid rgba(var(--z-line-rgb), 0.8);
		cursor: pointer;
		padding: 5px;
		margin-bottom: 10px;

		> div {
			margin-bottom: 5px;
		}

		.title {
			color: var(--z-font-color);
			font-size: 15px;
			font-weight: bold;

			.title-txt {
				text-overflow: ellipsis;
				overflow: hidden;
				white-space: nowrap;
			}
		}

		.txt {
			line-height: 2;
			text-align: justify;

			.click-view {
				color: var(--z-main);
				text-decoration: underline;
			}
		}

		.date {
			color: #333;
		}
	}

	.is-read {
		opacity: 0.5;
	}
}

.notice-item {
	.title {
		align-items: center;
		display: flex;
	}
	.title-badge {
		background-color: var(--z-danger);
		border-radius: 50%;
		height: 5px;
		margin-right: 5px;
		width: 5px;
	}
}
</style>
