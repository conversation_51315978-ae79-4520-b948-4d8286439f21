<script setup>
import {computed, onBeforeUnmount, onMounted, watch, ref, nextTick} from 'vue'
import {useRouter, useRoute} from 'vue-router'
import {MenuIcon} from '@/define/icon.define'

const emits = defineEmits(['clickItem', 'collapse'])
const props = defineProps({
	isCollapse: Boolean,
	items: Array,
	menuItem: Object | null, // 当前激活的菜单项
	beforeRouter: {
		type: Function,
		default: () => true,
	},
	badge: {
		type: Object,
		default: () => ({}),
	},
})

const route = useRoute()
const router = useRouter()

const menuRef = ref()
const menuItems = ref([])
const badges = ref(props.badge)
const activeIndex = ref(props.menuItem?.id)
const isCollapse = ref(props.isCollapse)

const SetMenuItems = () => {
	// 默认显示第一个菜单
	nextTick(() => {
		const historyMenuItems = localStorage.getItem('LABELS')
		if (!historyMenuItems) {
			emits('clickItem', menuItems.value[0])
		}
	})
}

const onClickMenuItem = (item) => {
	console.log('Aside Click:', item)
	const isContinue = props.beforeRouter(item)

	if (isContinue) {
		localStorage.setItem('MAIN_LABEL', item.path)
		emits('clickItem', item)
		if (item.path) {
			router.push({
				path: item.path,
			})
		}
	}
}

const onToggle = () => {
	isCollapse.value = !isCollapse.value
	emits('collapse', isCollapse.value)
}

watch(
	() => props.items,
	(newVal) => {
		console.log('Aside MenuItems:', newVal)
		menuItems.value = newVal
		SetMenuItems()
	},
	{deep: true}
)

watch(
	() => props.menuItem,
	(newVal) => {
		if (newVal.children && newVal.children.length > 0) {
			return
		}
		console.log('Aside MenuItem:', newVal)
		activeIndex.value = newVal?.id
	},
	{deep: true}
)

watch(
	() => props.badge,
	(newVal) => {
		nextTick(() => {
			badges.value = newVal
			console.log('Aside Badges:', newVal)
		})
	},
	{deep: true}
)

watch(
	() => route,
	(to) => {
		console.log('Aside Route:', to.path, menuItems.value)

		const toModule = `/${to.path.split('/')[1]}`
		let mainLable = localStorage.getItem('MAIN_LABEL')

		if (mainLable !== toModule) {
			mainLable = toModule
			localStorage.setItem('MAIN_LABEL', mainLable)
		}

		let curr = null
		let loops = (items, module) => {
			for (const item of items) {
				if (module && item.path === module) {
					curr = item
					return
				}

				if (item.path === to.path) {
					curr = item
					return
				}

				if (item.children && item.children.length > 0) {
					loops(item.children, module)
				}
			}
		}

		loops(menuItems.value)

		if (!curr) {
			loops(menuItems.value, mainLable)
		} else {
			localStorage.setItem('MAIN_LABEL', to.path)
		}

		console.log('Aside Route Curr:', curr)
		curr && emits('clickItem', curr)
		loops = null
	},
	{deep: true}
)

const onBackMain = () => {
	router.push('/')
}

onMounted(() => {})

onBeforeUnmount(() => {
	menuItems.value = []
})
</script>
<template>
	<el-aside width="170px" class="aside" :class="{collapse: isCollapse}">
		<!-- <h1 class="logo" @click="onBackMain">
			<span class="sub-logo"></span>
			<span style="display: none">一表通智能报表</span>
		</h1> -->

		<el-menu
			ref="menuRef"
			text-color="var(--z-bg)"
			active-text-color="var(--z-nav-font-active)"
			background-color="var(--z-main)"
			:default-openeds="[]"
			:collapse="isCollapse"
			:default-active="activeIndex"
		>
			<template v-for="item of menuItems">
				<el-menu-item
					v-if="
						(!item.children || item.children.length === 0) &&
						(item.hasOwnProperty('enable') ? item.enable : true)
					"
					:index="item.id"
					@click="onClickMenuItem(item)"
				>
					<i class="icon" v-html="MenuIcon[item.displayName]"></i>
					<template #title>
						<el-badge
							:value="badges[item.displayName]"
							:hidden="badges[item.displayName] === 0"
							:offset="[26, 27]"
							:max="99"
						>
							{{ item.displayName }}
						</el-badge>
					</template>
				</el-menu-item>

				<el-sub-menu
					v-if="
						item.children &&
						item.children.length > 0 &&
						(item.hasOwnProperty('enable') ? item.enable : true)
					"
					:index="item.id"
				>
					<template #title>
						<i class="icon" v-html="MenuIcon[item.displayName]"></i>
						<span>{{ item.displayName }}</span>
					</template>

					<template v-for="subItem of item.children">
						<el-menu-item
							v-if="!subItem.children || subItem.children.length === 0"
							:index="subItem.id"
							@click="onClickMenuItem(subItem)"
						>
							<template #title>
								<span>
									<el-badge
										:value="badges[subItem.displayName]"
										:hidden="badges[subItem.displayName] === 0"
										:offset="[26, 24]"
										:max="99"
										class="subitem-menu"
									>
										{{ subItem.displayName }}
									</el-badge>
								</span>
							</template>
						</el-menu-item>

						<el-sub-menu v-if="subItem.children && subItem.children.length > 0" :index="subItem.id">
							<template #title>
								<span>
									<el-badge
										:value="badges[subItem.displayName]"
										:hidden="badges[subItem.displayName] === 0"
										:offset="[26, 27]"
										:max="99"
									>
										{{ subItem.displayName }}
									</el-badge>
								</span>
							</template>

							<template v-for="subSubItem of subItem.children">
								<el-menu-item :index="subSubItem.index" @click="onClickMenuItem(subSubItem)">
									<template #title>
										<el-badge
											:value="badges[subSubItem.displayName]"
											:hidden="badges[subSubItem.displayName] === 0"
											:offset="[26, 27]"
											:max="99"
										>
											{{ subSubItem.displayName }}
										</el-badge>
									</template>
								</el-menu-item>
							</template>
						</el-sub-menu>
					</template>
				</el-sub-menu>
			</template>
		</el-menu>

		<div class="collapse-layout" @click="onToggle">
			<i class="icon collapse" :class="{active: isCollapse}">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					xmlns:xlink="http://www.w3.org/1999/xlink"
					t="1714359315213"
					class="icon"
					viewBox="0 0 1024 1024"
					version="1.1"
					p-id="4258"
					width="24"
					height="24"
				>
					<path
						d="M0 0m47.919115 0l1054.220532 0q47.919115 0 47.919115 47.919115l0 0q0 47.919115-47.919115 47.919115l-1054.220532 0q-47.919115 0-47.919115-47.919115l0 0q0-47.919115 47.919115-47.919115Z"
						fill="#fff"
						p-id="4259"
					/>
					<path
						d="M0 862.544072m47.919115 0l1054.220532 0q47.919115 0 47.919115 47.919115l0 0q0 47.919115-47.919115 47.919115l-1054.220532 0q-47.919115 0-47.919115-47.919115l0 0q0-47.919115 47.919115-47.919115Z"
						fill="#fff"
						p-id="4260"
					/>
					<path
						d="M383.352921 575.029381m47.919115 0l670.867611 0q47.919115 0 47.919115 47.919115l0 0q0 47.919115-47.919115 47.919115l-670.867611 0q-47.919115 0-47.919115-47.919115l0 0q0-47.919115 47.919115-47.919115Z"
						fill="#fff"
						p-id="4261"
					/>
					<path
						d="M383.352921 287.514691m47.919115 0l670.867611 0q47.919115 0 47.919115 47.919115l0 0q0 47.919115-47.919115 47.919115l-670.867611 0q-47.919115 0-47.919115-47.919115l0 0q0-47.919115 47.919115-47.919115Z"
						fill="#fff"
						p-id="4262"
					/>
					<path
						d="M6.9825 502.46615L262.870574 683.737317a16.566323 16.566323 0 0 0 26.150146-13.691176V307.640719a16.566323 16.566323 0 0 0-26.150146-13.691176L6.9825 475.357622a16.703234 16.703234 0 0 0 0 27.108528z"
						fill="#fff"
						p-id="4263"
					/>
				</svg>
			</i>
		</div>
	</el-aside>
</template>
<style scoped lang="scss">
.aside {
	background-color: var(--z-main);
	// border-right: 1px solid var(--z-line);
	display: flex;
	flex-direction: column;
	height: 100%;
	transition: all 0.3s ease-in-out;
	overflow-x: hidden;
	position: relative;
	z-index: 4;

	&.collapse {
		width: 60px;
	}

	.subitem-menu {
		padding-left: torem(10px);
	}

	:deep(.el-menu) {
		border-right: none;
		height: calc(100% - 40px);
		overflow: auto;
		overflow-x: hidden;
		.el-menu {
			overflow: hidden;
		}
		.el-menu > li > div,
		.el-menu > li > ul li {
			padding-left: calc(
				var(--el-menu-base-level-padding) + var(--el-menu-level) * var(--el-menu-level-padding) +
					torem(10px)
			);
		}
		.el-menu-item,
		.el-sub-menu__title {
			align-items: center;
			display: flex;
			.icon {
				line-height: 1;
				margin-top: -1px;
			}
		}
	}
	:deep(.el-sub-menu__icon-arrow) {
		margin-top: torem(-7px);
	}
}

.logo {
	background: url('/src/assets/image/logo.png') no-repeat 12px 10px;
	background-size: 40px 30px;
	cursor: pointer;
	height: 40px;
	.sub-logo {
		background: url('/src/assets/image/sys_name.png') no-repeat 55px 12px;
		background-size: 160px 30px;
		display: block;
		height: 40px;
	}
}

.collapse-layout {
	align-items: center;
	bottom: 0;
	border-top: 1px solid rgba(var(--z-line-rgb), 0.6);
	background: var(--z-main);
	cursor: pointer;
	display: flex;
	height: 40px;
	justify-content: center;

	width: 100%;
	.collapse {
		cursor: pointer;
		transform: rotate(0deg) scale(0.6);
		position: relative;
		top: 1px;

		&.active {
			transform: rotate(180deg) scale(0.6);
		}
	}
}
</style>
