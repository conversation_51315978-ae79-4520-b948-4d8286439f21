<script setup>
import {onMounted, onUnmounted, ref} from 'vue'

const contaioner = ref(null)
const scrollTop = ref(0)

const refresh = () => {
	scrollTop.value = contaioner.value.scrollTop
}
const onBackTop = () => {
	contaioner.value.scrollTop = 0
}

onMounted(() => {
	contaioner.value = document.querySelector('.main-container>.container-body')
	contaioner.value.addEventListener('scroll', refresh)
})
onUnmounted(() => {
	contaioner.value.removeEventListener('scroll', refresh)
})
</script>
<template>
	<el-footer class="footer">
		<div class="left">
			<el-button v-if="scrollTop >= 0" size="small" @click="onBackTop">
				<i class="icon i-ic-baseline-upload"></i>
				返回顶部
			</el-button>
		</div>
		<div class="center"></div>
		<div class="right"></div>
	</el-footer>
</template>
<style scoped lang="scss">
.footer {
	align-items: center;
	bottom: 0;
	background-color: transparent;
	display: flex;
	height: 40px;
	position: fixed;
	padding: 0;
	z-index: 2;

	> div {
		flex: 1;
	}

	.center {
		text-align: center;
	}

	.center,
	.right {
		pointer-events: none;
	}
}
</style>
