import {request as defHttp} from '@/api'

export const SaveWorkflow = (data: any) => {
  return defHttp.request({
    url: `/api/workflow/workflowSchemeInfo`,
    method: 'POST',
    data,
  })
}

// 获取数据结果分析列表
export const dataResultAnalysis = (params: any) => {
	return defHttp.request({
		url: `/api/new-feature/data-result-analysis`,
		method: 'GET',
		params,
	})
}

export const telecomTask = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/telecom-task`,
		method: 'GET',
		params,
	})
}

export const ledgerId = (id: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/${id}`,
		method: 'GET',
		// params,
	})
}
//创建数据结果分析
export const postresultanalysis = (data: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-result-analysis`,
    method: 'POST',
    data,
  })
}
export const warningMechanism = () => {
	return defHttp.request({
		url: `/api/new-feature/warning-mechanism`,
		method: 'GET',
		// params,
	})
}

//获取数据结果分析详情
export const dataresultanalysisId = (id: any) => {
	return defHttp.request({
		url: `/api/new-feature/data-result-analysis/${id}`,
		method: 'GET',
		// params,
	})
}
//更新数据结果分析
export const putdataresultanalysisId = (id:any,data: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-result-analysis/${id}`,
    method: 'put',
    data,
  })
}

//删除数据结果分析
export const deleteresultanalysisId = (id: string, ) => {
	return defHttp.request({
		url: `/api/new-feature/data-result-analysis/${id}`,
		method: 'DELETE',
	})
}

//查看配置结果分析后的台账数据
export const showdataanalysis = (id: any,params:any) => {
	return defHttp.request({
		url: `/api/new-feature/data-result-analysis/get-data-analysis/${id}`,
		method: 'GET',
		params,
	})
}
//批量删除数据结果分析
export const batchdelete = (data: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-result-analysis/batch-delete`,
    method: 'POST',
    data,
  })
}

//下载数据，数据导出Excel
export const analysisexport = (id:any,data: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-result-analysis/${id}/export`,
    method: 'POST',
    data,
  })
}
//分页查询下载记录
export const datadownloadrecord = (params:any) => {
	return defHttp.request({
		url: `/api/new-feature/data-download-record`,
		method: 'GET',
		params,
	})
}

export const telecomTaskList = (id:any) => {
	return defHttp.request({
		url: `/api/ledger/telecom-task/${id}`,
		method: 'GET',
		// params,
	})
}

export const dataManagement = (params:any) => {
	return defHttp.request({
		url: `/api/new-feature/data-rule-management`,
		method: 'GET',
		params,
	})
}


export const dataRuleManagement = (data: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-rule-management`,
    method: 'POST',
    data,
  })
}


export const managementbatchdelete = (data: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-rule-management/batch-delete`,
    method: 'POST',
    data,
  })
}
export const putdatarulemanagement = (id:any,data: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-rule-management/${id}`,
    method: 'put',
    data,
  })
}
export const putdatarulemanagementenable = (id:any,data: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-rule-management/${id}/enable`,
    method: 'put',
    data,
  })
}
