import {request as defHttp} from '@/api'

/**
 * 减负成效仪表盘---获取达标率排行数据
 * @param params
 */
export const getIntegrationSamplingRules = (params: any) => {
	return defHttp.request({
		url: `/api/new-feature/report-integration-sampling-rules`,
		method: 'GET',
		params,
	})
}
/**
 * 减负成效评估---获取规则详情
 * @param params
 */
export const getIntegrationSamplingRulesById = (id: string) => {
	return defHttp.request({
		url: `/api/new-feature/report-integration-sampling-rules/${id}`,
		method: 'GET',
	})
}

/**
 * 减负成效评估---删除规则
 * @param params
 */
export const deleteIntegrationSamplingRulesById = (id: string) => {
	return defHttp.request({
		url: `/api/new-feature/report-integration-sampling-rules/${id}`,
		method: 'DELETE',
	})
}
export const batchDeleteIntegrationSamplingRulesById = (ids: string[]) => {
	return defHttp.request({
		url: `/api/new-feature/report-integration-sampling-rules/batch`,
		method: 'DELETE',
		data: ids,
	})
}

/**
 * 减负成效评估---修改规则
 * @param params
 */
export const updateIntegrationSamplingRules = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/new-feature/report-integration-sampling-rules/${id}`,
		method: 'PUT',
		data,
	})
}
/**
 * 减负成效评估---新增规则
 * @param params
 */
export const createIntegrationSamplingRules = (data: any) => {
	return defHttp.request({
		url: `/api/new-feature/report-integration-sampling-rules`,
		method: 'POST',
		data,
	})
}

/**
 * 减负成效评估---获取任务列表
 * @param params
 */
export const getIntegrationSamplingTasks = (params: any) => {
	return defHttp.request({
		url: `/api/newFeature/reportTaskSampling`,
		method: 'GET',
		params,
	})
}

/**
 * 减负成效评估---获取任务详情
 * @param params
 */
export const getIntegrationSamplingTasksById = (id: string) => {
	return defHttp.request({
		url: `/api/newFeature/reportTaskSampling/${id}`,
		method: 'GET',
	})
}
/**
 * 减负成效评估---删除任务
 * @param params
 */
export const deleteIntegrationSamplingTasksById = (id: string) => {
	return defHttp.request({
		url: `/api/newFeature/reportTaskSampling/${id}`,
		method: 'DELETE',
	})
}
export const batchDeleteIntegrationSamplingTasksById = (ids: string[]) => {
	return defHttp.request({
		url: `/api/newFeature/reportTaskSampling/batch`,
		method: 'DELETE',
		data: ids,
	})
}
/**
 * 批量启停
 * @param flag
 * @param ids
 * @returns
 */
export const batchEnableOrDisable = (flag: boolean, ids: string[]) => {
	return defHttp.request({
		url: `/api/newFeature/reportTaskSampling/batch?enable=${flag}`,
		method: 'PUT',
		data: ids,
	})
}
/**
 * 减负成效评估---修改任务
 * @param params
 */
export const updateIntegrationSamplingTasks = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/newFeature/reportTaskSampling/${id}`,
		method: 'PUT',
		data,
	})
}
export const createIntegrationSamplingTasks = (data: any) => {
	return defHttp.request({
		url: `/api/newFeature/reportTaskSampling`,
		method: 'POST',
		data,
	})
}


