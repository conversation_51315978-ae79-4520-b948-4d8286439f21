import {request as defHttp} from '@/api'

// 批量删除数据关联校验
export const dataassociationverifydelete = (data: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-association-verify/batch-delete`,
    method: 'POST',
    data,
  })
}

// 获取数据结果分析列表
export const dataassociationverify = (params: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-association-verify`,
    method: 'GET',
    params,
  })
}
// 创建数据关联校验
export const dataassociationverifyadd = (data: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-association-verify`,
    method: 'POST',
    data,
  })
}
// 编辑数据关联校验
export const dataassociationverifyput = (id:any, data: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-association-verify/${id}`,
    method: 'put',
    data,
  })
}

// 获取数据结果分析列表
export const dataassociationverifyconfig = (params: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-association-verify-config`,
    method: 'GET',
    params,
  })
}


// 创建数据关联校验配置
export const dataassociationverifyaddconfig = (data: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-association-verify-config`,
    method: 'POST',
    data,
  })
}
// 修改数据关联校验配置
export const dataassociationverifyputconfig = (id:any, data: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-association-verify-config/${id}`,
    method: 'put',
    data,
  })
}

// 删除数据关联校验配置
export const dataassociationverifydeleteconfig = (id: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-association-verify-config/${id}`,
    method: 'DELETE',
  })
}