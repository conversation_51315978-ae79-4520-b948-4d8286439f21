import {request as defHttp} from '@/api'

export const PostProcessDelegation = (data: any) => {
	return defHttp.request({
		url: `/api/new-feature/process-delegations`,
		method: 'POST',
		data,
	})
}

export const PutProcessDelegation = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/new-feature/process-delegations/${id}`,
		method: 'PUT',
		data,
	})
}

export const DeleteProcessDelegation = (id: string) => {
	return defHttp.request({
		url: `/api/new-feature/process-delegations/${id}`,
		method: 'DELETE',
	})
}

export const PutSuggestion = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/new-feature/process-monitorings/${id}/optimization-suggestion`,
		method: 'PUT',
		data,
	})
}
