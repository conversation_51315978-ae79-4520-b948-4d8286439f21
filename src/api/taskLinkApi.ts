import { request as defHttp } from '@/api'
const Urlkey = 'base'

/*  模块：任务关联进度
    接口：TelecomTask 电信任务
*/

// 获取列表
export const getListApi = (params: any) => {
  return defHttp.request({
    url: '/api/ledger/telecom-task',
    method: 'GET',
    params,
    headers: { Urlkey },
  })
}


// id获取详情
export const getDetailApi = (id: string) => {
  return defHttp.request({
    url: `/api/ledger/telecom-task/${id}`, //
    method: 'GET',
  })
}

// 获取台账列表
export const getLedgerListApi = (params: any) => {
  return defHttp.request({
    url: '/api/ledger/ledger-user/my-ledgers',
    method: 'GET',
    params,
    headers: { Urlkey },
  })
}

// 获取配置进度计算方式 -得到台账字段信息
/* id 台账ID
computeMode 计算模式 */
export const getLedgerTableFieldsApi = (params: any) => {
  return defHttp.request({
    url: '/api/ledger-service/plan-progress-config/ledger-table-fields',
    method: 'GET',
    params,
    headers: { Urlkey },
  })
}



// 查询配置进度计算方式
export const queryPlanProgressConfigApi = (id: any) => {
  return defHttp.request({
    url: `/api/ledger-service/plan-progress-config/${id}`,
    method: 'GET',
    headers: { Urlkey },
  })
}

// 修改配置进度计算方式
export const updatePlanProgressConfigApi = (id: any, data: any,) => {
  return defHttp.request({
    url: `/api/ledger-service/plan-progress-config/${id}`,
    method: 'PUT',
    data,
    headers: { Urlkey },
  })
}

// 保存 绑定台账
export const savePlanProgressConfigApi = (taskId: any, params: any) => {
  return defHttp.request({
    url: `/api/ledger/telecom-task/${taskId}/ledger`,
    method: 'PUT',
    params,
    headers: { Urlkey },
  })
}

// 查看数据 根据配置的Id查询台账的数据
// 查表格头
export const headerLedgerApi = (id: any) => {
  return defHttp.request({
    url: `/api/ledger-service/ledger/${id}`,
    method: 'GET',
    headers: { Urlkey },
  })
}

// 查列表数据： id: 传配置的id
export const lookLedgerApi = (data: any) => {
  return defHttp.request({
    url: `/api/ledger-service/plan-progress-config/${data.id}/config-ledger-data`,
    method: 'GET',
    params: data.params,
    headers: { Urlkey },
  })
}

// -----
/**
 * 1 确认关联 更新任务状态
 * @param status // 1 取消关联 2 关联
 * @returns
 */
export const makeSurePlanProgressConfigApi = (taskId: any, params: any) => {
  return defHttp.request({
    url: `/api/ledger/telecom-task/${taskId}/status`,
    method: 'PUT',
    params,
    headers: { Urlkey },
  })
}

// 2创建配置进度计算方式
export const setPlanProgressConfigApi = (data: any) => {
  return defHttp.request({
    url: '/api/ledger-service/plan-progress-config',
    method: 'POST',
    data,
    headers: { Urlkey },
  })
}

// 3 绑定台账 taskId  params: ledgerId
export const BindLedgerByTaskId = (taskId: string, params: any) => {
  return defHttp.request({
    url: `/api/ledger/telecom-task/${taskId}/ledger`,
    method: 'PUT',
    params,
    headers: { Urlkey },
  })
}

// 4 删除任务绑定台账和台账配置 台账id taskId
export const deleteLedgerApi = (taskId: any) => {
  return defHttp.request({
    url: `/api/ledger/telecom-task/${taskId}/delete-config`,
    method: 'GET',
    headers: { Urlkey },
  })
}
// 删除配置进度计算方式
export const deletePlanProgressConfigApi = (id: any) => {
  return defHttp.request({
    url: `/api/ledger-service/plan-progress-config/${id}`,
    method: 'DELETE',
    headers: { Urlkey },
  })
}

export const indicatorgerApi = (dgpId: any) => {
  return defHttp.request({
    url: `/api/ledger/indicator-cockpit/data/${dgpId}`,
    method: 'GET',
    headers: { Urlkey },
  })
}