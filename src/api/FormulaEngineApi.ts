import {request as defHttp} from '@/api'

export const PostFormulaEngine = (data: any = {}) => {
	return defHttp.request({
		url: `/api/newFeature/formula-engine`,
		method: 'POST',
		data,
	})
}

export const PutFormulaEngine = (id: string, data: any = {}) => {
	return defHttp.request({
		url: `/api/newFeature/formula-engine/${id}`,
		method: 'PUT',
		data,
	})
}

export const DeleteFormulaEngine = (id: string) => {
	return defHttp.request({
		url: `/api/newFeature/formula-engine/${id}`,
		method: 'DELETE',
	})
}
