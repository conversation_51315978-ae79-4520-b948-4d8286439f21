import {request as defHttp} from '@/api'

/**
 * 根据报表id删除所有报表列表数据
 * @param id 报表id
 */
export const deleteReportListByReportId = (id: string) => {
	return defHttp.request({
		url: `/api/filling/report-table/${id}/all-row`,
		method: 'DELETE',
	})
}

/**
 * 根据报表id删除所有报表数据(关联字段、列表、配置规则)
 * @param id 报表id
 */
export const deleteReportDataAllByReportId = (id: string) => {
	return defHttp.request({
		url: `/api/filling/report-table/${id}/all-column`,
		method: 'DELETE',
	})
}

/**
 * 保存报表业务表字段关联关系
 * @param data 关联数据
 * @returns
 */
export const saveLedgerFieldLinked = (data: {
	ledgerId: string
	ledgerField: string
	reportTableId: string
	columnName: string
}) => {
	return defHttp.request({
		url: '/api/filling/fillingConfig',
		method: 'POST',
		data,
	})
}

/**
 * 根据关联关系id删除报表业务表字段关联关系
 * @param id 关联关系id
 */
export const deleteLedgerFieldLinkedById = (id: string) => {
	return defHttp.request({
		url: `/api/filling/fillingConfig/${id}`,
		method: 'DELETE',
	})
}

/**
 * 根据报表任务id和组织id获取报表头部信息
 * @param reportId 报表id
 * @param organizationId
 * @returns
 */
export const getReportTableList = (id: string, id2: string = '') => {
	const url = id2
		? `/api/filling/report-task-ou/${id}/${id2}/report-tables`
		: `/api/filling/report-task-ou/${id}/report-tables`
	return defHttp.request({
		url,
		method: 'GET',
	})
}

/**
 * 根据报表id获取报表数据
 * @param reportTableId 报表id
 * @returns
 */
export const getReportTableDataByReportTableId = (reportTableId: string) => {
	return defHttp.batch(`/api/filling/report-table/${reportTableId}/data`)
}

/**
 * 获取报表业务表字段关联关系
 * @param reportTableId 报表id
 */
export const getLinkedListByReportTabkeId = (reportTableId: string) => {
	return defHttp.request({
		url: `/api/filling/fillingConfig/by-table?ReportTableId=${reportTableId}`,
		method: 'GET',
	})
}

/**
 * 根据报表id获取切换报表类型
 * @param reportTableId 报表id
 * @param tableType 报表类型
 * @returns
 */
export const changeReportTableType = (reportTableId: string, tableType: number) => {
	return defHttp.request({
		url: `/api/filling/report-table/${reportTableId}/type`,
		method: 'PUT',
		data: {
			tableType,
		},
	})
}

export const getTodoNotice = (params: any, State: number, IsShowStateStop: boolean = false) => {
	params = {
		...params,
		State,
		IsShowStateStop,
	}
	return defHttp.request({
		url: `/api/platform/reportLedgerNotice`,
		method: 'GET',
		params,
		headers: {Urlkey: 'iframeCode'},
	})
}

export const GetLockReprotTable = (params: any) => {
	return defHttp.request({
		url: `/api/filling/report-table/get-reportTable-permissions-user`,
		method: 'GET',
		params,
	})
}

export const DeleteLockReprotTable = (params: any) => {
	return defHttp.request({
		url: `/api/filling/report-table/delete-reportTable-permissions-user`,
		method: 'DELETE',
		params,
	})
}

export const SetLockReprotTable = (params: any) => {
	return defHttp.request({
		url: `/api/filling/report-table/set-reportTable-permissions-user`,
		method: 'POST',
		params,
	})
}

/**
 * 更新填报人状态
 * @param data
 * @returns
 */
export const updateFillerStatus = (data: any) => {
	return defHttp.request({
		url: `/api/filling/report-task-ou-filler/update`,
		method: 'PUT',
		data,
	})
}

export const SetInternalStaff = (
	data: any,
	reportTaskId: string,
	areaOrganizationUnitId: string
) => {
	return defHttp.request({
		url: `/api/filling/report-task-ou/${reportTaskId}/${areaOrganizationUnitId}/set-internal-staff`,
		method: 'PUT',
		data,
	})
}

/**
 * 执行报表任务
 * @param data
 * @returns
 */
export const ExecutionReportTask = (params: any) => {
	return defHttp.request({
		url: `/api/filling/plan-task/planTask-execution`,
		method: 'POST',
		params,
	})
}

/**
 * 下发填报转发任务
 * @param param0
 * @returns
 */

export const IssueDataLeader = ({data, params}: any) => {
	return defHttp.request({
		url: `/api/filling/report-task-ou/DataLeader-issue`,
		method: 'POST',
		params,
		data,
	})
}

export const newIssueDataLeader = (data: any, params: any) => {
	return defHttp.request({
		url: `/api/filling/report-task-ou/issue`,
		method: 'POST',
		params,
		data,
	})
}

/**
 * 任务填报提交
 */
export const PushReportFlowTask = (data: any, id: string) => {
	return defHttp.request({
		url: `/api/filling/report-task-ou/${id}/submit`,
		method: 'POST',
		data,
	})
}

/**
 * 报表任务审核
 * @param params
 * @returns
 */
export const PushReportAudit = (params: any) => {
	return defHttp.request({
		url: '/api/filling/report-task-ou/data-audit',
		method: 'POST',
		params,
	})
}

/**
 * 删除
 * @param reportTaskId
 * @param areaOrganizationUnitId
 * @returns
 */
export const DeleteReportFlowTask = (reportTaskId: string, areaOrganizationUnitId: string) => {
	return defHttp.request({
		url: `/api/filling/report-task-ou/${reportTaskId}/${areaOrganizationUnitId}`,
		method: 'DELETE',
	})
}

/**
 * 内部转发报表撤回
 * @param params
 * @returns
 */
export const ReportRevokeInnerWrite = (params: any) => {
	return defHttp.request({
		url: `/api/filling/report-task-ou/revoke-innerWrite`,
		method: 'PUT',
		data: params,
	})
}
/**
 * 内部填报撤回
 * @param params
 * @returns
 */
export const detaileRevok = (params: any) => {
	return defHttp.request({
		url: `/api/filling/report-task-ou/detail-revok`,
		method: 'PUT',
		params,
	})
}
/**
 * 获取报表创建/转发详情
 * @param reportTaskId 任务ID
 * @returns
 */
export const GetReportInfo = (reportTaskId: any) => {
	return defHttp.request({
		url: `/api/filling/plan-task/${reportTaskId}`,
		method: 'GET',
	})
}

/**
 * 根据制定部门发送催办通知
 * @param params
 * @returns
 */
export const PushReportNotice = (data: any) => {
	return defHttp.request({
		url: `/api/filling/message-notice/part-urge`,
		method: 'POST',
		data,
	})
}

/**
 * 获取内部/下发任务流程进度
 * @param planTaskId
 * @returns
 */
export const GetPlanTaskProcess = (planTaskId: string) => {
	return defHttp.request({
		url: `/api/filling/process/planTask-process?planTaskId=${planTaskId}`,
		method: 'GET',
	})
}

/**
 * 查询填报详情流程记录(我创建的-详情-表格查看数据-详情, 下发/内部)
 * @param params
 * @returns
 */
export const GetPlanTaskProcessDetail = (params: any) => {
	return defHttp.request({
		url: `/api/filling/process/reportTask-areaOrganizationUnit-detail`,
		method: 'GET',
		params,
	})
}

/**
 * 我转发的-详情-
 * @param params
 */
export const GetTurnTaskProcess = (params: any) => {
	return defHttp.request({
		url: `/api/filling/process/transpond-reportTask-areaOrganizationUnit-detail`,
		method: 'GET',
		params,
	})
}

/**
 * 任务待办-填报任务-填报详情-流程记录
 * @param params
 */
export const GetReportFillProcess = (params: any) => {
	return defHttp.request({
		url: `/api/filling/process/pending-reportTask-areaOrganizationUnit`,
		method: 'GET',
		params,
	})
}

/**
 * 手动完成任务填报业务表任务填报
 * @param id
 * @returns
 */
export const ConfirmCompleted = (id: string) => {
	return defHttp.request({
		url: `/api/platform/workToDoRecord/${id}/manual-confirm-completed`,
		method: 'POST',
	})
}

/**
 * 根据businessId获取填报记录详情
 * @param id
 * @returns
 */
export const GetReportTaskDetail = (id: string) => {
	return defHttp.request({
		url: `/api/platform/wf-task-items/${id}`,
		method: 'GET',
	})
}

/**
 * 获取报表创建进度
 * @param ids
 * @returns
 */
export const GetPlanTaskProgress = (ids: string[]) => {
	return defHttp.request({
		url: `/api/filling/plan-task/progress`,
		method: 'POST',
		data: ids,
	})
}

/**
 * 1 是否流程审核中
 * @param
 * @returns
 */

export const getAuditingApi = () => {
	return defHttp.request({
		url: `/api/workflow/workflowProcess/auditing`,
		method: 'GET',
	})
}

/**
 * 2 批量审核
 * @param code 'agree
 * @param des '描述
 * @param name '通过
 * @param taskIds '[]'
 * @returns
 */
export const auditSomeApi = (data: any) => {
	return defHttp.request({
		url: `/api/workflow/workflowProcess/audit`,
		method: 'POST',
		data,
	})
}

/**
 * 3 全部审核
 * @param code 'agree
 * @param des '描述
 * @param name '通过
 * @param queryParam '{}
 * @returns
 */
export const auditAllApi = (data: any) => {
	return defHttp.request({
		url: `/api/workflow/workflowProcess/audit-all`,
		method: 'POST',
		data,
	})
}

/**
 * 4 审核编辑数据
 * @param ledgerId '
 * @param id '
 * @param data '
 * @returns
 */
export const auditEditApi = (ledgerId: any, id: any, data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledgerAuditedData/${ledgerId}/${id}/update-by-other-user `,
		method: 'POST',
		data: data,
	})
}
/**
 * 催办
 * @param id '
 * @param data '
 * @returns
 */
export const urging = (id: any, data: any) => {
	return defHttp.request({
		url: `/api/filling/message-notice/urging/${id} `,
		method: 'POST',
		data: data,
	})
}
/**
 * 下载文件
 * @param id
 * @returns
 */
export const downloadStatementFile = (id: string, status: number) => {
	return defHttp.request({
		url: `/api/filling/planTask-or-reportTask-file-download/download`,
		method: 'POST',
		data: {
			fileInfoId: id,
			status,
		},
		responseType: 'blob',
	})
}

export const downloadStaticFile = (id: string) => {
	return defHttp.request({
		url: `/api/filling/file-download/download?fileInfoId=${id}`,
		method: 'GET',
		responseType: 'blob',
	})
}

/**
 * 获取当前所属大部门的下级部门
 * @param params
 * @returns
 */
export const getDepartmentChildren = () => {
	return defHttp.request({
		url: `/api/platform/departmentInternal/get-department-children`,
		method: 'GET',
	})
}
