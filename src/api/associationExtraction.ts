import {request as defHttp} from '@/api'
// 创建关联同步
export const postdataComparisons = (data: any) => {
  return defHttp.request({
    url: `/api/ledger/associatedSyn`,
    method: 'POST',
    data,
  })
}
// 更新关联同步
export const putassociatedSyn = (LedgerId: any,SourceLedgerId:any,data: any) => {
  return defHttp.request({
    url: `/api/ledger/associatedSyn/${LedgerId}/${SourceLedgerId}`,
    method: 'PUT',
    data,
  })
}



// 获取关联同步
export const associatedSynList = (params: any) => {
  return defHttp.request({
    url: `/api/ledger/associatedSyn`,
    method: 'GET',
    params,
  })
}

// 删除关联同步
export const deleteassociatedSyn = (LedgerId: any,SourceLedgerId:any) => {
  return defHttp.request({
    url: `/api/ledger/associatedSyn/${LedgerId}/${SourceLedgerId}`,
    method: 'DELETE',
    // params,
  })
}

// 批量删除
export const deletebatch = (data: any) => {
  return defHttp.request({
    url: `/api/ledger/associatedSyn/delete-batch`,
    method: 'POST',
    data,
  })
}

