// 自定义公式计算模块的接口列表

import {request as defHttp} from '@/api'

/**
 * 获取自定义公式计算列表
 * @param params
 * @returns
 */
export const getCustomCalculationList = (params: any) =>
	defHttp.request({
		url: `/api/new-feature/calculation-formulas`,
		method: 'GET',
		params,
	})

/**
 * 获取自定义公式计算详情
 * @param id
 * @returns
 */
export const getCustomCalculationById = (id: string) =>
	defHttp.request({
		url: `/api/new-feature/calculation-formulas/${id}`,
		method: 'GET',
	})

/**
 * 创建自定义公式计算
 * @param data
 * @returns
 */
export const createCustomCalculation = (data: any) =>
	defHttp.request({
		url: `/api/new-feature/calculation-formulas`,
		method: 'POST',
		data,
	})

/**
 * 修改自定义公式计算
 * @param id
 * @param data
 * @returns
 */

export const updateCustomCalculation = (id: string, data: any) =>
	defHttp.request({
		url: `/api/new-feature/calculation-formulas/${id}`,
		method: 'PUT',
		data,
	})

/**
 * 删除自定义公式计算
 * @param id
 * @returns
 */

export const deleteCustomCalculation = (id: string) =>
	defHttp.request({
		url: `/api/new-feature/calculation-formulas/${id}`,
		method: 'DELETE',
	})

/**
 * 批量删除自定义公式计算
 * @param ids
 * @returns
 */
export const batchDeleteCustomCalculation = (ids: string[]) =>
	defHttp.request({
		url: `/api/new-feature/calculation-formulas/batch`,
		method: 'DELETE',
		data: ids,
	})

/**
 * 获取计算规则列表
 * @params  params
 */

export const getCalculationRules = (params: any) =>
	defHttp.request({
		url: `/api/new-feature/calculation-rules`,
		method: 'GET',
		params,
	})

/**
 * 获取计算规则详情
 * @params  id
 */
export const getCalculationRulesById = (id: string) =>
	defHttp.request({
		url: `/api/new-feature/calculation-rules/${id}`,
		method: 'GET',
	})

/**
 * 创建计算规则
 * @params  data
 */

export const createCalculationRules = (data: any) =>
	defHttp.request({
		url: `/api/new-feature/calculation-rules`,
		method: 'POST',
		data,
	})
/**
 * 更新计算规则
 * @params  id
 * @params  data
 */
export const updateCalculationRules = (id: string, data: any) =>
	defHttp.request({
		url: `/api/new-feature/calculation-rules/${id}`,
		method: 'PUT',
		data,
	})
/**
 * 删除计算规则
 * @params  id
 */

export const deleteCalculationRulesById = (id: string) =>
	defHttp.request({
		url: `/api/new-feature/calculation-rules/${id}`,
		method: 'DELETE',
	})

/**
 * 批量删除计算规则
 * @params  ids
 */

export const batchDeleteCalculationRulesById = (ids: string[]) =>
	defHttp.request({
		url: `/api/new-feature/calculation-rules/batch`,
		data: ids,
		method: 'DELETE',
	})

/**
 * 下载计算规则附件
 */
export const getCalculationRulesDownload = (params: any) =>
	defHttp.request({
		url: `/api/new-feature/calculation-rules/file-download`,
		method: 'GET',
		params,
		responseType: 'blob',
	})
/**
 * 获取计算任务列表
 * @params  params
 */

export const getCalculationTasks = (params: any) =>
	defHttp.request({
		url: `/api/new-feature/computational-tasks`,
		method: 'GET',
		params,
	})
/**
 * 获取计算任务详情
 * @params  id
 */
export const getCalculationTasksById = (id: string) =>
	defHttp.request({
		url: `/api/new-feature/computational-tasks/${id}`,
		method: 'GET',
	})
/**
 * 创建计算任务
 */
export const createCalculationTasks = (data: any) =>
	defHttp.request({
		url: `/api/new-feature/computational-tasks`,
		method: 'POST',
		data,
	})
/**
 * 编辑计算任务
 */
export const updateCalculationTasks = (id: string, data: any) =>
	defHttp.request({
		url: `/api/new-feature/computational-tasks/${id}`,
		method: 'PUT',
		data,
	})
/**
 * 删除计算任务
 * @params  id
 */
export const deleteCalculationTasksById = (id: string) =>
	defHttp.request({
		url: `/api/new-feature/computational-tasks/${id}`,
		method: 'DELETE',
	})

/**
 * 批量删除计算任务
 */
export const batchDeleteCalculationTasksById = (ids: string[]) =>
	defHttp.request({
		url: `/api/new-feature/computational-tasks/${ids}`,
		method: 'DELETE',
	})

/**
 * 新增计算结果推送
 *
 */
export const createCalculationResultsPush = (data: any) =>
	defHttp.request({
		url: `/api/newFeature/calculation-result-send`,
		method: 'POST',
		data,
	})
/**
 * 编辑计算结果推送
 */
export const updateCalculationResultsPush = (id: string, data: any) =>
	defHttp.request({
		url: `/api/newFeature/calculation-result-send/${id}`,
		method: 'PUT',
		data,
	})
/**
 * 删除计算结果推送
 */
export const deleteCalculationResultsPushById = (id: string) =>
	defHttp.request({
		url: `/api/newFeature/calculation-result-send/${id}`,
		method: 'DELETE',
	})
/**
 * 批量删除运算结果推送
 */
export const batchDeleteCalculationResultsPushById = (ids: string[]) =>
	defHttp.request({
		url: `/api/newFeature/calculation-result-send/delete-batch`,
		method: 'POST',
		data: ids,
	})
