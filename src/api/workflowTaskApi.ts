import {request as defHttp} from '@/api'

const Urlkey = 'base'

/**
 * 获取流程任务详情
 * @param id STRING
 * @returns
 */
export const GetWorkflowTaskById = (id: string) => {
	return defHttp.request({
		url: `/api/platform/wf-plan-tasks/${id}`,
		method: 'GET',
		headers: {Urlkey},
	})
}

/**
 * 获取流程任务子列表
 * @param data
 * @returns
 */
export const GetWorkflowTaskTable = (data: any) => {
	return defHttp.request({
		url: '/api/platform/wf-task-items',
		method: 'GET',
		params: data,
		headers: {Urlkey},
	})
}

export const pauseTask = (id: string) => {
	return defHttp.request({
		url: `/api/platform/wf-plan-tasks/pause?id=${id}`,
		method: 'POST',
		headers: {Urlkey},
	})
}

export const beginTask = (id: string) => {
	return defHttp.request({
		url: `/api/platform/wf-plan-tasks/resume?id=${id}`,
		method: 'POST',
		headers: {Urlkey},
	})
}

export const GetWorkflowProcess = (id: string) => {
	return defHttp.request({
		url: `/api/workflow/workflowProcess/${id}`,
		method: 'GET',
		headers: {Urlkey},
	})
}

export const GetWorkflowAuditors = (businessType: string, schemeCode: string, nodeId: string) => {
	return defHttp.request({
		url: `/api/workflow/workflowAuditors/${businessType}/${schemeCode}/${nodeId}`,
		method: 'GET',
		headers: {Urlkey},
	})
}

export const getWorkFlowLOgs = (processId: string) => {
	return defHttp.request({
		url: `/api/workflow/workflowTaskLog/${processId}`,
		method: 'GET',
		headers: {Urlkey},
	})
}
