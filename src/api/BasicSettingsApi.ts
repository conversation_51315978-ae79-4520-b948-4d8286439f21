import {request as defHttp} from '@/api/index'

export const PostVisualizationEngine = (data: any) => {
	return defHttp.request({
		url: '/api/new-feature/visualization-engines',
		method: 'POST',
		data,
	})
}

export const PutVisualizationEngine = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/new-feature/visualization-engines/${id}`,
		method: 'PUT',
		data,
	})
}

export const DeleteVisualizationEngine = (id: string) => {
	return defHttp.request({
		url: `/api/new-feature/visualization-engines/${id}`,
		method: 'DELETE',
	})
}

export const GetLedgers = () => {
	return defHttp.request({
		url: '/api/ledger/ledger-user/my-online-ledgers',
		method: 'GET',
	})
}

export const GetLedgerTableFieldNames = (id: string) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/${id}/table-field-names`,
		method: 'GET',
	})
}
