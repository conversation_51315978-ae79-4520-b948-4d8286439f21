import {request as defHttp} from '@/api'

const Urlkey = 'base'

export const CreateOrUpdateLinkRunway = (
	data: any,
	method: 'PUT' | 'POST' = 'POST',
	params: any
) => {
	return defHttp.request({
		url: '/api/ledger-service/ledger-runway-relations',
		method,
		params,
		data,
		headers: {Urlkey},
	})
}

/**
 * 获取跑道板块列表
 * @param params
 * @returns
 */
export const GetRunwayPlate = (params: any) => {
	return defHttp.request({
		url: '/api/ledger-service/ledger-runway-manages/get-runway-plate',
		method: 'GET',
		params,
		headers: {Urlkey},
	})
}

export const DeleteRunwayLink = (params: any) => {
	return defHttp.request({
		url: '/api/ledger-service/ledger-runway-relations',
		method: 'DELETE',
		params,
		headers: {Urlkey},
	})
}
