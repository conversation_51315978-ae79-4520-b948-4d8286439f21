import {request as defHttp} from '@/api'

/**
 * 获取所有角色
 * @returns
 */
export const GetAllRoles = () => {
	return defHttp.request({
		url: '/api/platform/user/all-role-list',
		method: 'POST',
	})
}

/**
 * 检查用户UID
 * @param data
 * @returns
 */
export const CheckUID = (data: any) => {
	return defHttp.request({
		url: '/api/platform/user/user-management-check-ykz-user',
		method: 'POST',
		data,
	})
}

export const getDepat = (params: any) => {
	return defHttp.request({
		url: '/api/platform/department/user-management-department-list',
		method: 'GET',
		params,
	})
}
/**
 * 创建用户
 * @param data
 * @returns
 */
export const CreateUser = (data: any) => {
	return defHttp.request({
		url: `/api/platform/user/user-management-create-user`,
		method: 'POST',
		data,
	})
}

/**
 * 批量更新用户信息
 * @param data
 */
export const UpdateBatchUsers = (data: any) => {
	return defHttp.request({
		url: `/api/platform/user/user-management-batch-update-user-role`,
		method: 'POST',
		data,
	})
}

/**
 * 校验用户是否可移除
 * @param data
 */
export const CheckRemoveUser = (data: any) => {
	return defHttp.request({
		url: `/api/platform/user/user-management-check-remove-user`,
		method: 'POST',
		data,
	})
}

/**
 * 批量删除用户信息
 * @param data
 */
export const DeleteBatchUsers = (data: any) => {
	return defHttp.request({
		url: `/api/platform/user/user-management-batch-remove-user`,
		method: 'POST',
		data,
	})
}

/**
 * 批量启/停用户
 * @param data
 */
export const EnableBatchUser = (data: any) => {
	return defHttp.request({
		url: '/api/platform/user/user-management-batch-enable-user',
		method: 'POST',
		data,
	})
}

/**
 * 获取当前用户所在大部门的自部门列表
 */

export const getDepartmentList = () => {
	return defHttp.request({
		url: `/api/platform/departmentInternal/get-department-children`,
		method: 'GET',
	})
}

/**
 * 获取当前用户所在大部門的用戶列表
 */
export const getUserListByDepartmentId = (id: string) => {
	return defHttp.request({
		url: `/api/platform/departmentInternal/${id}/bind-users`,
		method: 'GET',
	})
}

/**
 * 注销token
 */

export const logout = () => {
	return defHttp.request({
		url: `/api/jwt-config/lapse-token`,
		method: 'POST',
	})
}
