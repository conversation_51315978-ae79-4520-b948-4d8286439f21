declare global {
	interface Window {
		GOV_CONFIG: any
	}
}

const _recheck = (key: string) => {
	const CONFIG = window.GOV_CONFIG

	const keyValue: any = {
		base: CONFIG.ALLINONE,
		login: CONFIG.LOGIN,
		ws: CONFIG.WS,
		iframeCode: CONFIG.IFRAME_CODE,
		ledger: <PERSON>NFIG.ALLINONE,
		work: CONFIG.ALLINONE,
		org: CONFIG.ALLINONE,
		workflow: CONFIG.ALLINONE,
		SR: CONFIG.SR,
		FJ: CONFIG.FJ,
		preview: CONFIG.PREVIEW,
		file: CONFIG.FILE,
		llm: CONFIG.LLM,
	}
	return keyValue[key] || keyValue.base
}
export const APIConfig = (key: string) => _recheck(key)
