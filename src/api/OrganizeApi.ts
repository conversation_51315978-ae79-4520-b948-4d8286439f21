import {request as defHttp} from '@/api'

const Urlkey = 'org'
export const getOrganize = (grade: number = 3) => {
	return defHttp.request({
		url: `/api/platform/region/all`,
		method: 'GET',
		params: {
			grade,
		},
		headers: {Urlkey},
	})
}
// 批量获取指定的部门Id集合的部门完整父级名称
export const departmentExtendUsers = (params?: any) => {
	return defHttp.request({
		url: `/api/platform/departmentInternal/department-extend-users`,
		method: 'GET',
		params,
		headers: {Urlkey},
	})
}
// 批量获取指定的部门Id集合的部门完整父级名称    // 后端----- 接口在业务表授权里面调这个
export const departmentExtendUsersToAuthDetail = (params?: any) => {
	return defHttp.request({
		url: `/api/platform/departmentInternal/department-users`,
		method: 'GET',
		params,
		headers: {Urlkey},
	})
}
/**
 * 将多个用户授权到业务表
 * @param   params
 */
export const departmentFullnames = (params: any) => {
	return defHttp.request({
		url: `/api/platform/departmentInternal/full-names`,
		method: 'POST',
		data: params,
		headers: {Urlkey},
	})
}

/**
 * 获取指定用户Id集合获取用户的姓名集合
 * @param params 业务表参数
 * @returns
 */
export const getUserNames = (params?: any) => {
	return defHttp.request({
		url: `/api/platform/user/user-names`,
		method: 'POST',
		data: params,
		headers: {Urlkey},
	})
}

/**
 * 获取后台业务表参数
 * @param params 业务表参数
 * @returns
 */
export const getApplicationConfiguration = (params?: any) => {
	return defHttp.request({
		url: `/api/platform/user/my-auth`,
		method: 'GET',
		params,
	})
}

// 消息通知
/**
 * 获取消息通知列表
 * @param params 业务表参数
 * @returns
 */
export const getMyNotifilers = (params?: any) => {
	return defHttp.request({
		url: `/api/platform/my-notifilers/list-by-type`,
		method: 'GET',
		params,
		headers: {Urlkey},
	})
}

/**
 * 获取消息通知类型
 * @returns
 */
export const getNoticeBigTypes = () => {
	return defHttp.request({
		url: '/api/platform/my-notifilers/notification-types',
		method: 'GET',
		headers: {Urlkey},
	})
}

/**
 * 获取所有消息未读数量
 * @returns
 */
export const getUnreadNoticeCount = () => {
	return defHttp.request({
		url: '/api/platform/my-notifilers/notification-Unread-count',
		method: 'GET',
		headers: {Urlkey},
	})
}
// 消息通知
/**
 * 清理消息通知
 * @param clearTime 当前时间
 * @returns
 */
export const clearNotifilers = (clearTime: any) => {
	return defHttp.request({
		url: `/api/platform/my-notifilers/clear-unread/${clearTime}`,
		method: 'DELETE',
		headers: {Urlkey},
	})
}

/**
 * 市级授权区县列表
 * @param params
 * @returns
 */
export const GetDepartmentDistrictDepartments = (params?: any) => {
	// /api/platform/department/district-departments
	return defHttp.request({
		url: `api/platform/region/all/3`,
		method: 'GET',
		params,
		headers: {Urlkey},
	})
}

/**
 * 获取用户信息
 * @param id 用户id
 * @returns
 */
export const GetUserInfoById = (id: string) => {
	return defHttp.request({
		url: `/api/platform/user/${id}/Brief`,
		method: 'GET',
		headers: {Urlkey},
	})
}

export const GetUserDepartmentById = (id: string) => {
	return defHttp.request({
		url: `/api/platform/user-department/${id}/brief`,
		method: 'GET',
		headers: {Urlkey},
	})
}

export const getNextRegions = () => {
	return defHttp.request({
		url: '/api/platform/region/regions',
		method: 'GET',
		headers: {Urlkey},
	})
}

export const GetRegion = (parentId: string) => {
	return defHttp.request({
		url: `/api/platform/region/parent/${parentId}`,
		method: 'GET',
		headers: {Urlkey},
	})
}

export const GetDepartment = (data?: any) => {
	return defHttp.request({
		url: '/api/platform/departmentInternal/department-extend-list',
		method: 'POST',
		data,
		headers: {Urlkey},
	})
}

export const GetDepartmentSimple = (data?: any) => {
	return defHttp.request({
		url: '/api/platform/departmentInternal/simple-departmentExtend-list',
		method: 'POST',
		data,
	})
}

export const GetRegionDictionary = (params?: any) => {
	return defHttp.request({
		url: '/api/platform/region/all/dictionary',
		method: 'GET',
		params,
		headers: {Urlkey: 'base'},
	})
}
