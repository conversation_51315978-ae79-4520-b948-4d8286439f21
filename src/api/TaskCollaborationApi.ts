import {request as defHttp} from '@/api'

export const GetMissionObjectives = (params: any) => {
	return defHttp.request({
		url: '/api/newFeature/mission-objectives',
		method: 'GET',
		params,
	})
}

export const GetFlowNodes = (id: string, data: any = {}) => {
	return defHttp.request({
		url: `/api/newFeature/mission-objectives-records/tree/${id}`,
		method: 'POST',
		data,
	})
}

export const CreateFlowNode = (data: any) => {
	return defHttp.request({
		url: '/api/newFeature/mission-objectives-records',
		method: 'POST',
		data,
	})
}

export const UpdateFlowNode = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/newFeature/mission-objectives-records/${id}`,
		method: 'PUT',
		data,
	})
}

export const UpdateMissionObjectives = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/newFeature/mission-objectives/${id}`,
		method: 'PUT',
		data,
	})
}

export const GetUrge = (id: string, uid: string) => {
	return defHttp.request({
		url: `/api/newFeature/mission-objectives/urging/${id}/${uid}`,
		method: 'GET',
	})
}

export const GetAutoReminders = () => {
	return defHttp.request({
		url: `/api/new-feature/auto-reminders`,
		method: 'GET',
	})
}

export const PostAutoReminders = (data: any) => {
	return defHttp.request({
		url: `/api/new-feature/auto-reminders`,
		method: 'POST',
		data,
	})
}

export const PutAutoReminders = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/new-feature/auto-reminders/${id}`,
		method: 'PUT',
		data,
	})
}

export const DeleteAutoReminders = (id: string) => {
	return defHttp.request({
		url: `/api/new-feature/auto-reminders/${id}`,
		method: 'DELETE',
	})
}

export const PostReportCollaboration = (data: any) => {
	return defHttp.request({
		url: `/api/newFeature/report-collaboration`,
		method: 'POST',
		data,
	})
}

export const PutReportCollaboration = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/newFeature/report-collaboration/${id}`,
		method: 'PUT',
		data,
	})
}

export const DeleteReportCollaboration = (id: string) => {
	return defHttp.request({
		url: `/api/newFeature/report-collaboration/${id}`,
		method: 'DELETE',
	})
}

export const PostMissionObjectivesRoles = (data: any) => {
	return defHttp.request({
		url: `/api/newFeature/mission-objectives-roles`,
		method: 'POST',
		data,
	})
}

export const PutMissionObjectivesRoles = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/newFeature/mission-objectives-roles/${id}`,
		method: 'PUT',
		data,
	})
}

export const DeleteMissionObjectivesRoles = (id: string) => {
	return defHttp.request({
		url: `/api/newFeature/mission-objectives-roles/${id}`,
		method: 'DELETE',
	})
}
