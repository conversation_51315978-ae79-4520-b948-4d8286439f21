import { request as defHttp } from '@/api'

const Urlkey = 'base'

/**
 * 保存任务仪表板配置
 * @param taskId 任务ID
 * @param data 仪表板配置数据
 * @returns
 */
export const saveDashboardConfig = (taskId: string, data: any) => {
  return defHttp.request({
    url: `/api/ledger/task-dashboard/${taskId}/config`,
    method: 'POST',
    data,
    headers: { Urlkey },
  })
}

/**
 * 更新任务仪表板配置
 * @param taskId 任务ID
 * @param data 仪表板配置数据
 * @returns
 */
export const updateDashboardConfig = (taskId: string, data: any) => {
  return defHttp.request({
    url: `/api/ledger/task-dashboard/${taskId}/config`,
    method: 'PUT',
    data,
    headers: { Urlkey },
  })
}

/**
 * 获取任务仪表板配置
 * @param taskId 任务ID
 * @returns
 */
export const getDashboardConfig = (taskId: string) => {
  return defHttp.request({
    url: `/api/ledger/task-dashboard/${taskId}/config`,
    method: 'GET',
    headers: { Urlkey },
  })
}

/**
 * 删除任务仪表板配置
 * @param taskId 任务ID
 * @returns
 */
export const deleteDashboardConfig = (taskId: string) => {
  return defHttp.request({
    url: `/api/ledger/task-dashboard/${taskId}/config`,
    method: 'DELETE',
    headers: { Urlkey },
  })
}

/**
 * 保存或更新任务仪表板配置（智能判断）
 * @param taskId 任务ID
 * @param data 仪表板配置数据
 * @returns
 */
export const saveOrUpdateDashboardConfig = async (taskId: string, data: any) => {
  try {
    // 先尝试获取现有配置
    await getDashboardConfig(taskId)
    // 如果存在，则更新
    return updateDashboardConfig(taskId, data)
  } catch (error) {
    // 如果不存在，则创建
    return saveDashboardConfig(taskId, data)
  }
}
