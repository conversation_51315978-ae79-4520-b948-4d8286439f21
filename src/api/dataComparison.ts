import {request as defHttp} from '@/api'
// 创建数据对比
export const postdataComparisons = (data: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-comparisons`,
    method: 'POST',
    data,
  })
}

// 获取数据对比列表
export const dataComparisons = (params: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-comparisons`,
    method: 'GET',
    params,
  })
}

// 获取数据对比详情
export const dataComparisonsId = (id: any) => {
  return defHttp.request({
    url: `/api/new-feature/data-comparisons/${id}`,
    method: 'GET',
    // params,
  })
}
// 更新数据对比
export const putdataComparisonsId = (id: any,data:any) => {
  return defHttp.request({
    url: `/api/new-feature/data-comparisons/${id}`,
    method: 'PUT',
    data
    // params,
  })
}
// 删除数据对比
export const deletedataComparisonsId = (id: any,) => {
  return defHttp.request({
    url: `/api/new-feature/data-comparisons/${id}`,
    method: 'DELETE',
    // data
    // params,
  })
}
// 执行数据对比
export const dataComparisonsIdExecute = (id: any,data:any) => {
  return defHttp.request({
    url: `/api/new-feature/data-comparisons/${id}/execute`,
    method: 'POST',
    data
    // params,
  })
}
// 更新差异数据量
export const dataComparisonsIddifference = (id: any,) => {
  return defHttp.request({
    url: `/api/new-feature/data-comparisons/${id}/difference-count`,
    method: 'PUT',
    // params,
  })
}
// 根据业务表获取数据对比列表
export const bybusinesstable = (tableName: any) => {
  return defHttp.request({
    url: `/api/new-feature/business-tables/by-table-name/${tableName}`,
    method: 'GET',
    // params,
  })
}
// 获取数据源选项
export const datasources = () => {
  return defHttp.request({
    url: `/api/new-feature/data-comparisons/data-sources`,
    method: 'GET',
    // params,
  })
}
// 获取数据库选项
export const databases = () => {
  return defHttp.request({
    url: `/api/new-feature/data-comparisons/databases`,
    method: 'GET',
    // params,
  })
}
// 获取比对方式选项
export const comparisonmethods = () => {
  return defHttp.request({
    url: `/api/new-feature/data-comparisons/comparison-methods`,
    method: 'GET',
    // params,
  })
}
// 获取业务表选项
export const businesstables = (params:any) => {
  return defHttp.request({
    url: `/api/new-feature/business-tables`,
    method: 'GET',
    params,
  })
}