import {request as defHttp} from '@/api'
const Urlkey = 'ledger'

export const GetCollectGroupList = (params?: any) => {
	return defHttp.request({
		url: `/api/platform/region-favorite-group`,
		method: 'GET',
		params,
		headers: {Urlkey},
	})
}

export const CreateCollectGroup = (data: any) => {
	return defHttp.request({
		url: `/api/platform/region-favorite-group`,
		method: 'POST',
		data,
		headers: {Urlkey},
	})
}

export const CreateCollectBatch = (data: any) => {
	return defHttp.request({
		url: `/api/platform/region-favorite/batch`,
		method: 'POST',
		data,
		headers: {Urlkey},
	})
}

export const GetCollectGroupListById = (params?: any) => {
	return defHttp.request({
		url: `/api/platform/region-favorite`,
		method: 'GET',
		params,
		headers: {Urlkey},
	})
}

export const DeleteCollectGroupById = (id: string) => {
	return defHttp.request({
		url: `/api/platform/region-favorite-group/${id}`,
		method: 'DELETE',
		headers: {Urlkey},
	})
}

// 获取业务表授权权限
export const GetPermissions = (LedgerId: any, DepartmentId: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerDepartment/${LedgerId}/${DepartmentId}/department-permissions`,
		method: 'GET',
	})
}

// 添加授权 =============================================================
export const GetDepartmentList = () => {
	return defHttp.request({
		url: `/api/platform/departmentInternal/get-department-children`,
		method: 'GET',
	})
}

// 本部门授权创建
export const CreateAuthorization = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerPermissionsAuthorizationMode/create-authorization-modes`,
		method: 'POST',
		data,
	})
}

// 本部门授权修改
export const UpdateAuthorization = (params: any, data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerPermissionsAuthorizationMode/update-authorization-modes`,
		method: 'POST',
		params,
		data,
	})
}

// 本部门授权批量修改
export const UpdateBatchAuthorization = (data: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerPermissionsAuthorizationMode/batch-update-authorization-modes`,
		method: 'POST',
		data,
	})
}

// 本部门授权删除
export const DeleteAuthorization = (id: string) => {
	return defHttp.request({
		url: `/api/ledger/ledgerPermissionsAuthorizationMode/delete-authorization-modes`,
		method: 'DELETE',
		params: {id},
	})
}

//本部门授权批量删除
export const DeleteBatchAuthorization = (ids: any) => {
	return defHttp.request({
		url: `/api/ledger/ledgerPermissionsAuthorizationMode/batch-delete-authorization-modes`,
		method: 'DELETE',
		params: {ids},
	})
}

export const GetBindUsers = (id: string) => {
	return defHttp.request({
		url: `/api/platform/departmentInternal/${id}/bind-users`,
		method: 'GET',
	})
}
