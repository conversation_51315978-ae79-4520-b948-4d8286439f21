import {request as defHttp} from '@/api'

/**
 * 更新任务填报记录列表
 * @returns
 */
export const RefreshTaskFillHistory = () => {
	return defHttp.request({
		url: `/api/platform/workToDoRecord/manual-pull`,
		method: 'GET',
	})
}

// 大模型相关
export const GetLLM = (url: string, params?: any, config?: any) => {
	return defHttp.request({
		url: `/llm-api/v1/chat${url}`,
		method: 'GET',
		params,
		...config,
	})
}

export const GetLLMMessage = (url: string, params?: any, config?: any) => {
	return defHttp.request({
		url: `/llm-api/v1/message${url}`,
		method: 'GET',
		params,
		...config,
	})
}

export const PostLLMMessage = (url: string, data: any, config?: any) => {
	return defHttp.request({
		url: `/llm-api/v1/message${url}`,
		method: 'POST',
		data,
		...config,
	})
}

export const DeleteLLMessage = (url: string, params?: any, config?: any) => {
	return defHttp.request({
		url: `/llm-api/v1/message${url}`,
		method: 'POST', // DELETE
		params,
		...config,
	})
}

export const GetLedgerService = (url: string, params?: any, config?: any) => {
	return defHttp.request({
		url: `/api/ledger-service${url}`,
		method: 'GET',
		params,
		...config,
	})
}
