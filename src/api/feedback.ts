import {request as defHttp} from '@/api'
const Urlkey = 'iframeCode'

/**
 * @description: 创建反馈
 * @param {*} data
 * @return {*}
 */
export const createFeedbackList = (data?: any) => {
	return defHttp.request({
		url: `/api/platform/questionFeedback`,
		method: 'POST',
		data,
		headers: {Urlkey},
	})
}

/** 编辑 */
export const updateFeedBack = (id: string, data?: any) => {
	return defHttp.request({
		url: `/api/platform/questionFeedback/${id}`,
		method: 'PUT',
		data,
		headers: {Urlkey},
	})
}
/**
 * @description: 获取反馈列表
 * @param {*} data
 * @return {*}
 */
export const getFeedback = (data?: any) => {
	return defHttp.request({
		url: `/api/platform/questionFeedback`,
		method: 'GET',
		params: {...data, CreatorId: JSON.parse(localStorage.getItem('currentUserInfo') as string).id},
		headers: {Urlkey},
	})
}
/**
 * @description: 删除反馈
 * @param {*} id
 * @return {*}
 */
export const removeFeedbackByUser = (id: string) => {
	return defHttp.request({
		url: `/api/platform/questionFeedback/user-delete${id}`,
		method: 'DELETE',
		headers: {Urlkey},
	})
}

/**
 * @description: 获取反馈详情
 * @param {*} id
 * @return {*}
 */
export const getFeedbackById = (id: string) => {
	return defHttp.request({
		url: `/api/platform/questionFeedback/${id}`,
		method: 'GET',
		headers: {Urlkey},
	})
}
