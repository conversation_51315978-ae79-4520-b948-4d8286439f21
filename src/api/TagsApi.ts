import {request as defHttp} from '@/api'

export const PostTags = (data: any) => {
	return defHttp.request({
		url: '/api/new-feature/tags',
		method: 'POST',
		data,
	})
}

export const PutTags = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/new-feature/tags/${id}`,
		method: 'PUT',
		data,
	})
}

export const DeleteTags = (id: string) => {
	return defHttp.request({
		url: `/api/new-feature/tags/${id}`,
		method: 'DELETE',
	})
}
