import {request as defHttp} from '@/api'

const Urlkey = 'base'

/**
 * 获取核心业务表数据
 * @param id STRING
 * @returns
 */
export const coreList = () => {
	return defHttp.request({
		url: `/api/ledger/sourceLedgerType/core-list`,
		method: 'GET',
		headers: {Urlkey},
	})
}

/**
 * 创建核心业务表数据
 * @param id STRING
 * @returns
 */
export const sourceLedgerType = (params: any,) => {
	return defHttp.request({
		url: `/api/ledger/sourceLedgerType`,
		method: 'POST',
    data: {
			...params,
		},
		headers: {Urlkey},
	})
}
/**
 * 编辑核心业务表数据
 * @param id STRING
 * @returns
 */
export const sourceLedgerTypeId = (id:any,params: any) => {
	return defHttp.request({
		url: `/api/ledger/sourceLedgerType/${id}`,
		method: 'PUT',
    data: {
			...params,
		},
		headers: {Urlkey},
	})
}
/**
 * 删除核心业务表
 * @param id STRING
 * @returns
 */
export const deleteSourceLedgerType = (id: any) => {
	return defHttp.request({
		url: `/api/ledger/sourceLedgerType/${id}`,
		method: 'DELETE',
		headers: {Urlkey},
	})
}
/**
 * 删除核心业务表
 * @param id STRING
 * @returns
 */
export const details = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/sourceLedgerType`,
		method: 'GET',
    params,
		headers: {Urlkey},
	})
}
/**
 * 获取核心业务类型树形结构
 * @param id STRING
 * @returns
 */
export const treeNodes = (params: any) => {
	return defHttp.request({
		url: `/api/ledger/sourceLedgerType/tree-nodes`,
		method: 'GET',
    params,
		headers: {Urlkey},
	})
}
/**
 * 更新核心业务
 * @param id 业务表id
 * @param typeId 业务表typeid
 * @returns
 */
export const updateBusinessType = (ledgerId: string, sourceLedgerTypeId: string, regionId?: string,typeId:string) => {
	return defHttp.request({
		url: `/api/ledger/sourceLedgerType/${ledgerId}/${sourceLedgerTypeId}/${regionId}/update-business-type`,
		method: 'PUT',
		data:{
			ledgerId:ledgerId,
			regionId:regionId,
			sourceLedgerTypeId:typeId,
		},
		headers: {Urlkey},
	})
}


/**
 * 获取本部门人员
 * @param id STRING
 * @returns
 */
// export const departmentUsers = (departmentId:any) => {
// 	return defHttp.request({
// 		url: `/api/platform/user/user-management-user-list`,
// 		method: 'GET',
//     params:{
// 			departmentId:departmentId,
// 			skipCount: 0,
// 			maxResultCount: 1000,
// 		},
// 		headers: {Urlkey},
// 	})
// }
export const departmentUsers = () => {
	return defHttp.request({
		url: `/api/platform/departmentInternal/department-users`,
		method: 'GET',
    // params,
		headers: {Urlkey},
	})
}

/**
 * 创建源业务表数据
 * @param id STRING
 * @returns
 */
export const createSourceLedger = (params: any,) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/create-source-ledger`,
		method: 'POST',
    data: {
			...params,
		},
		headers: {Urlkey},
	})
}

/**
 * 根据业务表id获取业务表详情
 * @param id 业务表id
 * @returns
 */
export const getSource = (id: string) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/${id}/source`,
		method: 'GET',
		headers: {Urlkey},
	})
}
/**
 * 根据ID更新业务表管理的业务表名称
 * @param id 业务表id
 * @param name 业务表名称
 * @returns
 */
export const updatePermission = (id: string, data: string) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/${id}/permission-users`,
		method: 'PUT',
		data,
		headers: {Urlkey},
	})
}

/**
 * 获取数据维护列表
 * @param id STRING
 * @returns
 */
export const sourceLedgerFillStatistics = (params:any) => {
	return defHttp.request({
		url: `/api/ledger/sourceLedgerFillStatistics`,
		method: 'GET',
    params,
		headers: {Urlkey},
	})
}
/**
 * 获取数据维护列表
 * @param id STRING
 * @returns
 */
export const ledgerDataList = (data:any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/list`,
		method: 'POST',
    data,
		headers: {Urlkey},
	})
}

/**
 * @description 批量删除业务表数据
 * @param ledgerId string
 * @param DataIds string[]
 * @returns
 */
export const deleteSource = (ledgerId: string, DataIds: string[]) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/delete-source-ledger-datas`,
		method: 'DELETE',
    data: {ledgerId, DataIds},
		headers: {Urlkey},
	})
}
/**
 * 根据业务表id创建新的业务表数据
 * @param ledgerId 业务表id
 * @param data 新增数据
 * @returns
 */
export const createSourceLedgerData = (ledgerId: string, data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${ledgerId}/create-source-ledger-data`,
		method: 'POST',
		data,
		headers: {Urlkey},
	})
}
/**
 * 保存业务表筛选、统计、计算规则
 * @param LedgerId 业务表id
 * @param DataId 数据id
 * @param data
 * @returns
 */
export const updateSourceLedgerData = (LedgerId: string, DataId: string, data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger-data/${LedgerId}/${DataId}/update-source-ledger-data`,
		method: 'POST',
		data,
		headers: {Urlkey},
	})
}

/**
 * Excel上传成功后通知后台执行导入
 * @param data
 * @returns
 */
export const sourceLedgerImport = (data: any) => {
	return defHttp.request({
		url: `/api/ledger-service/dynamic-table/source-ledger-import`,
		method: 'POST',
		data,
		headers: {Urlkey},
	})
}