import {request as defHttp} from '@/api'

export const TestPost = (data: any) => {
	return defHttp.request({
		url: '/api/acceptance/test',
		method: 'POST',
		data,
	})
}

export const TestPut = (data: any) => {
	return defHttp.request({
		url: '/api/acceptance/test',
		method: 'PUT',
		data,
	})
}

export const TestDelete = (id: string) => {
	return defHttp.request({
		url: `/api/acceptance/test/${id}`,
		method: 'DELETE',
	})
}
