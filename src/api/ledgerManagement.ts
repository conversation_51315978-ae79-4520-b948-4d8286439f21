import {request as defHttp} from '@/api'

/**
 * 新增自动化审核校验
 */
export const createAutomationCheck = (data: any) => {
	return defHttp.request({
		url: `/api/newFeature/automatedVerification`,
		method: 'POST',
		data,
	})
}
/**
 * 获取自动化审核校验列表
 * @param params
 */
export const getAutomationCheckList = (params: any) => {
	return defHttp.request({
		url: `/api/newFeature/automatedVerification`,
		params,
		method: 'GET',
	})
}
/**
 * 获取自动化审核校验详情
 * @param id
 */
export const getAutomationCheckDetail = (id: string) => {
	return defHttp.request({
		url: `/api/newFeature/automatedVerification/${id}`,
		method: 'GET',
	})
}
/**
 * 修改自动化审核校验
 * @param id
 * @param data
 */
export const updateAutomationCheck = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/newFeature/automatedVerification/${id}`,
		method: 'PUT',
		data,
	})
}

/**
 * 删除自动化审核校验
 * @param id
 */
export const deleteAutomationCheck = (id: string) => {
	return defHttp.request({
		url: `/api/newFeature/automatedVerification/${id}`,
		method: 'DELETE',
	})
}
/**
 * 批量删除自动化审核校验数据
 * @param ids
 * @returns
 */
export const batchDeleteAutomationCheck = (ids: string[]) => {
	return defHttp.request({
		url: `/api/newFeature/automatedVerification/batch-delete`,
		method: 'POST',
		data: ids,
	})
}

/**
 * 获取业务表流程列表
 */
export const getBusinessProcessList = (params: any) => {
	return defHttp.request({
		url: `/api/workflow/workflowSchemeInfo`,
		params,
		method: 'GET',
	})
}
