// axios
export interface Request {
	[key: string]: any
	login: (url: string, options: {}, config?: {}) => Promise<any>
	get: (url: string) => Promise<any>
	put: (url: string, options: {}) => Promise<any>
	post: (url: string, options: {}) => Promise<any>
	delete: (url: string) => Promise<any>
	batch: (url: string, size?: number, params?: {}, headers?: {}, method?: string) => Promise<any>
	batchData: (config: any, size?: number) => Promise<any>
	uploadFile: (config: any, params?: any) => Promise<any>
	request: (config: {
		url: string
		method: string
		params?: any
		data?: any
		headers?: {}
		responseType?: string,
		paramsSerializer?:any
	}) => Promise<any>
}
