import {request as defHttp} from '@/api'

export const SaveWorkflow = (data: any) => {
	return defHttp.request({
		url: `/api/workflow/workflowSchemeInfo`,
		method: 'POST',
		data,
	})
}

export const GetWorkflowById = (id: string) => {
	return defHttp.request({
		url: `/api/workflow/workflowSchemeInfo/${id}`,
		method: 'GET',
	})
}

export const UpdateWorkflowById = (id: string, data: any) => {
	return defHttp.request({
		url: `/api/workflow/workflowSchemeInfo/${id}`,
		method: 'PUT',
		data,
	})
}

export const GetConditionDict = (conditionName: string) => {
	return defHttp.request({
		url: `/api/workflow/workflowSchemeInfo/${conditionName}/condition/dictionary`,
		method: 'GET',
	})
}

export const GetWorkflowByCode = (code: string) => {
	return defHttp.request({
		url: `/api/workflow/workflowSchemeInfo/by-code/${code}`,
		method: 'GET',
	})
}

export const EnableWorkflowById = (schemeInfoId: string, params: any) => {
	return defHttp.request({
		url: `/api/workflow/workflowSchemeInfo/${schemeInfoId}/chang-status`,
		method: 'POST',
		params,
	})
}

export const DeleteWorkflowByIds = (ids: string) => {
	return defHttp.request({
		url: `/api/workflow/workflowSchemeInfo`,
		method: 'DELETE',
		params: {ids},
	})
}

/**
 * 流程待办任务列表
 * @param data
 * @returns
 */
export const GetWorkflowMyUnCompleted = () => {
	return defHttp.request({
		url: `/api/workflow/workflowTask/my-unCompleted`,
		method: 'GET',
	})
}
/**
 *  任务待办表格查询
 * @param data
 * @returns
 */
export const ledgerAuditedData = (batchId: any, params: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledgerAuditedData/${batchId}/audit-data-list`,
		method: 'GET',
		params,
	})
}
/**
 *  表头
 * @param data
 * @returns
 */
export const ledgerTable = (ledgerId: any) => {
	return defHttp.request({
		url: `/api/ledger-service/ledger/${ledgerId}`,
		method: 'GET',
		// params,
	})
}
/**
 * 任务待办流程树
 * @param data
 * @returns
 */
export const workflowTaskLog = (id: string) => {
	return defHttp.request({
		url: `/api/workflow/workflowTaskLog/${id}`,
		method: 'GET',
		// data,
	})
}

/**
 * 流程审核通过/驳回
 * @param taskId
 * @param data
 * @returns
 */
export const PushWorkflowProcessAudit = (taskId: string, data: any) => {
	return defHttp.request({
		url: `/api/workflow/workflowProcess/audit/${taskId}`,
		method: 'POST',
		data,
	})
}

/**
 * 根据 code 获取审核人信息
 * @param code
 * @returns
 */
export const GetReportLogList = (code: string) => {
	return defHttp.request({
		url: `/api/workflow/workflowSchemeInfo/ReportTaskDataAudit/${code}/first-batch/auditors`,
		method: 'GET',
	})
}

/**
 * 获取导出数据审核详情
 * @param id
 * @returns
 */
export const GetExportRecordDetail = (id: string) => {
	return defHttp.request({
		url: `/api/ledger-service/table-data-export-record/${id}/and-ledger`,
		method: 'GET',
	})
}

/**
 * 获取导出数据审核记录进度
 * @param id
 * @returns
 */
export const GetExportRecordProcess = (id: string) => {
	return defHttp.request({
		url: `/api/workflow/workflowProcess/${id}/greed-node-auditors/turn`,
		method: 'GET',
	})
}
