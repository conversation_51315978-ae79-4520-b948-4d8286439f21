#!/usr/bin/env node

/**
 * 批量修改功能测试脚本
 * 运行单元测试和端到端测试
 */

import { execSync } from 'child_process'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

console.log('🚀 开始运行批量修改功能测试...\n')

// 项目根目录
const projectRoot = path.resolve(__dirname, '..')

try {
  console.log('🌐 运行端到端测试...')
  console.log('=' .repeat(50))

  // 运行端到端测试
  execSync('npm run test:e2e -- tests/e2e/primary-key-batch-edit.spec.ts', {
    cwd: projectRoot,
    stdio: 'inherit'
  })

  console.log('\n✅ 端到端测试通过!\n')

  console.log('🎉 所有测试都通过了!')
  console.log('批量修改功能已经准备就绪，可以安全部署。')
  
} catch (error) {
  console.error('\n❌ 测试失败:')
  console.error(error.message)
  
  console.log('\n🔧 故障排除建议:')
  console.log('1. 确保所有依赖已安装: npm install')
  console.log('2. 确保开发服务器正在运行: npm run dev')
  console.log('3. 检查测试环境配置是否正确')
  console.log('4. 查看具体的错误信息并修复代码')

  process.exit(1)
}

console.log('\n🏁 测试完成!')
