// 生成测试用的Excel文件
import * as ExcelJS from 'exceljs'
import { saveAs } from 'file-saver'
import fs from 'fs'
import path from 'path'

async function generateTestExcel() {
  try {
    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('字段类型数据')

    // 设置表头
    const headers = [
      { header: '字段名称', key: 'name', width: 30 },
      { header: '字段类型', key: 'type', width: 15 },
      { header: '最小长度', key: 'minLength', width: 12 },
      { header: '最大长度', key: 'maxLength', width: 12 },
      { header: '正则表达式', key: 'regex', width: 20 }
    ]
    
    worksheet.columns = headers

    // 设置表头样式
    worksheet.getRow(1).font = { bold: true }
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    }

    // 添加测试数据
    const testData = [
      {
        name: '测试字段1',
        type: '字符型',
        minLength: 1,
        maxLength: 50,
        regex: ''
      },
      {
        name: '测试字段2',
        type: '数值型',
        minLength: 1,
        maxLength: 10,
        regex: '^[0-9]+$'
      },
      {
        name: '测试字段3',
        type: 'image类型',
        minLength: 1,
        maxLength: 1,
        regex: ''
      },
      {
        name: '测试字段4',
        type: '日期型',
        minLength: 10,
        maxLength: 10,
        regex: ''
      },
      {
        name: '测试字段5',
        type: '字符型',
        minLength: 5,
        maxLength: 100,
        regex: '^[a-zA-Z0-9]+$'
      }
    ]

    // 添加数据到工作表
    testData.forEach(data => {
      worksheet.addRow(data)
    })

    // 生成文件
    const buffer = await workbook.xlsx.writeBuffer()
    
    // 确保下载目录存在
    const downloadDir = path.join(process.cwd(), 'downloads')
    if (!fs.existsSync(downloadDir)) {
      fs.mkdirSync(downloadDir, { recursive: true })
    }
    
    // 保存文件
    const fileName = `字段类型数据_${new Date().toISOString().slice(0, 10)}.xlsx`
    const filePath = path.join(downloadDir, fileName)
    
    fs.writeFileSync(filePath, buffer)
    
    console.log(`测试Excel文件已生成: ${filePath}`)
    return filePath
  } catch (error) {
    console.error('生成测试Excel文件失败:', error)
    throw error
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  generateTestExcel()
}

export { generateTestExcel }
