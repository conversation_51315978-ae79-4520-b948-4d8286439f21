// 生成测试用的Excel文件
import ExcelJS from 'exceljs'
import fs from 'fs'
import path from 'path'

async function createTestExcel() {
  try {
    // 创建工作簿
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('字段类型数据')

    // 设置表头
    worksheet.columns = [
      { header: '字段名称', key: 'name', width: 30 },
      { header: '字段类型', key: 'type', width: 15 },
      { header: '最小长度', key: 'minLength', width: 12 },
      { header: '最大长度', key: 'maxLength', width: 12 },
      { header: '正则表达式', key: 'regex', width: 20 }
    ]

    // 设置表头样式
    worksheet.getRow(1).font = { bold: true }
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    }

    // 添加测试数据
    const testData = [
      {
        name: '测试用户名字段',
        type: '字符型',
        minLength: 2,
        maxLength: 50,
        regex: ''
      },
      {
        name: '测试年龄字段',
        type: '数值型',
        minLength: 1,
        maxLength: 3,
        regex: '^[0-9]+$'
      },
      {
        name: '测试头像字段',
        type: 'image类型',
        minLength: 1,
        maxLength: 1,
        regex: ''
      },
      {
        name: '测试生日字段',
        type: '日期型',
        minLength: 10,
        maxLength: 10,
        regex: ''
      },
      {
        name: '测试邮箱字段',
        type: '字符型',
        minLength: 5,
        maxLength: 100,
        regex: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'
      }
    ]

    // 添加数据到工作表
    testData.forEach(data => {
      worksheet.addRow(data)
    })

    // 确保下载目录存在
    const downloadDir = path.join(process.cwd(), 'downloads')
    if (!fs.existsSync(downloadDir)) {
      fs.mkdirSync(downloadDir, { recursive: true })
    }
    
    // 生成文件
    const fileName = `字段类型数据_${new Date().toISOString().slice(0, 10)}.xlsx`
    const filePath = path.join(downloadDir, fileName)
    
    await workbook.xlsx.writeFile(filePath)
    
    console.log(`✅ 测试Excel文件已生成: ${filePath}`)
    console.log(`📁 文件包含 ${testData.length} 条测试数据`)
    
    return filePath
  } catch (error) {
    console.error('❌ 生成测试Excel文件失败:', error)
    throw error
  }
}

// 运行生成函数
createTestExcel().catch(console.error)
