---
type: "manual"
---

这是一个前端原型演示功能开发任务，具体要求如下：

**功能特性：**

-   这是纯前端演示功能，无需集成真实的后端 API 接口
-   所有用户交互产生的数据变更都必须持久化存储到浏览器本地存储（localStorage/sessionStorage）
-   页面刷新后数据应保持不丢失，确保演示的连续性和完整性

**数据处理：**

-   使用本地模拟数据（mock data）进行功能测试和演示
-   实现完整的 CRUD 操作逻辑，但数据操作仅限于本地存储
-   确保数据状态在不同页面间的一致性

**测试要求：**

-   功能开发完成后，必须编写 Playwright 自动化测试脚本
-   测试脚本需要覆盖所有主要功能路径和用户交互场景
-   执行完整的端到端功能测试，确保所有功能正常工作
-   测试应验证数据持久化功能是否正确实现

**交付标准：**

-   完整可运行的前端功能代码
-   配套的 Playwright 测试脚本
-   测试执行报告，确认所有测试用例通过
-   所有的分页查询都在加上默认的Loading 这样可以增加页面查询真实性


在执行需求任务之前需要先帮我创建详细的任务计划，如果在执行任务中遇到了不确定的业务你需要调用反馈 mcp 和我交流 
