// build-profile.mjs
import {spawn} from 'child_process'
import {fileURLToPath} from 'url'
import path from 'path'

const __dirname = path.dirname(fileURLToPath(import.meta.url))
const vitePath = path.resolve(__dirname, 'node_modules/vite/bin/vite.js')

// 启动 Vite build 子进程，开启 Node Inspector 和内存限制
const child = spawn(
	process.execPath,
	[
		'--inspect-brk=9229', // 开启调试器（Chrome 可连接）
		'--max-old-space-size=8192', // 设定最大内存为 4GB
		vitePath,
		'build',
	],
	{
		stdio: 'inherit',
		env: {
			...process.env,
			NODE_ENV: 'production',
		},
	}
)

child.on('exit', (code) => {
	console.log(`Vite build process exited with code ${code}`)
})
