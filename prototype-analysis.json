{"prototypes": [{"id": "task-detail-page", "name": "任务目标拆解详情页面", "type": "page", "description": "任务详情主页面，展示任务基本信息和子任务列表", "layout": {"header": {"title": "任务目标拆解", "backButton": {"text": "返回", "position": "left", "action": "handleBack"}}, "body": {"sections": [{"name": "taskInfoCard", "title": "任务基本信息", "type": "info-card", "fields": [{"label": "任务名称", "field": "taskName", "width": "140px"}, {"label": "任务类型", "field": "taskType", "width": "140px"}, {"label": "创建部门/创建责任人", "field": "createDepartment", "width": "140px"}, {"label": "开始时间", "field": "startTime", "width": "140px"}, {"label": "业务报表子任务数量", "field": "businessTaskCount", "width": "140px"}, {"label": "临时报表子任务数量", "field": "tempTaskCount", "width": "140px"}]}, {"name": "subTaskCard", "title": "子任务列表", "type": "table-card", "actions": [{"text": "进度配置", "type": "primary", "action": "handleProgressConfig"}, {"text": "导出", "type": "default", "action": "handleExport"}, {"text": "子任务关系展示", "type": "default", "action": "handleStyleTemplate"}, {"text": "更多操作", "type": "dropdown", "items": [{"text": "难度分析", "action": "handleDifficultyAnalysis"}, {"text": "时长分析", "action": "handleTimeAnalysis"}, {"text": "提醒历史", "action": "handleReminderHistory"}, {"text": "编辑", "action": "handleEditTask"}]}], "table": {"height": "500px", "autoHeight": true, "checkbox": true, "columns": [{"prop": "taskName", "label": "子任务名称", "minWidth": 150}, {"prop": "taskType", "label": "子任务类型", "width": 120}, {"prop": "taskCategory", "label": "子任务分类", "width": 120}, {"prop": "<PERSON><PERSON><PERSON>", "label": "责任人", "width": 100}, {"prop": "participants", "label": "参与人", "width": 150}, {"prop": "taskStatus", "label": "任务状态", "width": 100, "slot": true}, {"prop": "progress", "label": "任务进度", "width": 120, "slot": true}], "buttons": [{"text": "编辑", "type": "primary", "code": "edit"}, {"text": "删除", "type": "danger", "code": "delete"}, {"text": "复制", "type": "default", "code": "copy"}, {"text": "更多", "type": "default", "code": "more"}]}}]}}, "styles": {"labelWidth": "140px", "labelPosition": "right", "cardPadding": "20px", "cardMargin": "16px 0"}, "interactions": {"loadData": "loadTaskDetail", "backAction": "handleBack", "batchDelete": "handleBatchDelete"}}, {"id": "progress-config-dialog", "name": "进度配置弹窗", "type": "dialog", "description": "配置任务进度计算方式和样式模板", "dialog": {"title": "进度配置", "width": "800px", "visibleCloseButton": false, "visibleConfirmButton": false, "closeOnClickModal": false}, "layout": {"body": {"sections": [{"name": "progressCalculation", "type": "switch-section", "label": "任务进度完成百分比计算", "field": "enableProgressCalculation", "defaultValue": true}, {"name": "styleTemplateSection", "type": "template-section", "title": "进度条样式列表", "actions": [{"text": "样式模板设置", "type": "primary", "action": "openStyleTemplateDialog"}, {"text": "样式模板导入", "type": "default", "action": "openStyleImportDialog"}], "search": {"placeholder": "请输入模板名称", "field": "templateName", "resetButton": true}, "table": {"height": "300px", "columns": [{"prop": "templateName", "label": "进度条样式模板名称", "minWidth": 200}, {"prop": "inProgressColor", "label": "执行中配色", "width": 150, "slot": true}, {"prop": "completedColor", "label": "已完成配色", "width": 150, "slot": true}], "data": [{"templateName": "默认样式", "inProgressColor": "#409EFF", "completedColor": "#67C23A"}, {"templateName": "深色样式", "inProgressColor": "#409EFF", "completedColor": "#67C23A"}, {"templateName": "浅色样式", "inProgressColor": "#87CEEB", "completedColor": "#90EE90"}]}}]}, "footer": {"buttons": [{"text": "取消", "type": "default", "action": "handleCancel"}, {"text": "确认", "type": "primary", "action": "handleConfirm"}]}}}, {"id": "style-template-dialog", "name": "样式模板设置弹窗", "type": "dialog", "description": "设置样式模板的名称和颜色配置", "dialog": {"title": "样式模板设置", "width": "500px", "visibleCloseButton": false, "visibleConfirmButton": false, "closeOnClickModal": false}, "layout": {"body": {"form": {"labelWidth": "140px", "labelPosition": "right", "fields": [{"label": "样式模板名称", "field": "templateName", "type": "input", "placeholder": "请输入名称", "required": true}, {"label": "执行中配色", "field": "inProgressColor", "type": "input", "placeholder": "请输入颜色的十六进制值", "required": true}, {"label": "已完成配色", "field": "completedColor", "type": "input", "placeholder": "请输入颜色的十六进制值", "required": true}]}}, "footer": {"buttons": [{"text": "取消", "type": "default", "action": "handleCancel"}, {"text": "上传", "type": "primary", "action": "handleSave"}]}}}, {"id": "style-import-dialog", "name": "样式模板导入弹窗", "type": "dialog", "description": "文件上传界面，支持拖拽上传", "dialog": {"title": "样式模板导入", "width": "500px", "visibleCloseButton": false, "visibleConfirmButton": false, "closeOnClickModal": false}, "layout": {"body": {"uploadArea": {"type": "drag-upload", "style": {"border": "2px dashed #d9d9d9", "borderRadius": "6px", "padding": "40px", "textAlign": "center", "backgroundColor": "#fafafa"}, "icon": {"type": "cloud-upload", "color": "#409EFF", "size": "48px"}, "text": "点击或将文件拖拽到这里上传", "accept": ".xlsx,.xls,.csv"}}, "footer": {"buttons": [{"text": "取消", "type": "default", "action": "handleCancel"}, {"text": "上传", "type": "primary", "action": "handleUpload"}]}}}, {"id": "difficulty-analysis-dialog", "name": "难度分析弹窗", "type": "dialog", "description": "显示任务难度的多维度分析结果", "dialog": {"title": "难度分析", "width": "600px", "visibleCloseButton": false, "visibleConfirmButton": false, "closeOnClickModal": false}, "layout": {"body": {"analysisItems": [{"label": "时间压力", "content": "可以天数10天", "difficulty": "低", "difficultyColor": "#67C23A"}, {"label": "填报内容", "content": "需填报字段30项", "difficulty": "中等", "difficultyColor": "#E6A23C"}, {"label": "步骤复杂度", "content": "不包含审核流程", "difficulty": "低", "difficultyColor": "#67C23A"}], "summary": {"text": "综合分析本次任务难度为：低。", "style": {"marginTop": "20px", "fontWeight": "bold"}}}, "footer": {"buttons": [{"text": "取消", "type": "default", "action": "handleCancel"}, {"text": "确认", "type": "primary", "action": "handleConfirm"}]}}}, {"id": "time-analysis-dialog", "name": "时长分析弹窗", "type": "dialog", "description": "显示任务各环节的耗时分析", "dialog": {"title": "时长分析", "width": "500px", "visibleCloseButton": false, "visibleConfirmButton": false, "closeOnClickModal": false}, "layout": {"body": {"table": {"height": "auto", "columns": [{"prop": "stageName", "label": "环节名称", "minWidth": 150}, {"prop": "duration", "label": "耗时", "width": 100}], "data": [{"stageName": "任务接收", "duration": "2天"}, {"stageName": "任务填报", "duration": "5天"}]}}, "footer": {"buttons": [{"text": "取消", "type": "default", "action": "handleCancel"}, {"text": "确认", "type": "primary", "action": "handleConfirm"}]}}}, {"id": "reminder-history-dialog", "name": "提醒历史弹窗", "type": "dialog", "description": "显示任务提醒的历史记录", "dialog": {"title": "任务提醒历史", "width": "700px", "visibleCloseButton": false, "visibleConfirmButton": false, "closeOnClickModal": false}, "layout": {"body": {"table": {"height": "auto", "columns": [{"prop": "reminderTime", "label": "提醒时间", "width": 150}, {"prop": "<PERSON><PERSON><PERSON>", "label": "提醒人", "minWidth": 200}], "data": [{"reminderTime": "2025-04-23", "reminderPerson": "朱沅镇-公共服务-王五、朱沅镇-公共服务-李四"}, {"reminderTime": "2025-05-23", "reminderPerson": "朱沅镇-公共服务-王五、朱沅镇-公共服务-李四"}, {"reminderTime": "2025-06-23", "reminderPerson": "朱沅镇-公共服务-王五、朱沅镇-公共服务-李四"}]}}, "footer": {"buttons": [{"text": "取消", "type": "default", "action": "handleCancel"}, {"text": "确认", "type": "primary", "action": "handleConfirm"}]}}}, {"id": "style-template-table", "name": "样式模板数据表格", "type": "data-table", "description": "显示进度条样式模板的配置数据", "layout": {"table": {"columns": [{"prop": "templateName", "label": "进度条样式模板名称", "minWidth": 200}, {"prop": "inProgressColor", "label": "执行中配色", "width": 150}, {"prop": "completedColor", "label": "已完成配色", "width": 150}], "data": [{"templateName": "样式模板1", "inProgressColor": "#155BBC", "completedColor": "#156BBC"}, {"templateName": "样式模板2", "inProgressColor": "#156BBC", "completedColor": "#157BBC"}, {"templateName": "样式模板3", "inProgressColor": "#157BBC", "completedColor": "#158BBC"}, {"templateName": "样式模板4", "inProgressColor": "#158BBC", "completedColor": "#159BBC"}, {"templateName": "样式模板5", "inProgressColor": "#159BBC", "completedColor": "#160BBC"}, {"templateName": "样式模板6", "inProgressColor": "#160BBC", "completedColor": "#161BBC"}, {"templateName": "样式模板7", "inProgressColor": "#161BBC", "completedColor": "#162BBC"}]}}, "features": ["支持导出子任务列表数据", "子任务关系展示"]}], "designSpecs": {"colors": {"primary": "#409EFF", "success": "#67C23A", "warning": "#E6A23C", "danger": "#F56C6C", "info": "#909399"}, "spacing": {"cardPadding": "20px", "cardMargin": "16px 0", "formItemMargin": "22px 0"}, "typography": {"labelWidth": "140px", "labelPosition": "right", "fontSize": "14px"}, "components": {"dialog": {"visibleCloseButton": false, "visibleConfirmButton": false, "closeOnClickModal": false}, "table": {"defaultHeight": "500px", "autoHeight": true}}}}