<!DOCTYPE html>
<html lang="zh">
	<head>
		<meta charset="utf-8" />
		<title>iconfont Demo</title>
		<link
			rel="shortcut icon"
			href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"
			type="image/x-icon"
		/>
		<link
			rel="icon"
			type="image/svg+xml"
			href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"
		/>
		<link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css" />
		<link rel="stylesheet" href="demo.css" />
		<link rel="stylesheet" href="iconfont.css" />
		<script src="iconfont.js"></script>
		<!-- jQuery -->
		<script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
		<!-- 代码高亮 -->
		<script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
		<style>
			.main .logo {
				margin-top: 0;
				height: auto;
			}

			.main .logo a {
				display: flex;
				align-items: center;
			}

			.main .logo .sub-title {
				margin-left: 0.5em;
				font-size: 22px;
				color: #fff;
				background: linear-gradient(-45deg, #3967ff, #b500fe);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
			}
		</style>
	</head>
	<body>
		<div class="main">
			<h1 class="logo">
				<a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
					<img
						alt=""
						width="200"
						src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg"
					/>
				</a>
			</h1>
			<div class="nav-tabs">
				<ul id="tabs" class="dib-box">
					<li class="dib active"><span>Unicode</span></li>
					<li class="dib"><span>Font class</span></li>
					<li class="dib"><span>Symbol</span></li>
				</ul>

				<a
					href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4030052"
					target="_blank"
					class="nav-more"
					>查看项目</a
				>
			</div>
			<div class="tab-container">
				<div class="content unicode" style="display: block">
					<ul class="icon_lists dib-box">
						<li class="dib">
							<span class="icon iconfont">&#xe7c5;</span>
							<div class="name">业务表管理_1</div>
							<div class="code-name">&amp;#xe7c5;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe6a9;</span>
							<div class="name">标签</div>
							<div class="code-name">&amp;#xe6a9;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xea72;</span>
							<div class="name">组织管理</div>
							<div class="code-name">&amp;#xea72;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe69a;</span>
							<div class="name">房屋</div>
							<div class="code-name">&amp;#xe69a;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe67f;</span>
							<div class="name">开发：集体土地征收</div>
							<div class="code-name">&amp;#xe67f;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe632;</span>
							<div class="name">综合查询</div>
							<div class="code-name">&amp;#xe632;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe623;</span>
							<div class="name">工作人员</div>
							<div class="code-name">&amp;#xe623;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe657;</span>
							<div class="name">人口管理</div>
							<div class="code-name">&amp;#xe657;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe606;</span>
							<div class="name">首页</div>
							<div class="code-name">&amp;#xe606;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe605;</span>
							<div class="name">摄像头</div>
							<div class="code-name">&amp;#xe605;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe637;</span>
							<div class="name">我的待办</div>
							<div class="code-name">&amp;#xe637;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe66a;</span>
							<div class="name">更多</div>
							<div class="code-name">&amp;#xe66a;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe664;</span>
							<div class="name">w_村庄</div>
							<div class="code-name">&amp;#xe664;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe682;</span>
							<div class="name">房屋建筑物构建2</div>
							<div class="code-name">&amp;#xe682;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe612;</span>
							<div class="name">报表</div>
							<div class="code-name">&amp;#xe612;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xfabf;</span>
							<div class="name">组织</div>
							<div class="code-name">&amp;#xfabf;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe611;</span>
							<div class="name">退出</div>
							<div class="code-name">&amp;#xe611;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe61b;</span>
							<div class="name">icon／社区</div>
							<div class="code-name">&amp;#xe61b;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe627;</span>
							<div class="name">设备管理</div>
							<div class="code-name">&amp;#xe627;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe604;</span>
							<div class="name">icon／城市体征／产业园区数</div>
							<div class="code-name">&amp;#xe604;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe609;</span>
							<div class="name">icon／法人</div>
							<div class="code-name">&amp;#xe609;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe60f;</span>
							<div class="name">icon／监控</div>
							<div class="code-name">&amp;#xe60f;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe61c;</span>
							<div class="name">icon／实有人口</div>
							<div class="code-name">&amp;#xe61c;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe622;</span>
							<div class="name">icon／违建面积</div>
							<div class="code-name">&amp;#xe622;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe629;</span>
							<div class="name">icon／医院</div>
							<div class="code-name">&amp;#xe629;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe62a;</span>
							<div class="name">icon／疫苗接种</div>
							<div class="code-name">&amp;#xe62a;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe65f;</span>
							<div class="name">向右箭头</div>
							<div class="code-name">&amp;#xe65f;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe660;</span>
							<div class="name">向左箭头</div>
							<div class="code-name">&amp;#xe660;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe61e;</span>
							<div class="name">组件</div>
							<div class="code-name">&amp;#xe61e;</div>
						</li>

						<li class="dib">
							<span class="icon iconfont">&#xe636;</span>
							<div class="name">报表</div>
							<div class="code-name">&amp;#xe636;</div>
						</li>
					</ul>
					<div class="article markdown">
						<h2 id="unicode-">Unicode 引用</h2>
						<hr />

						<p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
						<ul>
							<li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
							<li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
						</ul>
						<blockquote>
							<p>
								注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol
								引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）
							</p>
						</blockquote>
						<p>Unicode 使用步骤如下：</p>
						<h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
						<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1685711178885') format('woff2'),
       url('iconfont.woff?t=1685711178885') format('woff'),
       url('iconfont.ttf?t=1685711178885') format('truetype');
}
</code></pre>
						<h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
						<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
						<h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
						<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
						<blockquote>
							<p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
						</blockquote>
					</div>
				</div>
				<div class="content font-class">
					<ul class="icon_lists dib-box">
						<li class="dib">
							<span class="icon iconfont icon-taizhangguanli_1"></span>
							<div class="name">业务表管理_1</div>
							<div class="code-name">.icon-taizhangguanli_1</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-biaoqian"></span>
							<div class="name">标签</div>
							<div class="code-name">.icon-biaoqian</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-zuzhiguanli"></span>
							<div class="name">组织管理</div>
							<div class="code-name">.icon-zuzhiguanli</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-fangwu"></span>
							<div class="name">房屋</div>
							<div class="code-name">.icon-fangwu</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-kaifajititudizhengshou"></span>
							<div class="name">开发：集体土地征收</div>
							<div class="code-name">.icon-kaifajititudizhengshou</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-zonghechaxun"></span>
							<div class="name">综合查询</div>
							<div class="code-name">.icon-zonghechaxun</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-dagongren"></span>
							<div class="name">工作人员</div>
							<div class="code-name">.icon-dagongren</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-menu_xqrkgl"></span>
							<div class="name">人口管理</div>
							<div class="code-name">.icon-menu_xqrkgl</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-shouye"></span>
							<div class="name">首页</div>
							<div class="code-name">.icon-shouye</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-shexiangtou"></span>
							<div class="name">摄像头</div>
							<div class="code-name">.icon-shexiangtou</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-wodedaibanx"></span>
							<div class="name">我的待办</div>
							<div class="code-name">.icon-wodedaibanx</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-gengduo"></span>
							<div class="name">更多</div>
							<div class="code-name">.icon-gengduo</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-w_cunzhuang"></span>
							<div class="name">w_村庄</div>
							<div class="code-name">.icon-w_cunzhuang</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-fangwujianzhuwugoujian2"></span>
							<div class="name">房屋建筑物构建2</div>
							<div class="code-name">.icon-fangwujianzhuwugoujian2</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-baobiao1"></span>
							<div class="name">报表</div>
							<div class="code-name">.icon-baobiao1</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-zuzhi"></span>
							<div class="name">组织</div>
							<div class="code-name">.icon-zuzhi</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-tuichu"></span>
							<div class="name">退出</div>
							<div class="code-name">.icon-tuichu</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-a-iconshequ"></span>
							<div class="name">icon／社区</div>
							<div class="code-name">.icon-a-iconshequ</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-a-ziyuan10"></span>
							<div class="name">设备管理</div>
							<div class="code-name">.icon-a-ziyuan10</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-a-iconchengshitizhengchanyeyuanqushu"></span>
							<div class="name">icon／城市体征／产业园区数</div>
							<div class="code-name">.icon-a-iconchengshitizhengchanyeyuanqushu</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-a-iconfaren"></span>
							<div class="name">icon／法人</div>
							<div class="code-name">.icon-a-iconfaren</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-a-iconjiankong"></span>
							<div class="name">icon／监控</div>
							<div class="code-name">.icon-a-iconjiankong</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-a-iconshiyourenkou"></span>
							<div class="name">icon／实有人口</div>
							<div class="code-name">.icon-a-iconshiyourenkou</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-a-iconweijianmianji"></span>
							<div class="name">icon／违建面积</div>
							<div class="code-name">.icon-a-iconweijianmianji</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-a-iconyiyuan"></span>
							<div class="name">icon／医院</div>
							<div class="code-name">.icon-a-iconyiyuan</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-a-iconyimiaojiezhong"></span>
							<div class="name">icon／疫苗接种</div>
							<div class="code-name">.icon-a-iconyimiaojiezhong</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-arraw-right"></span>
							<div class="name">向右箭头</div>
							<div class="code-name">.icon-arraw-right</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-arraw-left"></span>
							<div class="name">向左箭头</div>
							<div class="code-name">.icon-arraw-left</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-zujian"></span>
							<div class="name">组件</div>
							<div class="code-name">.icon-zujian</div>
						</li>

						<li class="dib">
							<span class="icon iconfont icon-baobiao"></span>
							<div class="name">报表</div>
							<div class="code-name">.icon-baobiao</div>
						</li>
					</ul>
					<div class="article markdown">
						<h2 id="font-class-">font-class 引用</h2>
						<hr />

						<p>
							font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode
							书写不直观，语意不明确的问题。
						</p>
						<p>与 Unicode 使用方式相比，具有如下特点：</p>
						<ul>
							<li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
							<li>
								因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode
								引用。
							</li>
						</ul>
						<p>使用步骤如下：</p>
						<h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
						<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
						<h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
						<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
						<blockquote>
							<p>" iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
						</blockquote>
					</div>
				</div>
				<div class="content symbol">
					<ul class="icon_lists dib-box">
						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-taizhangguanli_1"></use>
							</svg>
							<div class="name">业务表管理_1</div>
							<div class="code-name">#icon-taizhangguanli_1</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-biaoqian"></use>
							</svg>
							<div class="name">标签</div>
							<div class="code-name">#icon-biaoqian</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-zuzhiguanli"></use>
							</svg>
							<div class="name">组织管理</div>
							<div class="code-name">#icon-zuzhiguanli</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-fangwu"></use>
							</svg>
							<div class="name">房屋</div>
							<div class="code-name">#icon-fangwu</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-kaifajititudizhengshou"></use>
							</svg>
							<div class="name">开发：集体土地征收</div>
							<div class="code-name">#icon-kaifajititudizhengshou</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-zonghechaxun"></use>
							</svg>
							<div class="name">综合查询</div>
							<div class="code-name">#icon-zonghechaxun</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-dagongren"></use>
							</svg>
							<div class="name">工作人员</div>
							<div class="code-name">#icon-dagongren</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-menu_xqrkgl"></use>
							</svg>
							<div class="name">人口管理</div>
							<div class="code-name">#icon-menu_xqrkgl</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-shouye"></use>
							</svg>
							<div class="name">首页</div>
							<div class="code-name">#icon-shouye</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-shexiangtou"></use>
							</svg>
							<div class="name">摄像头</div>
							<div class="code-name">#icon-shexiangtou</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-wodedaibanx"></use>
							</svg>
							<div class="name">我的待办</div>
							<div class="code-name">#icon-wodedaibanx</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-gengduo"></use>
							</svg>
							<div class="name">更多</div>
							<div class="code-name">#icon-gengduo</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-w_cunzhuang"></use>
							</svg>
							<div class="name">w_村庄</div>
							<div class="code-name">#icon-w_cunzhuang</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-fangwujianzhuwugoujian2"></use>
							</svg>
							<div class="name">房屋建筑物构建2</div>
							<div class="code-name">#icon-fangwujianzhuwugoujian2</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-baobiao1"></use>
							</svg>
							<div class="name">报表</div>
							<div class="code-name">#icon-baobiao1</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-zuzhi"></use>
							</svg>
							<div class="name">组织</div>
							<div class="code-name">#icon-zuzhi</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-tuichu"></use>
							</svg>
							<div class="name">退出</div>
							<div class="code-name">#icon-tuichu</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-a-iconshequ"></use>
							</svg>
							<div class="name">icon／社区</div>
							<div class="code-name">#icon-a-iconshequ</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-a-ziyuan10"></use>
							</svg>
							<div class="name">设备管理</div>
							<div class="code-name">#icon-a-ziyuan10</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-a-iconchengshitizhengchanyeyuanqushu"></use>
							</svg>
							<div class="name">icon／城市体征／产业园区数</div>
							<div class="code-name">#icon-a-iconchengshitizhengchanyeyuanqushu</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-a-iconfaren"></use>
							</svg>
							<div class="name">icon／法人</div>
							<div class="code-name">#icon-a-iconfaren</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-a-iconjiankong"></use>
							</svg>
							<div class="name">icon／监控</div>
							<div class="code-name">#icon-a-iconjiankong</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-a-iconshiyourenkou"></use>
							</svg>
							<div class="name">icon／实有人口</div>
							<div class="code-name">#icon-a-iconshiyourenkou</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-a-iconweijianmianji"></use>
							</svg>
							<div class="name">icon／违建面积</div>
							<div class="code-name">#icon-a-iconweijianmianji</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-a-iconyiyuan"></use>
							</svg>
							<div class="name">icon／医院</div>
							<div class="code-name">#icon-a-iconyiyuan</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-a-iconyimiaojiezhong"></use>
							</svg>
							<div class="name">icon／疫苗接种</div>
							<div class="code-name">#icon-a-iconyimiaojiezhong</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-arraw-right"></use>
							</svg>
							<div class="name">向右箭头</div>
							<div class="code-name">#icon-arraw-right</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-arraw-left"></use>
							</svg>
							<div class="name">向左箭头</div>
							<div class="code-name">#icon-arraw-left</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-zujian"></use>
							</svg>
							<div class="name">组件</div>
							<div class="code-name">#icon-zujian</div>
						</li>

						<li class="dib">
							<svg class="icon svg-icon" aria-hidden="true">
								<use xlink:href="#icon-baobiao"></use>
							</svg>
							<div class="name">报表</div>
							<div class="code-name">#icon-baobiao</div>
						</li>
					</ul>
					<div class="article markdown">
						<h2 id="symbol-">Symbol 引用</h2>
						<hr />

						<p>
							这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a
								href=""
								>文章</a
							>
							这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：
						</p>
						<ul>
							<li>支持多色图标了，不再受单色限制。</li>
							<li>
								通过一些技巧，支持像字体那样，通过 <code>font-size</code>,
								<code>color</code> 来调整样式。
							</li>
							<li>兼容性较差，支持 IE9+，及现代浏览器。</li>
							<li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
						</ul>
						<p>使用步骤如下：</p>
						<h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
						<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
						<h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
						<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
						<h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
						<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
					</div>
				</div>
			</div>
		</div>
		<script>
			$(document).ready(function () {
				$('.tab-container .content:first').show()

				$('#tabs li').click(function (e) {
					var tabContent = $('.tab-container .content')
					var index = $(this).index()

					if ($(this).hasClass('active')) {
						return
					} else {
						$('#tabs li').removeClass('active')
						$(this).addClass('active')

						tabContent.hide().eq(index).fadeIn()
					}
				})
			})
		</script>
	</body>
</html>
