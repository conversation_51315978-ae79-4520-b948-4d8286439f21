# Technology Stack

## Core Framework
- **Vue 3.5.13** with Composition API and TypeScript
- **Vite 5.4.11** for build tooling and development server
- **TypeScript** for type safety and maintainability

## State & Routing
- **Pinia 2.2.6** for state management
- **Vue Router 4.4.5** for routing with auto-generated routes via `vite-plugin-pages`

## UI & Styling
- **Element Plus 2.8.8** with auto-import via `unplugin-vue-components`
- **SCSS** with `akvts` design system integration
- **Prettier** formatting: 4-space tabs, single quotes, no semicolons

## Key Libraries
- **Axios 1.7.7** for HTTP requests with interceptors
- **SignalR 8.0.7** for real-time communication
- **ExcelJS 4.4.0** for Excel file processing
- **LuckySheet** for spreadsheet functionality
- **ECharts 5.5.1** for data visualization

## Development Tools
- **Playwright** for E2E testing
- **Commitlint** with conventional commits
- **Auto-import** for Vue APIs and components

## Build Configuration
- **Path aliases**: `@/` (src), `#/` (api), `$/` (stores)
- **Chunk splitting**: Separate vendor bundles for Element Plus, Vue, and other dependencies
- **Compression**: Brotli compression enabled
- **Source maps**: Disabled in production

## Common Commands

```bash
# Development
npm run dev                 # Start dev server (port from VITE_PORT env)

# Build & Deploy  
npm run build              # Production build with memory optimization
npm run preview            # Preview production build

# Testing
npm run test:e2e           # Run Playwright E2E tests
npm run test:e2e:ui        # Run tests with UI
npm run test:e2e:report    # Show test report
```

## Environment Requirements
- Node.js >= 16.0.0
- npm >= 7.0.0
- Modern browser with ES6+ support