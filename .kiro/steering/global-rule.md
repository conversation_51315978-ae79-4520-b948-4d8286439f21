---
inclusion: always
---

# Global Development Rules

## Communication & Workflow
- Use **Chinese** for user communication and interface text
- When asking questions or completing requests, utilize MCP feedback mechanisms
- Continue MCP interactions until user feedback is complete before ending requests

## Code Style & Formatting
- Use **4-space indentation** for all files
- **Single quotes** for strings, **no semicolons** (Prettier configuration)
- **PascalCase** for components, **camelCase** for functions/variables
- **kebab-case** for file/folder names, except components which use PascalCase

## Vue 3 Conventions
- Use **Composition API** with `<script setup>` syntax
- Prefer **TypeScript** for type safety and maintainability
- Use **auto-imports** for Vue APIs (ref, reactive, computed, etc.)
- Component props should be typed with interfaces

## Architecture Patterns
- **Feature-based organization**: Group related files by domain/feature
- **Single responsibility**: One API file per domain, one store per feature
- **Path aliases**: Use `@/` for src, `#/` for api, `$/` for stores
- **Separation of concerns**: Keep business logic in composables/hooks

## API & Data Management
- All API calls go through centralized axios configuration in `api/index.ts`
- Use TypeScript interfaces for request/response types in `api/interface.ts`
- Handle loading states and error handling consistently
- Prefer Pinia stores for complex state management

## Component Guidelines
- Common reusable components in `src/components/common/` with `-comp.vue` suffix
- Feature-specific components grouped by domain
- Use Element Plus components with auto-import configuration
- Implement proper prop validation and default values

## Performance Considerations
- Use `v-memo` for expensive list rendering operations
- Implement proper key attributes for v-for loops
- Lazy load heavy components and routes
- Optimize bundle size with proper chunk splitting

## Testing Standards
- Write E2E tests using Playwright for critical user flows
- Test files should mirror source structure in `tests/e2e/`
- Use descriptive test names that explain expected behavior
- Include both positive and negative test cases

## Error Handling
- Implement global error handling via axios interceptors
- Provide user-friendly error messages in Chinese
- Log errors appropriately for debugging purposes
- Handle network failures and timeouts gracefully

## Government System Specific
- Maintain multi-level permission management patterns
- Ensure data validation and approval workflow compliance
- Support large-scale data table processing efficiently
- Implement real-time updates via SignalR where appropriate