# Project Structure

## Root Level Organization

```
├── public/                 # Static assets (fonts, images, third-party libs)
├── src/                   # Main source code
├── tests/                 # Test files (E2E with Playwright)
├── scripts/               # Build and utility scripts
└── docs/                  # Project documentation
```

## Source Code Structure (`src/`)

### Core Application Files
- `main.ts` - Application entry point with plugin initialization
- `App.vue` - Root component with global styles
- `vite-env.d.ts` - Vite environment type definitions

### Feature Organization
```
src/
├── api/                   # API layer
│   ├── index.ts          # Axios configuration & request interceptors
│   ├── config.ts         # API endpoint configuration
│   ├── interface.ts      # Request/response type definitions
│   └── *Api.ts           # Feature-specific API modules
├── components/           # Reusable components
│   ├── common/           # Base UI components
│   └── [feature].vue     # Feature-specific components
├── views/                # Page components (auto-routed)
│   └── index/            # Main application pages
├── stores/               # Pinia state management
├── hooks/                # Composable functions
├── define/               # Type definitions and constants
├── plugin/               # Plugin configurations
├── directive/            # Custom Vue directives
├── utils/                # Utility functions
├── assets/               # Images and static resources
├── styles/               # SCSS stylesheets
└── worker/               # Web Workers
```

## Key Conventions

### File Naming
- **Components**: PascalCase (e.g., `BusinessProcess.vue`)
- **Pages**: kebab-case in folders, PascalCase files (e.g., `primary-key-definition/index.vue`)
- **API files**: PascalCase with `Api` suffix (e.g., `LedgerApi.ts`)
- **Stores**: camelCase with descriptive names (e.g., `useUserStore.ts`)
- **Types**: PascalCase with `.define.ts` suffix (e.g., `ledger.define.ts`)

### Path Aliases
- `@/` → `src/` (main source)
- `#/` → `src/api/` (API modules)
- `$/` → `src/stores/` (state management)

### Component Organization
- **Common components** in `src/components/common/` with `-comp.vue` suffix
- **Feature components** grouped by domain in `src/components/`
- **Page components** in `src/views/` with auto-generated routes

### API Structure
- One API file per domain (e.g., `LedgerApi.ts`, `WorkflowApi.ts`)
- Centralized request configuration in `api/index.ts`
- Type definitions in `api/interface.ts`

### State Management
- Store files prefixed with `use` (e.g., `useUserStore.ts`)
- Feature-specific stores (e.g., `taskManageStore.ts`)
- Global state in `useGlobal.ts`

### Testing Structure
- E2E tests in `tests/e2e/` with `.spec.ts` extension
- Test utilities and helpers in `tests/` root
- Feature-specific test folders mirror source structure