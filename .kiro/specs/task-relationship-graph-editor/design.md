# 设计文档

## 概述

任务关系图谱绘制功能是一个基于Vue3的可视化任务关系管理工具，提供直观的图形化界面来创建、编辑和管理任务之间的关系图谱。该功能采用现代化的前端架构，结合专业的图形绘制库，为用户提供流畅的交互体验。

### 核心特性
- **可视化编辑**：支持拖拽、缩放、平移等直观操作
- **多样化节点**：支持6种不同类型和状态的任务节点
- **灵活连线**：支持多种连线样式表示不同关系类型
- **实时协作**：支持多人同时编辑和实时同步
- **数据持久化**：支持保存、导出和版本管理
- **响应式设计**：适配不同屏幕尺寸和设备

## 架构设计

### 技术选型

基于前期技术调研，我们选择以下技术栈：

#### 核心图形库：Vue Flow
- **选择理由**：专为Vue3设计，TypeScript支持完善，内置拖拽、节点连接等核心功能
- **优势**：
  - 与Vue3生态完美集成
  - 丰富的内置功能和扩展性
  - 活跃的社区支持和详细文档
  - 支持图片导出和自定义样式

#### 辅助技术栈
- **状态管理**：Pinia - 管理图谱数据和用户交互状态
- **UI组件**：Element Plus - 提供控制面板和工具栏组件
- **图标库**：Element Plus Icons - 提供工具栏和节点图标
- **导出功能**：html2canvas + FileSaver.js - 实现图片导出
- **实时协作**：SignalR - 支持多人协作功能

### 系统架构

```mermaid
graph TB
    A[用户界面层] --> B[组件层]
    B --> C[业务逻辑层]
    C --> D[数据管理层]
    D --> E[存储层]
    
    A --> A1[顶部工具栏]
    A --> A2[左侧控制面板]
    A --> A3[中央画布区域]
    A --> A4[右侧图例面板]
    
    B --> B1[GraphEditor组件]
    B --> B2[NodeComponent组件]
    B --> B3[EdgeComponent组件]
    B --> B4[ToolbarComponent组件]
    B --> B5[ControlPanel组件]
    
    C --> C1[图谱编辑逻辑]
    C --> C2[节点管理逻辑]
    C --> C3[连线管理逻辑]
    C --> C4[导出导入逻辑]
    C --> C5[协作同步逻辑]
    
    D --> D1[Pinia Store]
    D --> D2[本地缓存]
    D --> D3[API接口]
    
    E --> E1[LocalStorage]
    E --> E2[IndexedDB]
    E --> E3[后端API]
```

### 模块划分

#### 1. 核心编辑模块 (Core Editor Module)
- **职责**：管理图谱的核心编辑功能
- **组件**：GraphEditor.vue, NodeEditor.vue, EdgeEditor.vue
- **功能**：节点创建、编辑、删除，连线绘制，画布操作

#### 2. 用户界面模块 (UI Module)
- **职责**：提供用户交互界面
- **组件**：Toolbar.vue, ControlPanel.vue, LegendPanel.vue
- **功能**：工具栏操作，控制面板功能，图例说明

#### 3. 数据管理模块 (Data Management Module)
- **职责**：管理图谱数据和状态
- **Store**：useGraphStore.ts, useCollaborationStore.ts
- **功能**：数据持久化，状态管理，历史记录

#### 4. 导入导出模块 (Import/Export Module)
- **职责**：处理图谱的导入导出功能
- **Composables**：useExport.ts, useImport.ts
- **功能**：多格式导出，数据导入，文件处理

#### 5. 协作模块 (Collaboration Module)
- **职责**：支持多人实时协作
- **Services**：CollaborationService.ts
- **功能**：实时同步，冲突解决，用户状态管理
## 组件设
计

### 主要组件结构

#### 1. TaskRelationshipGraphEditor.vue (主容器组件)
```typescript
// 组件结构
<template>
  <div class="task-graph-editor">
    <GraphToolbar />
    <div class="editor-content">
      <ControlPanel />
      <GraphCanvas />
      <LegendPanel />
    </div>
  </div>
</template>

// 主要职责
- 整体布局管理
- 组件间通信协调
- 全局状态初始化
- 快捷键事件处理
```

#### 2. GraphCanvas.vue (画布组件)
```typescript
// 基于Vue Flow的核心画布
<template>
  <VueFlow
    v-model="elements"
    :class="canvasClass"
    @node-drag-stop="onNodeDragStop"
    @connect="onConnect"
    @edge-update="onEdgeUpdate"
  >
    <Background />
    <MiniMap />
    <Controls />
    
    <!-- 自定义节点类型 -->
    <template #node-task="nodeProps">
      <TaskNode v-bind="nodeProps" />
    </template>
    
    <!-- 自定义边类型 -->
    <template #edge-custom="edgeProps">
      <CustomEdge v-bind="edgeProps" />
    </template>
  </VueFlow>
</template>

// 主要功能
- 图谱渲染和交互
- 节点拖拽和连接
- 画布缩放和平移
- 网格背景和辅助线
```

#### 3. TaskNode.vue (任务节点组件)
```typescript
// 节点类型定义
interface TaskNodeData {
  id: string
  type: 'explanation' | 'main-task' | 'sub-task' | 'completed' | 'in-progress' | 'not-started'
  label: string
  status: TaskStatus
  color: string
  size: 'small' | 'medium' | 'large'
  position: { x: number, y: number }
  metadata?: Record<string, any>
}

// 组件功能
- 多种节点类型渲染
- 节点状态可视化
- 双击编辑文本
- 拖拽手柄显示
- 连接点管理
```

#### 4. GraphToolbar.vue (顶部工具栏)
```typescript
// 工具栏功能
<template>
  <div class="graph-toolbar">
    <div class="toolbar-left">
      <el-button @click="saveGraph">保存</el-button>
      <el-dropdown @command="exportGraph">
        <el-button>导出<el-icon><ArrowDown /></el-icon></el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="png">PNG图片</el-dropdown-item>
            <el-dropdown-item command="svg">SVG矢量图</el-dropdown-item>
            <el-dropdown-item command="json">JSON数据</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-button @click="quickAction">一键</el-button>
    </div>
    
    <div class="toolbar-right">
      <span class="status-info">{{ statusText }}</span>
    </div>
  </div>
</template>

// 主要功能
- 保存和导出操作
- 快速操作按钮
- 状态信息显示
- 操作历史管理
```

#### 5. ControlPanel.vue (左侧控制面板)
```typescript
// 控制面板结构
<template>
  <div class="control-panel">
    <div class="panel-header">
      <h3>任务关系图谱绘制</h3>
    </div>
    
    <el-collapse v-model="activeNames">
      <el-collapse-item title="控制面板" name="control">
        <NodePalette />
        <CanvasSettings />
      </el-collapse-item>
      
      <el-collapse-item title="协作" name="collaboration">
        <CollaborationPanel />
      </el-collapse-item>
      
      <el-collapse-item title="业务报表" name="business">
        <BusinessReports />
      </el-collapse-item>
      
      <el-collapse-item title="临时报表" name="temp">
        <TempReports />
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

// 功能模块
- 节点工具面板
- 画布设置选项
- 协作功能管理
- 报表功能集成
```

#### 6. LegendPanel.vue (右侧图例面板)
```typescript
// 图例面板
<template>
  <div class="legend-panel">
    <div class="panel-header">
      <h4>图例说明</h4>
    </div>
    
    <div class="legend-items">
      <div 
        v-for="legend in legendItems" 
        :key="legend.type"
        class="legend-item"
        @click="createNodeFromLegend(legend.type)"
      >
        <div 
          class="legend-icon" 
          :style="{ backgroundColor: legend.color }"
        ></div>
        <span class="legend-label">{{ legend.label }}</span>
      </div>
    </div>
  </div>
</template>

// 图例配置
const legendItems = [
  { type: 'explanation', label: '圆明说明', color: '#ff4d4f' },
  { type: 'main-task', label: '主任务节点', color: '#1890ff' },
  { type: 'sub-task', label: '子任务节点', color: '#13c2c2' },
  { type: 'completed', label: '已完成任务', color: '#52c41a' },
  { type: 'in-progress', label: '进行中任务', color: '#fa8c16' },
  { type: 'not-started', label: '未开始任务', color: '#f5222d' }
]
```

## 接口设计

### 数据模型接口

#### 1. 图谱数据结构
```typescript
// 图谱主数据结构
interface GraphData {
  id: string
  name: string
  description?: string
  nodes: TaskNode[]
  edges: TaskEdge[]
  viewport: Viewport
  metadata: GraphMetadata
  createdAt: Date
  updatedAt: Date
  version: number
}

// 节点数据结构
interface TaskNode {
  id: string
  type: NodeType
  position: Position
  data: TaskNodeData
  style?: NodeStyle
  className?: string
  draggable?: boolean
  selectable?: boolean
}

// 边数据结构
interface TaskEdge {
  id: string
  source: string
  target: string
  type: EdgeType
  data?: EdgeData
  style?: EdgeStyle
  animated?: boolean
  label?: string
}

// 视口信息
interface Viewport {
  x: number
  y: number
  zoom: number
}
```

#### 2. 节点类型定义
```typescript
// 节点类型枚举
enum NodeType {
  EXPLANATION = 'explanation',
  MAIN_TASK = 'main-task',
  SUB_TASK = 'sub-task',
  COMPLETED = 'completed',
  IN_PROGRESS = 'in-progress',
  NOT_STARTED = 'not-started'
}

// 节点状态
enum TaskStatus {
  NOT_STARTED = 'not-started',
  IN_PROGRESS = 'in-progress',
  COMPLETED = 'completed',
  BLOCKED = 'blocked',
  CANCELLED = 'cancelled'
}

// 节点数据
interface TaskNodeData {
  label: string
  description?: string
  status: TaskStatus
  priority?: 'low' | 'medium' | 'high'
  assignee?: string
  dueDate?: Date
  tags?: string[]
  metadata?: Record<string, any>
}
```

#### 3. 边类型定义
```typescript
// 边类型枚举
enum EdgeType {
  DEPENDENCY = 'dependency',
  SEQUENCE = 'sequence',
  PARALLEL = 'parallel',
  CONDITIONAL = 'conditional',
  CUSTOM = 'custom'
}

// 边数据
interface EdgeData {
  label?: string
  description?: string
  condition?: string
  weight?: number
  metadata?: Record<string, any>
}

// 边样式
interface EdgeStyle {
  stroke?: string
  strokeWidth?: number
  strokeDasharray?: string
  markerEnd?: string
}
```

## 数据模型

### 状态管理设计

#### 1. 图谱状态管理 (useGraphStore)
```typescript
// 图谱状态管理
interface GraphState {
  // 当前图谱数据
  currentGraph: GraphData | null
  // 节点列表
  nodes: TaskNode[]
  // 边列表
  edges: TaskEdge[]
  // 选中的元素
  selectedElements: string[]
  // 视口状态
  viewport: Viewport
  // 编辑历史
  history: GraphSnapshot[]
  // 当前历史位置
  historyIndex: number
  // 是否已修改
  isDirty: boolean
}

// 主要操作方法
interface GraphActions {
  // 节点操作
  addNode(node: TaskNode): void
  updateNode(id: string, data: Partial<TaskNodeData>): void
  deleteNode(id: string): void
  
  // 边操作
  addEdge(edge: TaskEdge): void
  updateEdge(id: string, data: Partial<EdgeData>): void
  deleteEdge(id: string): void
  
  // 选择操作
  selectElement(id: string): void
  selectMultiple(ids: string[]): void
  clearSelection(): void
  
  // 历史操作
  undo(): void
  redo(): void
  saveSnapshot(): void
  
  // 图谱操作
  saveGraph(): Promise<void>
  loadGraph(id: string): Promise<void>
  exportGraph(format: ExportFormat): Promise<void>
}
```

#### 2. 协作状态管理 (useCollaborationStore)
```typescript
// 协作状态
interface CollaborationState {
  // 在线用户
  onlineUsers: CollaborationUser[]
  // 当前用户
  currentUser: CollaborationUser
  // 实时光标
  cursors: UserCursor[]
  // 协作锁定
  locks: ElementLock[]
  // 消息历史
  messages: CollaborationMessage[]
}

// 协作用户信息
interface CollaborationUser {
  id: string
  name: string
  avatar?: string
  color: string
  isActive: boolean
  lastSeen: Date
}
```

### 样式设计规范

#### 1. 节点样式配置
```typescript
// 节点样式映射
const NODE_STYLES: Record<NodeType, NodeStyleConfig> = {
  [NodeType.EXPLANATION]: {
    backgroundColor: '#ff4d4f',
    borderColor: '#cf1322',
    textColor: '#ffffff',
    size: { width: 80, height: 80 },
    borderRadius: '50%'
  },
  [NodeType.MAIN_TASK]: {
    backgroundColor: '#1890ff',
    borderColor: '#096dd9',
    textColor: '#ffffff',
    size: { width: 120, height: 120 },
    borderRadius: '50%'
  },
  [NodeType.SUB_TASK]: {
    backgroundColor: '#13c2c2',
    borderColor: '#08979c',
    textColor: '#ffffff',
    size: { width: 100, height: 100 },
    borderRadius: '50%'
  },
  [NodeType.COMPLETED]: {
    backgroundColor: '#52c41a',
    borderColor: '#389e0d',
    textColor: '#ffffff',
    size: { width: 90, height: 90 },
    borderRadius: '50%'
  },
  [NodeType.IN_PROGRESS]: {
    backgroundColor: '#fa8c16',
    borderColor: '#d46b08',
    textColor: '#ffffff',
    size: { width: 90, height: 90 },
    borderRadius: '50%'
  },
  [NodeType.NOT_STARTED]: {
    backgroundColor: '#f5222d',
    borderColor: '#cf1322',
    textColor: '#ffffff',
    size: { width: 90, height: 90 },
    borderRadius: '50%'
  }
}
```

#### 2. 连线样式配置
```typescript
// 连线样式映射
const EDGE_STYLES: Record<EdgeType, EdgeStyleConfig> = {
  [EdgeType.DEPENDENCY]: {
    stroke: '#1890ff',
    strokeWidth: 2,
    strokeDasharray: 'none',
    markerEnd: 'url(#arrow)',
    animated: false
  },
  [EdgeType.SEQUENCE]: {
    stroke: '#52c41a',
    strokeWidth: 2,
    strokeDasharray: 'none',
    markerEnd: 'url(#arrow)',
    animated: true
  },
  [EdgeType.PARALLEL]: {
    stroke: '#fa8c16',
    strokeWidth: 2,
    strokeDasharray: '5,5',
    markerEnd: 'url(#arrow)',
    animated: false
  },
  [EdgeType.CONDITIONAL]: {
    stroke: '#722ed1',
    strokeWidth: 2,
    strokeDasharray: '10,5',
    markerEnd: 'url(#arrow)',
    animated: false
  }
}
```

## 错误处理

### 错误类型定义
```typescript
// 错误类型枚举
enum GraphErrorType {
  VALIDATION_ERROR = 'validation_error',
  NETWORK_ERROR = 'network_error',
  STORAGE_ERROR = 'storage_error',
  COLLABORATION_ERROR = 'collaboration_error',
  EXPORT_ERROR = 'export_error',
  IMPORT_ERROR = 'import_error'
}

// 错误信息接口
interface GraphError {
  type: GraphErrorType
  message: string
  details?: any
  timestamp: Date
  recoverable: boolean
}
```

### 错误处理策略
```typescript
// 全局错误处理器
class GraphErrorHandler {
  // 处理验证错误
  handleValidationError(error: ValidationError): void {
    ElMessage.error(`数据验证失败: ${error.message}`)
    // 高亮错误元素
    this.highlightErrorElements(error.elements)
  }
  
  // 处理网络错误
  handleNetworkError(error: NetworkError): void {
    ElMessage.error('网络连接失败，请检查网络设置')
    // 启用离线模式
    this.enableOfflineMode()
  }
  
  // 处理存储错误
  handleStorageError(error: StorageError): void {
    ElMessage.warning('数据保存失败，将使用临时存储')
    // 切换到内存存储
    this.switchToMemoryStorage()
  }
  
  // 处理协作错误
  handleCollaborationError(error: CollaborationError): void {
    ElMessage.warning('协作功能暂时不可用')
    // 禁用协作功能
    this.disableCollaboration()
  }
}
```

## 测试策略

### 单元测试
```typescript
// 组件测试示例
describe('TaskNode.vue', () => {
  it('应该正确渲染不同类型的节点', () => {
    // 测试节点类型渲染
  })
  
  it('应该支持双击编辑节点文本', () => {
    // 测试编辑功能
  })
  
  it('应该正确处理拖拽事件', () => {
    // 测试拖拽功能
  })
})

// Store测试示例
describe('useGraphStore', () => {
  it('应该正确添加和删除节点', () => {
    // 测试节点管理
  })
  
  it('应该正确处理撤销重做操作', () => {
    // 测试历史管理
  })
})
```

### 集成测试
```typescript
// E2E测试场景
describe('图谱编辑器集成测试', () => {
  it('应该能完成完整的图谱创建流程', () => {
    // 1. 创建节点
    // 2. 连接节点
    // 3. 编辑属性
    // 4. 保存图谱
  })
  
  it('应该能正确导出和导入图谱', () => {
    // 测试导入导出功能
  })
  
  it('应该支持多人协作编辑', () => {
    // 测试协作功能
  })
})
```

### 性能测试
```typescript
// 性能测试指标
const PERFORMANCE_METRICS = {
  // 节点渲染性能
  maxNodes: 1000,
  renderTime: 100, // ms
  
  // 交互响应性能
  dragResponseTime: 16, // ms (60fps)
  zoomResponseTime: 16, // ms
  
  // 内存使用
  maxMemoryUsage: 100, // MB
  
  // 导出性能
  exportTime: 5000 // ms
}
```

## 部署和维护

### 构建配置
```typescript
// Vite构建优化
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'vue-flow': ['@vue-flow/core', '@vue-flow/background', '@vue-flow/controls'],
          'element-plus': ['element-plus'],
          'collaboration': ['@microsoft/signalr']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  }
})
```

### 监控和日志
```typescript
// 性能监控
class PerformanceMonitor {
  // 监控渲染性能
  monitorRenderPerformance(): void {
    // 记录渲染时间
  }
  
  // 监控内存使用
  monitorMemoryUsage(): void {
    // 记录内存占用
  }
  
  // 监控用户行为
  trackUserActions(): void {
    // 记录用户操作
  }
}
```

### 版本管理
```typescript
// 版本兼容性处理
interface VersionCompatibility {
  currentVersion: string
  supportedVersions: string[]
  migrationStrategies: Record<string, MigrationStrategy>
}

// 数据迁移策略
interface MigrationStrategy {
  from: string
  to: string
  migrate: (data: any) => any
}
```