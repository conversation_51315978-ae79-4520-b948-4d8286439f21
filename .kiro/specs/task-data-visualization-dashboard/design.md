# 设计文档

## 概述

任务关系数据可视化仪表板是一个基于Vue3 + TypeScript + ECharts的纯前端数据展示系统。该系统采用组件化架构，支持多种图表类型的数据可视化，并提供灵活的配置面板用于自定义图表展示效果。所有数据通过模拟生成并存储在浏览器本地环境中。

## 架构

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Dashboard Layout                          │
├─────────────────┬─────────────────────────┬─────────────────┤
│   Navigation    │     Main Content        │  Config Panel   │
│   Sidebar       │     Area               │  (Collapsible)  │
│                 │                        │                 │
│ • 概览          │ ┌─────────────────────┐ │ ┌─────────────┐ │
│ • 基点指标      │ │   Data Overview     │ │ │   Chart     │ │
│ • 表格          │ │   Cards (5x)        │ │ │   Config    │ │
│ • 柱状图        │ └─────────────────────┘ │ │   Form      │ │
│ • 折线图        │                        │ │             │ │
│ • 饼图          │ ┌─────────────────────┐ │ └─────────────┘ │
│                 │ │   Chart/Table       │ │                 │
│                 │ │   Display Area      │ │                 │
│                 │ └─────────────────────┘ │                 │
└─────────────────┴─────────────────────────┴─────────────────┘
```

### 技术栈

- **前端框架**: Vue 3.5.13 + Composition API + TypeScript
- **UI组件库**: Element Plus 2.8.8
- **图表库**: ECharts 5.5.1
- **状态管理**: Pinia 2.2.6
- **样式**: SCSS + akvts设计系统
- **本地存储**: localStorage + IndexedDB (大数据场景)

## 组件和接口

### 核心组件结构

```typescript
// 主要组件层次结构
DashboardView.vue
├── NavigationSidebar.vue
├── DataOverviewCards.vue
│   └── OverviewCard.vue (x5)
├── ChartDisplayArea.vue
│   ├── DataTable.vue
│   ├── BarChart.vue
│   ├── LineChart.vue
│   └── PieChart.vue
└── ConfigPanel.vue
    ├── ChartConfigForm.vue
    └── DataFilterForm.vue
```

### 组件接口定义

#### 1. DashboardView.vue (主容器)
```typescript
interface DashboardState {
  currentView: ViewType
  dashboardData: DashboardData
  chartConfig: ChartConfig
  isConfigPanelVisible: boolean
}

enum ViewType {
  OVERVIEW = 'overview',
  TABLE = 'table', 
  BAR_CHART = 'bar-chart',
  LINE_CHART = 'line-chart',
  PIE_CHART = 'pie-chart'
}
```

#### 2. Chart组件通用接口
```typescript
interface BaseChartProps {
  data: ChartData
  config: ChartConfig
  loading?: boolean
  height?: string | number
}

interface ChartEvents {
  onDataPointClick: (data: DataPoint) => void
  onLegendClick: (legend: string) => void
  onChartReady: (chart: EChartsInstance) => void
}
```

#### 3. ConfigPanel组件
```typescript
interface ConfigPanelProps {
  visible: boolean
  currentView: ViewType
  config: ChartConfig
}

interface ConfigPanelEvents {
  onConfigChange: (config: Partial<ChartConfig>) => void
  onConfigSave: (config: ChartConfig) => void
  onConfigReset: () => void
  onClose: () => void
}
```

### 自定义Hooks

#### useECharts Hook
```typescript
interface UseEChartsOptions {
  autoResize?: boolean
  theme?: string
  renderer?: 'canvas' | 'svg'
}

interface UseEChartsReturn {
  chartRef: Ref<HTMLElement>
  chartInstance: Ref<EChartsInstance | null>
  setOption: (option: EChartsOption) => void
  resize: () => void
  dispose: () => void
}

function useECharts(options?: UseEChartsOptions): UseEChartsReturn
```

#### useLocalStorage Hook
```typescript
interface UseLocalStorageOptions<T> {
  defaultValue: T
  serializer?: {
    read: (value: string) => T
    write: (value: T) => string
  }
}

function useLocalStorage<T>(
  key: string, 
  options: UseLocalStorageOptions<T>
): [Ref<T>, (value: T) => void]
```

## 数据模型

### 核心数据结构

```typescript
// 仪表板主数据结构
interface DashboardData {
  overview: OverviewData
  tableData: TableData
  chartData: ChartData
  lastUpdated: Date
}

// 概览数据
interface OverviewData {
  cards: OverviewCard[]
}

interface OverviewCard {
  id: string
  title: string
  value: number
  unit?: string
  trend?: {
    direction: 'up' | 'down' | 'stable'
    percentage: number
  }
  icon?: string
  color?: string
}

// 表格数据
interface TableData {
  columns: TableColumn[]
  rows: TableRow[]
  pagination: PaginationInfo
}

interface TableColumn {
  key: string
  label: string
  type: 'text' | 'number' | 'date' | 'status'
  sortable?: boolean
  filterable?: boolean
  width?: number
}

interface TableRow {
  id: string
  [key: string]: any
}

// 图表数据
interface ChartData {
  barChart: BarChartData
  lineChart: LineChartData
  pieChart: PieChartData
}

interface BarChartData {
  categories: string[]
  series: BarSeries[]
}

interface BarSeries {
  name: string
  data: number[]
  color?: string
}

interface LineChartData {
  xAxis: (string | number)[]
  series: LineSeries[]
}

interface LineSeries {
  name: string
  data: (number | null)[]
  color?: string
  lineStyle?: LineStyle
}

interface PieChartData {
  series: PieSeries[]
}

interface PieSeries {
  name: string
  data: PieDataItem[]
}

interface PieDataItem {
  name: string
  value: number
  color?: string
}
```

### 配置数据结构

```typescript
// 图表配置
interface ChartConfig {
  common: CommonConfig
  barChart: BarChartConfig
  lineChart: LineChartConfig
  pieChart: PieChartConfig
}

interface CommonConfig {
  theme: 'light' | 'dark'
  animation: boolean
  responsive: boolean
  showLegend: boolean
  showTooltip: boolean
}

interface BarChartConfig extends CommonConfig {
  orientation: 'vertical' | 'horizontal'
  showDataLabels: boolean
  barWidth: number
  spacing: number
}

interface LineChartConfig extends CommonConfig {
  smooth: boolean
  showPoints: boolean
  showArea: boolean
  lineWidth: number
}

interface PieChartConfig extends CommonConfig {
  showPercentage: boolean
  innerRadius: number
  outerRadius: number
  startAngle: number
}
```

## 错误处理

### 错误类型定义

```typescript
enum ErrorType {
  DATA_LOAD_ERROR = 'DATA_LOAD_ERROR',
  CHART_RENDER_ERROR = 'CHART_RENDER_ERROR',
  CONFIG_SAVE_ERROR = 'CONFIG_SAVE_ERROR',
  STORAGE_ERROR = 'STORAGE_ERROR'
}

interface DashboardError {
  type: ErrorType
  message: string
  details?: any
  timestamp: Date
}
```

### 错误处理策略

1. **数据加载错误**: 显示友好的错误提示，提供重试机制
2. **图表渲染错误**: 降级到简单的数据展示，记录错误日志
3. **配置保存错误**: 提示用户保存失败，保留当前配置状态
4. **存储空间不足**: 自动清理旧数据，提示用户存储状态

### 全局错误处理

```typescript
// 错误处理服务
class ErrorHandler {
  static handle(error: DashboardError): void {
    // 记录错误日志
    console.error(`[${error.type}] ${error.message}`, error.details)
    
    // 显示用户友好的错误提示
    ElMessage.error(this.getErrorMessage(error.type))
    
    // 上报错误信息（如果需要）
    this.reportError(error)
  }
  
  private static getErrorMessage(type: ErrorType): string {
    const messages = {
      [ErrorType.DATA_LOAD_ERROR]: '数据加载失败，请稍后重试',
      [ErrorType.CHART_RENDER_ERROR]: '图表渲染异常，请检查数据格式',
      [ErrorType.CONFIG_SAVE_ERROR]: '配置保存失败，请重新尝试',
      [ErrorType.STORAGE_ERROR]: '本地存储异常，请清理浏览器缓存'
    }
    return messages[type] || '未知错误'
  }
}
```

## 测试策略

### 单元测试

1. **组件测试**: 使用Vue Test Utils测试各组件的渲染和交互
2. **Hook测试**: 测试自定义hooks的逻辑正确性
3. **工具函数测试**: 测试数据处理和格式化函数
4. **存储服务测试**: 测试本地存储的读写操作

### 集成测试

1. **数据流测试**: 测试组件间的数据传递和状态同步
2. **图表交互测试**: 测试图表的点击、悬停等交互功能
3. **配置面板测试**: 测试配置的实时预览和保存功能

### E2E测试

1. **用户流程测试**: 使用Playwright测试完整的用户操作流程
2. **跨浏览器测试**: 确保在不同浏览器中的兼容性
3. **响应式测试**: 测试在不同屏幕尺寸下的显示效果

### 测试覆盖率目标

- 单元测试覆盖率: ≥ 80%
- 集成测试覆盖率: ≥ 70%
- E2E测试覆盖关键用户路径: 100%