# 需求文档

## 介绍

本功能在任务目标拆解详情页面添加"子任务关系属性调整"按钮，并实现一个全面的子任务属性管理页面。该功能允许用户通过专用界面管理子任务关系和属性，具备查看、编辑和管理子任务属性的各种操作能力。功能采用按钮式导航设计，提供8个功能按钮用于触发不同的管理弹窗，并通过优化的表格组件正确显示子任务属性数据。

## 需求

### 需求 1：导航按钮功能

**用户故事：** 作为任务管理员，我希望能从任务详情页面访问子任务关系属性调整功能，以便高效管理子任务属性和关系。

#### 验收标准

1. 当查看任务目标拆解详情页面时，系统应在"重要程度调整"按钮之后显示"子任务关系属性调整"按钮
2. 当点击"子任务关系属性调整"按钮时，系统应导航到子任务属性管理页面
3. 当导航到子任务属性管理页面时，系统应保留当前任务上下文和查询参数
4. 当没有选中子任务时，按钮应处于禁用状态并显示警告信息

### 需求 2：子任务属性管理界面

**用户故事：** 作为任务管理员，我希望在专用的管理界面中查看和管理子任务属性，以便高效地组织和控制子任务属性。

#### 验收标准

1. 当访问子任务属性管理页面时，系统应显示包含子任务属性的综合列表
2. 当查看子任务列表时，系统应显示以下列：子任务属性名称、子任务类型、属性类型、属性值、紧急程度、风险等级、重要程度、状态、最后修改、修改人和操作
3. 当显示子任务数据时，系统应使用代表实际政府管理场景的真实模拟数据
4. 当查看页面时，系统应显示功能按钮包括：新增子任务属性、属性类型管理、属性可视化、属性计算、重要性统计、权限设置管理、紧急程度规则和历史记录
5. 表格数据应正确显示，不应出现"-"占位符

### 需求 3：子任务操作功能

**用户故事：** 作为任务管理员，我希望能对子任务属性执行各种操作，以便有效地维护和更新子任务信息。

#### 验收标准

1. 当在列表中查看子任务时，系统应提供操作按钮：调整、提交、审核和更多
2. 当点击"调整"按钮时，系统应允许修改子任务属性
3. 当点击"提交"按钮时，系统应提供子任务提交功能
4. 当点击"审核"按钮时，系统应提供子任务审核功能
5. 当点击"更多"按钮时，系统应显示额外的操作选项
6. 当执行操作时，系统应提供适当的反馈消息

### 需求 4：搜索和筛选功能

**用户故事：** 作为任务管理员，我希望能搜索和筛选子任务属性，以便快速找到特定的子任务及其属性。

#### 验收标准

1. 当访问子任务属性管理页面时，系统应提供子任务名称的搜索功能
2. 当使用搜索功能时，系统应实时筛选结果
3. 当查看搜索界面时，系统应提供清晰的输入字段和操作按钮
4. 当执行搜索时，系统应保持当前页面状态和上下文
5. 系统应提供任务类型和属性类型的下拉筛选功能

### 需求 5：返回导航功能

**用户故事：** 作为任务管理员，我希望能导航回任务详情页面，以便在管理子任务属性后返回到之前的上下文。

#### 验收标准

1. 当查看子任务属性管理页面时，系统应显示"返回"按钮
2. 当点击"返回"按钮时，系统应导航回任务目标拆解详情页面
3. 当返回详情页面时，系统应保留原始查询参数和页面状态
4. 当在页面间导航时，系统应保持一致的用户体验和上下文

### 需求 6：测试和验证

**用户故事：** 作为开发人员，我希望能使用Playwright MCP执行手动测试，以便通过自动化浏览器测试验证子任务关系属性管理功能。

#### 验收标准

1. 当实现功能时，系统应支持通过Playwright MCP集成进行手动测试
2. 当运行手动测试时，系统应允许测试按钮功能、导航流程和用户交互
3. 当执行Playwright MCP测试时，系统应验证从详情页面到属性管理页面再返回的完整用户工作流程
4. 当执行测试时，系统应验证所有操作按钮、搜索功能和数据显示都能正常工作
5. 测试应验证按钮式导航设计和表格数据的正确显示