# 设计文档

## 概述

本功能通过添加子任务关系属性管理能力来扩展现有的任务目标拆解系统。设计遵循已建立的Vue 3 + Element Plus架构，与现有任务管理工作流程无缝集成。解决方案包括对详情页面的UI增强，添加新的"子任务关系属性调整"按钮，以及一个全面的专用子任务属性管理界面，具备完整的CRUD操作、搜索功能和用于不同管理功能的按钮式导航。

**核心改进：**
- 将导航选项卡改为功能按钮，用于触发弹窗
- 修复表格数据显示问题，确保正确显示真实数据而非"-"占位符
- 优化操作按钮配置，包括调整、提交、审核和更多功能
- 实现响应式按钮布局和状态管理

## 架构设计

### 组件结构
```
src/views/index/taskObjectiveDecomposition/
├── detail/[id].vue (已修改)
└── subtaskAttributeManagement/
    ├── [id].vue (新增)
    └── mockData.ts (新增)
```

### 导航流程
```
任务详情页面 → 子任务属性管理页面 → 任务详情页面
```

### 状态管理
- 利用现有的Pinia存储进行任务管理
- 扩展当前任务存储以支持子任务属性操作
- 在导航过程中保持一致的状态

## 组件和接口

### 1. 增强的任务详情页面
**文件：** `src/views/index/taskObjectiveDecomposition/detail/[id].vue`

**修改内容：**
- 在"重要程度调整"按钮之后添加"子任务关系属性调整"按钮
- 实现新按钮的导航处理程序
- 保持现有功能和布局

**按钮实现：**
```vue
<el-button type="success" @click="handleSubtaskAttributeManagement" :disabled="selectedRows.length === 0">
  子任务关系属性调整
</el-button>
```

### 2. 子任务属性管理页面
**文件：** `src/views/index/taskObjectiveDecomposition/subtaskAttributeManagement/[id].vue`

**核心功能：**
- 全面的子任务属性列表，包含列：子任务属性名称、子任务类型、属性类型、属性值、紧急程度、风险等级、重要程度、状态、最后修改、修改人、操作
- 用于不同管理功能的导航按钮：新增子任务属性、属性类型管理、属性可视化、属性计算、重要性统计、权限设置管理、紧急程度规则、历史记录
- 子任务名称的实时搜索功能
- 每个子任务的操作按钮：调整、提交、审核、更多
- 返回详情页面的导航，保持状态

**布局结构：**
```vue
<template>
  <div class="subtask-attribute-management" v-loading="loading">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">子任务属性</h2>
        <span class="page-subtitle" v-if="taskDetail">{{ taskDetail.taskName }}</span>
      </div>
      <div class="header-actions">
        <el-button @click="handleReturn" class="return-button">返回</el-button>
      </div>
    </div>
    
    <!-- 导航按钮 -->
    <div class="navigation-buttons">
      <el-button
        v-for="tab in navigationTabs"
        :key="tab.key"
        :type="activeTab === tab.key ? 'primary' : 'default'"
        @click="handleButtonClick(tab)"
        class="nav-button"
      >
        {{ tab.label }}
      </el-button>
    </div>
    
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <el-input v-model="searchForm.keyword" placeholder="请输入子任务属性名称" />
        <el-select v-model="searchForm.taskType" placeholder="请选择任务类型" />
        <el-select v-model="searchForm.attributeType" placeholder="请选择属性类型" />
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </div>
    </div>
    
    <!-- 数据表格 -->
    <div class="table-section">
      <BaseTableComp
        :data="filteredData"
        :colData="tableColumns"
        :buttons="operationButtons"
        @clickButton="handleOperationClick"
      />
    </div>
  </div>
</template>
```

## 数据模型

### 子任务属性接口
```typescript
interface SubtaskAttribute {
  id: string
  attributeName: string // 子任务属性名称
  taskType: string // 子任务类型
  attributeType: string // 属性类型
  attributeValue: number // 属性值
  urgencyLevel: string // 紧急程度
  riskLevel: string // 风险等级
  importanceLevel: string // 重要程度
  status: string // 状态
  lastModified: string // 最后修改时间
  modifier: string // 修改人
}
```

### 模拟数据结构
```typescript
const mockSubtaskAttributes: SubtaskAttribute[] = [
  {
    id: 'attr_001',
    attributeName: '永川区民政局填报流程',
    taskType: TaskType.BUSINESS_REPORT,
    attributeType: AttributeType.PARTY_BUILDING,
    attributeValue: 5,
    urgencyLevel: UrgencyLevel.URGENT,
    riskLevel: RiskLevel.LOW,
    importanceLevel: ImportanceLevel.IMPORTANT,
    status: TaskStatus.IN_PROGRESS,
    lastModified: '2024-01-15',
    modifier: '张三'
  },
  {
    id: 'attr_002',
    attributeName: '社区网格化管理数据统计',
    taskType: TaskType.DATA_COLLECTION,
    attributeType: AttributeType.SOCIAL_GOVERNANCE,
    attributeValue: 8,
    urgencyLevel: UrgencyLevel.HIGH,
    riskLevel: RiskLevel.MEDIUM,
    importanceLevel: ImportanceLevel.CRITICAL,
    status: TaskStatus.IN_PROGRESS,
    lastModified: '2024-01-14',
    modifier: '李四'
  }
  // 包含10条真实的政府管理场景数据...
]
```

### 表格列配置（已修复）
```typescript
export const tableColumns = [
  { field: 'attributeName', title: '子任务属性名称', width: '200px', sortable: true },
  { field: 'taskType', title: '子任务类型', width: '120px', sortable: true },
  { field: 'attributeType', title: '属性类型', width: '120px', sortable: true },
  { field: 'attributeValue', title: '属性值', width: '80px', sortable: true },
  { field: 'urgencyLevel', title: '紧急程度', width: '100px', sortable: true },
  { field: 'riskLevel', title: '风险等级', width: '100px', sortable: true },
  { field: 'importanceLevel', title: '重要程度', width: '120px', sortable: true },
  { field: 'status', title: '状态', width: '100px', sortable: true },
  { field: 'lastModified', title: '最后修改', width: '120px', sortable: true },
  { field: 'modifier', title: '修改人', width: '100px' }
]
```

### 操作按钮配置（已修复）
```typescript
export const operationButtons = [
  { type: 'primary', code: 'adjust', title: '调整', icon: 'Edit' },
  { type: 'success', code: 'submit', title: '提交', icon: 'Upload' },
  { type: 'warning', code: 'audit', title: '审核', icon: 'View' },
  { type: 'info', code: 'more', title: '更多', icon: 'More' }
]
```

## 错误处理

### 导航错误处理
- 在导航前验证任务ID参数
- 处理缺失或无效的任务上下文
- 提供备用导航选项

### 数据加载错误处理
- 在数据获取期间显示加载状态
- 为失败的操作显示错误消息
- 为失败的请求实现重试机制

### 操作错误处理
- 在操作前验证用户权限
- 为失败的操作提供清晰的错误消息
- 为关键操作实现回滚机制

## 测试策略

### 单元测试
- 测试按钮添加和导航功能
- 测试子任务属性数据管理
- 测试搜索和筛选操作
- 测试错误处理场景

### 集成测试
- 测试页面间的导航流程
- 测试导航过程中的状态保持
- 测试组件间的数据一致性

### 端到端测试
- 测试从详情页面到属性管理的完整用户工作流程
- 测试所有操作按钮及其功能
- 测试搜索和筛选能力
- 测试返回导航和状态保持

### Playwright MCP手动测试
- 按钮功能和导航流程的交互式浏览器测试
- 用户交互和UI响应性的手动验证
- 操作按钮（调整、提交、审核、更多）行为的实时测试
- 浏览器环境中搜索功能和数据筛选的验证
- 从任务详情页面到属性管理页面再返回的完整工作流程测试
- 通过Playwright自动化进行跨浏览器兼容性测试

### 测试数据要求
- 真实的政府管理子任务场景
- 各种属性类型和值
- 不同的紧急程度和重要程度级别
- 多种任务类型和类别

## 性能考虑

### 数据加载
- 为大型子任务列表实现分页
- 使用虚拟滚动进行性能优化
- 缓存频繁访问的数据

### 状态管理
- 最小化不必要的重新渲染
- 优化组件更新
- 使用计算属性处理派生数据

### 导航性能
- 在导航期间保持组件状态
- 最小化返回导航时的数据重新获取
- 实现高效的路由缓存

## 可访问性合规

### 键盘导航
- 确保所有按钮都可通过键盘访问
- 实现正确的Tab顺序
- 为常见操作提供键盘快捷键

### 屏幕阅读器支持
- 添加适当的ARIA标签
- 为操作提供描述性文本
- 确保正确的标题层次结构

### 视觉可访问性
- 保持足够的颜色对比度
- 为交互元素提供视觉指示器
- 支持高对比度模式

## 安全考虑

### 数据访问控制
- 验证用户对子任务的访问权限
- 实现基于角色的操作限制
- 审计敏感操作

### 输入验证
- 清理所有用户输入
- 在处理前验证数据
- 防止注入攻击

### 状态安全
- 保护组件状态中的敏感数据
- 实现页面间的安全导航
- 验证路由参数

## 图形化关系编辑器设计

### 核心技术选型
基于原型图的分析，系统需要实现一个图形化的任务关系编辑器，支持节点拖拽、连线和属性编辑。

**技术栈选择：**
- **AntV X6** - 专业的图形编辑引擎，原生支持Vue 3
- **Element Plus** - 用于属性编辑弹窗和工具栏
- **Vue 3 Composition API** - 响应式状态管理

### 图形编辑器组件结构
```
src/views/index/taskObjectiveDecomposition/subtaskAttributeManagement/
├── [id].vue (主页面)
├── components/
│   ├── GraphEditor.vue (图形编辑器核心组件)
│   ├── NodePropertyPanel.vue (节点属性面板)
│   ├── EdgePropertyPanel.vue (连线属性面板)
│   ├── ToolbarPanel.vue (工具栏面板)
│   └── LayoutControls.vue (布局控制组件)
└── composables/
    ├── useGraphEditor.ts (图形编辑器逻辑)
    ├── useNodeManagement.ts (节点管理)
    └── useLayoutAlgorithm.ts (布局算法)
```

### 节点类型定义
```typescript
interface TaskNode {
  id: string
  type: 'main-task' | 'sub-task' | 'completed-task' | 'in-progress-task' | 'pending-task'
  label: string
  position: { x: number; y: number }
  size: { width: number; height: number }
  style: {
    fill: string
    stroke: string
    strokeWidth: number
  }
  data: {
    taskName: string
    taskType: TaskType
    status: TaskStatus
    urgencyLevel: UrgencyLevel
    importanceLevel: ImportanceLevel
    assignee?: string
    deadline?: string
  }
}
```

## 技术实现细节

### 图形编辑器核心实现
```typescript
// GraphEditor.vue 核心逻辑
import { Graph } from '@antv/x6'

const useGraphEditor = () => {
  const graphContainer = ref<HTMLElement>()
  const graph = ref<Graph>()
  
  // 初始化图形编辑器
  const initGraph = () => {
    graph.value = new Graph({
      container: graphContainer.value!,
      width: 800,
      height: 600,
      grid: true,
      panning: true,
      mousewheel: {
        enabled: true,
        zoomAtMousePosition: true,
        modifiers: 'ctrl',
        minScale: 0.5,
        maxScale: 3,
      },
      connecting: {
        router: 'manhattan',
        connector: {
          name: 'rounded',
          args: { radius: 8 },
        },
        anchor: 'center',
        connectionPoint: 'anchor',
        allowBlank: false,
        snap: { radius: 20 },
        createEdge() {
          return new Shape.Edge({
            attrs: {
              line: {
                stroke: '#A2B1C3',
                strokeWidth: 2,
                targetMarker: {
                  name: 'block',
                  width: 12,
                  height: 8,
                },
              },
            },
            zIndex: 0,
          })
        },
      },
    })
  }
  
  return { graph, initGraph }
}
```

### 节点样式配置
```typescript
// 节点样式定义
const nodeStyles = {
  'main-task': {
    width: 120,
    height: 60,
    attrs: {
      body: {
        fill: '#1890ff',
        stroke: '#1890ff',
        strokeWidth: 2,
        rx: 8,
        ry: 8,
      },
      text: {
        fill: '#ffffff',
        fontSize: 14,
        fontWeight: 'bold',
      },
    },
  },
  'sub-task': {
    width: 100,
    height: 50,
    attrs: {
      body: {
        fill: '#52c41a',
        stroke: '#52c41a',
        strokeWidth: 2,
        rx: 6,
        ry: 6,
      },
      text: {
        fill: '#ffffff',
        fontSize: 12,
      },
    },
  },
  'completed-task': {
    width: 100,
    height: 50,
    attrs: {
      body: {
        fill: '#13c2c2',
        stroke: '#13c2c2',
        strokeWidth: 2,
        rx: 6,
        ry: 6,
      },
      text: {
        fill: '#ffffff',
        fontSize: 12,
      },
    },
  },
}
```

### 按钮式导航实现
```typescript
// 导航按钮点击处理
const handleButtonClick = (tab: any) => {
  activeTab.value = tab.key
  
  // 根据不同按钮打开对应弹窗或切换视图
  switch (tab.key) {
    case 'graph-editor':
      showGraphEditor.value = true
      break
    case 'attribute-visualization':
      openVisualizationDialog()
      break
    case 'calculation':
      openCalculationDialog()
      break
    default:
      ElMessage.info(`打开${tab.label}弹窗`)
  }
}
```