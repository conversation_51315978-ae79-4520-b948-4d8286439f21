<!DOCTYPE html>
<html lang="zh">
	<head>
		<meta charset="UTF-8" />
		<link rel="icon" type="image/svg+xml" href="/vite.svg" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
		<meta http-equiv="Pragma" content="no-cache" />
		<meta http-equiv="Expires" content="0" />

		<title>“一表通”智能报表</title>
		<script src="/fonticon/iconfont.js"></script>
		<script src="/GOV_CONFIG.js"></script>

		<!-- luckysheet -->
		<link rel="stylesheet" href="/luckysheet/plugins/css/pluginsCss.css" />
		<link rel="stylesheet" href="/luckysheet/plugins/plugins.css" />
		<link rel="stylesheet" href="/luckysheet/css/luckysheet.css" />
		<link rel="stylesheet" href="/luckysheet/assets/iconfont/iconfont.css" />
		<script src="/luckysheet/plugins/js/plugin.js"></script>
		<script src="/luckysheet/luckysheet.umd.js"></script>
	</head>

	<body class="light">
		<div id="app"></div>
		<script type="module" src="/src/main.ts"></script>
		<script
			src="https://zd-wpkgate-emas.bigdatacq.com:32383/static/wpk-jssdk.1.0.2/wpkReporter.js"
			crossorigin="true"
		></script>
		<script>
			try {
				const config = {
					bid: 'cqsdsjyyfzglj-bbtzxt_kmift2ec_orm4loju',
					signkey: '1234567890abcdef',
					gateway: 'https://zd-wpkgate-emas.bigdatacq.com:32383',
				}
				const wpk = new wpkReporter(config)
				wpk.installAll()
				window._wpk = wpk
			} catch (err) {
				console.error('WpkReporter init fail', err)
			}
		</script>
	</body>
</html>
