# 批量修改功能手动测试指南

## 测试环境准备

1. 启动开发服务器：
   ```bash
   npm run dev
   ```

2. 在浏览器中访问：
   ```
   http://localhost:5176/index/primary-key-definition
   ```

## 测试用例

### 1. 基础功能测试

#### 1.1 批量修改按钮显示
- **测试步骤**：
  1. 访问主键定义页面
  2. 查看页面顶部右侧区域
- **预期结果**：
  - 应该看到"批量修改"按钮
  - 按钮应该是橙色（warning类型）
  - 按钮应该位于"新增主键"按钮左侧

#### 1.2 表格多选功能
- **测试步骤**：
  1. 查看表格左侧第一列
  2. 尝试点击复选框
- **预期结果**：
  - 表格第一列应该显示复选框
  - 点击复选框可以选择/取消选择行
  - 可以选择多行

#### 1.3 批量修改按钮状态
- **测试步骤**：
  1. 未选择任何行时，查看批量修改按钮状态
  2. 选择一行或多行后，查看按钮状态
- **预期结果**：
  - 未选择行时：按钮应该被禁用（灰色）
  - 选择行后：按钮应该被启用（可点击）

### 2. 批量修改弹窗测试

#### 2.1 打开批量修改弹窗
- **测试步骤**：
  1. 选择一行或多行数据
  2. 点击"批量修改"按钮
- **预期结果**：
  - 应该弹出批量修改对话框
  - 对话框标题为"批量修改"
  - 显示已选择的记录数量信息

#### 2.2 批量修改表单字段
- **测试步骤**：
  1. 在批量修改弹窗中查看表单字段
- **预期结果**：
  - 应该包含以下字段：
    - 主键数据类型（下拉选择，必填）
    - 最小长度（数字输入框）
    - 最大长度（数字输入框）
  - 所有字段应该为空（初始状态）

#### 2.3 数据类型选择
- **测试步骤**：
  1. 点击数据类型下拉框
  2. 查看可选项
- **预期结果**：
  - 应该显示所有可用的数据类型选项
  - 包括：字符型、数值型、定点型、日期型等

### 3. 表单验证测试

#### 3.1 必填字段验证
- **测试步骤**：
  1. 不选择数据类型，直接点击"确认"按钮
- **预期结果**：
  - 应该显示错误提示："请选择数据类型"
  - 弹窗不应该关闭

#### 3.2 数值字段验证
- **测试步骤**：
  1. 在最小长度和最大长度字段中输入负数或非数字
- **预期结果**：
  - 应该只允许输入非负整数
  - 输入框应该有合理的最大值限制

### 4. 批量修改执行测试

#### 4.1 成功修改场景
- **测试步骤**：
  1. 选择2-3行数据
  2. 打开批量修改弹窗
  3. 选择数据类型："字符型"
  4. 设置最小长度：5
  5. 设置最大长度：50
  6. 点击"确认"按钮
- **预期结果**：
  - 显示成功提示："成功批量修改 X 条记录"
  - 弹窗自动关闭
  - 表格中选中的行数据应该更新
  - 选择状态应该被清空
  - 批量修改按钮重新变为禁用状态

#### 4.2 取消修改场景
- **测试步骤**：
  1. 选择数据并打开批量修改弹窗
  2. 填写部分表单数据
  3. 点击"取消"按钮
- **预期结果**：
  - 弹窗应该关闭
  - 数据不应该被修改
  - 选择状态保持不变

### 5. 用户体验测试

#### 5.1 响应式设计
- **测试步骤**：
  1. 在不同屏幕尺寸下测试功能
  2. 调整浏览器窗口大小
- **预期结果**：
  - 在移动端和桌面端都应该正常显示
  - 按钮和弹窗应该适应屏幕尺寸
  - 不应该出现水平滚动条

#### 5.2 键盘导航
- **测试步骤**：
  1. 使用Tab键在页面元素间导航
  2. 使用Enter键激活按钮
  3. 使用Escape键关闭弹窗
- **预期结果**：
  - 所有交互元素都应该支持键盘导航
  - 焦点顺序应该合理
  - 键盘快捷键应该正常工作

### 6. 边界情况测试

#### 6.1 大量数据选择
- **测试步骤**：
  1. 如果页面有很多数据，尝试选择所有行
  2. 执行批量修改
- **预期结果**：
  - 应该能够处理大量数据的批量修改
  - 性能应该可接受
  - 成功提示应该显示正确的数量

#### 6.2 网络错误处理
- **测试步骤**：
  1. 断开网络连接
  2. 尝试执行批量修改
- **预期结果**：
  - 应该显示适当的错误提示
  - 不应该导致页面崩溃

## 测试结果记录

### 测试环境
- 浏览器：
- 操作系统：
- 测试日期：
- 测试人员：

### 测试结果
| 测试用例 | 状态 | 备注 |
|---------|------|------|
| 1.1 批量修改按钮显示 | ✅/❌ | |
| 1.2 表格多选功能 | ✅/❌ | |
| 1.3 批量修改按钮状态 | ✅/❌ | |
| 2.1 打开批量修改弹窗 | ✅/❌ | |
| 2.2 批量修改表单字段 | ✅/❌ | |
| 2.3 数据类型选择 | ✅/❌ | |
| 3.1 必填字段验证 | ✅/❌ | |
| 3.2 数值字段验证 | ✅/❌ | |
| 4.1 成功修改场景 | ✅/❌ | |
| 4.2 取消修改场景 | ✅/❌ | |
| 5.1 响应式设计 | ✅/❌ | |
| 5.2 键盘导航 | ✅/❌ | |
| 6.1 大量数据选择 | ✅/❌ | |
| 6.2 网络错误处理 | ✅/❌ | |

### 发现的问题
1. 
2. 
3. 

### 改进建议
1. 
2. 
3. 
