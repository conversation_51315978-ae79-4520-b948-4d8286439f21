# 业务表主键定义功能说明

## 功能概述

业务表主键定义功能是一个完整的主键管理系统，支持主键的增删改查、排序、搜索、批量操作以及 Excel 导入导出功能。

## 主要功能

### 1. 基础功能

- **主键列表显示**：展示所有主键的详细信息
- **搜索功能**：支持按主键名称搜索
- **排序功能**：点击主键名称列标题可切换升序/降序/无排序
- **分页功能**：支持分页显示，可调整每页显示数量

### 2. 主键管理

- **新增主键**：通过弹框表单添加新的主键定义
- **编辑主键**：修改现有主键的属性
- **删除主键**：删除不需要的主键定义
- **状态切换**：直接在列表中切换主键的启用/禁用状态

### 3. 批量操作

- **批量修改**：选中多个主键进行批量属性修改
- **批量导入**：从 Excel 文件导入主键数据
- **批量导出**：将主键数据导出为 Excel 文件

### 4. Excel 导入导出

#### 导出功能
- 支持将当前所有主键数据导出为 Excel 文件
- 导出文件包含完整的主键信息和格式化样式
- 文件名自动包含时间戳

#### 导入功能
- 支持从 Excel 文件导入主键数据
- 提供数据验证和错误提示
- 支持替换现有数据或追加到现有数据
- 自动跳过重复的主键名称

#### 导入模板
- 提供标准的 Excel 导入模板下载
- 模板包含示例数据和填写说明
- 确保导入数据格式的正确性

### 5. 数据持久化

- 使用 localStorage 存储所有数据
- 页面刷新后数据不丢失
- 支持完整的 CRUD 操作

### 6. 表单验证

- 必填项验证：主键名称、数据类型、更新通知
- 数据格式验证：长度范围、数值类型等
- 业务逻辑验证：重复名称检查、长度关系验证
- 实时错误提示和阻止无效提交

## 使用指南

### 访问页面

通过路由 `/primary-key-definition` 访问主键定义页面。

### 新增主键

1. 点击"自定义主键"按钮
2. 填写主键信息：
   - 主键名称（必填）
   - 主键数据类型（必填）
   - 最小长度、最大长度（可选）
   - 主键说明（可选）
   - 主键更新通知（必填）
   - 加密级别、加密内容（可选）
3. 点击"确定"保存

### 批量导入

1. 点击"更多操作" → "下载导入模板"获取标准模板
2. 按照模板格式填写数据
3. 点击"批量导入"选择 Excel 文件
4. 选择替换或追加数据方式
5. 系统自动验证并导入数据

### 批量导出

1. 点击"批量导出"按钮
2. 系统自动生成并下载 Excel 文件
3. 文件包含当前所有主键数据

### 主键清理规则

1. 点击"更多操作" → "主键清理规则"
2. 设置清理时间（天数）
3. 选择清理策略（归档/删除）
4. 保存规则配置

## 数据格式说明

### 主键字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| 主键名称 | 字符串 | 是 | 唯一标识，不能重复 |
| 数据类型 | 枚举 | 是 | 字符型C/数值型N/逻辑性B/日期型T/XXXX |
| 最小长度 | 数值 | 否 | 必须 ≤ 最大长度 |
| 最大长度 | 数值 | 否 | 必须 ≥ 最小长度 |
| 说明 | 字符串 | 否 | 主键描述信息 |
| 主键状态 | 布尔 | 是 | 启用/禁用 |
| 更新通知 | 布尔 | 是 | 开启/关闭 |
| 加密级别 | 枚举 | 否 | 无/低/高、中等、高级 |
| 加密内容 | 字符串 | 否 | 加密相关内容 |

### Excel 导入格式

导入的 Excel 文件应包含以下列：

1. 序号
2. 主键名称（必填）
3. 数据类型（必填，使用中文标签）
4. 最小长度
5. 最大长度
6. 说明
7. 主键状态（启用/禁用）
8. 更新通知（开启/关闭）
9. 加密级别
10. 加密内容

## 技术实现

- **前端框架**：Vue 3 + TypeScript + Element Plus
- **数据存储**：localStorage（纯前端实现）
- **Excel 处理**：ExcelJS + file-saver
- **测试覆盖**：Playwright 端到端测试
- **响应式设计**：支持桌面端和移动端

## 测试

项目包含完整的 Playwright 测试脚本，覆盖所有主要功能：

```bash
# 运行测试
npx playwright test primary-key-definition.spec.ts

# 查看测试报告
npx playwright show-report
```

## 注意事项

1. 数据仅存储在浏览器本地，清除浏览器数据会丢失所有信息
2. Excel 导入时会自动验证数据格式，无效数据会被跳过
3. 主键名称必须唯一，重复名称会被自动跳过
4. 建议定期导出数据作为备份
5. 大量数据导入时请耐心等待处理完成
