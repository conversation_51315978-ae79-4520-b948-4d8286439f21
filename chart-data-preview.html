<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表模拟数据预览</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .content {
            padding: 30px;
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        .chart-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .chart-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #495057;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .chart-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        .data-preview {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #dee2e6;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .data-type {
            color: #6f42c1;
            font-weight: bold;
        }
        .data-value {
            color: #28a745;
        }
        .data-key {
            color: #dc3545;
        }
        .stats-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin-top: 30px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        .stat-item {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
        }
        .test-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            margin-top: 20px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
        }
        .feature-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 图表模拟数据预览</h1>
            <p>为多图表仪表板提供丰富的模拟数据支持</p>
        </div>
        
        <div class="content">
            <div class="chart-grid">
                <div class="chart-card">
                    <div class="chart-title">
                        <div class="chart-icon">📊</div>
                        柱状图数据
                    </div>
                    <div class="data-preview">
                        <div><span class="data-key">categories:</span> <span class="data-value">["1月", "2月", "3月", "4月", "5月", "6月"]</span></div>
                        <div><span class="data-key">series:</span> [</div>
                        <div>&nbsp;&nbsp;{ <span class="data-key">name:</span> <span class="data-value">"已完成"</span>, <span class="data-key">data:</span> [45, 52, 38, 67, 43, 58] },</div>
                        <div>&nbsp;&nbsp;{ <span class="data-key">name:</span> <span class="data-value">"进行中"</span>, <span class="data-key">data:</span> [23, 31, 28, 35, 29, 42] }</div>
                        <div>]</div>
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-title">
                        <div class="chart-icon">📈</div>
                        折线图数据
                    </div>
                    <div class="data-preview">
                        <div><span class="data-key">xAxis:</span> <span class="data-value">["周一", "周二", "周三", "周四", "周五"]</span></div>
                        <div><span class="data-key">series:</span> [</div>
                        <div>&nbsp;&nbsp;{ <span class="data-key">name:</span> <span class="data-value">"任务完成率"</span>, <span class="data-key">data:</span> [85, 92, 78, 89, 94] },</div>
                        <div>&nbsp;&nbsp;{ <span class="data-key">name:</span> <span class="data-value">"效率指标"</span>, <span class="data-key">data:</span> [76, 88, 82, 91, 87] }</div>
                        <div>]</div>
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-title">
                        <div class="chart-icon">🥧</div>
                        饼图数据
                    </div>
                    <div class="data-preview">
                        <div><span class="data-key">data:</span> [</div>
                        <div>&nbsp;&nbsp;{ <span class="data-key">name:</span> <span class="data-value">"已完成"</span>, <span class="data-key">value:</span> 67, <span class="data-key">color:</span> <span class="data-value">"#67C23A"</span> },</div>
                        <div>&nbsp;&nbsp;{ <span class="data-key">name:</span> <span class="data-value">"进行中"</span>, <span class="data-key">value:</span> 23, <span class="data-key">color:</span> <span class="data-value">"#409EFF"</span> },</div>
                        <div>&nbsp;&nbsp;{ <span class="data-key">name:</span> <span class="data-value">"待开始"</span>, <span class="data-key">value:</span> 15, <span class="data-key">color:</span> <span class="data-value">"#E6A23C"</span> }</div>
                        <div>]</div>
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-title">
                        <div class="chart-icon">🎯</div>
                        雷达图数据
                    </div>
                    <div class="data-preview">
                        <div><span class="data-key">categories:</span> <span class="data-value">["执行力", "创新力", "协作力", "学习力", "领导力"]</span></div>
                        <div><span class="data-key">series:</span> [</div>
                        <div>&nbsp;&nbsp;{ <span class="data-key">name:</span> <span class="data-value">"团队A"</span>, <span class="data-key">data:</span> [85, 92, 78, 89, 94] },</div>
                        <div>&nbsp;&nbsp;{ <span class="data-key">name:</span> <span class="data-value">"团队B"</span>, <span class="data-key">data:</span> [76, 88, 82, 91, 87] }</div>
                        <div>]</div>
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-title">
                        <div class="chart-icon">🔥</div>
                        热力图数据
                    </div>
                    <div class="data-preview">
                        <div><span class="data-key">xAxis:</span> <span class="data-value">["周一", "周二", "周三", "周四", "周五"]</span></div>
                        <div><span class="data-key">yAxis:</span> <span class="data-value">["00:00", "06:00", "12:00", "18:00"]</span></div>
                        <div><span class="data-key">data:</span> [</div>
                        <div>&nbsp;&nbsp;[0, 0, 45], [0, 1, 23], [0, 2, 78], [0, 3, 56],</div>
                        <div>&nbsp;&nbsp;[1, 0, 67], [1, 1, 89], [1, 2, 92], [1, 3, 34]</div>
                        <div>]</div>
                    </div>
                </div>

                <div class="chart-card">
                    <div class="chart-title">
                        <div class="chart-icon">🗺️</div>
                        地图数据
                    </div>
                    <div class="data-preview">
                        <div><span class="data-key">regions:</span> [</div>
                        <div>&nbsp;&nbsp;{ <span class="data-key">name:</span> <span class="data-value">"北京"</span>, <span class="data-key">value:</span> 234 },</div>
                        <div>&nbsp;&nbsp;{ <span class="data-key">name:</span> <span class="data-value">"上海"</span>, <span class="data-key">value:</span> 187 },</div>
                        <div>&nbsp;&nbsp;{ <span class="data-key">name:</span> <span class="data-value">"广东"</span>, <span class="data-key">value:</span> 345 },</div>
                        <div>&nbsp;&nbsp;{ <span class="data-key">name:</span> <span class="data-value">"浙江"</span>, <span class="data-key">value:</span> 156 }</div>
                        <div>]</div>
                    </div>
                </div>
            </div>

            <div class="stats-section">
                <h2 style="text-align: center; color: #495057; margin-bottom: 25px;">📈 数据统计概览</h2>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">14</div>
                        <div class="stat-label">支持的图表类型</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">500+</div>
                        <div class="stat-label">模拟数据点</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">6</div>
                        <div class="stat-label">数据维度</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">实时更新</div>
                    </div>
                </div>
            </div>

            <div style="background: white; border-radius: 12px; padding: 25px; margin-top: 30px;">
                <h3 style="color: #495057; margin-bottom: 20px;">🚀 功能特性</h3>
                <ul class="feature-list">
                    <li>支持14种不同类型的图表数据生成</li>
                    <li>提供丰富的任务相关模拟数据</li>
                    <li>实时数据更新和刷新机制</li>
                    <li>多维度数据分析支持</li>
                    <li>可配置的数据范围和精度</li>
                    <li>完整的数据类型定义和验证</li>
                </ul>
                
                <div style="text-align: center; margin-top: 25px;">
                    <a href="http://localhost:5177/taskObjectiveDecomposition/dashboard/1?free=true" 
                       class="test-button">
                        🎯 立即测试图表功能
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
