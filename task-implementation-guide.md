# 原型设计图界面调整任务实施指南

## 任务概述

基于8个原型设计图，需要调整和完善任务目标拆解功能的界面布局，确保严格匹配原型设计规范。

## 技术要求

### 设计规范遵循
- 所有弹窗组件使用 `:visible-close-button="false"` 和 `:visible-confirm-button="false"` 隐藏默认按钮
- 表单 label 统一右对齐，宽度设置为 140px
- 所有表格组件默认高度设置为 500px，使用 `:auto-height="true"` 属性
- 数据加载操作添加 100 毫秒延迟效果
- 数据持久化到 localStorage，页面刷新后状态保持

### 文件结构
```
src/views/index/taskObjectiveDecomposition/
├── detail/[id].vue                    # 任务详情页面
├── components/
│   ├── ProgressConfigDialog.vue       # 进度配置弹窗
│   ├── StyleTemplateDialog.vue        # 样式模板设置弹窗
│   ├── StyleImportDialog.vue          # 样式模板导入弹窗
│   ├── DifficultyAnalysisDialog.vue   # 难度分析弹窗
│   ├── TimeAnalysisDialog.vue         # 时长分析弹窗
│   ├── ReminderHistoryDialog.vue      # 提醒历史弹窗
│   └── StyleTemplateTable.vue         # 样式模板数据表格
```

## 子任务详细说明

### 任务1: 调整任务详情页面布局
**文件**: `src/views/index/taskObjectiveDecomposition/detail/[id].vue`
**优先级**: 最高

#### 需要调整的内容:
1. **页面头部**
   - 确保标题显示为"任务目标拆解"
   - 返回按钮样式和位置调整

2. **任务基本信息卡片**
   - 调整信息字段的布局和样式
   - 确保label宽度为140px，右对齐
   - 字段包括：任务名称、任务类型、创建部门/创建责任人、开始时间、业务报表子任务数量、临时报表子任务数量

3. **子任务列表卡片**
   - 调整操作按钮布局：进度配置、导出、子任务关系展示、更多操作下拉菜单
   - 表格列配置调整
   - 批量删除功能实现

#### 实施步骤:
1. 分析现有页面结构与原型设计的差异
2. 调整CSS样式，确保布局匹配原型
3. 完善交互功能和事件处理
4. 测试页面响应性和数据展示

### 任务2: 实现进度配置弹窗
**文件**: `src/views/index/taskObjectiveDecomposition/components/ProgressConfigDialog.vue`
**优先级**: 高

#### 功能要求:
1. **弹窗基本配置**
   - 标题："进度配置"
   - 宽度：800px
   - 隐藏默认关闭和确认按钮

2. **功能区域**
   - 任务进度完成百分比计算开关
   - 进度条样式列表区域
   - 样式模板设置和导入按钮
   - 搜索框和重置功能
   - 样式模板表格展示

3. **数据处理**
   - 样式模板数据的增删改查
   - 颜色配置的验证和存储
   - 与父组件的数据交互

#### 实施步骤:
1. 创建弹窗组件基础结构
2. 实现开关控件和表格展示
3. 添加样式模板管理功能
4. 实现数据持久化
5. 测试所有交互功能

### 任务3: 实现样式模板设置弹窗
**文件**: `src/views/index/taskObjectiveDecomposition/components/StyleTemplateDialog.vue`
**优先级**: 中等

#### 功能要求:
1. **表单字段**
   - 样式模板名称（必填）
   - 执行中配色（十六进制值）
   - 已完成配色（十六进制值）

2. **表单验证**
   - 必填项验证
   - 颜色格式验证
   - 重复名称检查

3. **交互功能**
   - 取消和保存操作
   - 表单重置功能
   - 错误提示显示

### 任务4: 实现样式模板导入弹窗
**文件**: `src/views/index/taskObjectiveDecomposition/components/StyleImportDialog.vue`
**优先级**: 中等

#### 功能要求:
1. **文件上传区域**
   - 拖拽上传支持
   - 点击上传支持
   - 文件格式限制（.xlsx, .xls, .csv）

2. **UI设计**
   - 虚线边框样式
   - 云朵上传图标
   - 提示文字显示

3. **文件处理**
   - 文件格式验证
   - 文件大小限制
   - 上传进度显示
   - 错误处理

### 任务5-8: 分析类弹窗实现
**文件**: 
- `DifficultyAnalysisDialog.vue`
- `TimeAnalysisDialog.vue` 
- `ReminderHistoryDialog.vue`
- `StyleTemplateTable.vue`

#### 共同要求:
1. 遵循统一的弹窗设计规范
2. 数据展示格式与原型完全一致
3. 实现相应的数据模拟和处理逻辑
4. 添加适当的加载状态和错误处理

## 数据模拟要求

### Mock数据结构
```typescript
// 样式模板数据
interface StyleTemplate {
  id: string
  templateName: string
  inProgressColor: string
  completedColor: string
  createTime: string
}

// 难度分析数据
interface DifficultyAnalysis {
  timePressure: { content: string, difficulty: string, color: string }
  reportContent: { content: string, difficulty: string, color: string }
  stepComplexity: { content: string, difficulty: string, color: string }
  overallDifficulty: string
}

// 时长分析数据
interface TimeAnalysis {
  stageName: string
  duration: string
}

// 提醒历史数据
interface ReminderHistory {
  reminderTime: string
  reminderPerson: string
}
```

## 测试要求

每个组件完成后需要进行以下测试：
1. 功能测试：所有交互功能正常工作
2. 样式测试：与原型设计完全匹配
3. 数据测试：数据持久化和状态管理正确
4. 响应性测试：不同屏幕尺寸下的显示效果
5. 错误处理测试：异常情况的处理

## 完成标准

1. 所有界面布局与原型设计100%匹配
2. 所有交互功能正常工作
3. 数据持久化功能完整
4. 代码质量符合项目规范
5. 通过所有测试用例
