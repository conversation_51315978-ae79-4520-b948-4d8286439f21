<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主键配置功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #409EFF;
            border-bottom: 2px solid #409EFF;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✓ ";
            color: #67C23A;
            font-weight: bold;
        }
        .test-steps {
            background: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #409EFF;
            margin: 15px 0;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .test-steps li {
            margin: 8px 0;
        }
        .expected-result {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .data-structure {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>主键自动清理和存储位置配置功能测试</h1>
    
    <div class="test-section">
        <h2 class="test-title">功能概述</h2>
        <p>每个主键现在都有独立的清理配置和存储位置设置，不再是全局配置。</p>
        
        <h3>实现的功能：</h3>
        <ul class="feature-list">
            <li>每一行主键都有独立的"主键自动清理"配置</li>
            <li>每一行主键都有独立的"主键存储位置"配置</li>
            <li>配置数据保存在每个主键记录中</li>
            <li>支持数据持久化到localStorage</li>
            <li>新增主键时自动添加默认配置</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">数据结构变更</h2>
        <p>PrimaryKeyData接口已更新，新增了以下字段：</p>
        <div class="data-structure">
interface PrimaryKeyData {
    // ... 原有字段
    cleanupRule?: CleanupRuleData // 主键清理规则
    storageLocation?: StorageLocationConfig // 主键存储位置
    // ... 其他字段
}

interface CleanupRuleData {
    cleanupTime: number // 清理时间（天数）
    cleanupStrategy: string // 清理策略（archive/delete）
}

interface StorageLocationConfig {
    storageLocation: string // 存储位置（filesystem/configfile）
}
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">测试步骤</h2>
        
        <div class="test-steps">
            <h3>测试1：主键自动清理配置</h3>
            <ol>
                <li>打开主键定义页面</li>
                <li>在任意一行点击"更多"按钮</li>
                <li>选择"主键自动清理"</li>
                <li>设置清理时间（如：60天）</li>
                <li>选择清理策略（归档或删除）</li>
                <li>点击确定保存</li>
            </ol>
            <div class="expected-result">
                <strong>预期结果：</strong>
                <ul>
                    <li>弹窗正常显示，包含清理时间输入框和策略下拉选择</li>
                    <li>保存成功后显示成功提示</li>
                    <li>该行的配置已保存到对应的主键记录中</li>
                    <li>页面刷新后配置仍然存在</li>
                </ul>
            </div>
        </div>

        <div class="test-steps">
            <h3>测试2：主键存储位置配置</h3>
            <ol>
                <li>在同一行或不同行点击"更多"按钮</li>
                <li>选择"主键存储位置"</li>
                <li>选择存储位置（文件系统或存储到配置文件）</li>
                <li>点击确定保存</li>
            </ol>
            <div class="expected-result">
                <strong>预期结果：</strong>
                <ul>
                    <li>弹窗正常显示，包含存储位置下拉选择</li>
                    <li>保存成功后显示成功提示</li>
                    <li>该行的存储位置配置已保存</li>
                </ul>
            </div>
        </div>

        <div class="test-steps">
            <h3>测试3：不同行的独立配置</h3>
            <ol>
                <li>为第一行主键设置：清理时间30天，策略归档，存储位置文件系统</li>
                <li>为第二行主键设置：清理时间90天，策略删除，存储位置配置文件</li>
                <li>分别查看两行的配置</li>
            </ol>
            <div class="expected-result">
                <strong>预期结果：</strong>
                <ul>
                    <li>每行显示各自的配置，互不影响</li>
                    <li>重新打开配置弹窗时显示对应行的当前配置</li>
                </ul>
            </div>
        </div>

        <div class="test-steps">
            <h3>测试4：新增主键的默认配置</h3>
            <ol>
                <li>点击"新增主键"按钮</li>
                <li>填写主键信息并保存</li>
                <li>查看新增主键的配置</li>
            </ol>
            <div class="expected-result">
                <strong>预期结果：</strong>
                <ul>
                    <li>新增的主键自动包含默认配置</li>
                    <li>清理时间默认30天，策略默认归档</li>
                    <li>存储位置默认为空（请选择）</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">验证要点</h2>
        <ul class="feature-list">
            <li>每行的配置是独立的，不会相互影响</li>
            <li>配置数据正确保存到localStorage</li>
            <li>页面刷新后配置数据不丢失</li>
            <li>弹窗显示当前行的实际配置值</li>
            <li>新增主键时自动添加默认配置</li>
            <li>UI交互流畅，无错误提示</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">技术实现要点</h2>
        <ul class="feature-list">
            <li>移除了全局配置，改为行级配置</li>
            <li>使用currentRow跟踪当前操作的行数据</li>
            <li>配置保存时更新对应主键记录的字段</li>
            <li>弹窗显示时读取当前行的配置数据</li>
            <li>保持了原有的UI风格和交互方式</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">注意事项</h2>
        <p style="color: #E6A23C;">
            <strong>重要：</strong>现在每个主键都有自己独立的配置，这意味着：
        </p>
        <ul>
            <li>配置不再是全局的，而是针对每个具体的主键</li>
            <li>不同主键可以有不同的清理规则和存储位置</li>
            <li>修改一个主键的配置不会影响其他主键</li>
            <li>数据结构已更新，包含了新的配置字段</li>
        </ul>
    </div>
</body>
</html>
