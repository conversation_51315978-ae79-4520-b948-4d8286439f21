<!-- 修复后的 GraphCanvas.vue 关键部分 -->
<script setup lang="ts">
// ... 其他导入保持不变

// ==================== 修复1: 锁定布局控制 ====================

/** 画布是否被锁定 - 修复版本 */
const isLocked = computed(() => {
  return props.readonly || props.layoutMode === 'locked'
})

/** 节点是否可拖拽 - 修复版本 */
const nodesDraggable = computed(() => {
  return !isLocked.value
})

/** 节点是否可连接 - 修复版本 */
const nodesConnectable = computed(() => {
  return !isLocked.value
})

/** 元素是否可选择 - 修复版本 */
const elementsSelectable = computed(() => {
  return !isLocked.value
})

// ==================== 修复2: 高亮控制 ====================

/** 节点样式类 - 修复版本 */
const getNodeClass = (node: any) => {
  const classes = ['custom-node']

  // 只有在启用高亮时才添加选中状态类
  if (props.highlightOnSelect && graphStore.selectedElements.includes(node.id)) {
    classes.push('selected')
  }

  // 添加节点类型类
  if (node.data?.status) {
    classes.push(`status-${node.data.status}`)
  }

  return classes
}

/** 边样式类 - 修复版本 */
const getEdgeClass = (edge: any) => {
  const classes = ['custom-edge']

  // 只有在启用高亮时才添加选中状态类
  if (props.highlightOnSelect && graphStore.selectedElements.includes(edge.id)) {
    classes.push('selected')
  }

  return classes
}

// ==================== 修复3: 节点拖拽处理 ====================

/**
 * 处理节点拖拽结束 - 修复版本
 */
const handleNodeDragStop = (event: any) => {
  // 如果画布被锁定，阻止拖拽
  if (isLocked.value) {
    console.log('画布已锁定，拖拽被阻止')
    return
  }

  const { node } = event
  if (node) {
    console.log('节点拖拽结束:', node.id, node.position)
    graphStore.moveNode(node.id, node.position)
  }
}

/**
 * 处理节点点击 - 修复版本
 */
const handleNodeClick = (event: any) => {
  // 锁定模式下仍允许选择，但不允许其他操作
  const { node } = event
  if (!node) return

  // 处理多选
  if (event.event?.ctrlKey || event.event?.metaKey) {
    graphStore.toggleElementSelection(node.id)
  } else {
    graphStore.clearSelection()
    graphStore.selectElement(node.id)
  }

  emit('nodeClick', node)
}

// ==================== 修复4: 标签显示控制 ====================

/** Vue Flow 容器类 - 修复版本 */
const vueFlowClasses = computed(() => {
  return [
    'vue-flow-container',
    {
      'hide-node-labels': !props.showNodeLabels,
      'hide-edge-labels': !props.showEdgeLabels,
      'highlight-disabled': !props.highlightOnSelect,
      'layout-locked': isLocked.value
    }
  ]
})

// ==================== 监听器修复 ====================

// 监听布局模式变化 - 修复版本
watch(() => props.layoutMode, (newMode, oldMode) => {
  console.log('GraphCanvas: 布局模式从', oldMode, '切换到', newMode)

  // 根据布局模式调整画布行为
  if (newMode === 'locked') {
    // 锁定模式：清除选择状态，禁用交互
    graphStore.clearSelection()
    console.log('画布交互已锁定')
  } else if (newMode === 'auto' && oldMode !== 'auto') {
    // 切换到自动布局模式时，自动适应视图
    nextTick(() => {
      fitView()
    })
  } else if (newMode === 'manual') {
    console.log('画布交互已解锁')
  }
})

// 监听高亮设置变化 - 修复版本
watch(() => props.highlightOnSelect, (enabled) => {
  console.log('GraphCanvas: 高亮设置变更为', enabled)
  if (!enabled) {
    // 清除当前高亮效果
    graphStore.clearSelection()
  }
})

// 监听标签显示设置变化 - 修复版本
watch(() => props.showNodeLabels, (enabled) => {
  console.log('GraphCanvas: 节点标签显示变更为', enabled)
})

watch(() => props.showEdgeLabels, (enabled) => {
  console.log('GraphCanvas: 边标签显示变更为', enabled)
})

</script>

<template>
  <div 
    class="graph-canvas" 
    :style="{ height }"
    :class="{ 
      'readonly': readonly,
      'layout-locked': isLocked,
      'connecting': false
    }"
  >
    <VueFlow
      ref="vueFlowRef"
      :nodes="nodes"
      :edges="edges"
      :nodes-draggable="nodesDraggable"
      :nodes-connectable="nodesConnectable"
      :elements-selectable="elementsSelectable"
      :zoom-on-scroll="true"
      :pan-on-scroll="true"
      :zoom-on-pinch="true"
      :pan-on-drag="true"
      :default-zoom="1"
      :min-zoom="0.1"
      :max-zoom="4"
      :fit-view-on-init="true"
      @node-click="handleNodeClick"
      @node-double-click="handleNodeDoubleClick"
      @edge-click="handleEdgeClick"
      @pane-click="handleCanvasClick"
      @node-context-menu="handleNodeContextMenu"
      @edge-context-menu="handleEdgeContextMenu"
      @node-mouse-enter="handleNodeMouseEnter"
      @node-mouse-leave="handleNodeMouseLeave"
      @edge-mouse-enter="handleEdgeMouseEnter"
      @edge-mouse-leave="handleEdgeMouseLeave"
      :class="vueFlowClasses"
    >
      <!-- 背景网格 -->
      <Background v-if="showBackground" pattern-color="#e4e7ed" :gap="20" />
      
      <!-- 控制面板 -->
      <Controls v-if="showControls" />
      
      <!-- 小地图 -->
      <MiniMap
        v-if="showMiniMap"
        :node-color="getNodeColor"
        :mask-color="'rgba(240, 242, 247, 0.7)'"
        position="bottom-right"
      />

      <!-- 自定义节点模板 -->
      <template #node-default="{ data, id }">
        <div
          class="custom-node"
          :class="getNodeClass({ data, id })"
          :style="getNodeStyle(data)"
        >
          <!-- 节点编号 -->
          <div v-if="data.nodeNumber" class="node-number">
            {{ data.nodeNumber }}
          </div>

          <!-- 节点内容 - 修复标签显示控制 -->
          <div v-if="showNodeLabels" class="node-content">
            <div class="node-title">{{ data.label || '未命名节点' }}</div>
            <div v-if="data.description" class="node-description">
              {{ data.description }}
            </div>
          </div>

          <!-- 节点状态指示器 -->
          <div v-if="data.status" class="node-status" :class="`status-${data.status}`">
            <div class="status-dot"></div>
          </div>
        </div>
      </template>
    </VueFlow>

    <!-- 悬停提示 -->
    <HoverTooltip
      :visible="showTooltip"
      :position="tooltipPosition"
      :node="hoveredNode"
      :edge="hoveredEdge"
    />

    <!-- 锁定状态提示 -->
    <div v-if="isLocked" class="lock-hint">
      <el-icon><Lock /></el-icon>
      <span>布局已锁定</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* 修复后的样式 */

/* 锁定状态样式 */
.graph-canvas.layout-locked {
  .vue-flow-container {
    cursor: not-allowed;
  }
  
  :deep(.vue-flow__node) {
    cursor: not-allowed !important;
    
    &:hover {
      transform: none !important;
    }
  }
}

/* 高亮禁用样式 - 修复版本 */
.vue-flow-container.highlight-disabled {
  :deep(.vue-flow__node.selected) {
    box-shadow: none !important;
    border: 1px solid #d9d9d9 !important;
  }

  :deep(.vue-flow__edge.selected) {
    .vue-flow__edge-path {
      stroke: inherit !important;
      stroke-width: inherit !important;
    }
  }
  
  .custom-node.selected {
    box-shadow: none !important;
    border: 1px solid #d9d9d9 !important;
  }
}

/* 标签隐藏样式 - 修复版本 */
.vue-flow-container.hide-node-labels {
  :deep(.vue-flow__node-default .node-content) {
    display: none !important;
  }

  .custom-node .node-content {
    display: none !important;
  }
}

.vue-flow-container.hide-edge-labels {
  :deep(.vue-flow__edge-label) {
    display: none !important;
  }
  
  :deep(.edge-label) {
    display: none !important;
  }
}

/* 锁定提示样式 */
.lock-hint {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 193, 7, 0.9);
  color: #856404;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 4px;
  pointer-events: none;
}
</style>
